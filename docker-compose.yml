version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: wn-mysql-v2
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: blog_api_v3
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      #- ./infrastructure/docker/mysql/mysql-custom.cnf:/etc/mysql/conf.d/custom.cnf
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$MYSQL_ROOT_PASSWORD"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - wnapi-network

  # Minio - S3 compatible storage
  minio:
    image: minio/minio:latest
    container_name: wn-minio
    restart: always
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: minio123
    ports:
      - "9071:9000"
      - "9072:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - wnapi-network

  # Redis
  redis:
    image: redis:7.0-alpine
    container_name: wn-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - wnapi-network

  # MailCatcher
  mailcatcher:
    image: schickling/mailcatcher
    container_name: wn-mailcatcher
    restart: always
    ports:
      - "1025:1025"
      - "1080:1080"
    networks:
      - wnapi-network

  # Jaeger
  jaeger:
    image: jaegertracing/all-in-one:1.46
    container_name: wn-jaeger
    restart: always
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14250:14250"
      - "14268:14268"
      - "14269:14269"
      - "9411:9411"
    networks:
      - wnapi-network

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - ~/qdrant_storage:/qdrant/storage:z
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    restart: unless-stopped
    networks:
      - wnapi-network

  mcp-server-qdrant:
    image: mcp-server-qdrant
    container_name: blog-api-mcp-qdrant
    restart: unless-stopped
    ports:
      - "9058:8000"
    environment:
      - FASTMCP_HOST=0.0.0.0
      - QDRANT_URL=http://qdrant:6333
      - COLLECTION_NAME=blog-api-v3
    depends_on:
      - qdrant
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - wnapi-network

  n8n:
    image: n8nio/n8n:latest
    container_name: blog-api-n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=Asia/Ho_Chi_Minh
      - DB_TYPE=sqlite
      - DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite
      - N8N_LOG_LEVEL=info
      - N8N_ENCRYPTION_KEY=n8n-encryption
    volumes:
      - n8n_data:/home/<USER>/.n8n
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - wnapi-network

networks:
  wnapi-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
  n8n_data:
  minio_data:
