# API Testing

Please test the API endpoints for: $ARGUMENTS.

Follow these steps:
1. Use Bruno API testing tool in `api-tests/bruno/`
2. Run the specific test collection
3. Verify request/response schemas
4. Check authentication and authorization
5. Test error handling and edge cases
6. Validate multi-tenant functionality
7. Check performance and response times
8. Update test documentation

Commands to use:
- `bru run api-tests/bruno --env local`
- `bru run api-tests/bruno/$MODULE --env local`

Focus on:
- API functionality and correctness
- Authentication and authorization
- Error handling
- Performance
- Multi-tenant isolation
