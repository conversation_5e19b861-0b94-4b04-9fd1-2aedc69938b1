# Update Documentation

Please update the documentation for: $ARGUMENTS.

Follow these steps:
1. Review existing documentation
2. Identify outdated or missing information
3. Update API documentation
4. Update code comments and README files
5. Review and update architecture diagrams
6. Update deployment guides
7. Check documentation consistency
8. Test documentation examples

Focus on:
- Accuracy and completeness
- Code examples and usage
- Architecture explanations
- API reference updates
- Deployment instructions
