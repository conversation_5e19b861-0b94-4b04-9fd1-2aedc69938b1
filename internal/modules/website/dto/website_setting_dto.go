package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
)

// WebsiteSettingCreateRequest represents the request to create a new website setting
type WebsiteSettingCreateRequest struct {
	SettingKey      string                        `json:"setting_key" validate:"required,min=1,max=255" example:"site_title"`
	SettingName     string                        `json:"setting_name" validate:"required,min=1,max=255" example:"Site Title"`
	Category        models.WebsiteSettingCategory `json:"category" validate:"required,oneof=general seo social security theme analytics comments media" example:"general"`
	SettingValue    string                        `json:"setting_value,omitempty" example:"My Awesome Website"`
	DefaultValue    string                        `json:"default_value,omitempty" example:"Default Site Title"`
	DataType        models.WebsiteSettingDataType `json:"data_type" validate:"required,oneof=string number boolean json array" example:"string"`
	ValidationRules string                        `json:"validation_rules,omitempty" example:"required,min=1,max=100"`
	Description     string                        `json:"description,omitempty" example:"The main title of your website"`
	IsPublic        bool                          `json:"is_public" example:"true"`
	SortOrder       int                           `json:"sort_order" example:"1"`
}

// WebsiteSettingUpdateRequest represents the request to update a website setting
type WebsiteSettingUpdateRequest struct {
	SettingName     *string                        `json:"setting_name,omitempty" validate:"omitempty,min=1,max=255" example:"Updated Site Title"`
	Category        *models.WebsiteSettingCategory `json:"category,omitempty" validate:"omitempty,oneof=general seo social security theme analytics comments media" example:"seo"`
	SettingValue    *string                        `json:"setting_value,omitempty" example:"Updated Value"`
	DefaultValue    *string                        `json:"default_value,omitempty" example:"Updated Default"`
	DataType        *models.WebsiteSettingDataType `json:"data_type,omitempty" validate:"omitempty,oneof=string number boolean json array" example:"string"`
	ValidationRules *string                        `json:"validation_rules,omitempty" example:"required,min=5,max=200"`
	Description     *string                        `json:"description,omitempty" example:"Updated description"`
	IsPublic        *bool                          `json:"is_public,omitempty" example:"false"`
	SortOrder       *int                           `json:"sort_order,omitempty" example:"2"`
}

// WebsiteSettingBulkUpdateRequest represents the request for bulk updating website settings
type WebsiteSettingBulkUpdateRequest struct {
	Settings []struct {
		SettingKey   string `json:"setting_key" validate:"required" example:"site_title"`
		SettingValue string `json:"setting_value" example:"Bulk Updated Title"`
	} `json:"settings" validate:"required,min=1"`
}

// WebsiteSettingResponse represents the response for website setting operations
type WebsiteSettingResponse struct {
	ID              uint                          `json:"id" example:"1"`
	WebsiteID       uint                          `json:"website_id" example:"1"`
	TenantID        uint                          `json:"tenant_id" example:"1"`
	SettingKey      string                        `json:"setting_key" example:"site_title"`
	SettingName     string                        `json:"setting_name" example:"Site Title"`
	Category        models.WebsiteSettingCategory `json:"category" example:"general"`
	SettingValue    string                        `json:"setting_value,omitempty" example:"My Awesome Website"`
	DefaultValue    string                        `json:"default_value,omitempty" example:"Default Site Title"`
	DataType        models.WebsiteSettingDataType `json:"data_type" example:"string"`
	ValidationRules string                        `json:"validation_rules,omitempty" example:"required,min=1,max=100"`
	Description     string                        `json:"description,omitempty" example:"The main title of your website"`
	IsPublic        bool                          `json:"is_public" example:"true"`
	SortOrder       int                           `json:"sort_order" example:"1"`
	CreatedAt       time.Time                     `json:"created_at"`
	UpdatedAt       time.Time                     `json:"updated_at"`
}

// WebsiteSettingListResponse represents the response for listing website settings
type WebsiteSettingListResponse struct {
	Settings   []WebsiteSettingResponse `json:"settings"`
	TotalCount int64                    `json:"total_count" example:"25"`
	Page       int                      `json:"page" example:"1"`
	PageSize   int                      `json:"page_size" example:"20"`
	TotalPages int                      `json:"total_pages" example:"2"`
}