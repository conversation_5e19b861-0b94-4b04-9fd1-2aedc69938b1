package services

import (
	"context"
	"regexp"
	"strings"
	"unicode/utf8"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
)

// SEOAnalyzer defines the interface for SEO analysis
type SEOAnalyzer interface {
	AnalyzeSEOMeta(ctx context.Context, seoMeta *models.SEOMeta) (*models.SEOAnalysis, error)
	AnalyzeTitle(title string, focusKeyword string) *models.TextAnalysis
	AnalyzeDescription(description string, focusKeyword string) *models.TextAnalysis
	AnalyzeKeywordDensity(content string, keyword string) *models.KeywordAnalysis
	AnalyzeReadability(content string) *models.ReadabilityAnalysis
	AnalyzeTechnicalSEO(seoMeta *models.SEOMeta) *models.TechnicalAnalysis
	GenerateRecommendations(analysis *models.SEOAnalysis) []string
}

// seoAnalyzer implements SEOAnalyzer
type seoAnalyzer struct{}

// NewSEOAnalyzer creates a new SEO analyzer
func NewSEOAnalyzer() SEOAnalyzer {
	return &seoAnalyzer{}
}

// AnalyzeSEOMeta performs comprehensive SEO analysis
func (a *seoAnalyzer) AnalyzeSEOMeta(ctx context.Context, seoMeta *models.SEOMeta) (*models.SEOAnalysis, error) {
	analysis := &models.SEOAnalysis{}

	// Analyze title
	if seoMeta.MetaTitle != nil && *seoMeta.MetaTitle != "" {
		focusKeyword := ""
		if seoMeta.FocusKeyword != nil {
			focusKeyword = *seoMeta.FocusKeyword
		}
		analysis.TitleAnalysis = a.AnalyzeTitle(*seoMeta.MetaTitle, focusKeyword)
	}

	// Analyze description
	if seoMeta.MetaDescription != nil && *seoMeta.MetaDescription != "" {
		focusKeyword := ""
		if seoMeta.FocusKeyword != nil {
			focusKeyword = *seoMeta.FocusKeyword
		}
		analysis.DescriptionAnalysis = a.AnalyzeDescription(*seoMeta.MetaDescription, focusKeyword)
	}

	// Analyze keyword density (if we have content)
	if seoMeta.FocusKeyword != nil && *seoMeta.FocusKeyword != "" {
		content := ""
		if seoMeta.MetaTitle != nil {
			content += *seoMeta.MetaTitle + " "
		}
		if seoMeta.MetaDescription != nil {
			content += *seoMeta.MetaDescription + " "
		}
		if seoMeta.MetaKeywords != nil {
			content += *seoMeta.MetaKeywords
		}
		
		if content != "" {
			analysis.KeywordAnalysis = a.AnalyzeKeywordDensity(content, *seoMeta.FocusKeyword)
		}
	}

	// Analyze readability
	content := ""
	if seoMeta.MetaTitle != nil {
		content += *seoMeta.MetaTitle + " "
	}
	if seoMeta.MetaDescription != nil {
		content += *seoMeta.MetaDescription
	}
	if content != "" {
		analysis.ReadabilityAnalysis = a.AnalyzeReadability(content)
	}

	// Analyze technical SEO
	analysis.TechnicalAnalysis = a.AnalyzeTechnicalSEO(seoMeta)

	// Generate recommendations
	analysis.Recommendations = a.GenerateRecommendations(analysis)

	return analysis, nil
}

// AnalyzeTitle analyzes meta title for SEO optimization
func (a *seoAnalyzer) AnalyzeTitle(title string, focusKeyword string) *models.TextAnalysis {
	analysis := &models.TextAnalysis{
		Length:    utf8.RuneCountInString(title),
		WordCount: len(strings.Fields(title)),
		Issues:    make([]string, 0),
		Suggestions: make([]string, 0),
	}

	// Length analysis
	if analysis.Length == 0 {
		analysis.Issues = append(analysis.Issues, "Title is empty")
		analysis.Score = 0
	} else if analysis.Length < 30 {
		analysis.Issues = append(analysis.Issues, "Title is too short (less than 30 characters)")
		analysis.Score = 20
	} else if analysis.Length > 60 {
		analysis.Issues = append(analysis.Issues, "Title is too long (more than 60 characters)")
		analysis.Score = 60
	} else {
		analysis.Score = 90
	}

	// Keyword analysis
	if focusKeyword != "" {
		lowerTitle := strings.ToLower(title)
		lowerKeyword := strings.ToLower(focusKeyword)
		
		if !strings.Contains(lowerTitle, lowerKeyword) {
			analysis.Issues = append(analysis.Issues, "Focus keyword not found in title")
			analysis.Score -= 20
		} else {
			// Check keyword position
			index := strings.Index(lowerTitle, lowerKeyword)
			if index > len(title)/2 {
				analysis.Suggestions = append(analysis.Suggestions, "Consider moving focus keyword closer to the beginning of title")
				analysis.Score -= 10
			}
		}
	}

	// Special characters analysis
	if strings.Contains(title, "|") || strings.Contains(title, "-") || strings.Contains(title, "–") {
		analysis.Score += 5 // Bonus for separators
	}

	// Word count analysis
	if analysis.WordCount < 4 {
		analysis.Suggestions = append(analysis.Suggestions, "Consider adding more descriptive words to the title")
	}

	// Ensure score is within bounds
	if analysis.Score < 0 {
		analysis.Score = 0
	}
	if analysis.Score > 100 {
		analysis.Score = 100
	}

	return analysis
}

// AnalyzeDescription analyzes meta description for SEO optimization
func (a *seoAnalyzer) AnalyzeDescription(description string, focusKeyword string) *models.TextAnalysis {
	analysis := &models.TextAnalysis{
		Length:    utf8.RuneCountInString(description),
		WordCount: len(strings.Fields(description)),
		Issues:    make([]string, 0),
		Suggestions: make([]string, 0),
	}

	// Length analysis
	if analysis.Length == 0 {
		analysis.Issues = append(analysis.Issues, "Description is empty")
		analysis.Score = 0
	} else if analysis.Length < 120 {
		analysis.Issues = append(analysis.Issues, "Description is too short (less than 120 characters)")
		analysis.Score = 30
	} else if analysis.Length > 160 {
		analysis.Issues = append(analysis.Issues, "Description is too long (more than 160 characters)")
		analysis.Score = 70
	} else {
		analysis.Score = 90
	}

	// Keyword analysis
	if focusKeyword != "" {
		lowerDescription := strings.ToLower(description)
		lowerKeyword := strings.ToLower(focusKeyword)
		
		if !strings.Contains(lowerDescription, lowerKeyword) {
			analysis.Issues = append(analysis.Issues, "Focus keyword not found in description")
			analysis.Score -= 15
		}
	}

	// Call-to-action analysis
	ctaWords := []string{"learn", "discover", "find", "get", "buy", "download", "try", "start", "explore", "read"}
	hasCallToAction := false
	lowerDescription := strings.ToLower(description)
	
	for _, cta := range ctaWords {
		if strings.Contains(lowerDescription, cta) {
			hasCallToAction = true
			break
		}
	}
	
	if hasCallToAction {
		analysis.Score += 5
	} else {
		analysis.Suggestions = append(analysis.Suggestions, "Consider adding a call-to-action to make description more compelling")
	}

	// Duplicate content check (simplified)
	words := strings.Fields(strings.ToLower(description))
	wordCount := make(map[string]int)
	for _, word := range words {
		if len(word) > 3 { // Only count meaningful words
			wordCount[word]++
		}
	}
	
	duplicateWords := 0
	for _, count := range wordCount {
		if count > 2 {
			duplicateWords++
		}
	}
	
	if duplicateWords > 2 {
		analysis.Suggestions = append(analysis.Suggestions, "Reduce repetitive words in description")
		analysis.Score -= 5
	}

	// Ensure score is within bounds
	if analysis.Score < 0 {
		analysis.Score = 0
	}
	if analysis.Score > 100 {
		analysis.Score = 100
	}

	return analysis
}

// AnalyzeKeywordDensity analyzes keyword density in content
func (a *seoAnalyzer) AnalyzeKeywordDensity(content string, keyword string) *models.KeywordAnalysis {
	if keyword == "" || content == "" {
		return &models.KeywordAnalysis{
			Density:     0,
			Count:       0,
			Variations:  make([]string, 0),
			Suggestions: make([]string, 0),
		}
	}

	words := strings.Fields(strings.ToLower(content))
	totalWords := len(words)
	keywordLower := strings.ToLower(keyword)
	
	// Count exact matches
	exactCount := 0
	for _, word := range words {
		// Remove punctuation
		cleanWord := regexp.MustCompile(`[^\w]`).ReplaceAllString(word, "")
		if cleanWord == keywordLower {
			exactCount++
		}
	}

	// Count phrase matches
	phraseCount := strings.Count(strings.ToLower(content), keywordLower)
	
	count := exactCount + phraseCount
	density := 0.0
	if totalWords > 0 {
		density = (float64(count) / float64(totalWords)) * 100
	}

	analysis := &models.KeywordAnalysis{
		Density:     density,
		Count:       count,
		Variations:  make([]string, 0),
		Suggestions: make([]string, 0),
	}

	// Generate variations
	analysis.Variations = a.generateKeywordVariations(keyword)

	// Density analysis
	if density == 0 {
		analysis.Suggestions = append(analysis.Suggestions, "Consider including the focus keyword in your content")
	} else if density < 0.5 {
		analysis.Suggestions = append(analysis.Suggestions, "Keyword density is low, consider using the keyword more naturally")
	} else if density > 3.0 {
		analysis.Suggestions = append(analysis.Suggestions, "Keyword density is too high, reduce usage to avoid keyword stuffing")
	}

	return analysis
}

// generateKeywordVariations generates keyword variations
func (a *seoAnalyzer) generateKeywordVariations(keyword string) []string {
	variations := make([]string, 0)
	
	// Add plural form
	if !strings.HasSuffix(keyword, "s") {
		variations = append(variations, keyword+"s")
	}
	
	// Add gerund form for verbs
	words := strings.Fields(keyword)
	if len(words) > 0 {
		lastWord := words[len(words)-1]
		if !strings.HasSuffix(lastWord, "ing") {
			if strings.HasSuffix(lastWord, "e") {
				variations = append(variations, keyword[:len(keyword)-1]+"ing")
			} else {
				variations = append(variations, keyword+"ing")
			}
		}
	}
	
	// Add past tense for verbs
	if len(words) > 0 {
		lastWord := words[len(words)-1]
		if !strings.HasSuffix(lastWord, "ed") {
			if strings.HasSuffix(lastWord, "e") {
				variations = append(variations, keyword+"d")
			} else {
				variations = append(variations, keyword+"ed")
			}
		}
	}

	return variations
}

// AnalyzeReadability analyzes content readability
func (a *seoAnalyzer) AnalyzeReadability(content string) *models.ReadabilityAnalysis {
	if content == "" {
		return &models.ReadabilityAnalysis{
			Score:       0,
			Grade:       "N/A",
			Issues:      []string{"No content to analyze"},
			Suggestions: []string{"Add content for readability analysis"},
		}
	}

	sentences := a.countSentences(content)
	words := len(strings.Fields(content))
	syllables := a.countSyllables(content)

	// Flesch Reading Ease Score
	var score float64
	if sentences > 0 && words > 0 {
		score = 206.835 - (1.015 * float64(words) / float64(sentences)) - (84.6 * float64(syllables) / float64(words))
	}

	grade := a.getReadingGrade(score)
	
	analysis := &models.ReadabilityAnalysis{
		Score:       score,
		Grade:       grade,
		Issues:      make([]string, 0),
		Suggestions: make([]string, 0),
	}

	// Analyze readability issues
	if score < 30 {
		analysis.Issues = append(analysis.Issues, "Text is very difficult to read")
		analysis.Suggestions = append(analysis.Suggestions, "Use shorter sentences and simpler words")
	} else if score < 50 {
		analysis.Issues = append(analysis.Issues, "Text is difficult to read")
		analysis.Suggestions = append(analysis.Suggestions, "Consider simplifying sentence structure")
	} else if score < 60 {
		analysis.Suggestions = append(analysis.Suggestions, "Text readability could be improved")
	}

	// Check average sentence length
	if sentences > 0 {
		avgSentenceLength := float64(words) / float64(sentences)
		if avgSentenceLength > 20 {
			analysis.Issues = append(analysis.Issues, "Average sentence length is too long")
			analysis.Suggestions = append(analysis.Suggestions, "Break long sentences into shorter ones")
		}
	}

	return analysis
}

// countSentences counts the number of sentences in text
func (a *seoAnalyzer) countSentences(text string) int {
	// Simple sentence counting based on punctuation
	sentenceEnders := regexp.MustCompile(`[.!?]+`)
	matches := sentenceEnders.FindAllString(text, -1)
	count := len(matches)
	
	if count == 0 && text != "" {
		count = 1 // At least one sentence if there's text
	}
	
	return count
}

// countSyllables estimates syllable count in text
func (a *seoAnalyzer) countSyllables(text string) int {
	words := strings.Fields(strings.ToLower(text))
	totalSyllables := 0
	
	for _, word := range words {
		syllables := a.estimateSyllables(word)
		totalSyllables += syllables
	}
	
	return totalSyllables
}

// estimateSyllables estimates syllables in a single word
func (a *seoAnalyzer) estimateSyllables(word string) int {
	// Remove punctuation
	word = regexp.MustCompile(`[^\w]`).ReplaceAllString(word, "")
	
	if word == "" {
		return 0
	}
	
	// Simple syllable estimation
	vowels := regexp.MustCompile(`[aeiouy]`)
	vowelGroups := vowels.FindAllString(word, -1)
	syllables := len(vowelGroups)
	
	// Adjust for silent e
	if strings.HasSuffix(word, "e") && syllables > 1 {
		syllables--
	}
	
	// Minimum one syllable per word
	if syllables < 1 {
		syllables = 1
	}
	
	return syllables
}

// getReadingGrade converts Flesch score to reading grade
func (a *seoAnalyzer) getReadingGrade(score float64) string {
	switch {
	case score >= 90:
		return "5th grade"
	case score >= 80:
		return "6th grade"
	case score >= 70:
		return "7th grade"
	case score >= 60:
		return "8th & 9th grade"
	case score >= 50:
		return "10th to 12th grade"
	case score >= 30:
		return "College level"
	default:
		return "Graduate level"
	}
}

// AnalyzeTechnicalSEO analyzes technical SEO aspects
func (a *seoAnalyzer) AnalyzeTechnicalSEO(seoMeta *models.SEOMeta) *models.TechnicalAnalysis {
	analysis := &models.TechnicalAnalysis{
		Issues:      make([]string, 0),
		Suggestions: make([]string, 0),
	}

	// Check if title exists
	analysis.HasTitle = seoMeta.MetaTitle != nil && *seoMeta.MetaTitle != ""
	if !analysis.HasTitle {
		analysis.Issues = append(analysis.Issues, "Missing meta title")
		analysis.Suggestions = append(analysis.Suggestions, "Add a descriptive meta title")
	}

	// Check if description exists
	analysis.HasDescription = seoMeta.MetaDescription != nil && *seoMeta.MetaDescription != ""
	if !analysis.HasDescription {
		analysis.Issues = append(analysis.Issues, "Missing meta description")
		analysis.Suggestions = append(analysis.Suggestions, "Add a compelling meta description")
	}

	// Check canonical URL
	analysis.HasCanonical = seoMeta.CanonicalURL != nil && *seoMeta.CanonicalURL != ""
	if !analysis.HasCanonical {
		analysis.Suggestions = append(analysis.Suggestions, "Consider adding canonical URL to prevent duplicate content issues")
	}

	// Check Open Graph tags
	analysis.HasOpenGraph = (seoMeta.OGTitle != nil && *seoMeta.OGTitle != "") ||
		(seoMeta.OGDescription != nil && *seoMeta.OGDescription != "") ||
		(seoMeta.OGImage != nil && *seoMeta.OGImage != "")
	
	if !analysis.HasOpenGraph {
		analysis.Suggestions = append(analysis.Suggestions, "Add Open Graph tags for better social media sharing")
	}

	// Check Twitter Card tags
	analysis.HasTwitterCard = (seoMeta.TwitterTitle != nil && *seoMeta.TwitterTitle != "") ||
		(seoMeta.TwitterDescription != nil && *seoMeta.TwitterDescription != "") ||
		(seoMeta.TwitterImage != nil && *seoMeta.TwitterImage != "")
	
	if !analysis.HasTwitterCard {
		analysis.Suggestions = append(analysis.Suggestions, "Add Twitter Card tags for better Twitter sharing")
	}

	// Check structured data
	analysis.HasStructuredData = seoMeta.SchemaType != nil && *seoMeta.SchemaType != "" && len(seoMeta.SchemaData) > 0
	if !analysis.HasStructuredData {
		analysis.Suggestions = append(analysis.Suggestions, "Add structured data (Schema.org) for better search engine understanding")
	}

	// Check robots meta
	if seoMeta.MetaRobots == "noindex" || seoMeta.MetaRobots == "noindex,nofollow" {
		analysis.Issues = append(analysis.Issues, "Page is set to noindex")
	}

	// Check indexing settings
	if !seoMeta.IsIndexed {
		analysis.Issues = append(analysis.Issues, "Page is excluded from indexing")
	}

	return analysis
}

// GenerateRecommendations generates SEO recommendations based on analysis
func (a *seoAnalyzer) GenerateRecommendations(analysis *models.SEOAnalysis) []string {
	recommendations := make([]string, 0)

	// Title recommendations
	if analysis.TitleAnalysis != nil {
		if analysis.TitleAnalysis.Score < 70 {
			recommendations = append(recommendations, "Optimize meta title for better SEO performance")
		}
		recommendations = append(recommendations, analysis.TitleAnalysis.Suggestions...)
	}

	// Description recommendations
	if analysis.DescriptionAnalysis != nil {
		if analysis.DescriptionAnalysis.Score < 70 {
			recommendations = append(recommendations, "Improve meta description to increase click-through rates")
		}
		recommendations = append(recommendations, analysis.DescriptionAnalysis.Suggestions...)
	}

	// Keyword recommendations
	if analysis.KeywordAnalysis != nil {
		recommendations = append(recommendations, analysis.KeywordAnalysis.Suggestions...)
	}

	// Readability recommendations
	if analysis.ReadabilityAnalysis != nil {
		if analysis.ReadabilityAnalysis.Score < 60 {
			recommendations = append(recommendations, "Improve content readability for better user experience")
		}
		recommendations = append(recommendations, analysis.ReadabilityAnalysis.Suggestions...)
	}

	// Technical recommendations
	if analysis.TechnicalAnalysis != nil {
		recommendations = append(recommendations, analysis.TechnicalAnalysis.Suggestions...)
	}

	// Remove duplicates
	uniqueRecommendations := make([]string, 0)
	seen := make(map[string]bool)
	
	for _, rec := range recommendations {
		if !seen[rec] {
			uniqueRecommendations = append(uniqueRecommendations, rec)
			seen[rec] = true
		}
	}

	return uniqueRecommendations
}