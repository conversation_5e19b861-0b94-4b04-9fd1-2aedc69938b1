package services

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"unicode/utf8"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
)

// SEOOptimizer defines the interface for SEO optimization
type SEOOptimizer interface {
	OptimizeSEOMeta(ctx context.Context, seoMeta *models.SEOMeta, suggestions *models.SEOSuggestions) (*models.SEOMeta, error)
	OptimizeTitle(title string, focusKeyword string, maxLength int) string
	OptimizeDescription(description string, focusKeyword string, maxLength int) string
	GenerateKeywordVariations(keyword string) []string
	OptimizeKeywordDensity(content string, targetKeyword string, targetDensity float64) string
	GenerateSchemaData(seoMeta *models.SEOMeta, schemaType string) (models.SchemaData, error)
	OptimizeForLocalSEO(seoMeta *models.SEOMeta, location string) (*models.SEOMeta, error)
	OptimizeForMobileSEO(seoMeta *models.SEOMeta) (*models.SEOMeta, error)
}

// seoOptimizer implements SEOOptimizer
type seoOptimizer struct{}

// NewSEOOptimizer creates a new SEO optimizer
func NewSEOOptimizer() SEOOptimizer {
	return &seoOptimizer{}
}

// OptimizeSEOMeta optimizes SEO meta based on suggestions
func (o *seoOptimizer) OptimizeSEOMeta(ctx context.Context, seoMeta *models.SEOMeta, suggestions *models.SEOSuggestions) (*models.SEOMeta, error) {
	optimized := *seoMeta // Copy the original

	// Apply title optimization
	if suggestions.Title != nil {
		optimized.MetaTitle = suggestions.Title
	} else if seoMeta.MetaTitle != nil && seoMeta.FocusKeyword != nil {
		optimizedTitle := o.OptimizeTitle(*seoMeta.MetaTitle, *seoMeta.FocusKeyword, 60)
		optimized.MetaTitle = &optimizedTitle
	}

	// Apply description optimization
	if suggestions.Description != nil {
		optimized.MetaDescription = suggestions.Description
	} else if seoMeta.MetaDescription != nil && seoMeta.FocusKeyword != nil {
		optimizedDescription := o.OptimizeDescription(*seoMeta.MetaDescription, *seoMeta.FocusKeyword, 160)
		optimized.MetaDescription = &optimizedDescription
	}

	// Apply keyword optimization
	if len(suggestions.Keywords) > 0 {
		keywords := strings.Join(suggestions.Keywords, ", ")
		optimized.MetaKeywords = &keywords
	}

	// Apply structured data optimization
	if suggestions.StructuredData != nil {
		optimized.SchemaData = *suggestions.StructuredData
	} else if seoMeta.SchemaType != nil {
		schemaData, err := o.GenerateSchemaData(seoMeta, *seoMeta.SchemaType)
		if err == nil {
			optimized.SchemaData = schemaData
		}
	}

	// Apply additional meta tags
	if suggestions.AdditionalMeta != nil {
		optimized.AdditionalMeta = *suggestions.AdditionalMeta
	}

	// Optimize Open Graph tags if not already set
	if optimized.OGTitle == nil && optimized.MetaTitle != nil {
		optimized.OGTitle = optimized.MetaTitle
	}
	if optimized.OGDescription == nil && optimized.MetaDescription != nil {
		optimized.OGDescription = optimized.MetaDescription
	}

	// Optimize Twitter Card tags if not already set
	if optimized.TwitterTitle == nil && optimized.MetaTitle != nil {
		optimized.TwitterTitle = optimized.MetaTitle
	}
	if optimized.TwitterDescription == nil && optimized.MetaDescription != nil {
		optimized.TwitterDescription = optimized.MetaDescription
	}

	// Set default Twitter Card type if not set
	if optimized.TwitterCard == "" {
		if optimized.TwitterImage != nil && *optimized.TwitterImage != "" {
			optimized.TwitterCard = "summary_large_image"
		} else {
			optimized.TwitterCard = "summary"
		}
	}

	return &optimized, nil
}

// OptimizeTitle optimizes a meta title for SEO
func (o *seoOptimizer) OptimizeTitle(title string, focusKeyword string, maxLength int) string {
	if title == "" {
		return title
	}

	// Remove excessive whitespace
	title = regexp.MustCompile(`\s+`).ReplaceAllString(strings.TrimSpace(title), " ")

	// Ensure focus keyword is included at the beginning if possible
	if focusKeyword != "" {
		lowerTitle := strings.ToLower(title)
		lowerKeyword := strings.ToLower(focusKeyword)
		
		if !strings.Contains(lowerTitle, lowerKeyword) {
			// Add keyword at the beginning
			title = focusKeyword + " - " + title
		} else {
			// Move keyword to the beginning if it's not already there
			index := strings.Index(lowerTitle, lowerKeyword)
			if index > 20 { // If keyword is not near the beginning
				// Extract the keyword with proper capitalization
				actualKeyword := title[index : index+len(focusKeyword)]
				// Remove keyword from current position
				title = strings.Replace(title, actualKeyword, "", 1)
				title = strings.TrimSpace(title)
				// Add at the beginning
				title = actualKeyword + " - " + title
			}
		}
	}

	// Truncate if too long, preserving whole words
	if utf8.RuneCountInString(title) > maxLength {
		title = o.truncateToLength(title, maxLength-3) + "..."
	}

	return title
}

// OptimizeDescription optimizes a meta description for SEO
func (o *seoOptimizer) OptimizeDescription(description string, focusKeyword string, maxLength int) string {
	if description == "" {
		return description
	}

	// Remove excessive whitespace
	description = regexp.MustCompile(`\s+`).ReplaceAllString(strings.TrimSpace(description), " ")

	// Ensure focus keyword is included
	if focusKeyword != "" {
		lowerDescription := strings.ToLower(description)
		lowerKeyword := strings.ToLower(focusKeyword)
		
		if !strings.Contains(lowerDescription, lowerKeyword) {
			// Add keyword naturally at the beginning
			description = fmt.Sprintf("Discover %s. %s", focusKeyword, description)
		}
	}

	// Add call-to-action if missing
	if !o.hasCallToAction(description) {
		ctaWords := []string{"Learn more", "Discover", "Find out", "Get started", "Explore"}
		cta := ctaWords[len(description)%len(ctaWords)] // Simple selection
		description = description + " " + cta + " today."
	}

	// Truncate if too long, preserving whole words
	if utf8.RuneCountInString(description) > maxLength {
		description = o.truncateToLength(description, maxLength-3) + "..."
	}

	return description
}

// GenerateKeywordVariations generates keyword variations
func (o *seoOptimizer) GenerateKeywordVariations(keyword string) []string {
	if keyword == "" {
		return []string{}
	}

	variations := make([]string, 0)
	words := strings.Fields(keyword)

	// Add plural forms
	for _, word := range words {
		if !strings.HasSuffix(word, "s") {
			plural := word + "s"
			variations = append(variations, strings.Replace(keyword, word, plural, 1))
		}
	}

	// Add gerund forms
	for _, word := range words {
		if !strings.HasSuffix(word, "ing") {
			var gerund string
			if strings.HasSuffix(word, "e") {
				gerund = word[:len(word)-1] + "ing"
			} else {
				gerund = word + "ing"
			}
			variations = append(variations, strings.Replace(keyword, word, gerund, 1))
		}
	}

	// Add past tense forms
	for _, word := range words {
		if !strings.HasSuffix(word, "ed") {
			var pastTense string
			if strings.HasSuffix(word, "e") {
				pastTense = word + "d"
			} else {
				pastTense = word + "ed"
			}
			variations = append(variations, strings.Replace(keyword, word, pastTense, 1))
		}
	}

	// Add synonyms for common words
	synonyms := map[string][]string{
		"best":     {"top", "finest", "premier", "excellent"},
		"guide":    {"tutorial", "handbook", "manual"},
		"tips":     {"advice", "suggestions", "recommendations"},
		"how":      {"ways to", "methods to"},
		"free":     {"complimentary", "no-cost"},
		"easy":     {"simple", "quick", "effortless"},
		"complete": {"comprehensive", "full", "total"},
	}

	for _, word := range words {
		if syns, exists := synonyms[strings.ToLower(word)]; exists {
			for _, syn := range syns {
				variations = append(variations, strings.Replace(keyword, word, syn, 1))
			}
		}
	}

	// Remove duplicates and return
	unique := make(map[string]bool)
	result := make([]string, 0)
	
	for _, variation := range variations {
		if !unique[variation] && variation != keyword {
			unique[variation] = true
			result = append(result, variation)
		}
	}

	return result
}

// OptimizeKeywordDensity optimizes keyword density in content
func (o *seoOptimizer) OptimizeKeywordDensity(content string, targetKeyword string, targetDensity float64) string {
	if content == "" || targetKeyword == "" {
		return content
	}

	words := strings.Fields(content)
	totalWords := len(words)
	keywordCount := o.countKeywordOccurrences(content, targetKeyword)
	currentDensity := (float64(keywordCount) / float64(totalWords)) * 100

	// If density is already optimal (within 0.5% of target), return as is
	if currentDensity >= targetDensity-0.5 && currentDensity <= targetDensity+0.5 {
		return content
	}

	// If density is too low, add keyword variations
	if currentDensity < targetDensity {
		variations := o.GenerateKeywordVariations(targetKeyword)
		if len(variations) > 0 {
			// Add a few variations naturally
			for i, variation := range variations {
				if i >= 3 { // Limit to 3 variations
					break
				}
				content += fmt.Sprintf(" %s is important for your success.", variation)
			}
		}
	}

	// If density is too high, replace some keyword instances with variations
	if currentDensity > targetDensity+1.0 {
		variations := o.GenerateKeywordVariations(targetKeyword)
		if len(variations) > 0 {
			// Replace every 3rd occurrence with a variation
			content = o.replaceKeywordWithVariations(content, targetKeyword, variations, 3)
		}
	}

	return content
}

// GenerateSchemaData generates structured data for SEO
func (o *seoOptimizer) GenerateSchemaData(seoMeta *models.SEOMeta, schemaType string) (models.SchemaData, error) {
	schema := make(models.SchemaData)

	switch schemaType {
	case "Article":
		schema["@context"] = "https://schema.org"
		schema["@type"] = "Article"
		if seoMeta.MetaTitle != nil {
			schema["headline"] = *seoMeta.MetaTitle
		}
		if seoMeta.MetaDescription != nil {
			schema["description"] = *seoMeta.MetaDescription
		}
		if seoMeta.OGImage != nil {
			schema["image"] = *seoMeta.OGImage
		}
		schema["datePublished"] = seoMeta.CreatedAt.Format("2006-01-02")
		schema["dateModified"] = seoMeta.UpdatedAt.Format("2006-01-02")

	case "WebPage":
		schema["@context"] = "https://schema.org"
		schema["@type"] = "WebPage"
		if seoMeta.MetaTitle != nil {
			schema["name"] = *seoMeta.MetaTitle
		}
		if seoMeta.MetaDescription != nil {
			schema["description"] = *seoMeta.MetaDescription
		}
		if seoMeta.CanonicalURL != nil {
			schema["url"] = *seoMeta.CanonicalURL
		}

	case "Organization":
		schema["@context"] = "https://schema.org"
		schema["@type"] = "Organization"
		if seoMeta.OGSiteName != nil {
			schema["name"] = *seoMeta.OGSiteName
		}
		if seoMeta.MetaDescription != nil {
			schema["description"] = *seoMeta.MetaDescription
		}
		if seoMeta.OGImage != nil {
			schema["logo"] = map[string]interface{}{
				"@type": "ImageObject",
				"url":   *seoMeta.OGImage,
			}
		}

	case "Product":
		schema["@context"] = "https://schema.org"
		schema["@type"] = "Product"
		if seoMeta.MetaTitle != nil {
			schema["name"] = *seoMeta.MetaTitle
		}
		if seoMeta.MetaDescription != nil {
			schema["description"] = *seoMeta.MetaDescription
		}
		if seoMeta.OGImage != nil {
			schema["image"] = *seoMeta.OGImage
		}

	case "LocalBusiness":
		schema["@context"] = "https://schema.org"
		schema["@type"] = "LocalBusiness"
		if seoMeta.OGSiteName != nil {
			schema["name"] = *seoMeta.OGSiteName
		}
		if seoMeta.MetaDescription != nil {
			schema["description"] = *seoMeta.MetaDescription
		}

	default:
		return nil, fmt.Errorf("unsupported schema type: %s", schemaType)
	}

	return schema, nil
}

// OptimizeForLocalSEO optimizes SEO meta for local search
func (o *seoOptimizer) OptimizeForLocalSEO(seoMeta *models.SEOMeta, location string) (*models.SEOMeta, error) {
	optimized := *seoMeta

	// Add location to title if not present
	if optimized.MetaTitle != nil && location != "" {
		title := *optimized.MetaTitle
		if !strings.Contains(strings.ToLower(title), strings.ToLower(location)) {
			title = title + " in " + location
			optimized.MetaTitle = &title
		}
	}

	// Add location to description if not present
	if optimized.MetaDescription != nil && location != "" {
		description := *optimized.MetaDescription
		if !strings.Contains(strings.ToLower(description), strings.ToLower(location)) {
			description = description + " Located in " + location + "."
			optimized.MetaDescription = &description
		}
	}

	// Add local business schema
	if optimized.SchemaType == nil || *optimized.SchemaType != "LocalBusiness" {
		schemaType := "LocalBusiness"
		optimized.SchemaType = &schemaType
		
		localSchema, err := o.GenerateSchemaData(&optimized, "LocalBusiness")
		if err == nil {
			if location != "" {
				localSchema["address"] = map[string]interface{}{
					"@type":          "PostalAddress",
					"addressLocality": location,
				}
			}
			optimized.SchemaData = localSchema
		}
	}

	return &optimized, nil
}

// OptimizeForMobileSEO optimizes SEO meta for mobile search
func (o *seoOptimizer) OptimizeForMobileSEO(seoMeta *models.SEOMeta) (*models.SEOMeta, error) {
	optimized := *seoMeta

	// Ensure viewport meta tag is present
	if optimized.AdditionalMeta == nil {
		optimized.AdditionalMeta = make(models.AdditionalMetaTags, 0)
	}

	// Add viewport meta tag if not already present
	hasViewport := false
	for _, meta := range optimized.AdditionalMeta {
		if meta.Name == "viewport" {
			hasViewport = true
			break
		}
	}
	if !hasViewport {
		optimized.AdditionalMeta = append(optimized.AdditionalMeta, models.MetaTag{
			Name:    "viewport",
			Content: "width=device-width, initial-scale=1.0",
		})
	}

	// Add mobile-friendly meta tags
	optimized.AdditionalMeta = append(optimized.AdditionalMeta,
		models.MetaTag{Name: "mobile-web-app-capable", Content: "yes"},
		models.MetaTag{Name: "apple-mobile-web-app-capable", Content: "yes"},
		models.MetaTag{Name: "apple-mobile-web-app-status-bar-style", Content: "default"},
	)

	// Add mobile-friendly meta tags
	if optimized.OGImage != nil {
		optimized.AdditionalMeta = append(optimized.AdditionalMeta, models.MetaTag{
			Name:    "twitter:image:alt",
			Content: "Mobile-optimized image",
		})
	}

	// Optimize title for mobile (shorter)
	if optimized.MetaTitle != nil {
		title := *optimized.MetaTitle
		if utf8.RuneCountInString(title) > 50 {
			title = o.truncateToLength(title, 47) + "..."
			optimized.MetaTitle = &title
		}
	}

	// Optimize description for mobile
	if optimized.MetaDescription != nil {
		description := *optimized.MetaDescription
		if utf8.RuneCountInString(description) > 120 {
			description = o.truncateToLength(description, 117) + "..."
			optimized.MetaDescription = &description
		}
	}

	// AdditionalMeta already updated above
	return &optimized, nil
}

// Helper methods

// truncateToLength truncates text to specified length preserving whole words
func (o *seoOptimizer) truncateToLength(text string, maxLength int) string {
	if utf8.RuneCountInString(text) <= maxLength {
		return text
	}

	words := strings.Fields(text)
	result := ""
	
	for _, word := range words {
		if utf8.RuneCountInString(result+" "+word) > maxLength {
			break
		}
		if result != "" {
			result += " "
		}
		result += word
	}

	return result
}

// hasCallToAction checks if text contains call-to-action words
func (o *seoOptimizer) hasCallToAction(text string) bool {
	ctaWords := []string{
		"learn", "discover", "find", "get", "buy", "download", "try", "start",
		"explore", "read", "click", "visit", "contact", "call", "sign up",
		"register", "subscribe", "book", "order", "shop", "purchase",
	}

	lowerText := strings.ToLower(text)
	for _, cta := range ctaWords {
		if strings.Contains(lowerText, cta) {
			return true
		}
	}

	return false
}

// countKeywordOccurrences counts keyword occurrences in text
func (o *seoOptimizer) countKeywordOccurrences(text string, keyword string) int {
	lowerText := strings.ToLower(text)
	lowerKeyword := strings.ToLower(keyword)
	
	// Count exact phrase matches
	phraseCount := strings.Count(lowerText, lowerKeyword)
	
	// Count individual word matches
	words := strings.Fields(lowerText)
	keywordWords := strings.Fields(lowerKeyword)
	
	if len(keywordWords) == 1 {
		// Single word keyword
		wordCount := 0
		for _, word := range words {
			cleanWord := regexp.MustCompile(`[^\w]`).ReplaceAllString(word, "")
			if cleanWord == lowerKeyword {
				wordCount++
			}
		}
		return wordCount + phraseCount
	}

	return phraseCount
}

// replaceKeywordWithVariations replaces every nth keyword occurrence with variations
func (o *seoOptimizer) replaceKeywordWithVariations(content string, keyword string, variations []string, nth int) string {
	if len(variations) == 0 {
		return content
	}

	words := strings.Fields(content)
	keywordCount := 0
	variationIndex := 0

	for i, word := range words {
		cleanWord := regexp.MustCompile(`[^\w]`).ReplaceAllString(strings.ToLower(word), "")
		if cleanWord == strings.ToLower(keyword) {
			keywordCount++
			if keywordCount%nth == 0 {
				// Replace with variation, preserving original capitalization
				variation := variations[variationIndex%len(variations)]
				if strings.Title(word) == word {
					variation = strings.Title(variation)
				} else if strings.ToUpper(word) == word {
					variation = strings.ToUpper(variation)
				}
				words[i] = variation
				variationIndex++
			}
		}
	}

	return strings.Join(words, " ")
}