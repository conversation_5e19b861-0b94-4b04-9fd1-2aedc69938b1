package services

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/repositories"
	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

var (
	ErrSEOMetaNotFound      = errors.New("SEO metadata not found")
	ErrDuplicateSEOMeta     = errors.New("SEO metadata already exists for this page")
	ErrInvalidPageReference = errors.New("invalid page reference")
)

// SEOMetaListResponse represents the response for SEO metadata list operations
type SEOMetaListResponse struct {
	SEOMetas   []*models.SEOMetaResponse `json:"seo_metas"`
	Total      int                       `json:"total"`
	Page       int                       `json:"page"`
	PageSize   int                       `json:"page_size"`
	TotalPages int                       `json:"total_pages"`
}

// SEOMetaService interface defines SEO metadata service methods
type SEOMetaService interface {
	CreateMeta(ctx context.Context, req *models.CreateSEOMetaRequest) (*models.SEOMetaResponse, error)
	GetMetaByID(ctx context.Context, id, tenantID uint) (*models.SEOMetaResponse, error)
	GetMetaByPage(ctx context.Context, pageType string, pageID *uint, pageURL string, tenantID uint) (*models.SEOMetaResponse, error)
	UpdateMeta(ctx context.Context, id uint, req *models.UpdateSEOMetaRequest, tenantID uint) (*models.SEOMetaResponse, error)
	DeleteMeta(ctx context.Context, id, tenantID uint) error
	ListMeta(ctx context.Context, filter *models.SEOMetaFilter, page, limit int) (*SEOMetaListResponse, error)
	BulkCreateMeta(ctx context.Context, requests []models.CreateSEOMetaRequest) ([]models.SEOMetaResponse, error)
	AnalyzeMeta(ctx context.Context, id, tenantID uint) (*models.SEOAnalysis, error)
	ValidateMeta(ctx context.Context, id, tenantID uint) (*models.SEOValidationResult, error)
	GenerateMetaTags(ctx context.Context, id, tenantID uint) ([]string, string, error)
}

// seoMetaService implements SEOMetaService interface
type seoMetaService struct {
	seoMetaRepo repositories.SEOMetaRepository
	logger      utils.Logger
}

// NewSEOMetaService creates a new SEO meta service instance
func NewSEOMetaService(
	seoMetaRepo repositories.SEOMetaRepository,
	logger utils.Logger,
) SEOMetaService {
	return &seoMetaService{
		seoMetaRepo: seoMetaRepo,
		logger:      logger,
	}
}

// CreateMeta creates new SEO metadata
func (s *seoMetaService) CreateMeta(ctx context.Context, req *models.CreateSEOMetaRequest) (*models.SEOMetaResponse, error) {
	// Get tenant context
	tenantCtx, err := pkgcontext.FromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("tenant context is required: %w", err)
	}
	tenantID := tenantCtx.GetScope()
	
	// Check if SEO metadata already exists for this page
	var existing *models.SEOMeta
	
	if req.PageID != nil {
		existing, _ = s.seoMetaRepo.GetByPageIdentifier(ctx, tenantID, req.WebsiteID, req.PageType, *req.PageID)
	} else {
		existing, _ = s.seoMetaRepo.GetByPageURL(ctx, tenantID, req.WebsiteID, req.PageURL)
	}
	
	if existing != nil {
		return nil, ErrDuplicateSEOMeta
	}

	// Create SEO metadata model
	seoMeta := &models.SEOMeta{
		WebsiteID:  req.WebsiteID,
		TenantID:   tenantID,
		PageType:   req.PageType,
		PageID:     req.PageID,
		PageURL:    req.PageURL,
		PagePath:   req.PagePath,
		Status:     models.StatusActive,
	}

	// Set meta fields
	if req.MetaTitle != nil {
		seoMeta.MetaTitle = req.MetaTitle
	}
	if req.MetaDescription != nil {
		seoMeta.MetaDescription = req.MetaDescription
	}
	if req.MetaKeywords != nil {
		seoMeta.MetaKeywords = req.MetaKeywords
	}
	if req.MetaRobots != "" {
		seoMeta.MetaRobots = req.MetaRobots
	} else {
		seoMeta.MetaRobots = "index,follow"
	}
	if req.CanonicalURL != nil {
		seoMeta.CanonicalURL = req.CanonicalURL
	}

	// Set Open Graph fields
	if req.OGTitle != nil {
		seoMeta.OGTitle = req.OGTitle
	}
	if req.OGDescription != nil {
		seoMeta.OGDescription = req.OGDescription
	}
	if req.OGImage != nil {
		seoMeta.OGImage = req.OGImage
	}
	if req.OGType != "" {
		seoMeta.OGType = req.OGType
	} else {
		// Set default OG type based on page type
		switch req.PageType {
		case models.PageTypePost:
			seoMeta.OGType = models.OGTypeArticle
		case models.PageTypeProduct:
			seoMeta.OGType = models.OGTypeProduct
		default:
			seoMeta.OGType = models.OGTypeWebsite
		}
	}
	if req.OGURL != nil {
		seoMeta.OGURL = req.OGURL
	}
	if req.OGSiteName != nil {
		seoMeta.OGSiteName = req.OGSiteName
	}
	if req.OGLocale != "" {
		seoMeta.OGLocale = req.OGLocale
	} else {
		seoMeta.OGLocale = "en_US"
	}

	// Set Twitter Card fields
	if req.TwitterCard != "" {
		seoMeta.TwitterCard = req.TwitterCard
	} else {
		seoMeta.TwitterCard = models.TwitterCardSummary
	}
	if req.TwitterTitle != nil {
		seoMeta.TwitterTitle = req.TwitterTitle
	}
	if req.TwitterDescription != nil {
		seoMeta.TwitterDescription = req.TwitterDescription
	}
	if req.TwitterImage != nil {
		seoMeta.TwitterImage = req.TwitterImage
	}
	if req.TwitterCreator != nil {
		seoMeta.TwitterCreator = req.TwitterCreator
	}
	if req.TwitterSite != nil {
		seoMeta.TwitterSite = req.TwitterSite
	}

	// Set schema data
	if req.SchemaType != nil {
		seoMeta.SchemaType = req.SchemaType
	}
	if req.SchemaData != nil {
		seoMeta.SchemaData = *req.SchemaData
	}

	// Set additional meta tags
	if req.AdditionalMeta != nil {
		seoMeta.AdditionalMeta = *req.AdditionalMeta
	}

	// Set SEO settings
	if req.FocusKeyword != nil {
		seoMeta.FocusKeyword = req.FocusKeyword
	}

	// Set indexing settings
	if req.IsIndexed != nil {
		seoMeta.IsIndexed = *req.IsIndexed
	} else {
		seoMeta.IsIndexed = true
	}
	if req.IsSitemapIncluded != nil {
		seoMeta.IsSitemapIncluded = *req.IsSitemapIncluded
	} else {
		seoMeta.IsSitemapIncluded = true
	}
	if req.Priority != nil {
		seoMeta.Priority = *req.Priority
	} else {
		seoMeta.Priority = 0.5
	}
	if req.ChangeFrequency != nil {
		seoMeta.ChangeFrequency = *req.ChangeFrequency
	} else {
		seoMeta.ChangeFrequency = models.ChangeFrequencyWeekly
	}

	// Validate the model
	if err := seoMeta.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Create in repository
	err = s.seoMetaRepo.Create(ctx, seoMeta)
	if err != nil {
		s.logger.Error("Failed to create SEO meta", "error", err, "tenant_id", tenantID)
		return nil, fmt.Errorf("failed to create SEO metadata: %w", err)
	}

	// Retrieve the created metadata
	createdMeta, err := s.seoMetaRepo.GetByID(ctx, tenantID, seoMeta.ID)
	if err != nil {
		s.logger.Error("Failed to retrieve created SEO meta", "error", err, "id", seoMeta.ID)
		return nil, fmt.Errorf("failed to retrieve created SEO metadata: %w", err)
	}

	// Generate response
	return s.buildSEOMetaResponse(createdMeta), nil
}

// GetMetaByID retrieves SEO metadata by ID
func (s *seoMetaService) GetMetaByID(ctx context.Context, id, tenantID uint) (*models.SEOMetaResponse, error) {
	seoMeta, err := s.seoMetaRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, ErrSEOMetaNotFound
	}

	return s.buildSEOMetaResponse(seoMeta), nil
}

// GetMetaByPage retrieves SEO metadata by page identifier
func (s *seoMetaService) GetMetaByPage(ctx context.Context, pageType string, pageID *uint, pageURL string, tenantID uint) (*models.SEOMetaResponse, error) {
	var seoMeta *models.SEOMeta
	var err error
	
	// Use websiteID = 0 as fallback since the existing method doesn't specify it
	// This should be enhanced to properly pass websiteID
	if pageID != nil {
		seoMeta, err = s.seoMetaRepo.GetByPageIdentifier(ctx, tenantID, 0, pageType, *pageID)
	} else {
		seoMeta, err = s.seoMetaRepo.GetByPageURL(ctx, tenantID, 0, pageURL)
	}
	
	if err != nil {
		return nil, ErrSEOMetaNotFound
	}

	return s.buildSEOMetaResponse(seoMeta), nil
}

// UpdateMeta updates existing SEO metadata
func (s *seoMetaService) UpdateMeta(ctx context.Context, id uint, req *models.UpdateSEOMetaRequest, tenantID uint) (*models.SEOMetaResponse, error) {
	// Get existing metadata
	existing, err := s.seoMetaRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, ErrSEOMetaNotFound
	}

	// Update fields if provided
	if req.MetaTitle != nil {
		existing.MetaTitle = req.MetaTitle
	}
	if req.MetaDescription != nil {
		existing.MetaDescription = req.MetaDescription
	}
	if req.MetaKeywords != nil {
		existing.MetaKeywords = req.MetaKeywords
	}
	if req.MetaRobots != nil {
		existing.MetaRobots = *req.MetaRobots
	}
	if req.CanonicalURL != nil {
		existing.CanonicalURL = req.CanonicalURL
	}

	// Update Open Graph fields
	if req.OGTitle != nil {
		existing.OGTitle = req.OGTitle
	}
	if req.OGDescription != nil {
		existing.OGDescription = req.OGDescription
	}
	if req.OGImage != nil {
		existing.OGImage = req.OGImage
	}
	if req.OGType != nil {
		existing.OGType = *req.OGType
	}
	if req.OGURL != nil {
		existing.OGURL = req.OGURL
	}
	if req.OGSiteName != nil {
		existing.OGSiteName = req.OGSiteName
	}
	if req.OGLocale != nil {
		existing.OGLocale = *req.OGLocale
	}

	// Update Twitter Card fields
	if req.TwitterCard != nil {
		existing.TwitterCard = *req.TwitterCard
	}
	if req.TwitterTitle != nil {
		existing.TwitterTitle = req.TwitterTitle
	}
	if req.TwitterDescription != nil {
		existing.TwitterDescription = req.TwitterDescription
	}
	if req.TwitterImage != nil {
		existing.TwitterImage = req.TwitterImage
	}
	if req.TwitterCreator != nil {
		existing.TwitterCreator = req.TwitterCreator
	}
	if req.TwitterSite != nil {
		existing.TwitterSite = req.TwitterSite
	}

	// Update schema data
	if req.SchemaType != nil {
		existing.SchemaType = req.SchemaType
	}
	if req.SchemaData != nil {
		existing.SchemaData = *req.SchemaData
	}

	// Update additional meta tags
	if req.AdditionalMeta != nil {
		existing.AdditionalMeta = *req.AdditionalMeta
	}

	// Update SEO settings
	if req.FocusKeyword != nil {
		existing.FocusKeyword = req.FocusKeyword
	}

	// Update indexing settings
	if req.IsIndexed != nil {
		existing.IsIndexed = *req.IsIndexed
	}
	if req.IsSitemapIncluded != nil {
		existing.IsSitemapIncluded = *req.IsSitemapIncluded
	}
	if req.Priority != nil {
		existing.Priority = *req.Priority
	}
	if req.ChangeFrequency != nil {
		existing.ChangeFrequency = *req.ChangeFrequency
	}

	// Update status
	if req.Status != nil {
		existing.Status = *req.Status
	}

	// Validate the updated model
	if err := existing.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Update in repository
	err = s.seoMetaRepo.Update(ctx, tenantID, id, existing)
	if err != nil {
		s.logger.Error("Failed to update SEO meta", "error", err, "id", id, "tenant_id", tenantID)
		return nil, fmt.Errorf("failed to update SEO metadata: %w", err)
	}

	// Retrieve the updated metadata
	updatedMeta, err := s.seoMetaRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		s.logger.Error("Failed to retrieve updated SEO meta", "error", err, "id", id)
		return nil, fmt.Errorf("failed to retrieve updated SEO metadata: %w", err)
	}

	return s.buildSEOMetaResponse(updatedMeta), nil
}

// DeleteMeta deletes SEO metadata
func (s *seoMetaService) DeleteMeta(ctx context.Context, id, tenantID uint) error {
	// Check if metadata exists
	_, err := s.seoMetaRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return ErrSEOMetaNotFound
	}

	// Delete from repository
	err = s.seoMetaRepo.Delete(ctx, tenantID, id)
	if err != nil {
		s.logger.Error("Failed to delete SEO meta", "error", err, "id", id, "tenant_id", tenantID)
		return fmt.Errorf("failed to delete SEO metadata: %w", err)
	}

	return nil
}

// ListMeta lists SEO metadata with filtering and pagination
func (s *seoMetaService) ListMeta(ctx context.Context, filter *models.SEOMetaFilter, page, limit int) (*SEOMetaListResponse, error) {
	// Get tenant context
	tenantCtx, err := pkgcontext.FromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("tenant context is required: %w", err)
	}
	tenantID := tenantCtx.GetScope()
	
	// Create cursor for pagination
	cursor := &pagination.Cursor{}
	if page > 1 {
		cursor.ID = int64((page - 1) * limit)
	}

	seoMetas, _, err := s.seoMetaRepo.List(ctx, tenantID, filter, cursor)
	if err != nil {
		s.logger.Error("Failed to list SEO meta", "error", err, "tenant_id", tenantID)
		return nil, fmt.Errorf("failed to list SEO metadata: %w", err)
	}

	// Convert to response format
	responses := make([]*models.SEOMetaResponse, len(seoMetas))
	for i, meta := range seoMetas {
		responses[i] = s.buildSEOMetaResponse(meta)
	}

	// Calculate pagination
	total := len(responses)
	totalPages := (total + limit - 1) / limit
	if totalPages == 0 && total > 0 {
		totalPages = 1
	}

	return &SEOMetaListResponse{
		SEOMetas:   responses,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
	}, nil
}

// BulkCreateMeta creates multiple SEO metadata entries
func (s *seoMetaService) BulkCreateMeta(ctx context.Context, requests []models.CreateSEOMetaRequest) ([]models.SEOMetaResponse, error) {
	responses := make([]models.SEOMetaResponse, 0, len(requests))

	for _, req := range requests {
		response, err := s.CreateMeta(ctx, &req)
		if err != nil {
			s.logger.Error("Failed to create SEO meta in bulk", "error", err, "page_url", req.PageURL)
			continue // Skip failed entries
		}
		responses = append(responses, *response)
	}

	return responses, nil
}

// AnalyzeMeta analyzes SEO metadata and provides recommendations
func (s *seoMetaService) AnalyzeMeta(ctx context.Context, id, tenantID uint) (*models.SEOAnalysis, error) {
	seoMeta, err := s.seoMetaRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, ErrSEOMetaNotFound
	}

	analysis := &models.SEOAnalysis{
		OverallScore: 0,
	}

	// Analyze title
	if seoMeta.MetaTitle != nil {
		analysis.TitleAnalysis = s.analyzeTitle(*seoMeta.MetaTitle)
	}

	// Analyze description
	if seoMeta.MetaDescription != nil {
		analysis.DescriptionAnalysis = s.analyzeDescription(*seoMeta.MetaDescription)
	}

	// Analyze keywords
	if seoMeta.FocusKeyword != nil {
		analysis.KeywordAnalysis = s.analyzeKeywords(*seoMeta.FocusKeyword, seoMeta)
	}

	// Technical analysis
	analysis.TechnicalAnalysis = s.analyzeTechnical(seoMeta)

	// Calculate overall score
	analysis.OverallScore = s.calculateOverallScore(analysis)

	// Generate recommendations
	analysis.Recommendations = s.generateRecommendations(analysis, seoMeta)

	return analysis, nil
}

// ValidateMeta validates SEO metadata
func (s *seoMetaService) ValidateMeta(ctx context.Context, id, tenantID uint) (*models.SEOValidationResult, error) {
	seoMeta, err := s.seoMetaRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, ErrSEOMetaNotFound
	}

	result := &models.SEOValidationResult{
		IsValid: true,
		Score:   100,
	}

	// Validate required fields
	if seoMeta.MetaTitle == nil || *seoMeta.MetaTitle == "" {
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "meta_title",
			Code:     "MISSING_TITLE",
			Message:  "Meta title is required",
			Severity: "error",
		})
		result.IsValid = false
		result.Score -= 20
	}

	if seoMeta.MetaDescription == nil || *seoMeta.MetaDescription == "" {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "meta_description",
			Code:    "MISSING_DESCRIPTION",
			Message: "Meta description is recommended",
		})
		result.Score -= 10
	}

	// Validate title length
	if seoMeta.MetaTitle != nil && len(*seoMeta.MetaTitle) > 60 {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "meta_title",
			Code:    "TITLE_TOO_LONG",
			Message: "Meta title should be under 60 characters",
		})
		result.Score -= 5
	}

	// Validate description length
	if seoMeta.MetaDescription != nil && len(*seoMeta.MetaDescription) > 160 {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "meta_description",
			Code:    "DESCRIPTION_TOO_LONG",
			Message: "Meta description should be under 160 characters",
		})
		result.Score -= 5
	}

	return result, nil
}

// GenerateMetaTags generates HTML meta tags for SEO metadata
func (s *seoMetaService) GenerateMetaTags(ctx context.Context, id, tenantID uint) ([]string, string, error) {
	seoMeta, err := s.seoMetaRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, "", ErrSEOMetaNotFound
	}

	metaTags := seoMeta.GenerateMetaTags()
	structuredData, _ := seoMeta.GenerateStructuredData()

	return metaTags, structuredData, nil
}

// Helper methods

func (s *seoMetaService) buildSEOMetaResponse(seoMeta *models.SEOMeta) *models.SEOMetaResponse {
	response := &models.SEOMetaResponse{
		SEOMeta:       seoMeta,
		GeneratedTags: seoMeta.GenerateMetaTags(),
	}

	return response
}

func (s *seoMetaService) analyzeTitle(title string) *models.TextAnalysis {
	return &models.TextAnalysis{
		Length:    len(title),
		WordCount: len(strings.Fields(title)),
		Score:     s.calculateTitleScore(title),
	}
}

func (s *seoMetaService) analyzeDescription(description string) *models.TextAnalysis {
	return &models.TextAnalysis{
		Length:    len(description),
		WordCount: len(strings.Fields(description)),
		Score:     s.calculateDescriptionScore(description),
	}
}

func (s *seoMetaService) analyzeKeywords(keyword string, seoMeta *models.SEOMeta) *models.KeywordAnalysis {
	// Simple keyword analysis implementation
	return &models.KeywordAnalysis{
		Density: 0.0, // Would need content to calculate
		Count:   0,   // Would need content to calculate
	}
}

func (s *seoMetaService) analyzeTechnical(seoMeta *models.SEOMeta) *models.TechnicalAnalysis {
	return &models.TechnicalAnalysis{
		HasTitle:          seoMeta.MetaTitle != nil && *seoMeta.MetaTitle != "",
		HasDescription:    seoMeta.MetaDescription != nil && *seoMeta.MetaDescription != "",
		HasCanonical:      seoMeta.CanonicalURL != nil && *seoMeta.CanonicalURL != "",
		HasOpenGraph:      seoMeta.OGTitle != nil && *seoMeta.OGTitle != "",
		HasTwitterCard:    seoMeta.TwitterCard != "",
		HasStructuredData: seoMeta.SchemaType != nil && *seoMeta.SchemaType != "",
	}
}

func (s *seoMetaService) calculateTitleScore(title string) int {
	score := 100
	if len(title) < 30 {
		score -= 20
	}
	if len(title) > 60 {
		score -= 30
	}
	return score
}

func (s *seoMetaService) calculateDescriptionScore(description string) int {
	score := 100
	if len(description) < 120 {
		score -= 20
	}
	if len(description) > 160 {
		score -= 30
	}
	return score
}

func (s *seoMetaService) calculateOverallScore(analysis *models.SEOAnalysis) float64 {
	score := 0.0
	factors := 0

	if analysis.TitleAnalysis != nil {
		score += float64(analysis.TitleAnalysis.Score)
		factors++
	}

	if analysis.DescriptionAnalysis != nil {
		score += float64(analysis.DescriptionAnalysis.Score)
		factors++
	}

	if analysis.TechnicalAnalysis != nil {
		techScore := 0.0
		if analysis.TechnicalAnalysis.HasTitle {
			techScore += 20
		}
		if analysis.TechnicalAnalysis.HasDescription {
			techScore += 20
		}
		if analysis.TechnicalAnalysis.HasOpenGraph {
			techScore += 20
		}
		if analysis.TechnicalAnalysis.HasTwitterCard {
			techScore += 20
		}
		if analysis.TechnicalAnalysis.HasStructuredData {
			techScore += 20
		}
		score += techScore
		factors++
	}

	if factors > 0 {
		return score / float64(factors)
	}

	return 0
}

func (s *seoMetaService) generateRecommendations(analysis *models.SEOAnalysis, seoMeta *models.SEOMeta) []string {
	var recommendations []string

	if analysis.TitleAnalysis != nil && analysis.TitleAnalysis.Score < 80 {
		recommendations = append(recommendations, "Optimize meta title length (30-60 characters)")
	}

	if analysis.DescriptionAnalysis != nil && analysis.DescriptionAnalysis.Score < 80 {
		recommendations = append(recommendations, "Optimize meta description length (120-160 characters)")
	}

	if analysis.TechnicalAnalysis != nil {
		if !analysis.TechnicalAnalysis.HasOpenGraph {
			recommendations = append(recommendations, "Add Open Graph meta tags for social media")
		}
		if !analysis.TechnicalAnalysis.HasTwitterCard {
			recommendations = append(recommendations, "Add Twitter Card meta tags")
		}
		if !analysis.TechnicalAnalysis.HasStructuredData {
			recommendations = append(recommendations, "Add structured data markup")
		}
	}

	return recommendations
}