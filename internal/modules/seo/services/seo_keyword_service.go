package services

// TODO: Implement SEOKeyword functionality
// This file is temporarily disabled until SEOKeyword models and repository are implemented

/*
import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/repositories"
	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/sirupsen/logrus"
)

// SEOKeywordService handles keyword analysis and tracking
type SEOKeywordService struct {
	keywordRepo repositories.SEOKeywordRepository
	metaRepo    repositories.SEOMetaRepository
	logger      *logrus.Logger
}

// NewSEOKeywordService creates a new SEO keyword service
func NewSEOKeywordService(
	keywordRepo repositories.SEOKeywordRepository,
	metaRepo repositories.SEOMetaRepository,
	logger *logrus.Logger,
) *SEOKeywordService {
	return &SEOKeywordService{
		keywordRepo: keywordRepo,
		metaRepo:    metaRepo,
		logger:      logger,
	}
}

// TrackKeyword tracks a new keyword for a website
func (s *SEOKeywordService) TrackKeyword(ctx context.Context, req *models.CreateSEOKeywordRequest) (*models.SEOKeyword, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	keyword := &models.SEOKeyword{
		TenantID:      tenantCtx.TenantID,
		WebsiteID:     req.WebsiteID,
		Keyword:       strings.ToLower(strings.TrimSpace(req.Keyword)),
		SearchVolume:  req.SearchVolume,
		Difficulty:    req.Difficulty,
		CPC:           req.CPC,
		Competition:   req.Competition,
		TrackingStart: time.Now(),
		Status:        models.KeywordStatusActive,
	}

	if req.TargetURL != nil {
		keyword.TargetURL = req.TargetURL
	}

	if req.Tags != nil {
		keyword.Tags = req.Tags
	}

	// Validate keyword
	if err := keyword.Validate(); err != nil {
		return nil, fmt.Errorf("invalid keyword: %w", err)
	}

	// Check if keyword already exists
	existing, _ := s.keywordRepo.GetByKeyword(ctx, tenantCtx.TenantID, req.WebsiteID, keyword.Keyword)
	if existing != nil {
		return nil, fmt.Errorf("keyword already being tracked")
	}

	// Create keyword
	if err := s.keywordRepo.Create(ctx, keyword); err != nil {
		return nil, fmt.Errorf("failed to create keyword: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":  tenantCtx.TenantID,
		"website_id": req.WebsiteID,
		"keyword":    keyword.Keyword,
	}).Info("Keyword tracking started")

	return keyword, nil
}

// GetKeyword retrieves a keyword by ID
func (s *SEOKeywordService) GetKeyword(ctx context.Context, id uint) (*models.SEOKeyword, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	keyword, err := s.keywordRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get keyword: %w", err)
	}

	return keyword, nil
}

// UpdateKeyword updates a keyword
func (s *SEOKeywordService) UpdateKeyword(ctx context.Context, id uint, req *models.UpdateSEOKeywordRequest) (*models.SEOKeyword, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get existing keyword
	keyword, err := s.keywordRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get keyword: %w", err)
	}

	// Update fields
	if req.SearchVolume != nil {
		keyword.SearchVolume = req.SearchVolume
	}
	if req.Difficulty != nil {
		keyword.Difficulty = req.Difficulty
	}
	if req.CPC != nil {
		keyword.CPC = req.CPC
	}
	if req.Competition != nil {
		keyword.Competition = req.Competition
	}
	if req.TargetURL != nil {
		keyword.TargetURL = req.TargetURL
	}
	if req.Tags != nil {
		keyword.Tags = req.Tags
	}
	if req.Status != nil {
		keyword.Status = *req.Status
	}

	keyword.UpdatedAt = time.Now()

	// Validate
	if err := keyword.Validate(); err != nil {
		return nil, fmt.Errorf("invalid keyword: %w", err)
	}

	// Update
	if err := s.keywordRepo.Update(ctx, tenantCtx.TenantID, id, keyword); err != nil {
		return nil, fmt.Errorf("failed to update keyword: %w", err)
	}

	return keyword, nil
}

// ListKeywords lists keywords for a website
func (s *SEOKeywordService) ListKeywords(ctx context.Context, websiteID uint, filter *models.SEOKeywordFilter, cursor *pagination.Cursor) ([]*models.SEOKeyword, *pagination.Cursor, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, nil, fmt.Errorf("tenant context is required")
	}

	// Set website ID in filter
	if filter == nil {
		filter = &models.SEOKeywordFilter{}
	}
	filter.WebsiteID = &websiteID

	keywords, nextCursor, err := s.keywordRepo.List(ctx, tenantCtx.TenantID, filter, cursor)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list keywords: %w", err)
	}

	return keywords, nextCursor, nil
}

// DeleteKeyword deletes a keyword
func (s *SEOKeywordService) DeleteKeyword(ctx context.Context, id uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Get keyword to verify ownership
	keyword, err := s.keywordRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get keyword: %w", err)
	}

	// Mark as deleted
	keyword.Status = models.KeywordStatusDeleted
	keyword.UpdatedAt = time.Now()

	if err := s.keywordRepo.Update(ctx, tenantCtx.TenantID, id, keyword); err != nil {
		return fmt.Errorf("failed to delete keyword: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":  tenantCtx.TenantID,
		"keyword_id": id,
	}).Info("Keyword deleted")

	return nil
}

// AnalyzeKeywordDensity analyzes keyword density for a page
func (s *SEOKeywordService) AnalyzeKeywordDensity(ctx context.Context, websiteID uint, pageURL string) (*models.KeywordDensityAnalysis, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get page meta
	meta, err := s.metaRepo.GetByURL(ctx, tenantCtx.TenantID, websiteID, pageURL)
	if err != nil {
		return nil, fmt.Errorf("failed to get page meta: %w", err)
	}

	// Analyze content
	content := strings.ToLower(meta.MetaTitle + " " + meta.MetaDescription)
	words := strings.Fields(content)
	totalWords := len(words)

	if totalWords == 0 {
		return &models.KeywordDensityAnalysis{
			PageURL:    pageURL,
			TotalWords: 0,
			Keywords:   make(map[string]models.KeywordOccurrence),
		}, nil
	}

	// Count keyword occurrences
	keywordCount := make(map[string]int)
	for _, word := range words {
		// Clean word
		word = strings.Trim(word, ".,!?;:'\"")
		if len(word) > 2 { // Skip short words
			keywordCount[word]++
		}
	}

	// Calculate density
	keywords := make(map[string]models.KeywordOccurrence)
	for word, count := range keywordCount {
		if count > 1 { // Only include words that appear more than once
			density := float64(count) / float64(totalWords) * 100
			keywords[word] = models.KeywordOccurrence{
				Keyword:    word,
				Count:      count,
				Density:    density,
				Positions:  s.findWordPositions(words, word),
			}
		}
	}

	// Get tracked keywords for this page
	trackedKeywords, _, err := s.keywordRepo.List(ctx, tenantCtx.TenantID, &models.SEOKeywordFilter{
		WebsiteID: &websiteID,
		TargetURL: &pageURL,
		Status:    utils.StringPtr(string(models.KeywordStatusActive)),
	}, nil)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tracked keywords")
	}

	// Check tracked keywords
	trackedAnalysis := make(map[string]models.KeywordOccurrence)
	for _, tracked := range trackedKeywords {
		keyword := strings.ToLower(tracked.Keyword)
		count := strings.Count(strings.ToLower(content), keyword)
		if count > 0 {
			density := float64(count) / float64(totalWords) * 100
			trackedAnalysis[keyword] = models.KeywordOccurrence{
				Keyword:    keyword,
				Count:      count,
				Density:    density,
				Positions:  s.findPhrasePositions(content, keyword),
				IsTracked:  true,
			}
		}
	}

	return &models.KeywordDensityAnalysis{
		PageURL:         pageURL,
		TotalWords:      totalWords,
		Keywords:        keywords,
		TrackedKeywords: trackedAnalysis,
		AnalyzedAt:      time.Now(),
	}, nil
}

// BulkImportKeywords imports multiple keywords
func (s *SEOKeywordService) BulkImportKeywords(ctx context.Context, websiteID uint, keywords []models.CreateSEOKeywordRequest) (int, []error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return 0, []error{fmt.Errorf("tenant context is required")}
	}

	imported := 0
	errors := []error{}

	for i, req := range keywords {
		req.WebsiteID = websiteID
		_, err := s.TrackKeyword(ctx, &req)
		if err != nil {
			errors = append(errors, fmt.Errorf("row %d: %w", i+1, err))
			continue
		}
		imported++
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":  tenantCtx.TenantID,
		"website_id": websiteID,
		"imported":   imported,
		"errors":     len(errors),
	}).Info("Bulk keyword import completed")

	return imported, errors
}

// GetKeywordStats gets keyword statistics for a website
func (s *SEOKeywordService) GetKeywordStats(ctx context.Context, websiteID uint) (*models.SEOKeywordStatsResponse, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	stats, err := s.keywordRepo.GetStats(ctx, websiteID, tenantCtx.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get keyword stats: %w", err)
	}

	return stats, nil
}

// findWordPositions finds positions of a word in text
func (s *SEOKeywordService) findWordPositions(words []string, target string) []int {
	positions := []int{}
	for i, word := range words {
		if strings.Trim(word, ".,!?;:'\"") == target {
			positions = append(positions, i)
		}
	}
	return positions
}

// findPhrasePositions finds positions of a phrase in text
func (s *SEOKeywordService) findPhrasePositions(text, phrase string) []int {
	positions := []int{}
	text = strings.ToLower(text)
	phrase = strings.ToLower(phrase)
	
	start := 0
	for {
		pos := strings.Index(text[start:], phrase)
		if pos == -1 {
			break
		}
		positions = append(positions, start+pos)
		start = start + pos + 1
	}
	
	return positions
}

// CompareKeywordRankings compares keyword rankings between dates
func (s *SEOKeywordService) CompareKeywordRankings(ctx context.Context, websiteID uint, from, to time.Time) (*models.KeywordRankingComparison, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get keywords with rankings
	keywords, _, err := s.keywordRepo.List(ctx, tenantCtx.TenantID, &models.SEOKeywordFilter{
		WebsiteID: &websiteID,
		Status:    utils.StringPtr(string(models.KeywordStatusActive)),
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get keywords: %w", err)
	}

	comparison := &models.KeywordRankingComparison{
		WebsiteID:  websiteID,
		DateFrom:   from,
		DateTo:     to,
		Keywords:   make([]models.KeywordRankingChange, 0),
	}

	for _, keyword := range keywords {
		// Get rankings for both dates
		fromRanking, _ := s.keywordRepo.GetRankingByDate(ctx, tenantCtx.TenantID, keyword.ID, from)
		toRanking, _ := s.keywordRepo.GetRankingByDate(ctx, tenantCtx.TenantID, keyword.ID, to)

		if fromRanking != nil || toRanking != nil {
			change := models.KeywordRankingChange{
				Keyword: keyword.Keyword,
			}

			if fromRanking != nil {
				change.RankingFrom = &fromRanking.Position
			}
			if toRanking != nil {
				change.RankingTo = &toRanking.Position
			}

			// Calculate change
			if change.RankingFrom != nil && change.RankingTo != nil {
				diff := *change.RankingFrom - *change.RankingTo
				change.Change = &diff
			}

			comparison.Keywords = append(comparison.Keywords, change)
		}
	}

	// Calculate summary
	comparison.TotalKeywords = len(keywords)
	improved := 0
	declined := 0
	unchanged := 0
	
	for _, kw := range comparison.Keywords {
		if kw.Change != nil {
			if *kw.Change > 0 {
				improved++
			} else if *kw.Change < 0 {
				declined++
			} else {
				unchanged++
			}
		}
	}

	comparison.Improved = improved
	comparison.Declined = declined
	comparison.Unchanged = unchanged

	return comparison, nil
}
*/