package repositories

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// SEOAuditRepository defines the interface for SEO audit repository
type SEOAuditRepository interface {
	Create(ctx context.Context, audit *models.SEOAudit) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.SEOAudit, error)
	GetByWebsiteID(ctx context.Context, tenantID, websiteID uint) (*models.SEOAudit, error)
	GetLatestByWebsiteID(ctx context.Context, tenantID, websiteID uint) (*models.SEOAudit, error)
	Update(ctx context.Context, tenantID, id uint, audit *models.SEOAudit) error
	Delete(ctx context.Context, tenantID, id uint) error
	List(ctx context.Context, tenantID uint, filter *models.SEOAuditFilter, cursor *pagination.Cursor) ([]*models.SEOAudit, *pagination.Cursor, error)
	GetAuditHistory(ctx context.Context, tenantID, websiteID uint, limit int) ([]*models.SEOAudit, error)
	GetAuditTrends(ctx context.Context, tenantID, websiteID uint, days int) ([]*models.AuditTrendPoint, error)
	CleanupOldAudits(ctx context.Context, tenantID uint, retentionDays int) error
}

// seoAuditRepository implements SEOAuditRepository
type seoAuditRepository struct {
	db *sql.DB
}

// NewSEOAuditRepository creates a new SEO audit repository
func NewSEOAuditRepository(db *sql.DB) SEOAuditRepository {
	return &seoAuditRepository{
		db: db,
	}
}

// Create creates a new SEO audit
func (r *seoAuditRepository) Create(ctx context.Context, audit *models.SEOAudit) error {
	query := `
		INSERT INTO seo_audits (
			tenant_id, website_id, audit_type, audit_url, audit_status,
			total_score, performance_score, accessibility_score, best_practices_score, seo_score,
			total_pages, crawled_pages, errors_found, warnings_found,
			audit_results, performance_metrics, issue_summary, recommendations,
			started_at, completed_at, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	result, err := r.db.ExecContext(ctx, query,
		audit.TenantID, audit.WebsiteID, audit.AuditType, audit.AuditURL, audit.Status,
		audit.OverallScore, audit.PerformanceScore, audit.AccessibilityScore, audit.TechnicalScore, audit.ContentScore,
		audit.MaxPages, audit.PagesCrawled, audit.IssuesFound, audit.Warnings,
		audit.AuditResults, audit.LoadTime, audit.IssuesSummary, audit.Recommendations,
		audit.AuditStartedAt, audit.AuditCompletedAt, audit.CreatedAt, audit.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("failed to create SEO audit: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}

	audit.ID = uint(id)
	return nil
}

// GetByID retrieves an SEO audit by ID
func (r *seoAuditRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.SEOAudit, error) {
	query := `
		SELECT id, tenant_id, website_id, audit_type, audit_name, audit_description, audit_url,
			audit_started_at, audit_completed_at, audit_duration, audit_tool, audit_version,
			overall_score, technical_score, content_score, performance_score, accessibility_score, user_experience_score,
			pages_crawled, pages_indexed, issues_found, critical_issues, major_issues, minor_issues, warnings,
			audit_results, issues_summary, recommendations, comparison_data,
			audit_config, crawl_depth, max_pages, include_external_links, check_images, check_performance,
			status, error_message, created_by, created_at, updated_at
		FROM seo_audits 
		WHERE tenant_id = ? AND id = ?`

	audit := &models.SEOAudit{}
	err := r.db.QueryRowContext(ctx, query, tenantID, id).Scan(
		&audit.ID, &audit.TenantID, &audit.WebsiteID, &audit.AuditType, &audit.AuditName, &audit.AuditDescription, &audit.AuditURL,
		&audit.AuditStartedAt, &audit.AuditCompletedAt, &audit.AuditDuration, &audit.AuditTool, &audit.AuditVersion,
		&audit.OverallScore, &audit.TechnicalScore, &audit.ContentScore, &audit.PerformanceScore, &audit.AccessibilityScore, &audit.UserExperienceScore,
		&audit.PagesCrawled, &audit.PagesIndexed, &audit.IssuesFound, &audit.CriticalIssues, &audit.MajorIssues, &audit.MinorIssues, &audit.Warnings,
		&audit.AuditResults, &audit.IssuesSummary, &audit.Recommendations, &audit.ComparisonData,
		&audit.AuditConfig, &audit.CrawlDepth, &audit.MaxPages, &audit.IncludeExternalLinks, &audit.CheckImages, &audit.CheckPerformance,
		&audit.Status, &audit.ErrorMessage, &audit.CreatedBy, &audit.CreatedAt, &audit.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("SEO audit not found")
		}
		return nil, fmt.Errorf("failed to get SEO audit: %w", err)
	}

	return audit, nil
}

// GetByWebsiteID retrieves the latest SEO audit for a website
func (r *seoAuditRepository) GetByWebsiteID(ctx context.Context, tenantID, websiteID uint) (*models.SEOAudit, error) {
	query := `
		SELECT id, tenant_id, website_id, audit_type, audit_name, audit_description, audit_url,
			audit_started_at, audit_completed_at, audit_duration, audit_tool, audit_version,
			overall_score, technical_score, content_score, performance_score, accessibility_score, user_experience_score,
			pages_crawled, pages_indexed, issues_found, critical_issues, major_issues, minor_issues, warnings,
			audit_results, issues_summary, recommendations, comparison_data,
			audit_config, crawl_depth, max_pages, include_external_links, check_images, check_performance,
			status, error_message, created_by, created_at, updated_at
		FROM seo_audits 
		WHERE tenant_id = ? AND website_id = ?
		ORDER BY created_at DESC 
		LIMIT 1`

	audit := &models.SEOAudit{}
	err := r.db.QueryRowContext(ctx, query, tenantID, websiteID).Scan(
		&audit.ID, &audit.TenantID, &audit.WebsiteID, &audit.AuditType, &audit.AuditName, &audit.AuditDescription, &audit.AuditURL,
		&audit.AuditStartedAt, &audit.AuditCompletedAt, &audit.AuditDuration, &audit.AuditTool, &audit.AuditVersion,
		&audit.OverallScore, &audit.TechnicalScore, &audit.ContentScore, &audit.PerformanceScore, &audit.AccessibilityScore, &audit.UserExperienceScore,
		&audit.PagesCrawled, &audit.PagesIndexed, &audit.IssuesFound, &audit.CriticalIssues, &audit.MajorIssues, &audit.MinorIssues, &audit.Warnings,
		&audit.AuditResults, &audit.IssuesSummary, &audit.Recommendations, &audit.ComparisonData,
		&audit.AuditConfig, &audit.CrawlDepth, &audit.MaxPages, &audit.IncludeExternalLinks, &audit.CheckImages, &audit.CheckPerformance,
		&audit.Status, &audit.ErrorMessage, &audit.CreatedBy, &audit.CreatedAt, &audit.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("SEO audit not found")
		}
		return nil, fmt.Errorf("failed to get SEO audit: %w", err)
	}

	return audit, nil
}

// GetLatestByWebsiteID retrieves the latest completed SEO audit for a website
func (r *seoAuditRepository) GetLatestByWebsiteID(ctx context.Context, tenantID, websiteID uint) (*models.SEOAudit, error) {
	query := `
		SELECT id, tenant_id, website_id, audit_type, audit_name, audit_description, audit_url,
			audit_started_at, audit_completed_at, audit_duration, audit_tool, audit_version,
			overall_score, technical_score, content_score, performance_score, accessibility_score, user_experience_score,
			pages_crawled, pages_indexed, issues_found, critical_issues, major_issues, minor_issues, warnings,
			audit_results, issues_summary, recommendations, comparison_data,
			audit_config, crawl_depth, max_pages, include_external_links, check_images, check_performance,
			status, error_message, created_by, created_at, updated_at
		FROM seo_audits 
		WHERE tenant_id = ? AND website_id = ? AND status = 'completed'
		ORDER BY audit_completed_at DESC 
		LIMIT 1`

	audit := &models.SEOAudit{}
	err := r.db.QueryRowContext(ctx, query, tenantID, websiteID).Scan(
		&audit.ID, &audit.TenantID, &audit.WebsiteID, &audit.AuditType, &audit.AuditName, &audit.AuditDescription, &audit.AuditURL,
		&audit.AuditStartedAt, &audit.AuditCompletedAt, &audit.AuditDuration, &audit.AuditTool, &audit.AuditVersion,
		&audit.OverallScore, &audit.TechnicalScore, &audit.ContentScore, &audit.PerformanceScore, &audit.AccessibilityScore, &audit.UserExperienceScore,
		&audit.PagesCrawled, &audit.PagesIndexed, &audit.IssuesFound, &audit.CriticalIssues, &audit.MajorIssues, &audit.MinorIssues, &audit.Warnings,
		&audit.AuditResults, &audit.IssuesSummary, &audit.Recommendations, &audit.ComparisonData,
		&audit.AuditConfig, &audit.CrawlDepth, &audit.MaxPages, &audit.IncludeExternalLinks, &audit.CheckImages, &audit.CheckPerformance,
		&audit.Status, &audit.ErrorMessage, &audit.CreatedBy, &audit.CreatedAt, &audit.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("completed SEO audit not found")
		}
		return nil, fmt.Errorf("failed to get latest SEO audit: %w", err)
	}

	return audit, nil
}

// Update updates an existing SEO audit
func (r *seoAuditRepository) Update(ctx context.Context, tenantID, id uint, audit *models.SEOAudit) error {
	query := `
		UPDATE seo_audits SET
			audit_type = ?, audit_name = ?, audit_description = ?, audit_url = ?,
			audit_started_at = ?, audit_completed_at = ?, audit_duration = ?, audit_tool = ?, audit_version = ?,
			overall_score = ?, technical_score = ?, content_score = ?, performance_score = ?, accessibility_score = ?, user_experience_score = ?,
			pages_crawled = ?, pages_indexed = ?, issues_found = ?, critical_issues = ?, major_issues = ?, minor_issues = ?, warnings = ?,
			audit_results = ?, issues_summary = ?, recommendations = ?, comparison_data = ?,
			audit_config = ?, crawl_depth = ?, max_pages = ?, include_external_links = ?, check_images = ?, check_performance = ?,
			status = ?, error_message = ?, updated_at = ?
		WHERE tenant_id = ? AND id = ?`

	result, err := r.db.ExecContext(ctx, query,
		audit.AuditType, audit.AuditName, audit.AuditDescription, audit.AuditURL,
		audit.AuditStartedAt, audit.AuditCompletedAt, audit.AuditDuration, audit.AuditTool, audit.AuditVersion,
		audit.OverallScore, audit.TechnicalScore, audit.ContentScore, audit.PerformanceScore, audit.AccessibilityScore, audit.UserExperienceScore,
		audit.PagesCrawled, audit.PagesIndexed, audit.IssuesFound, audit.CriticalIssues, audit.MajorIssues, audit.MinorIssues, audit.Warnings,
		audit.AuditResults, audit.IssuesSummary, audit.Recommendations, audit.ComparisonData,
		audit.AuditConfig, audit.CrawlDepth, audit.MaxPages, audit.IncludeExternalLinks, audit.CheckImages, audit.CheckPerformance,
		audit.Status, audit.ErrorMessage, audit.UpdatedAt,
		tenantID, id,
	)
	if err != nil {
		return fmt.Errorf("failed to update SEO audit: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("SEO audit not found")
	}

	return nil
}

// Delete deletes an SEO audit
func (r *seoAuditRepository) Delete(ctx context.Context, tenantID, id uint) error {
	query := `DELETE FROM seo_audits WHERE tenant_id = ? AND id = ?`

	result, err := r.db.ExecContext(ctx, query, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to delete SEO audit: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("SEO audit not found")
	}

	return nil
}

// List retrieves SEO audits with filters and pagination
func (r *seoAuditRepository) List(ctx context.Context, tenantID uint, filter *models.SEOAuditFilter, cursor *pagination.Cursor) ([]*models.SEOAudit, *pagination.Cursor, error) {
	baseQuery := `
		SELECT id, tenant_id, website_id, audit_type, audit_name, audit_description, audit_url,
			audit_started_at, audit_completed_at, audit_duration, audit_tool, audit_version,
			overall_score, technical_score, content_score, performance_score, accessibility_score, user_experience_score,
			pages_crawled, pages_indexed, issues_found, critical_issues, major_issues, minor_issues, warnings,
			audit_results, issues_summary, recommendations, comparison_data,
			audit_config, crawl_depth, max_pages, include_external_links, check_images, check_performance,
			status, error_message, created_by, created_at, updated_at
		FROM seo_audits 
		WHERE tenant_id = ?`

	args := []interface{}{tenantID}
	whereConditions := []string{}

	// Apply filters
	if filter != nil {
		if filter.WebsiteID != nil {
			whereConditions = append(whereConditions, "website_id = ?")
			args = append(args, *filter.WebsiteID)
		}
		if filter.AuditType != nil {
			whereConditions = append(whereConditions, "audit_type = ?")
			args = append(args, *filter.AuditType)
		}
		if filter.Status != nil {
			whereConditions = append(whereConditions, "status = ?")
			args = append(args, *filter.Status)
		}
		if filter.MinOverallScore != nil {
			whereConditions = append(whereConditions, "overall_score >= ?")
			args = append(args, *filter.MinOverallScore)
		}
		if filter.MaxOverallScore != nil {
			whereConditions = append(whereConditions, "overall_score <= ?")
			args = append(args, *filter.MaxOverallScore)
		}
		if filter.CreatedAfter != nil {
			whereConditions = append(whereConditions, "created_at >= ?")
			args = append(args, *filter.CreatedAfter)
		}
		if filter.CreatedBefore != nil {
			whereConditions = append(whereConditions, "created_at <= ?")
			args = append(args, *filter.CreatedBefore)
		}
		if filter.CompletedAfter != nil {
			whereConditions = append(whereConditions, "audit_completed_at >= ?")
			args = append(args, *filter.CompletedAfter)
		}
		if filter.CompletedBefore != nil {
			whereConditions = append(whereConditions, "audit_completed_at <= ?")
			args = append(args, *filter.CompletedBefore)
		}
	}

	// Build final query with conditions
	query := baseQuery
	if len(whereConditions) > 0 {
		query += " AND " + fmt.Sprintf("(%s)", whereConditions[0])
		for _, condition := range whereConditions[1:] {
			query += " AND " + condition
		}
	}

	// Apply cursor pagination
	if cursor != nil && cursor.ID > 0 {
		query += " AND id > ?"
		args = append(args, cursor.ID)
	}

	query += " ORDER BY id ASC"

	// Apply limit
	limit := 20
	query += fmt.Sprintf(" LIMIT %d", limit+1) // +1 to check if there are more results

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list SEO audits: %w", err)
	}
	defer rows.Close()

	audits := make([]*models.SEOAudit, 0)
	for rows.Next() {
		audit := &models.SEOAudit{}
		err := rows.Scan(
			&audit.ID, &audit.TenantID, &audit.WebsiteID, &audit.AuditType, &audit.AuditName, &audit.AuditDescription, &audit.AuditURL,
			&audit.AuditStartedAt, &audit.AuditCompletedAt, &audit.AuditDuration, &audit.AuditTool, &audit.AuditVersion,
			&audit.OverallScore, &audit.TechnicalScore, &audit.ContentScore, &audit.PerformanceScore, &audit.AccessibilityScore, &audit.UserExperienceScore,
			&audit.PagesCrawled, &audit.PagesIndexed, &audit.IssuesFound, &audit.CriticalIssues, &audit.MajorIssues, &audit.MinorIssues, &audit.Warnings,
			&audit.AuditResults, &audit.IssuesSummary, &audit.Recommendations, &audit.ComparisonData,
			&audit.AuditConfig, &audit.CrawlDepth, &audit.MaxPages, &audit.IncludeExternalLinks, &audit.CheckImages, &audit.CheckPerformance,
			&audit.Status, &audit.ErrorMessage, &audit.CreatedBy, &audit.CreatedAt, &audit.UpdatedAt,
		)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to scan SEO audit: %w", err)
		}
		audits = append(audits, audit)
	}

	if err = rows.Err(); err != nil {
		return nil, nil, fmt.Errorf("error iterating rows: %w", err)
	}

	// Prepare next cursor
	var nextCursor *pagination.Cursor
	if len(audits) > limit {
		audits = audits[:limit] // Remove the extra item
		lastAudit := audits[len(audits)-1]
		nextCursor = &pagination.Cursor{
			ID:   int64(lastAudit.ID),
			Time: lastAudit.CreatedAt,
		}
	}

	return audits, nextCursor, nil
}

// GetAuditHistory retrieves audit history for a website
func (r *seoAuditRepository) GetAuditHistory(ctx context.Context, tenantID, websiteID uint, limit int) ([]*models.SEOAudit, error) {
	query := `
		SELECT id, tenant_id, website_id, audit_type, audit_name, audit_description, audit_url,
			audit_started_at, audit_completed_at, audit_duration, audit_tool, audit_version,
			overall_score, technical_score, content_score, performance_score, accessibility_score, user_experience_score,
			pages_crawled, pages_indexed, issues_found, critical_issues, major_issues, minor_issues, warnings,
			audit_results, issues_summary, recommendations, comparison_data,
			audit_config, crawl_depth, max_pages, include_external_links, check_images, check_performance,
			status, error_message, created_by, created_at, updated_at
		FROM seo_audits 
		WHERE tenant_id = ? AND website_id = ? AND status = 'completed'
		ORDER BY audit_completed_at DESC 
		LIMIT ?`

	rows, err := r.db.QueryContext(ctx, query, tenantID, websiteID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit history: %w", err)
	}
	defer rows.Close()

	audits := make([]*models.SEOAudit, 0)
	for rows.Next() {
		audit := &models.SEOAudit{}
		err := rows.Scan(
			&audit.ID, &audit.TenantID, &audit.WebsiteID, &audit.AuditType, &audit.AuditName, &audit.AuditDescription, &audit.AuditURL,
			&audit.AuditStartedAt, &audit.AuditCompletedAt, &audit.AuditDuration, &audit.AuditTool, &audit.AuditVersion,
			&audit.OverallScore, &audit.TechnicalScore, &audit.ContentScore, &audit.PerformanceScore, &audit.AccessibilityScore, &audit.UserExperienceScore,
			&audit.PagesCrawled, &audit.PagesIndexed, &audit.IssuesFound, &audit.CriticalIssues, &audit.MajorIssues, &audit.MinorIssues, &audit.Warnings,
			&audit.AuditResults, &audit.IssuesSummary, &audit.Recommendations, &audit.ComparisonData,
			&audit.AuditConfig, &audit.CrawlDepth, &audit.MaxPages, &audit.IncludeExternalLinks, &audit.CheckImages, &audit.CheckPerformance,
			&audit.Status, &audit.ErrorMessage, &audit.CreatedBy, &audit.CreatedAt, &audit.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan audit: %w", err)
		}
		audits = append(audits, audit)
	}

	return audits, nil
}

// GetAuditTrends retrieves audit trend data for a website
func (r *seoAuditRepository) GetAuditTrends(ctx context.Context, tenantID, websiteID uint, days int) ([]*models.AuditTrendPoint, error) {
	query := `
		SELECT 
			DATE(audit_completed_at) as date,
			AVG(overall_score) as avg_total_score,
			AVG(performance_score) as avg_performance_score,
			AVG(accessibility_score) as avg_accessibility_score,
			AVG(technical_score) as avg_best_practices_score,
			AVG(content_score) as avg_seo_score,
			COUNT(*) as audit_count
		FROM seo_audits 
		WHERE tenant_id = ? AND website_id = ? AND status = 'completed'
			AND audit_completed_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
		GROUP BY DATE(audit_completed_at)
		ORDER BY date ASC`

	rows, err := r.db.QueryContext(ctx, query, tenantID, websiteID, days)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit trends: %w", err)
	}
	defer rows.Close()

	trends := make([]*models.AuditTrendPoint, 0)
	for rows.Next() {
		trend := &models.AuditTrendPoint{}
		err := rows.Scan(
			&trend.Date, &trend.TotalScore, &trend.PerformanceScore, &trend.AccessibilityScore,
			&trend.BestPracticesScore, &trend.SEOScore, &trend.AuditCount,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan trend: %w", err)
		}
		trends = append(trends, trend)
	}

	return trends, nil
}

// CleanupOldAudits removes old audit records
func (r *seoAuditRepository) CleanupOldAudits(ctx context.Context, tenantID uint, retentionDays int) error {
	query := `
		DELETE FROM seo_audits 
		WHERE tenant_id = ? AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)`

	result, err := r.db.ExecContext(ctx, query, tenantID, retentionDays)
	if err != nil {
		return fmt.Errorf("failed to cleanup old audits: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	// Log the cleanup result if needed
	_ = rowsAffected

	return nil
}