package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SEORedirectHandler handles SEO redirect HTTP requests
type SEORedirectHandler struct {
	service services.SEORedirectService
	logger  utils.Logger
}

// NewSEORedirectHandler creates a new SEO redirect handler
func NewSEORedirectHandler(service services.SEORedirectService, logger utils.Logger) *SEORedirectHandler {
	return &SEORedirectHandler{
		service: service,
		logger:  logger,
	}
}

// CreateRedirect creates a new SEO redirect
func (h *SEORedirectHandler) CreateRedirect(c *gin.Context) {
	var req models.SEORedirectCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	redirect, err := h.service.Create(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create redirect", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    redirect,
	})
}

// GetRedirect retrieves an SEO redirect by ID
func (h *SEORedirectHandler) GetRedirect(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid redirect ID"})
		return
	}

	redirect, err := h.service.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to get redirect", "error", err, "id", id)
		c.JSON(http.StatusNotFound, gin.H{"error": "Redirect not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    redirect,
	})
}

// UpdateRedirect updates an existing SEO redirect
func (h *SEORedirectHandler) UpdateRedirect(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid redirect ID"})
		return
	}

	var req models.SEORedirectUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	redirect, err := h.service.Update(c.Request.Context(), uint(id), &req)
	if err != nil {
		h.logger.Error("Failed to update redirect", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    redirect,
	})
}

// DeleteRedirect deletes an SEO redirect
func (h *SEORedirectHandler) DeleteRedirect(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid redirect ID"})
		return
	}

	// Check if soft delete is requested
	softDelete := c.Query("soft") == "true"
	
	if softDelete {
		err = h.service.SoftDelete(c.Request.Context(), uint(id))
	} else {
		err = h.service.Delete(c.Request.Context(), uint(id))
	}

	if err != nil {
		h.logger.Error("Failed to delete redirect", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Redirect deleted successfully",
	})
}

// ListRedirects lists SEO redirects with pagination and filters
func (h *SEORedirectHandler) ListRedirects(c *gin.Context) {
	var req models.SEORedirectListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("Failed to bind query parameters", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set defaults if not provided
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	redirects, total, err := h.service.List(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to list redirects", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"redirects": redirects,
			"pagination": gin.H{
				"page":       req.Page,
				"page_size":  req.PageSize,
				"total":      total,
				"total_pages": (total + int64(req.PageSize) - 1) / int64(req.PageSize),
			},
		},
	})
}

// ListRedirectsWithCursor lists SEO redirects with cursor-based pagination
// @Summary      List redirects with cursor pagination
// @Description  Get paginated list of SEO redirects with filters using cursor pagination
// @Tags         SEO Redirects
// @Produce      json
// @Security     Bearer
// @Param        website_id path uint true "Website ID"
// @Param        status query string false "Status filter" Enums(active,inactive,expired,deleted)
// @Param        redirect_type query string false "Redirect type filter" Enums(301,302,303,307,308)
// @Param        source_url query string false "Source URL filter"
// @Param        search query string false "Search in URLs and notes"
// @Param        created_by query uint false "Created by user ID"
// @Param        date_from query string false "Date from filter (RFC3339)"
// @Param        date_to query string false "Date to filter (RFC3339)"
// @Param        cursor query string false "Pagination cursor"
// @Param        limit query int false "Items per page" default(20)
// @Param        sort_by query string false "Sort by field" Enums(id,created_at,updated_at,hit_count,last_hit_at) default(created_at)
// @Param        sort_order query string false "Sort order" Enums(asc,desc) default(desc)
// @Success      200 {object} dto.SEORedirectListResponse
// @Failure      400 {object} gin.H "Invalid filter parameters"
// @Failure      401 {object} gin.H "Authentication required"
// @Failure      500 {object} gin.H "Failed to retrieve redirects"
// @Router       /api/cms/v1/websites/{website_id}/seo/redirects [get]
func (h *SEORedirectHandler) ListRedirectsWithCursor(c *gin.Context) {
	var filter dto.SEORedirectFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		h.logger.Error("Failed to bind query parameters", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get website ID from path
	websiteIDStr := c.Param("website_id")
	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Tenant ID not found in context"})
		return
	}

	// Create cursor request
	cursorReq := &pagination.CursorRequest{
		Cursor: filter.Cursor,
		Limit:  pagination.ValidateLimit(filter.Limit),
	}

	// Build filters map
	filters := make(map[string]interface{})
	if filter.Status != nil {
		filters["status"] = *filter.Status
	}
	if filter.RedirectType != nil {
		filters["redirect_type"] = *filter.RedirectType
	}
	if filter.SourceURL != nil {
		filters["source_url"] = *filter.SourceURL
	}
	if filter.Search != "" {
		filters["search"] = filter.Search
	}
	if filter.CreatedBy != nil {
		filters["created_by"] = *filter.CreatedBy
	}
	if filter.DateFrom != nil {
		filters["date_from"] = *filter.DateFrom
	}
	if filter.DateTo != nil {
		filters["date_to"] = *filter.DateTo
	}
	if filter.SortBy != "" {
		filters["sort_by"] = filter.SortBy
	}
	if filter.SortOrder != "" {
		filters["sort_order"] = filter.SortOrder
	}

	redirectResponse, err := h.service.ListWithCursor(c.Request.Context(), uint(websiteID), tenantID.(uint), cursorReq, filters)
	if err != nil {
		h.logger.Error("Failed to list redirects with cursor", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    redirectResponse,
	})
}

// ProcessRedirect processes a redirect request and returns the destination
func (h *SEORedirectHandler) ProcessRedirect(c *gin.Context) {
	websiteID, err := strconv.ParseUint(c.Param("website_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
		return
	}

	path := c.Query("path")
	if path == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Path parameter is required"})
		return
	}

	// Extract request information
	ip := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")
	referrer := c.GetHeader("Referer")

	redirect, err := h.service.ProcessRedirect(c.Request.Context(), uint(websiteID), uint(tenantID), path, ip, userAgent, referrer)
	if err != nil {
		h.logger.Error("Failed to process redirect", "error", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "No redirect found for path"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"redirect_type":   redirect.RedirectType,
			"destination_url": redirect.DestinationURL,
			"redirect":        redirect,
		},
	})
}

// GetRedirectStats retrieves redirect statistics
func (h *SEORedirectHandler) GetRedirectStats(c *gin.Context) {
	websiteID, err := strconv.ParseUint(c.Param("website_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
		return
	}

	stats, err := h.service.GetStats(c.Request.Context(), uint(websiteID), uint(tenantID))
	if err != nil {
		h.logger.Error("Failed to get redirect stats", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetTopRedirects retrieves top redirects by hit count
func (h *SEORedirectHandler) GetTopRedirects(c *gin.Context) {
	websiteID, err := strconv.ParseUint(c.Param("website_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
		return
	}

	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	redirects, err := h.service.GetTopRedirects(c.Request.Context(), uint(websiteID), uint(tenantID), limit)
	if err != nil {
		h.logger.Error("Failed to get top redirects", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    redirects,
	})
}

// TestRedirect tests a redirect against a test path
func (h *SEORedirectHandler) TestRedirect(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid redirect ID"})
		return
	}

	testPath := c.Query("test_path")
	if testPath == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "test_path parameter is required"})
		return
	}

	destination, err := h.service.TestRedirect(c.Request.Context(), uint(id), testPath)
	if err != nil {
		h.logger.Error("Failed to test redirect", "error", err, "id", id)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"test_path":    testPath,
			"destination":  destination,
			"match":        true,
		},
	})
}

// BulkCreateRedirects creates multiple redirects
func (h *SEORedirectHandler) BulkCreateRedirects(c *gin.Context) {
	var requests []*models.SEORedirectCreateRequest
	if err := c.ShouldBindJSON(&requests); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	redirects, err := h.service.BulkCreate(c.Request.Context(), requests)
	if err != nil {
		h.logger.Error("Failed to bulk create redirects", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data": gin.H{
			"redirects": redirects,
			"count":     len(redirects),
		},
	})
}

// BulkDeleteRedirects deletes multiple redirects
func (h *SEORedirectHandler) BulkDeleteRedirects(c *gin.Context) {
	var request struct {
		IDs []uint `json:"ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := h.service.BulkDelete(c.Request.Context(), request.IDs)
	if err != nil {
		h.logger.Error("Failed to bulk delete redirects", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Redirects deleted successfully",
		"count":   len(request.IDs),
	})
}

// CleanupExpiredRedirects removes expired redirects
func (h *SEORedirectHandler) CleanupExpiredRedirects(c *gin.Context) {
	count, err := h.service.CleanupExpiredRedirects(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to cleanup expired redirects", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Expired redirects cleaned up successfully",
		"count":   count,
	})
}