package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
)

// SEOAuditCreateRequest represents request to create SEO audit
type SEOAuditCreateRequest struct {
	WebsiteID        uint    `json:"website_id" validate:"required" example:"1"`
	AuditType        string  `json:"audit_type" validate:"required,oneof=full page technical content performance accessibility" example:"full"`
	AuditName        string  `json:"audit_name" validate:"required,min=1,max=255" example:"Monthly SEO Audit"`
	AuditDescription *string `json:"audit_description" example:"Comprehensive SEO audit for all website pages"`
	AuditURL         *string `json:"audit_url" validate:"omitempty,url" example:"https://example.com"`

	// Audit Configuration
	CrawlDepth           *uint `json:"crawl_depth" validate:"omitempty,min=1,max=10" example:"3"`
	MaxPages             *uint `json:"max_pages" validate:"omitempty,min=1,max=10000" example:"1000"`
	IncludeExternalLinks *bool `json:"include_external_links" example:"true"`
	CheckImages          *bool `json:"check_images" example:"true"`
	CheckPerformance     *bool `json:"check_performance" example:"true"`

	// Additional Configuration
	AuditConfig         *models.AuditConfig `json:"audit_config" example:"{\"check_mobile\":true,\"analyze_speed\":true}"`
	ExcludePatterns     []string            `json:"exclude_patterns" example:"[\"/admin/*\",\"/private/*\"]"`
	IncludePatterns     []string            `json:"include_patterns" example:"[\"/blog/*\",\"/products/*\"]"`
	ScheduleAudit       bool                `json:"schedule_audit" example:"false"`
	ScheduledAt         *time.Time          `json:"scheduled_at" example:"2024-01-15T10:00:00Z"`
	NotifyOnCompletion  bool                `json:"notify_on_completion" example:"true"`
	CompareWithPrevious bool                `json:"compare_with_previous" example:"true"`
}

// SEOAuditUpdateRequest represents request to update SEO audit
type SEOAuditUpdateRequest struct {
	AuditName        *string `json:"audit_name" validate:"omitempty,min=1,max=255" example:"Updated Monthly SEO Audit"`
	AuditDescription *string `json:"audit_description" example:"Updated audit description"`
	Status           *string `json:"status" validate:"omitempty,oneof=pending running completed failed cancelled deleted" example:"completed"`

	// Scores (updated during audit execution)
	OverallScore        *float64 `json:"overall_score" validate:"omitempty,min=0,max=100" example:"85.5"`
	TechnicalScore      *float64 `json:"technical_score" validate:"omitempty,min=0,max=100" example:"90.0"`
	ContentScore        *float64 `json:"content_score" validate:"omitempty,min=0,max=100" example:"78.5"`
	PerformanceScore    *float64 `json:"performance_score" validate:"omitempty,min=0,max=100" example:"88.2"`
	AccessibilityScore  *float64 `json:"accessibility_score" validate:"omitempty,min=0,max=100" example:"82.1"`
	UserExperienceScore *float64 `json:"user_experience_score" validate:"omitempty,min=0,max=100" example:"80.5"`

	// Results (updated when audit completes)
	AuditResults    *models.AuditResults    `json:"audit_results"`
	IssuesSummary   *models.IssuesSummary   `json:"issues_summary"`
	Recommendations *models.Recommendations `json:"recommendations"`
	ComparisonData  *models.ComparisonData  `json:"comparison_data"`

	// Error handling
	ErrorMessage *string `json:"error_message" example:"Audit failed due to network timeout"`
}

// SEOAuditResponse represents SEO audit information in responses
type SEOAuditResponse struct {
	ID        uint `json:"id" example:"1"`
	WebsiteID uint `json:"website_id" example:"1"`
	TenantID  uint `json:"tenant_id" example:"1"`

	// Audit Information
	AuditType        string     `json:"audit_type" example:"full"`
	AuditName        string     `json:"audit_name" example:"Monthly SEO Audit"`
	AuditDescription *string    `json:"audit_description,omitempty" example:"Comprehensive SEO audit"`
	AuditURL         *string    `json:"audit_url,omitempty" example:"https://example.com"`

	// Audit Execution
	AuditStartedAt   time.Time  `json:"audit_started_at"`
	AuditCompletedAt *time.Time `json:"audit_completed_at,omitempty"`
	AuditDuration    *uint      `json:"audit_duration,omitempty" example:"1800"`
	AuditTool        string     `json:"audit_tool" example:"internal"`
	AuditVersion     *string    `json:"audit_version,omitempty" example:"1.0.0"`

	// Overall Scores
	OverallScore        float64 `json:"overall_score" example:"85.5"`
	TechnicalScore      float64 `json:"technical_score" example:"90.0"`
	ContentScore        float64 `json:"content_score" example:"78.5"`
	PerformanceScore    float64 `json:"performance_score" example:"88.2"`
	AccessibilityScore  float64 `json:"accessibility_score" example:"82.1"`
	UserExperienceScore float64 `json:"user_experience_score" example:"80.5"`

	// Detailed Metrics
	PagesCrawled   uint `json:"pages_crawled" example:"150"`
	PagesIndexed   uint `json:"pages_indexed" example:"142"`
	IssuesFound    uint `json:"issues_found" example:"25"`
	CriticalIssues uint `json:"critical_issues" example:"3"`
	MajorIssues    uint `json:"major_issues" example:"8"`
	MinorIssues    uint `json:"minor_issues" example:"12"`
	Warnings       uint `json:"warnings" example:"2"`

	// Performance Metrics
	LoadTime               *float64 `json:"load_time,omitempty" example:"2.5"`
	FirstContentfulPaint   *float64 `json:"first_contentful_paint,omitempty" example:"1.2"`
	LargestContentfulPaint *float64 `json:"largest_contentful_paint,omitempty" example:"2.8"`
	CumulativeLayoutShift  *float64 `json:"cumulative_layout_shift,omitempty" example:"0.05"`
	FirstInputDelay        *float64 `json:"first_input_delay,omitempty" example:"50.0"`
	TotalBlockingTime      *float64 `json:"total_blocking_time,omitempty" example:"120.0"`

	// Technical SEO Metrics
	MobileFriendly            *bool `json:"mobile_friendly,omitempty" example:"true"`
	HTTPSEnabled              *bool `json:"https_enabled,omitempty" example:"true"`
	XMLSitemapExists          *bool `json:"xml_sitemap_exists,omitempty" example:"true"`
	RobotsTxtExists           *bool `json:"robots_txt_exists,omitempty" example:"true"`
	StructuredDataValid       *bool `json:"structured_data_valid,omitempty" example:"true"`
	CanonicalURLsProper       *bool `json:"canonical_urls_proper,omitempty" example:"false"`
	MetaTitlesOptimized       *bool `json:"meta_titles_optimized,omitempty" example:"true"`
	MetaDescriptionsOptimized *bool `json:"meta_descriptions_optimized,omitempty" example:"false"`
	HeadingStructureProper    *bool `json:"heading_structure_proper,omitempty" example:"true"`
	ImagesOptimized           *bool `json:"images_optimized,omitempty" example:"false"`
	InternalLinksHealthy      *bool `json:"internal_links_healthy,omitempty" example:"true"`
	ExternalLinksHealthy      *bool `json:"external_links_healthy,omitempty" example:"true"`

	// Content Quality Metrics
	ContentUniqueness      *float64 `json:"content_uniqueness,omitempty" example:"95.5"`
	KeywordDensity         *float64 `json:"keyword_density,omitempty" example:"2.1"`
	ReadabilityScore       *float64 `json:"readability_score,omitempty" example:"75.8"`
	WordCountAverage       *uint    `json:"word_count_average,omitempty" example:"450"`
	DuplicateContentIssues uint     `json:"duplicate_content_issues" example:"2"`
	ThinContentPages       uint     `json:"thin_content_pages" example:"5"`

	// Status and Metadata
	Status       string     `json:"status" example:"completed"`
	ErrorMessage *string    `json:"error_message,omitempty"`
	CreatedBy    *uint      `json:"created_by,omitempty" example:"123"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`

	// Additional Analysis Data
	IssuesByCategory       map[string][]models.AuditIssue `json:"issues_by_category,omitempty"`
	TrendData              *models.AuditTrendData         `json:"trend_data,omitempty"`
	RecommendationsByPriority map[string][]models.AuditRecommendation `json:"recommendations_by_priority,omitempty"`
}

// SEOAuditListResponse represents response for listing SEO audits
type SEOAuditListResponse struct {
	Audits     []SEOAuditResponse `json:"audits"`
	Total      int64              `json:"total" example:"25"`
	Page       int                `json:"page" example:"1"`
	PageSize   int                `json:"page_size" example:"20"`
	TotalPages int                `json:"total_pages" example:"2"`
}

// SEOAuditFilter represents filter parameters for listing audits
type SEOAuditFilter struct {
	WebsiteID       *uint      `json:"website_id" example:"1"`
	AuditType       *string    `json:"audit_type" validate:"omitempty,oneof=full page technical content performance accessibility" example:"full"`
	Status          *string    `json:"status" validate:"omitempty,oneof=pending running completed failed cancelled deleted" example:"completed"`
	MinOverallScore *float64   `json:"min_overall_score" example:"70.0"`
	MaxOverallScore *float64   `json:"max_overall_score" example:"100.0"`
	CreatedBy       *uint      `json:"created_by" example:"123"`
	Search          string     `json:"search" example:"monthly"`
	DateFrom        *time.Time `json:"date_from" example:"2024-01-01T00:00:00Z"`
	DateTo          *time.Time `json:"date_to" example:"2024-12-31T23:59:59Z"`
	Page            int        `json:"page" validate:"min=1" example:"1"`
	PageSize        int        `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy          string     `json:"sort_by" validate:"omitempty,oneof=id created_at updated_at audit_started_at audit_completed_at overall_score" example:"audit_completed_at"`
	SortOrder       string     `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// SEOAuditStatsResponse represents audit statistics
type SEOAuditStatsResponse struct {
	TotalAudits       int                        `json:"total_audits" example:"50"`
	CompletedAudits   int                        `json:"completed_audits" example:"45"`
	RunningAudits     int                        `json:"running_audits" example:"2"`
	FailedAudits      int                        `json:"failed_audits" example:"3"`
	AverageScore      float64                    `json:"average_score" example:"82.5"`
	HighestScore      float64                    `json:"highest_score" example:"95.8"`
	LowestScore       float64                    `json:"lowest_score" example:"65.2"`
	ScoreDistribution map[string]int             `json:"score_distribution"`
	ByType            map[string]AuditTypeStats  `json:"by_type"`
	TrendData         []models.ScoreDataPoint    `json:"trend_data"`
	RecentAudits      []SEOAuditResponse         `json:"recent_audits"`
}

// AuditTypeStats represents statistics by audit type
type AuditTypeStats struct {
	Type         string  `json:"type" example:"full"`
	Count        int     `json:"count" example:"20"`
	AverageScore float64 `json:"average_score" example:"85.2"`
	LastAudit    *time.Time `json:"last_audit,omitempty"`
}

// SEOAuditRunRequest represents request to run an audit
type SEOAuditRunRequest struct {
	AuditID         uint   `json:"audit_id" validate:"required" example:"1"`
	Priority        string `json:"priority" validate:"omitempty,oneof=low normal high urgent" example:"normal"`
	RunAsync        bool   `json:"run_async" example:"true"`
	NotifyOnComplete bool  `json:"notify_on_complete" example:"true"`
	OverrideConfig  *models.AuditConfig `json:"override_config"`
}

// SEOAuditRunResponse represents response for running audit
type SEOAuditRunResponse struct {
	Success   bool   `json:"success" example:"true"`
	Message   string `json:"message" example:"Audit started successfully"`
	AuditID   uint   `json:"audit_id" example:"1"`
	TaskID    string `json:"task_id,omitempty" example:"task_audit_abc123"`
	EstimatedDuration int `json:"estimated_duration" example:"1800"`
	StartedAt time.Time `json:"started_at"`
}

// SEOAuditCancelRequest represents request to cancel audit
type SEOAuditCancelRequest struct {
	AuditID uint   `json:"audit_id" validate:"required" example:"1"`
	Reason  string `json:"reason,omitempty" example:"User requested cancellation"`
}

// SEOAuditCancelResponse represents response for cancelling audit
type SEOAuditCancelResponse struct {
	Success     bool      `json:"success" example:"true"`
	Message     string    `json:"message" example:"Audit cancelled successfully"`
	AuditID     uint      `json:"audit_id" example:"1"`
	CancelledAt time.Time `json:"cancelled_at"`
}

// SEOAuditCompareRequest represents request to compare audits
type SEOAuditCompareRequest struct {
	BaselineAuditID uint `json:"baseline_audit_id" validate:"required" example:"1"`
	CompareAuditID  uint `json:"compare_audit_id" validate:"required" example:"2"`
	CompareMetrics  []string `json:"compare_metrics" example:"[\"overall_score\",\"technical_score\",\"performance_score\"]"`
	IncludeDetails  bool `json:"include_details" example:"true"`
}

// SEOAuditCompareResponse represents response for audit comparison
type SEOAuditCompareResponse struct {
	BaselineAudit   SEOAuditResponse         `json:"baseline_audit"`
	CompareAudit    SEOAuditResponse         `json:"compare_audit"`
	Comparison      AuditComparisonResult    `json:"comparison"`
	ComparedAt      time.Time                `json:"compared_at"`
}

// AuditComparisonResult represents comparison results between two audits
type AuditComparisonResult struct {
	OverallScoreChange        float64                   `json:"overall_score_change" example:"5.5"`
	TechnicalScoreChange      float64                   `json:"technical_score_change" example:"2.0"`
	ContentScoreChange        float64                   `json:"content_score_change" example:"-1.5"`
	PerformanceScoreChange    float64                   `json:"performance_score_change" example:"8.2"`
	AccessibilityScoreChange  float64                   `json:"accessibility_score_change" example:"3.1"`
	UserExperienceScoreChange float64                   `json:"user_experience_score_change" example:"1.5"`
	IssueCountChange          int                       `json:"issue_count_change" example:"-5"`
	NewIssues                 []models.AuditIssue       `json:"new_issues,omitempty"`
	ResolvedIssues            []models.AuditIssue       `json:"resolved_issues,omitempty"`
	ImprovedAreas             []string                  `json:"improved_areas" example:"[\"performance\",\"technical\"]"`
	RegressedAreas            []string                  `json:"regressed_areas" example:"[\"content\"]"`
	Summary                   *models.ComparisonSummary `json:"summary"`
}

// SEOAuditExportRequest represents request for exporting audit results
type SEOAuditExportRequest struct {
	AuditIDs        []uint   `json:"audit_ids" validate:"required,min=1" example:"1,2,3"`
	Format          string   `json:"format" validate:"required,oneof=pdf csv json excel" example:"pdf"`
	IncludeSummary  bool     `json:"include_summary" example:"true"`
	IncludeDetails  bool     `json:"include_details" example:"true"`
	IncludeCharts   bool     `json:"include_charts" example:"true"`
	IncludeTrends   bool     `json:"include_trends" example:"false"`
	Sections        []string `json:"sections" example:"[\"overview\",\"issues\",\"recommendations\"]"`
	Language        string   `json:"language" validate:"omitempty,max=10" example:"en"`
	Template        string   `json:"template" validate:"omitempty" example:"standard"`
}

// SEOAuditExportResponse represents response for audit export
type SEOAuditExportResponse struct {
	Success     bool      `json:"success" example:"true"`
	Message     string    `json:"message" example:"Audit report exported successfully"`
	Format      string    `json:"format" example:"pdf"`
	DownloadURL string    `json:"download_url" example:"https://api.example.com/exports/audit_report_123.pdf"`
	ExportID    string    `json:"export_id" example:"export_abc123"`
	FileSize    int       `json:"file_size" example:"1048576"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// SEOAuditScheduleRequest represents request for scheduling audits
type SEOAuditScheduleRequest struct {
	WebsiteID       uint                   `json:"website_id" validate:"required" example:"1"`
	AuditType       string                 `json:"audit_type" validate:"required,oneof=full page technical content performance accessibility" example:"full"`
	AuditName       string                 `json:"audit_name" validate:"required,max=255" example:"Weekly SEO Audit"`
	Schedule        string                 `json:"schedule" validate:"required" example:"0 0 * * 1"`
	AuditConfig     *models.AuditConfig    `json:"audit_config"`
	NotifyOnComplete bool                  `json:"notify_on_complete" example:"true"`
	NotifyEmail     *string                `json:"notify_email" validate:"omitempty,email" example:"<EMAIL>"`
	IsActive        bool                   `json:"is_active" example:"true"`
	NextRun         *time.Time             `json:"next_run" example:"2024-01-22T00:00:00Z"`
}

// SEOAuditScheduleResponse represents response for audit scheduling
type SEOAuditScheduleResponse struct {
	Success     bool      `json:"success" example:"true"`
	Message     string    `json:"message" example:"Audit scheduled successfully"`
	ScheduleID  uint      `json:"schedule_id" example:"1"`
	NextRun     time.Time `json:"next_run"`
	CronExpression string `json:"cron_expression" example:"0 0 * * 1"`
}

// SEOAuditBulkActionRequest represents request for bulk operations on audits
type SEOAuditBulkActionRequest struct {
	AuditIDs []uint `json:"audit_ids" validate:"required,min=1" example:"1,2,3"`
	Action   string `json:"action" validate:"required,oneof=run cancel delete export archive" example:"run"`
	Settings map[string]interface{} `json:"settings,omitempty"`
	Reason   string `json:"reason,omitempty" example:"Bulk audit execution for monthly review"`
}

// SEOAuditBulkActionResponse represents response for bulk operations
type SEOAuditBulkActionResponse struct {
	Success      bool                 `json:"success" example:"true"`
	Message      string               `json:"message" example:"Bulk action completed successfully"`
	ProcessedIDs []uint               `json:"processed_ids" example:"1,2,3"`
	FailedIDs    []uint               `json:"failed_ids,omitempty"`
	Results      []AuditActionResult  `json:"results"`
	TaskID       string               `json:"task_id,omitempty" example:"task_bulk_audit_abc123"`
}

// AuditActionResult represents result for individual audit action
type AuditActionResult struct {
	AuditID uint   `json:"audit_id" example:"1"`
	Action  string `json:"action" example:"run"`
	Success bool   `json:"success" example:"true"`
	Message string `json:"message" example:"Audit started successfully"`
	TaskID  string `json:"task_id,omitempty" example:"task_audit_123"`
	Error   string `json:"error,omitempty"`
}

// ToServiceModel converts SEOAuditCreateRequest to models.CreateSEOAuditRequest
func (r *SEOAuditCreateRequest) ToServiceModel() models.CreateSEOAuditRequest {
	return models.CreateSEOAuditRequest{
		WebsiteID:              r.WebsiteID,
		AuditType:              models.AuditType(r.AuditType),
		AuditName:              r.AuditName,
		AuditDescription:       r.AuditDescription,
		AuditURL:               r.AuditURL,
		CrawlDepth:             r.CrawlDepth,
		MaxPages:               r.MaxPages,
		IncludeExternalLinks:   r.IncludeExternalLinks,
		CheckImages:            r.CheckImages,
		CheckPerformance:       r.CheckPerformance,
		AuditConfig:            r.AuditConfig,
	}
}

// ToServiceModel converts SEOAuditUpdateRequest to models.UpdateSEOAuditRequest
func (r *SEOAuditUpdateRequest) ToServiceModel() models.UpdateSEOAuditRequest {
	return models.UpdateSEOAuditRequest{
		AuditName:              r.AuditName,
		AuditDescription:       r.AuditDescription,
		Status:                 (*models.AuditStatus)(r.Status),
		OverallScore:           r.OverallScore,
		TechnicalScore:         r.TechnicalScore,
		ContentScore:           r.ContentScore,
		PerformanceScore:       r.PerformanceScore,
		AccessibilityScore:     r.AccessibilityScore,
		UserExperienceScore:    r.UserExperienceScore,
		AuditResults:           r.AuditResults,
		IssuesSummary:          r.IssuesSummary,
		Recommendations:        r.Recommendations,
		ComparisonData:         r.ComparisonData,
		ErrorMessage:           r.ErrorMessage,
	}
}