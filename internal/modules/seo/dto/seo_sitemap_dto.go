package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
)

// SEOSitemapCreateRequest represents request to create SEO sitemap
type SEOSitemapCreateRequest struct {
	WebsiteID uint `json:"website_id" validate:"required" example:"1"`

	// Sitemap Information
	Type        models.SitemapType `json:"type" validate:"required,oneof=pages blog static index" example:"pages"`
	Name        string             `json:"name" validate:"required,max=255" example:"Main Pages Sitemap"`
	Description *string            `json:"description" validate:"omitempty,max=1000" example:"Sitemap containing all main website pages"`
	Filename    string             `json:"filename" validate:"required,max=255" example:"sitemap-pages.xml"`

	// Content Information
	URLCount   int  `json:"url_count" validate:"omitempty,min=0" example:"150"`
	FileSize   int  `json:"file_size" validate:"omitempty,min=0" example:"25600"`
	Compressed bool `json:"compressed" example:"true"`

	// Generation Settings
	GenerationSettings map[string]interface{} `json:"generation_settings" example:"{\"include_images\":true,\"max_urls\":50000}"`

	// Status
	Status models.SitemapStatus `json:"status" validate:"omitempty,oneof=active pending error deleted" example:"active"`
}

// SEOSitemapUpdateRequest represents request to update SEO sitemap
type SEOSitemapUpdateRequest struct {
	// Sitemap Information
	Name        *string `json:"name" validate:"omitempty,max=255" example:"Updated Pages Sitemap"`
	Description *string `json:"description" validate:"omitempty,max=1000" example:"Updated sitemap description"`
	Filename    *string `json:"filename" validate:"omitempty,max=255" example:"updated-sitemap-pages.xml"`

	// Content Information
	URLCount   *int  `json:"url_count" validate:"omitempty,min=0" example:"200"`
	FileSize   *int  `json:"file_size" validate:"omitempty,min=0" example:"35600"`
	Compressed *bool `json:"compressed" example:"false"`

	// Generation Settings
	GenerationSettings map[string]interface{} `json:"generation_settings"`

	// Status
	Status *models.SitemapStatus `json:"status" validate:"omitempty,oneof=active pending error deleted" example:"active"`
}

// SEOSitemapResponse represents SEO sitemap information in responses
type SEOSitemapResponse struct {
	ID        uint `json:"id" example:"1"`
	WebsiteID uint `json:"website_id" example:"1"`
	TenantID  uint `json:"tenant_id" example:"1"`

	// Sitemap Information
	Type        models.SitemapType `json:"type" example:"pages"`
	Name        string             `json:"name" example:"Main Pages Sitemap"`
	Description *string            `json:"description,omitempty" example:"Sitemap containing all main pages"`
	Filename    string             `json:"filename" example:"sitemap-pages.xml"`

	// Content Information
	URLCount   int  `json:"url_count" example:"150"`
	FileSize   int  `json:"file_size" example:"25600"`
	Compressed bool `json:"compressed" example:"true"`

	// Generation Settings
	GenerationSettings map[string]interface{} `json:"generation_settings"`

	// Timestamps
	GeneratedAt time.Time `json:"generated_at"`

	// Status and Timestamps
	Status    models.SitemapStatus `json:"status" example:"active"`
	CreatedAt time.Time            `json:"created_at"`
	UpdatedAt time.Time            `json:"updated_at"`

	// Additional computed fields
	DownloadURL   string  `json:"download_url,omitempty" example:"https://api.example.com/sitemaps/sitemap-pages.xml"`
	LastIndexed   *time.Time `json:"last_indexed,omitempty"`
	IndexingErrors int     `json:"indexing_errors" example:"0"`
}

// SEOSitemapListResponse represents response for listing SEO sitemaps
type SEOSitemapListResponse struct {
	Sitemaps   []SEOSitemapResponse `json:"sitemaps"`
	Total      int64                `json:"total" example:"25"`
	Page       int                  `json:"page" example:"1"`
	PageSize   int                  `json:"page_size" example:"20"`
	TotalPages int                  `json:"total_pages" example:"2"`
}

// SEOSitemapFilter represents filter parameters for listing sitemaps
type SEOSitemapFilter struct {
	WebsiteID  *uint                 `json:"website_id" example:"1"`
	Type       *models.SitemapType   `json:"type" validate:"omitempty,oneof=pages blog static index" example:"pages"`
	Status     *models.SitemapStatus `json:"status" validate:"omitempty,oneof=active pending error deleted" example:"active"`
	Name       *string               `json:"name" example:"pages"`
	Search     string                `json:"search" example:"main"`
	MinURLs    *int                  `json:"min_urls" example:"10"`
	MaxURLs    *int                  `json:"max_urls" example:"1000"`
	Compressed *bool                 `json:"compressed" example:"true"`
	DateFrom   *time.Time            `json:"date_from" example:"2024-01-01T00:00:00Z"`
	DateTo     *time.Time            `json:"date_to" example:"2024-12-31T23:59:59Z"`
	Page       int                   `json:"page" validate:"min=1" example:"1"`
	PageSize   int                   `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy     string                `json:"sort_by" validate:"omitempty,oneof=id name created_at updated_at generated_at url_count file_size" example:"generated_at"`
	SortOrder  string                `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// SEOSitemapStatsResponse represents sitemap statistics
type SEOSitemapStatsResponse struct {
	TotalSitemaps      int                     `json:"total_sitemaps" example:"25"`
	ActiveSitemaps     int                     `json:"active_sitemaps" example:"20"`
	PendingSitemaps    int                     `json:"pending_sitemaps" example:"3"`
	ErrorSitemaps      int                     `json:"error_sitemaps" example:"2"`
	TotalURLs          int                     `json:"total_urls" example:"5000"`
	TotalFileSize      int                     `json:"total_file_size" example:"1048576"`
	CompressedSitemaps int                     `json:"compressed_sitemaps" example:"15"`
	ByType             map[string]SitemapTypeStats `json:"by_type"`
	RecentlyGenerated  []SEOSitemapResponse    `json:"recently_generated"`
	LargestSitemaps    []SEOSitemapResponse    `json:"largest_sitemaps"`
}

// SitemapTypeStats represents statistics by sitemap type
type SitemapTypeStats struct {
	Type      models.SitemapType `json:"type" example:"pages"`
	Count     int                `json:"count" example:"5"`
	TotalURLs int                `json:"total_urls" example:"1500"`
	AvgURLs   float64            `json:"avg_urls" example:"300.0"`
	TotalSize int                `json:"total_size" example:"256000"`
}

// SEOSitemapGenerateRequest represents request to generate sitemap
type SEOSitemapGenerateRequest struct {
	Type               models.SitemapType     `json:"type" validate:"required,oneof=pages blog static index" example:"pages"`
	Name               string                 `json:"name" validate:"required,max=255" example:"Generated Pages Sitemap"`
	IncludeImages      bool                   `json:"include_images" example:"true"`
	IncludeVideos      bool                   `json:"include_videos" example:"false"`
	IncludeNews        bool                   `json:"include_news" example:"false"`
	MaxURLs            int                    `json:"max_urls" validate:"omitempty,min=1,max=50000" example:"50000"`
	PriorityDefault    float64                `json:"priority_default" validate:"omitempty,min=0,max=1" example:"0.5"`
	ChangeFreqDefault  string                 `json:"change_freq_default" validate:"omitempty,oneof=always hourly daily weekly monthly yearly never" example:"weekly"`
	ExcludePatterns    []string               `json:"exclude_patterns" example:"[\"/admin/*\",\"/private/*\"]"`
	IncludePatterns    []string               `json:"include_patterns" example:"[\"/blog/*\",\"/products/*\"]"`
	CustomSettings     map[string]interface{} `json:"custom_settings"`
	Compress           bool                   `json:"compress" example:"true"`
	AutoSubmit         bool                   `json:"auto_submit" example:"true"`
}

// SEOSitemapGenerateResponse represents response for sitemap generation
type SEOSitemapGenerateResponse struct {
	Success     bool                 `json:"success" example:"true"`
	Message     string               `json:"message" example:"Sitemap generated successfully"`
	SitemapID   uint                 `json:"sitemap_id" example:"1"`
	Sitemap     *SEOSitemapResponse  `json:"sitemap"`
	GenerationStats SitemapGenerationStats `json:"generation_stats"`
	TaskID      string               `json:"task_id,omitempty" example:"task_abc123"`
}

// SitemapGenerationStats represents statistics from sitemap generation
type SitemapGenerationStats struct {
	ProcessedURLs   int           `json:"processed_urls" example:"150"`
	IncludedURLs    int           `json:"included_urls" example:"142"`
	ExcludedURLs    int           `json:"excluded_urls" example:"8"`
	ErrorURLs       int           `json:"error_urls" example:"0"`
	GenerationTime  time.Duration `json:"generation_time" example:"2500000000"`
	FileSize        int           `json:"file_size" example:"25600"`
	CompressionRatio float64      `json:"compression_ratio" example:"0.75"`
}

// SEOSitemapSubmitRequest represents request to submit sitemap to search engines
type SEOSitemapSubmitRequest struct {
	SitemapIDs       []uint   `json:"sitemap_ids" validate:"required,min=1" example:"1,2,3"`
	SearchEngines    []string `json:"search_engines" validate:"required,min=1" example:"google,bing,yandex"`
	NotifyIndexNow   bool     `json:"notify_index_now" example:"true"`
	SubmitToConsoles bool     `json:"submit_to_consoles" example:"true"`
}

// SEOSitemapSubmitResponse represents response for sitemap submission
type SEOSitemapSubmitResponse struct {
	Success       bool                      `json:"success" example:"true"`
	Message       string                    `json:"message" example:"Sitemaps submitted successfully"`
	SubmittedIDs  []uint                    `json:"submitted_ids" example:"1,2,3"`
	FailedIDs     []uint                    `json:"failed_ids,omitempty"`
	Results       []SitemapSubmissionResult `json:"results"`
	SubmissionID  string                    `json:"submission_id" example:"sub_abc123"`
}

// SitemapSubmissionResult represents result for individual sitemap submission
type SitemapSubmissionResult struct {
	SitemapID     uint   `json:"sitemap_id" example:"1"`
	SearchEngine  string `json:"search_engine" example:"google"`
	Success       bool   `json:"success" example:"true"`
	StatusCode    int    `json:"status_code" example:"200"`
	Message       string `json:"message" example:"Successfully submitted"`
	SubmissionURL string `json:"submission_url" example:"https://www.google.com/ping?sitemap=..."`
	SubmittedAt   time.Time `json:"submitted_at"`
}

// SEOSitemapValidateRequest represents request for sitemap validation
type SEOSitemapValidateRequest struct {
	SitemapID       *uint   `json:"sitemap_id" example:"1"`
	SitemapContent  *string `json:"sitemap_content" example:"<?xml version=\"1.0\"..."`
	CheckURLs       bool    `json:"check_urls" example:"true"`
	CheckImages     bool    `json:"check_images" example:"true"`
	CheckVideos     bool    `json:"check_videos" example:"false"`
	ValidateSchema  bool    `json:"validate_schema" example:"true"`
	MaxURLsToCheck  int     `json:"max_urls_to_check" validate:"omitempty,min=1,max=1000" example:"100"`
}

// SEOSitemapValidateResponse represents response for sitemap validation
type SEOSitemapValidateResponse struct {
	IsValid          bool                          `json:"is_valid" example:"true"`
	ValidationScore  float64                       `json:"validation_score" example:"95.5"`
	Errors           []models.ValidationError      `json:"errors,omitempty"`
	Warnings         []models.ValidationWarning    `json:"warnings,omitempty"`
	Suggestions      []models.ValidationSuggestion `json:"suggestions,omitempty"`
	URLResults       []SitemapURLValidation        `json:"url_results,omitempty"`
	SchemaValidation *SitemapSchemaValidation      `json:"schema_validation"`
	Summary          SitemapValidationSummary      `json:"summary"`
}

// SitemapURLValidation represents validation result for individual URL
type SitemapURLValidation struct {
	URL            string    `json:"url" example:"https://example.com/page"`
	IsAccessible   bool      `json:"is_accessible" example:"true"`
	StatusCode     int       `json:"status_code" example:"200"`
	ContentType    string    `json:"content_type" example:"text/html"`
	LastModified   *time.Time `json:"last_modified,omitempty"`
	Issues         []string  `json:"issues,omitempty"`
	Warnings       []string  `json:"warnings,omitempty"`
}

// SitemapSchemaValidation represents XML schema validation result
type SitemapSchemaValidation struct {
	IsValidXML     bool     `json:"is_valid_xml" example:"true"`
	SchemaVersion  string   `json:"schema_version" example:"0.9"`
	NamespaceValid bool     `json:"namespace_valid" example:"true"`
	SchemaErrors   []string `json:"schema_errors,omitempty"`
}

// SitemapValidationSummary represents summary of validation results
type SitemapValidationSummary struct {
	TotalURLs       int `json:"total_urls" example:"150"`
	ValidURLs       int `json:"valid_urls" example:"145"`
	ErrorURLs       int `json:"error_urls" example:"3"`
	WarningURLs     int `json:"warning_urls" example:"2"`
	InaccessibleURLs int `json:"inaccessible_urls" example:"3"`
	RedirectURLs    int `json:"redirect_urls" example:"5"`
}

// SEOSitemapBulkActionRequest represents request for bulk operations on sitemaps
type SEOSitemapBulkActionRequest struct {
	SitemapIDs []uint `json:"sitemap_ids" validate:"required,min=1" example:"1,2,3"`
	Action     string `json:"action" validate:"required,oneof=regenerate submit validate activate deactivate delete" example:"regenerate"`
	Settings   map[string]interface{} `json:"settings,omitempty"`
	Reason     string `json:"reason,omitempty" example:"Bulk regeneration for content updates"`
}

// SEOSitemapBulkActionResponse represents response for bulk operations
type SEOSitemapBulkActionResponse struct {
	Success      bool     `json:"success" example:"true"`
	Message      string   `json:"message" example:"Bulk action completed successfully"`
	ProcessedIDs []uint   `json:"processed_ids" example:"1,2,3"`
	FailedIDs    []uint   `json:"failed_ids,omitempty"`
	SkippedIDs   []uint   `json:"skipped_ids,omitempty"`
	Results      []SitemapActionResult `json:"results"`
	TaskID       string   `json:"task_id,omitempty" example:"task_bulk_abc123"`
}

// SitemapActionResult represents result for individual sitemap action
type SitemapActionResult struct {
	SitemapID uint   `json:"sitemap_id" example:"1"`
	Action    string `json:"action" example:"regenerate"`
	Success   bool   `json:"success" example:"true"`
	Message   string `json:"message" example:"Sitemap regenerated successfully"`
	Error     string `json:"error,omitempty"`
}

// SEOSitemapAnalyzeRequest represents request for sitemap analysis
type SEOSitemapAnalyzeRequest struct {
	SitemapID       uint   `json:"sitemap_id" validate:"required" example:"1"`
	AnalyzeContent  bool   `json:"analyze_content" example:"true"`
	CheckDuplicates bool   `json:"check_duplicates" example:"true"`
	CheckRedirects  bool   `json:"check_redirects" example:"true"`
	CheckCanonicals bool   `json:"check_canonicals" example:"true"`
	AnalyzePerformance bool `json:"analyze_performance" example:"false"`
}

// SEOSitemapAnalyzeResponse represents response for sitemap analysis
type SEOSitemapAnalyzeResponse struct {
	AnalysisID      string                   `json:"analysis_id" example:"analysis_abc123"`
	SitemapID       uint                     `json:"sitemap_id" example:"1"`
	OverallScore    float64                  `json:"overall_score" example:"85.5"`
	Issues          []models.SEOIssue        `json:"issues,omitempty"`
	Opportunities   []models.SEOOpportunity  `json:"opportunities,omitempty"`
	ContentAnalysis SitemapContentAnalysis   `json:"content_analysis"`
	DuplicateCheck  SitemapDuplicateCheck    `json:"duplicate_check"`
	RedirectCheck   SitemapRedirectCheck     `json:"redirect_check"`
	CanonicalCheck  SitemapCanonicalCheck    `json:"canonical_check"`
	Recommendations []string                 `json:"recommendations,omitempty"`
	AnalyzedAt      time.Time                `json:"analyzed_at"`
}

// SitemapContentAnalysis represents content analysis results
type SitemapContentAnalysis struct {
	AvgPriority        float64  `json:"avg_priority" example:"0.6"`
	MostCommonChangeFreq string `json:"most_common_change_freq" example:"weekly"`
	PriorityDistribution map[string]int `json:"priority_distribution"`
	ChangeFreqDistribution map[string]int `json:"change_freq_distribution"`
	LastModifiedSpread   time.Duration `json:"last_modified_spread"`
}

// SitemapDuplicateCheck represents duplicate URL check results
type SitemapDuplicateCheck struct {
	HasDuplicates   bool              `json:"has_duplicates" example:"false"`
	DuplicateCount  int               `json:"duplicate_count" example:"0"`
	DuplicateGroups []SitemapDuplicateGroup `json:"duplicate_groups,omitempty"`
}

// SitemapDuplicateGroup represents a group of duplicate URLs
type SitemapDuplicateGroup struct {
	URLs  []string `json:"urls" example:"[\"/page\",\"/page/\"]"`
	Count int      `json:"count" example:"2"`
}

// SitemapRedirectCheck represents redirect check results
type SitemapRedirectCheck struct {
	HasRedirects    bool                  `json:"has_redirects" example:"true"`
	RedirectCount   int                   `json:"redirect_count" example:"5"`
	RedirectDetails []SitemapRedirectDetail `json:"redirect_details,omitempty"`
}

// SitemapRedirectDetail represents details of redirected URL
type SitemapRedirectDetail struct {
	OriginalURL     string `json:"original_url" example:"/old-page"`
	FinalURL        string `json:"final_url" example:"/new-page"`
	RedirectType    string `json:"redirect_type" example:"301"`
	RedirectChain   int    `json:"redirect_chain" example:"1"`
}

// SitemapCanonicalCheck represents canonical URL check results
type SitemapCanonicalCheck struct {
	HasCanonicalIssues  bool                    `json:"has_canonical_issues" example:"false"`
	CanonicalConflicts  int                     `json:"canonical_conflicts" example:"0"`
	ConflictDetails     []SitemapCanonicalConflict `json:"conflict_details,omitempty"`
}

// SitemapCanonicalConflict represents canonical URL conflict
type SitemapCanonicalConflict struct {
	SitemapURL   string `json:"sitemap_url" example:"/page"`
	CanonicalURL string `json:"canonical_url" example:"/different-page"`
	ConflictType string `json:"conflict_type" example:"different_canonical"`
}

// ToServiceModel converts SEOSitemapCreateRequest to models.SEOSitemapCreateRequest
func (r *SEOSitemapCreateRequest) ToServiceModel() models.SEOSitemapCreateRequest {
	return models.SEOSitemapCreateRequest{
		WebsiteID:          r.WebsiteID,
		Type:               r.Type,
		Name:               r.Name,
		Description:        r.Description,
		Filename:           r.Filename,
		URLCount:           r.URLCount,
		FileSize:           r.FileSize,
		Compressed:         r.Compressed,
		GenerationSettings: r.GenerationSettings,
		Status:             r.Status,
	}
}

// ToServiceModel converts SEOSitemapUpdateRequest to models.SEOSitemapUpdateRequest
func (r *SEOSitemapUpdateRequest) ToServiceModel() models.SEOSitemapUpdateRequest {
	return models.SEOSitemapUpdateRequest{
		Name:               r.Name,
		Description:        r.Description,
		Filename:           r.Filename,
		URLCount:           r.URLCount,
		FileSize:           r.FileSize,
		Compressed:         r.Compressed,
		GenerationSettings: r.GenerationSettings,
		Status:             r.Status,
	}
}