package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
)

// SEOMetaCreateRequest represents request to create SEO metadata
type SEOMetaCreateRequest struct {
	WebsiteID uint   `json:"website_id" validate:"required" example:"1"`
	PageType  string `json:"page_type" validate:"required,oneof=page post category tag product custom" example:"page"`
	PageID    *uint  `json:"page_id" example:"123"`
	PageURL   string `json:"page_url" validate:"required,url" example:"https://example.com/about"`
	PagePath  string `json:"page_path" validate:"required" example:"/about"`

	// SEO Meta Tags
	MetaTitle       *string `json:"meta_title" validate:"omitempty,max=255" example:"About Us - Best Company Ever"`
	MetaDescription *string `json:"meta_description" validate:"omitempty,max=500" example:"Learn about our company history, mission, and values that drive our success."`
	MetaKeywords    *string `json:"meta_keywords" example:"about,company,mission,values"`
	MetaRobots      string  `json:"meta_robots" validate:"omitempty,max=100" example:"index,follow"`
	CanonicalURL    *string `json:"canonical_url" validate:"omitempty,url" example:"https://example.com/about"`

	// Open Graph Meta Tags
	OGTitle       *string `json:"og_title" validate:"omitempty,max=255" example:"About Us - Best Company Ever"`
	OGDescription *string `json:"og_description" validate:"omitempty,max=500" example:"Learn about our company history and mission"`
	OGImage       *string `json:"og_image" validate:"omitempty,url" example:"https://example.com/images/about-og.jpg"`
	OGType        string  `json:"og_type" validate:"omitempty,max=50" example:"website"`
	OGURL         *string `json:"og_url" validate:"omitempty,url" example:"https://example.com/about"`
	OGSiteName    *string `json:"og_site_name" validate:"omitempty,max=255" example:"Best Company Ever"`
	OGLocale      string  `json:"og_locale" validate:"omitempty,max=10" example:"en_US"`

	// Twitter Card Meta Tags
	TwitterCard        string  `json:"twitter_card" validate:"omitempty,max=50" example:"summary_large_image"`
	TwitterTitle       *string `json:"twitter_title" validate:"omitempty,max=255" example:"About Us - Best Company Ever"`
	TwitterDescription *string `json:"twitter_description" validate:"omitempty,max=500" example:"Learn about our company"`
	TwitterImage       *string `json:"twitter_image" validate:"omitempty,url" example:"https://example.com/images/about-twitter.jpg"`
	TwitterCreator     *string `json:"twitter_creator" validate:"omitempty,max=255" example:"@bestcompany"`
	TwitterSite        *string `json:"twitter_site" validate:"omitempty,max=255" example:"@bestcompany"`

	// Schema.org Structured Data
	SchemaType *string                     `json:"schema_type" validate:"omitempty,max=100" example:"Organization"`
	SchemaData *models.SchemaData          `json:"schema_data" example:"{\"name\":\"Best Company\",\"url\":\"https://example.com\"}"`

	// Additional Meta Tags
	AdditionalMeta *models.AdditionalMetaTags `json:"additional_meta"`

	// SEO Settings
	FocusKeyword *string `json:"focus_keyword" validate:"omitempty,max=255" example:"about company"`

	// Status and Indexing
	IsIndexed         *bool    `json:"is_indexed" example:"true"`
	IsSitemapIncluded *bool    `json:"is_sitemap_included" example:"true"`
	Priority          *float64 `json:"priority" validate:"omitempty,min=0,max=1" example:"0.8"`
	ChangeFrequency   *string  `json:"change_frequency" validate:"omitempty,oneof=always hourly daily weekly monthly yearly never" example:"monthly"`
}

// SEOMetaUpdateRequest represents request to update SEO metadata
type SEOMetaUpdateRequest struct {
	MetaTitle       *string `json:"meta_title" validate:"omitempty,max=255" example:"Updated About Us Page"`
	MetaDescription *string `json:"meta_description" validate:"omitempty,max=500" example:"Updated description"`
	MetaKeywords    *string `json:"meta_keywords" example:"updated,keywords"`
	MetaRobots      *string `json:"meta_robots" validate:"omitempty,max=100" example:"index,follow"`
	CanonicalURL    *string `json:"canonical_url" validate:"omitempty,url" example:"https://example.com/about"`

	// Open Graph Meta Tags
	OGTitle       *string `json:"og_title" validate:"omitempty,max=255" example:"Updated About Us"`
	OGDescription *string `json:"og_description" validate:"omitempty,max=500" example:"Updated OG description"`
	OGImage       *string `json:"og_image" validate:"omitempty,url" example:"https://example.com/images/new-og.jpg"`
	OGType        *string `json:"og_type" validate:"omitempty,max=50" example:"website"`
	OGURL         *string `json:"og_url" validate:"omitempty,url" example:"https://example.com/about"`
	OGSiteName    *string `json:"og_site_name" validate:"omitempty,max=255" example:"Best Company"`
	OGLocale      *string `json:"og_locale" validate:"omitempty,max=10" example:"en_US"`

	// Twitter Card Meta Tags
	TwitterCard        *string `json:"twitter_card" validate:"omitempty,max=50" example:"summary"`
	TwitterTitle       *string `json:"twitter_title" validate:"omitempty,max=255" example:"Updated About"`
	TwitterDescription *string `json:"twitter_description" validate:"omitempty,max=500" example:"Updated Twitter desc"`
	TwitterImage       *string `json:"twitter_image" validate:"omitempty,url" example:"https://example.com/images/new-twitter.jpg"`
	TwitterCreator     *string `json:"twitter_creator" validate:"omitempty,max=255" example:"@bestcompany"`
	TwitterSite        *string `json:"twitter_site" validate:"omitempty,max=255" example:"@bestcompany"`

	// Schema.org Structured Data
	SchemaType *string             `json:"schema_type" validate:"omitempty,max=100" example:"Organization"`
	SchemaData *models.SchemaData  `json:"schema_data"`

	// Additional Meta Tags
	AdditionalMeta *models.AdditionalMetaTags `json:"additional_meta"`

	// SEO Settings
	FocusKeyword *string `json:"focus_keyword" validate:"omitempty,max=255" example:"company about"`

	// Status and Indexing
	IsIndexed         *bool    `json:"is_indexed" example:"true"`
	IsSitemapIncluded *bool    `json:"is_sitemap_included" example:"true"`
	Priority          *float64 `json:"priority" validate:"omitempty,min=0,max=1" example:"0.9"`
	ChangeFrequency   *string  `json:"change_frequency" validate:"omitempty,oneof=always hourly daily weekly monthly yearly never" example:"weekly"`

	// Status
	Status *string `json:"status" validate:"omitempty,oneof=active inactive deleted" example:"active"`
}

// SEOMetaResponse represents SEO metadata information in responses
type SEOMetaResponse struct {
	ID        uint `json:"id" example:"1"`
	WebsiteID uint `json:"website_id" example:"1"`
	TenantID  uint `json:"tenant_id" example:"1"`

	// Page Information
	PageType string `json:"page_type" example:"page"`
	PageID   *uint  `json:"page_id" example:"123"`
	PageURL  string `json:"page_url" example:"https://example.com/about"`
	PagePath string `json:"page_path" example:"/about"`

	// SEO Meta Tags
	MetaTitle       *string `json:"meta_title,omitempty" example:"About Us - Best Company Ever"`
	MetaDescription *string `json:"meta_description,omitempty" example:"Learn about our company"`
	MetaKeywords    *string `json:"meta_keywords,omitempty" example:"about,company"`
	MetaRobots      string  `json:"meta_robots" example:"index,follow"`
	CanonicalURL    *string `json:"canonical_url,omitempty" example:"https://example.com/about"`

	// Open Graph Meta Tags
	OGTitle       *string `json:"og_title,omitempty" example:"About Us"`
	OGDescription *string `json:"og_description,omitempty" example:"Learn about our company"`
	OGImage       *string `json:"og_image,omitempty" example:"https://example.com/images/og.jpg"`
	OGType        string  `json:"og_type" example:"website"`
	OGURL         *string `json:"og_url,omitempty" example:"https://example.com/about"`
	OGSiteName    *string `json:"og_site_name,omitempty" example:"Best Company"`
	OGLocale      string  `json:"og_locale" example:"en_US"`

	// Twitter Card Meta Tags
	TwitterCard        string  `json:"twitter_card" example:"summary_large_image"`
	TwitterTitle       *string `json:"twitter_title,omitempty" example:"About Us"`
	TwitterDescription *string `json:"twitter_description,omitempty" example:"Learn about us"`
	TwitterImage       *string `json:"twitter_image,omitempty" example:"https://example.com/images/twitter.jpg"`
	TwitterCreator     *string `json:"twitter_creator,omitempty" example:"@company"`
	TwitterSite        *string `json:"twitter_site,omitempty" example:"@company"`

	// Schema.org Structured Data
	SchemaType *string                     `json:"schema_type,omitempty" example:"Organization"`
	SchemaData models.SchemaData           `json:"schema_data,omitempty"`

	// Additional Meta Tags
	AdditionalMeta models.AdditionalMetaTags `json:"additional_meta,omitempty"`

	// SEO Settings
	FocusKeyword     *string `json:"focus_keyword,omitempty" example:"about company"`
	SEOScore         float64 `json:"seo_score" example:"85.5"`
	ReadabilityScore float64 `json:"readability_score" example:"78.2"`

	// Status and Indexing
	IsIndexed         bool    `json:"is_indexed" example:"true"`
	IsSitemapIncluded bool    `json:"is_sitemap_included" example:"true"`
	Priority          float64 `json:"priority" example:"0.8"`
	ChangeFrequency   string  `json:"change_frequency" example:"monthly"`

	// Status and Timestamps
	Status    string    `json:"status" example:"active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Analysis data
	GeneratedTags []string             `json:"generated_tags,omitempty"`
	SEOAnalysis   *models.SEOAnalysis  `json:"seo_analysis,omitempty"`
}

// SEOMetaListResponse represents response for listing SEO metadata
type SEOMetaListResponse struct {
	SEOMeta    []SEOMetaResponse `json:"seo_meta"`
	Total      int64             `json:"total" example:"50"`
	Page       int               `json:"page" example:"1"`
	PageSize   int               `json:"page_size" example:"20"`
	TotalPages int               `json:"total_pages" example:"3"`
}

// SEOMetaFilter represents filter parameters for listing SEO metadata
type SEOMetaFilter struct {
	WebsiteID         *uint      `json:"website_id" example:"1"`
	PageType          *string    `json:"page_type" validate:"omitempty,oneof=page post category tag product custom" example:"page"`
	Status            *string    `json:"status" validate:"omitempty,oneof=active inactive deleted" example:"active"`
	IsIndexed         *bool      `json:"is_indexed" example:"true"`
	IsSitemapIncluded *bool      `json:"is_sitemap_included" example:"true"`
	FocusKeyword      *string    `json:"focus_keyword" example:"company"`
	MinSEOScore       *float64   `json:"min_seo_score" example:"50.0"`
	MaxSEOScore       *float64   `json:"max_seo_score" example:"100.0"`
	Search            string     `json:"search" example:"about"`
	CreatedAfter      *time.Time `json:"created_after" example:"2024-01-01T00:00:00Z"`
	CreatedBefore     *time.Time `json:"created_before" example:"2024-12-31T23:59:59Z"`
	Page              int        `json:"page" validate:"min=1" example:"1"`
	PageSize          int        `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy            string     `json:"sort_by" validate:"omitempty,oneof=id created_at updated_at seo_score page_type" example:"seo_score"`
	SortOrder         string     `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// SEOMetaValidationRequest represents request for SEO metadata validation
type SEOMetaValidationRequest struct {
	MetaTitle       *string                     `json:"meta_title" example:"Page Title"`
	MetaDescription *string                     `json:"meta_description" example:"Page description"`
	MetaKeywords    *string                     `json:"meta_keywords" example:"keyword1,keyword2"`
	FocusKeyword    *string                     `json:"focus_keyword" example:"main keyword"`
	Content         *string                     `json:"content" example:"Page content for analysis"`
	SchemaData      *models.SchemaData          `json:"schema_data"`
	AdditionalMeta  *models.AdditionalMetaTags  `json:"additional_meta"`
}

// SEOMetaValidationResponse represents SEO validation results
type SEOMetaValidationResponse struct {
	IsValid              bool                             `json:"is_valid" example:"true"`
	OverallScore         float64                          `json:"overall_score" example:"85.5"`
	TitleAnalysis        *models.TextAnalysis             `json:"title_analysis,omitempty"`
	DescriptionAnalysis  *models.TextAnalysis             `json:"description_analysis,omitempty"`
	KeywordAnalysis      *models.KeywordAnalysis          `json:"keyword_analysis,omitempty"`
	ReadabilityAnalysis  *models.ReadabilityAnalysis      `json:"readability_analysis,omitempty"`
	TechnicalAnalysis    *models.TechnicalAnalysis        `json:"technical_analysis,omitempty"`
	Errors               []models.ValidationError         `json:"errors,omitempty"`
	Warnings             []models.ValidationWarning       `json:"warnings,omitempty"`
	Suggestions          []models.ValidationSuggestion    `json:"suggestions,omitempty"`
	Recommendations      []string                         `json:"recommendations,omitempty"`
}

// SEOMetaBulkActionRequest represents request for bulk operations on SEO metadata
type SEOMetaBulkActionRequest struct {
	SEOMetaIDs []uint  `json:"seo_meta_ids" validate:"required,min=1" example:"1,2,3"`
	Action     string  `json:"action" validate:"required,oneof=index unindex include_sitemap exclude_sitemap activate deactivate delete" example:"index"`
	Reason     string  `json:"reason,omitempty" example:"Bulk indexing for new content"`
}

// SEOMetaBulkActionResponse represents response for bulk operations
type SEOMetaBulkActionResponse struct {
	Success       bool     `json:"success" example:"true"`
	Message       string   `json:"message" example:"Bulk action completed successfully"`
	ProcessedIDs  []uint   `json:"processed_ids" example:"1,2,3"`
	FailedIDs     []uint   `json:"failed_ids,omitempty"`
	SkippedIDs    []uint   `json:"skipped_ids,omitempty"`
	Errors        []string `json:"errors,omitempty"`
}

// SEOMetaAnalyzeRequest represents request for SEO analysis
type SEOMetaAnalyzeRequest struct {
	PageURL     string  `json:"page_url" validate:"required,url" example:"https://example.com/page"`
	Content     *string `json:"content" example:"Page content to analyze"`
	FocusKeyword *string `json:"focus_keyword" example:"target keyword"`
	AnalyzeImages bool   `json:"analyze_images" example:"true"`
	AnalyzeLinks  bool   `json:"analyze_links" example:"true"`
}

// SEOMetaAnalyzeResponse represents response for SEO analysis
type SEOMetaAnalyzeResponse struct {
	AnalysisID      string                       `json:"analysis_id" example:"analysis_abc123"`
	PageURL         string                       `json:"page_url" example:"https://example.com/page"`
	OverallScore    float64                      `json:"overall_score" example:"78.5"`
	TitleAnalysis   *models.TextAnalysis         `json:"title_analysis,omitempty"`
	DescriptionAnalysis *models.TextAnalysis     `json:"description_analysis,omitempty"`
	KeywordAnalysis *models.KeywordAnalysis      `json:"keyword_analysis,omitempty"`
	ReadabilityAnalysis *models.ReadabilityAnalysis `json:"readability_analysis,omitempty"`
	TechnicalAnalysis *models.TechnicalAnalysis  `json:"technical_analysis,omitempty"`
	Issues          []models.SEOIssue            `json:"issues,omitempty"`
	Opportunities   []models.SEOOpportunity      `json:"opportunities,omitempty"`
	Recommendations []string                     `json:"recommendations,omitempty"`
	SuggestedMeta   *models.SEOSuggestions       `json:"suggested_meta,omitempty"`
	AnalyzedAt      time.Time                    `json:"analyzed_at"`
}

// SEOMetaPreviewRequest represents request for meta tag preview
type SEOMetaPreviewRequest struct {
	MetaTitle       *string                     `json:"meta_title" example:"Page Title"`
	MetaDescription *string                     `json:"meta_description" example:"Page description"`
	OGTitle         *string                     `json:"og_title" example:"OG Title"`
	OGDescription   *string                     `json:"og_description" example:"OG Description"`
	OGImage         *string                     `json:"og_image" validate:"omitempty,url" example:"https://example.com/image.jpg"`
	TwitterTitle    *string                     `json:"twitter_title" example:"Twitter Title"`
	TwitterDescription *string                  `json:"twitter_description" example:"Twitter Description"`
	TwitterImage    *string                     `json:"twitter_image" validate:"omitempty,url" example:"https://example.com/image.jpg"`
	SchemaData      *models.SchemaData          `json:"schema_data"`
	AdditionalMeta  *models.AdditionalMetaTags  `json:"additional_meta"`
}

// SEOMetaPreviewResponse represents preview of how meta tags will appear
type SEOMetaPreviewResponse struct {
	GooglePreview   SEOGooglePreview   `json:"google_preview"`
	FacebookPreview SEOFacebookPreview `json:"facebook_preview"`
	TwitterPreview  SEOTwitterPreview  `json:"twitter_preview"`
	GeneratedHTML   []string           `json:"generated_html"`
	StructuredData  string             `json:"structured_data,omitempty"`
}

// SEOGooglePreview represents how content appears in Google search
type SEOGooglePreview struct {
	Title       string `json:"title" example:"Page Title"`
	URL         string `json:"url" example:"https://example.com/page"`
	Description string `json:"description" example:"Meta description text"`
}

// SEOFacebookPreview represents how content appears when shared on Facebook
type SEOFacebookPreview struct {
	Title       string  `json:"title" example:"OG Title"`
	Description string  `json:"description" example:"OG Description"`
	Image       *string `json:"image,omitempty" example:"https://example.com/image.jpg"`
	URL         string  `json:"url" example:"https://example.com/page"`
	SiteName    string  `json:"site_name" example:"Website Name"`
}

// SEOTwitterPreview represents how content appears when shared on Twitter
type SEOTwitterPreview struct {
	Card        string  `json:"card" example:"summary_large_image"`
	Title       string  `json:"title" example:"Twitter Title"`
	Description string  `json:"description" example:"Twitter Description"`
	Image       *string `json:"image,omitempty" example:"https://example.com/image.jpg"`
	Creator     *string `json:"creator,omitempty" example:"@username"`
	Site        *string `json:"site,omitempty" example:"@website"`
}

// ToServiceModel converts SEOMetaCreateRequest to models.CreateSEOMetaRequest
func (r *SEOMetaCreateRequest) ToServiceModel() models.CreateSEOMetaRequest {
	return models.CreateSEOMetaRequest{
		WebsiteID:              r.WebsiteID,
		PageType:               r.PageType,
		PageID:                 r.PageID,
		PageURL:                r.PageURL,
		PagePath:               r.PagePath,
		MetaTitle:              r.MetaTitle,
		MetaDescription:        r.MetaDescription,
		MetaKeywords:           r.MetaKeywords,
		MetaRobots:             r.MetaRobots,
		CanonicalURL:           r.CanonicalURL,
		OGTitle:                r.OGTitle,
		OGDescription:          r.OGDescription,
		OGImage:                r.OGImage,
		OGType:                 r.OGType,
		OGURL:                  r.OGURL,
		OGSiteName:             r.OGSiteName,
		OGLocale:               r.OGLocale,
		TwitterCard:            r.TwitterCard,
		TwitterTitle:           r.TwitterTitle,
		TwitterDescription:     r.TwitterDescription,
		TwitterImage:           r.TwitterImage,
		TwitterCreator:         r.TwitterCreator,
		TwitterSite:            r.TwitterSite,
		SchemaType:             r.SchemaType,
		SchemaData:             r.SchemaData,
		AdditionalMeta:         r.AdditionalMeta,
		FocusKeyword:           r.FocusKeyword,
		IsIndexed:              r.IsIndexed,
		IsSitemapIncluded:      r.IsSitemapIncluded,
		Priority:               r.Priority,
		ChangeFrequency:        r.ChangeFrequency,
	}
}

// ToServiceModel converts SEOMetaUpdateRequest to models.UpdateSEOMetaRequest
func (r *SEOMetaUpdateRequest) ToServiceModel() models.UpdateSEOMetaRequest {
	return models.UpdateSEOMetaRequest{
		MetaTitle:              r.MetaTitle,
		MetaDescription:        r.MetaDescription,
		MetaKeywords:           r.MetaKeywords,
		MetaRobots:             r.MetaRobots,
		CanonicalURL:           r.CanonicalURL,
		OGTitle:                r.OGTitle,
		OGDescription:          r.OGDescription,
		OGImage:                r.OGImage,
		OGType:                 r.OGType,
		OGURL:                  r.OGURL,
		OGSiteName:             r.OGSiteName,
		OGLocale:               r.OGLocale,
		TwitterCard:            r.TwitterCard,
		TwitterTitle:           r.TwitterTitle,
		TwitterDescription:     r.TwitterDescription,
		TwitterImage:           r.TwitterImage,
		TwitterCreator:         r.TwitterCreator,
		TwitterSite:            r.TwitterSite,
		SchemaType:             r.SchemaType,
		SchemaData:             r.SchemaData,
		AdditionalMeta:         r.AdditionalMeta,
		FocusKeyword:           r.FocusKeyword,
		IsIndexed:              r.IsIndexed,
		IsSitemapIncluded:      r.IsSitemapIncluded,
		Priority:               r.Priority,
		ChangeFrequency:        r.ChangeFrequency,
		Status:                 r.Status,
	}
}