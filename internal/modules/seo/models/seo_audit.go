package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// SEOAudit represents an SEO audit of a website
type SEOAudit struct {
	ID         uint      `json:"id" db:"id"`
	WebsiteID  uint      `json:"website_id" db:"website_id"`
	TenantID   uint      `json:"tenant_id" db:"tenant_id"`
	
	// Audit Information
	AuditType        AuditType `json:"audit_type" db:"audit_type"`
	AuditName        string    `json:"audit_name" db:"audit_name"`
	AuditDescription *string   `json:"audit_description" db:"audit_description"`
	AuditURL         *string   `json:"audit_url" db:"audit_url"`
	
	// Audit Execution
	AuditStartedAt   time.Time  `json:"audit_started_at" db:"audit_started_at"`
	AuditCompletedAt *time.Time `json:"audit_completed_at" db:"audit_completed_at"`
	AuditDuration    *uint      `json:"audit_duration" db:"audit_duration"`
	AuditTool        AuditTool  `json:"audit_tool" db:"audit_tool"`
	AuditVersion     *string    `json:"audit_version" db:"audit_version"`
	
	// Overall Scores
	OverallScore           float64 `json:"overall_score" db:"overall_score"`
	TechnicalScore         float64 `json:"technical_score" db:"technical_score"`
	ContentScore           float64 `json:"content_score" db:"content_score"`
	PerformanceScore       float64 `json:"performance_score" db:"performance_score"`
	AccessibilityScore     float64 `json:"accessibility_score" db:"accessibility_score"`
	UserExperienceScore    float64 `json:"user_experience_score" db:"user_experience_score"`
	
	// Detailed Metrics
	PagesCrawled   uint `json:"pages_crawled" db:"pages_crawled"`
	PagesIndexed   uint `json:"pages_indexed" db:"pages_indexed"`
	IssuesFound    uint `json:"issues_found" db:"issues_found"`
	CriticalIssues uint `json:"critical_issues" db:"critical_issues"`
	MajorIssues    uint `json:"major_issues" db:"major_issues"`
	MinorIssues    uint `json:"minor_issues" db:"minor_issues"`
	Warnings       uint `json:"warnings" db:"warnings"`
	
	// Performance Metrics
	LoadTime                *float64 `json:"load_time" db:"load_time"`
	FirstContentfulPaint    *float64 `json:"first_contentful_paint" db:"first_contentful_paint"`
	LargestContentfulPaint  *float64 `json:"largest_contentful_paint" db:"largest_contentful_paint"`
	CumulativeLayoutShift   *float64 `json:"cumulative_layout_shift" db:"cumulative_layout_shift"`
	FirstInputDelay         *float64 `json:"first_input_delay" db:"first_input_delay"`
	TotalBlockingTime       *float64 `json:"total_blocking_time" db:"total_blocking_time"`
	
	// Technical SEO Metrics
	MobileFriendly         *bool `json:"mobile_friendly" db:"mobile_friendly"`
	HTTPSEnabled           *bool `json:"https_enabled" db:"https_enabled"`
	XMLSitemapExists       *bool `json:"xml_sitemap_exists" db:"xml_sitemap_exists"`
	RobotsTxtExists        *bool `json:"robots_txt_exists" db:"robots_txt_exists"`
	StructuredDataValid    *bool `json:"structured_data_valid" db:"structured_data_valid"`
	CanonicalURLsProper    *bool `json:"canonical_urls_proper" db:"canonical_urls_proper"`
	MetaTitlesOptimized    *bool `json:"meta_titles_optimized" db:"meta_titles_optimized"`
	MetaDescriptionsOptimized *bool `json:"meta_descriptions_optimized" db:"meta_descriptions_optimized"`
	HeadingStructureProper *bool `json:"heading_structure_proper" db:"heading_structure_proper"`
	ImagesOptimized        *bool `json:"images_optimized" db:"images_optimized"`
	InternalLinksHealthy   *bool `json:"internal_links_healthy" db:"internal_links_healthy"`
	ExternalLinksHealthy   *bool `json:"external_links_healthy" db:"external_links_healthy"`
	
	// Content Quality Metrics
	ContentUniqueness       *float64 `json:"content_uniqueness" db:"content_uniqueness"`
	KeywordDensity          *float64 `json:"keyword_density" db:"keyword_density"`
	ReadabilityScore        *float64 `json:"readability_score" db:"readability_score"`
	WordCountAverage        *uint    `json:"word_count_average" db:"word_count_average"`
	DuplicateContentIssues  uint     `json:"duplicate_content_issues" db:"duplicate_content_issues"`
	ThinContentPages        uint     `json:"thin_content_pages" db:"thin_content_pages"`
	
	// Audit Results
	AuditResults     AuditResults     `json:"audit_results" db:"audit_results"`
	IssuesSummary    IssuesSummary    `json:"issues_summary" db:"issues_summary"`
	Recommendations  Recommendations  `json:"recommendations" db:"recommendations"`
	ComparisonData   ComparisonData   `json:"comparison_data" db:"comparison_data"`
	
	// Audit Configuration
	AuditConfig           AuditConfig `json:"audit_config" db:"audit_config"`
	CrawlDepth            uint        `json:"crawl_depth" db:"crawl_depth"`
	MaxPages              uint        `json:"max_pages" db:"max_pages"`
	IncludeExternalLinks  bool        `json:"include_external_links" db:"include_external_links"`
	CheckImages           bool        `json:"check_images" db:"check_images"`
	CheckPerformance      bool        `json:"check_performance" db:"check_performance"`
	
	// Status and Metadata
	Status       AuditStatus `json:"status" db:"status"`
	ErrorMessage *string `json:"error_message" db:"error_message"`
	CreatedBy    *uint   `json:"created_by" db:"created_by"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// AuditResults contains detailed audit results
type AuditResults map[string]interface{}

// Value implements the driver.Valuer interface
func (a AuditResults) Value() (driver.Value, error) {
	if a == nil {
		return "{}", nil
	}
	return json.Marshal(a)
}

// Scan implements the sql.Scanner interface
func (a *AuditResults) Scan(value interface{}) error {
	if value == nil {
		*a = make(AuditResults)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, a)
}

// IssuesSummary contains summary of issues found
type IssuesSummary []AuditIssue

// AuditIssue represents a single issue found during audit
type AuditIssue struct {
	Type        string                 `json:"type"`
	Severity    IssueSeverity          `json:"severity"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Pages       []string               `json:"pages"`
	Count       int                    `json:"count"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// Value implements the driver.Valuer interface
func (i IssuesSummary) Value() (driver.Value, error) {
	if i == nil {
		return "[]", nil
	}
	return json.Marshal(i)
}

// Scan implements the sql.Scanner interface
func (i *IssuesSummary) Scan(value interface{}) error {
	if value == nil {
		*i = make(IssuesSummary, 0)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, i)
}

// Recommendations contains SEO recommendations
type Recommendations []AuditRecommendation

// AuditRecommendation represents a single recommendation
type AuditRecommendation struct {
	Category    string                 `json:"category"`
	Priority    RecommendationPriority `json:"priority"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Action      string                 `json:"action"`
	Impact      RecommendationImpact   `json:"impact"`
	Effort      RecommendationEffort   `json:"effort"`
}

// Value implements the driver.Valuer interface
func (r Recommendations) Value() (driver.Value, error) {
	if r == nil {
		return "[]", nil
	}
	return json.Marshal(r)
}

// Scan implements the sql.Scanner interface
func (r *Recommendations) Scan(value interface{}) error {
	if value == nil {
		*r = make(Recommendations, 0)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, r)
}

// ComparisonData contains comparison with previous audits
type ComparisonData map[string]interface{}

// Value implements the driver.Valuer interface
func (c ComparisonData) Value() (driver.Value, error) {
	if c == nil {
		return "{}", nil
	}
	return json.Marshal(c)
}

// Scan implements the sql.Scanner interface
func (c *ComparisonData) Scan(value interface{}) error {
	if value == nil {
		*c = make(ComparisonData)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, c)
}

// AuditConfig contains configuration for audit
type AuditConfig map[string]interface{}

// Value implements the driver.Valuer interface
func (a AuditConfig) Value() (driver.Value, error) {
	if a == nil {
		return "{}", nil
	}
	return json.Marshal(a)
}

// Scan implements the sql.Scanner interface
func (a *AuditConfig) Scan(value interface{}) error {
	if value == nil {
		*a = make(AuditConfig)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, a)
}

// CreateSEOAuditRequest represents the request to create an SEO audit
type CreateSEOAuditRequest struct {
	WebsiteID        uint      `json:"website_id" validate:"required"`
	AuditType        AuditType `json:"audit_type" validate:"required,oneof=full page technical content performance accessibility"`
	AuditName        string    `json:"audit_name" validate:"required,min=1,max=255"`
	AuditDescription *string   `json:"audit_description"`
	AuditURL         *string   `json:"audit_url" validate:"omitempty,url"`
	
	// Audit Configuration
	CrawlDepth           *uint `json:"crawl_depth" validate:"omitempty,min=1,max=10"`
	MaxPages             *uint `json:"max_pages" validate:"omitempty,min=1,max=10000"`
	IncludeExternalLinks *bool `json:"include_external_links"`
	CheckImages          *bool `json:"check_images"`
	CheckPerformance     *bool `json:"check_performance"`
	
	// Additional Configuration
	AuditConfig *AuditConfig `json:"audit_config"`
}

// UpdateSEOAuditRequest represents the request to update an SEO audit
type UpdateSEOAuditRequest struct {
	AuditName        *string      `json:"audit_name" validate:"omitempty,min=1,max=255"`
	AuditDescription *string      `json:"audit_description"`
	Status           *AuditStatus `json:"status" validate:"omitempty,oneof=pending running completed failed cancelled deleted"`
	
	// Scores (updated during audit execution)
	OverallScore           *float64 `json:"overall_score" validate:"omitempty,min=0,max=100"`
	TechnicalScore         *float64 `json:"technical_score" validate:"omitempty,min=0,max=100"`
	ContentScore           *float64 `json:"content_score" validate:"omitempty,min=0,max=100"`
	PerformanceScore       *float64 `json:"performance_score" validate:"omitempty,min=0,max=100"`
	AccessibilityScore     *float64 `json:"accessibility_score" validate:"omitempty,min=0,max=100"`
	UserExperienceScore    *float64 `json:"user_experience_score" validate:"omitempty,min=0,max=100"`
	
	// Results (updated when audit completes)
	AuditResults    *AuditResults    `json:"audit_results"`
	IssuesSummary   *IssuesSummary   `json:"issues_summary"`
	Recommendations *Recommendations `json:"recommendations"`
	ComparisonData  *ComparisonData  `json:"comparison_data"`
	
	// Error handling
	ErrorMessage *string `json:"error_message"`
}

// SEOAuditResponse represents the response for SEO audit operations
type SEOAuditResponse struct {
	*SEOAudit
	IssuesByCategory map[string][]AuditIssue `json:"issues_by_category,omitempty"`
	TrendData        *AuditTrendData         `json:"trend_data,omitempty"`
}

// AuditTrendData contains trend analysis data
type AuditTrendData struct {
	ScoreTrend         []ScoreDataPoint `json:"score_trend"`
	IssueCountTrend    []IssueCountDataPoint `json:"issue_count_trend"`
	PerformanceTrend   []PerformanceDataPoint `json:"performance_trend"`
	ComparisonSummary  *ComparisonSummary `json:"comparison_summary"`
}

// ScoreDataPoint represents a score data point in trend analysis
type ScoreDataPoint struct {
	Date           time.Time `json:"date"`
	OverallScore   float64   `json:"overall_score"`
	TechnicalScore float64   `json:"technical_score"`
	ContentScore   float64   `json:"content_score"`
	PerformanceScore float64 `json:"performance_score"`
}

// IssueCountDataPoint represents issue count data point
type IssueCountDataPoint struct {
	Date           time.Time `json:"date"`
	CriticalIssues uint      `json:"critical_issues"`
	MajorIssues    uint      `json:"major_issues"`
	MinorIssues    uint      `json:"minor_issues"`
	Warnings       uint      `json:"warnings"`
}

// PerformanceDataPoint represents performance data point
type PerformanceDataPoint struct {
	Date                   time.Time `json:"date"`
	LoadTime               float64   `json:"load_time"`
	FirstContentfulPaint   float64   `json:"first_contentful_paint"`
	LargestContentfulPaint float64   `json:"largest_contentful_paint"`
	CumulativeLayoutShift  float64   `json:"cumulative_layout_shift"`
}

// ComparisonSummary contains summary of comparison with previous audit
type ComparisonSummary struct {
	ScoreChange         float64 `json:"score_change"`
	IssueCountChange    int     `json:"issue_count_change"`
	PerformanceChange   float64 `json:"performance_change"`
	NewIssuesFound      uint    `json:"new_issues_found"`
	ResolvedIssues      uint    `json:"resolved_issues"`
	Improvement         bool    `json:"improvement"`
	RegressionAreas     []string `json:"regression_areas"`
	ImprovementAreas    []string `json:"improvement_areas"`
}

// SEOAuditFilter represents filters for SEO audit queries
type SEOAuditFilter struct {
	WebsiteID        *uint        `json:"website_id"`
	AuditType        *AuditType   `json:"audit_type"`
	Status           *AuditStatus `json:"status"`
	MinOverallScore  *float64   `json:"min_overall_score"`
	MaxOverallScore  *float64   `json:"max_overall_score"`
	CreatedBy        *uint      `json:"created_by"`
	CreatedAfter     *time.Time `json:"created_after"`
	CreatedBefore    *time.Time `json:"created_before"`
	CompletedAfter   *time.Time `json:"completed_after"`
	CompletedBefore  *time.Time `json:"completed_before"`
}

// TableName returns the table name for the SEOAudit model
func (SEOAudit) TableName() string {
	return "seo_audits"
}

// Validate validates the SEOAudit model
func (s *SEOAudit) Validate() error {
	if s.WebsiteID == 0 {
		return errors.New("website_id is required")
	}
	
	if s.TenantID == 0 {
		return errors.New("tenant_id is required")
	}
	
	if s.AuditType == "" {
		return errors.New("audit_type is required")
	}
	
	if s.AuditName == "" {
		return errors.New("audit_name is required")
	}
	
	if s.CrawlDepth == 0 {
		return errors.New("crawl_depth must be greater than 0")
	}
	
	if s.MaxPages == 0 {
		return errors.New("max_pages must be greater than 0")
	}
	
	if s.OverallScore < 0 || s.OverallScore > 100 {
		return errors.New("overall_score must be between 0 and 100")
	}
	
	return nil
}

// IsCompleted returns true if the audit is completed
func (s *SEOAudit) IsCompleted() bool {
	return s.Status == AuditStatusCompleted
}

// IsRunning returns true if the audit is running
func (s *SEOAudit) IsRunning() bool {
	return s.Status == AuditStatusRunning
}

// IsFailed returns true if the audit failed
func (s *SEOAudit) IsFailed() bool {
	return s.Status == AuditStatusFailed
}

// GetDuration returns the audit duration in seconds
func (s *SEOAudit) GetDuration() uint {
	if s.AuditDuration != nil {
		return *s.AuditDuration
	}
	
	if s.AuditCompletedAt != nil {
		return uint(s.AuditCompletedAt.Sub(s.AuditStartedAt).Seconds())
	}
	
	return 0
}

// GetIssuesByCategory returns issues grouped by category
func (s *SEOAudit) GetIssuesByCategory() map[string][]AuditIssue {
	categories := make(map[string][]AuditIssue)
	
	for _, issue := range s.IssuesSummary {
		if _, exists := categories[issue.Type]; !exists {
			categories[issue.Type] = make([]AuditIssue, 0)
		}
		categories[issue.Type] = append(categories[issue.Type], issue)
	}
	
	return categories
}

// GetRecommendationsByPriority returns recommendations grouped by priority
func (s *SEOAudit) GetRecommendationsByPriority() map[string][]AuditRecommendation {
	priorities := make(map[string][]AuditRecommendation)
	
	for _, recommendation := range s.Recommendations {
		priorityStr := string(recommendation.Priority)
		if _, exists := priorities[priorityStr]; !exists {
			priorities[priorityStr] = make([]AuditRecommendation, 0)
		}
		priorities[priorityStr] = append(priorities[priorityStr], recommendation)
	}
	
	return priorities
}

// AuditType represents the type of SEO audit
// @Enum full,page,technical,content,performance,accessibility
type AuditType string

const (
	AuditTypeFull          AuditType = "full"
	AuditTypePage          AuditType = "page"
	AuditTypeTechnical     AuditType = "technical"
	AuditTypeContent       AuditType = "content"
	AuditTypePerformance   AuditType = "performance"
	AuditTypeAccessibility AuditType = "accessibility"
)

// AuditStatus represents the status of SEO audit
// @Enum pending,running,completed,failed,cancelled,deleted
type AuditStatus string

const (
	AuditStatusPending   AuditStatus = "pending"
	AuditStatusRunning   AuditStatus = "running"
	AuditStatusCompleted AuditStatus = "completed"
	AuditStatusFailed    AuditStatus = "failed"
	AuditStatusCancelled AuditStatus = "cancelled"
	AuditStatusDeleted   AuditStatus = "deleted"
)

// AuditTool represents the tool used for SEO audit
// @Enum internal,lighthouse,pagespeed,screamingfrog
type AuditTool string

const (
	AuditToolInternal      AuditTool = "internal"
	AuditToolLighthouse    AuditTool = "lighthouse"
	AuditToolPageSpeed     AuditTool = "pagespeed"
	AuditToolScreamingFrog AuditTool = "screamingfrog"
)

// IssueSeverity represents the severity of audit issues
// @Enum critical,major,minor,warning
type IssueSeverity string

const (
	IssueSeverityCritical IssueSeverity = "critical"
	IssueSeverityMajor    IssueSeverity = "major"
	IssueSeverityMinor    IssueSeverity = "minor"
	IssueSeverityWarning  IssueSeverity = "warning"
)

// RecommendationPriority represents the priority of recommendations
// @Enum high,medium,low
type RecommendationPriority string

const (
	RecommendationPriorityHigh   RecommendationPriority = "high"
	RecommendationPriorityMedium RecommendationPriority = "medium"
	RecommendationPriorityLow    RecommendationPriority = "low"
)

// RecommendationEffort represents the effort required for recommendations
// @Enum low,medium,high
type RecommendationEffort string

const (
	RecommendationEffortLow    RecommendationEffort = "low"
	RecommendationEffortMedium RecommendationEffort = "medium"
	RecommendationEffortHigh   RecommendationEffort = "high"
)

// RecommendationImpact represents the impact of recommendations
// @Enum high,medium,low
type RecommendationImpact string

const (
	RecommendationImpactHigh   RecommendationImpact = "high"
	RecommendationImpactMedium RecommendationImpact = "medium"
	RecommendationImpactLow    RecommendationImpact = "low"
)

// SEOAuditQueryFilter represents filters for SEO audit queries  
type SEOAuditQueryFilter struct {
	WebsiteID        *uint        `json:"website_id"`
	AuditType        *AuditType   `json:"audit_type"`
	AuditStatus      *AuditStatus `json:"audit_status"`
	MinScore         *float64   `json:"min_score"`
	MaxScore         *float64   `json:"max_score"`
	StartedAfter     *time.Time `json:"started_after"`
	StartedBefore    *time.Time `json:"started_before"`
	CompletedAfter   *time.Time `json:"completed_after"`
	CompletedBefore  *time.Time `json:"completed_before"`
}

// AuditTrendPoint represents a point in audit trends
type AuditTrendPoint struct {
	Date               time.Time `json:"date"`
	TotalScore         float64   `json:"total_score"`
	PerformanceScore   float64   `json:"performance_score"`
	AccessibilityScore float64   `json:"accessibility_score"`
	BestPracticesScore float64   `json:"best_practices_score"`
	SEOScore           float64   `json:"seo_score"`
	AuditCount         int       `json:"audit_count"`
}