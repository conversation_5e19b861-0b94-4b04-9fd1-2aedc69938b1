package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// SEOMeta represents the SEO metadata for a website page
type SEOMeta struct {
	ID         uint      `json:"id" db:"id"`
	WebsiteID  uint      `json:"website_id" db:"website_id"`
	TenantID   uint      `json:"tenant_id" db:"tenant_id"`
	
	// Page Information
	PageType string `json:"page_type" db:"page_type"`
	PageID   *uint  `json:"page_id" db:"page_id"`
	PageURL  string `json:"page_url" db:"page_url"`
	PagePath string `json:"page_path" db:"page_path"`
	
	// SEO Meta Tags
	MetaTitle       *string `json:"meta_title" db:"meta_title"`
	MetaDescription *string `json:"meta_description" db:"meta_description"`
	MetaKeywords    *string `json:"meta_keywords" db:"meta_keywords"`
	MetaRobots      string  `json:"meta_robots" db:"meta_robots"`
	CanonicalURL    *string `json:"canonical_url" db:"canonical_url"`
	
	// Open Graph Meta Tags
	OGTitle       *string `json:"og_title" db:"og_title"`
	OGDescription *string `json:"og_description" db:"og_description"`
	OGImage       *string `json:"og_image" db:"og_image"`
	OGType        string  `json:"og_type" db:"og_type"`
	OGURL         *string `json:"og_url" db:"og_url"`
	OGSiteName    *string `json:"og_site_name" db:"og_site_name"`
	OGLocale      string  `json:"og_locale" db:"og_locale"`
	
	// Twitter Card Meta Tags
	TwitterCard        string  `json:"twitter_card" db:"twitter_card"`
	TwitterTitle       *string `json:"twitter_title" db:"twitter_title"`
	TwitterDescription *string `json:"twitter_description" db:"twitter_description"`
	TwitterImage       *string `json:"twitter_image" db:"twitter_image"`
	TwitterCreator     *string `json:"twitter_creator" db:"twitter_creator"`
	TwitterSite        *string `json:"twitter_site" db:"twitter_site"`
	
	// Schema.org Structured Data
	SchemaType *string     `json:"schema_type" db:"schema_type"`
	SchemaData SchemaData  `json:"schema_data" db:"schema_data"`
	
	// Additional Meta Tags
	AdditionalMeta AdditionalMetaTags `json:"additional_meta" db:"additional_meta"`
	
	// SEO Settings
	FocusKeyword     *string `json:"focus_keyword" db:"focus_keyword"`
	SEOScore         float64 `json:"seo_score" db:"seo_score"`
	ReadabilityScore float64 `json:"readability_score" db:"readability_score"`
	
	// Status and Indexing
	IsIndexed          bool    `json:"is_indexed" db:"is_indexed"`
	IsSitemapIncluded  bool    `json:"is_sitemap_included" db:"is_sitemap_included"`
	Priority           float64 `json:"priority" db:"priority"`
	ChangeFrequency    string  `json:"change_frequency" db:"change_frequency"`
	
	// Status and Timestamps
	Status    string    `json:"status" db:"status"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// SchemaData represents structured data for Schema.org
type SchemaData map[string]interface{}

// Value implements the driver.Valuer interface for database storage
func (s SchemaData) Value() (driver.Value, error) {
	if s == nil {
		return "{}", nil
	}
	return json.Marshal(s)
}

// Scan implements the sql.Scanner interface for database retrieval
func (s *SchemaData) Scan(value interface{}) error {
	if value == nil {
		*s = make(SchemaData)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, s)
}

// AdditionalMetaTags represents additional custom meta tags
type AdditionalMetaTags []MetaTag

// MetaTag represents a single meta tag
type MetaTag struct {
	Name     string `json:"name"`
	Content  string `json:"content"`
	Property string `json:"property,omitempty"`
}

// Value implements the driver.Valuer interface for database storage
func (a AdditionalMetaTags) Value() (driver.Value, error) {
	if a == nil {
		return "[]", nil
	}
	return json.Marshal(a)
}

// Scan implements the sql.Scanner interface for database retrieval
func (a *AdditionalMetaTags) Scan(value interface{}) error {
	if value == nil {
		*a = make(AdditionalMetaTags, 0)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, a)
}

// CreateSEOMetaRequest represents the request to create SEO metadata
type CreateSEOMetaRequest struct {
	WebsiteID  uint   `json:"website_id" validate:"required"`
	PageType   string `json:"page_type" validate:"required,oneof=page post category tag product custom"`
	PageID     *uint  `json:"page_id"`
	PageURL    string `json:"page_url" validate:"required,url"`
	PagePath   string `json:"page_path" validate:"required"`
	
	// SEO Meta Tags
	MetaTitle       *string `json:"meta_title" validate:"omitempty,max=255"`
	MetaDescription *string `json:"meta_description" validate:"omitempty,max=500"`
	MetaKeywords    *string `json:"meta_keywords"`
	MetaRobots      string  `json:"meta_robots" validate:"omitempty,max=100"`
	CanonicalURL    *string `json:"canonical_url" validate:"omitempty,url"`
	
	// Open Graph Meta Tags
	OGTitle       *string `json:"og_title" validate:"omitempty,max=255"`
	OGDescription *string `json:"og_description" validate:"omitempty,max=500"`
	OGImage       *string `json:"og_image" validate:"omitempty,url"`
	OGType        string  `json:"og_type" validate:"omitempty,max=50"`
	OGURL         *string `json:"og_url" validate:"omitempty,url"`
	OGSiteName    *string `json:"og_site_name" validate:"omitempty,max=255"`
	OGLocale      string  `json:"og_locale" validate:"omitempty,max=10"`
	
	// Twitter Card Meta Tags
	TwitterCard        string  `json:"twitter_card" validate:"omitempty,max=50"`
	TwitterTitle       *string `json:"twitter_title" validate:"omitempty,max=255"`
	TwitterDescription *string `json:"twitter_description" validate:"omitempty,max=500"`
	TwitterImage       *string `json:"twitter_image" validate:"omitempty,url"`
	TwitterCreator     *string `json:"twitter_creator" validate:"omitempty,max=255"`
	TwitterSite        *string `json:"twitter_site" validate:"omitempty,max=255"`
	
	// Schema.org Structured Data
	SchemaType *string     `json:"schema_type" validate:"omitempty,max=100"`
	SchemaData *SchemaData `json:"schema_data"`
	
	// Additional Meta Tags
	AdditionalMeta *AdditionalMetaTags `json:"additional_meta"`
	
	// SEO Settings
	FocusKeyword *string `json:"focus_keyword" validate:"omitempty,max=255"`
	
	// Status and Indexing
	IsIndexed          *bool    `json:"is_indexed"`
	IsSitemapIncluded  *bool    `json:"is_sitemap_included"`
	Priority           *float64 `json:"priority" validate:"omitempty,min=0,max=1"`
	ChangeFrequency    *string  `json:"change_frequency" validate:"omitempty,oneof=always hourly daily weekly monthly yearly never"`
}

// UpdateSEOMetaRequest represents the request to update SEO metadata
type UpdateSEOMetaRequest struct {
	MetaTitle       *string `json:"meta_title" validate:"omitempty,max=255"`
	MetaDescription *string `json:"meta_description" validate:"omitempty,max=500"`
	MetaKeywords    *string `json:"meta_keywords"`
	MetaRobots      *string `json:"meta_robots" validate:"omitempty,max=100"`
	CanonicalURL    *string `json:"canonical_url" validate:"omitempty,url"`
	
	// Open Graph Meta Tags
	OGTitle       *string `json:"og_title" validate:"omitempty,max=255"`
	OGDescription *string `json:"og_description" validate:"omitempty,max=500"`
	OGImage       *string `json:"og_image" validate:"omitempty,url"`
	OGType        *string `json:"og_type" validate:"omitempty,max=50"`
	OGURL         *string `json:"og_url" validate:"omitempty,url"`
	OGSiteName    *string `json:"og_site_name" validate:"omitempty,max=255"`
	OGLocale      *string `json:"og_locale" validate:"omitempty,max=10"`
	
	// Twitter Card Meta Tags
	TwitterCard        *string `json:"twitter_card" validate:"omitempty,max=50"`
	TwitterTitle       *string `json:"twitter_title" validate:"omitempty,max=255"`
	TwitterDescription *string `json:"twitter_description" validate:"omitempty,max=500"`
	TwitterImage       *string `json:"twitter_image" validate:"omitempty,url"`
	TwitterCreator     *string `json:"twitter_creator" validate:"omitempty,max=255"`
	TwitterSite        *string `json:"twitter_site" validate:"omitempty,max=255"`
	
	// Schema.org Structured Data
	SchemaType *string     `json:"schema_type" validate:"omitempty,max=100"`
	SchemaData *SchemaData `json:"schema_data"`
	
	// Additional Meta Tags
	AdditionalMeta *AdditionalMetaTags `json:"additional_meta"`
	
	// SEO Settings
	FocusKeyword *string `json:"focus_keyword" validate:"omitempty,max=255"`
	
	// Status and Indexing
	IsIndexed          *bool    `json:"is_indexed"`
	IsSitemapIncluded  *bool    `json:"is_sitemap_included"`
	Priority           *float64 `json:"priority" validate:"omitempty,min=0,max=1"`
	ChangeFrequency    *string  `json:"change_frequency" validate:"omitempty,oneof=always hourly daily weekly monthly yearly never"`
	
	// Status
	Status *string `json:"status" validate:"omitempty,oneof=active inactive deleted"`
}

// SEOMetaResponse represents the response for SEO metadata operations
type SEOMetaResponse struct {
	*SEOMeta
	GeneratedTags []string `json:"generated_tags"`
	SEOAnalysis   *SEOAnalysis `json:"seo_analysis,omitempty"`
}

// SEOAnalysis represents the SEO analysis results
type SEOAnalysis struct {
	OverallScore        float64               `json:"overall_score"`
	TitleAnalysis       *TextAnalysis         `json:"title_analysis,omitempty"`
	DescriptionAnalysis *TextAnalysis         `json:"description_analysis,omitempty"`
	KeywordAnalysis     *KeywordAnalysis      `json:"keyword_analysis,omitempty"`
	ReadabilityAnalysis *ReadabilityAnalysis  `json:"readability_analysis,omitempty"`
	TechnicalAnalysis   *TechnicalAnalysis    `json:"technical_analysis,omitempty"`
	Recommendations     []string              `json:"recommendations,omitempty"`
}

// TextAnalysis represents analysis results for text content
type TextAnalysis struct {
	Length      int      `json:"length"`
	WordCount   int      `json:"word_count"`
	Score       int      `json:"score"`
	Issues      []string `json:"issues,omitempty"`
	Suggestions []string `json:"suggestions,omitempty"`
}

// KeywordAnalysis represents keyword analysis results
type KeywordAnalysis struct {
	Density     float64 `json:"density"`
	Count       int     `json:"count"`
	Variations  []string `json:"variations,omitempty"`
	Suggestions []string `json:"suggestions,omitempty"`
}

// ReadabilityAnalysis represents readability analysis results
type ReadabilityAnalysis struct {
	Score       float64 `json:"score"`
	Grade       string  `json:"grade"`
	Issues      []string `json:"issues,omitempty"`
	Suggestions []string `json:"suggestions,omitempty"`
}

// TechnicalAnalysis represents technical SEO analysis results
type TechnicalAnalysis struct {
	HasTitle          bool     `json:"has_title"`
	HasDescription    bool     `json:"has_description"`
	HasCanonical      bool     `json:"has_canonical"`
	HasOpenGraph      bool     `json:"has_open_graph"`
	HasTwitterCard    bool     `json:"has_twitter_card"`
	HasStructuredData bool     `json:"has_structured_data"`
	Issues            []string `json:"issues,omitempty"`
	Suggestions       []string `json:"suggestions,omitempty"`
}

// SEOMetaFilter represents filters for SEO metadata queries
type SEOMetaFilter struct {
	WebsiteID         *uint     `json:"website_id"`
	PageType          *string   `json:"page_type"`
	Status            *string   `json:"status"`
	IsIndexed         *bool     `json:"is_indexed"`
	IsSitemapIncluded *bool     `json:"is_sitemap_included"`
	FocusKeyword      *string   `json:"focus_keyword"`
	MinSEOScore       *float64  `json:"min_seo_score"`
	MaxSEOScore       *float64  `json:"max_seo_score"`
	CreatedAfter      *time.Time `json:"created_after"`
	CreatedBefore     *time.Time `json:"created_before"`
	UpdatedAfter      *time.Time `json:"updated_after"`
	UpdatedBefore     *time.Time `json:"updated_before"`
}

// TableName returns the table name for the SEOMeta model
func (SEOMeta) TableName() string {
	return "seo_meta"
}

// Validate validates the SEOMeta model
func (s *SEOMeta) Validate() error {
	if s.WebsiteID == 0 {
		return errors.New("website_id is required")
	}
	
	if s.TenantID == 0 {
		return errors.New("tenant_id is required")
	}
	
	if s.PageType == "" {
		return errors.New("page_type is required")
	}
	
	if s.PageURL == "" {
		return errors.New("page_url is required")
	}
	
	if s.PagePath == "" {
		return errors.New("page_path is required")
	}
	
	if s.Priority < 0 || s.Priority > 1 {
		return errors.New("priority must be between 0 and 1")
	}
	
	if s.SEOScore < 0 || s.SEOScore > 100 {
		return errors.New("seo_score must be between 0 and 100")
	}
	
	if s.ReadabilityScore < 0 || s.ReadabilityScore > 100 {
		return errors.New("readability_score must be between 0 and 100")
	}
	
	return nil
}

// GenerateMetaTags generates HTML meta tags based on the SEO metadata
func (s *SEOMeta) GenerateMetaTags() []string {
	var tags []string
	
	// Basic meta tags
	if s.MetaTitle != nil && *s.MetaTitle != "" {
		tags = append(tags, "<title>"+*s.MetaTitle+"</title>")
	}
	
	if s.MetaDescription != nil && *s.MetaDescription != "" {
		tags = append(tags, `<meta name="description" content="`+*s.MetaDescription+`">`)
	}
	
	if s.MetaKeywords != nil && *s.MetaKeywords != "" {
		tags = append(tags, `<meta name="keywords" content="`+*s.MetaKeywords+`">`)
	}
	
	if s.MetaRobots != "" {
		tags = append(tags, `<meta name="robots" content="`+s.MetaRobots+`">`)
	}
	
	if s.CanonicalURL != nil && *s.CanonicalURL != "" {
		tags = append(tags, `<link rel="canonical" href="`+*s.CanonicalURL+`">`)
	}
	
	// Open Graph tags
	if s.OGTitle != nil && *s.OGTitle != "" {
		tags = append(tags, `<meta property="og:title" content="`+*s.OGTitle+`">`)
	}
	
	if s.OGDescription != nil && *s.OGDescription != "" {
		tags = append(tags, `<meta property="og:description" content="`+*s.OGDescription+`">`)
	}
	
	if s.OGImage != nil && *s.OGImage != "" {
		tags = append(tags, `<meta property="og:image" content="`+*s.OGImage+`">`)
	}
	
	if s.OGType != "" {
		tags = append(tags, `<meta property="og:type" content="`+s.OGType+`">`)
	}
	
	if s.OGURL != nil && *s.OGURL != "" {
		tags = append(tags, `<meta property="og:url" content="`+*s.OGURL+`">`)
	}
	
	if s.OGSiteName != nil && *s.OGSiteName != "" {
		tags = append(tags, `<meta property="og:site_name" content="`+*s.OGSiteName+`">`)
	}
	
	if s.OGLocale != "" {
		tags = append(tags, `<meta property="og:locale" content="`+s.OGLocale+`">`)
	}
	
	// Twitter Card tags
	if s.TwitterCard != "" {
		tags = append(tags, `<meta name="twitter:card" content="`+s.TwitterCard+`">`)
	}
	
	if s.TwitterTitle != nil && *s.TwitterTitle != "" {
		tags = append(tags, `<meta name="twitter:title" content="`+*s.TwitterTitle+`">`)
	}
	
	if s.TwitterDescription != nil && *s.TwitterDescription != "" {
		tags = append(tags, `<meta name="twitter:description" content="`+*s.TwitterDescription+`">`)
	}
	
	if s.TwitterImage != nil && *s.TwitterImage != "" {
		tags = append(tags, `<meta name="twitter:image" content="`+*s.TwitterImage+`">`)
	}
	
	if s.TwitterCreator != nil && *s.TwitterCreator != "" {
		tags = append(tags, `<meta name="twitter:creator" content="`+*s.TwitterCreator+`">`)
	}
	
	if s.TwitterSite != nil && *s.TwitterSite != "" {
		tags = append(tags, `<meta name="twitter:site" content="`+*s.TwitterSite+`">`)
	}
	
	// Additional meta tags
	for _, tag := range s.AdditionalMeta {
		if tag.Name != "" && tag.Content != "" {
			tags = append(tags, `<meta name="`+tag.Name+`" content="`+tag.Content+`">`)
		}
		if tag.Property != "" && tag.Content != "" {
			tags = append(tags, `<meta property="`+tag.Property+`" content="`+tag.Content+`">`)
		}
	}
	
	return tags
}

// GenerateStructuredData generates JSON-LD structured data
func (s *SEOMeta) GenerateStructuredData() (string, error) {
	if s.SchemaType == nil || *s.SchemaType == "" || len(s.SchemaData) == 0 {
		return "", nil
	}
	
	structuredData := map[string]interface{}{
		"@context": "https://schema.org",
		"@type":    *s.SchemaType,
	}
	
	// Merge schema data
	for key, value := range s.SchemaData {
		structuredData[key] = value
	}
	
	jsonData, err := json.Marshal(structuredData)
	if err != nil {
		return "", err
	}
	
	return "<script type=\"application/ld+json\">" + string(jsonData) + "</script>", nil
}

// Constants for SEO metadata
const (
	PageTypePage     = "page"
	PageTypePost     = "post"
	PageTypeCategory = "category"
	PageTypeTag      = "tag"
	PageTypeProduct  = "product"
	PageTypeCustom   = "custom"
	
	StatusActive   = "active"
	StatusInactive = "inactive"
	StatusDeleted  = "deleted"
	
	ChangeFrequencyAlways  = "always"
	ChangeFrequencyHourly  = "hourly"
	ChangeFrequencyDaily   = "daily"
	ChangeFrequencyWeekly  = "weekly"
	ChangeFrequencyMonthly = "monthly"
	ChangeFrequencyYearly  = "yearly"
	ChangeFrequencyNever   = "never"
	
	OGTypeWebsite = "website"
	OGTypeArticle = "article"
	OGTypeProduct = "product"
	
	TwitterCardSummary            = "summary"
	TwitterCardSummaryLargeImage  = "summary_large_image"
	TwitterCardApp                = "app"
	TwitterCardPlayer             = "player"
)

// Validation models

// SEOValidationResult represents validation results for SEO meta
type SEOValidationResult struct {
	IsValid     bool                    `json:"is_valid"`
	Score       float64                 `json:"score"`
	Errors      []ValidationError       `json:"errors"`
	Warnings    []ValidationWarning     `json:"warnings"`
	Suggestions []ValidationSuggestion  `json:"suggestions"`
}

// ValidationResult represents a generic validation result
type ValidationResult struct {
	IsValid     bool                    `json:"is_valid"`
	Errors      []ValidationError       `json:"errors"`
	Warnings    []ValidationWarning     `json:"warnings"`
	Suggestions []ValidationSuggestion  `json:"suggestions"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field    string `json:"field"`
	Code     string `json:"code"`
	Message  string `json:"message"`
	Severity string `json:"severity"`
	Context  string `json:"context,omitempty"`
}

// ValidationWarning represents a validation warning
type ValidationWarning struct {
	Field   string `json:"field"`
	Code    string `json:"code"`
	Message string `json:"message"`
	Context string `json:"context,omitempty"`
}

// DuplicateMetaResult represents duplicate meta tag results
type DuplicateMetaResult struct {
	MetaValue string `json:"meta_value"`
	Count     int    `json:"count"`
	URLs      []string `json:"urls"`
}

// ValidationSuggestion represents a validation suggestion
type ValidationSuggestion struct {
	Field       string `json:"field"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Impact      string `json:"impact"`
	Context     string `json:"context,omitempty"`
}

// BulkSEOAnalysis represents bulk SEO analysis results
type BulkSEOAnalysis struct {
	WebsiteID          uint              `json:"website_id"`
	TotalPages         int               `json:"total_pages"`
	AverageScore       float64           `json:"average_score"`
	TotalIssues        int               `json:"total_issues"`
	TotalOpportunities int               `json:"total_opportunities"`
	Issues             []SEOIssue        `json:"issues"`
	Opportunities      []SEOOpportunity  `json:"opportunities"`
	AnalyzedAt         time.Time         `json:"analyzed_at"`
}

// SEOIssue represents an SEO issue found during analysis
type SEOIssue struct {
	Type        string `json:"type"`
	Severity    string `json:"severity"`
	Title       string `json:"title"`
	Description string `json:"description"`
	PageURL     string `json:"page_url,omitempty"`
	PageID      uint   `json:"page_id,omitempty"`
	Count       int    `json:"count,omitempty"`
}

// SEOOpportunity represents an SEO optimization opportunity
type SEOOpportunity struct {
	Type        string  `json:"type"`
	Title       string  `json:"title"`
	Description string  `json:"description"`
	Impact      string  `json:"impact"`
	Effort      string  `json:"effort"`
	Score       float64 `json:"score"`
	PageURL     string  `json:"page_url,omitempty"`
	PageID      uint    `json:"page_id,omitempty"`
}

// SEOInsights represents SEO insights for a website
type SEOInsights struct {
	WebsiteID             uint      `json:"website_id"`
	TotalPages            int       `json:"total_pages"`
	OptimizedPages        int       `json:"optimized_pages"`
	MissingTitles         int       `json:"missing_titles"`
	MissingDescriptions   int       `json:"missing_descriptions"`
	DuplicateTitles       int       `json:"duplicate_titles"`
	DuplicateDescriptions int       `json:"duplicate_descriptions"`
	OptimizationScore     float64   `json:"optimization_score"`
	GeneratedAt           time.Time `json:"generated_at"`
}

// SEOSuggestions represents optimization suggestions
type SEOSuggestions struct {
	Title             *string                `json:"title"`
	Description       *string                `json:"description"`
	Keywords          []string               `json:"keywords"`
	StructuredData    *SchemaData            `json:"structured_data"`
	AdditionalMeta    *AdditionalMetaTags    `json:"additional_meta"`
	ApplyAll          bool                   `json:"apply_all"`
}