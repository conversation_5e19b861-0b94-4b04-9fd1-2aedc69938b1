package models

import (
	"time"
	"gorm.io/gorm"
)

// RedirectType represents the HTTP redirect status codes
type RedirectType string

const (
	RedirectType301 RedirectType = "301"
	RedirectType302 RedirectType = "302"
	RedirectType303 RedirectType = "303"
	RedirectType307 RedirectType = "307"
	RedirectType308 RedirectType = "308"
)

// RedirectMatch represents how to match the source URL
type RedirectMatch string

const (
	RedirectMatchExact    RedirectMatch = "exact"
	RedirectMatchRegex    RedirectMatch = "regex"
	RedirectMatchWildcard RedirectMatch = "wildcard"
)

// QueryStringHandling represents how to handle query strings
type QueryStringHandling string

const (
	QueryStringIgnore   QueryStringHandling = "ignore"
	QueryStringPreserve QueryStringHandling = "preserve"
	QueryStringAppend   QueryStringHandling = "append"
)

// RedirectStatus represents the status of a redirect
type RedirectStatus string

const (
	RedirectStatusActive   RedirectStatus = "active"
	RedirectStatusInactive RedirectStatus = "inactive"
	RedirectStatusExpired  RedirectStatus = "expired"
	RedirectStatusDeleted  RedirectStatus = "deleted"
)

// SEORedirect represents a URL redirect configuration
type SEORedirect struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	WebsiteID uint      `json:"website_id" gorm:"not null;index"`
	TenantID  uint      `json:"tenant_id" gorm:"not null;index"`
	
	// Redirect Information
	SourceURL       string  `json:"source_url" gorm:"type:varchar(500);not null;index"`
	SourcePath      string  `json:"source_path" gorm:"type:varchar(500);not null;index"`
	DestinationURL  string  `json:"destination_url" gorm:"type:varchar(500);not null;index"`
	DestinationPath *string `json:"destination_path" gorm:"type:varchar(500);index"`
	
	// Redirect Configuration
	RedirectType      RedirectType        `json:"redirect_type" gorm:"type:enum('301','302','303','307','308');not null;default:'301';index"`
	RedirectMatch     RedirectMatch       `json:"redirect_match" gorm:"type:enum('exact','regex','wildcard');not null;default:'exact';index"`
	RegexPattern      *string             `json:"regex_pattern" gorm:"type:varchar(1000)"`
	ReplacementPattern *string            `json:"replacement_pattern" gorm:"type:varchar(1000)"`
	
	// Conditional Redirects
	Conditions           map[string]interface{} `json:"conditions" gorm:"type:json;default:'{}'"`
	QueryStringHandling  QueryStringHandling    `json:"query_string_handling" gorm:"type:enum('ignore','preserve','append');not null;default:'preserve'"`
	
	// Tracking and Analytics
	HitCount           uint      `json:"hit_count" gorm:"not null;default:0;index"`
	LastHitAt          *time.Time `json:"last_hit_at" gorm:"index"`
	LastHitIP          *string   `json:"last_hit_ip" gorm:"type:varchar(45)"`
	LastHitUserAgent   *string   `json:"last_hit_user_agent" gorm:"type:text"`
	LastHitReferrer    *string   `json:"last_hit_referrer" gorm:"type:varchar(500)"`
	
	// SEO Information
	SEOReason *string `json:"seo_reason" gorm:"type:varchar(100)"`
	Notes     *string `json:"notes" gorm:"type:text"`
	CreatedBy *uint   `json:"created_by" gorm:"index"`
	UpdatedBy *uint   `json:"updated_by" gorm:"index"`
	
	// Scheduling
	ActiveFrom  *time.Time `json:"active_from" gorm:"index"`
	ActiveUntil *time.Time `json:"active_until" gorm:"index"`
	
	// Status and Timestamps
	Status    RedirectStatus `json:"status" gorm:"type:varchar(20);not null;default:'active';index"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime;index"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime;index"`
	
	// Relationships - removed to avoid circular dependencies
}

// SEORedirectCreateRequest represents the request to create a new SEO redirect
type SEORedirectCreateRequest struct {
	WebsiteID uint   `json:"website_id" binding:"required"`
	TenantID  uint   `json:"tenant_id" binding:"required"`
	
	// Redirect Information
	SourceURL       string  `json:"source_url" binding:"required,max=500"`
	SourcePath      string  `json:"source_path" binding:"required,max=500"`
	DestinationURL  string  `json:"destination_url" binding:"required,max=500"`
	DestinationPath *string `json:"destination_path" binding:"omitempty,max=500"`
	
	// Redirect Configuration
	RedirectType      RedirectType        `json:"redirect_type" binding:"omitempty,oneof=301 302 303 307 308"`
	RedirectMatch     RedirectMatch       `json:"redirect_match" binding:"omitempty,oneof=exact regex wildcard"`
	RegexPattern      *string             `json:"regex_pattern" binding:"omitempty,max=1000"`
	ReplacementPattern *string            `json:"replacement_pattern" binding:"omitempty,max=1000"`
	
	// Conditional Redirects
	Conditions           map[string]interface{} `json:"conditions"`
	QueryStringHandling  QueryStringHandling    `json:"query_string_handling" binding:"omitempty,oneof=ignore preserve append"`
	
	// SEO Information
	SEOReason *string `json:"seo_reason" binding:"omitempty,max=100"`
	Notes     *string `json:"notes" binding:"omitempty,max=1000"`
	CreatedBy *uint   `json:"created_by"`
	
	// Scheduling
	ActiveFrom  *time.Time `json:"active_from"`
	ActiveUntil *time.Time `json:"active_until"`
	
	// Status
	Status RedirectStatus `json:"status" binding:"omitempty,oneof=active inactive expired deleted"`
}

// SEORedirectUpdateRequest represents the request to update an SEO redirect
type SEORedirectUpdateRequest struct {
	// Redirect Information
	SourceURL       *string `json:"source_url" binding:"omitempty,max=500"`
	SourcePath      *string `json:"source_path" binding:"omitempty,max=500"`
	DestinationURL  *string `json:"destination_url" binding:"omitempty,max=500"`
	DestinationPath *string `json:"destination_path" binding:"omitempty,max=500"`
	
	// Redirect Configuration
	RedirectType      *RedirectType        `json:"redirect_type" binding:"omitempty,oneof=301 302 303 307 308"`
	RedirectMatch     *RedirectMatch       `json:"redirect_match" binding:"omitempty,oneof=exact regex wildcard"`
	RegexPattern      *string              `json:"regex_pattern" binding:"omitempty,max=1000"`
	ReplacementPattern *string             `json:"replacement_pattern" binding:"omitempty,max=1000"`
	
	// Conditional Redirects
	Conditions           map[string]interface{} `json:"conditions"`
	QueryStringHandling  *QueryStringHandling   `json:"query_string_handling" binding:"omitempty,oneof=ignore preserve append"`
	
	// SEO Information
	SEOReason *string `json:"seo_reason" binding:"omitempty,max=100"`
	Notes     *string `json:"notes" binding:"omitempty,max=1000"`
	UpdatedBy *uint   `json:"updated_by"`
	
	// Scheduling
	ActiveFrom  *time.Time `json:"active_from"`
	ActiveUntil *time.Time `json:"active_until"`
	
	// Status
	Status *RedirectStatus `json:"status" binding:"omitempty,oneof=active inactive expired deleted"`
}

// SEORedirectResponse represents the response for SEO redirect operations
type SEORedirectResponse struct {
	ID        uint      `json:"id"`
	WebsiteID uint      `json:"website_id"`
	TenantID  uint      `json:"tenant_id"`
	
	// Redirect Information
	SourceURL       string  `json:"source_url"`
	SourcePath      string  `json:"source_path"`
	DestinationURL  string  `json:"destination_url"`
	DestinationPath *string `json:"destination_path"`
	
	// Redirect Configuration
	RedirectType      RedirectType        `json:"redirect_type"`
	RedirectMatch     RedirectMatch       `json:"redirect_match"`
	RegexPattern      *string             `json:"regex_pattern"`
	ReplacementPattern *string            `json:"replacement_pattern"`
	
	// Conditional Redirects
	Conditions           map[string]interface{} `json:"conditions"`
	QueryStringHandling  QueryStringHandling    `json:"query_string_handling"`
	
	// Tracking and Analytics
	HitCount           uint      `json:"hit_count"`
	LastHitAt          *time.Time `json:"last_hit_at"`
	LastHitIP          *string   `json:"last_hit_ip"`
	LastHitUserAgent   *string   `json:"last_hit_user_agent"`
	LastHitReferrer    *string   `json:"last_hit_referrer"`
	
	// SEO Information
	SEOReason *string `json:"seo_reason"`
	Notes     *string `json:"notes"`
	CreatedBy *uint   `json:"created_by"`
	UpdatedBy *uint   `json:"updated_by"`
	
	// Scheduling
	ActiveFrom  *time.Time `json:"active_from"`
	ActiveUntil *time.Time `json:"active_until"`
	
	// Status and Timestamps
	Status    RedirectStatus `json:"status"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	
	// Relationships - removed to avoid circular dependencies
}

// SEORedirectListRequest represents the request to list SEO redirects
type SEORedirectListRequest struct {
	WebsiteID uint   `json:"website_id" binding:"required"`
	TenantID  uint   `json:"tenant_id" binding:"required"`
	
	// Filters
	Status       *RedirectStatus `json:"status" binding:"omitempty,oneof=active inactive expired deleted"`
	RedirectType *RedirectType   `json:"redirect_type" binding:"omitempty,oneof=301 302 303 307 308"`
	SourceURL    *string         `json:"source_url" binding:"omitempty,max=500"`
	CreatedBy    *uint           `json:"created_by"`
	
	// Pagination
	Page     int `json:"page" binding:"omitempty,min=1"`
	PageSize int `json:"page_size" binding:"omitempty,min=1,max=100"`
	
	// Sorting
	SortBy    *string `json:"sort_by" binding:"omitempty,oneof=created_at updated_at hit_count last_hit_at"`
	SortOrder *string `json:"sort_order" binding:"omitempty,oneof=asc desc"`
}

// SEORedirectStatsResponse represents redirect statistics
type SEORedirectStatsResponse struct {
	TotalRedirects    int `json:"total_redirects"`
	ActiveRedirects   int `json:"active_redirects"`
	InactiveRedirects int `json:"inactive_redirects"`
	ExpiredRedirects  int `json:"expired_redirects"`
	TotalHits         int `json:"total_hits"`
	Top301Redirects   int `json:"top_301_redirects"`
	Top302Redirects   int `json:"top_302_redirects"`
	RecentHits        int `json:"recent_hits"`
}

// IsActive checks if the redirect is currently active
func (r *SEORedirect) IsActive() bool {
	if r.Status != RedirectStatusActive {
		return false
	}
	
	now := time.Now()
	
	// Check if redirect is within active period
	if r.ActiveFrom != nil && now.Before(*r.ActiveFrom) {
		return false
	}
	
	if r.ActiveUntil != nil && now.After(*r.ActiveUntil) {
		return false
	}
	
	return true
}

// IncrementHit increments the hit count and updates hit tracking information
func (r *SEORedirect) IncrementHit(ip, userAgent, referrer string) {
	now := time.Now()
	r.HitCount++
	r.LastHitAt = &now
	r.LastHitIP = &ip
	r.LastHitUserAgent = &userAgent
	r.LastHitReferrer = &referrer
}

// ToResponse converts SEORedirect to SEORedirectResponse
func (r *SEORedirect) ToResponse() *SEORedirectResponse {
	return &SEORedirectResponse{
		ID:        r.ID,
		WebsiteID: r.WebsiteID,
		TenantID:  r.TenantID,
		
		SourceURL:       r.SourceURL,
		SourcePath:      r.SourcePath,
		DestinationURL:  r.DestinationURL,
		DestinationPath: r.DestinationPath,
		
		RedirectType:      r.RedirectType,
		RedirectMatch:     r.RedirectMatch,
		RegexPattern:      r.RegexPattern,
		ReplacementPattern: r.ReplacementPattern,
		
		Conditions:           r.Conditions,
		QueryStringHandling:  r.QueryStringHandling,
		
		HitCount:           r.HitCount,
		LastHitAt:          r.LastHitAt,
		LastHitIP:          r.LastHitIP,
		LastHitUserAgent:   r.LastHitUserAgent,
		LastHitReferrer:    r.LastHitReferrer,
		
		SEOReason: r.SEOReason,
		Notes:     r.Notes,
		CreatedBy: r.CreatedBy,
		UpdatedBy: r.UpdatedBy,
		
		ActiveFrom:  r.ActiveFrom,
		ActiveUntil: r.ActiveUntil,
		
		Status:    r.Status,
		CreatedAt: r.CreatedAt,
		UpdatedAt: r.UpdatedAt,
		
		// Relationships removed to avoid circular dependencies
	}
}

// TableName returns the table name for GORM
func (SEORedirect) TableName() string {
	return "seo_redirects"
}

// BeforeCreate hook for GORM
func (r *SEORedirect) BeforeCreate(tx *gorm.DB) error {
	if r.Conditions == nil {
		r.Conditions = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (r *SEORedirect) BeforeUpdate(tx *gorm.DB) error {
	if r.Conditions == nil {
		r.Conditions = make(map[string]interface{})
	}
	return nil
}