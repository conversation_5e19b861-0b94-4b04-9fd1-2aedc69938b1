package models

import (
	"time"
	"gorm.io/gorm"
)

// SitemapType represents the type of sitemap
type SitemapType string

const (
	SitemapTypePages  SitemapType = "pages"
	SitemapTypeBlog   SitemapType = "blog"
	SitemapTypeStatic SitemapType = "static"
	SitemapTypeIndex  SitemapType = "index"
)

// SitemapStatus represents the status of a sitemap
type SitemapStatus string

const (
	SitemapStatusActive  SitemapStatus = "active"
	SitemapStatusPending SitemapStatus = "pending"
	SitemapStatusError   SitemapStatus = "error"
	SitemapStatusDeleted SitemapStatus = "deleted"
)

// SEOSitemap represents a sitemap configuration
type SEOSitemap struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	WebsiteID uint      `json:"website_id" gorm:"not null;index"`
	TenantID  uint      `json:"tenant_id" gorm:"not null;index"`
	
	// Sitemap Information
	Type        SitemapType `json:"type" gorm:"type:enum('pages','blog','static','index');not null;index"`
	Name        string      `json:"name" gorm:"type:varchar(255);not null;index"`
	Description *string     `json:"description" gorm:"type:text"`
	Filename    string      `json:"filename" gorm:"type:varchar(255);not null;index"`
	
	// Content Information
	URLCount    int     `json:"url_count" gorm:"not null;default:0"`
	FileSize    int     `json:"file_size" gorm:"not null;default:0"`
	Compressed  bool    `json:"compressed" gorm:"not null;default:false"`
	Content     string  `json:"content,omitempty" gorm:"type:longtext"`
	
	// Generation Settings
	GenerationSettings map[string]interface{} `json:"generation_settings" gorm:"type:json;default:'{}';comment:Settings for sitemap generation"`
	
	// Timestamps
	GeneratedAt time.Time `json:"generated_at" gorm:"autoCreateTime"`
	
	// Status and Timestamps
	Status    SitemapStatus `json:"status" gorm:"type:varchar(20);not null;default:'active';index"`
	CreatedAt time.Time     `json:"created_at" gorm:"autoCreateTime;index"`
	UpdatedAt time.Time     `json:"updated_at" gorm:"autoUpdateTime;index"`
	
	// Relationships - removed to avoid circular dependencies
}

// SEOSitemapCreateRequest represents the request to create a new SEO sitemap
type SEOSitemapCreateRequest struct {
	WebsiteID uint   `json:"website_id" binding:"required"`
	TenantID  uint   `json:"tenant_id" binding:"required"`
	
	// Sitemap Information
	Type        SitemapType `json:"type" binding:"required,oneof=pages blog static index"`
	Name        string      `json:"name" binding:"required,max=255"`
	Description *string     `json:"description" binding:"omitempty,max=1000"`
	Filename    string      `json:"filename" binding:"required,max=255"`
	
	// Content Information
	URLCount    int  `json:"url_count" binding:"omitempty,min=0"`
	FileSize    int  `json:"file_size" binding:"omitempty,min=0"`
	Compressed  bool `json:"compressed"`
	
	// Generation Settings
	GenerationSettings map[string]interface{} `json:"generation_settings"`
	
	// Status
	Status SitemapStatus `json:"status" binding:"omitempty,oneof=active pending error deleted"`
}

// SEOSitemapUpdateRequest represents the request to update an SEO sitemap
type SEOSitemapUpdateRequest struct {
	// Sitemap Information
	Name        *string `json:"name" binding:"omitempty,max=255"`
	Description *string `json:"description" binding:"omitempty,max=1000"`
	Filename    *string `json:"filename" binding:"omitempty,max=255"`
	
	// Content Information
	URLCount    *int  `json:"url_count" binding:"omitempty,min=0"`
	FileSize    *int  `json:"file_size" binding:"omitempty,min=0"`
	Compressed  *bool `json:"compressed"`
	
	// Generation Settings
	GenerationSettings map[string]interface{} `json:"generation_settings"`
	
	// Status
	Status *SitemapStatus `json:"status" binding:"omitempty,oneof=active pending error deleted"`
}

// SEOSitemapResponse represents the response for SEO sitemap operations
type SEOSitemapResponse struct {
	ID        uint      `json:"id"`
	WebsiteID uint      `json:"website_id"`
	TenantID  uint      `json:"tenant_id"`
	
	// Sitemap Information
	Type        SitemapType `json:"type"`
	Name        string      `json:"name"`
	Description *string     `json:"description"`
	Filename    string      `json:"filename"`
	
	// Content Information
	URLCount    int  `json:"url_count"`
	FileSize    int  `json:"file_size"`
	Compressed  bool `json:"compressed"`
	
	// Generation Settings
	GenerationSettings map[string]interface{} `json:"generation_settings"`
	
	// Timestamps
	GeneratedAt time.Time `json:"generated_at"`
	
	// Status and Timestamps
	Status    SitemapStatus `json:"status"`
	CreatedAt time.Time     `json:"created_at"`
	UpdatedAt time.Time     `json:"updated_at"`
	
	// Relationships - removed to avoid circular dependencies
}

// SEOSitemapListRequest represents the request to list SEO sitemaps
type SEOSitemapListRequest struct {
	WebsiteID uint   `json:"website_id" binding:"required"`
	TenantID  uint   `json:"tenant_id" binding:"required"`
	
	// Filters
	Type   *SitemapType   `json:"type" binding:"omitempty,oneof=pages blog static index"`
	Status *SitemapStatus `json:"status" binding:"omitempty,oneof=active pending error deleted"`
	Name   *string        `json:"name" binding:"omitempty,max=255"`
	
	// Pagination
	Page     int `json:"page" binding:"omitempty,min=1"`
	PageSize int `json:"page_size" binding:"omitempty,min=1,max=100"`
	
	// Sorting
	SortBy    *string `json:"sort_by" binding:"omitempty,oneof=created_at updated_at generated_at url_count file_size"`
	SortOrder *string `json:"sort_order" binding:"omitempty,oneof=asc desc"`
}

// SEOSitemapStatsResponse represents sitemap statistics
type SEOSitemapStatsResponse struct {
	TotalSitemaps      int `json:"total_sitemaps"`
	ActiveSitemaps     int `json:"active_sitemaps"`
	PendingSitemaps    int `json:"pending_sitemaps"`
	ErrorSitemaps      int `json:"error_sitemaps"`
	TotalURLs          int `json:"total_urls"`
	TotalFileSize      int `json:"total_file_size"`
	CompressedSitemaps int `json:"compressed_sitemaps"`
}

// IsActive checks if the sitemap is currently active
func (s *SEOSitemap) IsActive() bool {
	return s.Status == SitemapStatusActive
}

// ToResponse converts SEOSitemap to SEOSitemapResponse
func (s *SEOSitemap) ToResponse() *SEOSitemapResponse {
	return &SEOSitemapResponse{
		ID:        s.ID,
		WebsiteID: s.WebsiteID,
		TenantID:  s.TenantID,
		
		Type:        s.Type,
		Name:        s.Name,
		Description: s.Description,
		Filename:    s.Filename,
		
		URLCount:    s.URLCount,
		FileSize:    s.FileSize,
		Compressed:  s.Compressed,
		
		GenerationSettings: s.GenerationSettings,
		
		GeneratedAt: s.GeneratedAt,
		
		Status:    s.Status,
		CreatedAt: s.CreatedAt,
		UpdatedAt: s.UpdatedAt,
		
		// Relationships removed to avoid circular dependencies
	}
}

// TableName returns the table name for GORM
func (SEOSitemap) TableName() string {
	return "seo_sitemaps"
}

// BeforeCreate hook for GORM
func (s *SEOSitemap) BeforeCreate(tx *gorm.DB) error {
	if s.GenerationSettings == nil {
		s.GenerationSettings = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (s *SEOSitemap) BeforeUpdate(tx *gorm.DB) error {
	if s.GenerationSettings == nil {
		s.GenerationSettings = make(map[string]interface{})
	}
	return nil
}