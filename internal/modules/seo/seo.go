package seo

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// SEOModule represents the SEO module with all its components
type SEOModule struct {
	// Repositories
	RedirectRepository repositories.SEORedirectRepository
	SitemapRepository  repositories.SEOSitemapRepository
	
	// Services
	RedirectService services.SEORedirectService
	SitemapService  services.SEOSitemapService
	
	// Handlers
	RedirectHandler *handlers.SEORedirectHandler
	SitemapHandler  *handlers.SEOSitemapHandler
}

// NewSEOModule creates a new SEO module instance
func NewSEOModule(db *gorm.DB, logger utils.Logger) *SEOModule {
	// Initialize repositories
	redirectRepo := repositories.NewSEORedirectRepository(db)
	sitemapRepo := repositories.NewSEOSitemapRepository(db)
	
	// Initialize services
	redirectService := services.NewSEORedirectService(redirectRepo, logger)
	sitemapService := services.NewSEOSitemapService(sitemapRepo, logger)
	
	// Initialize handlers
	redirectHandler := handlers.NewSEORedirectHandler(redirectService, logger)
	sitemapHandler := handlers.NewSEOSitemapHandler(sitemapService, logger)
	
	return &SEOModule{
		RedirectRepository: redirectRepo,
		SitemapRepository:  sitemapRepo,
		RedirectService:    redirectService,
		SitemapService:     sitemapService,
		RedirectHandler:    redirectHandler,
		SitemapHandler:     sitemapHandler,
	}
}

// RegisterRoutes registers all SEO module routes
func (m *SEOModule) RegisterRoutes(router *gin.RouterGroup) {
	RegisterRoutes(router, m.RedirectHandler, m.SitemapHandler)
}

// GetRedirectService returns the redirect service
func (m *SEOModule) GetRedirectService() services.SEORedirectService {
	return m.RedirectService
}

// GetSitemapService returns the sitemap service
func (m *SEOModule) GetSitemapService() services.SEOSitemapService {
	return m.SitemapService
}