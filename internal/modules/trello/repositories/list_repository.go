package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
)

// ListRepository defines the interface for list data operations
type ListRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, list *models.TrelloList) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.TrelloList, error)
	Update(ctx context.Context, tenantID, id uint, list *models.TrelloList) error
	Delete(ctx context.Context, tenantID, id uint) error
	
	// List operations
	ListByBoard(ctx context.Context, tenantID, boardID uint) ([]models.TrelloList, error)
	ListByBoardWithCards(ctx context.Context, tenantID, boardID uint) ([]models.TrelloList, error)
	ListByStatus(ctx context.Context, tenantID, boardID uint, status models.TrelloStatus) ([]models.TrelloList, error)
	
	// List-specific operations
	GetByName(ctx context.Context, tenantID, boardID uint, name string) (*models.TrelloList, error)
	GetWithCards(ctx context.Context, tenantID, id uint) (*models.TrelloList, error)
	
	// Position operations
	GetMaxPosition(ctx context.Context, tenantID, boardID uint) (float64, error)
	GetNextPosition(ctx context.Context, tenantID, boardID uint, afterPosition *float64) (float64, error)
	UpdatePosition(ctx context.Context, tenantID, id uint, position float64) error
	UpdatePositions(ctx context.Context, tenantID, boardID uint, positions map[uint]float64) error
	
	// Archive operations
	Archive(ctx context.Context, tenantID, id uint) error
	Unarchive(ctx context.Context, tenantID, id uint) error
	
	// Move operations
	Move(ctx context.Context, tenantID, id uint, boardID *uint, position float64) error
	MoveToBoard(ctx context.Context, tenantID, listID, newBoardID uint, position float64) error
	
	// Count operations
	CountByBoard(ctx context.Context, tenantID, boardID uint) (int64, error)
	CountActiveByBoard(ctx context.Context, tenantID, boardID uint) (int64, error)
}