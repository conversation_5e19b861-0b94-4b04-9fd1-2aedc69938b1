package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// CardRepository defines the interface for card data operations
type CardRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, card *models.TrelloCard) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.TrelloCard, error)
	Update(ctx context.Context, tenantID, id uint, card *models.TrelloCard) error
	Delete(ctx context.Context, tenantID, id uint) error
	
	// List operations
	ListByList(ctx context.Context, tenantID, listID uint, pagination *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error)
	ListByBoard(ctx context.Context, tenantID, boardID uint, pagination *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error)
	ListByStatus(ctx context.Context, tenantID, listID uint, status models.TrelloStatus, pagination *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error)
	
	// Card-specific operations
	GetDetail(ctx context.Context, tenantID, id uint) (*models.TrelloCard, error)
	GetWithDetails(ctx context.Context, tenantID, id uint) (*models.TrelloCard, error)
	GetWithMembers(ctx context.Context, tenantID, id uint) (*models.TrelloCard, error)
	GetWithLabels(ctx context.Context, tenantID, id uint) (*models.TrelloCard, error)
	GetWithChecklists(ctx context.Context, tenantID, id uint) (*models.TrelloCard, error)
	GetWithComments(ctx context.Context, tenantID, id uint) (*models.TrelloCard, error)
	GetWithActivities(ctx context.Context, tenantID, id uint) (*models.TrelloCard, error)
	
	// Position operations
	GetMaxPosition(ctx context.Context, tenantID, listID uint) (float64, error)
	GetNextPosition(ctx context.Context, tenantID, listID uint, afterPosition *float64) (float64, error)
	UpdatePosition(ctx context.Context, tenantID, id uint, position float64) error
	UpdatePositions(ctx context.Context, tenantID, listID uint, positions map[uint]float64) error
	
	// Move operations
	Move(ctx context.Context, tenantID, id uint, listID *uint, position float64) error
	MoveToList(ctx context.Context, tenantID, cardID, newListID uint, position float64) error
	MoveBatch(ctx context.Context, tenantID uint, moves []struct{ CardID, ListID uint; Position float64 }) error
	
	// Member operations
	AssignMember(ctx context.Context, member *models.TrelloCardMember) error
	UnassignMember(ctx context.Context, tenantID, cardID, userID uint) error
	GetMember(ctx context.Context, tenantID, cardID, userID uint) (*models.TrelloCardMember, error)
	GetMembers(ctx context.Context, tenantID, cardID uint) ([]models.TrelloCardMember, error)
	ListMembers(ctx context.Context, tenantID, cardID uint) ([]models.TrelloCardMember, error)
	IsMemberAssigned(ctx context.Context, tenantID, cardID, userID uint) (bool, error)
	
	// Label operations
	ApplyLabel(ctx context.Context, cardLabel *models.TrelloCardLabel) error
	RemoveLabel(ctx context.Context, tenantID, cardID, labelID uint) error
	GetLabel(ctx context.Context, tenantID, cardID, labelID uint) (*models.TrelloCardLabel, error)
	GetLabels(ctx context.Context, tenantID, cardID uint) ([]models.TrelloCardLabel, error)
	ListLabels(ctx context.Context, tenantID, cardID uint) ([]models.TrelloCardLabel, error)
	IsLabelApplied(ctx context.Context, tenantID, cardID, labelID uint) (bool, error)
	
	// Comment operations
	CreateComment(ctx context.Context, comment *models.TrelloCardComment) error
	UpdateComment(ctx context.Context, tenantID, commentID uint, comment *models.TrelloCardComment) error
	DeleteComment(ctx context.Context, tenantID, commentID uint) error
	GetComment(ctx context.Context, tenantID, commentID uint) (*models.TrelloCardComment, error)
	ListComments(ctx context.Context, tenantID, cardID uint, pagination *pagination.CursorPagination) ([]models.TrelloCardComment, *pagination.CursorResponse, error)
	
	// Activity operations
	CreateActivity(ctx context.Context, activity *models.TrelloCardActivity) error
	ListActivities(ctx context.Context, tenantID, cardID uint, pagination *pagination.CursorPagination) ([]models.TrelloCardActivity, *pagination.CursorResponse, error)
	ListBoardActivities(ctx context.Context, tenantID, boardID uint, pagination *pagination.CursorPagination) ([]models.TrelloCardActivity, *pagination.CursorResponse, error)
	
	// Archive operations
	Archive(ctx context.Context, tenantID, id uint) error
	Unarchive(ctx context.Context, tenantID, id uint) error
	
	// Search operations
	Search(ctx context.Context, tenantID, boardID uint, query string, pagination *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error)
	SearchByTitle(ctx context.Context, tenantID, boardID uint, title string, pagination *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error)
	
	// Due date operations
	GetCardsDueBy(ctx context.Context, tenantID, boardID uint, dueDate time.Time, pagination *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error)
	GetOverdueCards(ctx context.Context, tenantID, boardID uint, pagination *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error)
	GetCardsAssignedToUser(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.TrelloCard, *pagination.CursorResponse, error)
	
	// Count operations
	CountByList(ctx context.Context, tenantID, listID uint) (int64, error)
	CountByBoard(ctx context.Context, tenantID, boardID uint) (int64, error)
	CountActiveByList(ctx context.Context, tenantID, listID uint) (int64, error)
}