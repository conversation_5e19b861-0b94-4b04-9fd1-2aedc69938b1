package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BoardRepository defines the interface for board data operations
type BoardRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, board *models.TrelloBoard) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.TrelloBoard, error)
	Update(ctx context.Context, tenantID, id uint, board *models.TrelloBoard) error
	Delete(ctx context.Context, tenantID, id uint) error
	
	// List operations
	List(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error)
	ListByWorkspace(ctx context.Context, tenantID, workspaceID uint, pagination *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error)
	ListByStatus(ctx context.Context, tenantID uint, status models.TrelloStatus, pagination *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error)
	
	// Board-specific operations
	GetByName(ctx context.Context, tenantID, workspaceID uint, name string) (*models.TrelloBoard, error)
	GetWithLists(ctx context.Context, tenantID, id uint) (*models.TrelloBoard, error)
	GetWithMembers(ctx context.Context, tenantID, id uint) (*models.TrelloBoard, error)
	GetWithLabels(ctx context.Context, tenantID, id uint) (*models.TrelloBoard, error)
	
	// Member operations
	AddMember(ctx context.Context, member *models.TrelloBoardMember) error
	RemoveMember(ctx context.Context, tenantID, boardID, userID uint) error
	UpdateMember(ctx context.Context, tenantID, boardID, userID uint, member *models.TrelloBoardMember) error
	GetMember(ctx context.Context, tenantID, boardID, userID uint) (*models.TrelloBoardMember, error)
	ListMembers(ctx context.Context, tenantID, boardID uint) ([]models.TrelloBoardMember, error)
	IsMember(ctx context.Context, tenantID, boardID, userID uint) (bool, error)
	GetMemberRole(ctx context.Context, tenantID, boardID, userID uint) (models.MemberRole, error)
	
	// Label operations
	CreateLabel(ctx context.Context, label *models.TrelloBoardLabel) error
	UpdateLabel(ctx context.Context, tenantID, labelID uint, label *models.TrelloBoardLabel) error
	DeleteLabel(ctx context.Context, tenantID, labelID uint) error
	GetLabel(ctx context.Context, tenantID, labelID uint) (*models.TrelloBoardLabel, error)
	ListLabels(ctx context.Context, tenantID, boardID uint) ([]models.TrelloBoardLabel, error)
	
	// Search and filter operations
	SearchByName(ctx context.Context, tenantID uint, query string, pagination *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error)
	GetUserBoards(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error)
	GetStarredBoards(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error)
	GetRecentBoards(ctx context.Context, tenantID, userID uint, limit int) ([]models.TrelloBoard, error)
}