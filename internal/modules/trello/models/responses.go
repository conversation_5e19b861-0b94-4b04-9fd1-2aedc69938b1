package models

import (
	"time"

	"gorm.io/datatypes"
)

// Workspace Responses
type WorkspaceResponse struct {
	ID          uint           `json:"id"`
	TenantID    uint           `json:"tenant_id"`
	Name        string         `json:"name"`
	Description *string        `json:"description,omitempty"`
	Visibility  VisibilityType `json:"visibility"`
	LogoURL     *string        `json:"logo_url,omitempty"`
	WebsiteURL  *string        `json:"website_url,omitempty"`
	Settings    datatypes.JSON `json:"settings"`
	CreatedBy   uint           `json:"created_by"`
	Status      TrelloStatus   `json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

type WorkspaceWithBoardsResponse struct {
	WorkspaceResponse
	Boards []BoardSummaryResponse `json:"boards"`
}

type WorkspaceWithMembersResponse struct {
	WorkspaceResponse
	Members []WorkspaceMemberResponse `json:"members"`
}

type WorkspaceMemberResponse struct {
	ID          uint         `json:"id"`
	TenantID    uint         `json:"tenant_id"`
	WorkspaceID uint         `json:"workspace_id"`
	UserID      uint         `json:"user_id"`
	Role        MemberRole   `json:"role"`
	InvitedBy   uint         `json:"invited_by"`
	JoinedAt    time.Time    `json:"joined_at"`
	Status      MemberStatus `json:"status"`
}

// Board Responses
type BoardResponse struct {
	ID                uint           `json:"id"`
	TenantID          uint           `json:"tenant_id"`
	WorkspaceID       uint           `json:"workspace_id"`
	Name              string         `json:"name"`
	Description       *string        `json:"description,omitempty"`
	BackgroundType    BackgroundType `json:"background_type"`
	BackgroundValue   string         `json:"background_value"`
	Visibility        VisibilityType `json:"visibility"`
	IsClosed          bool           `json:"is_closed"`
	IsStarred         bool           `json:"is_starred"`
	Preferences       datatypes.JSON `json:"preferences"`
	LabelsNormalized  datatypes.JSON `json:"labels_normalized"`
	CreatedBy         uint           `json:"created_by"`
	Status            TrelloStatus   `json:"status"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
}

type BoardSummaryResponse struct {
	ID              uint           `json:"id"`
	Name            string         `json:"name"`
	BackgroundType  BackgroundType `json:"background_type"`
	BackgroundValue string         `json:"background_value"`
	Visibility      VisibilityType `json:"visibility"`
	IsClosed        bool           `json:"is_closed"`
	IsStarred       bool           `json:"is_starred"`
	UpdatedAt       time.Time      `json:"updated_at"`
}

type BoardWithListsResponse struct {
	BoardResponse
	Lists []ListWithCardsResponse `json:"lists"`
}

type BoardMemberResponse struct {
	ID        uint         `json:"id"`
	TenantID  uint         `json:"tenant_id"`
	BoardID   uint         `json:"board_id"`
	UserID    uint         `json:"user_id"`
	Role      MemberRole   `json:"role"`
	InvitedBy uint         `json:"invited_by"`
	JoinedAt  time.Time    `json:"joined_at"`
	Status    MemberStatus `json:"status"`
}

// List Responses
type ListResponse struct {
	ID           uint         `json:"id"`
	TenantID     uint         `json:"tenant_id"`
	BoardID      uint         `json:"board_id"`
	Name         string       `json:"name"`
	Position     float64      `json:"position"`
	IsClosed     bool         `json:"is_closed"`
	IsSubscribed bool         `json:"is_subscribed"`
	Settings     datatypes.JSON `json:"settings"`
	Status       TrelloStatus `json:"status"`
	CreatedAt    time.Time    `json:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at"`
}

type ListWithCardsResponse struct {
	ListResponse
	Cards []CardSummaryResponse `json:"cards"`
}

// Card Responses
type CardResponse struct {
	ID           uint           `json:"id"`
	TenantID     uint           `json:"tenant_id"`
	BoardID      uint           `json:"board_id"`
	ListID       uint           `json:"list_id"`
	Title        string         `json:"title"`
	Description  *string        `json:"description,omitempty"`
	Position     float64        `json:"position"`
	DueDate      *time.Time     `json:"due_date,omitempty"`
	StartDate    *time.Time     `json:"start_date,omitempty"`
	DueComplete  bool           `json:"due_complete"`
	IsClosed     bool           `json:"is_closed"`
	IsSubscribed bool           `json:"is_subscribed"`
	CoverType    CoverType      `json:"cover_type"`
	CoverValue   *string        `json:"cover_value,omitempty"`
	CoverColor   *string        `json:"cover_color,omitempty"`
	CoverSize    CoverSize      `json:"cover_size"`
	Badges       datatypes.JSON `json:"badges"`
	CustomFields datatypes.JSON `json:"custom_fields"`
	CreatedBy    uint           `json:"created_by"`
	Status       TrelloStatus   `json:"status"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
}

type CardSummaryResponse struct {
	ID          uint      `json:"id"`
	Title       string    `json:"title"`
	Position    float64   `json:"position"`
	DueDate     *time.Time `json:"due_date,omitempty"`
	DueComplete bool      `json:"due_complete"`
	IsClosed    bool      `json:"is_closed"`
	CoverType   CoverType `json:"cover_type"`
	CoverValue  *string   `json:"cover_value,omitempty"`
	CoverColor  *string   `json:"cover_color,omitempty"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type CardDetailResponse struct {
	CardResponse
	Members    []CardMemberResponse    `json:"members"`
	Labels     []CardLabelResponse     `json:"labels"`
	Checklists []ChecklistResponse     `json:"checklists"`
	Comments   []CardCommentResponse   `json:"comments"`
	Activities []CardActivityResponse  `json:"activities"`
}

// Label Responses
type LabelResponse struct {
	ID        uint         `json:"id"`
	TenantID  uint         `json:"tenant_id"`
	BoardID   uint         `json:"board_id"`
	Name      string       `json:"name"`
	Color     string       `json:"color"`
	Uses      uint         `json:"uses"`
	CreatedBy uint         `json:"created_by"`
	Status    TrelloStatus `json:"status"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
}

type CardLabelResponse struct {
	ID        uint      `json:"id"`
	LabelID   uint      `json:"label_id"`
	Name      string    `json:"name"`
	Color     string    `json:"color"`
	AppliedBy uint      `json:"applied_by"`
	AppliedAt time.Time `json:"applied_at"`
}

// Member Responses
type CardMemberResponse struct {
	ID         uint      `json:"id"`
	UserID     uint      `json:"user_id"`
	AssignedBy uint      `json:"assigned_by"`
	AssignedAt time.Time `json:"assigned_at"`
}

// Checklist Responses
type ChecklistResponse struct {
	ID                    uint         `json:"id"`
	TenantID              uint         `json:"tenant_id"`
	CardID                uint         `json:"card_id"`
	Name                  string       `json:"name"`
	Position              float64      `json:"position"`
	CheckItemsCount       uint         `json:"check_items_count"`
	CheckItemsCheckedCount uint        `json:"check_items_checked_count"`
	CreatedBy             uint         `json:"created_by"`
	Status                TrelloStatus `json:"status"`
	CreatedAt             time.Time    `json:"created_at"`
	UpdatedAt             time.Time    `json:"updated_at"`
	Items                 []ChecklistItemResponse `json:"items"`
}

type ChecklistItemResponse struct {
	ID             uint         `json:"id"`
	TenantID       uint         `json:"tenant_id"`
	ChecklistID    uint         `json:"checklist_id"`
	CardID         uint         `json:"card_id"`
	Name           string       `json:"name"`
	Position       float64      `json:"position"`
	IsChecked      bool         `json:"is_checked"`
	DueDate        *time.Time   `json:"due_date,omitempty"`
	AssignedUserID *uint        `json:"assigned_user_id,omitempty"`
	CreatedBy      uint         `json:"created_by"`
	CheckedBy      *uint        `json:"checked_by,omitempty"`
	CheckedAt      *time.Time   `json:"checked_at,omitempty"`
	Status         TrelloStatus `json:"status"`
	CreatedAt      time.Time    `json:"created_at"`
	UpdatedAt      time.Time    `json:"updated_at"`
}

// Comment Responses
type CardCommentResponse struct {
	ID        uint        `json:"id"`
	TenantID  uint        `json:"tenant_id"`
	CardID    uint        `json:"card_id"`
	BoardID   uint        `json:"board_id"`
	UserID    uint        `json:"user_id"`
	Content   string      `json:"content"`
	Type      CommentType `json:"type"`
	CreatedAt time.Time   `json:"created_at"`
	UpdatedAt time.Time   `json:"updated_at"`
}

// Activity Responses
type CardActivityResponse struct {
	ID        uint           `json:"id"`
	TenantID  uint           `json:"tenant_id"`
	BoardID   uint           `json:"board_id"`
	CardID    uint           `json:"card_id"`
	ListID    *uint          `json:"list_id,omitempty"`
	UserID    uint           `json:"user_id"`
	Type      string         `json:"type"`
	Data      datatypes.JSON `json:"data"`
	OldValue  datatypes.JSON `json:"old_value,omitempty"`
	NewValue  datatypes.JSON `json:"new_value,omitempty"`
	CreatedAt time.Time      `json:"created_at"`
}