package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"gorm.io/datatypes"
)

// @Summary Create board request
// @Description Request to create a new Trello board
// @Tags Board
type CreateBoardRequest struct {
	WorkspaceID     uint                    `json:"workspace_id" validate:"required" example:"1"`
	Name            string                  `json:"name" validate:"required,max=255" example:"Project Board"`
	Description     *string                 `json:"description" validate:"omitempty,max=1000" example:"Board for tracking project tasks"`
	BackgroundType  models.BackgroundType   `json:"background_type" validate:"oneof=color image gradient" example:"color"`
	BackgroundValue *string                 `json:"background_value,omitempty" example:"#3498db"`
	Visibility      models.VisibilityType   `json:"visibility" validate:"oneof=private workspace public" example:"workspace"`
	Preferences     datatypes.JSON          `json:"preferences,omitempty" example:"{\"voting\":true,\"comments\":true}"`
}

// @Summary Update board request
// @Description Request to update an existing Trello board
// @Tags Board
type UpdateBoardRequest struct {
	Name            *string                 `json:"name" validate:"omitempty,max=255" example:"Updated Project Board"`
	Description     *string                 `json:"description" validate:"omitempty,max=1000" example:"Updated description"`
	BackgroundType  *models.BackgroundType  `json:"background_type" validate:"omitempty,oneof=color image gradient" example:"gradient"`
	BackgroundValue *string                 `json:"background_value,omitempty" example:"linear-gradient(45deg, #3498db, #2ecc71)"`
	Visibility      *models.VisibilityType  `json:"visibility" validate:"omitempty,oneof=private workspace public" example:"public"`
	IsClosed        *bool                   `json:"is_closed,omitempty" example:"false"`
	IsStarred       *bool                   `json:"is_starred,omitempty" example:"true"`
	Preferences     datatypes.JSON          `json:"preferences,omitempty" example:"{\"voting\":false,\"comments\":true}"`
}

// @Summary Board response
// @Description Board information in API responses
// @Tags Board
type BoardResponse struct {
	ID                uint                    `json:"id" example:"1"`
	TenantID          uint                    `json:"tenant_id" example:"1"`
	WorkspaceID       uint                    `json:"workspace_id" example:"1"`
	Name              string                  `json:"name" example:"Project Board"`
	Description       *string                 `json:"description,omitempty" example:"Board for tracking project tasks"`
	BackgroundType    models.BackgroundType   `json:"background_type" example:"color"`
	BackgroundValue   string                  `json:"background_value" example:"#3498db"`
	Visibility        models.VisibilityType   `json:"visibility" example:"workspace"`
	IsClosed          bool                    `json:"is_closed" example:"false"`
	IsStarred         bool                    `json:"is_starred" example:"true"`
	Preferences       datatypes.JSON          `json:"preferences" example:"{\"voting\":true}"`
	LabelsNormalized  datatypes.JSON          `json:"labels_normalized" example:"{\"green\":\"Done\",\"yellow\":\"In Progress\"}"`
	CreatedBy         uint                    `json:"created_by" example:"123"`
	Status            models.TrelloStatus     `json:"status" example:"active"`
	CreatedAt         time.Time               `json:"created_at"`
	UpdatedAt         time.Time               `json:"updated_at"`
}

// @Summary Board summary response
// @Description Simplified board information for listings
// @Tags Board
type BoardSummaryResponse struct {
	ID              uint                    `json:"id" example:"1"`
	Name            string                  `json:"name" example:"Project Board"`
	BackgroundType  models.BackgroundType   `json:"background_type" example:"color"`
	BackgroundValue string                  `json:"background_value" example:"#3498db"`
	Visibility      models.VisibilityType   `json:"visibility" example:"workspace"`
	IsClosed        bool                    `json:"is_closed" example:"false"`
	IsStarred       bool                    `json:"is_starred" example:"true"`
	UpdatedAt       time.Time               `json:"updated_at"`
}

// @Summary Board with lists response
// @Description Board information with associated lists
// @Tags Board
type BoardWithListsResponse struct {
	BoardResponse
	Lists []ListWithCardsResponse `json:"lists"`
}

// @Summary Board member response
// @Description Board member information in API responses
// @Tags Board
type BoardMemberResponse struct {
	ID        uint                 `json:"id" example:"1"`
	TenantID  uint                 `json:"tenant_id" example:"1"`
	BoardID   uint                 `json:"board_id" example:"1"`
	UserID    uint                 `json:"user_id" example:"123"`
	Role      models.MemberRole    `json:"role" example:"member"`
	InvitedBy uint                 `json:"invited_by" example:"456"`
	JoinedAt  time.Time            `json:"joined_at"`
	Status    models.MemberStatus  `json:"status" example:"active"`
}

// @Summary Add board member request
// @Description Request to add a member to board
// @Tags Board
type AddBoardMemberRequest struct {
	UserID uint              `json:"user_id" validate:"required" example:"123"`
	Role   models.MemberRole `json:"role" validate:"oneof=owner admin member observer" example:"member"`
}

// @Summary Update board member request
// @Description Request to update board member role/status
// @Tags Board
type UpdateBoardMemberRequest struct {
	Role   *models.MemberRole   `json:"role,omitempty" validate:"omitempty,oneof=owner admin member observer" example:"admin"`
	Status *models.MemberStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive pending suspended" example:"active"`
}

// @Summary Board list response
// @Description Response for listing boards
// @Tags Board
type BoardListResponse struct {
	Boards     []BoardResponse `json:"boards"`
	Total      int64           `json:"total" example:"15"`
	Page       int             `json:"page" example:"1"`
	PageSize   int             `json:"page_size" example:"10"`
	TotalPages int             `json:"total_pages" example:"2"`
}

// @Summary Board filter
// @Description Filter parameters for listing boards
// @Tags Board
type BoardFilter struct {
	WorkspaceID    *uint                   `json:"workspace_id,omitempty" example:"1"`
	Name           string                  `json:"name,omitempty" example:"Project"`
	Visibility     *models.VisibilityType  `json:"visibility,omitempty" validate:"omitempty,oneof=private workspace public" example:"workspace"`
	BackgroundType *models.BackgroundType  `json:"background_type,omitempty" validate:"omitempty,oneof=color image gradient" example:"color"`
	Status         *models.TrelloStatus    `json:"status,omitempty" validate:"omitempty,oneof=active inactive archived deleted" example:"active"`
	IsClosed       *bool                   `json:"is_closed,omitempty" example:"false"`
	IsStarred      *bool                   `json:"is_starred,omitempty" example:"true"`
	CreatedBy      *uint                   `json:"created_by,omitempty" example:"123"`
	Search         string                  `json:"search,omitempty" example:"project"`
	DateFrom       *time.Time              `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
	DateTo         *time.Time              `json:"date_to,omitempty" example:"2024-12-31T23:59:59Z"`
	Page           int                     `json:"page" validate:"min=1" example:"1"`
	PageSize       int                     `json:"page_size" validate:"min=1,max=100" example:"10"`
	SortBy         string                  `json:"sort_by" validate:"omitempty,oneof=id name created_at updated_at" example:"name"`
	SortOrder      string                  `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"asc"`
}

// @Summary Board stats response
// @Description Board statistics information
// @Tags Board
type BoardStatsResponse struct {
	TotalBoards       int                          `json:"total_boards" example:"15"`
	ActiveBoards      int                          `json:"active_boards" example:"12"`
	ClosedBoards      int                          `json:"closed_boards" example:"3"`
	StarredBoards     int                          `json:"starred_boards" example:"5"`
	TotalLists        int                          `json:"total_lists" example:"45"`
	TotalCards        int                          `json:"total_cards" example:"234"`
	ByVisibility      map[string]int               `json:"by_visibility"`
	ByBackgroundType  map[string]int               `json:"by_background_type"`
	RecentBoards      []BoardResponse              `json:"recent_boards"`
	PopularBoards     []BoardWithStatsResponse     `json:"popular_boards"`
}

// @Summary Board with stats response
// @Description Board information with usage statistics
// @Tags Board
type BoardWithStatsResponse struct {
	BoardResponse
	ListCount   int `json:"list_count" example:"5"`
	CardCount   int `json:"card_count" example:"28"`
	MemberCount int `json:"member_count" example:"8"`
	LabelCount  int `json:"label_count" example:"12"`
}

// @Summary Star board request
// @Description Request to star/unstar a board
// @Tags Board
type StarBoardRequest struct {
	IsStarred bool `json:"is_starred" example:"true"`
}

// @Summary Close board request
// @Description Request to close/reopen a board
// @Tags Board
type CloseBoardRequest struct {
	IsClosed bool `json:"is_closed" example:"true"`
}

// @Summary Board activity response
// @Description Board activity information
// @Tags Board
type BoardActivityResponse struct {
	ID        uint           `json:"id" example:"1"`
	TenantID  uint           `json:"tenant_id" example:"1"`
	BoardID   uint           `json:"board_id" example:"1"`
	UserID    uint           `json:"user_id" example:"123"`
	Type      string         `json:"type" example:"card_created"`
	Data      datatypes.JSON `json:"data" example:"{\"card_name\":\"New Task\"}"`
	CreatedAt time.Time      `json:"created_at"`
}

// ToModelCreateRequest converts DTO to models.CreateBoardRequest
func (r *CreateBoardRequest) ToModelCreateRequest() models.CreateBoardRequest {
	return models.CreateBoardRequest{
		WorkspaceID:     r.WorkspaceID,
		Name:            r.Name,
		Description:     r.Description,
		BackgroundType:  r.BackgroundType,
		BackgroundValue: r.BackgroundValue,
		Visibility:      r.Visibility,
		Preferences:     r.Preferences,
	}
}

// ToModelUpdateRequest converts DTO to models.UpdateBoardRequest
func (r *UpdateBoardRequest) ToModelUpdateRequest() models.UpdateBoardRequest {
	return models.UpdateBoardRequest{
		Name:            r.Name,
		Description:     r.Description,
		BackgroundType:  *r.BackgroundType,
		BackgroundValue: r.BackgroundValue,
		Visibility:      *r.Visibility,
		IsClosed:        r.IsClosed,
		IsStarred:       r.IsStarred,
		Preferences:     r.Preferences,
	}
}

// ToModelAddMemberRequest converts DTO to models.AddBoardMemberRequest
func (r *AddBoardMemberRequest) ToModelAddMemberRequest() models.AddBoardMemberRequest {
	return models.AddBoardMemberRequest{
		UserID: r.UserID,
		Role:   r.Role,
	}
}

// ToModelUpdateMemberRequest converts DTO to models.UpdateBoardMemberRequest
func (r *UpdateBoardMemberRequest) ToModelUpdateMemberRequest() models.UpdateBoardMemberRequest {
	return models.UpdateBoardMemberRequest{
		Role:   r.Role,
		Status: r.Status,
	}
}