package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"gorm.io/datatypes"
)

// @Summary Create list request
// @Description Request to create a new Trello list
// @Tags List
type CreateListRequest struct {
	BoardID  uint    `json:"board_id" validate:"required" example:"1"`
	Name     string  `json:"name" validate:"required,max=255" example:"To Do"`
	Position float64 `json:"position" example:"1024.0"`
}

// @Summary Update list request
// @Description Request to update an existing Trello list
// @Tags List
type UpdateListRequest struct {
	Name         *string  `json:"name" validate:"omitempty,max=255" example:"Updated To Do"`
	Position     *float64 `json:"position,omitempty" example:"2048.0"`
	IsClosed     *bool    `json:"is_closed,omitempty" example:"false"`
	IsSubscribed *bool    `json:"is_subscribed,omitempty" example:"true"`
}

// @Summary Move list request
// @Description Request to move a list to different position or board
// @Tags List
type MoveListRequest struct {
	BoardID  *uint   `json:"board_id,omitempty" example:"2"`
	Position float64 `json:"position" validate:"required" example:"3072.0"`
}

// @Summary List response
// @Description List information in API responses
// @Tags List
type ListResponse struct {
	ID           uint                 `json:"id" example:"1"`
	TenantID     uint                 `json:"tenant_id" example:"1"`
	BoardID      uint                 `json:"board_id" example:"1"`
	Name         string               `json:"name" example:"To Do"`
	Position     float64              `json:"position" example:"1024.0"`
	IsClosed     bool                 `json:"is_closed" example:"false"`
	IsSubscribed bool                 `json:"is_subscribed" example:"true"`
	Settings     datatypes.JSON       `json:"settings" example:"{\"card_limit\":10}"`
	Status       models.TrelloStatus  `json:"status" example:"active"`
	CreatedAt    time.Time            `json:"created_at"`
	UpdatedAt    time.Time            `json:"updated_at"`
}

// @Summary List with cards response
// @Description List information with associated cards
// @Tags List
type ListWithCardsResponse struct {
	ListResponse
	Cards []CardSummaryResponse `json:"cards"`
}

// @Summary List summary response
// @Description Simplified list information for nested responses
// @Tags List
type ListSummaryResponse struct {
	ID        uint    `json:"id" example:"1"`
	Name      string  `json:"name" example:"To Do"`
	Position  float64 `json:"position" example:"1024.0"`
	IsClosed  bool    `json:"is_closed" example:"false"`
	CardCount int     `json:"card_count" example:"5"`
	UpdatedAt time.Time `json:"updated_at"`
}

// @Summary Archive list request
// @Description Request to archive/unarchive a list
// @Tags List
type ArchiveListRequest struct {
	IsClosed bool `json:"is_closed" example:"true"`
}

// @Summary Subscribe to list request
// @Description Request to subscribe/unsubscribe to list notifications
// @Tags List
type SubscribeListRequest struct {
	IsSubscribed bool `json:"is_subscribed" example:"true"`
}

// @Summary List filter
// @Description Filter parameters for listing lists
// @Tags List
type ListFilter struct {
	BoardID      *uint                `json:"board_id,omitempty" example:"1"`
	Name         string               `json:"name,omitempty" example:"To Do"`
	IsClosed     *bool                `json:"is_closed,omitempty" example:"false"`
	IsSubscribed *bool                `json:"is_subscribed,omitempty" example:"true"`
	Status       *models.TrelloStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive archived deleted" example:"active"`
	Search       string               `json:"search,omitempty" example:"todo"`
	DateFrom     *time.Time           `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
	DateTo       *time.Time           `json:"date_to,omitempty" example:"2024-12-31T23:59:59Z"`
	Page         int                  `json:"page" validate:"min=1" example:"1"`
	PageSize     int                  `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy       string               `json:"sort_by" validate:"omitempty,oneof=id name position created_at updated_at" example:"position"`
	SortOrder    string               `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"asc"`
}

// @Summary List stats response
// @Description List statistics information
// @Tags List
type ListStatsResponse struct {
	TotalLists       int                       `json:"total_lists" example:"12"`
	ActiveLists      int                       `json:"active_lists" example:"10"`
	ClosedLists      int                       `json:"closed_lists" example:"2"`
	TotalCards       int                       `json:"total_cards" example:"85"`
	AverageCardsPerList float64              `json:"average_cards_per_list" example:"7.08"`
	ListsWithoutCards   int                   `json:"lists_without_cards" example:"1"`
	RecentLists      []ListResponse            `json:"recent_lists"`
	PopularLists     []ListWithStatsResponse   `json:"popular_lists"`
}

// @Summary List with stats response
// @Description List information with usage statistics
// @Tags List
type ListWithStatsResponse struct {
	ListResponse
	CardCount        int     `json:"card_count" example:"8"`
	CompletedCards   int     `json:"completed_cards" example:"5"`
	OverdueCards     int     `json:"overdue_cards" example:"1"`
	RecentActivity   int     `json:"recent_activity" example:"3"`
	AverageCardAge   float64 `json:"average_card_age" example:"5.2"`
}

// @Summary Copy list request
// @Description Request to copy a list with options
// @Tags List
type CopyListRequest struct {
	BoardID      uint   `json:"board_id" validate:"required" example:"1"`
	Name         string `json:"name" validate:"required,max=255" example:"Copy of To Do"`
	Position     float64 `json:"position" example:"4096.0"`
	CopyCards    bool   `json:"copy_cards" example:"true"`
	KeepLabels   bool   `json:"keep_labels" example:"true"`
	KeepMembers  bool   `json:"keep_members" example:"false"`
	KeepDueDates bool   `json:"keep_due_dates" example:"true"`
}

// @Summary Move all cards request
// @Description Request to move all cards from one list to another
// @Tags List
type MoveAllCardsRequest struct {
	DestinationListID uint `json:"destination_list_id" validate:"required" example:"2"`
	Position          *float64 `json:"position,omitempty" example:"1024.0"`
}

// @Summary Archive all cards request
// @Description Request to archive all cards in a list
// @Tags List
type ArchiveAllCardsRequest struct {
	ConfirmArchive bool `json:"confirm_archive" validate:"required" example:"true"`
}

// @Summary List activity response
// @Description List activity information
// @Tags List
type ListActivityResponse struct {
	ID        uint           `json:"id" example:"1"`
	TenantID  uint           `json:"tenant_id" example:"1"`
	BoardID   uint           `json:"board_id" example:"1"`
	ListID    uint           `json:"list_id" example:"1"`
	UserID    uint           `json:"user_id" example:"123"`
	Type      string         `json:"type" example:"list_renamed"`
	Data      datatypes.JSON `json:"data" example:"{\"old_name\":\"To Do\",\"new_name\":\"Updated To Do\"}"`
	CreatedAt time.Time      `json:"created_at"`
}

// ToModelCreateRequest converts DTO to models.CreateListRequest
func (r *CreateListRequest) ToModelCreateRequest() models.CreateListRequest {
	return models.CreateListRequest{
		BoardID:  r.BoardID,
		Name:     r.Name,
		Position: r.Position,
	}
}

// ToModelUpdateRequest converts DTO to models.UpdateListRequest
func (r *UpdateListRequest) ToModelUpdateRequest() models.UpdateListRequest {
	return models.UpdateListRequest{
		Name:         r.Name,
		Position:     r.Position,
		IsClosed:     r.IsClosed,
		IsSubscribed: r.IsSubscribed,
	}
}

// ToModelMoveRequest converts DTO to models.MoveListRequest
func (r *MoveListRequest) ToModelMoveRequest() models.MoveListRequest {
	return models.MoveListRequest{
		BoardID:  r.BoardID,
		Position: r.Position,
	}
}