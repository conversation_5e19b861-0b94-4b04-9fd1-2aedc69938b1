package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"gorm.io/datatypes"
)

// @Summary Create workspace request
// @Description Request to create a new Trello workspace
// @Tags Workspace
type CreateWorkspaceRequest struct {
	Name        string                  `json:"name" validate:"required,max=255" example:"My Workspace"`
	Description *string                 `json:"description" validate:"omitempty,max=1000" example:"A workspace for team collaboration"`
	Visibility  models.VisibilityType   `json:"visibility" validate:"oneof=private workspace public" example:"workspace"`
	LogoURL     *string                 `json:"logo_url" validate:"omitempty,url" example:"https://example.com/logo.png"`
	WebsiteURL  *string                 `json:"website_url" validate:"omitempty,url" example:"https://mycompany.com"`
	Settings    datatypes.JSON          `json:"settings,omitempty" example:"{\"notifications\":true,\"auto_join\":false}"`
}

// @Summary Update workspace request
// @Description Request to update an existing Trello workspace
// @Tags Workspace
type UpdateWorkspaceRequest struct {
	Name        *string                 `json:"name" validate:"omitempty,max=255" example:"Updated Workspace"`
	Description *string                 `json:"description" validate:"omitempty,max=1000" example:"Updated description"`
	Visibility  *models.VisibilityType  `json:"visibility" validate:"omitempty,oneof=private workspace public" example:"public"`
	LogoURL     *string                 `json:"logo_url" validate:"omitempty,url" example:"https://example.com/new-logo.png"`
	WebsiteURL  *string                 `json:"website_url" validate:"omitempty,url" example:"https://newcompany.com"`
	Settings    datatypes.JSON          `json:"settings,omitempty" example:"{\"notifications\":false,\"auto_join\":true}"`
}

// @Summary Workspace response
// @Description Workspace information in API responses
// @Tags Workspace
type WorkspaceResponse struct {
	ID          uint                  `json:"id" example:"1"`
	TenantID    uint                  `json:"tenant_id" example:"1"`
	Name        string                `json:"name" example:"My Workspace"`
	Description *string               `json:"description,omitempty" example:"A workspace for team collaboration"`
	Visibility  models.VisibilityType `json:"visibility" example:"workspace"`
	LogoURL     *string               `json:"logo_url,omitempty" example:"https://example.com/logo.png"`
	WebsiteURL  *string               `json:"website_url,omitempty" example:"https://mycompany.com"`
	Settings    datatypes.JSON        `json:"settings" example:"{\"notifications\":true}"`
	CreatedBy   uint                  `json:"created_by" example:"123"`
	Status      models.TrelloStatus   `json:"status" example:"active"`
	CreatedAt   time.Time             `json:"created_at"`
	UpdatedAt   time.Time             `json:"updated_at"`
}

// @Summary Workspace with boards response
// @Description Workspace information with associated boards
// @Tags Workspace
type WorkspaceWithBoardsResponse struct {
	WorkspaceResponse
	Boards []BoardSummaryResponse `json:"boards"`
}

// @Summary Workspace with members response
// @Description Workspace information with associated members
// @Tags Workspace
type WorkspaceWithMembersResponse struct {
	WorkspaceResponse
	Members []WorkspaceMemberResponse `json:"members"`
}

// @Summary Workspace member response
// @Description Workspace member information in API responses
// @Tags Workspace
type WorkspaceMemberResponse struct {
	ID          uint                 `json:"id" example:"1"`
	TenantID    uint                 `json:"tenant_id" example:"1"`
	WorkspaceID uint                 `json:"workspace_id" example:"1"`
	UserID      uint                 `json:"user_id" example:"123"`
	Role        models.MemberRole    `json:"role" example:"member"`
	InvitedBy   uint                 `json:"invited_by" example:"456"`
	JoinedAt    time.Time            `json:"joined_at"`
	Status      models.MemberStatus  `json:"status" example:"active"`
}

// @Summary Add workspace member request
// @Description Request to add a member to workspace
// @Tags Workspace
type AddWorkspaceMemberRequest struct {
	UserID uint              `json:"user_id" validate:"required" example:"123"`
	Role   models.MemberRole `json:"role" validate:"oneof=owner admin member observer guest" example:"member"`
}

// @Summary Update workspace member request
// @Description Request to update workspace member role/status
// @Tags Workspace
type UpdateWorkspaceMemberRequest struct {
	Role   *models.MemberRole   `json:"role,omitempty" validate:"omitempty,oneof=owner admin member observer guest" example:"admin"`
	Status *models.MemberStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive pending suspended" example:"active"`
}

// @Summary Workspace list response
// @Description Response for listing workspaces
// @Tags Workspace
type WorkspaceListResponse struct {
	Workspaces []WorkspaceResponse `json:"workspaces"`
	Total      int64               `json:"total" example:"25"`
	Page       int                 `json:"page" example:"1"`
	PageSize   int                 `json:"page_size" example:"20"`
	TotalPages int                 `json:"total_pages" example:"2"`
}

// @Summary Workspace filter
// @Description Filter parameters for listing workspaces
// @Tags Workspace
type WorkspaceFilter struct {
	Name       string                  `json:"name,omitempty" example:"My Workspace"`
	Visibility *models.VisibilityType  `json:"visibility,omitempty" validate:"omitempty,oneof=private workspace public" example:"workspace"`
	Status     *models.TrelloStatus    `json:"status,omitempty" validate:"omitempty,oneof=active inactive archived deleted" example:"active"`
	CreatedBy  *uint                   `json:"created_by,omitempty" example:"123"`
	Search     string                  `json:"search,omitempty" example:"workspace"`
	DateFrom   *time.Time              `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
	DateTo     *time.Time              `json:"date_to,omitempty" example:"2024-12-31T23:59:59Z"`
	Page       int                     `json:"page" validate:"min=1" example:"1"`
	PageSize   int                     `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy     string                  `json:"sort_by" validate:"omitempty,oneof=id name created_at updated_at" example:"name"`
	SortOrder  string                  `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"asc"`
}

// @Summary Workspace stats response
// @Description Workspace statistics information
// @Tags Workspace
type WorkspaceStatsResponse struct {
	TotalWorkspaces  int                               `json:"total_workspaces" example:"10"`
	ActiveWorkspaces int                               `json:"active_workspaces" example:"8"`
	PublicWorkspaces int                               `json:"public_workspaces" example:"3"`
	TotalBoards      int                               `json:"total_boards" example:"45"`
	TotalMembers     int                               `json:"total_members" example:"150"`
	ByVisibility     map[string]int                    `json:"by_visibility"`
	RecentWorkspaces []WorkspaceResponse               `json:"recent_workspaces"`
	TopWorkspaces    []WorkspaceWithStatsResponse      `json:"top_workspaces"`
}

// @Summary Workspace with stats response
// @Description Workspace information with usage statistics
// @Tags Workspace
type WorkspaceWithStatsResponse struct {
	WorkspaceResponse
	BoardCount  int `json:"board_count" example:"5"`
	MemberCount int `json:"member_count" example:"12"`
	CardCount   int `json:"card_count" example:"78"`
}

// ToModelCreateRequest converts DTO to models.CreateWorkspaceRequest
func (r *CreateWorkspaceRequest) ToModelCreateRequest() models.CreateWorkspaceRequest {
	return models.CreateWorkspaceRequest{
		Name:        r.Name,
		Description: r.Description,
		Visibility:  r.Visibility,
		LogoURL:     r.LogoURL,
		WebsiteURL:  r.WebsiteURL,
		Settings:    r.Settings,
	}
}

// ToModelUpdateRequest converts DTO to models.UpdateWorkspaceRequest
func (r *UpdateWorkspaceRequest) ToModelUpdateRequest() models.UpdateWorkspaceRequest {
	return models.UpdateWorkspaceRequest{
		Name:        r.Name,
		Description: r.Description,
		Visibility:  *r.Visibility,
		LogoURL:     r.LogoURL,
		WebsiteURL:  r.WebsiteURL,
		Settings:    r.Settings,
	}
}

// ToModelAddMemberRequest converts DTO to models.AddWorkspaceMemberRequest
func (r *AddWorkspaceMemberRequest) ToModelAddMemberRequest() models.AddWorkspaceMemberRequest {
	return models.AddWorkspaceMemberRequest{
		UserID: r.UserID,
		Role:   r.Role,
	}
}

// ToModelUpdateMemberRequest converts DTO to models.UpdateWorkspaceMemberRequest
func (r *UpdateWorkspaceMemberRequest) ToModelUpdateMemberRequest() models.UpdateWorkspaceMemberRequest {
	return models.UpdateWorkspaceMemberRequest{
		Role:   r.Role,
		Status: r.Status,
	}
}