package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

type ListHandler struct {
	service   services.ListService
	validator validator.Validator
	logger    utils.Logger
}

func NewListHandler(service services.ListService, validator validator.Validator, logger utils.Logger) *ListHandler {
	return &ListHandler{
		service:   service,
		validator: validator,
		logger:    logger,
	}
}

func (h *ListHandler) Create(c *gin.Context) {
	var request models.CreateListRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	list, err := h.service.Create(c.Request.Context(), tenantID.(uint), userID.(uint), &request)
	if err != nil {
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to create list", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create list"})
		return
	}

	c.JSON(http.StatusCreated, list)
}

func (h *ListHandler) GetByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid list ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	list, err := h.service.GetByID(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "list not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "List not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to get list", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get list"})
		return
	}

	c.JSON(http.StatusOK, list)
}

func (h *ListHandler) Update(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid list ID"})
		return
	}

	var request models.UpdateListRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	list, err := h.service.Update(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id), &request)
	if err != nil {
		if err.Error() == "list not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "List not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to update list", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update list"})
		return
	}

	c.JSON(http.StatusOK, list)
}

func (h *ListHandler) Delete(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid list ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	err = h.service.Delete(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "list not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "List not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to delete list", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete list"})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

func (h *ListHandler) ListByBoard(c *gin.Context) {
	boardID, err := strconv.ParseUint(c.Param("boardId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid board ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	lists, err := h.service.ListByBoard(c.Request.Context(), tenantID.(uint), userID.(uint), uint(boardID))
	if err != nil {
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to list lists by board", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list lists"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": lists})
}

func (h *ListHandler) GetWithCards(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid list ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	list, err := h.service.GetWithCards(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "list not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "List not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to get list with cards", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get list with cards"})
		return
	}

	c.JSON(http.StatusOK, list)
}

func (h *ListHandler) Move(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid list ID"})
		return
	}

	var request models.MoveListRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	list, err := h.service.Move(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id), &request)
	if err != nil {
		if err.Error() == "list not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "List not found"})
			return
		}
		if err.Error() == "access denied" || err.Error() == "access denied to target board" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to move list", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to move list"})
		return
	}

	c.JSON(http.StatusOK, list)
}

func (h *ListHandler) Archive(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid list ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	err = h.service.Archive(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "list not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "List not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to archive list", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to archive list"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "List archived successfully"})
}

func (h *ListHandler) Unarchive(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid list ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	err = h.service.Unarchive(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "list not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "List not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to unarchive list", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unarchive list"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "List unarchived successfully"})
}