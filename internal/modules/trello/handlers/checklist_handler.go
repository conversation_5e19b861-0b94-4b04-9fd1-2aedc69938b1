package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

type ChecklistHandler struct {
	service   services.ChecklistService
	validator validator.Validator
	logger    utils.Logger
}

func NewChecklistHandler(service services.ChecklistService, validator validator.Validator, logger utils.Logger) *ChecklistHandler {
	return &ChecklistHandler{
		service:   service,
		validator: validator,
		logger:    logger,
	}
}

func (h *ChecklistHandler) Create(c *gin.Context) {
	var request models.CreateChecklistRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	checklist, err := h.service.Create(c.Request.Context(), tenantID.(uint), userID.(uint), &request)
	if err != nil {
		if err.Error() == "card not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Card not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to create checklist", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create checklist"})
		return
	}

	c.JSON(http.StatusCreated, checklist)
}

func (h *ChecklistHandler) GetByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid checklist ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	checklist, err := h.service.GetByID(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "checklist not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Checklist not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to get checklist", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get checklist"})
		return
	}

	c.JSON(http.StatusOK, checklist)
}

func (h *ChecklistHandler) Update(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid checklist ID"})
		return
	}

	var request models.UpdateChecklistRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	checklist, err := h.service.Update(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id), &request)
	if err != nil {
		if err.Error() == "checklist not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Checklist not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to update checklist", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update checklist"})
		return
	}

	c.JSON(http.StatusOK, checklist)
}

func (h *ChecklistHandler) Delete(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid checklist ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	err = h.service.Delete(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "checklist not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Checklist not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to delete checklist", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete checklist"})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

func (h *ChecklistHandler) ListByCard(c *gin.Context) {
	cardID, err := strconv.ParseUint(c.Param("cardId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid card ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	checklists, err := h.service.ListByCard(c.Request.Context(), tenantID.(uint), userID.(uint), uint(cardID))
	if err != nil {
		if err.Error() == "card not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Card not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to list checklists by card", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list checklists"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": checklists})
}

func (h *ChecklistHandler) CreateItem(c *gin.Context) {
	var request models.CreateChecklistItemRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	item, err := h.service.CreateItem(c.Request.Context(), tenantID.(uint), userID.(uint), &request)
	if err != nil {
		if err.Error() == "checklist not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Checklist not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to create checklist item", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create checklist item"})
		return
	}

	c.JSON(http.StatusCreated, item)
}

func (h *ChecklistHandler) UpdateItem(c *gin.Context) {
	itemID, err := strconv.ParseUint(c.Param("itemId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	var request models.UpdateChecklistItemRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	item, err := h.service.UpdateItem(c.Request.Context(), tenantID.(uint), userID.(uint), uint(itemID), &request)
	if err != nil {
		if err.Error() == "checklist item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Checklist item not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to update checklist item", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update checklist item"})
		return
	}

	c.JSON(http.StatusOK, item)
}

func (h *ChecklistHandler) DeleteItem(c *gin.Context) {
	itemID, err := strconv.ParseUint(c.Param("itemId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	err = h.service.DeleteItem(c.Request.Context(), tenantID.(uint), userID.(uint), uint(itemID))
	if err != nil {
		if err.Error() == "checklist item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Checklist item not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to delete checklist item", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete checklist item"})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

func (h *ChecklistHandler) CheckItem(c *gin.Context) {
	itemID, err := strconv.ParseUint(c.Param("itemId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	item, err := h.service.CheckItem(c.Request.Context(), tenantID.(uint), userID.(uint), uint(itemID))
	if err != nil {
		if err.Error() == "checklist item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Checklist item not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to check item", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check item"})
		return
	}

	c.JSON(http.StatusOK, item)
}

func (h *ChecklistHandler) UncheckItem(c *gin.Context) {
	itemID, err := strconv.ParseUint(c.Param("itemId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	item, err := h.service.UncheckItem(c.Request.Context(), tenantID.(uint), userID.(uint), uint(itemID))
	if err != nil {
		if err.Error() == "checklist item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Checklist item not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to uncheck item", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to uncheck item"})
		return
	}

	c.JSON(http.StatusOK, item)
}