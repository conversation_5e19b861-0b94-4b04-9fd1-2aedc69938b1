package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

type checklistService struct {
	repo      repositories.ChecklistRepository
	cardRepo  repositories.CardRepository
	boardRepo repositories.BoardRepository
	logger    utils.Logger
}

func NewChecklistService(repo repositories.ChecklistRepository, cardRepo repositories.CardRepository, boardRepo repositories.BoardRepository, logger utils.Logger) ChecklistService {
	return &checklistService{
		repo:      repo,
		cardRepo:  cardRepo,
		boardRepo: boardRepo,
		logger:    logger,
	}
}

func (s *checklistService) Create(ctx context.Context, tenantID, userID uint, request *models.CreateChecklistRequest) (*models.ChecklistResponse, error) {
	card, err := s.cardRepo.GetByID(ctx, tenantID, request.CardID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("card not found")
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	position := request.Position
	if position == 0 {
		nextPos, err := s.repo.GetNextPosition(ctx, tenantID, request.CardID, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to get next position: %w", err)
		}
		position = nextPos
	}

	checklist := &models.TrelloChecklist{
		TenantID:  tenantID,
		CardID:    request.CardID,
		Name:      request.Name,
		Position:  position,
		CreatedBy: userID,
		Status:    models.TrelloStatusActive,
	}

	if err := s.repo.Create(ctx, checklist); err != nil {
		s.logger.Error("failed to create checklist", utils.WithError(err))
		return nil, fmt.Errorf("failed to create checklist: %w", err)
	}

	return s.modelToResponse(checklist), nil
}

func (s *checklistService) GetByID(ctx context.Context, tenantID, userID, id uint) (*models.ChecklistResponse, error) {
	checklist, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("checklist not found")
		}
		return nil, fmt.Errorf("failed to get checklist: %w", err)
	}

	card, err := s.cardRepo.GetByID(ctx, tenantID, checklist.CardID)
	if err != nil {
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	return s.modelToResponse(checklist), nil
}

func (s *checklistService) Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateChecklistRequest) (*models.ChecklistResponse, error) {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("checklist not found")
		}
		return nil, fmt.Errorf("failed to get checklist: %w", err)
	}

	card, err := s.cardRepo.GetByID(ctx, tenantID, existing.CardID)
	if err != nil {
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	checklist := &models.TrelloChecklist{}
	if request.Name != nil {
		checklist.Name = *request.Name
	}
	if request.Position != nil {
		checklist.Position = *request.Position
	}

	if err := s.repo.Update(ctx, tenantID, id, checklist); err != nil {
		s.logger.Error("failed to update checklist", utils.WithError(err))
		return nil, fmt.Errorf("failed to update checklist: %w", err)
	}

	return s.GetByID(ctx, tenantID, userID, id)
}

func (s *checklistService) Delete(ctx context.Context, tenantID, userID, id uint) error {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("checklist not found")
		}
		return fmt.Errorf("failed to get checklist: %w", err)
	}

	card, err := s.cardRepo.GetByID(ctx, tenantID, existing.CardID)
	if err != nil {
		return fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.Delete(ctx, tenantID, id); err != nil {
		s.logger.Error("failed to delete checklist", utils.WithError(err))
		return fmt.Errorf("failed to delete checklist: %w", err)
	}

	return nil
}

func (s *checklistService) ListByCard(ctx context.Context, tenantID, userID, cardID uint) ([]models.ChecklistResponse, error) {
	card, err := s.cardRepo.GetByID(ctx, tenantID, cardID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("card not found")
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	checklists, err := s.repo.ListByCard(ctx, tenantID, cardID)
	if err != nil {
		s.logger.Error("failed to list checklists by card", utils.WithError(err))
		return nil, fmt.Errorf("failed to list checklists by card: %w", err)
	}

	responses := make([]models.ChecklistResponse, len(checklists))
	for i, checklist := range checklists {
		responses[i] = *s.modelToResponseWithItems(&checklist)
	}

	return responses, nil
}

func (s *checklistService) CreateItem(ctx context.Context, tenantID, userID uint, request *models.CreateChecklistItemRequest) (*models.ChecklistItemResponse, error) {
	checklist, err := s.repo.GetByID(ctx, tenantID, request.ChecklistID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("checklist not found")
		}
		return nil, fmt.Errorf("failed to get checklist: %w", err)
	}

	card, err := s.cardRepo.GetByID(ctx, tenantID, checklist.CardID)
	if err != nil {
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	position := request.Position
	if position == 0 {
		nextPos, err := s.repo.GetNextItemPosition(ctx, tenantID, request.ChecklistID, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to get next position: %w", err)
		}
		position = nextPos
	}

	item := &models.TrelloChecklistItem{
		TenantID:       tenantID,
		ChecklistID:    request.ChecklistID,
		CardID:         checklist.CardID,
		Name:           request.Name,
		Position:       position,
		DueDate:        request.DueDate,
		AssignedUserID: request.AssignedUserID,
		CreatedBy:      userID,
		Status:         models.TrelloStatusActive,
	}

	if err := s.repo.CreateItem(ctx, item); err != nil {
		s.logger.Error("failed to create checklist item", utils.WithError(err))
		return nil, fmt.Errorf("failed to create checklist item: %w", err)
	}

	return s.itemToResponse(item), nil
}

func (s *checklistService) UpdateItem(ctx context.Context, tenantID, userID, itemID uint, request *models.UpdateChecklistItemRequest) (*models.ChecklistItemResponse, error) {
	existing, err := s.repo.GetItem(ctx, tenantID, itemID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("checklist item not found")
		}
		return nil, fmt.Errorf("failed to get checklist item: %w", err)
	}

	card, err := s.cardRepo.GetByID(ctx, tenantID, existing.CardID)
	if err != nil {
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	item := &models.TrelloChecklistItem{}
	if request.Name != nil {
		item.Name = *request.Name
	}
	if request.Position != nil {
		item.Position = *request.Position
	}
	if request.IsChecked != nil {
		item.IsChecked = *request.IsChecked
	}
	if request.DueDate != nil {
		item.DueDate = request.DueDate
	}
	if request.AssignedUserID != nil {
		item.AssignedUserID = request.AssignedUserID
	}

	if err := s.repo.UpdateItem(ctx, tenantID, itemID, item); err != nil {
		s.logger.Error("failed to update checklist item", utils.WithError(err))
		return nil, fmt.Errorf("failed to update checklist item: %w", err)
	}

	updated, err := s.repo.GetItem(ctx, tenantID, itemID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated item: %w", err)
	}

	return s.itemToResponse(updated), nil
}

func (s *checklistService) DeleteItem(ctx context.Context, tenantID, userID, itemID uint) error {
	existing, err := s.repo.GetItem(ctx, tenantID, itemID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("checklist item not found")
		}
		return fmt.Errorf("failed to get checklist item: %w", err)
	}

	card, err := s.cardRepo.GetByID(ctx, tenantID, existing.CardID)
	if err != nil {
		return fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.DeleteItem(ctx, tenantID, itemID); err != nil {
		s.logger.Error("failed to delete checklist item", utils.WithError(err))
		return fmt.Errorf("failed to delete checklist item: %w", err)
	}

	return nil
}

func (s *checklistService) CheckItem(ctx context.Context, tenantID, userID, itemID uint) (*models.ChecklistItemResponse, error) {
	existing, err := s.repo.GetItem(ctx, tenantID, itemID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("checklist item not found")
		}
		return nil, fmt.Errorf("failed to get checklist item: %w", err)
	}

	card, err := s.cardRepo.GetByID(ctx, tenantID, existing.CardID)
	if err != nil {
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	if err := s.repo.CheckItem(ctx, tenantID, itemID, userID); err != nil {
		s.logger.Error("failed to check item", utils.WithError(err))
		return nil, fmt.Errorf("failed to check item: %w", err)
	}

	updated, err := s.repo.GetItem(ctx, tenantID, itemID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated item: %w", err)
	}

	return s.itemToResponse(updated), nil
}

func (s *checklistService) UncheckItem(ctx context.Context, tenantID, userID, itemID uint) (*models.ChecklistItemResponse, error) {
	existing, err := s.repo.GetItem(ctx, tenantID, itemID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("checklist item not found")
		}
		return nil, fmt.Errorf("failed to get checklist item: %w", err)
	}

	card, err := s.cardRepo.GetByID(ctx, tenantID, existing.CardID)
	if err != nil {
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	if err := s.repo.UncheckItem(ctx, tenantID, itemID); err != nil {
		s.logger.Error("failed to uncheck item", utils.WithError(err))
		return nil, fmt.Errorf("failed to uncheck item: %w", err)
	}

	updated, err := s.repo.GetItem(ctx, tenantID, itemID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated item: %w", err)
	}

	return s.itemToResponse(updated), nil
}

func (s *checklistService) checkBoardAccess(ctx context.Context, tenantID, boardID, userID uint) bool {
	isMember, err := s.boardRepo.IsMember(ctx, tenantID, boardID, userID)
	if err != nil {
		s.logger.Error("failed to check board access", utils.WithError(err))
		return false
	}
	return isMember
}

func (s *checklistService) modelToResponse(checklist *models.TrelloChecklist) *models.ChecklistResponse {
	return &models.ChecklistResponse{
		ID:                    checklist.ID,
		TenantID:              checklist.TenantID,
		CardID:                checklist.CardID,
		Name:                  checklist.Name,
		Position:              checklist.Position,
		CheckItemsCount:       checklist.CheckItemsCount,
		CheckItemsCheckedCount: checklist.CheckItemsCheckedCount,
		CreatedBy:             checklist.CreatedBy,
		Status:                checklist.Status,
		CreatedAt:             checklist.CreatedAt,
		UpdatedAt:             checklist.UpdatedAt,
		Items:                 make([]models.ChecklistItemResponse, 0),
	}
}

func (s *checklistService) modelToResponseWithItems(checklist *models.TrelloChecklist) *models.ChecklistResponse {
	response := s.modelToResponse(checklist)
	response.Items = make([]models.ChecklistItemResponse, len(checklist.Items))
	
	for i, item := range checklist.Items {
		response.Items[i] = *s.itemToResponse(&item)
	}
	
	return response
}

func (s *checklistService) itemToResponse(item *models.TrelloChecklistItem) *models.ChecklistItemResponse {
	return &models.ChecklistItemResponse{
		ID:             item.ID,
		TenantID:       item.TenantID,
		ChecklistID:    item.ChecklistID,
		CardID:         item.CardID,
		Name:           item.Name,
		Position:       item.Position,
		IsChecked:      item.IsChecked,
		DueDate:        item.DueDate,
		AssignedUserID: item.AssignedUserID,
		CreatedBy:      item.CreatedBy,
		CheckedBy:      item.CheckedBy,
		CheckedAt:      item.CheckedAt,
		Status:         item.Status,
		CreatedAt:      item.CreatedAt,
		UpdatedAt:      item.UpdatedAt,
	}
}