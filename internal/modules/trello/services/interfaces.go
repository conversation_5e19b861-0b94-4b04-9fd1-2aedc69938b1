package services

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// WorkspaceService defines the interface for workspace operations
type WorkspaceService interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenantID, userID uint, request *models.CreateWorkspaceRequest) (*models.WorkspaceResponse, error)
	GetByID(ctx context.Context, tenantID, userID, id uint) (*models.WorkspaceResponse, error)
	Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateWorkspaceRequest) (*models.WorkspaceResponse, error)
	Delete(ctx context.Context, tenantID, userID, id uint) error
	
	// List operations
	List(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.WorkspaceResponse, *pagination.CursorResponse, error)
	GetUserWorkspaces(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.WorkspaceResponse, *pagination.CursorResponse, error)
	
	// Workspace details
	GetWithBoards(ctx context.Context, tenantID, userID, id uint) (*models.WorkspaceWithBoardsResponse, error)
	GetWithMembers(ctx context.Context, tenantID, userID, id uint) (*models.WorkspaceWithMembersResponse, error)
	
	// Member management
	AddMember(ctx context.Context, tenantID, userID, workspaceID uint, request *models.AddWorkspaceMemberRequest) (*models.WorkspaceMemberResponse, error)
	RemoveMember(ctx context.Context, tenantID, userID, workspaceID, memberUserID uint) error
	UpdateMember(ctx context.Context, tenantID, userID, workspaceID, memberUserID uint, request *models.UpdateWorkspaceMemberRequest) (*models.WorkspaceMemberResponse, error)
	ListMembers(ctx context.Context, tenantID, userID, workspaceID uint) ([]models.WorkspaceMemberResponse, error)
}

// BoardService defines the interface for board operations
type BoardService interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenantID, userID uint, request *models.CreateBoardRequest) (*models.BoardResponse, error)
	GetByID(ctx context.Context, tenantID, userID, id uint) (*models.BoardResponse, error)
	Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateBoardRequest) (*models.BoardResponse, error)
	Delete(ctx context.Context, tenantID, userID, id uint) error
	
	// List operations
	List(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.BoardResponse, *pagination.CursorResponse, error)
	ListByWorkspace(ctx context.Context, tenantID, userID, workspaceID uint, pagination *pagination.CursorPagination) ([]models.BoardResponse, *pagination.CursorResponse, error)
	GetUserBoards(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.BoardResponse, *pagination.CursorResponse, error)
	GetStarredBoards(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.BoardResponse, *pagination.CursorResponse, error)
	
	// Board details
	GetWithLists(ctx context.Context, tenantID, userID, id uint) (*models.BoardWithListsResponse, error)
	
	// Member management
	AddMember(ctx context.Context, tenantID, userID, boardID uint, request *models.AddBoardMemberRequest) (*models.BoardMemberResponse, error)
	RemoveMember(ctx context.Context, tenantID, userID, boardID, memberUserID uint) error
	UpdateMember(ctx context.Context, tenantID, userID, boardID, memberUserID uint, request *models.UpdateBoardMemberRequest) (*models.BoardMemberResponse, error)
	ListMembers(ctx context.Context, tenantID, userID, boardID uint) ([]models.BoardMemberResponse, error)
	
	// Label management
	CreateLabel(ctx context.Context, tenantID, userID uint, request *models.CreateLabelRequest) (*models.LabelResponse, error)
	UpdateLabel(ctx context.Context, tenantID, userID, labelID uint, request *models.UpdateLabelRequest) (*models.LabelResponse, error)
	DeleteLabel(ctx context.Context, tenantID, userID, labelID uint) error
	ListLabels(ctx context.Context, tenantID, userID, boardID uint) ([]models.LabelResponse, error)
}

// ListService defines the interface for list operations
type ListService interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenantID, userID uint, request *models.CreateListRequest) (*models.ListResponse, error)
	GetByID(ctx context.Context, tenantID, userID, id uint) (*models.ListResponse, error)
	Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateListRequest) (*models.ListResponse, error)
	Delete(ctx context.Context, tenantID, userID, id uint) error
	
	// List operations
	ListByBoard(ctx context.Context, tenantID, userID, boardID uint) ([]models.ListResponse, error)
	GetWithCards(ctx context.Context, tenantID, userID, id uint) (*models.ListWithCardsResponse, error)
	
	// Move operations
	Move(ctx context.Context, tenantID, userID, id uint, request *models.MoveListRequest) (*models.ListResponse, error)
	
	// Archive operations
	Archive(ctx context.Context, tenantID, userID, id uint) error
	Unarchive(ctx context.Context, tenantID, userID, id uint) error
}

// CardService defines the interface for card operations
type CardService interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenantID, userID uint, request *models.CreateCardRequest) (*models.CardResponse, error)
	GetByID(ctx context.Context, tenantID, userID, id uint) (*models.CardResponse, error)
	Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateCardRequest) (*models.CardResponse, error)
	Delete(ctx context.Context, tenantID, userID, id uint) error
	
	// List operations
	ListByList(ctx context.Context, tenantID, userID, listID uint, pagination *pagination.CursorPagination) ([]models.CardResponse, *pagination.CursorResponse, error)
	ListByBoard(ctx context.Context, tenantID, userID, boardID uint, pagination *pagination.CursorPagination) ([]models.CardResponse, *pagination.CursorResponse, error)
	
	// Card details
	GetDetail(ctx context.Context, tenantID, userID, id uint) (*models.CardDetailResponse, error)
	
	// Move operations
	Move(ctx context.Context, tenantID, userID, id uint, request *models.MoveCardRequest) (*models.CardResponse, error)
	
	// Member operations
	AssignMember(ctx context.Context, tenantID, userID, cardID uint, request *models.AssignCardMemberRequest) (*models.CardMemberResponse, error)
	UnassignMember(ctx context.Context, tenantID, userID, cardID, memberUserID uint) error
	
	// Label operations
	ApplyLabel(ctx context.Context, tenantID, userID, cardID uint, request *models.ApplyCardLabelRequest) (*models.CardLabelResponse, error)
	RemoveLabel(ctx context.Context, tenantID, userID, cardID, labelID uint) error
	
	// Comment operations
	CreateComment(ctx context.Context, tenantID, userID uint, request *models.CreateCommentRequest) (*models.CardCommentResponse, error)
	
	// Archive operations
	Archive(ctx context.Context, tenantID, userID, id uint) error
	Unarchive(ctx context.Context, tenantID, userID, id uint) error
	
	// Search operations
	Search(ctx context.Context, tenantID, userID, boardID uint, query string, pagination *pagination.CursorPagination) ([]models.CardResponse, *pagination.CursorResponse, error)
}

// ChecklistService defines the interface for checklist operations
type ChecklistService interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenantID, userID uint, request *models.CreateChecklistRequest) (*models.ChecklistResponse, error)
	GetByID(ctx context.Context, tenantID, userID, id uint) (*models.ChecklistResponse, error)
	Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateChecklistRequest) (*models.ChecklistResponse, error)
	Delete(ctx context.Context, tenantID, userID, id uint) error
	
	// List operations
	ListByCard(ctx context.Context, tenantID, userID, cardID uint) ([]models.ChecklistResponse, error)
	
	// Item operations
	CreateItem(ctx context.Context, tenantID, userID uint, request *models.CreateChecklistItemRequest) (*models.ChecklistItemResponse, error)
	UpdateItem(ctx context.Context, tenantID, userID, itemID uint, request *models.UpdateChecklistItemRequest) (*models.ChecklistItemResponse, error)
	DeleteItem(ctx context.Context, tenantID, userID, itemID uint) error
	CheckItem(ctx context.Context, tenantID, userID, itemID uint) (*models.ChecklistItemResponse, error)
	UncheckItem(ctx context.Context, tenantID, userID, itemID uint) (*models.ChecklistItemResponse, error)
}