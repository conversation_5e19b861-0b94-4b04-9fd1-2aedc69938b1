package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

type listService struct {
	repo      repositories.ListRepository
	boardRepo repositories.BoardRepository
	logger    utils.Logger
}

func NewListService(repo repositories.ListRepository, boardRepo repositories.BoardRepository, logger utils.Logger) ListService {
	return &listService{
		repo:      repo,
		boardRepo: boardRepo,
		logger:    logger,
	}
}

func (s *listService) Create(ctx context.Context, tenantID, userID uint, request *models.CreateListRequest) (*models.ListResponse, error) {
	if !s.checkBoardAccess(ctx, tenantID, request.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	position := request.Position
	if position == 0 {
		nextPos, err := s.repo.GetNextPosition(ctx, tenantID, request.BoardID, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to get next position: %w", err)
		}
		position = nextPos
	}

	list := &models.TrelloList{
		TenantID: tenantID,
		BoardID:  request.BoardID,
		Name:     request.Name,
		Position: position,
		Status:   models.TrelloStatusActive,
	}

	if err := s.repo.Create(ctx, list); err != nil {
		s.logger.Error("failed to create list", utils.WithError(err))
		return nil, fmt.Errorf("failed to create list: %w", err)
	}

	return s.modelToResponse(list), nil
}

func (s *listService) GetByID(ctx context.Context, tenantID, userID, id uint) (*models.ListResponse, error) {
	list, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("list not found")
		}
		return nil, fmt.Errorf("failed to get list: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, list.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	return s.modelToResponse(list), nil
}

func (s *listService) Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateListRequest) (*models.ListResponse, error) {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("list not found")
		}
		return nil, fmt.Errorf("failed to get list: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	list := &models.TrelloList{}
	if request.Name != nil {
		list.Name = *request.Name
	}
	if request.Position != nil {
		list.Position = *request.Position
	}
	if request.IsClosed != nil {
		list.IsClosed = *request.IsClosed
	}
	if request.IsSubscribed != nil {
		list.IsSubscribed = *request.IsSubscribed
	}

	if err := s.repo.Update(ctx, tenantID, id, list); err != nil {
		s.logger.Error("failed to update list", utils.WithError(err))
		return nil, fmt.Errorf("failed to update list: %w", err)
	}

	return s.GetByID(ctx, tenantID, userID, id)
}

func (s *listService) Delete(ctx context.Context, tenantID, userID, id uint) error {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("list not found")
		}
		return fmt.Errorf("failed to get list: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.Delete(ctx, tenantID, id); err != nil {
		s.logger.Error("failed to delete list", utils.WithError(err))
		return fmt.Errorf("failed to delete list: %w", err)
	}

	return nil
}

func (s *listService) ListByBoard(ctx context.Context, tenantID, userID, boardID uint) ([]models.ListResponse, error) {
	if !s.checkBoardAccess(ctx, tenantID, boardID, userID) {
		return nil, errors.New("access denied")
	}

	lists, err := s.repo.ListByBoard(ctx, tenantID, boardID)
	if err != nil {
		s.logger.Error("failed to list lists by board", utils.WithError(err))
		return nil, fmt.Errorf("failed to list lists by board: %w", err)
	}

	responses := make([]models.ListResponse, len(lists))
	for i, list := range lists {
		responses[i] = *s.modelToResponse(&list)
	}

	return responses, nil
}

func (s *listService) GetWithCards(ctx context.Context, tenantID, userID, id uint) (*models.ListWithCardsResponse, error) {
	list, err := s.repo.GetWithCards(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("list not found")
		}
		return nil, fmt.Errorf("failed to get list with cards: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, list.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	response := &models.ListWithCardsResponse{
		ListResponse: *s.modelToResponse(list),
		Cards:        make([]models.CardSummaryResponse, len(list.Cards)),
	}

	for i, card := range list.Cards {
		response.Cards[i] = models.CardSummaryResponse{
			ID:          card.ID,
			Title:       card.Title,
			Position:    card.Position,
			DueDate:     card.DueDate,
			DueComplete: card.DueComplete,
			IsClosed:    card.IsClosed,
			CoverType:   card.CoverType,
			CoverValue:  card.CoverValue,
			CoverColor:  card.CoverColor,
			UpdatedAt:   card.UpdatedAt,
		}
	}

	return response, nil
}

func (s *listService) Move(ctx context.Context, tenantID, userID, id uint, request *models.MoveListRequest) (*models.ListResponse, error) {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("list not found")
		}
		return nil, fmt.Errorf("failed to get list: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	if request.BoardID != nil && !s.checkBoardAccess(ctx, tenantID, *request.BoardID, userID) {
		return nil, errors.New("access denied to target board")
	}

	if err := s.repo.Move(ctx, tenantID, id, request.BoardID, request.Position); err != nil {
		s.logger.Error("failed to move list", utils.WithError(err))
		return nil, fmt.Errorf("failed to move list: %w", err)
	}

	return s.GetByID(ctx, tenantID, userID, id)
}

func (s *listService) Archive(ctx context.Context, tenantID, userID, id uint) error {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("list not found")
		}
		return fmt.Errorf("failed to get list: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.Archive(ctx, tenantID, id); err != nil {
		s.logger.Error("failed to archive list", utils.WithError(err))
		return fmt.Errorf("failed to archive list: %w", err)
	}

	return nil
}

func (s *listService) Unarchive(ctx context.Context, tenantID, userID, id uint) error {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("list not found")
		}
		return fmt.Errorf("failed to get list: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.Unarchive(ctx, tenantID, id); err != nil {
		s.logger.Error("failed to unarchive list", utils.WithError(err))
		return fmt.Errorf("failed to unarchive list: %w", err)
	}

	return nil
}

func (s *listService) checkBoardAccess(ctx context.Context, tenantID, boardID, userID uint) bool {
	isMember, err := s.boardRepo.IsMember(ctx, tenantID, boardID, userID)
	if err != nil {
		s.logger.Error("failed to check board access", utils.WithError(err))
		return false
	}
	return isMember
}

func (s *listService) modelToResponse(list *models.TrelloList) *models.ListResponse {
	return &models.ListResponse{
		ID:           list.ID,
		TenantID:     list.TenantID,
		BoardID:      list.BoardID,
		Name:         list.Name,
		Position:     list.Position,
		IsClosed:     list.IsClosed,
		IsSubscribed: list.IsSubscribed,
		Settings:     list.Settings,
		Status:       list.Status,
		CreatedAt:    list.CreatedAt,
		UpdatedAt:    list.UpdatedAt,
	}
}