package dto

// APIKeyValidationRequest represents the request to validate an API key
type APIKeyValidationRequest struct {
	Key      string `json:"key" validate:"required" example:"ak_1234567890abcdef"`
	Resource string `json:"resource,omitempty" example:"users"`
	Action   string `json:"action,omitempty" example:"read"`
}

// APIKeyValidationResponse represents the response for API key validation
type APIKeyValidationResponse struct {
	Valid       bool                   `json:"valid" example:"true"`
	APIKey      *APIKeyResponse        `json:"api_key,omitempty"`
	Permissions map[string]interface{} `json:"permissions,omitempty"`
	Scopes      []string               `json:"scopes,omitempty"`
	RateLimit   *RateLimitInfo         `json:"rate_limit,omitempty"`
	Error       string                 `json:"error,omitempty"`
}

// RateLimitInfo represents rate limiting information
type RateLimitInfo struct {
	Limit      int   `json:"limit" example:"1000"`
	Remaining  int   `json:"remaining" example:"950"`
	Reset      int64 `json:"reset" example:"1640995200"`
	RetryAfter int   `json:"retry_after,omitempty" example:"3600"`
}