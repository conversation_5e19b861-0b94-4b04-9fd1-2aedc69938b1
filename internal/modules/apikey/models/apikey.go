package models

import (
	"time"
	"encoding/json"
	"gorm.io/datatypes"
)

// APIKeyStatus represents the status of an API key
// @Enum active,inactive,expired,revoked,deleted
type APIKeyStatus string

const (
	APIKeyStatusActive   APIKeyStatus = "active"
	APIKeyStatusInactive APIKeyStatus = "inactive"
	APIKeyStatusExpired  APIKeyStatus = "expired"
	APIKeyStatusRevoked  APIKeyStatus = "revoked"
	APIKeyStatusDeleted  APIKeyStatus = "deleted"
)

// APIKey represents an API key in the system
type APIKey struct {
	ID          uint                   `gorm:"primaryKey" json:"id"`
	TenantID    uint                   `gorm:"not null;index:idx_api_keys_tenant_id" json:"tenant_id"`
	WebsiteID   uint                   `gorm:"not null;index:idx_api_keys_website_id" json:"website_id"`
	KeyHash     string                 `gorm:"size:64;not null;unique" json:"-"`
	KeyPrefix   string                 `gorm:"size:12;not null;index:idx_api_keys_key_prefix" json:"key_prefix"`
	Name        string                 `gorm:"size:100;not null" json:"name"`
	Description string                 `gorm:"type:text" json:"description"`
	Status      APIKeyStatus           `gorm:"type:enum('active','inactive','expired','revoked','deleted');not null;default:'active';index:idx_api_keys_status" json:"status"`
	Permissions datatypes.JSON         `gorm:"type:json;default:'{}'" json:"permissions"`
	Scopes      datatypes.JSON         `gorm:"type:json;default:'[]'" json:"scopes"`
	IPWhitelist datatypes.JSON         `gorm:"type:json;default:'[]'" json:"ip_whitelist"`
	RateLimit   int                    `gorm:"not null;default:1000" json:"rate_limit"`
	RateWindow  int                    `gorm:"not null;default:3600" json:"rate_window"`
	ExpiresAt   *time.Time             `gorm:"index:idx_api_keys_expires_at" json:"expires_at"`
	LastUsedAt  *time.Time             `gorm:"index:idx_api_keys_last_used_at" json:"last_used_at"`
	CreatedBy   uint                   `gorm:"not null;index:idx_api_keys_created_by" json:"created_by"`
	CreatedAt   time.Time              `gorm:"index:idx_api_keys_created_at" json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`

	// Relationships
	PermissionsDetail []APIKeyPermission `gorm:"foreignKey:APIKeyID" json:"permissions_detail,omitempty"`
	Usage             []APIKeyUsage      `gorm:"foreignKey:APIKeyID" json:"usage,omitempty"`
	Rotations         []APIKeyRotation   `gorm:"foreignKey:APIKeyID" json:"rotations,omitempty"`
	ScopesDetail      []APIKeyScope      `gorm:"foreignKey:APIKeyID" json:"scopes_detail,omitempty"`
}

// TableName returns the table name for APIKey
func (APIKey) TableName() string {
	return "api_keys"
}

// IsActive checks if the API key is active
func (a *APIKey) IsActive() bool {
	return a.Status == APIKeyStatusActive
}

// IsExpired checks if the API key is expired
func (a *APIKey) IsExpired() bool {
	if a.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*a.ExpiresAt)
}

// GetPermissionsList returns permissions as a map
func (a *APIKey) GetPermissionsList() (map[string]interface{}, error) {
	var permissions map[string]interface{}
	if err := json.Unmarshal(a.Permissions, &permissions); err != nil {
		return nil, err
	}
	return permissions, nil
}

// GetScopesList returns scopes as a string slice
func (a *APIKey) GetScopesList() ([]string, error) {
	var scopes []string
	if err := json.Unmarshal(a.Scopes, &scopes); err != nil {
		return nil, err
	}
	return scopes, nil
}

// GetIPWhitelistList returns IP whitelist as a string slice
func (a *APIKey) GetIPWhitelistList() ([]string, error) {
	var ipList []string
	if err := json.Unmarshal(a.IPWhitelist, &ipList); err != nil {
		return nil, err
	}
	return ipList, nil
}

// SetPermissions sets the permissions JSON field
func (a *APIKey) SetPermissions(permissions map[string]interface{}) error {
	data, err := json.Marshal(permissions)
	if err != nil {
		return err
	}
	a.Permissions = data
	return nil
}

// SetScopes sets the scopes JSON field
func (a *APIKey) SetScopes(scopes []string) error {
	data, err := json.Marshal(scopes)
	if err != nil {
		return err
	}
	a.Scopes = data
	return nil
}

// SetIPWhitelist sets the IP whitelist JSON field
func (a *APIKey) SetIPWhitelist(ipList []string) error {
	data, err := json.Marshal(ipList)
	if err != nil {
		return err
	}
	a.IPWhitelist = data
	return nil
}

// Activate activates the API key
func (a *APIKey) Activate() {
	a.Status = APIKeyStatusActive
}

// Deactivate deactivates the API key
func (a *APIKey) Deactivate() {
	a.Status = APIKeyStatusInactive
}

// Revoke revokes the API key
func (a *APIKey) Revoke() {
	a.Status = APIKeyStatusRevoked
}

// Delete soft deletes the API key
func (a *APIKey) Delete() {
	a.Status = APIKeyStatusDeleted
}

// Permission represents a permission structure
type Permission struct {
	Resource   string                 `json:"resource"`
	Actions    []string               `json:"actions"`
	Conditions map[string]interface{} `json:"conditions,omitempty"`
}

// APIKeyPermission represents detailed permissions for an API key
type APIKeyPermission struct {
	ID         uint                   `gorm:"primaryKey" json:"id"`
	TenantID   uint                   `gorm:"not null;index:idx_api_key_permissions_tenant_id" json:"tenant_id"`
	APIKeyID   uint                   `gorm:"not null;index:idx_api_key_permissions_api_key_id" json:"api_key_id"`
	Resource   string                 `gorm:"size:100;not null;index:idx_api_key_permissions_resource" json:"resource"`
	Action     string                 `gorm:"size:50;not null;index:idx_api_key_permissions_action" json:"action"`
	Conditions datatypes.JSON         `gorm:"type:json;default:'{}'" json:"conditions"`
	CreatedAt  time.Time              `gorm:"index:idx_api_key_permissions_created_at" json:"created_at"`

	// Relationships
	APIKey APIKey `gorm:"foreignKey:APIKeyID" json:"api_key,omitempty"`
}

// TableName returns the table name for APIKeyPermission
func (APIKeyPermission) TableName() string {
	return "api_key_permissions"
}

// GetConditions returns conditions as a map
func (p *APIKeyPermission) GetConditions() (map[string]interface{}, error) {
	var conditions map[string]interface{}
	if err := json.Unmarshal(p.Conditions, &conditions); err != nil {
		return nil, err
	}
	return conditions, nil
}

// HTTPMethod represents HTTP methods for API key usage
// @Enum GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS
type HTTPMethod string

const (
	HTTPMethodGET     HTTPMethod = "GET"
	HTTPMethodPOST    HTTPMethod = "POST"
	HTTPMethodPUT     HTTPMethod = "PUT"
	HTTPMethodPATCH   HTTPMethod = "PATCH"
	HTTPMethodDELETE  HTTPMethod = "DELETE"
	HTTPMethodHEAD    HTTPMethod = "HEAD"
	HTTPMethodOPTIONS HTTPMethod = "OPTIONS"
)

// APIKeyUsage represents usage tracking for an API key
type APIKeyUsage struct {
	ID           uint                   `gorm:"primaryKey" json:"id"`
	TenantID     uint                   `gorm:"not null;index:idx_api_key_usage_tenant_id" json:"tenant_id"`
	WebsiteID    uint                   `gorm:"not null;index:idx_api_key_usage_website_id" json:"website_id"`
	APIKeyID     uint                   `gorm:"not null;index:idx_api_key_usage_api_key_id" json:"api_key_id"`
	Endpoint     string                 `gorm:"size:255;not null;index:idx_api_key_usage_endpoint" json:"endpoint"`
	Method       HTTPMethod             `gorm:"type:enum('GET','POST','PUT','PATCH','DELETE','HEAD','OPTIONS');not null;index:idx_api_key_usage_method" json:"method"`
	ResponseCode int                    `gorm:"not null;index:idx_api_key_usage_response_code" json:"response_code"`
	ResponseTime int                    `gorm:"not null" json:"response_time"`
	RequestSize  int                    `gorm:"default:0" json:"request_size"`
	ResponseSize int                    `gorm:"default:0" json:"response_size"`
	IPAddress    string                 `gorm:"size:45;not null;index:idx_api_key_usage_ip_address" json:"ip_address"`
	UserAgent    string                 `gorm:"type:text" json:"user_agent"`
	Referer      string                 `gorm:"size:500" json:"referer"`
	RequestID    string                 `gorm:"size:36;index:idx_api_key_usage_request_id" json:"request_id"`
	ErrorMessage string                 `gorm:"type:text" json:"error_message"`
	Metadata     datatypes.JSON         `gorm:"type:json;default:'{}'" json:"metadata"`
	CreatedAt    time.Time              `gorm:"index:idx_api_key_usage_created_at" json:"created_at"`

	// Relationships
	APIKey APIKey `gorm:"foreignKey:APIKeyID" json:"api_key,omitempty"`
}

// TableName returns the table name for APIKeyUsage
func (APIKeyUsage) TableName() string {
	return "api_key_usage"
}

// IsSuccessful checks if the request was successful
func (u *APIKeyUsage) IsSuccessful() bool {
	return u.ResponseCode >= 200 && u.ResponseCode < 300
}

// IsError checks if the request resulted in an error
func (u *APIKeyUsage) IsError() bool {
	return u.ResponseCode >= 400
}

// RotationType represents the type of key rotation
// @Enum manual,automatic,emergency,scheduled
type RotationType string

const (
	RotationTypeManual    RotationType = "manual"
	RotationTypeAutomatic RotationType = "automatic"
	RotationTypeEmergency RotationType = "emergency"
	RotationTypeScheduled RotationType = "scheduled"
)

// RotationStatus represents the status of key rotation
// @Enum initiated,in_progress,completed,failed,cancelled
type RotationStatus string

const (
	RotationStatusInitiated  RotationStatus = "initiated"
	RotationStatusInProgress RotationStatus = "in_progress"
	RotationStatusCompleted  RotationStatus = "completed"
	RotationStatusFailed     RotationStatus = "failed"
	RotationStatusCancelled  RotationStatus = "cancelled"
)

// APIKeyRotation represents key rotation history
type APIKeyRotation struct {
	ID                     uint                   `gorm:"primaryKey" json:"id"`
	TenantID               uint                   `gorm:"not null;index:idx_api_key_rotation_tenant_id" json:"tenant_id"`
	WebsiteID              uint                   `gorm:"not null;index:idx_api_key_rotation_website_id" json:"website_id"`
	APIKeyID               uint                   `gorm:"not null;index:idx_api_key_rotation_api_key_id" json:"api_key_id"`
	OldKeyHash             string                 `gorm:"size:64;not null;index:idx_api_key_rotation_old_key_hash" json:"old_key_hash"`
	NewKeyHash             string                 `gorm:"size:64;not null;index:idx_api_key_rotation_new_key_hash" json:"new_key_hash"`
	RotationType           RotationType           `gorm:"type:enum('manual','automatic','emergency','scheduled');not null;default:'manual';index:idx_api_key_rotation_type" json:"rotation_type"`
	Reason                 string                 `gorm:"size:255" json:"reason"`
	GracePeriodHours       int                    `gorm:"default:24" json:"grace_period_hours"`
	GracePeriodExpiresAt   *time.Time             `gorm:"index:idx_api_key_rotation_grace_expires" json:"grace_period_expires_at"`
	Status                 RotationStatus         `gorm:"type:enum('initiated','in_progress','completed','failed','cancelled');not null;default:'initiated';index:idx_api_key_rotation_status" json:"status"`
	RotatedBy              *uint                  `gorm:"index:idx_api_key_rotation_rotated_by" json:"rotated_by"`
	RotatedAt              time.Time              `gorm:"index:idx_api_key_rotation_rotated_at" json:"rotated_at"`
	CompletedAt            *time.Time             `json:"completed_at"`
	Metadata               datatypes.JSON         `gorm:"type:json;default:'{}'" json:"metadata"`

	// Relationships
	APIKey APIKey `gorm:"foreignKey:APIKeyID" json:"api_key,omitempty"`
}

// TableName returns the table name for APIKeyRotation
func (APIKeyRotation) TableName() string {
	return "api_key_rotation"
}

// IsCompleted checks if the rotation is completed
func (r *APIKeyRotation) IsCompleted() bool {
	return r.Status == RotationStatusCompleted
}

// IsInProgress checks if the rotation is in progress
func (r *APIKeyRotation) IsInProgress() bool {
	return r.Status == RotationStatusInProgress
}

// Complete marks the rotation as completed
func (r *APIKeyRotation) Complete() {
	r.Status = RotationStatusCompleted
	now := time.Now()
	r.CompletedAt = &now
}

// Fail marks the rotation as failed
func (r *APIKeyRotation) Fail() {
	r.Status = RotationStatusFailed
}

// Cancel marks the rotation as cancelled
func (r *APIKeyRotation) Cancel() {
	r.Status = RotationStatusCancelled
}

// APIKeyScope represents fine-grained scope control
type APIKeyScope struct {
	ID        uint                   `gorm:"primaryKey" json:"id"`
	TenantID  uint                   `gorm:"not null;index:idx_api_key_scopes_tenant_id" json:"tenant_id"`
	WebsiteID uint                   `gorm:"not null;index:idx_api_key_scopes_website_id" json:"website_id"`
	APIKeyID  uint                   `gorm:"not null;index:idx_api_key_scopes_api_key_id" json:"api_key_id"`
	Scope     string                 `gorm:"size:100;not null;index:idx_api_key_scopes_scope" json:"scope"`
	Resource  string                 `gorm:"size:100;not null;index:idx_api_key_scopes_resource" json:"resource"`
	Actions   datatypes.JSON         `gorm:"type:json;not null" json:"actions"`
	Conditions datatypes.JSON        `gorm:"type:json;default:'{}'" json:"conditions"`
	IsActive  bool                   `gorm:"not null;default:true;index:idx_api_key_scopes_is_active" json:"is_active"`
	ExpiresAt *time.Time             `gorm:"index:idx_api_key_scopes_expires_at" json:"expires_at"`
	CreatedAt time.Time              `gorm:"index:idx_api_key_scopes_created_at" json:"created_at"`
	UpdatedAt time.Time              `gorm:"index:idx_api_key_scopes_updated_at" json:"updated_at"`

	// Relationships
	APIKey APIKey `gorm:"foreignKey:APIKeyID" json:"api_key,omitempty"`
}

// TableName returns the table name for APIKeyScope
func (APIKeyScope) TableName() string {
	return "api_key_scopes"
}

// GetActions returns actions as a string slice
func (s *APIKeyScope) GetActions() ([]string, error) {
	var actions []string
	if err := json.Unmarshal(s.Actions, &actions); err != nil {
		return nil, err
	}
	return actions, nil
}

// GetConditions returns conditions as a map
func (s *APIKeyScope) GetConditions() (map[string]interface{}, error) {
	var conditions map[string]interface{}
	if err := json.Unmarshal(s.Conditions, &conditions); err != nil {
		return nil, err
	}
	return conditions, nil
}

// IsExpired checks if the scope is expired
func (s *APIKeyScope) IsExpired() bool {
	if s.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*s.ExpiresAt)
}

// Activate activates the scope
func (s *APIKeyScope) Activate() {
	s.IsActive = true
}

// Deactivate deactivates the scope
func (s *APIKeyScope) Deactivate() {
	s.IsActive = false
}