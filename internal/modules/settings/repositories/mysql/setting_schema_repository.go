package mysql

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/repositories"
)

type settingSchemaRepository struct {
	db      *gorm.DB
	options *repositories.SettingSchemaRepositoryOptions
}

// NewSettingSchemaRepository creates a new MySQL setting schema repository
func NewSettingSchemaRepository(db *gorm.DB, options *repositories.SettingSchemaRepositoryOptions) repositories.SettingSchemaRepository {
	if options == nil {
		options = &repositories.SettingSchemaRepositoryOptions{
			EnableCache:      false,
			CacheTimeout:     300,
			CachePrefix:      "setting_schema:",
			EnableVersioning: false,
			MaxVersions:      10,
			ValidateOnCreate: true,
			ValidateOnUpdate: true,
		}
	}
	return &settingSchemaRepository{
		db:      db,
		options: options,
	}
}

// Create creates a new setting schema
func (r *settingSchemaRepository) Create(ctx context.Context, schema *models.SettingSchema) error {
	if r.options.ValidateOnCreate {
		if validationErrors, err := r.ValidateSchema(ctx, schema); err != nil {
			return fmt.Errorf("schema validation failed: %w", err)
		} else if len(validationErrors) > 0 {
			return fmt.Errorf("schema validation failed: %d errors", len(validationErrors))
		}
	}

	if err := r.db.WithContext(ctx).Create(schema).Error; err != nil {
		return fmt.Errorf("failed to create setting schema: %w", err)
	}

	return nil
}

// GetByID retrieves a setting schema by ID
func (r *settingSchemaRepository) GetByID(ctx context.Context, id uint) (*models.SettingSchema, error) {
	var schema models.SettingSchema
	err := r.db.WithContext(ctx).
		Where("id = ? AND status != ?", id, models.SchemaStatusDeleted).
		First(&schema).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get setting schema by ID: %w", err)
	}

	return &schema, nil
}

// GetByKey retrieves a setting schema by key
func (r *settingSchemaRepository) GetByKey(ctx context.Context, key string) (*models.SettingSchema, error) {
	var schema models.SettingSchema
	err := r.db.WithContext(ctx).
		Where("key = ? AND status != ?", key, models.SchemaStatusDeleted).
		First(&schema).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get setting schema by key: %w", err)
	}

	return &schema, nil
}

// Update updates a setting schema
func (r *settingSchemaRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	// Remove fields that shouldn't be updated directly
	delete(updates, "id")
	delete(updates, "key")
	delete(updates, "created_at")
	delete(updates, "created_by")

	// Increment version if schema is updated
	updates["version"] = gorm.Expr("version + 1")

	result := r.db.WithContext(ctx).
		Model(&models.SettingSchema{}).
		Where("id = ?", id).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("failed to update setting schema: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("setting schema not found")
	}

	return nil
}

// Delete permanently deletes a setting schema
func (r *settingSchemaRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.SettingSchema{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete setting schema: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("setting schema not found")
	}
	return nil
}

// SoftDelete soft deletes a setting schema by setting status to deleted
func (r *settingSchemaRepository) SoftDelete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).
		Model(&models.SettingSchema{}).
		Where("id = ?", id).
		Update("status", models.SchemaStatusDeleted)

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete setting schema: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("setting schema not found")
	}

	return nil
}

// SoftDeleteByKey soft deletes a setting schema by key
func (r *settingSchemaRepository) SoftDeleteByKey(ctx context.Context, key string) error {
	result := r.db.WithContext(ctx).
		Model(&models.SettingSchema{}).
		Where("key = ?", key).
		Update("status", models.SchemaStatusDeleted)

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete setting schema by key: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("setting schema not found")
	}

	return nil
}

// Restore restores a soft deleted setting schema
func (r *settingSchemaRepository) Restore(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).
		Model(&models.SettingSchema{}).
		Where("id = ? AND status = ?", id, models.SchemaStatusDeleted).
		Update("status", models.SchemaStatusActive)

	if result.Error != nil {
		return fmt.Errorf("failed to restore setting schema: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("setting schema not found or not deleted")
	}

	return nil
}

// Upsert creates or updates a setting schema
func (r *settingSchemaRepository) Upsert(ctx context.Context, schema *models.SettingSchema) error {
	if r.options.ValidateOnCreate || r.options.ValidateOnUpdate {
		if validationErrors, err := r.ValidateSchema(ctx, schema); err != nil {
			return fmt.Errorf("schema validation failed: %w", err)
		} else if len(validationErrors) > 0 {
			return fmt.Errorf("schema validation failed: %d errors", len(validationErrors))
		}
	}

	// Fields to update on conflict
	updateFields := []string{
		"title",
		"description",
		"category",
		"data_type",
		"default_value",
		"validation_rules",
		"required",
		"min_length",
		"max_length",
		"min_value",
		"max_value",
		"pattern",
		"allowed_values",
		"allowed_scopes",
		"is_encrypted",
		"is_public",
		"is_editable",
		"ui_component",
		"ui_props",
		"display_order",
		"icon",
		"status",
		"updated_by",
		"updated_at",
	}

	return r.db.WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "key"}},
			DoUpdates: clause.AssignmentColumns(updateFields),
		}).
		Create(schema).Error
}

// BulkCreate creates multiple setting schemas
func (r *settingSchemaRepository) BulkCreate(ctx context.Context, schemas []*models.SettingSchema) error {
	if len(schemas) == 0 {
		return nil
	}

	// Validate all schemas if enabled
	if r.options.ValidateOnCreate {
		for _, schema := range schemas {
			if validationErrors, err := r.ValidateSchema(ctx, schema); err != nil {
				return fmt.Errorf("schema validation failed for key '%s': %w", schema.Key, err)
			} else if len(validationErrors) > 0 {
				return fmt.Errorf("schema validation failed for key '%s': %d errors", schema.Key, len(validationErrors))
			}
		}
	}

	return r.db.WithContext(ctx).CreateInBatches(schemas, 100).Error
}

// BulkUpdate updates multiple setting schemas
func (r *settingSchemaRepository) BulkUpdate(ctx context.Context, schemas []*models.SettingSchema) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, schema := range schemas {
			if err := tx.Save(schema).Error; err != nil {
				return fmt.Errorf("failed to update setting schema '%s': %w", schema.Key, err)
			}
		}
		return nil
	})
}

// BulkUpsert creates or updates multiple setting schemas
func (r *settingSchemaRepository) BulkUpsert(ctx context.Context, schemas []*models.SettingSchema) error {
	if len(schemas) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, schema := range schemas {
			if err := r.Upsert(ctx, schema); err != nil {
				return fmt.Errorf("failed to upsert setting schema '%s': %w", schema.Key, err)
			}
		}
		return nil
	})
}

// BulkDelete deletes multiple setting schemas
func (r *settingSchemaRepository) BulkDelete(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Model(&models.SettingSchema{}).
		Where("key IN ?", keys).
		Update("status", models.SchemaStatusDeleted).Error
}

// List retrieves setting schemas based on filter
func (r *settingSchemaRepository) List(ctx context.Context, filter models.SchemaFilter) ([]*models.SettingSchema, error) {
	query := r.buildQuery(ctx, filter)
	
	var schemas []*models.SettingSchema
	if err := query.Find(&schemas).Error; err != nil {
		return nil, fmt.Errorf("failed to list setting schemas: %w", err)
	}

	return schemas, nil
}

// ListWithCount retrieves setting schemas with count based on filter
func (r *settingSchemaRepository) ListWithCount(ctx context.Context, filter models.SchemaFilter) ([]*models.SettingSchema, int64, error) {
	query := r.buildQuery(ctx, filter)
	
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count setting schemas: %w", err)
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	var schemas []*models.SettingSchema
	if err := query.Find(&schemas).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list setting schemas: %w", err)
	}

	return schemas, count, nil
}

// buildQuery builds the GORM query based on filter
func (r *settingSchemaRepository) buildQuery(ctx context.Context, filter models.SchemaFilter) *gorm.DB {
	query := r.db.WithContext(ctx).Model(&models.SettingSchema{})

	// Default filter for non-deleted records
	if filter.Status == "" {
		query = query.Where("status != ?", models.SchemaStatusDeleted)
	}

	// Apply filters
	if filter.Key != "" {
		query = query.Where("key = ?", filter.Key)
	}

	if len(filter.Keys) > 0 {
		query = query.Where("key IN ?", filter.Keys)
	}

	if filter.Title != "" {
		query = query.Where("title LIKE ?", "%"+filter.Title+"%")
	}

	if filter.Category != "" {
		query = query.Where("category = ?", filter.Category)
	}

	if len(filter.Categories) > 0 {
		query = query.Where("category IN ?", filter.Categories)
	}

	if filter.DataType != "" {
		query = query.Where("data_type = ?", filter.DataType)
	}

	if filter.Required != nil {
		query = query.Where("required = ?", *filter.Required)
	}

	if filter.IsPublic != nil {
		query = query.Where("is_public = ?", *filter.IsPublic)
	}

	if filter.IsEditable != nil {
		query = query.Where("is_editable = ?", *filter.IsEditable)
	}

	if filter.IsSystemSchema != nil {
		query = query.Where("is_system_schema = ?", *filter.IsSystemSchema)
	}

	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	if filter.UIComponent != "" {
		query = query.Where("ui_component = ?", filter.UIComponent)
	}

	if filter.CreatedBy > 0 {
		query = query.Where("created_by = ?", filter.CreatedBy)
	}

	if filter.UpdatedBy > 0 {
		query = query.Where("updated_by = ?", filter.UpdatedBy)
	}

	if filter.Search != "" {
		search := "%" + filter.Search + "%"
		query = query.Where("key LIKE ? OR title LIKE ? OR description LIKE ?", search, search, search)
	}

	// Default ordering
	query = query.Order("category ASC, display_order ASC, title ASC")

	return query
}

// GetByCategory retrieves setting schemas by category
func (r *settingSchemaRepository) GetByCategory(ctx context.Context, category string) ([]*models.SettingSchema, error) {
	filter := models.SchemaFilter{
		Category: category,
	}
	return r.List(ctx, filter)
}

// GetByKeys retrieves setting schemas by multiple keys
func (r *settingSchemaRepository) GetByKeys(ctx context.Context, keys []string) ([]*models.SettingSchema, error) {
	filter := models.SchemaFilter{
		Keys: keys,
	}
	return r.List(ctx, filter)
}

// GetByDataType retrieves setting schemas by data type
func (r *settingSchemaRepository) GetByDataType(ctx context.Context, dataType models.DataType) ([]*models.SettingSchema, error) {
	filter := models.SchemaFilter{
		DataType: dataType,
	}
	return r.List(ctx, filter)
}

// GetByScope retrieves setting schemas by scope
func (r *settingSchemaRepository) GetByScope(ctx context.Context, scope models.ScopeType) ([]*models.SettingSchema, error) {
	var schemas []*models.SettingSchema
	
	// This requires a more complex query to search within JSON
	// For now, we'll get all schemas and filter in Go
	allSchemas, err := r.GetActiveSchemas(ctx)
	if err != nil {
		return nil, err
	}
	
	for _, schema := range allSchemas {
		if schema.IsScopeAllowed(scope) {
			schemas = append(schemas, schema)
		}
	}
	
	return schemas, nil
}

// GetActiveSchemas retrieves all active setting schemas
func (r *settingSchemaRepository) GetActiveSchemas(ctx context.Context) ([]*models.SettingSchema, error) {
	filter := models.SchemaFilter{
		Status: models.SchemaStatusActive,
	}
	return r.List(ctx, filter)
}

// GetActiveSchemasForScope retrieves active schemas for a specific scope
func (r *settingSchemaRepository) GetActiveSchemasForScope(ctx context.Context, scope models.ScopeType) ([]*models.SettingSchema, error) {
	activeSchemas, err := r.GetActiveSchemas(ctx)
	if err != nil {
		return nil, err
	}
	
	var schemas []*models.SettingSchema
	for _, schema := range activeSchemas {
		if schema.IsScopeAllowed(scope) {
			schemas = append(schemas, schema)
		}
	}
	
	return schemas, nil
}

// GetActiveSchemasForCategory retrieves active schemas for a category
func (r *settingSchemaRepository) GetActiveSchemasForCategory(ctx context.Context, category string) ([]*models.SettingSchema, error) {
	filter := models.SchemaFilter{
		Category: category,
		Status:   models.SchemaStatusActive,
	}
	return r.List(ctx, filter)
}

// GetSystemSchemas retrieves system setting schemas
func (r *settingSchemaRepository) GetSystemSchemas(ctx context.Context) ([]*models.SettingSchema, error) {
	isSystem := true
	filter := models.SchemaFilter{
		IsSystemSchema: &isSystem,
		Status:         models.SchemaStatusActive,
	}
	return r.List(ctx, filter)
}

// GetUserSchemas retrieves user setting schemas
func (r *settingSchemaRepository) GetUserSchemas(ctx context.Context) ([]*models.SettingSchema, error) {
	isSystem := false
	filter := models.SchemaFilter{
		IsSystemSchema: &isSystem,
		Status:         models.SchemaStatusActive,
	}
	return r.List(ctx, filter)
}

// ValidateSchema validates a setting schema
func (r *settingSchemaRepository) ValidateSchema(ctx context.Context, schema *models.SettingSchema) ([]repositories.ValidationError, error) {
	var errors []repositories.ValidationError
	
	if schema.Key == "" {
		errors = append(errors, repositories.ValidationError{
			Key:     schema.Key,
			Field:   "key",
			Message: "Key is required",
		})
	}
	
	if schema.Title == "" {
		errors = append(errors, repositories.ValidationError{
			Key:     schema.Key,
			Field:   "title",
			Message: "Title is required",
		})
	}
	
	if schema.Category == "" {
		errors = append(errors, repositories.ValidationError{
			Key:     schema.Key,
			Field:   "category",
			Message: "Category is required",
		})
	}
	
	if schema.DataType == "" {
		errors = append(errors, repositories.ValidationError{
			Key:     schema.Key,
			Field:   "data_type",
			Message: "Data type is required",
		})
	}
	
	if len(schema.AllowedScopes) == 0 {
		errors = append(errors, repositories.ValidationError{
			Key:     schema.Key,
			Field:   "allowed_scopes",
			Message: "At least one allowed scope is required",
		})
	}
	
	// Validate JSON fields
	if len(schema.ValidationRules) > 0 {
		var rules []models.ValidationRule
		if err := json.Unmarshal(schema.ValidationRules, &rules); err != nil {
			errors = append(errors, repositories.ValidationError{
				Key:     schema.Key,
				Field:   "validation_rules",
				Message: "Invalid JSON format for validation rules",
			})
		}
	}
	
	if len(schema.AllowedScopes) > 0 {
		var scopes []models.ScopeType
		if err := json.Unmarshal(schema.AllowedScopes, &scopes); err != nil {
			errors = append(errors, repositories.ValidationError{
				Key:     schema.Key,
				Field:   "allowed_scopes",
				Message: "Invalid JSON format for allowed scopes",
			})
		}
	}
	
	return errors, nil
}

// ValidateSchemaValue validates a value against a schema
func (r *settingSchemaRepository) ValidateSchemaValue(ctx context.Context, schemaKey string, value interface{}) error {
	schema, err := r.GetByKey(ctx, schemaKey)
	if err != nil {
		return fmt.Errorf("failed to get schema: %w", err)
	}
	
	if schema == nil {
		return fmt.Errorf("schema not found: %s", schemaKey)
	}
	
	return schema.ValidateValue(value)
}

// ValidateSchemaMultiple validates multiple values against their schemas
func (r *settingSchemaRepository) ValidateSchemaMultiple(ctx context.Context, values map[string]interface{}) ([]repositories.ValidationError, error) {
	var errors []repositories.ValidationError
	
	for key, value := range values {
		if err := r.ValidateSchemaValue(ctx, key, value); err != nil {
			errors = append(errors, repositories.ValidationError{
				Key:     key,
				Message: err.Error(),
			})
		}
	}
	
	return errors, nil
}

// Search searches setting schemas
func (r *settingSchemaRepository) Search(ctx context.Context, query string, filter models.SchemaFilter) ([]*models.SettingSchema, error) {
	filter.Search = query
	return r.List(ctx, filter)
}

// SearchByTitle searches setting schemas by title
func (r *settingSchemaRepository) SearchByTitle(ctx context.Context, title string, filter models.SchemaFilter) ([]*models.SettingSchema, error) {
	filter.Title = title
	return r.List(ctx, filter)
}

// SearchByDescription searches setting schemas by description
func (r *settingSchemaRepository) SearchByDescription(ctx context.Context, description string, filter models.SchemaFilter) ([]*models.SettingSchema, error) {
	// Custom query for description search
	query := r.buildQuery(ctx, filter)
	query = query.Where("description LIKE ?", "%"+description+"%")
	
	var schemas []*models.SettingSchema
	if err := query.Find(&schemas).Error; err != nil {
		return nil, fmt.Errorf("failed to search schemas by description: %w", err)
	}
	
	return schemas, nil
}

// Statistics operations
func (r *settingSchemaRepository) CountByCategory(ctx context.Context, category string) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.SettingSchema{}).
		Where("category = ? AND status != ?", category, models.SchemaStatusDeleted).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count schemas by category: %w", err)
	}
	return count, nil
}

func (r *settingSchemaRepository) CountByDataType(ctx context.Context, dataType models.DataType) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.SettingSchema{}).
		Where("data_type = ? AND status != ?", dataType, models.SchemaStatusDeleted).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count schemas by data type: %w", err)
	}
	return count, nil
}

func (r *settingSchemaRepository) CountByStatus(ctx context.Context, status models.SchemaStatus) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.SettingSchema{}).
		Where("status = ?", status).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count schemas by status: %w", err)
	}
	return count, nil
}

func (r *settingSchemaRepository) CountByScope(ctx context.Context, scope models.ScopeType) (int64, error) {
	// This requires searching within JSON, which is complex
	// For now, we'll count all and filter in Go
	schemas, err := r.GetByScope(ctx, scope)
	if err != nil {
		return 0, err
	}
	return int64(len(schemas)), nil
}

// Export/Import operations
func (r *settingSchemaRepository) Export(ctx context.Context, filter models.SchemaFilter) ([]*models.SettingSchema, error) {
	return r.List(ctx, filter)
}

func (r *settingSchemaRepository) Import(ctx context.Context, schemas []*models.SettingSchema) error {
	return r.BulkCreate(ctx, schemas)
}

// Categories operations
func (r *settingSchemaRepository) GetCategories(ctx context.Context) ([]string, error) {
	var categories []string
	if err := r.db.WithContext(ctx).Model(&models.SettingSchema{}).
		Where("status != ?", models.SchemaStatusDeleted).
		Distinct("category").
		Pluck("category", &categories).Error; err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}
	return categories, nil
}

func (r *settingSchemaRepository) GetCategoriesWithCount(ctx context.Context) (map[string]int64, error) {
	type CategoryCount struct {
		Category string
		Count    int64
	}
	
	var results []CategoryCount
	if err := r.db.WithContext(ctx).Model(&models.SettingSchema{}).
		Where("status != ?", models.SchemaStatusDeleted).
		Select("category, COUNT(*) as count").
		Group("category").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get categories with count: %w", err)
	}
	
	categoriesMap := make(map[string]int64)
	for _, result := range results {
		categoriesMap[result.Category] = result.Count
	}
	
	return categoriesMap, nil
}

// UI Component operations
func (r *settingSchemaRepository) GetUIComponents(ctx context.Context) ([]string, error) {
	var components []string
	if err := r.db.WithContext(ctx).Model(&models.SettingSchema{}).
		Where("status != ? AND ui_component IS NOT NULL AND ui_component != ''", models.SchemaStatusDeleted).
		Distinct("ui_component").
		Pluck("ui_component", &components).Error; err != nil {
		return nil, fmt.Errorf("failed to get UI components: %w", err)
	}
	return components, nil
}

func (r *settingSchemaRepository) GetSchemasByUIComponent(ctx context.Context, component string) ([]*models.SettingSchema, error) {
	filter := models.SchemaFilter{
		UIComponent: component,
	}
	return r.List(ctx, filter)
}

// Placeholder implementations for remaining methods
func (r *settingSchemaRepository) ClearCache(ctx context.Context, key string) error {
	return nil
}

func (r *settingSchemaRepository) ClearCacheByCategory(ctx context.Context, category string) error {
	return nil
}

func (r *settingSchemaRepository) RefreshCache(ctx context.Context, key string) error {
	return nil
}

func (r *settingSchemaRepository) Exists(ctx context.Context, key string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.SettingSchema{}).
		Where("key = ? AND status != ?", key, models.SchemaStatusDeleted).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check schema existence: %w", err)
	}
	return count > 0, nil
}

func (r *settingSchemaRepository) ExistsMultiple(ctx context.Context, keys []string) (map[string]bool, error) {
	result := make(map[string]bool)
	for _, key := range keys {
		exists, err := r.Exists(ctx, key)
		if err != nil {
			return nil, err
		}
		result[key] = exists
	}
	return result, nil
}

func (r *settingSchemaRepository) GetSchemaVersions(ctx context.Context, key string) ([]*models.SettingSchema, error) {
	var schemas []*models.SettingSchema
	if err := r.db.WithContext(ctx).
		Where("key = ?", key).
		Order("version DESC").
		Find(&schemas).Error; err != nil {
		return nil, fmt.Errorf("failed to get schema versions: %w", err)
	}
	return schemas, nil
}

func (r *settingSchemaRepository) GetLatestVersion(ctx context.Context, key string) (*models.SettingSchema, error) {
	var schema models.SettingSchema
	err := r.db.WithContext(ctx).
		Where("key = ? AND status != ?", key, models.SchemaStatusDeleted).
		Order("version DESC").
		First(&schema).Error
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get latest schema version: %w", err)
	}
	
	return &schema, nil
}

func (r *settingSchemaRepository) CreateVersion(ctx context.Context, schema *models.SettingSchema) error {
	// For versioning, we would create a new record with incremented version
	// For now, just create the schema
	return r.Create(ctx, schema)
}

func (r *settingSchemaRepository) GetDefaultValues(ctx context.Context, keys []string) (map[string]interface{}, error) {
	schemas, err := r.GetByKeys(ctx, keys)
	if err != nil {
		return nil, err
	}
	
	defaultValues := make(map[string]interface{})
	for _, schema := range schemas {
		if len(schema.DefaultValue) > 0 {
			var value interface{}
			if err := json.Unmarshal(schema.DefaultValue, &value); err == nil {
				defaultValues[schema.Key] = value
			}
		}
	}
	
	return defaultValues, nil
}

func (r *settingSchemaRepository) GetDefaultValuesForScope(ctx context.Context, scope models.ScopeType) (map[string]interface{}, error) {
	schemas, err := r.GetByScope(ctx, scope)
	if err != nil {
		return nil, err
	}
	
	defaultValues := make(map[string]interface{})
	for _, schema := range schemas {
		if len(schema.DefaultValue) > 0 {
			var value interface{}
			if err := json.Unmarshal(schema.DefaultValue, &value); err == nil {
				defaultValues[schema.Key] = value
			}
		}
	}
	
	return defaultValues, nil
}

func (r *settingSchemaRepository) GetDependentSchemas(ctx context.Context, key string) ([]*models.SettingSchema, error) {
	// This would require a dependency system to be implemented
	// For now, return empty slice
	return []*models.SettingSchema{}, nil
}

func (r *settingSchemaRepository) GetSchemaDependencies(ctx context.Context, key string) ([]*models.SettingSchema, error) {
	// This would require a dependency system to be implemented
	// For now, return empty slice
	return []*models.SettingSchema{}, nil
}

func (r *settingSchemaRepository) Cleanup(ctx context.Context, daysOld int) (int64, error) {
	cutoffTime := time.Now().AddDate(0, 0, -daysOld)
	
	result := r.db.WithContext(ctx).
		Where("status = ? AND updated_at < ?", models.SchemaStatusDeleted, cutoffTime).
		Delete(&models.SettingSchema{})
	
	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old schemas: %w", result.Error)
	}
	
	return result.RowsAffected, nil
}

func (r *settingSchemaRepository) Optimize(ctx context.Context) error {
	// Database optimization tasks would go here
	return nil
}

func (r *settingSchemaRepository) GetPublicSchemas(ctx context.Context, scope models.ScopeType) ([]*models.SettingSchema, error) {
	isPublic := true
	filter := models.SchemaFilter{
		IsPublic: &isPublic,
	}
	
	allSchemas, err := r.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	
	var schemas []*models.SettingSchema
	for _, schema := range allSchemas {
		if schema.IsScopeAllowed(scope) {
			schemas = append(schemas, schema)
		}
	}
	
	return schemas, nil
}

func (r *settingSchemaRepository) GetEditableSchemas(ctx context.Context, scope models.ScopeType) ([]*models.SettingSchema, error) {
	isEditable := true
	filter := models.SchemaFilter{
		IsEditable: &isEditable,
	}
	
	allSchemas, err := r.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	
	var schemas []*models.SettingSchema
	for _, schema := range allSchemas {
		if schema.IsScopeAllowed(scope) {
			schemas = append(schemas, schema)
		}
	}
	
	return schemas, nil
}

func (r *settingSchemaRepository) CheckSchemaPermission(ctx context.Context, key string, scope models.ScopeType, userID uint) (bool, error) {
	// Permission checking logic would go here
	// For now, just return true
	return true, nil
}