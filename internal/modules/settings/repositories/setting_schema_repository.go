package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/models"
)

// SettingSchemaRepository defines the interface for setting schema data operations
type SettingSchemaRepository interface {
	// Basic CRUD operations
	CreateSchema(ctx context.Context, schema *models.SettingSchema) (*models.SettingSchema, error)
	GetSchemaByID(ctx context.Context, schemaID uint) (*models.SettingSchema, error)
	GetSchemaByKey(ctx context.Context, category, key string) (*models.SettingSchema, error)
	UpdateSchema(ctx context.Context, schema *models.SettingSchema) (*models.SettingSchema, error)
	DeleteSchema(ctx context.Context, schemaID uint) error
	
	// Query operations
	GetSchemas(ctx context.Context, filter models.SchemaFilter) ([]*models.SettingSchema, error)
	GetSchemasByCategory(ctx context.Context, category string) ([]*models.SettingSchema, error)
	
	// Existence check
	SchemaExists(ctx context.Context, category, key string) (bool, error)
}