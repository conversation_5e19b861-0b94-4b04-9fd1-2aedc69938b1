package models

import (
	"database/sql/driver"
	"fmt"
)

// DataType represents the data type of a setting value
// @Enum string,number,boolean,json,array
type DataType string

const (
	DataTypeString  DataType = "string"
	DataTypeNumber  DataType = "number"
	DataTypeBoolean DataType = "boolean"
	DataTypeJSON    DataType = "json"
	DataTypeArray   DataType = "array"
)

// <PERSON>an implements sql.Scanner interface for DataType
func (d *DataType) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	if bytes, ok := value.([]byte); ok {
		*d = DataType(bytes)
		return nil
	}
	return fmt.Errorf("cannot scan %T into DataType", value)
}

// Value implements driver.Valuer interface for DataType
func (d DataType) Value() (driver.Value, error) {
	return string(d), nil
}

// IsValid checks if the DataType is valid
func (d DataType) IsValid() bool {
	switch d {
	case DataTypeString, DataTypeNumber, DataTypeBoolean, DataTypeJSON, DataTypeArray:
		return true
	default:
		return false
	}
}

// String returns the string representation of DataType
func (d DataType) String() string {
	return string(d)
}

// ScopeType represents the scope type for hierarchical settings
// @Enum global,tenant,website,user
type ScopeType string

const (
	ScopeTypeGlobal  ScopeType = "global"
	ScopeTypeTenant  ScopeType = "tenant"
	ScopeTypeWebsite ScopeType = "website"
	ScopeTypeUser    ScopeType = "user"
)

// Scan implements sql.Scanner interface for ScopeType
func (s *ScopeType) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	if bytes, ok := value.([]byte); ok {
		*s = ScopeType(bytes)
		return nil
	}
	return fmt.Errorf("cannot scan %T into ScopeType", value)
}

// Value implements driver.Valuer interface for ScopeType
func (s ScopeType) Value() (driver.Value, error) {
	return string(s), nil
}

// IsValid checks if the ScopeType is valid
func (s ScopeType) IsValid() bool {
	switch s {
	case ScopeTypeGlobal, ScopeTypeTenant, ScopeTypeWebsite, ScopeTypeUser:
		return true
	default:
		return false
	}
}

// String returns the string representation of ScopeType
func (s ScopeType) String() string {
	return string(s)
}

// ValidationRuleType represents the type of validation rule
// @Enum required,min_length,max_length,min,max,regex,enum,email,url
type ValidationRuleType string

const (
	ValidationRuleRequired ValidationRuleType = "required"
	ValidationRuleMinLen   ValidationRuleType = "min_length"
	ValidationRuleMaxLen   ValidationRuleType = "max_length"
	ValidationRuleMin      ValidationRuleType = "min"
	ValidationRuleMax      ValidationRuleType = "max"
	ValidationRuleRegex    ValidationRuleType = "regex"
	ValidationRuleEnum     ValidationRuleType = "enum"
	ValidationRuleEmail    ValidationRuleType = "email"
	ValidationRuleURL      ValidationRuleType = "url"
)

// SettingCategory represents predefined setting categories
type SettingCategory struct {
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Description string `json:"description"`
	Icon        string `json:"icon,omitempty"`
	Order       int    `json:"order"`
}

// PredefinedCategories returns the list of predefined setting categories
func PredefinedCategories() []SettingCategory {
	return []SettingCategory{
		{Name: "general", DisplayName: "General Settings", Description: "Basic configuration", Order: 1},
		{Name: "branding", DisplayName: "Branding", Description: "Logo, colors, and appearance", Order: 2},
		{Name: "email", DisplayName: "Email Settings", Description: "Email configuration and templates", Order: 3},
		{Name: "security", DisplayName: "Security", Description: "Security and authentication settings", Order: 4},
		{Name: "api", DisplayName: "API Settings", Description: "API keys and integration settings", Order: 5},
		{Name: "notifications", DisplayName: "Notifications", Description: "Notification preferences", Order: 6},
		{Name: "billing", DisplayName: "Billing", Description: "Billing and payment settings", Order: 7},
		{Name: "advanced", DisplayName: "Advanced", Description: "Advanced configuration options", Order: 8},
	}
}

// GetCategoryByName returns a category by name
func GetCategoryByName(name string) (SettingCategory, bool) {
	for _, category := range PredefinedCategories() {
		if category.Name == name {
			return category, true
		}
	}
	return SettingCategory{}, false
}