package models

import (
	"encoding/json"
)

// SettingCreateRequest represents the request to create a new setting
type SettingCreateRequest struct {
	ScopeType       ScopeType       `json:"scope_type" validate:"required,oneof=global tenant website user"`
	ScopeID         *uint           `json:"scope_id,omitempty"`
	Category        string          `json:"category" validate:"required,max=100"`
	Key             string          `json:"key" validate:"required,max=255"`
	Value           interface{}     `json:"value" validate:"required"`
	Description     string          `json:"description,omitempty" validate:"max=1000"`
	DataType        DataType        `json:"data_type" validate:"required,oneof=string number boolean json array"`
	IsEncrypted     bool            `json:"is_encrypted,omitempty"`
	IsPublic        bool            `json:"is_public,omitempty"`
	IsRequired      bool            `json:"is_required,omitempty"`
	IsReadOnly      bool            `json:"is_read_only,omitempty"`
	ValidationRules json.RawMessage `json:"validation_rules,omitempty"`
	DefaultValue    interface{}     `json:"default_value,omitempty"`
	Options         json.RawMessage `json:"options,omitempty"`
}

// SettingUpdateRequest represents the request to update an existing setting
type SettingUpdateRequest struct {
	Value           interface{}     `json:"value,omitempty"`
	Description     string          `json:"description,omitempty" validate:"max=1000"`
	IsPublic        *bool           `json:"is_public,omitempty"`
	IsRequired      *bool           `json:"is_required,omitempty"`
	IsReadOnly      *bool           `json:"is_read_only,omitempty"`
	ValidationRules json.RawMessage `json:"validation_rules,omitempty"`
	DefaultValue    interface{}     `json:"default_value,omitempty"`
	Options         json.RawMessage `json:"options,omitempty"`
}

// SettingBulkCreateRequest represents the request to create multiple settings
type SettingBulkCreateRequest struct {
	ScopeType ScopeType             `json:"scope_type" validate:"required,oneof=global tenant website user"`
	ScopeID   *uint                 `json:"scope_id,omitempty"`
	Settings  []SettingCreateItem   `json:"settings" validate:"required,dive"`
}

// SettingCreateItem represents a single setting item in bulk create
type SettingCreateItem struct {
	Category        string          `json:"category" validate:"required,max=100"`
	Key             string          `json:"key" validate:"required,max=255"`
	Value           interface{}     `json:"value" validate:"required"`
	Description     string          `json:"description,omitempty" validate:"max=1000"`
	DataType        DataType        `json:"data_type" validate:"required,oneof=string number boolean json array"`
	IsEncrypted     bool            `json:"is_encrypted,omitempty"`
	IsPublic        bool            `json:"is_public,omitempty"`
	IsRequired      bool            `json:"is_required,omitempty"`
	IsReadOnly      bool            `json:"is_read_only,omitempty"`
	ValidationRules json.RawMessage `json:"validation_rules,omitempty"`
	DefaultValue    interface{}     `json:"default_value,omitempty"`
	Options         json.RawMessage `json:"options,omitempty"`
}

// SettingBulkUpdateRequest represents the request to update multiple settings
type SettingBulkUpdateRequest struct {
	Settings []SettingUpdateItem `json:"settings" validate:"required,dive"`
}

// SettingUpdateItem represents a single setting update in bulk operation
type SettingUpdateItem struct {
	Category string      `json:"category" validate:"required"`
	Key      string      `json:"key" validate:"required"`
	Value    interface{} `json:"value" validate:"required"`
}

// SettingSchemaCreateRequest represents the request to create a setting schema
type SettingSchemaCreateRequest struct {
	Category        string          `json:"category" validate:"required,max=100"`
	Key             string          `json:"key" validate:"required,max=255"`
	Name            string          `json:"name" validate:"required,max=255"`
	Description     string          `json:"description,omitempty" validate:"max=1000"`
	DataType        DataType        `json:"data_type" validate:"required,oneof=string number boolean json array"`
	IsEncrypted     bool            `json:"is_encrypted,omitempty"`
	IsPublic        bool            `json:"is_public,omitempty"`
	IsRequired      bool            `json:"is_required,omitempty"`
	IsReadOnly      bool            `json:"is_read_only,omitempty"`
	IsDeletable     bool            `json:"is_deletable,omitempty"`
	ValidationRules json.RawMessage `json:"validation_rules,omitempty"`
	DefaultValue    interface{}     `json:"default_value,omitempty"`
	Options         json.RawMessage `json:"options,omitempty"`
	UIConfig        json.RawMessage `json:"ui_config,omitempty"`
}

// SettingSchemaUpdateRequest represents the request to update a setting schema
type SettingSchemaUpdateRequest struct {
	Name            string          `json:"name,omitempty" validate:"max=255"`
	Description     string          `json:"description,omitempty" validate:"max=1000"`
	DataType        DataType        `json:"data_type,omitempty" validate:"omitempty,oneof=string number boolean json array"`
	IsPublic        *bool           `json:"is_public,omitempty"`
	IsRequired      *bool           `json:"is_required,omitempty"`
	IsReadOnly      *bool           `json:"is_read_only,omitempty"`
	IsDeletable     *bool           `json:"is_deletable,omitempty"`
	ValidationRules json.RawMessage `json:"validation_rules,omitempty"`
	DefaultValue    interface{}     `json:"default_value,omitempty"`
	Options         json.RawMessage `json:"options,omitempty"`
	UIConfig        json.RawMessage `json:"ui_config,omitempty"`
}

// SettingQueryRequest represents the request to query settings
type SettingQueryRequest struct {
	ScopeType  ScopeType `json:"scope_type,omitempty" validate:"omitempty,oneof=global tenant website user"`
	ScopeID    *uint     `json:"scope_id,omitempty"`
	Category   string    `json:"category,omitempty" validate:"max=100"`
	Key        string    `json:"key,omitempty" validate:"max=255"`
	IsPublic   *bool     `json:"is_public,omitempty"`
	IsRequired *bool     `json:"is_required,omitempty"`
	Search     string    `json:"search,omitempty" validate:"max=255"`
	Page       int       `json:"page,omitempty" validate:"min=1"`
	PageSize   int       `json:"page_size,omitempty" validate:"min=1,max=100"`
}

// SettingValidationRequest represents the request to validate a setting value
type SettingValidationRequest struct {
	Category string      `json:"category" validate:"required,max=100"`
	Key      string      `json:"key" validate:"required,max=255"`
	Value    interface{} `json:"value" validate:"required"`
}

// SettingHierarchyRequest represents the request to resolve setting hierarchy
type SettingHierarchyRequest struct {
	Category string `json:"category" validate:"required,max=100"`
	Key      string `json:"key" validate:"required,max=255"`
	TenantID *uint  `json:"tenant_id,omitempty"`
	WebsiteID *uint `json:"website_id,omitempty"`
	UserID   *uint  `json:"user_id,omitempty"`
}

// SettingCopyRequest represents the request to copy settings between scopes
type SettingCopyRequest struct {
	SourceScopeType ScopeType `json:"source_scope_type" validate:"required,oneof=global tenant website user"`
	SourceScopeID   *uint     `json:"source_scope_id,omitempty"`
	TargetScopeType ScopeType `json:"target_scope_type" validate:"required,oneof=global tenant website user"`
	TargetScopeID   *uint     `json:"target_scope_id,omitempty"`
	Categories      []string  `json:"categories,omitempty"`
	Keys            []string  `json:"keys,omitempty"`
	OverwriteExisting bool    `json:"overwrite_existing,omitempty"`
}

// SettingExportRequest represents the request to export settings
type SettingExportRequest struct {
	ScopeType  ScopeType `json:"scope_type,omitempty" validate:"omitempty,oneof=global tenant website user"`
	ScopeID    *uint     `json:"scope_id,omitempty"`
	Categories []string  `json:"categories,omitempty"`
	Keys       []string  `json:"keys,omitempty"`
	Format     string    `json:"format,omitempty" validate:"omitempty,oneof=json yaml env"`
	IncludeEncrypted bool `json:"include_encrypted,omitempty"`
	IncludeMetadata  bool `json:"include_metadata,omitempty"`
}

// SettingImportRequest represents the request to import settings
type SettingImportRequest struct {
	ScopeType         ScopeType `json:"scope_type" validate:"required,oneof=global tenant website user"`
	ScopeID           *uint     `json:"scope_id,omitempty"`
	Data              string    `json:"data" validate:"required"`
	Format            string    `json:"format" validate:"required,oneof=json yaml env"`
	OverwriteExisting bool      `json:"overwrite_existing,omitempty"`
	DryRun            bool      `json:"dry_run,omitempty"`
}

// SettingFilter represents filters for querying settings
type SettingFilter struct {
	ScopeType   ScopeType `json:"scope_type,omitempty"`
	ScopeID     *uint     `json:"scope_id,omitempty"`
	ScopeIDs    []uint    `json:"scope_ids,omitempty"`
	Category    string    `json:"category,omitempty"`
	Categories  []string  `json:"categories,omitempty"`
	Key         string    `json:"key,omitempty"`
	Keys        []string  `json:"keys,omitempty"`
	DataType    DataType  `json:"data_type,omitempty"`
	IsPublic    *bool     `json:"is_public,omitempty"`
	IsRequired  *bool     `json:"is_required,omitempty"`
	IsReadOnly  *bool     `json:"is_read_only,omitempty"`
	IsEncrypted *bool     `json:"is_encrypted,omitempty"`
	Search      string    `json:"search,omitempty"`
	Page        int       `json:"page,omitempty"`
	PageSize    int       `json:"page_size,omitempty"`
}

// SetSettingValueRequest represents the request to set a setting value
type SetSettingValueRequest struct {
	ScopeType ScopeType   `json:"scope_type" validate:"required,oneof=global tenant website user"`
	ScopeID   *uint       `json:"scope_id,omitempty"`
	Value     interface{} `json:"value" validate:"required"`
}

// Validate validates the setting filter
func (f *SettingFilter) Validate() error {
	if f.Page < 0 {
		f.Page = 1
	}
	if f.PageSize <= 0 {
		f.PageSize = 50
	}
	if f.PageSize > 100 {
		f.PageSize = 100
	}
	return nil
}