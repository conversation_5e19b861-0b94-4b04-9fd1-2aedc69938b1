package models

import (
	"encoding/json"
	"time"
)

// SettingResponse represents the response for a single setting
type SettingResponse struct {
	ID          uint            `json:"id"`
	ScopeType   ScopeType       `json:"scope_type"`
	ScopeID     *uint           `json:"scope_id,omitempty"`
	Category    string          `json:"category"`
	Key         string          `json:"key"`
	Value       interface{}     `json:"value"`
	Description string          `json:"description,omitempty"`
	DataType    DataType        `json:"data_type"`
	IsEncrypted bool            `json:"is_encrypted"`
	IsPublic    bool            `json:"is_public"`
	IsRequired  bool            `json:"is_required"`
	IsReadOnly  bool            `json:"is_read_only"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	
	// Optional metadata (only included in detailed responses)
	ValidationRules json.RawMessage `json:"validation_rules,omitempty"`
	DefaultValue    interface{}     `json:"default_value,omitempty"`
	Options         json.RawMessage `json:"options,omitempty"`
	ScopeKey        string          `json:"scope_key,omitempty"`
	FullKey         string          `json:"full_key,omitempty"`
}

// SettingListResponse represents the response for listing settings
type SettingListResponse struct {
	Settings   []SettingResponse `json:"settings"`
	Pagination *PaginationInfo   `json:"pagination,omitempty"`
}

// SettingSchemaResponse represents the response for a setting schema
type SettingSchemaResponse struct {
	ID          uint            `json:"id"`
	Category    string          `json:"category"`
	Key         string          `json:"key"`
	Name        string          `json:"name"`
	Description string          `json:"description,omitempty"`
	DataType    DataType        `json:"data_type"`
	IsEncrypted bool            `json:"is_encrypted"`
	IsPublic    bool            `json:"is_public"`
	IsRequired  bool            `json:"is_required"`
	IsReadOnly  bool            `json:"is_read_only"`
	IsDeletable bool            `json:"is_deletable"`
	Version     int             `json:"version"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	
	// Optional metadata
	ValidationRules json.RawMessage `json:"validation_rules,omitempty"`
	DefaultValue    interface{}     `json:"default_value,omitempty"`
	Options         json.RawMessage `json:"options,omitempty"`
	UIConfig        json.RawMessage `json:"ui_config,omitempty"`
}

// SettingSchemaListResponse represents the response for listing setting schemas
type SettingSchemaListResponse struct {
	Schemas    []SettingSchemaResponse `json:"schemas"`
	Pagination *PaginationInfo         `json:"pagination,omitempty"`
}

// SettingCategoryResponse represents the response for a setting category
type SettingCategoryResponse struct {
	Name        string            `json:"name"`
	DisplayName string            `json:"display_name"`
	Description string            `json:"description"`
	Icon        string            `json:"icon,omitempty"`
	Order       int               `json:"order"`
	Settings    []SettingResponse `json:"settings,omitempty"`
	Count       int               `json:"count"`
}

// SettingCategoryListResponse represents the response for listing setting categories
type SettingCategoryListResponse struct {
	Categories []SettingCategoryResponse `json:"categories"`
	Total      int                       `json:"total"`
}

// SettingValueResponse represents the response for a setting value only
type SettingValueResponse struct {
	Value    interface{} `json:"value"`
	DataType DataType    `json:"data_type"`
}

// SettingValidationResponse represents the response for setting validation
type SettingValidationResponse struct {
	IsValid bool                   `json:"is_valid"`
	Errors  []ValidationError      `json:"errors,omitempty"`
	Warnings []ValidationWarning   `json:"warnings,omitempty"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// ValidationWarning represents a validation warning
type ValidationWarning struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// SettingHierarchyResponse represents the response for setting hierarchy resolution
type SettingHierarchyResponse struct {
	Category     string                   `json:"category"`
	Key          string                   `json:"key"`
	ResolvedValue interface{}             `json:"resolved_value"`
	DataType     DataType                 `json:"data_type"`
	Sources      []SettingHierarchySource `json:"sources"`
}

// SettingHierarchySource represents a source in the setting hierarchy
type SettingHierarchySource struct {
	ScopeType   ScopeType   `json:"scope_type"`
	ScopeID     *uint       `json:"scope_id,omitempty"`
	Value       interface{} `json:"value"`
	IsUsed      bool        `json:"is_used"`
	IsOverridden bool       `json:"is_overridden"`
}

// SettingBulkOperationResponse represents the response for bulk operations
type SettingBulkOperationResponse struct {
	Success    []SettingOperationResult `json:"success"`
	Failed     []SettingOperationResult `json:"failed"`
	TotalCount int                      `json:"total_count"`
	SuccessCount int                    `json:"success_count"`
	FailedCount  int                    `json:"failed_count"`
}

// SettingOperationResult represents the result of a single setting operation
type SettingOperationResult struct {
	Category string  `json:"category"`
	Key      string  `json:"key"`
	Success  bool    `json:"success"`
	Error    string  `json:"error,omitempty"`
	Setting  *SettingResponse `json:"setting,omitempty"`
}

// SettingExportResponse represents the response for setting export
type SettingExportResponse struct {
	Format   string      `json:"format"`
	Data     string      `json:"data"`
	Metadata ExportMetadata `json:"metadata"`
}

// ExportMetadata represents metadata for exported settings
type ExportMetadata struct {
	ExportedAt    time.Time `json:"exported_at"`
	ScopeType     ScopeType `json:"scope_type"`
	ScopeID       *uint     `json:"scope_id,omitempty"`
	SettingsCount int       `json:"settings_count"`
	Categories    []string  `json:"categories"`
}

// SettingImportResponse represents the response for setting import
type SettingImportResponse struct {
	DryRun      bool                     `json:"dry_run"`
	Success     []SettingOperationResult `json:"success"`
	Failed      []SettingOperationResult `json:"failed"`
	TotalCount  int                      `json:"total_count"`
	SuccessCount int                     `json:"success_count"`
	FailedCount  int                     `json:"failed_count"`
	Warnings    []ValidationWarning      `json:"warnings,omitempty"`
}

// SettingStatsResponse represents the response for setting statistics
type SettingStatsResponse struct {
	TotalSettings    int                        `json:"total_settings"`
	SettingsByScope  map[ScopeType]int          `json:"settings_by_scope"`
	SettingsByCategory map[string]int           `json:"settings_by_category"`
	SettingsByType   map[DataType]int           `json:"settings_by_type"`
	EncryptedCount   int                        `json:"encrypted_count"`
	PublicCount      int                        `json:"public_count"`
	RequiredCount    int                        `json:"required_count"`
	ReadOnlyCount    int                        `json:"read_only_count"`
}

// PaginationInfo represents pagination information
type PaginationInfo struct {
	Page        int `json:"page"`
	PageSize    int `json:"page_size"`
	TotalCount  int `json:"total_count"`
	TotalPages  int `json:"total_pages"`
	HasNext     bool `json:"has_next"`
	HasPrevious bool `json:"has_previous"`
}

// OptionItem represents an option for select/enum fields
type OptionItem struct {
	Value    interface{} `json:"value"`
	Label    string      `json:"label"`
	Disabled bool        `json:"disabled,omitempty"`
	Group    string      `json:"group,omitempty"`
}

// CreateSettingResponse creates a setting response from a setting model
func CreateSettingResponse(setting *Setting, includeMetadata bool) *SettingResponse {
	value, _ := setting.GetTypedValue()
	
	response := &SettingResponse{
		ID:          setting.ID,
		ScopeType:   setting.ScopeType,
		ScopeID:     setting.ScopeID,
		Category:    setting.Category,
		Key:         setting.Key,
		Value:       value,
		Description: setting.Description,
		DataType:    setting.DataType,
		IsEncrypted: setting.IsEncrypted,
		IsPublic:    setting.IsPublic,
		IsRequired:  setting.IsRequired,
		IsReadOnly:  setting.IsReadOnly,
		CreatedAt:   setting.CreatedAt,
		UpdatedAt:   setting.UpdatedAt,
	}
	
	if includeMetadata {
		response.ValidationRules = setting.ValidationRules
		response.Options = setting.Options
		response.ScopeKey = setting.GetScopeKey()
		response.FullKey = setting.GetFullKey()
		
		if setting.DefaultValue != nil {
			var defaultValue interface{}
			json.Unmarshal(setting.DefaultValue, &defaultValue)
			response.DefaultValue = defaultValue
		}
	}
	
	return response
}

// CreateSettingSchemaResponse creates a setting schema response from a schema model
func CreateSettingSchemaResponse(schema *SettingSchema) *SettingSchemaResponse {
	response := &SettingSchemaResponse{
		ID:          schema.ID,
		Category:    schema.Category,
		Key:         schema.Key,
		Name:        schema.Name,
		Description: schema.Description,
		DataType:    schema.DataType,
		IsEncrypted: schema.IsEncrypted,
		IsPublic:    schema.IsPublic,
		IsRequired:  schema.IsRequired,
		IsReadOnly:  schema.IsReadOnly,
		IsDeletable: schema.IsDeletable,
		Version:     schema.Version,
		CreatedAt:   schema.CreatedAt,
		UpdatedAt:   schema.UpdatedAt,
		ValidationRules: schema.ValidationRules,
		Options:     schema.Options,
		UIConfig:    schema.UIConfig,
	}
	
	if schema.DefaultValue != nil {
		var defaultValue interface{}
		json.Unmarshal(schema.DefaultValue, &defaultValue)
		response.DefaultValue = defaultValue
	}
	
	return response
}

// FromSetting converts a Setting model to SettingResponse
func (sr *SettingResponse) FromSetting(setting *Setting) {
	value, _ := setting.GetTypedValue()
	
	sr.ID = setting.ID
	sr.ScopeType = setting.ScopeType
	sr.ScopeID = setting.ScopeID
	sr.Category = setting.Category
	sr.Key = setting.Key
	sr.Value = value
	sr.Description = setting.Description
	sr.DataType = setting.DataType
	sr.IsEncrypted = setting.IsEncrypted
	sr.IsPublic = setting.IsPublic
	sr.IsRequired = setting.IsRequired
	sr.IsReadOnly = setting.IsReadOnly
	sr.CreatedAt = setting.CreatedAt
	sr.UpdatedAt = setting.UpdatedAt
	sr.ValidationRules = setting.ValidationRules
	sr.Options = setting.Options
	sr.ScopeKey = setting.GetScopeKey()
	sr.FullKey = setting.GetFullKey()
	
	if setting.DefaultValue != nil {
		var defaultValue interface{}
		json.Unmarshal(setting.DefaultValue, &defaultValue)
		sr.DefaultValue = defaultValue
	}
}

// FromSettingSchema converts a SettingSchema model to SettingSchemaResponse  
func (ssr *SettingSchemaResponse) FromSettingSchema(schema *SettingSchema) {
	ssr.ID = schema.ID
	ssr.Category = schema.Category
	ssr.Key = schema.Key
	ssr.Name = schema.Name
	ssr.Description = schema.Description
	ssr.DataType = schema.DataType
	ssr.IsEncrypted = schema.IsEncrypted
	ssr.IsPublic = schema.IsPublic
	ssr.IsRequired = schema.IsRequired
	ssr.IsReadOnly = schema.IsReadOnly
	ssr.IsDeletable = schema.IsDeletable
	ssr.Version = schema.Version
	ssr.CreatedAt = schema.CreatedAt
	ssr.UpdatedAt = schema.UpdatedAt
	ssr.ValidationRules = schema.ValidationRules
	ssr.Options = schema.Options
	ssr.UIConfig = schema.UIConfig
	
	if schema.DefaultValue != nil {
		var defaultValue interface{}
		json.Unmarshal(schema.DefaultValue, &defaultValue)
		ssr.DefaultValue = defaultValue
	}
}

// CreateSettingListResponse creates a setting list response
func CreateSettingListResponse(settings []Setting, pagination *PaginationInfo, includeMetadata bool) *SettingListResponse {
	response := &SettingListResponse{
		Settings:   make([]SettingResponse, len(settings)),
		Pagination: pagination,
	}
	
	for i, setting := range settings {
		response.Settings[i] = *CreateSettingResponse(&setting, includeMetadata)
	}
	
	return response
}

// CreateSettingSchemaListResponse creates a setting schema list response
func CreateSettingSchemaListResponse(schemas []SettingSchema, pagination *PaginationInfo) *SettingSchemaListResponse {
	response := &SettingSchemaListResponse{
		Schemas:    make([]SettingSchemaResponse, len(schemas)),
		Pagination: pagination,
	}
	
	for i, schema := range schemas {
		response.Schemas[i] = *CreateSettingSchemaResponse(&schema)
	}
	
	return response
}