package models

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// SettingSchema represents the schema definition for settings
type SettingSchema struct {
	ID          uint            `json:"id" gorm:"primaryKey"`
	Category    string          `json:"category" gorm:"type:varchar(100);not null;index:idx_schema_category_key,unique"`
	Key         string          `json:"key" gorm:"type:varchar(255);not null;index:idx_schema_category_key,unique"`
	Name        string          `json:"name" gorm:"type:varchar(255);not null"`
	Description string          `json:"description,omitempty" gorm:"type:text"`
	DataType    DataType        `json:"data_type" gorm:"type:enum('string','number','boolean','json','array');not null"`
	IsEncrypted bool            `json:"is_encrypted" gorm:"not null;default:false"`
	IsPublic    bool            `json:"is_public" gorm:"not null;default:false"`
	IsRequired  bool            `json:"is_required" gorm:"not null;default:false"`
	IsReadOnly  bool            `json:"is_read_only" gorm:"not null;default:false"`
	IsDeletable bool            `json:"is_deletable" gorm:"not null;default:true"`
	
	// Validation and defaults
	ValidationRules json.RawMessage `json:"validation_rules,omitempty" gorm:"type:json"`
	DefaultValue    json.RawMessage `json:"default_value,omitempty" gorm:"type:json"`
	Options         json.RawMessage `json:"options,omitempty" gorm:"type:json"`
	
	// UI configuration
	UIConfig json.RawMessage `json:"ui_config,omitempty" gorm:"type:json"`
	
	// Metadata
	Version   int       `json:"version" gorm:"not null;default:1"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName specifies the table name for SettingSchema
func (SettingSchema) TableName() string {
	return "setting_schemas"
}

// UIConfiguration represents the UI configuration for a setting
type UIConfiguration struct {
	InputType   string                 `json:"input_type"`   // text, number, boolean, select, textarea, json, array
	Label       string                 `json:"label"`
	Placeholder string                 `json:"placeholder,omitempty"`
	HelpText    string                 `json:"help_text,omitempty"`
	Group       string                 `json:"group,omitempty"`
	Order       int                    `json:"order,omitempty"`
	Attributes  map[string]interface{} `json:"attributes,omitempty"`
}

// GetUIConfig returns the UI configuration
func (s *SettingSchema) GetUIConfig() (*UIConfiguration, error) {
	if s.UIConfig == nil {
		return nil, nil
	}
	
	var config UIConfiguration
	err := json.Unmarshal(s.UIConfig, &config)
	return &config, err
}

// SetUIConfig sets the UI configuration
func (s *SettingSchema) SetUIConfig(config *UIConfiguration) error {
	if config == nil {
		s.UIConfig = nil
		return nil
	}
	
	bytes, err := json.Marshal(config)
	if err != nil {
		return err
	}
	s.UIConfig = bytes
	return nil
}

// GetOptions returns the options for select/enum fields
func (s *SettingSchema) GetOptions() ([]OptionItem, error) {
	if s.Options == nil {
		return nil, nil
	}
	
	var options []OptionItem
	err := json.Unmarshal(s.Options, &options)
	return options, err
}

// SetOptions sets the options for select/enum fields
func (s *SettingSchema) SetOptions(options []OptionItem) error {
	if len(options) == 0 {
		s.Options = nil
		return nil
	}
	
	bytes, err := json.Marshal(options)
	if err != nil {
		return err
	}
	s.Options = bytes
	return nil
}

// GetValidationRules returns the validation rules
func (s *SettingSchema) GetValidationRules() (map[string]interface{}, error) {
	if s.ValidationRules == nil {
		return nil, nil
	}
	
	var rules map[string]interface{}
	err := json.Unmarshal(s.ValidationRules, &rules)
	return rules, err
}

// SetValidationRules sets the validation rules
func (s *SettingSchema) SetValidationRules(rules map[string]interface{}) error {
	if len(rules) == 0 {
		s.ValidationRules = nil
		return nil
	}
	
	bytes, err := json.Marshal(rules)
	if err != nil {
		return err
	}
	s.ValidationRules = bytes
	return nil
}

// GetDefaultValue returns the default value
func (s *SettingSchema) GetDefaultValue() (interface{}, error) {
	if s.DefaultValue == nil {
		return nil, nil
	}
	
	var value interface{}
	err := json.Unmarshal(s.DefaultValue, &value)
	return value, err
}

// SetDefaultValue sets the default value
func (s *SettingSchema) SetDefaultValue(value interface{}) error {
	if value == nil {
		s.DefaultValue = nil
		return nil
	}
	
	bytes, err := json.Marshal(value)
	if err != nil {
		return err
	}
	s.DefaultValue = bytes
	return nil
}

// CreateSettingFromSchema creates a new setting instance from this schema
func (s *SettingSchema) CreateSettingFromSchema(scopeType ScopeType, scopeID *uint, value interface{}) (*Setting, error) {
	setting := &Setting{
		ScopeType:       scopeType,
		ScopeID:         scopeID,
		Category:        s.Category,
		Key:             s.Key,
		Description:     s.Description,
		DataType:        s.DataType,
		IsEncrypted:     s.IsEncrypted,
		IsPublic:        s.IsPublic,
		IsRequired:      s.IsRequired,
		IsReadOnly:      s.IsReadOnly,
		ValidationRules: s.ValidationRules,
		DefaultValue:    s.DefaultValue,
		Options:         s.Options,
	}
	
	// Set the value or use default
	if value != nil {
		if err := setting.SetValue(value); err != nil {
			return nil, err
		}
	} else if s.DefaultValue != nil {
		setting.Value = s.DefaultValue
	}
	
	return setting, nil
}

// Validate validates the schema configuration
func (s *SettingSchema) Validate() error {
	if s.Category == "" {
		return gorm.ErrInvalidField
	}
	
	if s.Key == "" {
		return gorm.ErrInvalidField
	}
	
	if s.Name == "" {
		return gorm.ErrInvalidField
	}
	
	if !s.DataType.IsValid() {
		return gorm.ErrInvalidField
	}
	
	return nil
}

// BeforeCreate hook for GORM
func (s *SettingSchema) BeforeCreate(tx *gorm.DB) error {
	return s.Validate()
}

// BeforeUpdate hook for GORM
func (s *SettingSchema) BeforeUpdate(tx *gorm.DB) error {
	return s.Validate()
}

// SchemaFilter represents filters for querying schemas
type SchemaFilter struct {
	Category   string   `json:"category,omitempty"`
	Categories []string `json:"categories,omitempty"`
	Key        string   `json:"key,omitempty"`
	DataType   DataType `json:"data_type,omitempty"`
	IsPublic   *bool    `json:"is_public,omitempty"`
	IsRequired *bool    `json:"is_required,omitempty"`
	Search     string   `json:"search,omitempty"`
}