package settings

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/handlers"
	"github.com/tranthanhloi/wn-api-v3/pkg/middleware"
)

// RegisterRoutes registers all settings module routes
func RegisterRoutes(router *gin.RouterGroup, settingsHandler *handlers.SettingsHandler, schemaHandler *handlers.SettingSchemaHandler) {
	settings := router.Group("/settings")
	{
		// Settings routes
		settings.GET("", middleware.RequireAuth(), settingsHandler.GetSettings)
		settings.POST("", middleware.RequireAuth(), settingsHandler.CreateSetting)
		settings.GET("/:id", middleware.RequireAuth(), settingsHandler.GetSetting)
		settings.PUT("/:id", middleware.RequireAuth(), settingsHandler.UpdateSetting)
		settings.DELETE("/:id", middleware.RequireAuth(), settingsHandler.DeleteSetting)
		
		// Category routes
		settings.GET("/categories", settingsHandler.GetCategories)
		settings.GET("/category/:category", middleware.RequireAuth(), settingsHandler.GetSettingsByCategory)
		
		// Value routes
		settings.GET("/:category/:key/value", middleware.RequireAuth(), settingsHandler.GetSettingValue)
		settings.PUT("/:category/:key/value", middleware.RequireAuth(), settingsHandler.SetSettingValue)
	}
	
	// Setting schemas routes
	schemas := router.Group("/setting-schemas")
	{
		schemas.GET("", middleware.RequireAuth(), schemaHandler.GetSchemas)
		schemas.POST("", middleware.RequireAuth(), middleware.RequirePermission("settings.schema.create"), schemaHandler.CreateSchema)
		schemas.GET("/:id", middleware.RequireAuth(), schemaHandler.GetSchema)
		schemas.PUT("/:id", middleware.RequireAuth(), middleware.RequirePermission("settings.schema.update"), schemaHandler.UpdateSchema)
		schemas.DELETE("/:id", middleware.RequireAuth(), middleware.RequirePermission("settings.schema.delete"), schemaHandler.DeleteSchema)
		
		// Schema category and key routes
		schemas.GET("/category/:category", middleware.RequireAuth(), schemaHandler.GetSchemasByCategory)
		schemas.GET("/:category/:key", middleware.RequireAuth(), schemaHandler.GetSchemaByKey)
	}
}