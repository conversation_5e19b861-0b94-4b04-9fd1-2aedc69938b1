package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/models"
)

// SettingScope represents the scope of a setting (System, Tenant, Module, User)
type SettingScope string

const (
	ScopeSystem SettingScope = "system"
	ScopeTenant SettingScope = "tenant"
	ScopeModule SettingScope = "module"
	ScopeUser   SettingScope = "user"
)

// SettingValue represents a setting value with metadata
type SettingValue struct {
	Value       interface{}   `json:"value"`
	Scope       SettingScope  `json:"scope"`
	IsEncrypted bool          `json:"is_encrypted"`
	IsInherited bool          `json:"is_inherited"`
	Source      string        `json:"source"` // Which level this value comes from
	UpdatedAt   time.Time     `json:"updated_at"`
	UpdatedBy   uint          `json:"updated_by,omitempty"`
	Schema      *SettingSchema `json:"schema,omitempty"`
}

// SettingSchema defines validation and type information for a setting
type SettingSchema struct {
	Type        string                 `json:"type"`        // string, number, boolean, json, array
	Required    bool                   `json:"required"`
	Default     interface{}            `json:"default"`
	Validation  map[string]interface{} `json:"validation"`  // validation rules
	Enum        []interface{}          `json:"enum"`        // allowed values
	Description string                 `json:"description"`
	IsPublic    bool                   `json:"is_public"`   // can be read by frontend
	IsSecret    bool                   `json:"is_secret"`   // should be encrypted
}

// SettingFilter represents filters for querying settings
type SettingFilter struct {
	Scope       SettingScope `json:"scope,omitempty"`
	Category    string       `json:"category,omitempty"`
	Key         string       `json:"key,omitempty"`
	IsPublic    *bool        `json:"is_public,omitempty"`
	IsEncrypted *bool        `json:"is_encrypted,omitempty"`
	Search      string       `json:"search,omitempty"`
}

// BulkSettingOperation represents a bulk operation on settings
type BulkSettingOperation struct {
	Operation string      `json:"operation"` // set, delete
	Category  string      `json:"category"`
	Key       string      `json:"key"`
	Value     interface{} `json:"value,omitempty"`
	Schema    *SettingSchema `json:"schema,omitempty"`
}

// SettingContext provides context for setting operations
type SettingContext struct {
	TenantID  *uint  `json:"tenant_id,omitempty"`
	ModuleID  *uint  `json:"module_id,omitempty"`
	UserID    *uint  `json:"user_id,omitempty"`
	RequestID string `json:"request_id,omitempty"`
}

// SettingsService defines the main settings service interface
type SettingsService interface {
	// Core CRUD operations
	GetSettings(ctx context.Context, tenantID uint, filter models.SettingFilter) ([]*models.SettingResponse, error)
	GetSettingByID(ctx context.Context, tenantID, settingID uint) (*models.SettingResponse, error)
	CreateSetting(ctx context.Context, tenantID, userID uint, req *models.SettingCreateRequest) (*models.SettingResponse, error)
	UpdateSetting(ctx context.Context, tenantID, userID, settingID uint, req *models.SettingUpdateRequest) (*models.SettingResponse, error)
	DeleteSetting(ctx context.Context, tenantID, userID, settingID uint) error
	
	// Value operations
	GetSettingValue(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (interface{}, error)
	SetSettingValue(ctx context.Context, tenantID, userID uint, scopeType models.ScopeType, scopeID *uint, category, key string, value interface{}) error
	
	// Type-safe getters
	GetString(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (string, error)
	GetInt(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (int, error)
	GetFloat(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (float64, error)
	GetBool(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (bool, error)
	GetJSON(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string, dest interface{}) error
	
	// Hierarchical resolution
	ResolveSettingValue(ctx context.Context, tenantID uint, userID *uint, websiteID *uint, category, key string) (interface{}, error)
	
	// Cache management
	InvalidateCache(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) error
	WarmCache(ctx context.Context, tenantID uint, categories []string) error
	
	// Utilities
	ValidateSettingValue(ctx context.Context, category, key string, value interface{}) error
}

// SettingSchemaService defines the schema service interface
type SettingSchemaService interface {
	GetSchemas(ctx context.Context, filter models.SchemaFilter) ([]*models.SettingSchemaResponse, error)
	GetSchemaByID(ctx context.Context, schemaID uint) (*models.SettingSchemaResponse, error)
	GetSchemaByKey(ctx context.Context, category, key string) (*models.SettingSchemaResponse, error)
	CreateSchema(ctx context.Context, req *models.SettingSchemaCreateRequest) (*models.SettingSchemaResponse, error)
	UpdateSchema(ctx context.Context, schemaID uint, req *models.SettingSchemaUpdateRequest) (*models.SettingSchemaResponse, error)
	DeleteSchema(ctx context.Context, schemaID uint) error
}

// ValidationService handles setting validation
type ValidationService interface {
	// Validate a single setting
	ValidateSetting(ctx context.Context, schema *SettingSchema, value interface{}) error
	
	// Validate multiple settings
	ValidateSettings(ctx context.Context, settings map[string]map[string]interface{}, schemas map[string]map[string]*SettingSchema) error
	
	// Schema validation
	ValidateSchema(ctx context.Context, schema *SettingSchema) error
	
	// Type checking
	ValidateType(ctx context.Context, expectedType string, value interface{}) error
	
	// Custom validation rules
	RegisterValidator(name string, validator func(value interface{}, rule interface{}) error)
	GetValidator(name string) (func(value interface{}, rule interface{}) error, bool)
}

// EncryptionService handles encryption/decryption of sensitive settings
type EncryptionService interface {
	// Encrypt sensitive data
	Encrypt(ctx context.Context, plaintext []byte) ([]byte, error)
	
	// Decrypt sensitive data
	Decrypt(ctx context.Context, ciphertext []byte) ([]byte, error)
	
	// Encrypt JSON value
	EncryptJSON(ctx context.Context, value interface{}) ([]byte, error)
	
	// Decrypt JSON value
	DecryptJSON(ctx context.Context, ciphertext []byte, dest interface{}) error
	
	// Check if data is encrypted
	IsEncrypted(data []byte) bool
	
	// Key management
	RotateKeys(ctx context.Context) error
	GetCurrentKeyID(ctx context.Context) (string, error)
}

// CacheService handles caching strategies for settings
type CacheService interface {
	// Basic cache operations
	Get(ctx context.Context, key string, dest interface{}) error
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
	
	// Bulk operations
	GetMulti(ctx context.Context, keys []string) (map[string]interface{}, error)
	SetMulti(ctx context.Context, items map[string]interface{}, ttl time.Duration) error
	DeleteMulti(ctx context.Context, keys []string) error
	
	// Pattern operations
	DeletePattern(ctx context.Context, pattern string) error
	
	// Cache warming
	WarmCache(ctx context.Context, settingCtx *SettingContext, categories []string) error
	
	// Cache invalidation
	InvalidateSettingCache(ctx context.Context, settingCtx *SettingContext, category, key string) error
	InvalidateContextCache(ctx context.Context, settingCtx *SettingContext) error
	
	// Cache key generation
	GenerateKey(settingCtx *SettingContext, category, key string) string
	GeneratePatternKey(settingCtx *SettingContext, category string) string
	
	// Statistics
	GetStats(ctx context.Context) (*CacheStats, error)
}

// CacheStats represents cache statistics
type CacheStats struct {
	Hits        int64         `json:"hits"`
	Misses      int64         `json:"misses"`
	HitRate     float64       `json:"hit_rate"`
	Size        int64         `json:"size"`
	MemoryUsage int64         `json:"memory_usage"`
	Uptime      time.Duration `json:"uptime"`
}

// Common errors
var (
	ErrSettingNotFound   = fmt.Errorf("setting not found")
	ErrInvalidValue      = fmt.Errorf("invalid setting value")
	ErrValidationFailed  = fmt.Errorf("validation failed")
	ErrEncryptionFailed  = fmt.Errorf("encryption failed")
	ErrDecryptionFailed  = fmt.Errorf("decryption failed")
	ErrCacheOperationFailed = fmt.Errorf("cache operation failed")
	ErrSchemaNotFound    = fmt.Errorf("schema not found")
	ErrInvalidSchema     = fmt.Errorf("invalid schema")
	ErrUnauthorized      = fmt.Errorf("unauthorized operation")
	ErrScopeNotSupported = fmt.Errorf("scope not supported")
)

// DefaultTTL represents default cache TTL for different types of settings
const (
	DefaultSystemTTL = 30 * time.Minute
	DefaultTenantTTL = 15 * time.Minute
	DefaultModuleTTL = 10 * time.Minute
	DefaultUserTTL   = 5 * time.Minute
)

// CacheKeyPrefixes for different setting scopes
const (
	CacheKeyPrefixSystem = "settings:system"
	CacheKeyPrefixTenant = "settings:tenant"
	CacheKeyPrefixModule = "settings:module"
	CacheKeyPrefixUser   = "settings:user"
)