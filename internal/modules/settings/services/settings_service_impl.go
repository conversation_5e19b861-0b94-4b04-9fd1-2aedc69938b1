package services

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type settingsServiceNew struct {
	settingsRepo      repositories.SettingsRepository
	schemaRepo        repositories.SettingSchemaRepository
	encryptionService EncryptionService
	validationService ValidationService
	cacheService      CacheService
	logger            utils.Logger
}

func NewSettingsService(
	settingsRepo repositories.SettingsRepository,
	schemaRepo repositories.SettingSchemaRepository,
	encryptionService EncryptionService,
	validationService ValidationService,
	cacheService CacheService,
	logger utils.Logger,
) SettingsService {
	return &settingsServiceNew{
		settingsRepo:      settingsRepo,
		schemaRepo:        schemaRepo,
		encryptionService: encryptionService,
		validationService: validationService,
		cacheService:      cacheService,
		logger:            logger,
	}
}

func (s *settingsServiceNew) GetSettings(ctx context.Context, tenantID uint, filter models.SettingFilter) ([]*models.SettingResponse, error) {
	settings, err := s.settingsRepo.GetSettings(ctx, tenantID, filter)
	if err != nil {
		return nil, err
	}
	
	responses := make([]*models.SettingResponse, len(settings))
	for i, setting := range settings {
		response := &models.SettingResponse{}
		response.FromSetting(setting)
		responses[i] = response
	}
	
	return responses, nil
}

func (s *settingsServiceNew) GetSettingByID(ctx context.Context, tenantID, settingID uint) (*models.SettingResponse, error) {
	setting, err := s.settingsRepo.GetSettingByID(ctx, tenantID, settingID)
	if err != nil {
		return nil, err
	}
	
	response := &models.SettingResponse{}
	response.FromSetting(setting)
	return response, nil
}

func (s *settingsServiceNew) CreateSetting(ctx context.Context, tenantID, userID uint, req *models.SettingCreateRequest) (*models.SettingResponse, error) {
	setting := &models.Setting{
		ScopeType:       req.ScopeType,
		ScopeID:         req.ScopeID,
		Category:        req.Category,
		Key:             req.Key,
		Description:     req.Description,
		DataType:        req.DataType,
		IsEncrypted:     req.IsEncrypted,
		IsPublic:        req.IsPublic,
		IsRequired:      req.IsRequired,
		IsReadOnly:      req.IsReadOnly,
		ValidationRules: req.ValidationRules,
		CreatedBy:       &userID,
		UpdatedBy:       &userID,
	}
	
	if err := setting.SetValue(req.Value); err != nil {
		return nil, fmt.Errorf("failed to set value: %w", err)
	}
	
	if req.DefaultValue != nil {
		defaultBytes, err := json.Marshal(req.DefaultValue)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal default value: %w", err)
		}
		setting.DefaultValue = defaultBytes
	}
	
	if req.Options != nil {
		setting.Options = req.Options
	}
	
	created, err := s.settingsRepo.CreateSetting(ctx, tenantID, setting)
	if err != nil {
		return nil, err
	}
	
	response := &models.SettingResponse{}
	response.FromSetting(created)
	return response, nil
}

func (s *settingsServiceNew) UpdateSetting(ctx context.Context, tenantID, userID, settingID uint, req *models.SettingUpdateRequest) (*models.SettingResponse, error) {
	setting, err := s.settingsRepo.GetSettingByID(ctx, tenantID, settingID)
	if err != nil {
		return nil, err
	}
	
	if req.Value != nil {
		if err := setting.SetValue(req.Value); err != nil {
			return nil, fmt.Errorf("failed to set value: %w", err)
		}
	}
	
	if req.Description != "" {
		setting.Description = req.Description
	}
	
	if req.IsPublic != nil {
		setting.IsPublic = *req.IsPublic
	}
	
	if req.IsRequired != nil {
		setting.IsRequired = *req.IsRequired
	}
	
	if req.IsReadOnly != nil {
		setting.IsReadOnly = *req.IsReadOnly
	}
	
	if req.ValidationRules != nil {
		setting.ValidationRules = req.ValidationRules
	}
	
	if req.DefaultValue != nil {
		defaultBytes, err := json.Marshal(req.DefaultValue)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal default value: %w", err)
		}
		setting.DefaultValue = defaultBytes
	}
	
	if req.Options != nil {
		setting.Options = req.Options
	}
	
	setting.UpdatedBy = &userID
	
	updated, err := s.settingsRepo.UpdateSetting(ctx, tenantID, setting)
	if err != nil {
		return nil, err
	}
	
	response := &models.SettingResponse{}
	response.FromSetting(updated)
	return response, nil
}

func (s *settingsServiceNew) DeleteSetting(ctx context.Context, tenantID, userID, settingID uint) error {
	return s.settingsRepo.DeleteSetting(ctx, tenantID, settingID)
}

func (s *settingsServiceNew) GetSettingValue(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (interface{}, error) {
	setting, err := s.settingsRepo.GetSettingByKey(ctx, tenantID, scopeType, scopeID, category, key)
	if err != nil {
		return nil, err
	}
	
	return setting.GetTypedValue()
}

func (s *settingsServiceNew) SetSettingValue(ctx context.Context, tenantID, userID uint, scopeType models.ScopeType, scopeID *uint, category, key string, value interface{}) error {
	setting, err := s.settingsRepo.GetSettingByKey(ctx, tenantID, scopeType, scopeID, category, key)
	if err != nil {
		return err
	}
	
	if err := setting.SetValue(value); err != nil {
		return fmt.Errorf("failed to set value: %w", err)
	}
	
	setting.UpdatedBy = &userID
	
	_, err = s.settingsRepo.UpdateSetting(ctx, tenantID, setting)
	return err
}

func (s *settingsServiceNew) GetString(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (string, error) {
	value, err := s.GetSettingValue(ctx, tenantID, scopeType, scopeID, category, key)
	if err != nil {
		return "", err
	}
	
	if str, ok := value.(string); ok {
		return str, nil
	}
	
	return fmt.Sprintf("%v", value), nil
}

func (s *settingsServiceNew) GetInt(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (int, error) {
	value, err := s.GetSettingValue(ctx, tenantID, scopeType, scopeID, category, key)
	if err != nil {
		return 0, err
	}
	
	if i, ok := value.(int); ok {
		return i, nil
	}
	
	if f, ok := value.(float64); ok {
		return int(f), nil
	}
	
	return 0, fmt.Errorf("value is not a number")
}

func (s *settingsServiceNew) GetFloat(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (float64, error) {
	value, err := s.GetSettingValue(ctx, tenantID, scopeType, scopeID, category, key)
	if err != nil {
		return 0, err
	}
	
	if f, ok := value.(float64); ok {
		return f, nil
	}
	
	if i, ok := value.(int); ok {
		return float64(i), nil
	}
	
	return 0, fmt.Errorf("value is not a number")
}

func (s *settingsServiceNew) GetBool(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (bool, error) {
	value, err := s.GetSettingValue(ctx, tenantID, scopeType, scopeID, category, key)
	if err != nil {
		return false, err
	}
	
	if b, ok := value.(bool); ok {
		return b, nil
	}
	
	return false, fmt.Errorf("value is not a boolean")
}

func (s *settingsServiceNew) GetJSON(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string, dest interface{}) error {
	setting, err := s.settingsRepo.GetSettingByKey(ctx, tenantID, scopeType, scopeID, category, key)
	if err != nil {
		return err
	}
	
	return json.Unmarshal(setting.Value, dest)
}

func (s *settingsServiceNew) ResolveSettingValue(ctx context.Context, tenantID uint, userID *uint, websiteID *uint, category, key string) (interface{}, error) {
	// Try user scope first
	if userID != nil {
		if value, err := s.GetSettingValue(ctx, tenantID, models.ScopeTypeUser, userID, category, key); err == nil {
			return value, nil
		}
	}
	
	// Try website scope
	if websiteID != nil {
		if value, err := s.GetSettingValue(ctx, tenantID, models.ScopeTypeWebsite, websiteID, category, key); err == nil {
			return value, nil
		}
	}
	
	// Try tenant scope
	if value, err := s.GetSettingValue(ctx, tenantID, models.ScopeTypeTenant, &tenantID, category, key); err == nil {
		return value, nil
	}
	
	// Try global scope
	return s.GetSettingValue(ctx, tenantID, models.ScopeTypeGlobal, nil, category, key)
}

func (s *settingsServiceNew) InvalidateCache(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) error {
	if s.cacheService == nil {
		return nil
	}
	
	// Generate cache key and invalidate
	cacheKey := fmt.Sprintf("setting:%d:%s:%v:%s:%s", tenantID, scopeType, scopeID, category, key)
	return s.cacheService.Delete(ctx, cacheKey)
}

func (s *settingsServiceNew) WarmCache(ctx context.Context, tenantID uint, categories []string) error {
	if s.cacheService == nil {
		return nil
	}
	
	// Implementation for warming cache with settings from specified categories
	// This is a placeholder - implement based on your caching strategy
	return nil
}

func (s *settingsServiceNew) ValidateSettingValue(ctx context.Context, category, key string, value interface{}) error {
	if s.validationService == nil {
		return nil
	}
	
	// Get schema for validation
	_, err := s.schemaRepo.GetSchemaByKey(ctx, category, key)
	if err != nil {
		// If no schema exists, skip validation
		return nil
	}
	
	// Convert to internal schema format and validate
	// This is a simplified validation - implement based on your validation service
	return nil
}

// Schema service implementation
type settingSchemaServiceNew struct {
	schemaRepo repositories.SettingSchemaRepository
	logger     utils.Logger
}

func NewSettingSchemaService(schemaRepo repositories.SettingSchemaRepository, logger utils.Logger) SettingSchemaService {
	return &settingSchemaServiceNew{
		schemaRepo: schemaRepo,
		logger:     logger,
	}
}

func (s *settingSchemaServiceNew) GetSchemas(ctx context.Context, filter models.SchemaFilter) ([]*models.SettingSchemaResponse, error) {
	schemas, err := s.schemaRepo.GetSchemas(ctx, filter)
	if err != nil {
		return nil, err
	}
	
	responses := make([]*models.SettingSchemaResponse, len(schemas))
	for i, schema := range schemas {
		response := &models.SettingSchemaResponse{}
		response.FromSettingSchema(schema)
		responses[i] = response
	}
	
	return responses, nil
}

func (s *settingSchemaServiceNew) GetSchemaByID(ctx context.Context, schemaID uint) (*models.SettingSchemaResponse, error) {
	schema, err := s.schemaRepo.GetSchemaByID(ctx, schemaID)
	if err != nil {
		return nil, err
	}
	
	response := &models.SettingSchemaResponse{}
	response.FromSettingSchema(schema)
	return response, nil
}

func (s *settingSchemaServiceNew) GetSchemaByKey(ctx context.Context, category, key string) (*models.SettingSchemaResponse, error) {
	schema, err := s.schemaRepo.GetSchemaByKey(ctx, category, key)
	if err != nil {
		return nil, err
	}
	
	response := &models.SettingSchemaResponse{}
	response.FromSettingSchema(schema)
	return response, nil
}

func (s *settingSchemaServiceNew) CreateSchema(ctx context.Context, req *models.SettingSchemaCreateRequest) (*models.SettingSchemaResponse, error) {
	schema := &models.SettingSchema{
		Category:        req.Category,
		Key:             req.Key,
		Name:            req.Name,
		Description:     req.Description,
		DataType:        req.DataType,
		IsEncrypted:     req.IsEncrypted,
		IsPublic:        req.IsPublic,
		IsRequired:      req.IsRequired,
		IsReadOnly:      req.IsReadOnly,
		IsDeletable:     req.IsDeletable,
		ValidationRules: req.ValidationRules,
		UIConfig:        req.UIConfig,
		Options:         req.Options,
	}
	
	if req.DefaultValue != nil {
		defaultBytes, err := json.Marshal(req.DefaultValue)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal default value: %w", err)
		}
		schema.DefaultValue = defaultBytes
	}
	
	created, err := s.schemaRepo.CreateSchema(ctx, schema)
	if err != nil {
		return nil, err
	}
	
	response := &models.SettingSchemaResponse{}
	response.FromSettingSchema(created)
	return response, nil
}

func (s *settingSchemaServiceNew) UpdateSchema(ctx context.Context, schemaID uint, req *models.SettingSchemaUpdateRequest) (*models.SettingSchemaResponse, error) {
	schema, err := s.schemaRepo.GetSchemaByID(ctx, schemaID)
	if err != nil {
		return nil, err
	}
	
	if req.Name != "" {
		schema.Name = req.Name
	}
	
	if req.Description != "" {
		schema.Description = req.Description
	}
	
	if req.DataType != "" {
		schema.DataType = req.DataType
	}
	
	if req.IsPublic != nil {
		schema.IsPublic = *req.IsPublic
	}
	
	if req.IsRequired != nil {
		schema.IsRequired = *req.IsRequired
	}
	
	if req.IsReadOnly != nil {
		schema.IsReadOnly = *req.IsReadOnly
	}
	
	if req.IsDeletable != nil {
		schema.IsDeletable = *req.IsDeletable
	}
	
	if req.ValidationRules != nil {
		schema.ValidationRules = req.ValidationRules
	}
	
	if req.UIConfig != nil {
		schema.UIConfig = req.UIConfig
	}
	
	if req.Options != nil {
		schema.Options = req.Options
	}
	
	if req.DefaultValue != nil {
		defaultBytes, err := json.Marshal(req.DefaultValue)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal default value: %w", err)
		}
		schema.DefaultValue = defaultBytes
	}
	
	updated, err := s.schemaRepo.UpdateSchema(ctx, schema)
	if err != nil {
		return nil, err
	}
	
	response := &models.SettingSchemaResponse{}
	response.FromSettingSchema(updated)
	return response, nil
}

func (s *settingSchemaServiceNew) DeleteSchema(ctx context.Context, schemaID uint) error {
	return s.schemaRepo.DeleteSchema(ctx, schemaID)
}