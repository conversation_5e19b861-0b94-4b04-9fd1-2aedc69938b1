package services

import (
	"context"
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"

)

// validationService implements the ValidationService interface
type validationService struct {
	validators map[string]func(value interface{}, rule interface{}) error
}

// NewValidationService creates a new validation service
func NewValidationService() ValidationService {
	service := &validationService{
		validators: make(map[string]func(value interface{}, rule interface{}) error),
	}
	
	// Register built-in validators
	service.registerBuiltinValidators()
	
	return service
}

// ValidateSetting validates a single setting against its schema
func (v *validationService) ValidateSetting(ctx context.Context, schema *SettingSchema, value interface{}) error {
	if schema == nil {
		return nil // No validation if no schema
	}

	// Check if value is required
	if schema.Required && (value == nil || v.isEmpty(value)) {
		return fmt.Errorf("value is required")
	}

	// Skip validation if value is nil and not required
	if value == nil {
		return nil
	}

	// Validate type
	if err := v.ValidateType(ctx, schema.Type, value); err != nil {
		return fmt.Errorf("type validation failed: %w", err)
	}

	// Validate against enum values
	if len(schema.Enum) > 0 {
		if err := v.validateEnum(value, schema.Enum); err != nil {
			return fmt.Errorf("enum validation failed: %w", err)
		}
	}

	// Apply custom validation rules
	if err := v.applyValidationRules(value, schema.Validation); err != nil {
		return fmt.Errorf("custom validation failed: %w", err)
	}

	return nil
}

// ValidateSettings validates multiple settings against their schemas
func (v *validationService) ValidateSettings(ctx context.Context, settings map[string]map[string]interface{}, schemas map[string]map[string]*SettingSchema) error {
	for category, categorySettings := range settings {
		categorySchemas, hasSchemas := schemas[category]
		
		for key, value := range categorySettings {
			if hasSchemas {
				if schema, hasSchema := categorySchemas[key]; hasSchema {
					if err := v.ValidateSetting(ctx, schema, value); err != nil {
						return fmt.Errorf("validation failed for %s.%s: %w", category, key, err)
					}
				}
			}
		}
	}
	return nil
}

// ValidateSchema validates a schema definition
func (v *validationService) ValidateSchema(ctx context.Context, schema *SettingSchema) error {
	if schema == nil {
		return fmt.Errorf("schema cannot be nil")
	}

	// Validate type
	validTypes := []string{"string", "number", "boolean", "json", "array"}
	if !v.containsString(validTypes, schema.Type) {
		return fmt.Errorf("invalid type: %s", schema.Type)
	}

	// Validate default value against type
	if schema.Default != nil {
		if err := v.ValidateType(ctx, schema.Type, schema.Default); err != nil {
			return fmt.Errorf("default value type validation failed: %w", err)
		}
	}

	// Validate enum values
	if len(schema.Enum) > 0 {
		for i, enumValue := range schema.Enum {
			if err := v.ValidateType(ctx, schema.Type, enumValue); err != nil {
				return fmt.Errorf("enum value %d type validation failed: %w", i, err)
			}
		}
	}

	// Validate validation rules
	if err := v.validateValidationRules(schema.Type, schema.Validation); err != nil {
		return fmt.Errorf("validation rules validation failed: %w", err)
	}

	return nil
}

// ValidateType validates that a value matches the expected type
func (v *validationService) ValidateType(ctx context.Context, expectedType string, value interface{}) error {
	if value == nil {
		return nil // Allow nil values
	}

	switch expectedType {
	case "string":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("expected string, got %T", value)
		}
	case "number":
		if !v.isNumber(value) {
			return fmt.Errorf("expected number, got %T", value)
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("expected boolean, got %T", value)
		}
	case "json":
		// JSON can be any valid JSON value
		if !v.isValidJSON(value) {
			return fmt.Errorf("expected valid JSON value")
		}
	case "array":
		if !v.isArray(value) {
			return fmt.Errorf("expected array, got %T", value)
		}
	default:
		return fmt.Errorf("unknown type: %s", expectedType)
	}

	return nil
}

// RegisterValidator registers a custom validator
func (v *validationService) RegisterValidator(name string, validator func(value interface{}, rule interface{}) error) {
	v.validators[name] = validator
}

// GetValidator returns a validator by name
func (v *validationService) GetValidator(name string) (func(value interface{}, rule interface{}) error, bool) {
	validator, exists := v.validators[name]
	return validator, exists
}

// Helper methods

func (v *validationService) isEmpty(value interface{}) bool {
	if value == nil {
		return true
	}

	switch v := value.(type) {
	case string:
		return strings.TrimSpace(v) == ""
	case []interface{}:
		return len(v) == 0
	case map[string]interface{}:
		return len(v) == 0
	default:
		return false
	}
}

func (v *validationService) isNumber(value interface{}) bool {
	switch value.(type) {
	case int, int8, int16, int32, int64:
		return true
	case uint, uint8, uint16, uint32, uint64:
		return true
	case float32, float64:
		return true
	default:
		return false
	}
}

func (v *validationService) isArray(value interface{}) bool {
	switch value.(type) {
	case []interface{}:
		return true
	default:
		// Check if it's a slice using reflection
		rv := reflect.ValueOf(value)
		return rv.Kind() == reflect.Slice || rv.Kind() == reflect.Array
	}
}

func (v *validationService) isValidJSON(value interface{}) bool {
	// For our purposes, any JSON-marshalable value is valid
	switch value.(type) {
	case string, bool, int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
		return true
	case []interface{}, map[string]interface{}:
		return true
	case nil:
		return true
	default:
		return false
	}
}

func (v *validationService) validateEnum(value interface{}, enum []interface{}) error {
	for _, enumValue := range enum {
		if v.valuesEqual(value, enumValue) {
			return nil
		}
	}
	return fmt.Errorf("value must be one of: %v", enum)
}

func (v *validationService) valuesEqual(a, b interface{}) bool {
	return reflect.DeepEqual(a, b)
}

func (v *validationService) applyValidationRules(value interface{}, rules map[string]interface{}) error {
	if rules == nil {
		return nil
	}

	for ruleName, ruleValue := range rules {
		if validator, exists := v.validators[ruleName]; exists {
			if err := validator(value, ruleValue); err != nil {
				return fmt.Errorf("rule '%s' failed: %w", ruleName, err)
			}
		} else {
			return fmt.Errorf("unknown validation rule: %s", ruleName)
		}
	}

	return nil
}

func (v *validationService) validateValidationRules(schemaType string, rules map[string]interface{}) error {
	if rules == nil {
		return nil
	}

	for ruleName, ruleValue := range rules {
		if _, exists := v.validators[ruleName]; !exists {
			return fmt.Errorf("unknown validation rule: %s", ruleName)
		}

		// Validate rule value based on rule type
		if err := v.validateRuleValue(schemaType, ruleName, ruleValue); err != nil {
			return fmt.Errorf("invalid rule value for '%s': %w", ruleName, err)
		}
	}

	return nil
}

func (v *validationService) validateRuleValue(schemaType, ruleName string, ruleValue interface{}) error {
	switch ruleName {
	case "min", "max":
		if schemaType == "number" {
			if !v.isNumber(ruleValue) {
				return fmt.Errorf("min/max rule value must be a number")
			}
		} else if schemaType == "string" {
			if !v.isNumber(ruleValue) {
				return fmt.Errorf("min/max rule value must be a number for string length")
			}
		}
	case "pattern":
		if schemaType == "string" {
			if _, ok := ruleValue.(string); !ok {
				return fmt.Errorf("pattern rule value must be a string")
			}
			// Test if it's a valid regex
			if _, err := regexp.Compile(ruleValue.(string)); err != nil {
				return fmt.Errorf("pattern rule value must be a valid regex: %w", err)
			}
		}
	case "items":
		if schemaType == "array" {
			// items rule can be a schema or array of schemas
			// For now, we'll just check if it's a valid type
		}
	}

	return nil
}

func (v *validationService) containsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func (v *validationService) registerBuiltinValidators() {
	// String validators
	v.RegisterValidator("min", func(value interface{}, rule interface{}) error {
		minVal, ok := rule.(float64)
		if !ok {
			return fmt.Errorf("min rule must be a number")
		}

		switch v := value.(type) {
		case string:
			if float64(len(v)) < minVal {
				return fmt.Errorf("string length must be at least %v", minVal)
			}
		case int:
			if float64(v) < minVal {
				return fmt.Errorf("value must be at least %v", minVal)
			}
		case float64:
			if v < minVal {
				return fmt.Errorf("value must be at least %v", minVal)
			}
		default:
			return fmt.Errorf("min validation not supported for type %T", value)
		}
		return nil
	})

	v.RegisterValidator("max", func(value interface{}, rule interface{}) error {
		maxVal, ok := rule.(float64)
		if !ok {
			return fmt.Errorf("max rule must be a number")
		}

		switch v := value.(type) {
		case string:
			if float64(len(v)) > maxVal {
				return fmt.Errorf("string length must be at most %v", maxVal)
			}
		case int:
			if float64(v) > maxVal {
				return fmt.Errorf("value must be at most %v", maxVal)
			}
		case float64:
			if v > maxVal {
				return fmt.Errorf("value must be at most %v", maxVal)
			}
		default:
			return fmt.Errorf("max validation not supported for type %T", value)
		}
		return nil
	})

	v.RegisterValidator("pattern", func(value interface{}, rule interface{}) error {
		pattern, ok := rule.(string)
		if !ok {
			return fmt.Errorf("pattern rule must be a string")
		}

		str, ok := value.(string)
		if !ok {
			return fmt.Errorf("pattern validation only applies to strings")
		}

		matched, err := regexp.MatchString(pattern, str)
		if err != nil {
			return fmt.Errorf("invalid regex pattern: %w", err)
		}

		if !matched {
			return fmt.Errorf("string does not match pattern: %s", pattern)
		}

		return nil
	})

	v.RegisterValidator("format", func(value interface{}, rule interface{}) error {
		format, ok := rule.(string)
		if !ok {
			return fmt.Errorf("format rule must be a string")
		}

		str, ok := value.(string)
		if !ok {
			return fmt.Errorf("format validation only applies to strings")
		}

		return v.validateFormat(str, format)
	})

	v.RegisterValidator("unique", func(value interface{}, rule interface{}) error {
		isUnique, ok := rule.(bool)
		if !ok {
			return fmt.Errorf("unique rule must be a boolean")
		}

		if isUnique {
			// In a real implementation, this would check uniqueness against the database
			// For now, we'll just validate that the value is not empty
			if v.isEmpty(value) {
				return fmt.Errorf("unique values cannot be empty")
			}
		}

		return nil
	})

	v.RegisterValidator("email", func(value interface{}, rule interface{}) error {
		str, ok := value.(string)
		if !ok {
			return fmt.Errorf("email validation only applies to strings")
		}

		return v.validateEmail(str)
	})

	v.RegisterValidator("url", func(value interface{}, rule interface{}) error {
		str, ok := value.(string)
		if !ok {
			return fmt.Errorf("url validation only applies to strings")
		}

		return v.validateURL(str)
	})
}

func (v *validationService) validateFormat(value, format string) error {
	switch format {
	case "email":
		return v.validateEmail(value)
	case "url":
		return v.validateURL(value)
	case "uuid":
		return v.validateUUID(value)
	case "date":
		return v.validateDate(value)
	case "time":
		return v.validateTime(value)
	case "datetime":
		return v.validateDateTime(value)
	case "ipv4":
		return v.validateIPv4(value)
	case "ipv6":
		return v.validateIPv6(value)
	case "mac":
		return v.validateMAC(value)
	default:
		return fmt.Errorf("unknown format: %s", format)
	}
}

func (v *validationService) validateEmail(email string) error {
	// Simple email validation regex
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return fmt.Errorf("invalid email format")
	}
	return nil
}

func (v *validationService) validateURL(url string) error {
	// Simple URL validation
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return fmt.Errorf("invalid URL format")
	}
	return nil
}

func (v *validationService) validateUUID(uuid string) error {
	// Simple UUID validation
	uuidRegex := regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)
	if !uuidRegex.MatchString(uuid) {
		return fmt.Errorf("invalid UUID format")
	}
	return nil
}

func (v *validationService) validateDate(date string) error {
	// Simple date validation (YYYY-MM-DD)
	dateRegex := regexp.MustCompile(`^\d{4}-\d{2}-\d{2}$`)
	if !dateRegex.MatchString(date) {
		return fmt.Errorf("invalid date format (expected YYYY-MM-DD)")
	}
	return nil
}

func (v *validationService) validateTime(time string) error {
	// Simple time validation (HH:MM:SS)
	timeRegex := regexp.MustCompile(`^\d{2}:\d{2}:\d{2}$`)
	if !timeRegex.MatchString(time) {
		return fmt.Errorf("invalid time format (expected HH:MM:SS)")
	}
	return nil
}

func (v *validationService) validateDateTime(datetime string) error {
	// Simple datetime validation (YYYY-MM-DDTHH:MM:SS)
	datetimeRegex := regexp.MustCompile(`^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}`)
	if !datetimeRegex.MatchString(datetime) {
		return fmt.Errorf("invalid datetime format (expected YYYY-MM-DDTHH:MM:SS)")
	}
	return nil
}

func (v *validationService) validateIPv4(ip string) error {
	// Simple IPv4 validation
	ipRegex := regexp.MustCompile(`^(\d{1,3}\.){3}\d{1,3}$`)
	if !ipRegex.MatchString(ip) {
		return fmt.Errorf("invalid IPv4 format")
	}
	
	// Check each octet is 0-255
	parts := strings.Split(ip, ".")
	for _, part := range parts {
		num, err := strconv.Atoi(part)
		if err != nil || num < 0 || num > 255 {
			return fmt.Errorf("invalid IPv4 format")
		}
	}
	
	return nil
}

func (v *validationService) validateIPv6(ip string) error {
	// Simple IPv6 validation
	ipRegex := regexp.MustCompile(`^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$`)
	if !ipRegex.MatchString(ip) {
		return fmt.Errorf("invalid IPv6 format")
	}
	return nil
}

func (v *validationService) validateMAC(mac string) error {
	// Simple MAC address validation
	macRegex := regexp.MustCompile(`^([0-9a-fA-F]{2}[:-]){5}([0-9a-fA-F]{2})$`)
	if !macRegex.MatchString(mac) {
		return fmt.Errorf("invalid MAC address format")
	}
	return nil
}