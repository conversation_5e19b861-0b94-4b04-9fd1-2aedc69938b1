package services

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/cache"
)

// ServiceFactory creates and configures all settings services
type ServiceFactory struct {
	cacheClient      cache.JsonCache
	cacheConfig      *CacheConfig
	encryptionConfig *EncryptionConfig
}

// EncryptionConfig holds encryption configuration
type EncryptionConfig struct {
	KeyRotationInterval time.Duration `json:"key_rotation_interval"`
	MaxKeyAge          time.Duration `json:"max_key_age"`
	EnableKeyRotation  bool          `json:"enable_key_rotation"`
}

// ServiceBundle contains all settings services
type ServiceBundle struct {
	Settings   SettingsService
	Validation ValidationService
	Encryption EncryptionService
	Cache      CacheService
}

// NewServiceFactory creates a new service factory
func NewServiceFactory(cacheClient cache.JsonCache, cacheConfig *CacheConfig, encryptionConfig *EncryptionConfig) *ServiceFactory {
	if cacheConfig == nil {
		cacheConfig = DefaultCacheConfig()
	}
	
	if encryptionConfig == nil {
		encryptionConfig = DefaultEncryptionConfig()
	}

	return &ServiceFactory{
		cacheClient:      cacheClient,
		cacheConfig:      cacheConfig,
		encryptionConfig: encryptionConfig,
	}
}

// DefaultEncryptionConfig returns default encryption configuration
func DefaultEncryptionConfig() *EncryptionConfig {
	return &EncryptionConfig{
		KeyRotationInterval: 24 * time.Hour,
		MaxKeyAge:          365 * 24 * time.Hour,
		EnableKeyRotation:  true,
	}
}

// CreateServices creates all settings services - DEPRECATED
func (f *ServiceFactory) CreateServices() (*ServiceBundle, error) {
	// Create validation service
	validationService := NewValidationService()

	// Create encryption service
	encryptionService, _ := NewEncryptionService()

	// Create cache service
	cacheService := NewCacheService(f.cacheClient, f.cacheConfig)

	return &ServiceBundle{
		Settings:   nil, // Use settings module instead
		Validation: validationService,
		Encryption: encryptionService,
		Cache:      cacheService,
	}, nil
}

// CreateLayeredServices creates services with layered caching - DEPRECATED
func (f *ServiceFactory) CreateLayeredServices(l1Cache cache.JsonCache, l2Cache cache.JsonCache) (*ServiceBundle, error) {
	// Create validation service
	validationService := NewValidationService()

	// Create encryption service
	encryptionService, _ := NewEncryptionService()

	// Create layered cache service
	l1CacheService := NewCacheService(l1Cache, f.cacheConfig)
	l2CacheService := NewCacheService(l2Cache, f.cacheConfig)
	
	layeredCacheService := NewLayeredCacheService(l1CacheService, l2CacheService, nil)

	return &ServiceBundle{
		Settings:   nil, // Use settings module instead
		Validation: validationService,
		Encryption: encryptionService,
		Cache:      layeredCacheService,
	}, nil
}

// ServiceManager manages the lifecycle of settings services
type ServiceManager struct {
	services *ServiceBundle
	factory  *ServiceFactory
}

// NewServiceManager creates a new service manager
func NewServiceManager(factory *ServiceFactory) *ServiceManager {
	return &ServiceManager{
		factory: factory,
	}
}

// Start initializes and starts all services
func (sm *ServiceManager) Start() error {
	services, err := sm.factory.CreateServices()
	if err != nil {
		return err
	}
	
	sm.services = services
	return nil
}

// Stop gracefully stops all services
func (sm *ServiceManager) Stop() error {
	// Cleanup if needed
	return nil
}

// GetServices returns the service bundle
func (sm *ServiceManager) GetServices() *ServiceBundle {
	return sm.services
}