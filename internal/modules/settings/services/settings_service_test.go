package services

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// MockCacheService is a mock implementation of CacheService
type MockCacheService struct {
	mock.Mock
}

func (m *MockCacheService) Get(ctx context.Context, key string, dest interface{}) error {
	args := m.Called(ctx, key, dest)
	return args.Error(0)
}

func (m *MockCacheService) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	args := m.Called(ctx, key, value, ttl)
	return args.Error(0)
}

func (m *MockCacheService) Delete(ctx context.Context, key string) error {
	args := m.Called(ctx, key)
	return args.Error(0)
}

func (m *MockCacheService) GetMulti(ctx context.Context, keys []string) (map[string]interface{}, error) {
	args := m.Called(ctx, keys)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockCacheService) SetMulti(ctx context.Context, items map[string]interface{}, ttl time.Duration) error {
	args := m.Called(ctx, items, ttl)
	return args.Error(0)
}

func (m *MockCacheService) DeleteMulti(ctx context.Context, keys []string) error {
	args := m.Called(ctx, keys)
	return args.Error(0)
}

func (m *MockCacheService) DeletePattern(ctx context.Context, pattern string) error {
	args := m.Called(ctx, pattern)
	return args.Error(0)
}

func (m *MockCacheService) WarmCache(ctx context.Context, settingCtx *SettingContext, categories []string) error {
	args := m.Called(ctx, settingCtx, categories)
	return args.Error(0)
}

func (m *MockCacheService) InvalidateSettingCache(ctx context.Context, settingCtx *SettingContext, category, key string) error {
	args := m.Called(ctx, settingCtx, category, key)
	return args.Error(0)
}

func (m *MockCacheService) InvalidateContextCache(ctx context.Context, settingCtx *SettingContext) error {
	args := m.Called(ctx, settingCtx)
	return args.Error(0)
}

func (m *MockCacheService) GenerateKey(settingCtx *SettingContext, category, key string) string {
	args := m.Called(settingCtx, category, key)
	return args.String(0)
}

func (m *MockCacheService) GeneratePatternKey(settingCtx *SettingContext, category string) string {
	args := m.Called(settingCtx, category)
	return args.String(0)
}

func (m *MockCacheService) GetStats(ctx context.Context) (*CacheStats, error) {
	args := m.Called(ctx)
	return args.Get(0).(*CacheStats), args.Error(1)
}

// MockValidationService is a mock implementation of ValidationService
type MockValidationService struct {
	mock.Mock
}

func (m *MockValidationService) ValidateSetting(ctx context.Context, schema *SettingSchema, value interface{}) error {
	args := m.Called(ctx, schema, value)
	return args.Error(0)
}

func (m *MockValidationService) ValidateSettings(ctx context.Context, settings map[string]map[string]interface{}, schemas map[string]map[string]*SettingSchema) error {
	args := m.Called(ctx, settings, schemas)
	return args.Error(0)
}

func (m *MockValidationService) ValidateSchema(ctx context.Context, schema *SettingSchema) error {
	args := m.Called(ctx, schema)
	return args.Error(0)
}

func (m *MockValidationService) ValidateType(ctx context.Context, expectedType string, value interface{}) error {
	args := m.Called(ctx, expectedType, value)
	return args.Error(0)
}

func (m *MockValidationService) RegisterValidator(name string, validator func(value interface{}, rule interface{}) error) {
	m.Called(name, validator)
}

func (m *MockValidationService) GetValidator(name string) (func(value interface{}, rule interface{}) error, bool) {
	args := m.Called(name)
	return args.Get(0).(func(value interface{}, rule interface{}) error), args.Bool(1)
}

// MockEncryptionService is a mock implementation of EncryptionService
type MockEncryptionService struct {
	mock.Mock
}

func (m *MockEncryptionService) Encrypt(ctx context.Context, plaintext []byte) ([]byte, error) {
	args := m.Called(ctx, plaintext)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockEncryptionService) Decrypt(ctx context.Context, ciphertext []byte) ([]byte, error) {
	args := m.Called(ctx, ciphertext)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockEncryptionService) EncryptJSON(ctx context.Context, value interface{}) ([]byte, error) {
	args := m.Called(ctx, value)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockEncryptionService) DecryptJSON(ctx context.Context, ciphertext []byte, dest interface{}) error {
	args := m.Called(ctx, ciphertext, dest)
	return args.Error(0)
}

func (m *MockEncryptionService) IsEncrypted(data []byte) bool {
	args := m.Called(data)
	return args.Bool(0)
}

func (m *MockEncryptionService) RotateKeys(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockEncryptionService) GetCurrentKeyID(ctx context.Context) (string, error) {
	args := m.Called(ctx)
	return args.String(0), args.Error(1)
}

func setupTestService() (SettingsService, *MockCacheService, *MockValidationService, *MockEncryptionService) {
	cache := &MockCacheService{}
	validation := &MockValidationService{}
	encryption := &MockEncryptionService{}

	service := NewSettingsService(cache, validation, encryption)
	return service, cache, validation, encryption
}

func TestSettingsService_RegisterSchema(t *testing.T) {
	service, _, mockValidation, _ := setupTestService()

	schema := &SettingSchema{
		Type:        "string",
		Required:    true,
		Description: "Test setting",
		IsPublic:    true,
		IsSecret:    false,
	}

	mockValidation.On("ValidateSchema", mock.Anything, schema).Return(nil)

	err := service.RegisterSchema(context.Background(), "test", "key", schema)
	assert.NoError(t, err)

	// Verify schema was registered
	retrievedSchema, err := service.GetSchema(context.Background(), "test", "key")
	assert.NoError(t, err)
	assert.Equal(t, schema, retrievedSchema)

	mockValidation.AssertExpectations(t)
}

func TestSettingsService_RegisterSchema_InvalidSchema(t *testing.T) {
	service, _, mockValidation, _ := setupTestService()

	schema := &SettingSchema{
		Type: "invalid_type",
	}

	mockValidation.On("ValidateSchema", mock.Anything, schema).Return(ErrInvalidSchema)

	err := service.RegisterSchema(context.Background(), "test", "key", schema)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid schema")

	mockValidation.AssertExpectations(t)
}

func TestSettingsService_Set(t *testing.T) {
	service, mockCache, mockValidation, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Register schema first
	schema := &SettingSchema{
		Type:     "string",
		Required: true,
		IsSecret: false,
	}
	mockValidation.On("ValidateSchema", mock.Anything, schema).Return(nil)
	service.RegisterSchema(ctx, "test", "key", schema)

	// Mock validation and cache operations
	mockValidation.On("ValidateSetting", ctx, schema, "test_value").Return(nil)
	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
	mockCache.On("Delete", ctx, "settings:t1:test:key").Return(nil)
	mockCache.On("Set", ctx, "settings:t1:test:key", mock.AnythingOfType("*services.SettingValue"), DefaultSystemTTL).Return(nil)

	err := service.Set(ctx, settingCtx, ScopeSystem, "test", "key", "test_value")
	assert.NoError(t, err)

	mockValidation.AssertExpectations(t)
	mockCache.AssertExpectations(t)
}

func TestSettingsService_Set_ValidationFailed(t *testing.T) {
	service, _, mockValidation, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Register schema first
	schema := &SettingSchema{
		Type:     "string",
		Required: true,
		IsSecret: false,
	}
	mockValidation.On("ValidateSchema", mock.Anything, schema).Return(nil)
	service.RegisterSchema(ctx, "test", "key", schema)

	// Mock validation failure
	mockValidation.On("ValidateSetting", ctx, schema, "").Return(ErrValidationFailed)

	err := service.Set(ctx, settingCtx, ScopeSystem, "test", "key", "")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "validation failed")

	mockValidation.AssertExpectations(t)
}

func TestSettingsService_Get_FromCache(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	expectedValue := &SettingValue{
		Value:       "cached_value",
		Scope:       ScopeSystem,
		IsEncrypted: false,
		IsInherited: false,
		Source:      "system",
	}

	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
	mockCache.On("Get", ctx, "settings:t1:test:key", mock.AnythingOfType("*services.SettingValue")).
		Run(func(args mock.Arguments) {
			dest := args.Get(2).(*SettingValue)
			*dest = *expectedValue
		}).Return(nil)

	value, err := service.Get(ctx, settingCtx, "test", "key")
	assert.NoError(t, err)
	assert.Equal(t, expectedValue, value)

	mockCache.AssertExpectations(t)
}

func TestSettingsService_Get_CacheMiss(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Register schema first
	schema := &SettingSchema{
		Type:    "string",
		Default: "default_value",
	}
	service.(*settingsService).schemas["test"] = map[string]*SettingSchema{"key": schema}

	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
	mockCache.On("Get", ctx, "settings:t1:test:key", mock.AnythingOfType("*services.SettingValue")).
		Return(ErrCacheOperationFailed)

	value, err := service.Get(ctx, settingCtx, "test", "key")
	assert.NoError(t, err)
	assert.Equal(t, "default_value", value.Value)
	assert.Equal(t, "schema_default", value.Source)

	mockCache.AssertExpectations(t)
}

func TestSettingsService_GetWithDefault(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
	mockCache.On("Get", ctx, "settings:t1:test:key", mock.AnythingOfType("*services.SettingValue")).
		Return(ErrCacheOperationFailed)

	value, err := service.GetWithDefault(ctx, settingCtx, "test", "key", "fallback_value")
	assert.NoError(t, err)
	assert.Equal(t, "fallback_value", value.Value)
	assert.Equal(t, "default", value.Source)

	mockCache.AssertExpectations(t)
}

func TestSettingsService_GetString(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	expectedValue := &SettingValue{
		Value: "string_value",
		Scope: ScopeSystem,
	}

	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
	mockCache.On("Get", ctx, "settings:t1:test:key", mock.AnythingOfType("*services.SettingValue")).
		Run(func(args mock.Arguments) {
			dest := args.Get(2).(*SettingValue)
			*dest = *expectedValue
		}).Return(nil)

	value, err := service.GetString(ctx, settingCtx, "test", "key")
	assert.NoError(t, err)
	assert.Equal(t, "string_value", value)

	mockCache.AssertExpectations(t)
}

func TestSettingsService_GetString_InvalidType(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	expectedValue := &SettingValue{
		Value: 123, // Not a string
		Scope: ScopeSystem,
	}

	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
	mockCache.On("Get", ctx, "settings:t1:test:key", mock.AnythingOfType("*services.SettingValue")).
		Run(func(args mock.Arguments) {
			dest := args.Get(2).(*SettingValue)
			*dest = *expectedValue
		}).Return(nil)

	_, err := service.GetString(ctx, settingCtx, "test", "key")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not a string")

	mockCache.AssertExpectations(t)
}

func TestSettingsService_GetInt(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	testCases := []struct {
		name     string
		value    interface{}
		expected int
	}{
		{"int", 42, 42},
		{"float64", 42.0, 42},
		{"string", "42", 42},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			expectedValue := &SettingValue{
				Value: tc.value,
				Scope: ScopeSystem,
			}

			mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
			mockCache.On("Get", ctx, "settings:t1:test:key", mock.AnythingOfType("*services.SettingValue")).
				Run(func(args mock.Arguments) {
					dest := args.Get(2).(*SettingValue)
					*dest = *expectedValue
				}).Return(nil)

			value, err := service.GetInt(ctx, settingCtx, "test", "key")
			assert.NoError(t, err)
			assert.Equal(t, tc.expected, value)

			mockCache.AssertExpectations(t)
		})
	}
}

func TestSettingsService_GetBool(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	testCases := []struct {
		name     string
		value    interface{}
		expected bool
	}{
		{"bool_true", true, true},
		{"bool_false", false, false},
		{"string_true", "true", true},
		{"string_false", "false", false},
		{"int_zero", 0, false},
		{"int_nonzero", 1, true},
		{"float_zero", 0.0, false},
		{"float_nonzero", 1.0, true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			expectedValue := &SettingValue{
				Value: tc.value,
				Scope: ScopeSystem,
			}

			mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
			mockCache.On("Get", ctx, "settings:t1:test:key", mock.AnythingOfType("*services.SettingValue")).
				Run(func(args mock.Arguments) {
					dest := args.Get(2).(*SettingValue)
					*dest = *expectedValue
				}).Return(nil)

			value, err := service.GetBool(ctx, settingCtx, "test", "key")
			assert.NoError(t, err)
			assert.Equal(t, tc.expected, value)

			mockCache.AssertExpectations(t)
		})
	}
}

func TestSettingsService_Delete(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
	mockCache.On("Delete", ctx, "settings:t1:test:key").Return(nil)

	err := service.Delete(ctx, settingCtx, ScopeSystem, "test", "key")
	assert.NoError(t, err)

	mockCache.AssertExpectations(t)
}

func TestSettingsService_BulkSet(t *testing.T) {
	service, mockCache, mockValidation, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Register schema first
	schema := &SettingSchema{
		Type:     "string",
		Required: true,
		IsSecret: false,
	}
	mockValidation.On("ValidateSchema", mock.Anything, schema).Return(nil)
	service.RegisterSchema(ctx, "test", "key1", schema)
	service.RegisterSchema(ctx, "test", "key2", schema)

	operations := []BulkSettingOperation{
		{
			Operation: "set",
			Category:  "test",
			Key:       "key1",
			Value:     "value1",
		},
		{
			Operation: "set",
			Category:  "test",
			Key:       "key2",
			Value:     "value2",
		},
	}

	// Mock validation for both operations
	mockValidation.On("ValidateSetting", ctx, schema, "value1").Return(nil)
	mockValidation.On("ValidateSetting", ctx, schema, "value2").Return(nil)

	// Mock cache operations for both settings
	mockCache.On("GenerateKey", settingCtx, "test", "key1").Return("settings:t1:test:key1")
	mockCache.On("GenerateKey", settingCtx, "test", "key2").Return("settings:t1:test:key2")
	mockCache.On("Delete", ctx, "settings:t1:test:key1").Return(nil)
	mockCache.On("Delete", ctx, "settings:t1:test:key2").Return(nil)
	mockCache.On("Set", ctx, "settings:t1:test:key1", mock.AnythingOfType("*services.SettingValue"), DefaultSystemTTL).Return(nil)
	mockCache.On("Set", ctx, "settings:t1:test:key2", mock.AnythingOfType("*services.SettingValue"), DefaultSystemTTL).Return(nil)

	// Mock cache pattern invalidation
	mockCache.On("GeneratePatternKey", settingCtx, "test").Return("settings:t1:test:*")
	mockCache.On("Delete", ctx, "settings:t1:test:*").Return(nil)

	err := service.BulkSet(ctx, settingCtx, ScopeSystem, operations)
	assert.NoError(t, err)

	mockValidation.AssertExpectations(t)
	mockCache.AssertExpectations(t)
}

func TestSettingsService_GetPublicSettings(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Register public and private schemas
	publicSchema := &SettingSchema{
		Type:     "string",
		IsPublic: true,
	}
	privateSchema := &SettingSchema{
		Type:     "string",
		IsPublic: false,
	}

	service.(*settingsService).schemas["test"] = map[string]*SettingSchema{
		"public_key":  publicSchema,
		"private_key": privateSchema,
	}

	// Mock cache operations
	mockCache.On("GeneratePatternKey", settingCtx, "test").Return("settings:t1:test:*")
	mockCache.On("Get", ctx, "settings:t1:test:*", mock.AnythingOfType("map[string]*services.SettingValue")).
		Run(func(args mock.Arguments) {
			dest := args.Get(2).(map[string]*SettingValue)
			dest["public_key"] = &SettingValue{
				Value:  "public_value",
				Schema: publicSchema,
			}
			dest["private_key"] = &SettingValue{
				Value:  "private_value",
				Schema: privateSchema,
			}
		}).Return(nil)

	result, err := service.GetPublicSettings(ctx, settingCtx, "test")
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, "public_value", result["public_key"])
	assert.NotContains(t, result, "private_key")

	mockCache.AssertExpectations(t)
}

func TestSettingsService_InvalidateCache(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
	mockCache.On("Delete", ctx, "settings:t1:test:key").Return(nil)

	err := service.InvalidateCache(ctx, settingCtx, "test", "key")
	assert.NoError(t, err)

	mockCache.AssertExpectations(t)
}

func TestSettingsService_InvalidateCachePattern(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	pattern := "settings:t1:*"

	mockCache.On("DeletePattern", ctx, pattern).Return(nil)

	err := service.InvalidateCachePattern(ctx, pattern)
	assert.NoError(t, err)

	mockCache.AssertExpectations(t)
}

func TestSettingsService_Import(t *testing.T) {
	service, mockCache, mockValidation, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Register schema first
	schema := &SettingSchema{
		Type:     "string",
		Required: true,
		IsSecret: false,
	}
	mockValidation.On("ValidateSchema", mock.Anything, schema).Return(nil)
	service.RegisterSchema(ctx, "test", "key", schema)

	settings := map[string]map[string]interface{}{
		"test": {
			"key": "imported_value",
		},
	}

	// Mock validation and cache operations
	mockValidation.On("ValidateSetting", ctx, schema, "imported_value").Return(nil)
	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:test:key")
	mockCache.On("Delete", ctx, "settings:t1:test:key").Return(nil)
	mockCache.On("Set", ctx, "settings:t1:test:key", mock.AnythingOfType("*services.SettingValue"), DefaultSystemTTL).Return(nil)

	err := service.Import(ctx, settingCtx, ScopeSystem, settings)
	assert.NoError(t, err)

	mockValidation.AssertExpectations(t)
	mockCache.AssertExpectations(t)
}

func TestSettingsService_Export(t *testing.T) {
	service, _, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Set up some system settings
	serviceImpl := service.(*settingsService)
	serviceImpl.systemSettings["test"] = map[string]interface{}{
		"key1": "value1",
		"key2": "value2",
	}

	result, err := service.Export(ctx, settingCtx, ScopeSystem, []string{"test"})
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, "value1", result["test"]["key1"])
	assert.Equal(t, "value2", result["test"]["key2"])
}

func TestSettingsService_WarmCache(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	categories := []string{"test1", "test2"}

	mockCache.On("WarmCache", ctx, settingCtx, categories).Return(nil)

	err := service.WarmCache(ctx, settingCtx, categories)
	assert.NoError(t, err)

	mockCache.AssertExpectations(t)
}

// Integration tests

func TestSettingsService_HierarchicalResolution(t *testing.T) {
	service, mockCache, _, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
		UserID:   &[]uint{2}[0],
	}

	// Register schema with default
	schema := &SettingSchema{
		Type:    "string",
		Default: "system_default",
	}
	service.(*settingsService).schemas["test"] = map[string]*SettingSchema{"key": schema}

	// Mock cache miss
	mockCache.On("GenerateKey", settingCtx, "test", "key").Return("settings:t1:u2:test:key")
	mockCache.On("Get", ctx, "settings:t1:u2:test:key", mock.AnythingOfType("*services.SettingValue")).
		Return(ErrCacheOperationFailed)

	value, err := service.Get(ctx, settingCtx, "test", "key")
	assert.NoError(t, err)
	assert.Equal(t, "system_default", value.Value)
	assert.Equal(t, "schema_default", value.Source)

	mockCache.AssertExpectations(t)
}

func TestSettingsService_EncryptedSetting(t *testing.T) {
	service, mockCache, mockValidation, mockEncryption := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Register secret schema
	schema := &SettingSchema{
		Type:     "string",
		Required: true,
		IsSecret: true,
	}
	mockValidation.On("ValidateSchema", mock.Anything, schema).Return(nil)
	service.RegisterSchema(ctx, "test", "secret_key", schema)

	// Mock encryption
	mockEncryption.On("EncryptJSON", ctx, "secret_value").Return([]byte("encrypted_data"), nil)

	// Mock validation and cache operations
	mockValidation.On("ValidateSetting", ctx, schema, "secret_value").Return(nil)
	mockCache.On("GenerateKey", settingCtx, "test", "secret_key").Return("settings:t1:test:secret_key")
	mockCache.On("Delete", ctx, "settings:t1:test:secret_key").Return(nil)
	mockCache.On("Set", ctx, "settings:t1:test:secret_key", mock.AnythingOfType("*services.SettingValue"), DefaultSystemTTL).Return(nil)

	err := service.Set(ctx, settingCtx, ScopeSystem, "test", "secret_key", "secret_value")
	assert.NoError(t, err)

	mockValidation.AssertExpectations(t)
	mockEncryption.AssertExpectations(t)
	mockCache.AssertExpectations(t)
}

func TestSettingsService_ValidationRules(t *testing.T) {
	service, mockCache, mockValidation, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Register schema with validation rules
	schema := &SettingSchema{
		Type:     "string",
		Required: true,
		Validation: map[string]interface{}{
			"min": 3,
			"max": 10,
		},
	}
	mockValidation.On("ValidateSchema", mock.Anything, schema).Return(nil)
	service.RegisterSchema(ctx, "test", "validated_key", schema)

	// Test valid value
	mockValidation.On("ValidateSetting", ctx, schema, "valid").Return(nil)
	mockCache.On("GenerateKey", settingCtx, "test", "validated_key").Return("settings:t1:test:validated_key")
	mockCache.On("Delete", ctx, "settings:t1:test:validated_key").Return(nil)
	mockCache.On("Set", ctx, "settings:t1:test:validated_key", mock.AnythingOfType("*services.SettingValue"), DefaultSystemTTL).Return(nil)

	err := service.Set(ctx, settingCtx, ScopeSystem, "test", "validated_key", "valid")
	assert.NoError(t, err)

	// Test invalid value
	mockValidation.On("ValidateSetting", ctx, schema, "x").Return(ErrValidationFailed)

	err = service.Set(ctx, settingCtx, ScopeSystem, "test", "validated_key", "x")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "validation failed")

	mockValidation.AssertExpectations(t)
	mockCache.AssertExpectations(t)
}

func TestSettingsService_ConcurrentAccess(t *testing.T) {
	service, mockCache, mockValidation, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{
		TenantID: &[]uint{1}[0],
	}

	// Register schema
	schema := &SettingSchema{
		Type:     "string",
		Required: true,
	}
	mockValidation.On("ValidateSchema", mock.Anything, schema).Return(nil)
	service.RegisterSchema(ctx, "test", "concurrent_key", schema)

	// Mock operations for concurrent access
	mockValidation.On("ValidateSetting", ctx, schema, mock.AnythingOfType("string")).Return(nil)
	mockCache.On("GenerateKey", settingCtx, "test", "concurrent_key").Return("settings:t1:test:concurrent_key")
	mockCache.On("Delete", ctx, "settings:t1:test:concurrent_key").Return(nil)
	mockCache.On("Set", ctx, "settings:t1:test:concurrent_key", mock.AnythingOfType("*services.SettingValue"), DefaultSystemTTL).Return(nil)

	// Test concurrent writes
	const numGoroutines = 10
	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(i int) {
			err := service.Set(ctx, settingCtx, ScopeSystem, "test", "concurrent_key", fmt.Sprintf("value_%d", i))
			assert.NoError(t, err)
			done <- true
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// Note: In a real implementation, we'd need to be more careful about the exact number
	// of mock calls since they happen concurrently. For this test, we just verify
	// that no errors occurred.
}

func TestSettingsService_ErrorHandling(t *testing.T) {
	service, mockCache, mockValidation, _ := setupTestService()

	ctx := context.Background()
	settingCtx := &SettingContext{}

	// Test getting non-existent schema
	_, err := service.GetSchema(ctx, "nonexistent", "key")
	assert.Error(t, err)
	assert.Equal(t, ErrSchemaNotFound, err)

	// Test getting non-existent setting
	mockCache.On("GenerateKey", settingCtx, "nonexistent", "key").Return("settings:nonexistent:key")
	mockCache.On("Get", ctx, "settings:nonexistent:key", mock.AnythingOfType("*services.SettingValue")).
		Return(ErrCacheOperationFailed)

	_, err = service.Get(ctx, settingCtx, "nonexistent", "key")
	assert.Error(t, err)
	assert.Equal(t, ErrSettingNotFound, err)

	mockCache.AssertExpectations(t)
}