package dto

import "time"

// BlogCategoryCreateRequest represents the request payload for creating a blog category
type BlogCategoryCreateRequest struct {
	TenantID    uint   `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID   uint   `json:"website_id" validate:"required,min=1" example:"1"`
	Name        string `json:"name" validate:"required,min=1,max=100" example:"Technology"`
	Slug        string `json:"slug" validate:"required,min=1,max=100" example:"technology"`
	Description string `json:"description,omitempty" validate:"max=500" example:"Posts about technology and programming"`
	ParentID    *uint  `json:"parent_id,omitempty" validate:"omitempty,min=1" example:"1"`
	SortOrder   int    `json:"sort_order" example:"1"`
	IsActive    bool   `json:"is_active" example:"true"`
	MetaTitle   string `json:"meta_title,omitempty" validate:"max=255" example:"Technology Category"`
	MetaKeywords string `json:"meta_keywords,omitempty" validate:"max=500" example:"technology, programming, software"`
}

// BlogCategoryUpdateRequest represents the request payload for updating a blog category
type BlogCategoryUpdateRequest struct {
	Name         *string `json:"name,omitempty" validate:"omitempty,min=1,max=100" example:"Updated Technology"`
	Slug         *string `json:"slug,omitempty" validate:"omitempty,min=1,max=100" example:"updated-technology"`
	Description  *string `json:"description,omitempty" validate:"omitempty,max=500" example:"Updated description"`
	ParentID     *uint   `json:"parent_id,omitempty" validate:"omitempty,min=1" example:"2"`
	SortOrder    *int    `json:"sort_order,omitempty" example:"2"`
	IsActive     *bool   `json:"is_active,omitempty" example:"false"`
	MetaTitle    *string `json:"meta_title,omitempty" validate:"omitempty,max=255" example:"Updated Meta Title"`
	MetaKeywords *string `json:"meta_keywords,omitempty" validate:"omitempty,max=500" example:"updated, keywords"`
}

// BlogCategoryResponse represents the response payload for a blog category
// KEEP_OMITEMPTY: Optional description, SEO meta fields, and nullable parent relationship
type BlogCategoryResponse struct {
	ID          uint      `json:"id"`                      
	TenantID    uint      `json:"tenant_id"`              
	WebsiteID   uint      `json:"website_id"`            
	Name        string    `json:"name"`                   
	Slug        string    `json:"slug"`                   
	Description string    `json:"description,omitempty"`   // KEEP_OMITEMPTY: Optional content
	ParentID    *uint     `json:"parent_id,omitempty"`     // KEEP_OMITEMPTY: Optional hierarchy relationship
	SortOrder   int       `json:"sort_order"`             
	PostCount   int       `json:"post_count"`             
	IsActive    bool      `json:"is_active"`              
	MetaTitle   string    `json:"meta_title,omitempty"`    // KEEP_OMITEMPTY: Optional SEO field
	MetaKeywords string   `json:"meta_keywords,omitempty"` // KEEP_OMITEMPTY: Optional SEO field
	CreatedAt   time.Time `json:"created_at"`             
	UpdatedAt   time.Time `json:"updated_at"`             
}

// BlogCategoryListResponse represents the response payload for listing blog categories
type BlogCategoryListResponse struct {
	Categories []BlogCategoryResponse `json:"categories"`
	Total      int64                  `json:"total"`     
}