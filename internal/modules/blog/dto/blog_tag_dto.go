package dto

import "time"

// BlogTagCreateRequest represents the request payload for creating a blog tag
type BlogTagCreateRequest struct {
	TenantID    uint   `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID   uint   `json:"website_id" validate:"required,min=1" example:"1"`
	Name        string `json:"name" validate:"required,min=1,max=50" example:"Programming"`
	Slug        string `json:"slug" validate:"required,min=1,max=50" example:"programming"`
	Description string `json:"description,omitempty" validate:"max=255" example:"Posts about programming and coding"`
	Color       string `json:"color,omitempty" validate:"omitempty,hexcolor" example:"#3498db"`
	IsActive    bool   `json:"is_active" example:"true"`
}

// BlogTagUpdateRequest represents the request payload for updating a blog tag
type BlogTagUpdateRequest struct {
	Name        *string `json:"name,omitempty" validate:"omitempty,min=1,max=50" example:"Advanced Programming"`
	Slug        *string `json:"slug,omitempty" validate:"omitempty,min=1,max=50" example:"advanced-programming"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=255" example:"Advanced programming concepts"`
	Color       *string `json:"color,omitempty" validate:"omitempty,hexcolor" example:"#2ecc71"`
	IsActive    *bool   `json:"is_active,omitempty" example:"false"`
}

// BlogTagResponse represents the response payload for a blog tag
// KEEP_OMITEMPTY: Optional description and display customizations
type BlogTagResponse struct {
	ID          uint      `json:"id"`                    
	TenantID    uint      `json:"tenant_id"`            
	WebsiteID   uint      `json:"website_id"`          
	Name        string    `json:"name"`                 
	Slug        string    `json:"slug"`                 
	Description string    `json:"description,omitempty"` // KEEP_OMITEMPTY: Optional content
	Color       string    `json:"color,omitempty"`       // KEEP_OMITEMPTY: Optional display customization
	PostCount   int       `json:"post_count"`           
	IsActive    bool      `json:"is_active"`            
	CreatedAt   time.Time `json:"created_at"`           
	UpdatedAt   time.Time `json:"updated_at"`           
}

// BlogTagListResponse represents the response payload for listing blog tags
type BlogTagListResponse struct {
	Tags  []BlogTagResponse `json:"tags"` 
	Total int64             `json:"total"`
}

// BlogTagStatsResponse represents tag usage statistics
type BlogTagStatsResponse struct {
	TotalTags      int64 `json:"total_tags"`     
	ActiveTags     int64 `json:"active_tags"`    
	InactiveTags   int64 `json:"inactive_tags"`  
	MostUsedTags   []BlogTagResponse `json:"most_used_tags"`  
	RecentlyAdded  []BlogTagResponse `json:"recently_added"`  
}