package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogPostCreateRequest represents the request payload for creating a blog post
type BlogPostCreateRequest struct {
	TenantID      uint                   `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID     uint                   `json:"website_id" validate:"required,min=1" example:"1"`
	Slug          string                 `json:"slug" validate:"required,min=1,max=255" example:"my-first-blog-post"`
	Title         string                 `json:"title" validate:"required,min=1,max=255" example:"My First Blog Post"`
	Content       string                 `json:"content" validate:"required" example:"This is the comprehensive content of my first blog post..."`
	Excerpt       string                 `json:"excerpt,omitempty" validate:"max=1000" example:"A brief summary of my first blog post"`
	AuthorID      uint                   `json:"author_id" validate:"required,min=1" example:"1"`
	CategoryID    *uint                  `json:"category_id,omitempty" validate:"omitempty,min=1" example:"1"`
	Type          models.BlogPostType    `json:"type" validate:"oneof=post page announcement" example:"post"`
	IsFeatured    bool                   `json:"is_featured" example:"false"`
	AllowComments bool                   `json:"allow_comments" example:"true"`
	Password      string                 `json:"password,omitempty" validate:"max=255" example:""`
	FeaturedImage string                 `json:"featured_image,omitempty" validate:"omitempty,url" example:"https://example.com/images/featured-image.jpg"`
	ScheduledAt   *time.Time             `json:"scheduled_at,omitempty" example:"2025-01-20T15:00:00Z"`
	Status        models.BlogPostStatus  `json:"status" validate:"oneof=draft review published scheduled archived rejected" example:"draft"`
	TagIDs        []uint                 `json:"tag_ids,omitempty"`
}

// BlogPostUpdateRequest represents the request payload for updating a blog post
type BlogPostUpdateRequest struct {
	Slug          *string                `json:"slug,omitempty" validate:"omitempty,min=1,max=255" example:"updated-blog-post"`
	Title         *string                `json:"title,omitempty" validate:"omitempty,min=1,max=255" example:"Updated Blog Post Title"`
	Content       *string                `json:"content,omitempty" validate:"omitempty" example:"Updated content of the blog post..."`
	Excerpt       *string                `json:"excerpt,omitempty" validate:"omitempty,max=1000" example:"Updated excerpt"`
	CategoryID    *uint                  `json:"category_id,omitempty" validate:"omitempty,min=1" example:"2"`
	Type          *models.BlogPostType   `json:"type,omitempty" validate:"omitempty,oneof=post page announcement" example:"post"`
	IsFeatured    *bool                  `json:"is_featured,omitempty" example:"true"`
	AllowComments *bool                  `json:"allow_comments,omitempty" example:"false"`
	Password      *string                `json:"password,omitempty" validate:"omitempty,max=255" example:""`
	FeaturedImage *string                `json:"featured_image,omitempty" validate:"omitempty,url" example:"https://example.com/images/updated.jpg"`
	ScheduledAt   *time.Time             `json:"scheduled_at,omitempty" example:"2025-01-22T15:00:00Z"`
	Status        *models.BlogPostStatus `json:"status,omitempty" validate:"omitempty,oneof=draft review published scheduled archived rejected" example:"published"`
	TagIDs        []uint                 `json:"tag_ids,omitempty"`
}

// BlogPostResponse represents the response payload for a blog post
// KEEP_OMITEMPTY: Optional content fields, nullable relationships, and optional nested objects
type BlogPostResponse struct {
	ID            uint                   `json:"id"`                          
	TenantID      uint                   `json:"tenant_id"`                   
	WebsiteID     uint                   `json:"website_id"`                  
	Slug          string                 `json:"slug"`                        
	Title         string                 `json:"title"`                       
	Content       string                 `json:"content"`                     
	Excerpt       string                 `json:"excerpt,omitempty"`            // KEEP_OMITEMPTY: Optional content summary
	AuthorID      uint                   `json:"author_id"`                   
	CategoryID    *uint                  `json:"category_id,omitempty"`        // KEEP_OMITEMPTY: Optional relationship
	Type          models.BlogPostType    `json:"type"`                        
	IsFeatured    bool                   `json:"is_featured"`                 
	AllowComments bool                   `json:"allow_comments"`              
	FeaturedImage string                 `json:"featured_image,omitempty"`     // KEEP_OMITEMPTY: Optional media
	ViewCount     uint                   `json:"view_count"`                  
	ScheduledAt   *time.Time             `json:"scheduled_at,omitempty"`       // KEEP_OMITEMPTY: Optional schedule timestamp
	PublishedAt   *time.Time             `json:"published_at,omitempty"`       // KEEP_OMITEMPTY: Optional publish timestamp
	Status        models.BlogPostStatus  `json:"status"`                      
	CreatedAt     time.Time              `json:"created_at"`                  
	UpdatedAt     time.Time              `json:"updated_at"`                  
	Category      *BlogCategoryResponse  `json:"category,omitempty"`           // KEEP_OMITEMPTY: Optional nested object
	Tags          []BlogTagResponse      `json:"tags,omitempty"`               // KEEP_OMITEMPTY: Optional nested array
}

// BlogPostListResponse represents the response payload for listing blog posts
type BlogPostListResponse struct {
	Posts      []BlogPostResponse          `json:"posts"`     
	Pagination *pagination.CursorResponse `json:"pagination"`
}

// BlogPostFilter represents filter parameters for listing blog posts
type BlogPostFilter struct {
	pagination.CursorRequest
	Title       string                 `json:"title,omitempty" form:"title" validate:"omitempty,max=255" example:"My Blog"`
	Status      *models.BlogPostStatus `json:"status,omitempty" form:"status" validate:"omitempty,oneof=draft review published scheduled archived rejected" example:"published"`
	Type        *models.BlogPostType   `json:"type,omitempty" form:"type" validate:"omitempty,oneof=post page announcement" example:"post"`
	CategoryID  *uint                  `json:"category_id,omitempty" form:"category_id" validate:"omitempty,min=1" example:"1"`
	AuthorID    *uint                  `json:"author_id,omitempty" form:"author_id" validate:"omitempty,min=1" example:"1"`
	IsFeatured  *bool                  `json:"is_featured,omitempty" form:"is_featured" example:"true"`
	TagID       *uint                  `json:"tag_id,omitempty" form:"tag_id" validate:"omitempty,min=1" example:"1"`
	DateFrom    *time.Time             `json:"date_from,omitempty" form:"date_from" example:"2024-01-01T00:00:00Z"`
	DateTo      *time.Time             `json:"date_to,omitempty" form:"date_to" example:"2024-12-31T23:59:59Z"`
	SortBy      string                 `json:"sort_by,omitempty" form:"sort_by" validate:"omitempty,oneof=id title created_at updated_at published_at view_count" example:"created_at"`
	SortOrder   string                 `json:"sort_order,omitempty" form:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}