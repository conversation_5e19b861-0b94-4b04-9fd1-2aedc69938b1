package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
)

// BlogPostScheduleRepository implements the blog post schedule repository interface using MySQL
type BlogPostScheduleRepository struct {
	db *gorm.DB
}

// NewBlogPostScheduleRepository creates a new instance of BlogPostScheduleRepository
func NewBlogPostScheduleRepository(db *gorm.DB) repositories.BlogPostScheduleRepository {
	return &BlogPostScheduleRepository{db: db}
}

// <PERSON><PERSON> creates a new blog post schedule
func (r *BlogPostScheduleRepository) Create(ctx context.Context, schedule *models.BlogPostSchedule) error {
	return r.db.WithContext(ctx).Create(schedule).Error
}

// GetByPostID gets a blog post schedule by post ID and tenant ID
func (r *BlogPostScheduleRepository) GetByPostID(ctx context.Context, tenantID, postID uint) (*models.BlogPostSchedule, error) {
	var schedule models.BlogPostSchedule
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND post_id = ? AND status != ?", tenantID, postID, models.BlogPostScheduleStatusDeleted).
		Preload("Post").
		First(&schedule).Error

	if err != nil {
		return nil, err
	}
	return &schedule, nil
}

// Update updates a blog post schedule
func (r *BlogPostScheduleRepository) Update(ctx context.Context, tenantID, id uint, schedule *models.BlogPostSchedule) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.BlogPostScheduleStatusDeleted).
		Updates(schedule).Error
}

// Delete soft deletes a blog post schedule by setting status to deleted
func (r *BlogPostScheduleRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogPostSchedule{}).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.BlogPostScheduleStatusDeleted).
		Update("status", models.BlogPostScheduleStatusDeleted).Error
}

// GetPendingSchedules gets all pending schedules that need to be processed
func (r *BlogPostScheduleRepository) GetPendingSchedules(ctx context.Context) ([]models.BlogPostSchedule, error) {
	var schedules []models.BlogPostSchedule
	err := r.db.WithContext(ctx).
		Where("status = ? AND scheduled_at <= NOW()", models.BlogPostScheduleStatusPending).
		Preload("Post").
		Find(&schedules).Error

	return schedules, err
}

// MarkAsExecuted marks a schedule as executed/completed
func (r *BlogPostScheduleRepository) MarkAsExecuted(ctx context.Context, tenantID, scheduleID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogPostSchedule{}).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, scheduleID, models.BlogPostScheduleStatusDeleted).
		Updates(map[string]interface{}{
			"status": models.BlogPostScheduleStatusCompleted,
		}).Error
}

// List retrieves blog post schedules with filters and pagination
func (r *BlogPostScheduleRepository) List(ctx context.Context, filter *models.BlogPostScheduleFilter) ([]models.BlogPostSchedule, int64, error) {
	var schedules []models.BlogPostSchedule
	var total int64

	query := r.db.WithContext(ctx).Model(&models.BlogPostSchedule{})

	// Exclude deleted records by default
	query = query.Where("status != ?", models.BlogPostScheduleStatusDeleted)

	// Apply filters
	if filter.TenantID != 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.PostID != 0 {
		query = query.Where("post_id = ?", filter.PostID)
	}
	if filter.ScheduleType != "" {
		query = query.Where("schedule_type = ?", filter.ScheduleType)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.DateFrom != nil {
		query = query.Where("scheduled_at >= ?", *filter.DateFrom)
	}
	if filter.DateTo != nil {
		query = query.Where("scheduled_at <= ?", *filter.DateTo)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := "scheduled_at"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}
	sortOrder := "ASC"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// Execute query with preloads
	err := query.
		Preload("Post").
		Find(&schedules).Error

	return schedules, total, err
}

// MarkAsFailed marks a schedule as failed with error message
func (r *BlogPostScheduleRepository) MarkAsFailed(ctx context.Context, tenantID, scheduleID uint, errorMessage string) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogPostSchedule{}).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, scheduleID, models.BlogPostScheduleStatusDeleted).
		Updates(map[string]interface{}{
			"status":        models.BlogPostScheduleStatusFailed,
			"error_message": errorMessage,
		}).Error
}

// GetByStatus gets schedules by status
func (r *BlogPostScheduleRepository) GetByStatus(ctx context.Context, tenantID uint, status models.BlogPostScheduleStatus) ([]models.BlogPostSchedule, error) {
	var schedules []models.BlogPostSchedule
	query := r.db.WithContext(ctx)

	if tenantID > 0 {
		query = query.Where("tenant_id = ?", tenantID)
	}

	err := query.
		Where("status = ?", status).
		Preload("Post").
		Order("scheduled_at ASC").
		Find(&schedules).Error

	return schedules, err
}
