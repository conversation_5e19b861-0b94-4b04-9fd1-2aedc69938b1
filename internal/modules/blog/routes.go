package blog

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	seoServices "github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// RegisterBlogRoutes registers all blog module routes
func RegisterBlogRoutes(r *gin.RouterGroup, blogServices *services.BlogServices, seoMetaService seoServices.SEOMetaService, logger utils.Logger) {
	// Create handlers
	categoryHandler := handlers.NewBlogCategoryHandler(blogServices.CategoryService)
	postHandler := handlers.NewBlogPostHandler(blogServices.PostService)
	tagHandler := handlers.NewBlogTagHandler(blogServices.TagService)
	
	// Create blog routes group
	blog := r.Group("/blog")
	{
		// Category routes
		categories := blog.Group("/categories")
		{
			categories.GET("", categoryHandler.ListCategories)
			categories.POST("", categoryHandler.CreateCategory)
			categories.GET("/:id", categoryHandler.GetCategory)
			categories.GET("/slug/:slug", categoryHandler.GetCategoryBySlug)
			categories.PUT("/:id", categoryHandler.UpdateCategory)
			categories.DELETE("/:id", categoryHandler.DeleteCategory)
			categories.GET("/hierarchy", categoryHandler.GetCategoryHierarchy)
			categories.POST("/:id/move", categoryHandler.MoveCategory)
			categories.POST("/positions", categoryHandler.UpdateCategoryPositions)
		}

		// Post routes
		posts := blog.Group("/posts")
		{
			posts.GET("", postHandler.ListPosts)
			posts.POST("", postHandler.CreatePost)
			posts.GET("/:id", postHandler.GetPost)
			posts.GET("/slug/:slug", postHandler.GetPostBySlug)
			posts.PUT("/:id", postHandler.UpdatePost)
			posts.DELETE("/:id", postHandler.DeletePost)
			posts.GET("/published", postHandler.GetPublishedPosts)
			posts.GET("/featured", postHandler.GetFeaturedPosts)
			posts.GET("/:id/related", postHandler.GetRelatedPosts)
			posts.POST("/:id/publish", postHandler.PublishPost)
			posts.POST("/:id/unpublish", postHandler.UnpublishPost)
			posts.GET("/stats", postHandler.GetPostStats)
			posts.GET("/popular", postHandler.GetPopularPosts)
			posts.POST("/:id/tags/attach", postHandler.AttachTags)
			posts.POST("/:id/tags/detach", postHandler.DetachTags)
			posts.POST("/:id/tags/sync", postHandler.SyncTags)
			
			// TODO: SEO convenience endpoints for blog posts
			// These will be re-enabled once SEO module integration is complete
			// posts.POST("/:id/seo", blogPostSEOHandler.CreatePostSEO)           // Create SEO for post
			// posts.GET("/:id/seo", blogPostSEOHandler.GetPostSEO)               // Get SEO for post  
			// posts.PUT("/:id/seo", blogPostSEOHandler.UpdatePostSEO)            // Update SEO for post
			// posts.DELETE("/:id/seo", blogPostSEOHandler.DeletePostSEO)         // Delete SEO for post
			// posts.POST("/:id/seo/analyze", blogPostSEOHandler.AnalyzePostSEO)  // Analyze post SEO
			// posts.POST("/:id/seo/validate", blogPostSEOHandler.ValidatePostSEO) // Validate post SEO
			// posts.GET("/:id/seo/tags", blogPostSEOHandler.GeneratePostMetaTags) // Generate meta tags
		}

		// Tag routes
		tags := blog.Group("/tags")
		{
			tags.GET("", tagHandler.ListTags)
			tags.POST("", tagHandler.CreateTag)
			tags.GET("/:id", tagHandler.GetTag)
			tags.GET("/slug/:slug", tagHandler.GetTagBySlug)
			tags.PUT("/:id", tagHandler.UpdateTag)
			tags.DELETE("/:id", tagHandler.DeleteTag)
			tags.GET("/most-used", tagHandler.GetMostUsedTags)
			tags.GET("/suggestions", tagHandler.GetTagSuggestions)
			tags.GET("/stats", tagHandler.GetTagStats)
		}
	}
}