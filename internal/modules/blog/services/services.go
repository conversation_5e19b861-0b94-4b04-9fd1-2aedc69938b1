package services

import (
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// BlogServices contains all blog services
type BlogServices struct {
	CategoryService      BlogCategoryService
	TagService           BlogTagService
	PostService          BlogPostService
	PostScheduleService  BlogPostScheduleService
	PostRevisionService  BlogPostRevisionService
	HomepageBlockService HomepageBlockService
	BlockTemplateService BlockTemplateService
}

// NewBlogServices creates a new instance of blog services
func NewBlogServices(
	categoryRepo repositories.BlogCategoryRepository,
	tagRepo repositories.BlogTagRepository,
	postRepo repositories.BlogPostRepository,
	scheduleRepo repositories.BlogPostScheduleRepository,
	revisionRepo repositories.BlogPostRevisionRepository,
	homepageBlockRepo repositories.HomepageBlockRepository,
	blockTemplateRepo repositories.BlockTemplateRepository,
	notificationService notificationServices.NotificationService,
	logger utils.Logger,
) *BlogServices {
	// Create tag service first as it's needed by post service
	tagService := NewBlogTagService(tagRepo)

	return &BlogServices{
		CategoryService:      NewBlogCategoryService(categoryRepo),
		TagService:           tagService,
		PostService:          NewBlogPostService(postRepo, tagRepo, notificationService, logger),
		PostScheduleService:  NewBlogPostScheduleService(scheduleRepo, postRepo),
		PostRevisionService:  NewBlogPostRevisionService(revisionRepo, postRepo),
		HomepageBlockService: NewHomepageBlockService(homepageBlockRepo, blockTemplateRepo, logger),
		BlockTemplateService: NewBlockTemplateService(blockTemplateRepo, logger),
	}
}
