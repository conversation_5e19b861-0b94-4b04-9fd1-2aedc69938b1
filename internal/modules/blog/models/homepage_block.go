package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
)

// HomepageBlockType represents the type of homepage block
// @Enum featured_posts,latest_posts,category_showcase,tag_cloud,popular_posts,video_gallery,image_carousel,announcement_banner,newsletter_signup,social_media,testimonials,statistics,custom_html,search_box,contact_form
type HomepageBlockType string

const (
	BlockTypeFeaturedPosts      HomepageBlockType = "featured_posts"
	BlockTypeLatestPosts        HomepageBlockType = "latest_posts"
	BlockTypeCategoryShowcase   HomepageBlockType = "category_showcase"
	BlockTypeTagCloud          HomepageBlockType = "tag_cloud"
	BlockTypePopularPosts      HomepageBlockType = "popular_posts"
	BlockTypeVideoGallery      HomepageBlockType = "video_gallery"
	BlockTypeImageCarousel     HomepageBlockType = "image_carousel"
	BlockTypeAnnouncementBanner HomepageBlockType = "announcement_banner"
	BlockTypeNewsletterSignup  HomepageBlockType = "newsletter_signup"
	BlockTypeSocialMedia       HomepageBlockType = "social_media"
	BlockTypeTestimonials      HomepageBlockType = "testimonials"
	BlockTypeStatistics        HomepageBlockType = "statistics"
	BlockTypeCustomHTML        HomepageBlockType = "custom_html"
	BlockTypeSearchBox         HomepageBlockType = "search_box"
	BlockTypeContactForm       HomepageBlockType = "contact_form"
)

// JSON is a custom type for handling JSON data in database
type JSON map[string]interface{}

// Value implements the driver.Valuer interface for database storage
func (j JSON) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan implements the sql.Scanner interface for database retrieval
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("cannot scan into JSON")
	}

	return json.Unmarshal(bytes, j)
}

// HomepageBlock represents a homepage content block
type HomepageBlock struct {
	ID        uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint `gorm:"not null;index" json:"tenant_id"`
	WebsiteID uint `gorm:"not null;index" json:"website_id"`

	// Block Configuration
	BlockType     HomepageBlockType `gorm:"type:varchar(50);not null" json:"block_type" validate:"required"`
	Title         string            `gorm:"type:varchar(255);not null" json:"title" validate:"required,min=1,max=255"`
	Subtitle      string            `gorm:"type:varchar(255)" json:"subtitle,omitempty" validate:"max=255"`
	Configuration JSON              `gorm:"type:json" json:"configuration"`
	Styling       JSON              `gorm:"type:json" json:"styling"`

	// Display Settings
	IsActive        bool `gorm:"default:true" json:"is_active"`
	SortOrder       uint `gorm:"default:0" json:"sort_order"`
	VisibilityRules JSON `gorm:"type:json" json:"visibility_rules"`

	// Status using enum for soft delete strategy
	Status string `gorm:"type:varchar(20);not null;default:'active'" json:"status" validate:"oneof=active inactive deleted"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName returns the table name for the HomepageBlock model
func (HomepageBlock) TableName() string {
	return "homepage_blocks"
}

// HomepageBlockCreateRequest represents the request to create a homepage block
type HomepageBlockCreateRequest struct {
	TenantID        uint              `json:"tenant_id" validate:"required,min=1"`
	WebsiteID       uint              `json:"website_id" validate:"required,min=1"`
	BlockType       HomepageBlockType `json:"block_type" validate:"required"`
	Title           string            `json:"title" validate:"required,min=1,max=255"`
	Subtitle        string            `json:"subtitle,omitempty" validate:"max=255"`
	Configuration   JSON              `json:"configuration"`
	Styling         JSON              `json:"styling"`
	IsActive        bool              `json:"is_active"`
	SortOrder       uint              `json:"sort_order"`
	VisibilityRules JSON              `json:"visibility_rules"`
}

// HomepageBlockUpdateRequest represents the request to update a homepage block
type HomepageBlockUpdateRequest struct {
	Title           string `json:"title" validate:"required,min=1,max=255"`
	Subtitle        string `json:"subtitle,omitempty" validate:"max=255"`
	Configuration   JSON   `json:"configuration"`
	Styling         JSON   `json:"styling"`
	IsActive        bool   `json:"is_active"`
	SortOrder       uint   `json:"sort_order"`
	VisibilityRules JSON   `json:"visibility_rules"`
}

// HomepageBlockResponse represents the response when returning homepage block data
type HomepageBlockResponse struct {
	ID              uint              `json:"id"`
	TenantID        uint              `json:"tenant_id"`
	WebsiteID       uint              `json:"website_id"`
	BlockType       HomepageBlockType `json:"block_type"`
	Title           string            `json:"title"`
	Subtitle        string            `json:"subtitle,omitempty"`
	Configuration   JSON              `json:"configuration"`
	Styling         JSON              `json:"styling"`
	IsActive        bool              `json:"is_active"`
	SortOrder       uint              `json:"sort_order"`
	VisibilityRules JSON              `json:"visibility_rules"`
	Status          string            `json:"status"`
	CreatedAt       time.Time         `json:"created_at"`
	UpdatedAt       time.Time         `json:"updated_at"`
}

// FromHomepageBlock converts a HomepageBlock model to HomepageBlockResponse
func (hbr *HomepageBlockResponse) FromHomepageBlock(block *HomepageBlock) {
	hbr.ID = block.ID
	hbr.TenantID = block.TenantID
	hbr.WebsiteID = block.WebsiteID
	hbr.BlockType = block.BlockType
	hbr.Title = block.Title
	hbr.Subtitle = block.Subtitle
	hbr.Configuration = block.Configuration
	hbr.Styling = block.Styling
	hbr.IsActive = block.IsActive
	hbr.SortOrder = block.SortOrder
	hbr.VisibilityRules = block.VisibilityRules
	hbr.Status = block.Status
	hbr.CreatedAt = block.CreatedAt
	hbr.UpdatedAt = block.UpdatedAt
}

// HomepageBlockFilter represents filters for querying homepage blocks
type HomepageBlockFilter struct {
	TenantID  uint              `json:"tenant_id,omitempty"`
	WebsiteID uint              `json:"website_id,omitempty"`
	BlockType HomepageBlockType `json:"block_type,omitempty"`
	IsActive  *bool             `json:"is_active,omitempty"`
	Status    string            `json:"status,omitempty"`
	Page      int               `json:"page,omitempty"`
	PageSize  int               `json:"page_size,omitempty"`
	SortBy    string            `json:"sort_by,omitempty"`
	SortOrder string            `json:"sort_order,omitempty"`
}

// BlockReorderRequest represents the request to reorder blocks
type BlockReorderRequest struct {
	BlockIDs []uint `json:"block_ids" validate:"required,min=1"`
}

// BlockPreviewRequest represents the request to preview a block
type BlockPreviewRequest struct {
	DeviceType string `json:"device_type,omitempty" validate:"omitempty,oneof=desktop tablet mobile"`
	Context    JSON   `json:"context,omitempty"`
}

// BlockPreviewResponse represents the response for block preview
type BlockPreviewResponse struct {
	HTML string `json:"html"`
	CSS  string `json:"css,omitempty"`
	JS   string `json:"js,omitempty"`
}

// BlockTemplate represents a reusable block template
type BlockTemplate struct {
	ID          uint              `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint              `gorm:"not null;index" json:"tenant_id"`
	Name        string            `gorm:"type:varchar(255);not null" json:"name" validate:"required,min=1,max=255"`
	Description string            `gorm:"type:text" json:"description,omitempty"`
	BlockType   HomepageBlockType `gorm:"type:varchar(50);not null" json:"block_type" validate:"required"`
	Configuration JSON            `gorm:"type:json" json:"configuration"`
	Styling     JSON              `gorm:"type:json" json:"styling"`
	IsPublic    bool              `gorm:"default:false" json:"is_public"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// TableName returns the table name for the BlockTemplate model
func (BlockTemplate) TableName() string {
	return "homepage_block_templates"
}

// BlockTemplateCreateRequest represents the request to create a block template
type BlockTemplateCreateRequest struct {
	TenantID      uint              `json:"tenant_id" validate:"required,min=1"`
	Name          string            `json:"name" validate:"required,min=1,max=255"`
	Description   string            `json:"description,omitempty" validate:"max=1000"`
	BlockType     HomepageBlockType `json:"block_type" validate:"required"`
	Configuration JSON              `json:"configuration"`
	Styling       JSON              `json:"styling"`
	IsPublic      bool              `json:"is_public"`
}

// BlockTemplateUpdateRequest represents the request to update a block template
type BlockTemplateUpdateRequest struct {
	Name          string            `json:"name" validate:"required,min=1,max=255"`
	Description   string            `json:"description,omitempty" validate:"max=1000"`
	BlockType     HomepageBlockType `json:"block_type" validate:"required"`
	Configuration JSON              `json:"configuration"`
	Styling       JSON              `json:"styling"`
	IsPublic      bool              `json:"is_public"`
}

// BlockTemplateResponse represents the response when returning block template data
type BlockTemplateResponse struct {
	ID            uint              `json:"id"`
	TenantID      uint              `json:"tenant_id"`
	Name          string            `json:"name"`
	Description   string            `json:"description,omitempty"`
	BlockType     HomepageBlockType `json:"block_type"`
	Configuration JSON              `json:"configuration"`
	Styling       JSON              `json:"styling"`
	IsPublic      bool              `json:"is_public"`
	CreatedAt     time.Time         `json:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at"`
}

// FromBlockTemplate converts a BlockTemplate model to BlockTemplateResponse
func (btr *BlockTemplateResponse) FromBlockTemplate(template *BlockTemplate) {
	btr.ID = template.ID
	btr.TenantID = template.TenantID
	btr.Name = template.Name
	btr.Description = template.Description
	btr.BlockType = template.BlockType
	btr.Configuration = template.Configuration
	btr.Styling = template.Styling
	btr.IsPublic = template.IsPublic
	btr.CreatedAt = template.CreatedAt
	btr.UpdatedAt = template.UpdatedAt
}

// BlockTemplateFilter represents filters for querying block templates
type BlockTemplateFilter struct {
	TenantID  uint              `json:"tenant_id,omitempty"`
	BlockType HomepageBlockType `json:"block_type,omitempty"`
	IsPublic  *bool             `json:"is_public,omitempty"`
	Status    string            `json:"status,omitempty"`
	Search    string            `json:"search,omitempty"`
	Page      int               `json:"page,omitempty"`
	PageSize  int               `json:"page_size,omitempty"`
	SortBy    string            `json:"sort_by,omitempty"`
	SortOrder string            `json:"sort_order,omitempty"`
}

// Block Configuration Types

// FeaturedPostsConfiguration represents configuration for featured posts block
type FeaturedPostsConfiguration struct {
	MaxPosts         int                    `json:"max_posts" validate:"min=1,max=50"`
	Layout           string                 `json:"layout" validate:"oneof=grid list carousel masonry"`
	Columns          ResponsiveColumns      `json:"columns"`
	ShowExcerpt      bool                   `json:"show_excerpt"`
	ExcerptLength    int                    `json:"excerpt_length" validate:"min=50,max=500"`
	ShowAuthor       bool                   `json:"show_author"`
	ShowDate         bool                   `json:"show_date"`
	ShowCategory     bool                   `json:"show_category"`
	ShowTags         bool                   `json:"show_tags"`
	ShowReadMore     bool                   `json:"show_read_more"`
	ImageAspectRatio string                 `json:"image_aspect_ratio" validate:"oneof=16:9 4:3 square auto"`
	ImagePosition    string                 `json:"image_position" validate:"oneof=top left right background"`
	CategoryFilter   []uint                 `json:"category_filter,omitempty"`
	TagFilter        []uint                 `json:"tag_filter,omitempty"`
	AuthorFilter     []uint                 `json:"author_filter,omitempty"`
	DateRange        DateRangeFilter        `json:"date_range,omitempty"`
	SortBy           string                 `json:"sort_by" validate:"oneof=date views comments title"`
	SortOrder        string                 `json:"sort_order" validate:"oneof=asc desc"`
	ExcludeCurrentPost bool                 `json:"exclude_current_post"`
	EnableLazyLoading  bool                 `json:"enable_lazy_loading"`
	EnableInfiniteScroll bool               `json:"enable_infinite_scroll"`
}

// CategoryShowcaseConfiguration represents configuration for category showcase block
type CategoryShowcaseConfiguration struct {
	CategoryIDs        []uint            `json:"category_ids,omitempty"`
	MaxCategories      int               `json:"max_categories" validate:"min=1,max=50"`
	Layout             string            `json:"layout" validate:"oneof=grid list carousel masonry"`
	Columns            ResponsiveColumns `json:"columns"`
	ShowPostCount      bool              `json:"show_post_count"`
	ShowDescription    bool              `json:"show_description"`
	ShowSubcategories  bool              `json:"show_subcategories"`
	MaxSubcategories   int               `json:"max_subcategories" validate:"min=0,max=10"`
	ShowCategoryImage  bool              `json:"show_category_image"`
	ImageAspectRatio   string            `json:"image_aspect_ratio" validate:"oneof=16:9 4:3 square auto"`
	CategoryLinkType   string            `json:"category_link_type" validate:"oneof=category_page post_list custom_url"`
	SortBy             string            `json:"sort_by" validate:"oneof=name post_count created_date custom_order"`
	SortOrder          string            `json:"sort_order" validate:"oneof=asc desc"`
	IncludeEmptyCategories bool          `json:"include_empty_categories"`
	EnableHoverEffects bool              `json:"enable_hover_effects"`
	EnableLazyLoading  bool              `json:"enable_lazy_loading"`
}

// ResponsiveColumns represents responsive column configuration
type ResponsiveColumns struct {
	Desktop int `json:"desktop" validate:"min=1,max=12"`
	Tablet  int `json:"tablet" validate:"min=1,max=6"`
	Mobile  int `json:"mobile" validate:"min=1,max=3"`
}

// DateRangeFilter represents date range filtering
type DateRangeFilter struct {
	From *time.Time `json:"from,omitempty"`
	To   *time.Time `json:"to,omitempty"`
}

// VideoItem represents a video in video gallery
type VideoItem struct {
	ID          uint   `json:"id"`
	Title       string `json:"title" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty" validate:"max=1000"`
	URL         string `json:"url" validate:"required,url"`
	Thumbnail   string `json:"thumbnail,omitempty" validate:"omitempty,url"`
	Duration    int    `json:"duration,omitempty"`
	ViewCount   int    `json:"view_count,omitempty"`
	SourceType  string `json:"source_type" validate:"required,oneof=youtube vimeo direct embed"`
	EmbedCode   string `json:"embed_code,omitempty"`
	IsFeatured  bool   `json:"is_featured"`
	SortOrder   int    `json:"sort_order"`
	CreatedAt   time.Time `json:"created_at"`
}

// VideoGalleryConfiguration represents configuration for video gallery block
type VideoGalleryConfiguration struct {
	Videos              []VideoItem       `json:"videos"`
	MaxVideos           int               `json:"max_videos" validate:"min=1,max=50"`
	Layout              string            `json:"layout" validate:"oneof=grid carousel masonry featured"`
	Columns             ResponsiveColumns `json:"columns"`
	VideoAspectRatio    string            `json:"video_aspect_ratio" validate:"oneof=16:9 4:3 square auto"`
	Autoplay            bool              `json:"autoplay"`
	ShowControls        bool              `json:"show_controls"`
	ShowTitle           bool              `json:"show_title"`
	ShowDescription     bool              `json:"show_description"`
	ShowDuration        bool              `json:"show_duration"`
	ShowViewCount       bool              `json:"show_view_count"`
	ThumbnailQuality    string            `json:"thumbnail_quality" validate:"oneof=high medium low"`
	EnableLightbox      bool              `json:"enable_lightbox"`
	EnableFullscreen    bool              `json:"enable_fullscreen"`
	EnableLazyLoading   bool              `json:"enable_lazy_loading"`
	EnableInfiniteScroll bool             `json:"enable_infinite_scroll"`
	SortBy              string            `json:"sort_by" validate:"oneof=date views duration title custom_order"`
	SortOrder           string            `json:"sort_order" validate:"oneof=asc desc"`
}