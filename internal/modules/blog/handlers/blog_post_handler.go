package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type BlogPostHandler struct {
	postService services.BlogPostService
}

func NewBlogPostHandler(postService services.BlogPostService) *BlogPostHandler {
	return &BlogPostHandler{
		postService: postService,
	}
}

// convertToPostResponse converts models.BlogPostResponse to dto.BlogPostResponse
func (h *BlogPostHandler) convertToPostResponse(post *models.BlogPostResponse) *dto.BlogPostResponse {
	response := &dto.BlogPostResponse{
		ID:            post.ID,
		TenantID:      post.TenantID,
		WebsiteID:     post.WebsiteID,
		Slug:          post.Slug,
		Title:         post.Title,
		Content:       post.Content,
		Excerpt:       post.Excerpt,
		AuthorID:      post.AuthorID,
		CategoryID:    post.CategoryID,
		Type:          post.Type,
		IsFeatured:    post.IsFeatured,
		AllowComments: post.AllowComments,
		FeaturedImage: post.FeaturedImage,
		ViewCount:     post.ViewCount,
		ScheduledAt:   post.ScheduledAt,
		PublishedAt:   post.PublishedAt,
		Status:        post.Status,
		CreatedAt:     post.CreatedAt,
		UpdatedAt:     post.UpdatedAt,
	}

	// Convert category if exists
	if post.Category != nil {
		response.Category = h.convertToCategoryResponse(post.Category)
	}

	// Convert tags if exists
	if len(post.Tags) > 0 {
		response.Tags = make([]dto.BlogTagResponse, len(post.Tags))
		for i, tag := range post.Tags {
			response.Tags[i] = *h.convertToTagResponse(&tag)
		}
	}

	return response
}

// convertToCategoryResponse converts models.BlogCategoryResponse to dto.BlogCategoryResponse
func (h *BlogPostHandler) convertToCategoryResponse(category *models.BlogCategoryResponse) *dto.BlogCategoryResponse {
	return &dto.BlogCategoryResponse{
		ID:          category.ID,
		TenantID:    category.TenantID,
		WebsiteID:   category.WebsiteID,
		Name:        category.Name,
		Slug:        category.Slug,
		Description: category.Description,
		ParentID:    category.ParentID,
		SortOrder:   int(category.SortOrder),
		PostCount:   0, // Not available in models.BlogCategoryResponse
		IsActive:    category.IsActive,
		MetaTitle:   "", // Not available in models.BlogCategoryResponse
		MetaKeywords: "", // Not available in models.BlogCategoryResponse
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}
}

// convertToTagResponse converts models.BlogTagResponse to dto.BlogTagResponse
func (h *BlogPostHandler) convertToTagResponse(tag *models.BlogTagResponse) *dto.BlogTagResponse {
	return &dto.BlogTagResponse{
		ID:          tag.ID,
		TenantID:    tag.TenantID,
		WebsiteID:   tag.WebsiteID,
		Name:        tag.Name,
		Slug:        tag.Slug,
		Description: tag.Description,
		Color:       tag.Color,
		PostCount:   int(tag.PostCount),
		IsActive:    tag.IsActive,
		CreatedAt:   tag.CreatedAt,
		UpdatedAt:   tag.UpdatedAt,
	}
}

// CreatePost creates a new blog post
// @Summary      Create a new blog post
// @Description  Creates a new blog post with the provided data
// @Tags         Blog Posts
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        request body dto.BlogPostCreateRequest true "Blog post data"
// @Success      201 {object} response.Response{data=dto.BlogPostResponse} "Blog post created successfully"
// @Failure      400 {object} response.Response "Invalid request body"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to create post"
// @Router       /api/cms/v1/blog/posts [post]
func (h *BlogPostHandler) CreatePost(c *gin.Context) {
	var req dto.BlogPostCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}
	req.TenantID = tenantID.(uint)

	// Convert DTO to models
	serviceReq := &models.BlogPostCreateRequest{
		TenantID:      req.TenantID,
		WebsiteID:     req.WebsiteID,
		Slug:          req.Slug,
		Title:         req.Title,
		Content:       req.Content,
		Excerpt:       req.Excerpt,
		AuthorID:      req.AuthorID,
		CategoryID:    req.CategoryID,
		Type:          req.Type,
		IsFeatured:    req.IsFeatured,
		AllowComments: req.AllowComments,
		Password:      req.Password,
		FeaturedImage: req.FeaturedImage,
		ScheduledAt:   req.ScheduledAt,
		Status:        req.Status,
		TagIDs:        req.TagIDs,
	}

	post, err := h.postService.Create(c.Request.Context(), serviceReq)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to create post")
		return
	}

	// Convert models response to DTO response
	response := h.convertToPostResponse(post)

	httpresponse.Created(c.Writer, response)
}

// GetPost retrieves a blog post by ID
// @Summary      Get a blog post by ID
// @Description  Retrieves a single blog post by its ID
// @Tags         Blog Posts
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Blog post ID"
// @Success      200 {object} response.Response{data=dto.BlogPostResponse} "Blog post retrieved successfully"
// @Failure      400 {object} response.Response "Invalid post ID"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Post not found"
// @Router       /api/cms/v1/blog/posts/{id} [get]
func (h *BlogPostHandler) GetPost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	post, err := h.postService.GetByID(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		httpresponse.NotFound(c.Writer, "Post not found")
		return
	}

	// Convert models response to DTO response
	response := h.convertToPostResponse(post)

	httpresponse.OK(c.Writer, response)
}

// GetPostBySlug retrieves a blog post by slug
func (h *BlogPostHandler) GetPostBySlug(c *gin.Context) {
	slug := c.Param("slug")
	websiteIDStr := c.Query("website_id")
	
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	post, err := h.postService.GetBySlug(c.Request.Context(), tenantID.(uint), uint(websiteID), slug)
	if err != nil {
		httpresponse.NotFound(c.Writer, "Post not found")
		return
	}

	// Increment view count for published posts
	if post.Status == models.BlogPostStatusPublished {
		_ = h.postService.IncrementViewCount(c.Request.Context(), tenantID.(uint), post.ID)
	}

	// Convert models response to DTO response
	response := h.convertToPostResponse(post)

	httpresponse.OK(c.Writer, response)
}

// UpdatePost updates a blog post
// @Summary      Update a blog post
// @Description  Updates an existing blog post with the provided data
// @Tags         Blog Posts
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Blog post ID"
// @Param        request body dto.BlogPostUpdateRequest true "Blog post update data"
// @Success      200 {object} response.Response{data=dto.BlogPostResponse} "Blog post updated successfully"
// @Failure      400 {object} response.Response "Invalid request body or post ID"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Post not found"
// @Failure      500 {object} response.Response "Failed to update post"
// @Router       /api/cms/v1/blog/posts/{id} [put]
func (h *BlogPostHandler) UpdatePost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	var req dto.BlogPostUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	// Convert DTO to models
	serviceReq := &models.BlogPostUpdateRequest{
		CategoryID: req.CategoryID,
		ScheduledAt: req.ScheduledAt,
		TagIDs:     req.TagIDs,
	}

	if req.Slug != nil {
		serviceReq.Slug = *req.Slug
	}
	if req.Title != nil {
		serviceReq.Title = *req.Title
	}
	if req.Content != nil {
		serviceReq.Content = *req.Content
	}
	if req.Excerpt != nil {
		serviceReq.Excerpt = *req.Excerpt
	}
	if req.Type != nil {
		serviceReq.Type = *req.Type
	}
	if req.IsFeatured != nil {
		serviceReq.IsFeatured = *req.IsFeatured
	}
	if req.AllowComments != nil {
		serviceReq.AllowComments = *req.AllowComments
	}
	if req.Password != nil {
		serviceReq.Password = *req.Password
	}
	if req.FeaturedImage != nil {
		serviceReq.FeaturedImage = *req.FeaturedImage
	}
	if req.Status != nil {
		serviceReq.Status = *req.Status
	}

	post, err := h.postService.Update(c.Request.Context(), tenantID.(uint), uint(id), serviceReq)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to update post")
		return
	}

	// Convert models response to DTO response
	response := h.convertToPostResponse(post)

	httpresponse.OK(c.Writer, response)
}

// DeletePost deletes a blog post
func (h *BlogPostHandler) DeletePost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	err = h.postService.Delete(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to delete post")
		return
	}

	httpresponse.OK(c.Writer, nil)
}

// ListPosts lists blog posts with filtering
func (h *BlogPostHandler) ListPosts(c *gin.Context) {
	var filter models.BlogPostFilter

	// Parse query parameters
	if tenantIDStr := c.Query("tenant_id"); tenantIDStr != "" {
		if tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
			filter.TenantID = uint(tenantID)
		}
	}

	if websiteIDStr := c.Query("website_id"); websiteIDStr != "" {
		if websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32); err == nil {
			filter.WebsiteID = uint(websiteID)
		}
	}

	if authorIDStr := c.Query("author_id"); authorIDStr != "" {
		if authorID, err := strconv.ParseUint(authorIDStr, 10, 32); err == nil {
			filter.AuthorID = uint(authorID)
		}
	}

	if categoryIDStr := c.Query("category_id"); categoryIDStr != "" {
		if categoryID, err := strconv.ParseUint(categoryIDStr, 10, 32); err == nil {
			categoryIDUint := uint(categoryID)
			filter.CategoryID = &categoryIDUint
		}
	}

	if typeStr := c.Query("type"); typeStr != "" {
		filter.Type = models.BlogPostType(typeStr)
	}

	if statusStr := c.Query("status"); statusStr != "" {
		filter.Status = models.BlogPostStatus(statusStr)
	}

	if isFeaturedStr := c.Query("is_featured"); isFeaturedStr != "" {
		if isFeatured, err := strconv.ParseBool(isFeaturedStr); err == nil {
			filter.IsFeatured = &isFeatured
		}
	}

	filter.Search = c.Query("search")
	filter.Page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
	filter.PageSize, _ = strconv.Atoi(c.DefaultQuery("page_size", "20"))
	filter.SortBy = c.DefaultQuery("sort_by", "created_at")
	filter.SortOrder = c.DefaultQuery("sort_order", "desc")

	// Set tenant ID from context if not provided
	if filter.TenantID == 0 {
		tenantID, exists := c.Get("tenant_id")
		if !exists {
			httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
			return
		}
		filter.TenantID = tenantID.(uint)
	}

	posts, total, err := h.postService.List(c.Request.Context(), &filter)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to list posts")
		return
	}

	data := map[string]interface{}{
		"posts": posts,
		"pagination": map[string]interface{}{
			"page":       filter.Page,
			"page_size":  filter.PageSize,
			"total":      int(total),
			"total_pages": (int(total) + filter.PageSize - 1) / filter.PageSize,
		},
	}

	httpresponse.OK(c.Writer, data)
}

// ListPostsWithCursor lists blog posts with cursor-based pagination
// @Summary      List blog posts with cursor pagination
// @Description  Get paginated list of blog posts with filters using cursor pagination
// @Tags         Blog Posts
// @Produce      json
// @Security     Bearer
// @Param        website_id path uint true "Website ID"
// @Param        title query string false "Title filter"
// @Param        status query string false "Status filter" Enums(draft,review,published,scheduled,archived,rejected)
// @Param        type query string false "Type filter" Enums(post,page,announcement)
// @Param        category_id query uint false "Category ID filter"
// @Param        author_id query uint false "Author ID filter"
// @Param        is_featured query bool false "Featured filter"
// @Param        tag_id query uint false "Tag ID filter"
// @Param        date_from query string false "Date from filter (RFC3339)"
// @Param        date_to query string false "Date to filter (RFC3339)"
// @Param        cursor query string false "Pagination cursor"
// @Param        limit query int false "Items per page" default(20)
// @Param        sort_by query string false "Sort by field" Enums(id,title,created_at,updated_at,published_at,view_count) default(created_at)
// @Param        sort_order query string false "Sort order" Enums(asc,desc) default(desc)
// @Success      200 {object} dto.BlogPostListResponse
// @Failure      400 {object} httpresponse.Response "Invalid filter parameters"
// @Failure      401 {object} httpresponse.Response "Authentication required"
// @Failure      500 {object} httpresponse.Response "Failed to retrieve blog posts"
// @Router       /api/cms/v1/websites/{website_id}/blog/posts [get]
func (h *BlogPostHandler) ListPostsWithCursor(c *gin.Context) {
	var filter dto.BlogPostFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid filter parameters", err.Error())
		return
	}

	// Get website ID from path
	websiteIDStr := c.Param("website_id")
	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found in context")
		return
	}

	// Create cursor request
	cursorReq := &pagination.CursorRequest{
		Cursor: filter.Cursor,
		Limit:  pagination.ValidateLimit(filter.Limit),
	}

	// Build filters map
	filters := make(map[string]interface{})
	if filter.Title != "" {
		filters["title"] = filter.Title
	}
	if filter.Status != nil {
		filters["status"] = *filter.Status
	}
	if filter.Type != nil {
		filters["type"] = *filter.Type
	}
	if filter.CategoryID != nil {
		filters["category_id"] = *filter.CategoryID
	}
	if filter.AuthorID != nil {
		filters["author_id"] = *filter.AuthorID
	}
	if filter.IsFeatured != nil {
		filters["is_featured"] = *filter.IsFeatured
	}
	if filter.TagID != nil {
		filters["tag_id"] = *filter.TagID
	}
	if filter.DateFrom != nil {
		filters["date_from"] = *filter.DateFrom
	}
	if filter.DateTo != nil {
		filters["date_to"] = *filter.DateTo
	}
	if filter.SortBy != "" {
		filters["sort_by"] = filter.SortBy
	}
	if filter.SortOrder != "" {
		filters["sort_order"] = filter.SortOrder
	}

	blogPostResponse, err := h.postService.ListWithCursor(c.Request.Context(), tenantID.(uint), uint(websiteID), cursorReq, filters)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to retrieve blog posts")
		return
	}

	httpresponse.OK(c.Writer, blogPostResponse)
}

// GetPublishedPosts retrieves published blog posts
func (h *BlogPostHandler) GetPublishedPosts(c *gin.Context) {
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	posts, total, err := h.postService.GetPublished(c.Request.Context(), tenantID.(uint), uint(websiteID), limit, offset)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to get published posts")
		return
	}

	data := map[string]interface{}{
		"posts": posts,
		"total": total,
	}

	httpresponse.OK(c.Writer, data)
}

// GetFeaturedPosts retrieves featured blog posts
func (h *BlogPostHandler) GetFeaturedPosts(c *gin.Context) {
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	posts, err := h.postService.GetFeatured(c.Request.Context(), tenantID.(uint), uint(websiteID), limit)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to get featured posts")
		return
	}

	httpresponse.OK(c.Writer, posts)
}

// GetRelatedPosts retrieves related blog posts
func (h *BlogPostHandler) GetRelatedPosts(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "5"))

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	posts, err := h.postService.GetRelated(c.Request.Context(), tenantID.(uint), uint(id), limit)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to get related posts")
		return
	}

	httpresponse.OK(c.Writer, posts)
}

// PublishPost publishes a blog post
func (h *BlogPostHandler) PublishPost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	post, err := h.postService.Publish(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to publish post")
		return
	}

	httpresponse.OK(c.Writer, post)
}

// UnpublishPost unpublishes a blog post
func (h *BlogPostHandler) UnpublishPost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	post, err := h.postService.Unpublish(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to unpublish post")
		return
	}

	httpresponse.OK(c.Writer, post)
}

// GetPostStats retrieves blog post statistics
func (h *BlogPostHandler) GetPostStats(c *gin.Context) {
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	stats, err := h.postService.GetStats(c.Request.Context(), tenantID.(uint), uint(websiteID))
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to get post stats")
		return
	}

	httpresponse.OK(c.Writer, stats)
}

// GetPopularPosts retrieves popular blog posts
func (h *BlogPostHandler) GetPopularPosts(c *gin.Context) {
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	days, _ := strconv.Atoi(c.DefaultQuery("days", "7"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	posts, err := h.postService.GetPopular(c.Request.Context(), tenantID.(uint), uint(websiteID), days, limit)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to get popular posts")
		return
	}

	httpresponse.OK(c.Writer, posts)
}

// AttachTags attaches tags to a blog post
func (h *BlogPostHandler) AttachTags(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	var req struct {
		TagIDs []uint `json:"tag_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	err = h.postService.AttachTags(c.Request.Context(), tenantID.(uint), uint(id), req.TagIDs)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to attach tags")
		return
	}

	httpresponse.OK(c.Writer, nil)
}

// DetachTags detaches tags from a blog post
func (h *BlogPostHandler) DetachTags(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	var req struct {
		TagIDs []uint `json:"tag_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	err = h.postService.DetachTags(c.Request.Context(), tenantID.(uint), uint(id), req.TagIDs)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to detach tags")
		return
	}

	httpresponse.OK(c.Writer, nil)
}

// SyncTags syncs tags for a blog post
func (h *BlogPostHandler) SyncTags(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid post ID")
		return
	}

	var req struct {
		TagIDs []uint `json:"tag_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	err = h.postService.SyncTags(c.Request.Context(), tenantID.(uint), uint(id), req.TagIDs)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to sync tags")
		return
	}

	httpresponse.OK(c.Writer, nil)
}