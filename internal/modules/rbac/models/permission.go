package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// PermissionStatus represents the status of a permission
// @Enum active,inactive,deprecated
type PermissionStatus string

const (
	PermissionStatusActive     PermissionStatus = "active"
	PermissionStatusInactive   PermissionStatus = "inactive"
	PermissionStatusDeprecated PermissionStatus = "deprecated"
)

// Scan implements sql.Scanner interface
func (s *PermissionStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = PermissionStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s PermissionStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// PermissionScope represents the scope of a permission
// @Enum tenant,website,global
type PermissionScope string

const (
	PermissionScopeTenant  PermissionScope = "tenant"
	PermissionScopeWebsite PermissionScope = "website"
	PermissionScopeGlobal  PermissionScope = "global"
)

// Scan implements sql.Scanner interface
func (s *PermissionScope) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = PermissionScope(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s PermissionScope) Value() (driver.Value, error) {
	return string(s), nil
}

// PermissionRiskLevel represents the risk level of a permission
// @Enum low,medium,high,critical
type PermissionRiskLevel string

const (
	PermissionRiskLevelLow      PermissionRiskLevel = "low"
	PermissionRiskLevelMedium   PermissionRiskLevel = "medium"
	PermissionRiskLevelHigh     PermissionRiskLevel = "high"
	PermissionRiskLevelCritical PermissionRiskLevel = "critical"
)

// Scan implements sql.Scanner interface
func (s *PermissionRiskLevel) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = PermissionRiskLevel(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s PermissionRiskLevel) Value() (driver.Value, error) {
	return string(s), nil
}

// PermissionConditions represents a JSON object of permission conditions
type PermissionConditions map[string]interface{}

// Scan implements sql.Scanner interface
func (pc *PermissionConditions) Scan(value interface{}) error {
	if value == nil {
		*pc = make(PermissionConditions)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, pc)
}

// Value implements driver.Valuer interface
func (pc PermissionConditions) Value() (driver.Value, error) {
	if pc == nil {
		return "{}", nil
	}
	return json.Marshal(pc)
}

// PermissionLimitations represents a JSON object of permission limitations
type PermissionLimitations map[string]interface{}

// Scan implements sql.Scanner interface
func (pl *PermissionLimitations) Scan(value interface{}) error {
	if value == nil {
		*pl = make(PermissionLimitations)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, pl)
}

// Value implements driver.Valuer interface
func (pl PermissionLimitations) Value() (driver.Value, error) {
	if pl == nil {
		return "{}", nil
	}
	return json.Marshal(pl)
}

// Permission represents a permission in the RBAC system
type Permission struct {
	ID uint `json:"id" gorm:"primaryKey"`

	// Permission Information
	Name        string  `json:"name" gorm:"not null;size:100;uniqueIndex" validate:"required,max=100,matches=^[a-z0-9_.-]+$"`
	DisplayName string  `json:"display_name" gorm:"not null;size:255" validate:"required,max=255"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Permission Categorization
	Module   string `json:"module" gorm:"not null;size:50" validate:"required,max=50,matches=^[a-z0-9_-]+$"`
	Resource string `json:"resource" gorm:"not null;size:100" validate:"required,max=100,matches=^[a-z0-9_-]+$"`
	Action   string `json:"action" gorm:"not null;size:50" validate:"required,max=50,matches=^[a-z0-9_-]+$"`

	// Permission Scope and Context
	Scope             PermissionScope `json:"scope" gorm:"type:enum('tenant','website','global');default:'tenant';not null"`
	RequiresOwnership bool            `json:"requires_ownership" gorm:"default:false"`

	// Permission Metadata
	IsSystemPermission bool                    `json:"is_system_permission" gorm:"default:false"`
	RiskLevel          PermissionRiskLevel     `json:"risk_level" gorm:"type:enum('low','medium','high','critical');default:'low';not null"`

	// Permission Constraints
	Conditions  PermissionConditions  `json:"conditions" gorm:"type:json;default:'{}'"`
	Limitations PermissionLimitations `json:"limitations" gorm:"type:json;default:'{}'"`

	// Status and Timestamps
	Status    PermissionStatus `json:"status" gorm:"type:enum('active','inactive','deprecated');default:'active';not null"`
	CreatedAt time.Time        `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time        `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations (loaded separately to avoid circular references)
	// RolePermissions []RolePermission `json:"role_permissions,omitempty" gorm:"foreignKey:PermissionID"`
}

// TableName specifies the table name for Permission
func (Permission) TableName() string {
	return "rbac_permissions"
}

// IsActive checks if permission is active
func (p *Permission) IsActive() bool {
	return p.Status == PermissionStatusActive
}

// IsSystemPermissionCheck checks if permission is a system permission
func (p *Permission) IsSystemPermissionCheck() bool {
	return p.IsSystemPermission
}

// IsHighRisk checks if permission is high risk or critical
func (p *Permission) IsHighRisk() bool {
	return p.RiskLevel == PermissionRiskLevelHigh || p.RiskLevel == PermissionRiskLevelCritical
}

// GetFullName returns the full permission name (module.resource.action)
func (p *Permission) GetFullName() string {
	return p.Module + "." + p.Resource + "." + p.Action
}

// HasCondition checks if permission has a specific condition
func (p *Permission) HasCondition(condition string) bool {
	_, exists := p.Conditions[condition]
	return exists
}

// GetConditionValue gets the value of a specific condition
func (p *Permission) GetConditionValue(condition string) interface{} {
	return p.Conditions[condition]
}

// HasLimitation checks if permission has a specific limitation
func (p *Permission) HasLimitation(limitation string) bool {
	_, exists := p.Limitations[limitation]
	return exists
}

// GetLimitationValue gets the value of a specific limitation
func (p *Permission) GetLimitationValue(limitation string) interface{} {
	return p.Limitations[limitation]
}

// BeforeCreate hook for Permission
func (p *Permission) BeforeCreate() error {
	if p.Conditions == nil {
		p.Conditions = make(PermissionConditions)
	}
	if p.Limitations == nil {
		p.Limitations = make(PermissionLimitations)
	}
	return nil
}

// PermissionFilter represents filters for querying permissions
type PermissionFilter struct {
	Status             PermissionStatus     `json:"status,omitempty"`
	Module             string               `json:"module,omitempty"`
	Resource           string               `json:"resource,omitempty"`
	Action             string               `json:"action,omitempty"`
	Scope              PermissionScope      `json:"scope,omitempty"`
	RiskLevel          PermissionRiskLevel  `json:"risk_level,omitempty"`
	IsSystemPermission *bool                `json:"is_system_permission,omitempty"`
	RequiresOwnership  *bool                `json:"requires_ownership,omitempty"`
	Search             string               `json:"search,omitempty"`
	Page               int                  `json:"page,omitempty"`
	PageSize           int                  `json:"page_size,omitempty"`
	SortBy             string               `json:"sort_by,omitempty"`
	SortOrder          string               `json:"sort_order,omitempty"`
}

// PermissionCreateRequest represents the request to create a permission
type PermissionCreateRequest struct {
	Name               string                    `json:"name" validate:"required,max=100,matches=^[a-z0-9_.-]+$"`
	DisplayName        string                    `json:"display_name" validate:"required,max=255"`
	Description        *string                   `json:"description,omitempty"`
	Module             string                    `json:"module" validate:"required,max=50,matches=^[a-z0-9_-]+$"`
	Resource           string                    `json:"resource" validate:"required,max=100,matches=^[a-z0-9_-]+$"`
	Action             string                    `json:"action" validate:"required,max=50,matches=^[a-z0-9_-]+$"`
	Scope              PermissionScope           `json:"scope,omitempty" validate:"omitempty,oneof=tenant website global"`
	RequiresOwnership  bool                      `json:"requires_ownership,omitempty"`
	IsSystemPermission bool                      `json:"is_system_permission,omitempty"`
	RiskLevel          PermissionRiskLevel       `json:"risk_level,omitempty" validate:"omitempty,oneof=low medium high critical"`
	Conditions         PermissionConditions      `json:"conditions,omitempty"`
	Limitations        PermissionLimitations     `json:"limitations,omitempty"`
}

// PermissionUpdateRequest represents the request to update a permission
type PermissionUpdateRequest struct {
	Name               *string                    `json:"name,omitempty" validate:"omitempty,max=100,matches=^[a-z0-9_.-]+$"`
	DisplayName        *string                    `json:"display_name,omitempty" validate:"omitempty,max=255"`
	Description        *string                    `json:"description,omitempty"`
	Module             *string                    `json:"module,omitempty" validate:"omitempty,max=50,matches=^[a-z0-9_-]+$"`
	Resource           *string                    `json:"resource,omitempty" validate:"omitempty,max=100,matches=^[a-z0-9_-]+$"`
	Action             *string                    `json:"action,omitempty" validate:"omitempty,max=50,matches=^[a-z0-9_-]+$"`
	Scope              *PermissionScope           `json:"scope,omitempty" validate:"omitempty,oneof=tenant website global"`
	RequiresOwnership  *bool                      `json:"requires_ownership,omitempty"`
	IsSystemPermission *bool                      `json:"is_system_permission,omitempty"`
	RiskLevel          *PermissionRiskLevel       `json:"risk_level,omitempty" validate:"omitempty,oneof=low medium high critical"`
	Status             *PermissionStatus          `json:"status,omitempty" validate:"omitempty,oneof=active inactive deprecated"`
	Conditions         *PermissionConditions      `json:"conditions,omitempty"`
	Limitations        *PermissionLimitations     `json:"limitations,omitempty"`
}

// PermissionBulkCreateRequest represents the request to bulk create permissions
type PermissionBulkCreateRequest struct {
	Permissions []PermissionCreateRequest `json:"permissions" validate:"required,min=1,dive"`
}

// PermissionBulkUpdateRequest represents the request to bulk update permissions
type PermissionBulkUpdateRequest struct {
	PermissionIDs []uint                  `json:"permission_ids" validate:"required,min=1,dive,min=1"`
	Updates       PermissionUpdateRequest `json:"updates" validate:"required"`
}