package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// UserRoleStatus represents the status of a user role assignment
// @Enum active,inactive,suspended,revoked
type UserRoleStatus string

const (
	UserRoleStatusActive    UserRoleStatus = "active"
	UserRoleStatusInactive  UserRoleStatus = "inactive"
	UserRoleStatusSuspended UserRoleStatus = "suspended"
	UserRoleStatusRevoked   UserRoleStatus = "revoked"
)

// Scan implements sql.Scanner interface
func (s *UserRoleStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = UserRoleStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s UserRoleStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// UserRoleContextType represents the context type for user role
// @Enum tenant,website,global
type UserRoleContextType string

const (
	UserRoleContextTypeTenant  UserRoleContextType = "tenant"
	UserRoleContextTypeWebsite UserRoleContextType = "website"
	UserRoleContextTypeGlobal  UserRoleContextType = "global"
)

// Scan implements sql.Scanner interface
func (s *UserRoleContextType) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = UserRoleContextType(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s UserRoleContextType) Value() (driver.Value, error) {
	return string(s), nil
}

// UserRoleConditions represents a JSON object of user role conditions
type UserRoleConditions map[string]interface{}

// Scan implements sql.Scanner interface
func (urc *UserRoleConditions) Scan(value interface{}) error {
	if value == nil {
		*urc = make(UserRoleConditions)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, urc)
}

// Value implements driver.Valuer interface
func (urc UserRoleConditions) Value() (driver.Value, error) {
	if urc == nil {
		return "{}", nil
	}
	return json.Marshal(urc)
}

// UserRoleLimitations represents a JSON object of user role limitations
type UserRoleLimitations map[string]interface{}

// Scan implements sql.Scanner interface
func (url *UserRoleLimitations) Scan(value interface{}) error {
	if value == nil {
		*url = make(UserRoleLimitations)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, url)
}

// Value implements driver.Valuer interface
func (url UserRoleLimitations) Value() (driver.Value, error) {
	if url == nil {
		return "{}", nil
	}
	return json.Marshal(url)
}

// UserRole represents a user-role assignment in the RBAC system
type UserRole struct {
	ID       uint `json:"id" gorm:"primaryKey"`
	TenantID uint `json:"tenant_id" gorm:"not null;index"`
	UserID   uint `json:"user_id" gorm:"not null;index"`
	RoleID   uint `json:"role_id" gorm:"not null;index"`

	// Assignment Information
	AssignedBy *uint     `json:"assigned_by,omitempty" gorm:"index"`
	AssignedAt time.Time `json:"assigned_at" gorm:"autoCreateTime"`

	// Role Context and Scope
	ContextType UserRoleContextType `json:"context_type" gorm:"type:enum('tenant','website','global');default:'tenant';not null"`
	ContextID   *uint               `json:"context_id,omitempty" gorm:"index"`

	// Role Assignment Constraints
	Conditions  UserRoleConditions  `json:"conditions" gorm:"type:json;default:'{}'"`
	Limitations UserRoleLimitations `json:"limitations" gorm:"type:json;default:'{}'"`

	// Assignment Metadata
	IsPrimary   bool `json:"is_primary" gorm:"default:false"`
	IsInherited bool `json:"is_inherited" gorm:"default:false"`
	IsTemporary bool `json:"is_temporary" gorm:"default:false"`

	// Temporal Constraints
	ValidFrom  *time.Time `json:"valid_from,omitempty" gorm:"index"`
	ValidUntil *time.Time `json:"valid_until,omitempty" gorm:"index"`

	// Status and Timestamps
	Status    UserRoleStatus `json:"status" gorm:"type:enum('active','inactive','suspended','revoked');default:'active';not null"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	RevokedAt *time.Time     `json:"revoked_at,omitempty"`
	RevokedBy *uint          `json:"revoked_by,omitempty" gorm:"index"`

	// Associations (loaded separately to avoid circular references)
	// User          *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
	// Role          *Role `json:"role,omitempty" gorm:"foreignKey:RoleID"`
	// AssignedByUser *User `json:"assigned_by_user,omitempty" gorm:"foreignKey:AssignedBy"`
	// RevokedByUser  *User `json:"revoked_by_user,omitempty" gorm:"foreignKey:RevokedBy"`
}

// TableName specifies the table name for UserRole
func (UserRole) TableName() string {
	return "rbac_user_roles"
}

// IsActive checks if user role is active
func (ur *UserRole) IsActive() bool {
	return ur.Status == UserRoleStatusActive
}

// IsRevoked checks if user role is revoked
func (ur *UserRole) IsRevoked() bool {
	return ur.Status == UserRoleStatusRevoked
}

// IsSuspended checks if user role is suspended
func (ur *UserRole) IsSuspended() bool {
	return ur.Status == UserRoleStatusSuspended
}

// IsPrimaryCheck checks if user role is primary
func (ur *UserRole) IsPrimaryCheck() bool {
	return ur.IsPrimary
}

// IsInheritedCheck checks if user role is inherited
func (ur *UserRole) IsInheritedCheck() bool {
	return ur.IsInherited
}

// IsTemporaryCheck checks if user role is temporary
func (ur *UserRole) IsTemporaryCheck() bool {
	return ur.IsTemporary
}

// IsExpired checks if user role has expired
func (ur *UserRole) IsExpired() bool {
	if ur.ValidUntil == nil {
		return false
	}
	return time.Now().After(*ur.ValidUntil)
}

// IsValid checks if user role is valid (not expired and in valid time range)
func (ur *UserRole) IsValid() bool {
	now := time.Now()
	
	// Check if valid from constraint is met
	if ur.ValidFrom != nil && now.Before(*ur.ValidFrom) {
		return false
	}
	
	// Check if valid until constraint is met
	if ur.ValidUntil != nil && now.After(*ur.ValidUntil) {
		return false
	}
	
	return true
}

// HasCondition checks if user role has a specific condition
func (ur *UserRole) HasCondition(condition string) bool {
	_, exists := ur.Conditions[condition]
	return exists
}

// GetConditionValue gets the value of a specific condition
func (ur *UserRole) GetConditionValue(condition string) interface{} {
	return ur.Conditions[condition]
}

// HasLimitation checks if user role has a specific limitation
func (ur *UserRole) HasLimitation(limitation string) bool {
	_, exists := ur.Limitations[limitation]
	return exists
}

// GetLimitationValue gets the value of a specific limitation
func (ur *UserRole) GetLimitationValue(limitation string) interface{} {
	return ur.Limitations[limitation]
}

// BeforeCreate hook for UserRole
func (ur *UserRole) BeforeCreate() error {
	if ur.Conditions == nil {
		ur.Conditions = make(UserRoleConditions)
	}
	if ur.Limitations == nil {
		ur.Limitations = make(UserRoleLimitations)
	}
	return nil
}

// UserRoleFilter represents filters for querying user roles
type UserRoleFilter struct {
	TenantID    *uint               `json:"tenant_id,omitempty"`
	UserID      *uint               `json:"user_id,omitempty"`
	RoleID      *uint               `json:"role_id,omitempty"`
	Status      UserRoleStatus      `json:"status,omitempty"`
	ContextType UserRoleContextType `json:"context_type,omitempty"`
	ContextID   *uint               `json:"context_id,omitempty"`
	IsPrimary   *bool               `json:"is_primary,omitempty"`
	IsInherited *bool               `json:"is_inherited,omitempty"`
	IsTemporary *bool               `json:"is_temporary,omitempty"`
	AssignedBy  *uint               `json:"assigned_by,omitempty"`
	ValidFrom   *time.Time          `json:"valid_from,omitempty"`
	ValidUntil  *time.Time          `json:"valid_until,omitempty"`
	Page        int                 `json:"page,omitempty"`
	PageSize    int                 `json:"page_size,omitempty"`
	SortBy      string              `json:"sort_by,omitempty"`
	SortOrder   string              `json:"sort_order,omitempty"`
}

// UserRoleCreateRequest represents the request to create a user role
type UserRoleCreateRequest struct {
	TenantID    uint                `json:"tenant_id" validate:"required,min=1"`
	UserID      uint                `json:"user_id" validate:"required,min=1"`
	RoleID      uint                `json:"role_id" validate:"required,min=1"`
	ContextType UserRoleContextType `json:"context_type,omitempty" validate:"omitempty,oneof=tenant website global"`
	ContextID   *uint               `json:"context_id,omitempty"`
	IsPrimary   bool                `json:"is_primary,omitempty"`
	IsInherited bool                `json:"is_inherited,omitempty"`
	IsTemporary bool                `json:"is_temporary,omitempty"`
	ValidFrom   *time.Time          `json:"valid_from,omitempty"`
	ValidUntil  *time.Time          `json:"valid_until,omitempty"`
	Conditions  UserRoleConditions  `json:"conditions,omitempty"`
	Limitations UserRoleLimitations `json:"limitations,omitempty"`
}

// UserRoleUpdateRequest represents the request to update a user role
type UserRoleUpdateRequest struct {
	ContextType *UserRoleContextType `json:"context_type,omitempty" validate:"omitempty,oneof=tenant website global"`
	ContextID   *uint                `json:"context_id,omitempty"`
	IsPrimary   *bool                `json:"is_primary,omitempty"`
	IsInherited *bool                `json:"is_inherited,omitempty"`
	IsTemporary *bool                `json:"is_temporary,omitempty"`
	ValidFrom   *time.Time           `json:"valid_from,omitempty"`
	ValidUntil  *time.Time           `json:"valid_until,omitempty"`
	Status      *UserRoleStatus      `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended revoked"`
	Conditions  *UserRoleConditions  `json:"conditions,omitempty"`
	Limitations *UserRoleLimitations `json:"limitations,omitempty"`
}

// UserRoleBulkCreateRequest represents the request to bulk create user roles
type UserRoleBulkCreateRequest struct {
	UserRoles []UserRoleCreateRequest `json:"user_roles" validate:"required,min=1,dive"`
}

// UserRoleBulkRevokeRequest represents the request to bulk revoke user roles
type UserRoleBulkRevokeRequest struct {
	UserRoleIDs []uint `json:"user_role_ids" validate:"required,min=1,dive,min=1"`
}

// UserRoleAssignRequest represents the request to assign a role to a user
type UserRoleAssignRequest struct {
	RoleID      uint                `json:"role_id" validate:"required,min=1"`
	ContextType UserRoleContextType `json:"context_type,omitempty" validate:"omitempty,oneof=tenant website global"`
	ContextID   *uint               `json:"context_id,omitempty"`
	IsPrimary   bool                `json:"is_primary,omitempty"`
	IsTemporary bool                `json:"is_temporary,omitempty"`
	ValidFrom   *time.Time          `json:"valid_from,omitempty"`
	ValidUntil  *time.Time          `json:"valid_until,omitempty"`
	Conditions  UserRoleConditions  `json:"conditions,omitempty"`
	Limitations UserRoleLimitations `json:"limitations,omitempty"`
}

// UserRoleRevokeRequest represents the request to revoke a role from a user
type UserRoleRevokeRequest struct {
	RoleID      uint                `json:"role_id" validate:"required,min=1"`
	ContextType UserRoleContextType `json:"context_type,omitempty" validate:"omitempty,oneof=tenant website global"`
	ContextID   *uint               `json:"context_id,omitempty"`
}