package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// RoleStatus represents the status of a role
// @Enum active,inactive,deprecated
type RoleStatus string

const (
	RoleStatusActive     RoleStatus = "active"
	RoleStatusInactive   RoleStatus = "inactive"
	RoleStatusDeprecated RoleStatus = "deprecated"
)

// Scan implements sql.Scanner interface
func (s *RoleStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = RoleStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s RoleStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// RoleScope represents the scope of a role
// @Enum tenant,website,global
type RoleScope string

const (
	RoleScopeTenant  RoleScope = "tenant"
	RoleScopeWebsite RoleScope = "website"
	RoleScopeGlobal  RoleScope = "global"
)

// Scan implements sql.Scanner interface
func (s *RoleScope) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = RoleScope(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s RoleScope) Value() (driver.Value, error) {
	return string(s), nil
}

// RoleCapabilities represents a JSON array of role capabilities
type RoleCapabilities []string

// Scan implements sql.Scanner interface
func (rc *RoleCapabilities) Scan(value interface{}) error {
	if value == nil {
		*rc = make(RoleCapabilities, 0)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, rc)
}

// Value implements driver.Valuer interface
func (rc RoleCapabilities) Value() (driver.Value, error) {
	if rc == nil {
		return "[]", nil
	}
	return json.Marshal(rc)
}

// RoleRestrictions represents a JSON object of role restrictions
type RoleRestrictions map[string]interface{}

// Scan implements sql.Scanner interface
func (rr *RoleRestrictions) Scan(value interface{}) error {
	if value == nil {
		*rr = make(RoleRestrictions)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, rr)
}

// Value implements driver.Valuer interface
func (rr RoleRestrictions) Value() (driver.Value, error) {
	if rr == nil {
		return "{}", nil
	}
	return json.Marshal(rr)
}

// Role represents a role in the RBAC system
type Role struct {
	ID       uint `json:"id" gorm:"primaryKey"`
	TenantID uint `json:"tenant_id" gorm:"not null;index"`

	// Role Information
	Name        string `json:"name" gorm:"not null;size:100" validate:"required,max=100,matches=^[a-z0-9_-]+$"`
	DisplayName string `json:"display_name" gorm:"not null;size:255" validate:"required,max=255"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Role Configuration
	IsSystemRole  bool `json:"is_system_role" gorm:"default:false"`
	IsDefaultRole bool `json:"is_default_role" gorm:"default:false"`
	Level         uint `json:"level" gorm:"default:0" validate:"min=0,max=100"`

	// Role Scope and Context
	Scope     RoleScope `json:"scope" gorm:"type:enum('tenant','website','global');default:'tenant';not null"`
	ContextID *uint     `json:"context_id,omitempty" gorm:"index"`

	// Role Metadata
	Color *string `json:"color,omitempty" gorm:"size:7" validate:"omitempty,matches=^#[0-9A-Fa-f]{6}$"`
	Icon  *string `json:"icon,omitempty" gorm:"size:100"`

	// Role Capabilities
	Capabilities RoleCapabilities `json:"capabilities" gorm:"type:json;default:'[]'"`
	Restrictions RoleRestrictions `json:"restrictions" gorm:"type:json;default:'{}'"`

	// Status and Timestamps
	Status    RoleStatus `json:"status" gorm:"type:enum('active','inactive','deprecated');default:'active';not null"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations (loaded separately to avoid circular references)
	// Tenant           *models.Tenant           `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	// RolePermissions  []RolePermission         `json:"role_permissions,omitempty" gorm:"foreignKey:RoleID"`
	// UserRoles        []UserRole               `json:"user_roles,omitempty" gorm:"foreignKey:RoleID"`
}

// TableName specifies the table name for Role
func (Role) TableName() string {
	return "rbac_roles"
}

// IsActive checks if role is active
func (r *Role) IsActive() bool {
	return r.Status == RoleStatusActive
}

// IsSystemRoleCheck checks if role is a system role
func (r *Role) IsSystemRoleCheck() bool {
	return r.IsSystemRole
}

// IsDefaultRoleCheck checks if role is a default role
func (r *Role) IsDefaultRoleCheck() bool {
	return r.IsDefaultRole
}

// HasCapability checks if role has a specific capability
func (r *Role) HasCapability(capability string) bool {
	for _, cap := range r.Capabilities {
		if cap == capability {
			return true
		}
	}
	return false
}

// HasRestriction checks if role has a specific restriction
func (r *Role) HasRestriction(restriction string) bool {
	_, exists := r.Restrictions[restriction]
	return exists
}

// GetRestrictionValue gets the value of a specific restriction
func (r *Role) GetRestrictionValue(restriction string) interface{} {
	return r.Restrictions[restriction]
}

// BeforeCreate hook for Role
func (r *Role) BeforeCreate() error {
	if r.Capabilities == nil {
		r.Capabilities = make(RoleCapabilities, 0)
	}
	if r.Restrictions == nil {
		r.Restrictions = make(RoleRestrictions)
	}
	return nil
}

// RoleFilter represents filters for querying roles
type RoleFilter struct {
	TenantID      uint       `json:"tenant_id,omitempty"`
	Status        RoleStatus `json:"status,omitempty"`
	Scope         RoleScope  `json:"scope,omitempty"`
	ContextID     *uint      `json:"context_id,omitempty"`
	IsSystemRole  *bool      `json:"is_system_role,omitempty"`
	IsDefaultRole *bool      `json:"is_default_role,omitempty"`
	Level         *uint      `json:"level,omitempty"`
	Search        string     `json:"search,omitempty"`
	Page          int        `json:"page,omitempty"`
	PageSize      int        `json:"page_size,omitempty"`
	SortBy        string     `json:"sort_by,omitempty"`
	SortOrder     string     `json:"sort_order,omitempty"`
}

// RoleCreateRequest represents the request to create a role
type RoleCreateRequest struct {
	TenantID      uint             `json:"tenant_id" validate:"required,min=1"`
	Name          string           `json:"name" validate:"required,max=100,matches=^[a-z0-9_-]+$"`
	DisplayName   string           `json:"display_name" validate:"required,max=255"`
	Description   *string          `json:"description,omitempty"`
	IsSystemRole  bool             `json:"is_system_role,omitempty"`
	IsDefaultRole bool             `json:"is_default_role,omitempty"`
	Level         uint             `json:"level,omitempty" validate:"min=0,max=100"`
	Scope         RoleScope        `json:"scope,omitempty" validate:"omitempty,oneof=tenant website global"`
	ContextID     *uint            `json:"context_id,omitempty"`
	Color         *string          `json:"color,omitempty" validate:"omitempty,matches=^#[0-9A-Fa-f]{6}$"`
	Icon          *string          `json:"icon,omitempty" validate:"omitempty,max=100"`
	Capabilities  RoleCapabilities `json:"capabilities,omitempty"`
	Restrictions  RoleRestrictions `json:"restrictions,omitempty"`
}

// RoleUpdateRequest represents the request to update a role
type RoleUpdateRequest struct {
	Name          *string           `json:"name,omitempty" validate:"omitempty,max=100,matches=^[a-z0-9_-]+$"`
	DisplayName   *string           `json:"display_name,omitempty" validate:"omitempty,max=255"`
	Description   *string           `json:"description,omitempty"`
	IsSystemRole  *bool             `json:"is_system_role,omitempty"`
	IsDefaultRole *bool             `json:"is_default_role,omitempty"`
	Level         *uint             `json:"level,omitempty" validate:"omitempty,min=0,max=100"`
	Scope         *RoleScope        `json:"scope,omitempty" validate:"omitempty,oneof=tenant website global"`
	ContextID     *uint             `json:"context_id,omitempty"`
	Color         *string           `json:"color,omitempty" validate:"omitempty,matches=^#[0-9A-Fa-f]{6}$"`
	Icon          *string           `json:"icon,omitempty" validate:"omitempty,max=100"`
	Status        *RoleStatus       `json:"status,omitempty" validate:"omitempty,oneof=active inactive deprecated"`
	Capabilities  *RoleCapabilities `json:"capabilities,omitempty"`
	Restrictions  *RoleRestrictions `json:"restrictions,omitempty"`
}

// RolePermissionAssignRequest represents the request to assign permissions to a role
type RolePermissionAssignRequest struct {
	PermissionIDs []uint `json:"permission_ids" validate:"required,min=1,dive,min=1"`
}

// RolePermissionRevokeRequest represents the request to revoke permissions from a role
type RolePermissionRevokeRequest struct {
	PermissionIDs []uint `json:"permission_ids" validate:"required,min=1,dive,min=1"`
}