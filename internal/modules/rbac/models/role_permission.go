package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// RolePermissionStatus represents the status of a role permission assignment
// @Enum active,inactive,revoked
type RolePermissionStatus string

const (
	RolePermissionStatusActive   RolePermissionStatus = "active"
	RolePermissionStatusInactive RolePermissionStatus = "inactive"
	RolePermissionStatusRevoked  RolePermissionStatus = "revoked"
)

// Scan implements sql.Scanner interface
func (s *RolePermissionStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = RolePermissionStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s RolePermissionStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// RolePermissionContextType represents the context type for role permission
// @Enum website,tenant,global
type RolePermissionContextType string

const (
	RolePermissionContextTypeWebsite RolePermissionContextType = "website"
	RolePermissionContextTypeTenant  RolePermissionContextType = "tenant"
	RolePermissionContextTypeGlobal  RolePermissionContextType = "global"
)

// <PERSON>an implements sql.Scanner interface
func (s *RolePermissionContextType) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = RolePermissionContextType(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s RolePermissionContextType) Value() (driver.Value, error) {
	return string(s), nil
}

// RolePermissionConditions represents a JSON object of role permission conditions
type RolePermissionConditions map[string]interface{}

// Scan implements sql.Scanner interface
func (rpc *RolePermissionConditions) Scan(value interface{}) error {
	if value == nil {
		*rpc = make(RolePermissionConditions)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, rpc)
}

// Value implements driver.Valuer interface
func (rpc RolePermissionConditions) Value() (driver.Value, error) {
	if rpc == nil {
		return "{}", nil
	}
	return json.Marshal(rpc)
}

// RolePermissionLimitations represents a JSON object of role permission limitations
type RolePermissionLimitations map[string]interface{}

// Scan implements sql.Scanner interface
func (rpl *RolePermissionLimitations) Scan(value interface{}) error {
	if value == nil {
		*rpl = make(RolePermissionLimitations)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, rpl)
}

// Value implements driver.Valuer interface
func (rpl RolePermissionLimitations) Value() (driver.Value, error) {
	if rpl == nil {
		return "{}", nil
	}
	return json.Marshal(rpl)
}

// RolePermission represents a role-permission assignment in the RBAC system
type RolePermission struct {
	ID           uint `json:"id" gorm:"primaryKey"`
	RoleID       uint `json:"role_id" gorm:"not null;index"`
	PermissionID uint `json:"permission_id" gorm:"not null;index"`

	// Permission Grant Information
	GrantedBy *uint      `json:"granted_by,omitempty" gorm:"index"`
	GrantedAt time.Time  `json:"granted_at" gorm:"autoCreateTime"`

	// Permission Context and Scope
	ContextType *RolePermissionContextType `json:"context_type,omitempty" gorm:"type:enum('website','tenant','global');index"`
	ContextID   *uint                      `json:"context_id,omitempty" gorm:"index"`

	// Permission Constraints
	Conditions  RolePermissionConditions  `json:"conditions" gorm:"type:json;default:'{}'"`
	Limitations RolePermissionLimitations `json:"limitations" gorm:"type:json;default:'{}'"`

	// Permission Metadata
	IsInherited bool `json:"is_inherited" gorm:"default:false"`
	IsTemporary bool `json:"is_temporary" gorm:"default:false"`

	// Temporal Constraints
	ValidFrom  *time.Time `json:"valid_from,omitempty" gorm:"index"`
	ValidUntil *time.Time `json:"valid_until,omitempty" gorm:"index"`

	// Status and Timestamps
	Status    RolePermissionStatus `json:"status" gorm:"type:enum('active','inactive','revoked');default:'active';not null"`
	CreatedAt time.Time            `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time            `json:"updated_at" gorm:"autoUpdateTime"`
	RevokedAt *time.Time           `json:"revoked_at,omitempty"`
	RevokedBy *uint                `json:"revoked_by,omitempty" gorm:"index"`

	// Associations (loaded separately to avoid circular references)
	// Role       *Role       `json:"role,omitempty" gorm:"foreignKey:RoleID"`
	// Permission *Permission `json:"permission,omitempty" gorm:"foreignKey:PermissionID"`
	// GrantedByUser *User    `json:"granted_by_user,omitempty" gorm:"foreignKey:GrantedBy"`
	// RevokedByUser *User    `json:"revoked_by_user,omitempty" gorm:"foreignKey:RevokedBy"`
}

// TableName specifies the table name for RolePermission
func (RolePermission) TableName() string {
	return "rbac_role_permissions"
}

// IsActive checks if role permission is active
func (rp *RolePermission) IsActive() bool {
	return rp.Status == RolePermissionStatusActive
}

// IsRevoked checks if role permission is revoked
func (rp *RolePermission) IsRevoked() bool {
	return rp.Status == RolePermissionStatusRevoked
}

// IsInheritedCheck checks if role permission is inherited
func (rp *RolePermission) IsInheritedCheck() bool {
	return rp.IsInherited
}

// IsTemporaryCheck checks if role permission is temporary
func (rp *RolePermission) IsTemporaryCheck() bool {
	return rp.IsTemporary
}

// IsExpired checks if role permission has expired
func (rp *RolePermission) IsExpired() bool {
	if rp.ValidUntil == nil {
		return false
	}
	return time.Now().After(*rp.ValidUntil)
}

// IsValid checks if role permission is valid (not expired and in valid time range)
func (rp *RolePermission) IsValid() bool {
	now := time.Now()
	
	// Check if valid from constraint is met
	if rp.ValidFrom != nil && now.Before(*rp.ValidFrom) {
		return false
	}
	
	// Check if valid until constraint is met
	if rp.ValidUntil != nil && now.After(*rp.ValidUntil) {
		return false
	}
	
	return true
}

// HasCondition checks if role permission has a specific condition
func (rp *RolePermission) HasCondition(condition string) bool {
	_, exists := rp.Conditions[condition]
	return exists
}

// GetConditionValue gets the value of a specific condition
func (rp *RolePermission) GetConditionValue(condition string) interface{} {
	return rp.Conditions[condition]
}

// HasLimitation checks if role permission has a specific limitation
func (rp *RolePermission) HasLimitation(limitation string) bool {
	_, exists := rp.Limitations[limitation]
	return exists
}

// GetLimitationValue gets the value of a specific limitation
func (rp *RolePermission) GetLimitationValue(limitation string) interface{} {
	return rp.Limitations[limitation]
}

// BeforeCreate hook for RolePermission
func (rp *RolePermission) BeforeCreate() error {
	if rp.Conditions == nil {
		rp.Conditions = make(RolePermissionConditions)
	}
	if rp.Limitations == nil {
		rp.Limitations = make(RolePermissionLimitations)
	}
	return nil
}

// RolePermissionFilter represents filters for querying role permissions
type RolePermissionFilter struct {
	RoleID       *uint                     `json:"role_id,omitempty"`
	PermissionID *uint                     `json:"permission_id,omitempty"`
	Status       RolePermissionStatus      `json:"status,omitempty"`
	ContextType  *RolePermissionContextType `json:"context_type,omitempty"`
	ContextID    *uint                     `json:"context_id,omitempty"`
	IsInherited  *bool                     `json:"is_inherited,omitempty"`
	IsTemporary  *bool                     `json:"is_temporary,omitempty"`
	GrantedBy    *uint                     `json:"granted_by,omitempty"`
	ValidFrom    *time.Time                `json:"valid_from,omitempty"`
	ValidUntil   *time.Time                `json:"valid_until,omitempty"`
	Page         int                       `json:"page,omitempty"`
	PageSize     int                       `json:"page_size,omitempty"`
	SortBy       string                    `json:"sort_by,omitempty"`
	SortOrder    string                    `json:"sort_order,omitempty"`
}

// RolePermissionCreateRequest represents the request to create a role permission
type RolePermissionCreateRequest struct {
	RoleID       uint                          `json:"role_id" validate:"required,min=1"`
	PermissionID uint                          `json:"permission_id" validate:"required,min=1"`
	ContextType  *RolePermissionContextType    `json:"context_type,omitempty" validate:"omitempty,oneof=website tenant global"`
	ContextID    *uint                         `json:"context_id,omitempty"`
	IsInherited  bool                          `json:"is_inherited,omitempty"`
	IsTemporary  bool                          `json:"is_temporary,omitempty"`
	ValidFrom    *time.Time                    `json:"valid_from,omitempty"`
	ValidUntil   *time.Time                    `json:"valid_until,omitempty"`
	Conditions   RolePermissionConditions      `json:"conditions,omitempty"`
	Limitations  RolePermissionLimitations     `json:"limitations,omitempty"`
}

// RolePermissionUpdateRequest represents the request to update a role permission
type RolePermissionUpdateRequest struct {
	ContextType  *RolePermissionContextType    `json:"context_type,omitempty" validate:"omitempty,oneof=website tenant global"`
	ContextID    *uint                         `json:"context_id,omitempty"`
	IsInherited  *bool                         `json:"is_inherited,omitempty"`
	IsTemporary  *bool                         `json:"is_temporary,omitempty"`
	ValidFrom    *time.Time                    `json:"valid_from,omitempty"`
	ValidUntil   *time.Time                    `json:"valid_until,omitempty"`
	Status       *RolePermissionStatus         `json:"status,omitempty" validate:"omitempty,oneof=active inactive revoked"`
	Conditions   *RolePermissionConditions     `json:"conditions,omitempty"`
	Limitations  *RolePermissionLimitations    `json:"limitations,omitempty"`
}

// RolePermissionBulkCreateRequest represents the request to bulk create role permissions
type RolePermissionBulkCreateRequest struct {
	RolePermissions []RolePermissionCreateRequest `json:"role_permissions" validate:"required,min=1,dive"`
}

// RolePermissionBulkRevokeRequest represents the request to bulk revoke role permissions
type RolePermissionBulkRevokeRequest struct {
	RolePermissionIDs []uint `json:"role_permission_ids" validate:"required,min=1,dive,min=1"`
}