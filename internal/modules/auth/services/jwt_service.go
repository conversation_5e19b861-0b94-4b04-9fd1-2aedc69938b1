package services

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	userrepos "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
)

var (
	ErrInvalidToken      = errors.New("invalid token")
	ErrTokenExpired      = errors.New("token expired")
	ErrTokenNotValidYet  = errors.New("token not valid yet")
	ErrInvalidClaims     = errors.New("invalid token claims")
	ErrTokenBlacklisted  = errors.New("token blacklisted")
	ErrInvalidTokenType  = errors.New("invalid token type")
	ErrMissingTenantID   = errors.New("missing tenant_id in token")
	ErrMissingUserID     = errors.New("missing user_id in token")
	ErrInvalidSigningKey = errors.New("invalid signing key")
)

// JWTService handles JWT token operations
type JWTService interface {
	GenerateAccessToken(claims *models.JWTClaims) (string, error)
	GenerateRefreshToken(claims *models.JWTClaims) (string, error)
	GenerateTokenPair(claims *models.JWTClaims) (accessToken, refreshToken string, err error)
	ValidateToken(ctx context.Context, tokenString string) (*models.JWTClaims, error)
	ValidateAccessToken(tokenString string) (*models.JWTClaims, error)
	ValidateRefreshToken(tokenString string) (*models.JWTClaims, error)
	RefreshAccessToken(refreshToken string) (string, error)
	ExtractClaims(tokenString string) (*models.JWTClaims, error)
	GenerateTokenID() string
}

type jwtService struct {
	cfg                        *config.Config
	signingKey                 []byte
	accessTokenTTL             time.Duration
	refreshTokenTTL            time.Duration
	blacklistService           TokenBlacklistService
	tenantMembershipRepository userrepos.TenantMembershipRepository
}

// NewJWTService creates a new JWT service
func NewJWTService(cfg *config.Config, blacklistService TokenBlacklistService, tenantMembershipRepo userrepos.TenantMembershipRepository) (JWTService, error) {
	if cfg.Auth.JWTSecret == "" {
		return nil, ErrInvalidSigningKey
	}

	return &jwtService{
		cfg:                        cfg,
		signingKey:                 []byte(cfg.Auth.JWTSecret),
		accessTokenTTL:             cfg.Auth.AccessTokenTTL,
		refreshTokenTTL:            cfg.Auth.RefreshTokenTTL,
		blacklistService:           blacklistService,
		tenantMembershipRepository: tenantMembershipRepo,
	}, nil
}

// GenerateTokenID generates a unique token ID for JWT JTI claim
func (s *jwtService) GenerateTokenID() string {
	b := make([]byte, 16)
	if _, err := rand.Read(b); err != nil {
		// Fallback to timestamp-based ID
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return base64.URLEncoding.EncodeToString(b)
}


// GenerateAccessToken generates a new access token
func (s *jwtService) GenerateAccessToken(claims *models.JWTClaims) (string, error) {
	// Validate required claims
	if claims.UserID == 0 {
		return "", ErrMissingUserID
	}

	// Set token metadata
	now := time.Now()
	claims.IssuedAt = now.Unix()
	claims.ExpiresAt = now.Add(s.accessTokenTTL).Unix()
	claims.NotBefore = now.Unix()

	if claims.Issuer == "" {
		claims.Issuer = s.cfg.Auth.JWTIssuer
	}
	if claims.ID == "" {
		claims.ID = s.GenerateTokenID()
	}
	if claims.Subject == "" {
		claims.Subject = fmt.Sprintf("user:%d", claims.UserID)
	}
	if claims.TokenType == "" {
		claims.TokenType = models.TokenTypeAPI
	}

	// Create token with user-only claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id":    claims.UserID,
		"email":      claims.Email,
		"scopes":     claims.Scopes,
		"token_type": claims.TokenType,
		"session_id": claims.SessionID,
		"iss":        claims.Issuer,
		"sub":        claims.Subject,
		"aud":        claims.Audience,
		"exp":        claims.ExpiresAt,
		"nbf":        claims.NotBefore,
		"iat":        claims.IssuedAt,
		"jti":        claims.ID,
	})

	// Sign token
	tokenString, err := token.SignedString(s.signingKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// GenerateRefreshToken generates a new refresh token
func (s *jwtService) GenerateRefreshToken(claims *models.JWTClaims) (string, error) {
	// Validate required claims
	if claims.UserID == 0 {
		return "", ErrMissingUserID
	}

	// Set token metadata
	now := time.Now()
	claims.IssuedAt = now.Unix()
	claims.ExpiresAt = now.Add(s.refreshTokenTTL).Unix()
	claims.NotBefore = now.Unix()

	if claims.Issuer == "" {
		claims.Issuer = s.cfg.Auth.JWTIssuer
	}
	if claims.ID == "" {
		claims.ID = s.GenerateTokenID()
	}
	if claims.Subject == "" {
		claims.Subject = fmt.Sprintf("user:%d", claims.UserID)
	}
	claims.TokenType = "refresh" // Always set refresh token type

	// Create token with minimal claims for refresh token
	mapClaims := jwt.MapClaims{
		"user_id":    claims.UserID,
		"token_type": "refresh",
		"session_id": claims.SessionID,
		"iss":        claims.Issuer,
		"sub":        claims.Subject,
		"exp":        claims.ExpiresAt,
		"nbf":        claims.NotBefore,
		"iat":        claims.IssuedAt,
		"jti":        claims.ID,
	}
	
	// Include email if available
	if claims.Email != "" {
		mapClaims["email"] = claims.Email
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, mapClaims)

	// Sign token
	tokenString, err := token.SignedString(s.signingKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return tokenString, nil
}

// GenerateTokenPair generates both access and refresh tokens
func (s *jwtService) GenerateTokenPair(claims *models.JWTClaims) (accessToken, refreshToken string, err error) {
	// Generate access token
	accessToken, err = s.GenerateAccessToken(claims)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate access token: %w", err)
	}

	// Generate refresh token with new JTI
	refreshClaims := *claims
	refreshClaims.ID = s.GenerateTokenID()
	refreshToken, err = s.GenerateRefreshToken(&refreshClaims)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return accessToken, refreshToken, nil
}

// ValidateToken validates any token and returns claims
func (s *jwtService) ValidateToken(ctx context.Context, tokenString string) (*models.JWTClaims, error) {
	// Parse token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.signingKey, nil
	})

	if err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, ErrTokenExpired
		}
		if errors.Is(err, jwt.ErrTokenNotValidYet) {
			return nil, ErrTokenNotValidYet
		}
		return nil, fmt.Errorf("%w: %v", ErrInvalidToken, err)
	}

	// Check if token is valid
	if !token.Valid {
		return nil, ErrInvalidToken
	}

	// Extract claims
	mapClaims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, ErrInvalidClaims
	}

	// Convert to JWTClaims
	claims := &models.JWTClaims{}

	// Required fields
	if userID, ok := mapClaims["user_id"].(float64); ok {
		claims.UserID = uint(userID)
	} else {
		return nil, ErrMissingUserID
	}

	// Optional fields extracted from token

	if email, ok := mapClaims["email"].(string); ok {
		claims.Email = email
	}

	if tokenType, ok := mapClaims["token_type"].(string); ok {
		claims.TokenType = models.TokenType(tokenType)
	}

	if sessionID, ok := mapClaims["session_id"].(float64); ok {
		sessionIDUint := uint(sessionID)
		claims.SessionID = &sessionIDUint
	}

	// JWT standard claims
	if iss, ok := mapClaims["iss"].(string); ok {
		claims.Issuer = iss
	}
	if sub, ok := mapClaims["sub"].(string); ok {
		claims.Subject = sub
	}
	if aud, ok := mapClaims["aud"].(string); ok {
		claims.Audience = aud
	}
	if exp, ok := mapClaims["exp"].(float64); ok {
		claims.ExpiresAt = int64(exp)
	}
	if nbf, ok := mapClaims["nbf"].(float64); ok {
		claims.NotBefore = int64(nbf)
	}
	if iat, ok := mapClaims["iat"].(float64); ok {
		claims.IssuedAt = int64(iat)
	}
	if jti, ok := mapClaims["jti"].(string); ok {
		claims.ID = jti
	}

	// Handle scopes
	if scopesInterface, ok := mapClaims["scopes"]; ok {
		switch v := scopesInterface.(type) {
		case []interface{}:
			for _, scope := range v {
				if s, ok := scope.(string); ok {
					claims.Scopes = append(claims.Scopes, s)
				}
			}
		case []string:
			claims.Scopes = v
		}
	}

	// Handle tenant memberships
	if membershipsInterface, ok := mapClaims["tenant_memberships"]; ok {
		if membershipsArray, ok := membershipsInterface.([]interface{}); ok {
			for _, membershipInterface := range membershipsArray {
				if membershipMap, ok := membershipInterface.(map[string]interface{}); ok {
					membership := models.TenantMembership{}

					if tenantID, ok := membershipMap["tenant_id"].(float64); ok {
						membership.TenantID = uint(tenantID)
					}
					if localUsername, ok := membershipMap["local_username"].(string); ok {
						membership.LocalUsername = localUsername
					}
					if displayName, ok := membershipMap["display_name"].(string); ok {
						membership.DisplayName = displayName
					}
					if status, ok := membershipMap["status"].(string); ok {
						membership.Status = status
					}
					if role, ok := membershipMap["role"].(string); ok {
						membership.Role = role
					}
					if joinedAt, ok := membershipMap["joined_at"].(float64); ok {
						membership.JoinedAt = int64(joinedAt)
					}

					//claims.TenantMemberships = append(claims.TenantMemberships, membership)
				}
			}
		}
	}

	// Check if token is blacklisted
	if claims.ID != "" && s.blacklistService != nil {
		blacklisted, err := s.blacklistService.IsBlacklisted(ctx, claims.ID)
		if err == nil && blacklisted {
			return nil, ErrTokenBlacklisted
		}
	}

	return claims, nil
}

// ValidateAccessToken validates access token specifically
func (s *jwtService) ValidateAccessToken(tokenString string) (*models.JWTClaims, error) {
	claims, err := s.ValidateToken(context.Background(), tokenString)
	if err != nil {
		return nil, err
	}

	// Verify it's not a refresh token
	if claims.TokenType == "refresh" {
		return nil, ErrInvalidTokenType
	}

	return claims, nil
}

// ValidateRefreshToken validates refresh token specifically
func (s *jwtService) ValidateRefreshToken(tokenString string) (*models.JWTClaims, error) {
	claims, err := s.ValidateToken(context.Background(), tokenString)
	if err != nil {
		return nil, err
	}

	// Verify it's a refresh token
	if claims.TokenType != "refresh" {
		return nil, ErrInvalidTokenType
	}

	return claims, nil
}

// RefreshAccessToken creates a new access token from a refresh token
func (s *jwtService) RefreshAccessToken(refreshToken string) (string, error) {
	// Validate refresh token
	refreshClaims, err := s.ValidateRefreshToken(refreshToken)
	if err != nil {
		return "", fmt.Errorf("invalid refresh token: %w", err)
	}

	// Create new access token claims with user info only
	accessClaims := &models.JWTClaims{
		UserID:    refreshClaims.UserID,
		Email:     refreshClaims.Email, // TODO: Should fetch from DB to ensure up-to-date
		SessionID: refreshClaims.SessionID,
		// Note: Email and Scopes should be fetched from DB to ensure they're up-to-date
	}

	// Generate new access token
	return s.GenerateAccessToken(accessClaims)
}

// ExtractClaims extracts claims without full validation (useful for debugging)
func (s *jwtService) ExtractClaims(tokenString string) (*models.JWTClaims, error) {
	// Parse without validation
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// Extract claims
	mapClaims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, ErrInvalidClaims
	}

	// Convert to JWTClaims (same logic as ValidateToken)
	claims := &models.JWTClaims{}

	if userID, ok := mapClaims["user_id"].(float64); ok {
		claims.UserID = uint(userID)
	}
	// Extract user information from token
	if email, ok := mapClaims["email"].(string); ok {
		claims.Email = email
	}
	if jti, ok := mapClaims["jti"].(string); ok {
		claims.ID = jti
	}
	if exp, ok := mapClaims["exp"].(float64); ok {
		claims.ExpiresAt = int64(exp)
	}

	// Handle tenant memberships
	if membershipsInterface, ok := mapClaims["tenant_memberships"]; ok {
		if membershipsArray, ok := membershipsInterface.([]interface{}); ok {
			for _, membershipInterface := range membershipsArray {
				if membershipMap, ok := membershipInterface.(map[string]interface{}); ok {
					membership := models.TenantMembership{}

					if tenantID, ok := membershipMap["tenant_id"].(float64); ok {
						membership.TenantID = uint(tenantID)
					}
					if localUsername, ok := membershipMap["local_username"].(string); ok {
						membership.LocalUsername = localUsername
					}
					if displayName, ok := membershipMap["display_name"].(string); ok {
						membership.DisplayName = displayName
					}
					if status, ok := membershipMap["status"].(string); ok {
						membership.Status = status
					}
					if role, ok := membershipMap["role"].(string); ok {
						membership.Role = role
					}
					if joinedAt, ok := membershipMap["joined_at"].(float64); ok {
						membership.JoinedAt = int64(joinedAt)
					}

					//claims.TenantMemberships = append(claims.TenantMemberships, membership)
				}
			}
		}
	}

	return claims, nil
}
