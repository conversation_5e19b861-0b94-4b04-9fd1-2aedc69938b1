package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/config"
	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	authRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	notificationModels "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	notificationRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding
	tenantServices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	userRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	websiteModels "github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	websiteServices "github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// EmailVerificationConfig holds configuration for email verification
type EmailVerificationConfig struct {
	TokenExpiryHours    int    `json:"token_expiry_hours"`    // Default: 24
	MaxResendAttempts   uint   `json:"max_resend_attempts"`   // Default: 3
	ResendIntervalMins  int    `json:"resend_interval_mins"`  // Default: 5
	BaseVerificationURL string `json:"base_verification_url"` // Frontend URL for verification
}

// DefaultEmailVerificationConfig returns default configuration
func DefaultEmailVerificationConfig() *EmailVerificationConfig {
	return &EmailVerificationConfig{
		TokenExpiryHours:    24,
		MaxResendAttempts:   3,
		ResendIntervalMins:  5,
		BaseVerificationURL: "http://localhost:9078/auth/verify-email",
	}
}

// EmailVerificationService interface defines methods for email verification operations
type EmailVerificationService interface {
	// Token management
	CreateVerificationToken(ctx context.Context, tenantID uint, req *authModels.CreateEmailVerificationTokenRequest) (*authModels.EmailVerificationToken, error)
	ResendVerificationEmail(ctx context.Context, tenantID uint, req *authModels.ResendVerificationEmailRequest) (*authModels.EmailVerificationToken, error)
	VerifyEmail(ctx context.Context, req *authModels.VerifyEmailRequest) (*authModels.EmailVerificationResult, error)

	// Token operations
	GetTokenByID(ctx context.Context, id uint) (*authModels.EmailVerificationToken, error)
	GetActiveTokenForUser(ctx context.Context, userID uint) (*authModels.EmailVerificationToken, error)
	GetActiveTokenForEmail(ctx context.Context, email string) (*authModels.EmailVerificationToken, error)
	InvalidateUserTokens(ctx context.Context, userID uint) error

	// Analytics and monitoring
	GetTokenStats(ctx context.Context, userID uint) (*authModels.TokenStats, error)
	GetRecentTokensForUser(ctx context.Context, userID uint, limit int) ([]*authModels.EmailVerificationToken, error)
	CleanupExpiredTokens(ctx context.Context) (int64, error)

	// Configuration
	UpdateConfig(config *EmailVerificationConfig)
	GetConfig() *EmailVerificationConfig
}

// emailVerificationService implements EmailVerificationService
type emailVerificationService struct {
	tokenRepo           authRepositories.EmailVerificationTokenRepository
	userRepo            userRepositories.UserRepository
	notificationService notificationServices.NotificationService
	templateRepo        notificationRepositories.TemplateRepository
	websiteService      websiteServices.WebsiteService
	tenantService       tenantServices.TenantService
	membershipRepo      userRepositories.TenantMembershipRepository
	config              *EmailVerificationConfig
	authConfig          *config.AuthConfig
	logger              utils.Logger
}

// NewEmailVerificationService creates a new email verification service
func NewEmailVerificationService(
	tokenRepo authRepositories.EmailVerificationTokenRepository,
	userRepo userRepositories.UserRepository,
	notificationService notificationServices.NotificationService,
	templateRepo notificationRepositories.TemplateRepository,
	websiteService websiteServices.WebsiteService,
	tenantService tenantServices.TenantService,
	membershipRepo userRepositories.TenantMembershipRepository,
	authConfig *config.AuthConfig,
	logger utils.Logger,
) EmailVerificationService {
	return &emailVerificationService{
		tokenRepo:           tokenRepo,
		userRepo:            userRepo,
		notificationService: notificationService,
		templateRepo:        templateRepo,
		websiteService:      websiteService,
		tenantService:       tenantService,
		membershipRepo:      membershipRepo,
		config:              DefaultEmailVerificationConfig(),
		authConfig:          authConfig,
		logger:              logger,
	}
}

// CreateVerificationToken creates a new verification token and sends email
func (s *emailVerificationService) CreateVerificationToken(ctx context.Context, tenantID uint, req *authModels.CreateEmailVerificationTokenRequest) (*authModels.EmailVerificationToken, error) {
	// Get user to verify they exist
	user, err := s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": req.UserID,
			"email":   req.Email,
		}).Error("Failed to get user for email verification")
		return nil, fmt.Errorf("user not found")
	}

	// Verify email matches user's email
	if !strings.EqualFold(user.Email, req.Email) {
		s.logger.WithFields(map[string]interface{}{
			"user_id":    req.UserID,
			"user_email": user.Email,
			"req_email":  req.Email,
		}).Warn("Email mismatch in verification request")
		return nil, fmt.Errorf("email does not match user account")
	}

	// Check if user already has verified email
	if user.EmailVerified {
		s.logger.WithFields(map[string]interface{}{
			"user_id": req.UserID,
			"email":   req.Email,
		}).Info("Attempted to create verification token for already verified email")
		return nil, fmt.Errorf("email is already verified")
	}

	// Invalidate any existing tokens for this user
	if err := s.tokenRepo.InvalidateTokensForUser(ctx, req.UserID); err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Warn("Failed to invalidate existing tokens")
	}

	// Create new token
	token := &authModels.EmailVerificationToken{
		UserID:    req.UserID,
		Email:     req.Email,
		UserAgent: req.UserAgent,
		IPAddress: req.IPAddress,
	}

	// Set custom expiry if provided
	if req.ExpiresIn != nil {
		token.ExpiresAt = time.Now().Add(time.Duration(*req.ExpiresIn) * time.Second)
	} else {
		token.ExpiresAt = time.Now().Add(time.Duration(s.config.TokenExpiryHours) * time.Hour)
	}

	// Create token in database
	if err := s.tokenRepo.CreateToken(ctx, token); err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": req.UserID,
			"email":   req.Email,
		}).Error("Failed to create verification token")
		return nil, fmt.Errorf("failed to create verification token")
	}

	// Send verification email
	if err := s.sendVerificationEmail(ctx, tenantID, token, user, false); err != nil {
		s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to send verification email")
		// Don't return error - token was created successfully
	}

	s.logger.WithFields(map[string]interface{}{
		"token_id": token.ID,
		"user_id":  req.UserID,
		"email":    req.Email,
	}).Info("Created email verification token")

	return token, nil
}

// ResendVerificationEmail resends verification email with rate limiting
func (s *emailVerificationService) ResendVerificationEmail(ctx context.Context, tenantID uint, req *authModels.ResendVerificationEmailRequest) (*authModels.EmailVerificationToken, error) {
	// Get active token for email
	token, err := s.tokenRepo.GetTokenByEmail(ctx, req.Email)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("no active verification token found for this email")
		}
		s.logger.WithError(err).WithField("email", req.Email).Error("Failed to get token for email")
		return nil, fmt.Errorf("failed to retrieve verification token")
	}

	// Check rate limiting
	resendInterval := time.Duration(s.config.ResendIntervalMins) * time.Minute
	if !token.CanResend(s.config.MaxResendAttempts, resendInterval) {
		if token.ResendCount >= s.config.MaxResendAttempts {
			s.logger.WithFields(map[string]interface{}{
				"token_id":     token.ID,
				"email":        req.Email,
				"resend_count": token.ResendCount,
				"max_resends":  s.config.MaxResendAttempts,
			}).Warn("Maximum resend attempts exceeded")
			return nil, fmt.Errorf("maximum resend attempts exceeded")
		}

		if token.LastResentAt != nil {
			nextAllowedTime := token.LastResentAt.Add(resendInterval)
			waitTime := time.Until(nextAllowedTime)
			if waitTime > 0 {
				s.logger.WithFields(map[string]interface{}{
					"token_id":  token.ID,
					"email":     req.Email,
					"wait_time": waitTime.String(),
				}).Warn("Resend rate limit exceeded")
				return nil, fmt.Errorf("please wait %v before requesting another verification email", waitTime.Round(time.Second))
			}
		}
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, token.UserID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", token.UserID).Error("Failed to get user for resend")
		return nil, fmt.Errorf("user not found")
	}

	// Update resend count and timestamp
	token.IncrementResendCount()
	if err := s.tokenRepo.UpdateToken(ctx, token); err != nil {
		s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to update token resend count")
		return nil, fmt.Errorf("failed to update token")
	}

	// Send verification email (resend version)
	if err := s.sendVerificationEmail(ctx, tenantID, token, user, true); err != nil {
		s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to send resend verification email")
		return nil, fmt.Errorf("failed to send verification email")
	}

	s.logger.WithFields(map[string]interface{}{
		"token_id":     token.ID,
		"email":        req.Email,
		"resend_count": token.ResendCount,
	}).Info("Resent email verification")

	return token, nil
}

// VerifyEmail verifies an email using the provided token
func (s *emailVerificationService) VerifyEmail(ctx context.Context, req *authModels.VerifyEmailRequest) (*authModels.EmailVerificationResult, error) {
	// Verify and get token
	token, err := s.tokenRepo.VerifyToken(ctx, req.Token)
	if err != nil {
		s.logger.WithError(err).WithField("token", req.Token[:10]+"...").Warn("Invalid verification token")
		return &authModels.EmailVerificationResult{
			Success:   false,
			Message:   "Invalid or expired verification token",
			TokenUsed: false,
		}, nil
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, token.UserID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", token.UserID).Error("Failed to get user for verification")
		return &authModels.EmailVerificationResult{
			Success:   false,
			Message:   "User not found",
			TokenUsed: false,
		}, nil
	}

	// Check if email is already verified
	if user.EmailVerified {
		s.logger.WithFields(map[string]interface{}{
			"user_id": token.UserID,
			"email":   token.Email,
		}).Info("Attempted to verify already verified email")

		// Mark token as used anyway
		if err := s.tokenRepo.MarkTokenAsUsed(ctx, token.ID); err != nil {
			s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to mark token as used")
		}

		return &authModels.EmailVerificationResult{
			Success:   true,
			Message:   "Email is already verified",
			UserID:    user.ID,
			Email:     user.Email,
			TokenUsed: true,
		}, nil
	}

	// Mark token as used
	if err := s.tokenRepo.MarkTokenAsUsed(ctx, token.ID); err != nil {
		s.logger.WithError(err).WithField("token_id", token.ID).Error("Failed to mark token as used")
		return &authModels.EmailVerificationResult{
			Success:   false,
			Message:   "Failed to process verification",
			TokenUsed: false,
		}, nil
	}

	// Update user's email verification status
	now := time.Now()
	user.EmailVerified = true
	user.EmailVerifiedAt = &now

	// If user was pending verification, set them to active
	if user.Status == userModels.UserStatusPendingVerification {
		user.Status = userModels.UserStatusActive
	}

	if err := s.userRepo.Update(ctx, user); err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to update user verification status")
		return &authModels.EmailVerificationResult{
			Success:   false,
			Message:   "Failed to update verification status",
			TokenUsed: true,
		}, nil
	}

	// Check if SKIP_ONBOARDING is enabled and auto-create tenant and website
	if s.authConfig.SkipOnboarding {
		if err := s.createTenantAndWebsiteForUser(ctx, user); err != nil {
			s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to create tenant and website for user during skip onboarding")
			// Don't fail email verification, just log the error
		}
	} else {
		// Create onboarding record for the user
		if err := s.createOnboardingForUser(ctx, user.ID); err != nil {
			s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to create onboarding record for user")
			// Don't fail email verification, just log the error
		}
	}

	// Invalidate all other tokens for this user
	if err := s.tokenRepo.InvalidateTokensForUser(ctx, token.UserID); err != nil {
		s.logger.WithError(err).WithField("user_id", token.UserID).Warn("Failed to invalidate other tokens")
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":  user.ID,
		"email":    user.Email,
		"token_id": token.ID,
	}).Info("Successfully verified email")

	return &authModels.EmailVerificationResult{
		Success:   true,
		Message:   "Email verified successfully",
		UserID:    user.ID,
		Email:     user.Email,
		TokenUsed: true,
	}, nil
}

// sendVerificationEmail sends the verification email using notification service
func (s *emailVerificationService) sendVerificationEmail(ctx context.Context, tenantID uint, token *authModels.EmailVerificationToken, user *userModels.User, isResend bool) error {
	// Get domain from tenant ID
	baseURL, err := s.getDomainFromTenantID(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"tenant_id": tenantID,
		}).Warn("Failed to get domain from tenant ID, using default")
		baseURL = s.config.BaseVerificationURL
	}

	// Generate verification URL
	verificationURL := fmt.Sprintf("%s?token=%s", baseURL, token.Token)

	// Calculate expiry time in human-readable format
	expiryTime := "24 hours"
	if timeRemaining := time.Until(token.ExpiresAt); timeRemaining > 0 {
		if timeRemaining > time.Hour {
			expiryTime = fmt.Sprintf("%.1f hours", timeRemaining.Hours())
		} else {
			expiryTime = fmt.Sprintf("%.0f minutes", timeRemaining.Minutes())
		}
	}

	// Choose template based on whether this is a resend
	templateCode := "email_verification"
	if isResend {
		templateCode = "email_verification_resend"
	}

	// Create notification request
	notificationReq := notificationModels.CreateNotificationRequest{
		Type:    "email_verification",
		Channel: notificationModels.ChannelEmail,
		Subject: "Verify Your Email Address",
		Recipients: []notificationModels.CreateRecipientRequest{
			{
				RecipientType:    notificationModels.RecipientTypeUser,
				RecipientAddress: user.Email,
				UserID:           &user.ID,
			},
		},
		TemplateID: s.getTemplateIDByCode(tenantID, templateCode),
		TemplateData: map[string]interface{}{
			"user": map[string]interface{}{
				"name":  user.GetDisplayName(),
				"email": user.Email,
			},
			"verification_url": verificationURL,
			"expiry_time":      expiryTime,
			"brand": map[string]interface{}{
				"name":          "BlogAPI",
				"support_email": "<EMAIL>",
				"logo_url":      "https://blogapi.com/logo.png",
			},
			"current_year": time.Now().Year(),
		},
		Priority: notificationModels.PriorityHigh,
	}

	// Add resend-specific data
	if isResend {
		notificationReq.TemplateData["resend_count"] = token.ResendCount
		notificationReq.TemplateData["max_resends"] = s.config.MaxResendAttempts
	}

	// Create notification
	notification, err := s.notificationService.CreateNotification(tenantID, notificationReq)
	if err != nil {
		return fmt.Errorf("failed to create verification email: %w", err)
	}

	// Actually send the email immediately
	if err := s.notificationService.SendNotification(tenantID, notification.ID); err != nil {
		return fmt.Errorf("failed to send verification email: %w", err)
	}

	return nil
}

// GetTokenByID retrieves a token by ID
func (s *emailVerificationService) GetTokenByID(ctx context.Context, id uint) (*authModels.EmailVerificationToken, error) {
	return s.tokenRepo.GetTokenByID(ctx, id)
}

// GetActiveTokenForUser retrieves active token for user
func (s *emailVerificationService) GetActiveTokenForUser(ctx context.Context, userID uint) (*authModels.EmailVerificationToken, error) {
	return s.tokenRepo.GetTokenByUserID(ctx, userID)
}

// GetActiveTokenForEmail retrieves active token for email
func (s *emailVerificationService) GetActiveTokenForEmail(ctx context.Context, email string) (*authModels.EmailVerificationToken, error) {
	return s.tokenRepo.GetTokenByEmail(ctx, email)
}

// InvalidateUserTokens invalidates all tokens for a user
func (s *emailVerificationService) InvalidateUserTokens(ctx context.Context, userID uint) error {
	return s.tokenRepo.InvalidateTokensForUser(ctx, userID)
}

// GetTokenStats returns token statistics for a user
func (s *emailVerificationService) GetTokenStats(ctx context.Context, userID uint) (*authModels.TokenStats, error) {
	return s.tokenRepo.GetTokenStats(ctx, userID)
}

// GetRecentTokensForUser returns recent tokens for a user
func (s *emailVerificationService) GetRecentTokensForUser(ctx context.Context, userID uint, limit int) ([]*authModels.EmailVerificationToken, error) {
	return s.tokenRepo.GetRecentTokensForUser(ctx, userID, limit)
}

// CleanupExpiredTokens removes expired tokens
func (s *emailVerificationService) CleanupExpiredTokens(ctx context.Context) (int64, error) {
	return s.tokenRepo.CleanupExpiredTokens(ctx)
}

// UpdateConfig updates the service configuration
func (s *emailVerificationService) UpdateConfig(config *EmailVerificationConfig) {
	s.config = config
	s.logger.WithFields(map[string]interface{}{
		"token_expiry_hours":   config.TokenExpiryHours,
		"max_resend_attempts":  config.MaxResendAttempts,
		"resend_interval_mins": config.ResendIntervalMins,
	}).Info("Updated email verification configuration")
}

// GetConfig returns the current configuration
func (s *emailVerificationService) GetConfig() *EmailVerificationConfig {
	return s.config
}

// getTemplateIDByCode looks up template ID by code from database
func (s *emailVerificationService) getTemplateIDByCode(tenantID uint, templateCode string) *uint {
	// Query the template repository to get template by code
	template, err := s.templateRepo.GetByCode(tenantID, templateCode)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"tenant_id":     tenantID,
			"template_code": templateCode,
		}).Error("Failed to get template by code")
		return nil
	}

	if template == nil {
		s.logger.WithFields(map[string]interface{}{
			"tenant_id":     tenantID,
			"template_code": templateCode,
		}).Warn("Template not found")
		return nil
	}

	return &template.ID
}

// getDomainFromTenantID gets the domain/URL from tenant ID
func (s *emailVerificationService) getDomainFromTenantID(ctx context.Context, tenantID uint) (string, error) {
	// Get the active website for the tenant
	filter := websiteModels.WebsiteFilter{
		Status: "active",
	}
	websiteListResponse, err := s.websiteService.ListWebsites(ctx, tenantID, filter)
	if err != nil {
		return "", fmt.Errorf("failed to get websites for tenant %d: %w", tenantID, err)
	}

	if len(websiteListResponse.Websites) == 0 {
		return "", fmt.Errorf("no websites found for tenant %d", tenantID)
	}

	// Get the first active website
	var activeWebsite *websiteModels.WebsiteResponse
	for i, website := range websiteListResponse.Websites {
		if website.Status == "active" {
			activeWebsite = &websiteListResponse.Websites[i]
			break
		}
	}

	if activeWebsite == nil {
		return "", fmt.Errorf("no active website found for tenant %d", tenantID)
	}

	// Build the verification URL
	var baseURL string
	if activeWebsite.Domain != "" {
		// Use custom domain
		baseURL = fmt.Sprintf("https://%s/verify-email", activeWebsite.Domain)
	} else if activeWebsite.Subdomain != "" {
		// Use subdomain with base domain
		baseURL = fmt.Sprintf("https://%s.example.com/verify-email", activeWebsite.Subdomain)
	} else {
		return "", fmt.Errorf("no domain or subdomain configured for tenant %d", tenantID)
	}

	// Check if we're in development mode
	if utils.GetEnv("APP_ENV", "production") == "development" {
		// In development, use localhost with appropriate port
		port := utils.GetEnv("FRONTEND_PORT", "9200")
		baseURL = fmt.Sprintf("http://localhost:%s/verify-email", port)
	}

	return baseURL, nil
}

// createDefaultTenantForUser creates a default tenant and membership for a newly verified user
func (s *emailVerificationService) createDefaultTenantForUser(ctx context.Context, user *userModels.User) error {
	// Generate tenant name from user info
	tenantName := "My Organization"
	if user.FirstName != nil && *user.FirstName != "" {
		tenantName = *user.FirstName + "'s Organization"
	}

	// Generate unique domain slug from email with timestamp
	emailParts := strings.Split(user.Email, "@")
	domainSlug := emailParts[0]
	if len(emailParts) > 1 {
		domainSlug = emailParts[0] + "-" + strings.Split(emailParts[1], ".")[0]
	}

	// Add timestamp to ensure uniqueness
	timestamp := time.Now().Unix()
	domainSlug = fmt.Sprintf("%s-%d", domainSlug, timestamp)

	// Create tenant with default plan (assuming plan ID 1 is default)
	tenantInput := tenantServices.CreateTenantInput{
		Name:        tenantName,
		Domain:      domainSlug,
		PlanID:      1, // Default plan ID
		AdminEmail:  user.Email,
		AdminName:   user.Email,
		CompanyInfo: make(map[string]interface{}),
	}

	// Set admin name if available
	if user.FirstName != nil || user.LastName != nil {
		adminName := ""
		if user.FirstName != nil && *user.FirstName != "" {
			adminName = *user.FirstName
		}
		if user.LastName != nil && *user.LastName != "" {
			if adminName != "" {
				adminName += " " + *user.LastName
			} else {
				adminName = *user.LastName
			}
		}
		if adminName != "" {
			tenantInput.AdminName = adminName
		}
	}

	// Create tenant
	tenant, err := s.tenantService.Create(ctx, tenantInput)
	if err != nil {
		return fmt.Errorf("failed to create tenant: %w", err)
	}

	// Create primary owner membership
	now := time.Now()
	membership := &userModels.TenantMembership{
		UserID:    user.ID,
		TenantID:  tenant.ID,
		IsPrimary: true,
		Status:    userModels.TenantMembershipStatusActive,
		JoinedAt:  now,
	}

	if err := s.membershipRepo.Create(ctx, membership); err != nil {
		// If membership creation fails, we should probably delete the tenant
		// but for now just log the error
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   user.ID,
			"tenant_id": tenant.ID,
		}).Error("Failed to create tenant membership after tenant creation")
		return fmt.Errorf("failed to create tenant membership: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":     user.ID,
		"tenant_id":   tenant.ID,
		"tenant_name": tenant.Name,
		"domain_slug": tenant.Domain,
	}).Info("Created default tenant and membership for verified user")

	return nil
}

// createTenantAndWebsiteForUser creates a tenant and website for user when SKIP_ONBOARDING is enabled
func (s *emailVerificationService) createTenantAndWebsiteForUser(ctx context.Context, user *userModels.User) error {
	s.logger.WithField("user_id", user.ID).Info("Creating tenant and website for user (skip onboarding)")

	// Check if user already has tenant memberships
	memberships, err := s.membershipRepo.GetByUserID(ctx, user.ID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Warn("Failed to check user tenant memberships")
	} else if len(memberships) > 0 {
		s.logger.WithField("user_id", user.ID).Info("User already has tenant memberships, skipping auto-creation")
		return nil
	}

	// Generate unique tenant name and slug from user's email
	emailParts := strings.Split(user.Email, "@")
	baseName := emailParts[0]

	// Generate admin name from user data or fallback to email
	adminName := user.Email
	if user.FirstName != nil && user.LastName != nil && *user.FirstName != "" && *user.LastName != "" {
		adminName = fmt.Sprintf("%s %s", *user.FirstName, *user.LastName)
	} else if user.FirstName != nil && *user.FirstName != "" {
		adminName = *user.FirstName
	}

	// Create tenant input using the correct struct from tenant services
	tenantInput := tenantServices.CreateTenantInput{
		Name:       fmt.Sprintf("%s Organization", baseName),
		Domain:     fmt.Sprintf("%s-org", baseName),
		PlanID:     1, // Default plan ID
		AdminEmail: user.Email,
		AdminName:  adminName,
		CompanyInfo: map[string]interface{}{
			"description":  "Auto-created organization",
			"auto_created": true,
		},
	}

	// Create tenant
	tenant, err := s.tenantService.Create(ctx, tenantInput)
	if err != nil {
		return fmt.Errorf("failed to create tenant: %w", err)
	}

	// Create primary owner membership
	now := time.Now()
	membership := &userModels.TenantMembership{
		UserID:    user.ID,
		TenantID:  tenant.ID,
		IsPrimary: true,
		Status:    userModels.TenantMembershipStatusActive,
		JoinedAt:  now,
	}

	if err := s.membershipRepo.Create(ctx, membership); err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   user.ID,
			"tenant_id": tenant.ID,
		}).Error("Failed to create tenant membership")
		return fmt.Errorf("failed to create tenant membership: %w", err)
	}

	// Create default website using correct struct fields
	websiteInput := &websiteModels.WebsiteCreateRequest{
		Name:        fmt.Sprintf("%s Website", baseName),
		Subdomain:   baseName,
		Description: "Auto-created website",
		ActiveTheme: "default",
		Timezone:    "UTC",
	}

	website, err := s.websiteService.CreateWebsite(ctx, tenant.ID, websiteInput)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   user.ID,
			"tenant_id": tenant.ID,
		}).Error("Failed to create website")
		return fmt.Errorf("failed to create website: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":      user.ID,
		"tenant_id":    tenant.ID,
		"tenant_name":  tenant.Name,
		"website_id":   website.ID,
		"website_name": website.Name,
		"subdomain":    website.Subdomain,
	}).Info("Successfully created tenant and website for user (skip onboarding)")

	return nil
}

// createOnboardingForUser creates an onboarding record for a user after email verification
func (s *emailVerificationService) createOnboardingForUser(ctx context.Context, userID uint) error {
	// Get onboarding integration
	onboardingIntegration := onboarding.GetOnboardingIntegration(s.db, s.logger)

	// Create onboarding record for the user
	if err := onboardingIntegration.HandleEmailVerified(ctx, userID); err != nil {
		return fmt.Errorf("failed to create onboarding record: %w", err)
	}

	return nil
}
