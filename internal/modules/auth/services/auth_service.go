package services

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	notificationModels "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	onboardingServices "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	tenantServices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	userRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

var (
	ErrInvalidCredentials       = errors.New("invalid email or password")
	ErrAccountLocked            = errors.New("account is locked due to too many failed attempts")
	ErrAccountInactive          = errors.New("account is inactive")
	ErrAccountSuspended         = errors.New("account is suspended")
	ErrEmailNotVerified         = errors.New("email not verified")
	ErrUserNotFound             = errors.New("user not found")
	ErrEmailAlreadyExists       = errors.New("email already exists")
	ErrUsernameAlreadyExists    = errors.New("username already exists")
	ErrWeakPassword             = errors.New("password does not meet requirements")
	ErrInvalidRefreshToken      = errors.New("invalid refresh token")
	ErrSessionExpired           = errors.New("session has expired")
	ErrUnauthorized             = errors.New("unauthorized")
	ErrInvalidInvitation        = errors.New("invalid or expired invitation")
	ErrTenantCreationFailed     = errors.New("failed to create tenant")
	ErrMembershipCreationFailed = errors.New("failed to create tenant membership")
)

// AuthService handles authentication business logic
type AuthService interface {
	// Authentication
	Login(ctx context.Context, req *LoginRequest) (*authModels.AuthResponse, error)
	Logout(ctx context.Context, userID uint, sessionID uint) (*authModels.LogoutResponse, error)
	LogoutAllDevices(ctx context.Context, userID uint) (*authModels.LogoutResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*authModels.RefreshTokenResponse, error)

	// Registration
	Register(ctx context.Context, req *RegisterRequest) (*authModels.AuthResponse, error)
	VerifyEmail(ctx context.Context, token string) (*authModels.EmailVerificationResponse, error)
	ResendVerificationEmail(ctx context.Context, email string) (*authModels.EmailVerificationResponse, error)

	// Session management
	GetActiveSessions(ctx context.Context, userID uint) (*authModels.SessionResponse, error)
	RevokeSession(ctx context.Context, userID uint, sessionID uint) (*authModels.SessionResponse, error)

	// Two-factor authentication
	EnableTwoFactor(ctx context.Context, userID uint) (*authModels.TwoFactorSetupResponse, error)
	DisableTwoFactor(ctx context.Context, userID uint, code string) (*authModels.TwoFactorVerifyResponse, error)
	VerifyTwoFactor(ctx context.Context, userID uint, code string) (*authModels.TwoFactorVerifyResponse, error)

	// Password management
	ChangePassword(ctx context.Context, userID uint, req *authModels.ChangePasswordRequest) (*authModels.PasswordChangeResponse, error)
	RequestPasswordReset(ctx context.Context, email string) (*authModels.PasswordResetResponse, error)
	ResetPassword(ctx context.Context, token string, newPassword string) (*authModels.PasswordResetResponse, error)

	// User profile and security
	GetProfile(ctx context.Context, userID uint) (*authModels.UserProfileResponse, error)
	UpdateProfile(ctx context.Context, userID uint, req *userModels.UserUpdateRequest) (*authModels.UserProfileResponse, error)
	GetSecurityInfo(ctx context.Context, userID uint) (*authModels.SecurityResponse, error)

	// Validation and utilities
	ValidateToken(ctx context.Context, token string) (*authModels.ValidationResponse, error)
	ValidateSession(ctx context.Context, sessionID uint) (*authModels.ValidationResponse, error)
	GetAuthStats(ctx context.Context, userID uint) (*authModels.StatsResponse, error)
}

type authService struct {
	userRepo         repositories.UserRepository
	sessionRepo      repositories.SessionRepository
	loginAttemptRepo repositories.LoginAttemptRepository
	jwtService       JWTService
	passwordService  PasswordService
	rateLimiter      RateLimitingService
	emailService     EmailService
	logger           utils.Logger
	config           *AuthConfig
	// Tenant services
	tenantService tenantServices.TenantService
	// User invitation and membership repositories
	invitationRepo userRepositories.UserInvitationRepository
	membershipRepo userRepositories.TenantMembershipRepository
	// Onboarding service
	onboardingService onboardingServices.OnboardingIntegrationService
	// Notification service
	notificationService notificationServices.NotificationService
	// Email verification service
	emailVerificationService EmailVerificationService
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	RequireEmailVerification bool
	AllowRegistration        bool
	MaxLoginAttempts         int
	LockoutDuration          time.Duration
	SessionTimeout           time.Duration
	RefreshTokenTTL          time.Duration
	TwoFactorIssuer          string
}

// NewAuthService creates a new auth service
func NewAuthService(
	userRepo repositories.UserRepository,
	sessionRepo repositories.SessionRepository,
	loginAttemptRepo repositories.LoginAttemptRepository,
	jwtService JWTService,
	passwordService PasswordService,
	rateLimiter RateLimitingService,
	emailService EmailService,
	logger utils.Logger,
	config *AuthConfig,
	tenantService tenantServices.TenantService,
	invitationRepo userRepositories.UserInvitationRepository,
	membershipRepo userRepositories.TenantMembershipRepository,
	onboardingService onboardingServices.OnboardingIntegrationService,
	notificationService notificationServices.NotificationService,
	emailVerificationService EmailVerificationService,
) AuthService {
	return &authService{
		userRepo:                 userRepo,
		sessionRepo:              sessionRepo,
		loginAttemptRepo:         loginAttemptRepo,
		jwtService:               jwtService,
		passwordService:          passwordService,
		rateLimiter:              rateLimiter,
		emailService:             emailService,
		logger:                   logger,
		config:                   config,
		tenantService:            tenantService,
		invitationRepo:           invitationRepo,
		membershipRepo:           membershipRepo,
		onboardingService:        onboardingService,
		notificationService:      notificationService,
		emailVerificationService: emailVerificationService,
	}
}

// Login handles user login
func (s *authService) Login(ctx context.Context, req *LoginRequest) (*authModels.AuthResponse, error) {
	// Check rate limiting
	locked, _, err := s.rateLimiter.IsAccountLocked(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to check rate limit: %w", err)
	}
	if locked {
		return nil, ErrAccountLocked
	}

	// Find user by email or username
	user, err := s.userRepo.GetByEmailOrUsername(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		// Record failed attempt
		_ = s.rateLimiter.RecordFailedAttempt(ctx, req.Email, req.IPAddress)
		return nil, ErrInvalidCredentials
	}

	// Verify password
	if err := s.passwordService.VerifyPassword(user.PasswordHash, req.Password); err != nil {
		// Record failed attempt
		_ = s.rateLimiter.RecordFailedAttempt(ctx, req.Email, req.IPAddress)
		return nil, ErrInvalidCredentials
	}

	// Check account status
	switch user.Status {
	case userModels.UserStatusInactive:
		return nil, ErrAccountInactive
	case userModels.UserStatusSuspended:
		return nil, ErrAccountSuspended
	case userModels.UserStatusDeleted:
		return nil, ErrUserNotFound
	case userModels.UserStatusPendingVerification:
		// User needs to verify email before they can login
		return nil, ErrEmailNotVerified
	}

	// Check email verification if required (additional check for safety)
	if s.config.RequireEmailVerification && !user.EmailVerified {
		return nil, ErrEmailNotVerified
	}

	// Check two-factor authentication
	if user.TwoFactorEnabled && req.TwoFactorCode == "" {
		return &authModels.AuthResponse{
			RequiresTwoFactor: true,
		}, nil
	}

	if user.TwoFactorEnabled {
		_, err := s.VerifyTwoFactor(ctx, user.ID, req.TwoFactorCode)
		if err != nil {
			return nil, fmt.Errorf("invalid two-factor code: %w", err)
		}
	}

	// Get user's active tenant membership for JWT claims
	memberships, err := s.membershipRepo.GetByUserID(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user memberships: %w", err)
	}

	// Find active membership (use first active one for now)
	var activeMembership *userModels.TenantMembership
	requiresOnboarding := false

	for _, membership := range memberships {
		if membership.IsActive() {
			activeMembership = &membership
			break
		}
	}

	// Check if user needs onboarding (no active memberships)
	if activeMembership == nil {
		requiresOnboarding = true
		// For users without memberships, we'll create a session without tenant context
		// This allows them to proceed to onboarding
	}

	// Create session with or without tenant context
	ipAddr := req.IPAddress // Convert to pointer
	session := &authModels.Session{
		UserID:     user.ID,
		IPAddress:  &ipAddr,
		DeviceType: detectDeviceType(req.UserAgent),
		DeviceName: &req.DeviceName,
		ExpiresAt:  time.Now().Add(s.config.SessionTimeout),
	}

	// Only set TenantID if user has a membership
	if activeMembership != nil {
		session.TenantID = activeMembership.TenantID
	}

	if err := s.sessionRepo.Create(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	// Generate JWT tokens with or without tenant context
	claims := &authModels.JWTClaims{
		UserID:    user.ID,
		Email:     user.Email,
		SessionID: &session.ID,
	}

	// Only set CurrentTenantID if user has a membership
	// if activeMembership != nil {
	// 	claims.CurrentTenantID = activeMembership.TenantID
	// }

	accessToken, refreshToken, err := s.jwtService.GenerateTokenPair(claims)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Update session with refresh token
	session.Token = refreshToken
	if err := s.sessionRepo.Update(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to update session: %w", err)
	}

	// Update last login
	if err := s.userRepo.UpdateLastLogin(ctx, user.ID, req.IPAddress); err != nil {
		s.logger.WithError(err).Error("Failed to update last login")
	}

	// Record successful login
	_ = s.rateLimiter.RecordSuccessfulLogin(ctx, user.ID, req.IPAddress)

	// Get tenant and membership info for response
	var tenant *tenantModels.Tenant
	if activeMembership != nil {
		tenant, err = s.tenantService.GetByID(ctx, activeMembership.TenantID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to get tenant info for login response")
		}
	}

	return &authModels.AuthResponse{
		User:               user,
		AccessToken:        accessToken,
		RefreshToken:       refreshToken,
		ExpiresIn:          int(s.config.SessionTimeout.Seconds()),
		TokenType:          "Bearer",
		Tenant:             tenant,
		Membership:         activeMembership,
		SessionID:          session.ID,
		RequiresOnboarding: requiresOnboarding,
	}, nil
}

// Logout handles user logout
func (s *authService) Logout(ctx context.Context, userID uint, sessionID uint) (*authModels.LogoutResponse, error) {
	// Invalidate session
	if err := s.sessionRepo.InvalidateSession(ctx, sessionID); err != nil {
		return nil, fmt.Errorf("failed to invalidate session: %w", err)
	}

	// Get session to blacklist tokens
	session, err := s.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	sessionsTerminated := 1
	if session != nil && session.UserID == userID {
		// Blacklist the refresh token
		if session.Token != "" {
			if claims, err := s.jwtService.ExtractClaims(session.Token); err == nil && claims.ID != "" {
				// Type assertion to access blacklist service
				if jwtSvc, ok := s.jwtService.(*jwtService); ok {
					_ = jwtSvc.blacklistService.BlacklistToken(
						ctx,
						claims.ID,
						time.Unix(claims.ExpiresAt, 0),
						userID,
						authModels.TokenBlacklistReasonLogout,
					)
				}
			}
		}
	}

	return &authModels.LogoutResponse{
		SessionsTerminated: sessionsTerminated,
	}, nil
}

// LogoutAllDevices logs out user from all devices
func (s *authService) LogoutAllDevices(ctx context.Context, userID uint) (*authModels.LogoutResponse, error) {
	// Get all active sessions
	sessions, err := s.sessionRepo.GetActiveSessionsByUser(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get sessions: %w", err)
	}

	sessionCount := len(sessions)

	// Blacklist all session tokens
	for _, session := range sessions {
		if session.Token != "" {
			if claims, err := s.jwtService.ExtractClaims(session.Token); err == nil && claims.ID != "" {
				// Type assertion to access blacklist service
				if jwtSvc, ok := s.jwtService.(*jwtService); ok {
					_ = jwtSvc.blacklistService.BlacklistToken(
						ctx,
						claims.ID,
						time.Unix(claims.ExpiresAt, 0),
						userID,
						authModels.TokenBlacklistReasonLogout,
					)
				}
			}
		}
	}

	// Invalidate all sessions
	if err := s.sessionRepo.InvalidateAllUserSessions(ctx, userID); err != nil {
		return nil, fmt.Errorf("failed to invalidate sessions: %w", err)
	}

	return &authModels.LogoutResponse{
		SessionsTerminated: sessionCount,
	}, nil
}

// RefreshToken refreshes access token using refresh token
func (s *authService) RefreshToken(ctx context.Context, refreshToken string) (*authModels.RefreshTokenResponse, error) {
	// Validate refresh token
	claims, err := s.jwtService.ValidateRefreshToken(refreshToken)
	if err != nil {
		return nil, ErrInvalidRefreshToken
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil || user.Status != userModels.UserStatusActive {
		return nil, ErrUserNotFound
	}

	// Get session
	if claims.SessionID != nil {
		session, err := s.sessionRepo.GetByID(ctx, *claims.SessionID)
		if err != nil {
			return nil, fmt.Errorf("failed to get session: %w", err)
		}
		if session == nil || !session.IsActive() || session.IsExpired() {
			return nil, ErrSessionExpired
		}

		// Extend session
		newExpiry := time.Now().Add(s.config.SessionTimeout)
		if err := s.sessionRepo.ExtendExpiration(ctx, session.ID, newExpiry); err != nil {
			s.logger.WithError(err).Error("Failed to extend session")
		}
	}

	// Get user's active tenant membership for JWT claims
	memberships, err := s.membershipRepo.GetByUserID(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user memberships: %w", err)
	}

	// Find active membership (use first active one for now)
	var activeMembership *userModels.TenantMembership
	for _, membership := range memberships {
		if membership.IsActive() {
			activeMembership = &membership
			break
		}
	}

	// User must have at least one active membership
	if activeMembership == nil {
		return nil, errors.New("user has no active tenant membership")
	}

	// Generate new access token with tenant context
	newClaims := &authModels.JWTClaims{
		UserID: user.ID,
		//CurrentTenantID: activeMembership.TenantID,
		Email:     user.Email,
		SessionID: claims.SessionID,
	}

	accessToken, err := s.jwtService.GenerateAccessToken(newClaims)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// Get tenant info for response
	var tenant *tenantModels.Tenant
	if activeMembership != nil {
		tenant, err = s.tenantService.GetByID(ctx, activeMembership.TenantID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to get tenant info for refresh response")
		}
	}
	_ = tenant // Use tenant variable to avoid unused variable error

	return &authModels.RefreshTokenResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int(s.config.SessionTimeout.Seconds()),
		TokenType:    "Bearer",
	}, nil
}

// Register handles user registration - creates ONLY user account (no tenant)
func (s *authService) Register(ctx context.Context, req *RegisterRequest) (*authModels.AuthResponse, error) {
	// Check if registration is allowed
	if !s.config.AllowRegistration {
		return nil, errors.New("registration is disabled")
	}

	// Validate password strength
	if err := s.passwordService.ValidatePassword(req.Password); err != nil {
		return nil, err
	}

	// Check if email exists
	exists, err := s.userRepo.Exists(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to check email existence: %w", err)
	}
	if exists {
		return nil, ErrEmailAlreadyExists
	}

	// Check if username exists
	if req.Username != "" {
		exists, err := s.userRepo.ExistsByUsername(ctx, req.Username)
		if err != nil {
			return nil, fmt.Errorf("failed to check username existence: %w", err)
		}
		if exists {
			return nil, ErrUsernameAlreadyExists
		}
	}

	// Hash password
	hashedPassword, err := s.passwordService.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Handle invitation flow if token provided
	if req.InvitationToken != "" {
		return s.processInvitationRegistration(ctx, req)
	}

	// Create user WITHOUT tenant assignment (global user)
	var username, firstName, lastName *string
	if req.Username != "" {
		username = &req.Username
	}
	if req.FirstName != "" {
		firstName = &req.FirstName
	}
	if req.LastName != "" {
		lastName = &req.LastName
	}

	user := &userModels.User{
		Email:        req.Email,
		Username:     username,
		PasswordHash: hashedPassword,
		FirstName:    firstName,
		LastName:     lastName,
		// NO tenant_id - user is global
	}

	// Set email verification status and user status
	if s.config.RequireEmailVerification {
		user.EmailVerified = false
		user.Status = userModels.UserStatusPendingVerification
	} else {
		user.EmailVerified = true
		user.Status = userModels.UserStatusActive
		now := time.Now()
		user.EmailVerifiedAt = &now
	}

	// Create user in database
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Initialize response - NO tenant or membership created during registration
	response := &authModels.AuthResponse{
		User: user,
		// NO tenant or membership data
	}

	// Handle email verification requirement
	if s.config.RequireEmailVerification {
		// Send email verification using default tenant ID for registration emails
		err := s.sendEmailVerification(ctx, user, false) // false = first time, not resend
		if err != nil {
			s.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id": user.ID,
				"email":   user.Email,
			}).Error("Failed to send email verification")
			// Don't fail registration if email sending fails
		}

		// Return response indicating email verification is required
		response.RequiresEmailVerification = true

		s.logger.WithFields(map[string]interface{}{
			"user_id": user.ID,
			"email":   user.Email,
		}).Info("User registered successfully - email verification required")

		return response, nil
	}

	// If email verification is not required, generate tokens WITHOUT tenant context
	// Create session without tenant (user hasn't created organization yet)
	ipAddr := req.IPAddress
	session := &authModels.Session{
		TenantID:   0, // No tenant for global user
		UserID:     user.ID,
		IPAddress:  &ipAddr,
		DeviceType: detectDeviceType(req.UserAgent),
		DeviceName: nil, // No device name in registration
		ExpiresAt:  time.Now().Add(s.config.SessionTimeout),
	}

	if err := s.sessionRepo.Create(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	// Generate JWT tokens WITHOUT tenant context (user will get tenant during onboarding)
	claims := &authModels.JWTClaims{
		UserID: user.ID,
		//CurrentTenantID: 0, // No tenant yet
		Email:     user.Email,
		SessionID: &session.ID,
	}

	accessToken, refreshToken, err := s.jwtService.GenerateTokenPair(claims)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Update session with refresh token
	session.Token = refreshToken
	if err := s.sessionRepo.Update(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to update session: %w", err)
	}

	// Update last login
	if err := s.userRepo.UpdateLastLogin(ctx, user.ID, req.IPAddress); err != nil {
		s.logger.WithError(err).Error("Failed to update last login")
	}

	// Record successful registration
	_ = s.rateLimiter.RecordSuccessfulLogin(ctx, user.ID, req.IPAddress)

	// Add token information to response
	response.AccessToken = accessToken
	response.RefreshToken = refreshToken
	response.ExpiresIn = int(s.config.SessionTimeout.Seconds())
	response.TokenType = "Bearer"
	response.SessionID = session.ID

	s.logger.WithFields(map[string]interface{}{
		"user_id": user.ID,
		"email":   user.Email,
	}).Info("User registered successfully - ready for onboarding")

	return response, nil
}

// VerifyEmail verifies user email using token
func (s *authService) VerifyEmail(ctx context.Context, token string) (*authModels.EmailVerificationResponse, error) {
	verifyReq := &authModels.VerifyEmailRequest{
		Token: token,
	}

	result, err := s.emailVerificationService.VerifyEmail(ctx, verifyReq)
	if err != nil {
		s.logger.WithError(err).WithField("token", token[:10]+"...").Error("Failed to verify email")
		return nil, fmt.Errorf("email verification failed: %w", err)
	}

	if !result.Success {
		s.logger.WithField("message", result.Message).Warn("Email verification unsuccessful")
		return nil, fmt.Errorf("verification failed: %s", result.Message)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id": result.UserID,
		"email":   result.Email,
	}).Info("Email verified successfully")

	return &authModels.EmailVerificationResponse{
		EmailVerified: true,
		UserID:        result.UserID,
		Email:         result.Email,
		TokenUsed:     result.TokenUsed,
	}, nil
}

// ResendVerificationEmail resends verification email with rate limiting
func (s *authService) ResendVerificationEmail(ctx context.Context, email string) (*authModels.EmailVerificationResponse, error) {
	// Validate user exists and is not already verified
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	if user.EmailVerified {
		return nil, errors.New("email already verified")
	}

	// Get user's tenant to pass to verification service
	// First try to get tenant from user's membership (for users who completed onboarding)
	var tenantID uint
	memberships, err := s.membershipRepo.GetByUserID(ctx, user.ID)
	if err == nil && len(memberships) > 0 {
		// User has completed onboarding and has tenant memberships
		tenantID = memberships[0].TenantID
	} else {
		// User registered but hasn't completed onboarding yet - use default tenant
		// This matches the pattern used in sendEmailVerification
		tenantID = uint(utils.GetEnvAsInt("DEFAULT_TENANT_ID", 1))
		s.logger.WithFields(map[string]interface{}{
			"user_id":   user.ID,
			"email":     user.Email,
			"tenant_id": tenantID,
		}).Info("Using default tenant for email verification resend (user not onboarded)")
	}

	// Use email verification service to resend with rate limiting
	resendReq := &authModels.ResendVerificationEmailRequest{
		Email: email,
	}

	resp, err := s.emailVerificationService.ResendVerificationEmail(ctx, tenantID, resendReq)
	if err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"email":     email,
			"user_id":   user.ID,
			"tenant_id": tenantID,
		}).Error("Failed to resend verification email")
		return nil, fmt.Errorf("failed to resend verification email: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"email":     email,
		"user_id":   user.ID,
		"tenant_id": tenantID,
	}).Info("Verification email resent successfully")

	// Calculate next resend time (5 minutes from now)
	nextResendAt := time.Now().Add(5 * time.Minute)

	return &authModels.EmailVerificationResponse{
		TokenSent:    true,
		Email:        email,
		UserID:       user.ID,
		ResendCount:  resp.ResendCount,
		MaxResends:   3, // Default max resends
		NextResendAt: &nextResendAt,
		ExpiresAt:    &resp.ExpiresAt,
	}, nil
}

// GetActiveSessions gets user's active sessions
func (s *authService) GetActiveSessions(ctx context.Context, userID uint) (*authModels.SessionResponse, error) {
	sessions, err := s.sessionRepo.GetActiveSessionsByUser(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active sessions: %w", err)
	}

	return &authModels.SessionResponse{
		Sessions: sessions,
	}, nil
}

// RevokeSession revokes a specific session
func (s *authService) RevokeSession(ctx context.Context, userID uint, sessionID uint) (*authModels.SessionResponse, error) {
	// Verify session belongs to user
	session, err := s.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}
	if session == nil || session.UserID != userID {
		return nil, ErrUnauthorized
	}

	logoutResp, err := s.Logout(ctx, userID, sessionID)
	if err != nil {
		return nil, err
	}

	return &authModels.SessionResponse{
		Session:      session,
		Revoked:      true,
		RevokedCount: logoutResp.SessionsTerminated,
	}, nil
}

// EnableTwoFactor enables two-factor authentication
func (s *authService) EnableTwoFactor(ctx context.Context, userID uint) (*authModels.TwoFactorSetupResponse, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	if user.TwoFactorEnabled {
		return nil, errors.New("two-factor already enabled")
	}

	// Generate secret
	secret, qrCode, err := utils.GenerateTOTPSecret(user.Email, s.config.TwoFactorIssuer)
	if err != nil {
		return nil, fmt.Errorf("failed to generate TOTP secret: %w", err)
	}

	// Generate backup codes
	backupCodes := make([]string, 10)
	for i := 0; i < 10; i++ {
		backupCodes[i] = utils.GenerateRandomCode(8)
	}

	// Save secret (not enabled yet)
	if err := s.userRepo.UpdateTwoFactorBackupCodes(ctx, userID, backupCodes); err != nil {
		return nil, fmt.Errorf("failed to save backup codes: %w", err)
	}

	return &authModels.TwoFactorSetupResponse{
		Secret:      secret,
		QRCode:      qrCode,
		BackupCodes: backupCodes,
	}, nil
}

// DisableTwoFactor disables two-factor authentication
func (s *authService) DisableTwoFactor(ctx context.Context, userID uint, code string) (*authModels.TwoFactorVerifyResponse, error) {
	// Verify code first
	_, err := s.VerifyTwoFactor(ctx, userID, code)
	if err != nil {
		return nil, err
	}

	err = s.userRepo.DisableTwoFactor(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to disable two-factor: %w", err)
	}

	return &authModels.TwoFactorVerifyResponse{
		Verified: true,
	}, nil
}

// VerifyTwoFactor verifies two-factor code
func (s *authService) VerifyTwoFactor(ctx context.Context, userID uint, code string) (*authModels.TwoFactorVerifyResponse, error) {
	secret, err := s.userRepo.GetTwoFactorSecret(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get two-factor secret: %w", err)
	}

	if !utils.VerifyTOTPCode(secret, code) {
		return nil, errors.New("invalid two-factor code")
	}

	return &authModels.TwoFactorVerifyResponse{
		Verified: true,
	}, nil
}

// handleNewTenantRegistration handles registration when user needs a new tenant
// func (s *authService) handleNewTenantRegistration(ctx context.Context, user *userModels.User, req *RegisterRequest) (*tenantModels.Tenant, *userModels.TenantMembership, error) {
// 	// Generate tenant name from user info
// 	tenantName := "My Organization"
// 	if req.FirstName != "" {
// 		tenantName = req.FirstName + "'s Organization"
// 	}

// 	// Generate domain slug from email
// 	emailParts := strings.Split(user.Email, "@")
// 	domainSlug := emailParts[0]
// 	if len(emailParts) > 1 {
// 		domainSlug = emailParts[0] + "-" + strings.Split(emailParts[1], ".")[0]
// 	}

// 	// Create tenant with default plan (assuming plan ID 1 is default)
// 	tenantInput := tenantServices.CreateTenantInput{
// 		Name:        tenantName,
// 		Domain:      domainSlug,
// 		PlanID:      1, // Default plan ID
// 		AdminEmail:  user.Email,
// 		AdminName:   user.Email,
// 		CompanyInfo: make(map[string]interface{}),
// 	}

// 	// Set admin name if available
// 	if req.FirstName != "" || req.LastName != "" {
// 		adminName := ""
// 		if req.FirstName != "" {
// 			adminName = req.FirstName
// 		}
// 		if req.LastName != "" {
// 			if adminName != "" {
// 				adminName += " " + req.LastName
// 			} else {
// 				adminName = req.LastName
// 			}
// 		}
// 		tenantInput.AdminName = adminName
// 	}

// 	// Create tenant
// 	tenant, err := s.tenantService.Create(ctx, tenantInput)
// 	if err != nil {
// 		return nil, nil, fmt.Errorf("failed to create tenant: %w", err)
// 	}

// 	// Create owner membership
// 	now := time.Now()
// 	membership := &userModels.TenantMembership{
// 		UserID:   user.ID,
// 		TenantID: tenant.ID,
// 		Status:   userModels.TenantMembershipStatusActive,
// 		JoinedAt: now,
// 	}

// 	if err := s.membershipRepo.Create(ctx, membership); err != nil {
// 		return nil, nil, fmt.Errorf("failed to create membership: %w", err)
// 	}

// 	return tenant, membership, nil
// }

// detectDeviceType detects device type from user agent
func detectDeviceType(userAgent string) authModels.DeviceType {
	// Simple detection - can be enhanced
	userAgentLower := strings.ToLower(userAgent)
	if strings.Contains(userAgentLower, "mobile") {
		return authModels.DeviceTypeMobile
	}
	if strings.Contains(userAgentLower, "tablet") {
		return authModels.DeviceTypeTablet
	}
	if userAgent == "" {
		return authModels.DeviceTypeUnknown
	}
	return authModels.DeviceTypeDesktop
}

// processInvitationRegistration handles registration through invitation token
func (s *authService) processInvitationRegistration(ctx context.Context, req *RegisterRequest) (*authModels.AuthResponse, error) {
	// Validate invitation token
	invitation, err := s.invitationRepo.GetByToken(ctx, req.InvitationToken)
	if err != nil {
		s.logger.WithError(err).WithField("token", req.InvitationToken[:10]+"...").Warn("Invalid invitation token")
		return nil, ErrInvalidInvitation
	}

	// Check if invitation is valid and pending
	if !invitation.IsPending() {
		s.logger.WithFields(map[string]interface{}{
			"invitation_id": invitation.ID,
			"status":        invitation.Status,
			"expired":       invitation.IsExpired(),
		}).Warn("Invitation is not valid or has expired")
		return nil, ErrInvalidInvitation
	}

	// Verify email matches invitation
	if !strings.EqualFold(invitation.Email, req.Email) {
		s.logger.WithFields(map[string]interface{}{
			"invitation_email": invitation.Email,
			"request_email":    req.Email,
		}).Warn("Email does not match invitation")
		return nil, fmt.Errorf("email does not match invitation")
	}

	// Check if email already exists
	exists, err := s.userRepo.Exists(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to check email existence: %w", err)
	}
	if exists {
		return nil, ErrEmailAlreadyExists
	}

	// Check if username exists (if provided)
	if req.Username != "" {
		exists, err := s.userRepo.ExistsByUsername(ctx, req.Username)
		if err != nil {
			return nil, fmt.Errorf("failed to check username existence: %w", err)
		}
		if exists {
			return nil, ErrUsernameAlreadyExists
		}
	}

	// Hash password
	hashedPassword, err := s.passwordService.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	var username, firstName, lastName *string
	if req.Username != "" {
		username = &req.Username
	}
	if req.FirstName != "" {
		firstName = &req.FirstName
	}
	if req.LastName != "" {
		lastName = &req.LastName
	}

	user := &userModels.User{
		Email:         req.Email,
		Username:      username,
		PasswordHash:  hashedPassword,
		FirstName:     firstName,
		LastName:      lastName,
		EmailVerified: true, // Skip email verification for invited users
		Status:        userModels.UserStatusActive,
	}

	// Set email verified timestamp
	now := time.Now()
	user.EmailVerifiedAt = &now

	// Create user in database
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Accept the invitation
	invitation.Accept(user.ID)
	if err := s.invitationRepo.Update(ctx, invitation); err != nil {
		s.logger.WithError(err).WithField("invitation_id", invitation.ID).Error("Failed to accept invitation")
		// Don't fail registration, just log error
	}

	// Create tenant membership based on invitation
	membership := &userModels.TenantMembership{
		UserID:               user.ID,
		TenantID:             invitation.TenantID,
		IsPrimary:            false, // Invited users are not primary owners
		Status:               userModels.TenantMembershipStatusActive,
		JoinedAt:             now,
		InvitedBy:            &invitation.InvitedBy,
		InvitationAcceptedAt: &now,
	}

	if err := s.membershipRepo.Create(ctx, membership); err != nil {
		s.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   user.ID,
			"tenant_id": invitation.TenantID,
		}).Error("Failed to create tenant membership for invited user")
		return nil, ErrMembershipCreationFailed
	}

	// Get tenant information for response
	tenant, err := s.tenantService.GetByID(ctx, invitation.TenantID)
	if err != nil {
		s.logger.WithError(err).WithField("tenant_id", invitation.TenantID).Warn("Failed to get tenant info")
		// Don't fail registration, continue without tenant info
	}

	// Create session for automatic login
	ipAddr := req.IPAddress
	session := &authModels.Session{
		TenantID:   membership.TenantID,
		UserID:     user.ID,
		IPAddress:  &ipAddr,
		DeviceType: detectDeviceType(req.UserAgent),
		ExpiresAt:  time.Now().Add(24 * time.Hour), // 24 hour session
	}

	if err := s.sessionRepo.Create(ctx, session); err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to create session for invited user")
		// Return success but without tokens - user can login manually
		return &authModels.AuthResponse{
			User:       user,
			Tenant:     tenant,
			Membership: membership,
		}, nil
	}

	// Generate JWT tokens
	claims := &authModels.JWTClaims{
		UserID: user.ID,
		//CurrentTenantID: membership.TenantID,
		Email:     user.Email,
		SessionID: &session.ID,
	}

	accessToken, err := s.jwtService.GenerateAccessToken(claims)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to generate access token")
		return &authModels.AuthResponse{
			User:       user,
			Tenant:     tenant,
			Membership: membership,
		}, nil
	}

	refreshToken, err := s.jwtService.GenerateRefreshToken(claims)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to generate refresh token")
		return &authModels.AuthResponse{
			User:        user,
			Tenant:      tenant,
			Membership:  membership,
			AccessToken: accessToken,
		}, nil
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":       user.ID,
		"tenant_id":     invitation.TenantID,
		"invitation_id": invitation.ID,
	}).Info("Successfully registered user through invitation")

	return &authModels.AuthResponse{
		User:         user,
		Tenant:       tenant,
		Membership:   membership,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    3600, // 1 hour
	}, nil
}

// ValidateInvitationToken validates an invitation token and returns invitation details
func (s *authService) ValidateInvitationToken(ctx context.Context, token string) (*userModels.UserInvitation, error) {
	invitation, err := s.invitationRepo.GetByToken(ctx, token)
	if err != nil {
		return nil, ErrInvalidInvitation
	}

	if !invitation.IsPending() {
		return nil, ErrInvalidInvitation
	}

	return invitation, nil
}

// createTenantMembership creates a tenant membership for the user
func (s *authService) createTenantMembership(ctx context.Context, user *userModels.User, tenant *tenantModels.Tenant) (*userModels.TenantMembership, error) {
	now := time.Now()
	membership := &userModels.TenantMembership{
		UserID:   user.ID,
		TenantID: tenant.ID,
		Status:   userModels.TenantMembershipStatusActive,
		JoinedAt: now, // time.Time not *time.Time
	}

	// Create membership using tenant membership repository
	if s.membershipRepo != nil {
		if err := s.membershipRepo.Create(ctx, membership); err != nil {
			return nil, fmt.Errorf("failed to create tenant membership: %w", err)
		}
	}

	return membership, nil
}

// sendWelcomeEmailNotification sends a welcome email notification to newly registered users
func (s *authService) sendWelcomeEmailNotification(ctx context.Context, user *userModels.User, tenant *tenantModels.Tenant) error {
	// Check if user has a profile for additional data
	var userName string
	if user.FirstName != nil && *user.FirstName != "" {
		userName = *user.FirstName
		if user.LastName != nil && *user.LastName != "" {
			userName += " " + *user.LastName
		}
	} else {
		userName = user.Email // Fallback to email if no name
	}

	// Template data for welcome email
	templateData := map[string]interface{}{
		"user": map[string]interface{}{
			"name":  userName,
			"email": user.Email,
		},
		"brand": map[string]interface{}{
			"name":          tenant.Name,
			"support_email": "support@" + tenant.Slug + ".com", // Adjust based on your domain structure
		},
		"current_year": time.Now().Year(),
	}

	// Create notification request
	notificationReq := notificationModels.CreateNotificationRequest{
		Type:         "user_welcome",
		Channel:      notificationModels.ChannelEmail,
		Subject:      fmt.Sprintf("🎉 Welcome to %s!", tenant.Name),
		TemplateID:   func() *uint { id := uint(1); return &id }(), // Use seeded welcome template ID
		TemplateData: templateData,
		Recipients: []notificationModels.CreateRecipientRequest{
			{
				UserID:           func() *uint { id := user.ID; return &id }(),
				RecipientType:    notificationModels.RecipientTypeEmail,
				RecipientAddress: user.Email,
			},
		},
		Priority: notificationModels.PriorityNormal,
		Metadata: map[string]interface{}{
			"source":    "auth_registration",
			"user_id":   user.ID,
			"tenant_id": tenant.ID,
		},
	}

	// Send notification via notification service
	_, err := s.notificationService.CreateNotification(tenant.ID, notificationReq)
	if err != nil {
		return fmt.Errorf("failed to create welcome notification: %w", err)
	}

	return nil
}

// Request and response types

type LoginRequest struct {
	Email         string `json:"email" validate:"required,email" example:"<EMAIL>"`
	Password      string `json:"password" validate:"required" example:"SecurePass123!"`
	TwoFactorCode string `json:"two_factor_code,omitempty" example:""`
	IPAddress     string `json:"-"`
	UserAgent     string `json:"-"`
	DeviceName    string `json:"device_name,omitempty" example:"iPhone 15 Pro"`
}

type RegisterRequest struct {
	Email           string `json:"email" validate:"required,email" example:"<EMAIL>"`
	Username        string `json:"username,omitempty" validate:"omitempty,min=3,max=50" example:"johndoe"`
	Password        string `json:"password" validate:"required,min=8" example:"SecurePass123!"`
	FirstName       string `json:"first_name,omitempty" validate:"omitempty,max=100" example:"John"`
	LastName        string `json:"last_name,omitempty" validate:"omitempty,max=100" example:"Doe"`
	InvitationToken string `json:"invitation_token,omitempty" example:""`
	IPAddress       string `json:"-"`
	UserAgent       string `json:"-"`
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.refresh_token_payload.signature"`
}

// sendEmailVerification sends email verification using default tenant ID
func (s *authService) sendEmailVerification(ctx context.Context, user *userModels.User, isResend bool) error {
	// Get default tenant ID from environment variable
	defaultTenantID := uint(utils.GetEnvAsInt("DEFAULT_TENANT_ID", 1))

	if isResend {
		// Use resend email verification
		req := &authModels.ResendVerificationEmailRequest{
			Email: user.Email,
		}

		_, err := s.emailVerificationService.ResendVerificationEmail(ctx, defaultTenantID, req)
		if err != nil {
			return fmt.Errorf("failed to resend email verification: %w", err)
		}

		s.logger.WithFields(map[string]interface{}{
			"user_id":   user.ID,
			"email":     user.Email,
			"tenant_id": defaultTenantID,
			"is_resend": true,
		}).Info("Email verification resent successfully")

	} else {
		// Create new email verification token
		req := &authModels.CreateEmailVerificationTokenRequest{
			UserID: user.ID,
			Email:  user.Email,
		}

		_, err := s.emailVerificationService.CreateVerificationToken(ctx, defaultTenantID, req)
		if err != nil {
			return fmt.Errorf("failed to create email verification token: %w", err)
		}

		s.logger.WithFields(map[string]interface{}{
			"user_id":   user.ID,
			"email":     user.Email,
			"tenant_id": defaultTenantID,
			"is_resend": false,
		}).Info("Email verification sent successfully")
	}

	return nil
}

// ChangePassword changes user's password
func (s *authService) ChangePassword(ctx context.Context, userID uint, req *authModels.ChangePasswordRequest) (*authModels.PasswordChangeResponse, error) {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	// Verify current password
	if err := s.passwordService.VerifyPassword(req.CurrentPassword, user.PasswordHash); err != nil {
		return nil, ErrInvalidCredentials
	}

	// Hash new password
	newPasswordHash, err := s.passwordService.HashPassword(req.NewPassword)
	if err != nil {
		return nil, fmt.Errorf("failed to hash new password: %w", err)
	}

	// Update password
	err = s.userRepo.UpdatePassword(ctx, userID, newPasswordHash)
	if err != nil {
		return nil, fmt.Errorf("failed to update password: %w", err)
	}

	// Optionally revoke all other sessions for security
	sessionsInvalidated := false
	if err := s.sessionRepo.InvalidateAllUserSessions(ctx, userID); err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Warn("Failed to revoke sessions after password change")
	} else {
		sessionsInvalidated = true
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":              userID,
		"sessions_invalidated": sessionsInvalidated,
	}).Info("Password changed successfully")

	return &authModels.PasswordChangeResponse{
		SessionsInvalidated: sessionsInvalidated,
	}, nil
}

// GetAuthStats returns authentication statistics for the user
func (s *authService) GetAuthStats(ctx context.Context, userID uint) (*authModels.StatsResponse, error) {
	// Get active sessions count
	activeSessions, err := s.sessionRepo.CountActiveUserSessions(ctx, userID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to count active sessions")
		activeSessions = 0
	}

	// Get user info
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	return &authModels.StatsResponse{
		TotalUsers:     1, // This user
		ActiveUsers:    1, // This user is active if they're querying stats
		ActiveSessions: uint(activeSessions),
		LoginAttempts:  user.LoginCount,
		TimeRange:      "current",
		Details: map[string]interface{}{
			"two_factor_enabled": user.TwoFactorEnabled,
			"email_verified":     user.EmailVerified,
			"last_login_at":      user.LastLoginAt,
		},
	}, nil
}

// GetProfile returns user profile information
func (s *authService) GetProfile(ctx context.Context, userID uint) (*authModels.UserProfileResponse, error) {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	// Get user's tenant memberships
	memberships, err := s.membershipRepo.GetByUserID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user memberships")
		memberships = nil
	}

	// Get active tenant (if any)
	var activeTenant *tenantModels.Tenant
	if len(memberships) > 0 {
		// Find the primary or first active membership
		for _, membership := range memberships {
			if membership.IsPrimary {
				activeTenant, err = s.tenantService.GetByID(ctx, membership.TenantID)
				if err != nil {
					s.logger.WithError(err).WithField("tenant_id", membership.TenantID).Error("Failed to get active tenant")
				}
				break
			}
		}
		// If no primary found, use first active membership
		if activeTenant == nil && len(memberships) > 0 {
			activeTenant, err = s.tenantService.GetByID(ctx, memberships[0].TenantID)
			if err != nil {
				s.logger.WithError(err).WithField("tenant_id", memberships[0].TenantID).Error("Failed to get tenant")
			}
		}
	}

	return &authModels.UserProfileResponse{
		User:         user,
		Memberships:  convertMembershipsToPointers(memberships),
		ActiveTenant: activeTenant,
	}, nil
}

// convertMembershipsToPointers converts a slice of TenantMembership to a slice of pointers
func convertMembershipsToPointers(memberships []userModels.TenantMembership) []*userModels.TenantMembership {
	result := make([]*userModels.TenantMembership, len(memberships))
	for i := range memberships {
		result[i] = &memberships[i]
	}
	return result
}

// GetSecurityInfo returns security information for the user
func (s *authService) GetSecurityInfo(ctx context.Context, userID uint) (*authModels.SecurityResponse, error) {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	// Get active sessions
	sessions, err := s.sessionRepo.GetActiveSessionsByUser(ctx, userID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to get active sessions")
		sessions = nil
	}

	return &authModels.SecurityResponse{
		TwoFactorEnabled: user.TwoFactorEnabled,
		Sessions:         sessions,
	}, nil
}

// RequestPasswordReset initiates password reset process
func (s *authService) RequestPasswordReset(ctx context.Context, email string) (*authModels.PasswordResetResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Check if user exists
	// 2. Generate password reset token
	// 3. Send email with reset link
	// 4. Store token in database

	return &authModels.PasswordResetResponse{
		ID:              0, // This would be populated from actual password reset record
		Email:           email,
		IsUsed:          false,
		ExpiresAt:       time.Now().Add(24 * time.Hour),
		CreatedAt:       time.Now(),
		IsExpired:       false,
		TimeUntilExpiry: "24h0m0s",
	}, nil
}

// ResetPassword resets user password using token
func (s *authService) ResetPassword(ctx context.Context, token string, newPassword string) (*authModels.PasswordResetResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Validate the reset token
	// 2. Check if token is not expired
	// 3. Update user password
	// 4. Invalidate the token
	// 5. Optionally invalidate all sessions

	return &authModels.PasswordResetResponse{
		ID:              0,  // This would be populated from actual password reset record
		Email:           "", // Would be populated from token lookup
		IsUsed:          true,
		UsedAt:          &[]time.Time{time.Now()}[0],
		ExpiresAt:       time.Now(),
		CreatedAt:       time.Now().Add(-1 * time.Hour),
		IsExpired:       false,
		TimeUntilExpiry: "",
	}, nil
}

// UpdateProfile updates user profile
func (s *authService) UpdateProfile(ctx context.Context, userID uint, req *userModels.UserUpdateRequest) (*authModels.UserProfileResponse, error) {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	// Update user fields if provided
	// Note: Email is not updatable through profile update
	if req.FirstName != nil {
		user.FirstName = req.FirstName
	}
	if req.LastName != nil {
		user.LastName = req.LastName
	}
	if req.DisplayName != nil {
		user.DisplayName = req.DisplayName
	}
	if req.Phone != nil {
		user.Phone = req.Phone
	}
	if req.AvatarURL != nil {
		user.AvatarURL = req.AvatarURL
	}
	if req.Timezone != "" {
		user.Timezone = req.Timezone
	}
	if req.Language != "" {
		user.Language = req.Language
	}

	// Update user in database
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Return updated profile
	return s.GetProfile(ctx, userID)
}

// ValidateToken validates a JWT token
func (s *authService) ValidateToken(ctx context.Context, token string) (*authModels.ValidationResponse, error) {
	// Validate and parse the token
	claims, err := s.jwtService.ValidateToken(ctx, token)
	if err != nil {
		return &authModels.ValidationResponse{
			Valid: false,
		}, nil
	}

	// Get user information
	user, err := s.userRepo.GetByID(ctx, claims.UserID)
	if err != nil || user == nil {
		return &authModels.ValidationResponse{
			Valid: false,
		}, nil
	}

	// Get tenant information if available
	// TODO
	var tenant *tenantModels.Tenant
	// if claims.CurrentTenantID > 0 {
	// 	tenant, _ = s.tenantService.GetByID(ctx, claims.CurrentTenantID)
	// }

	// Handle optional SessionID
	var sessionID uint
	if claims.SessionID != nil {
		sessionID = *claims.SessionID
	}

	// Convert Unix timestamp to time.Time
	expiresAt := time.Unix(claims.ExpiresAt, 0)

	return &authModels.ValidationResponse{
		Valid:  true,
		UserID: claims.UserID,
		//TenantID:  claims.CurrentTenantID,
		SessionID: sessionID,
		ExpiresAt: &expiresAt,
		User:      user,
		Tenant:    tenant,
	}, nil
}

// ValidateSession validates a session by ID
func (s *authService) ValidateSession(ctx context.Context, sessionID uint) (*authModels.ValidationResponse, error) {
	// Get session
	session, err := s.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return &authModels.ValidationResponse{
			Valid: false,
		}, nil
	}

	if session == nil {
		return &authModels.ValidationResponse{
			Valid: false,
		}, nil
	}

	// Check if session is active
	if !session.IsActive() {
		return &authModels.ValidationResponse{
			Valid: false,
		}, nil
	}

	// Get user information
	user, err := s.userRepo.GetByID(ctx, session.UserID)
	if err != nil || user == nil {
		return &authModels.ValidationResponse{
			Valid: false,
		}, nil
	}

	// Get tenant information if available
	var tenant *tenantModels.Tenant
	var membership *userModels.TenantMembership
	if session.TenantID > 0 {
		tenant, _ = s.tenantService.GetByID(ctx, session.TenantID)
		// Get membership info
		memberships, _ := s.membershipRepo.GetByUserID(ctx, user.ID)
		for _, m := range memberships {
			if m.TenantID == session.TenantID {
				membership = &m
				break
			}
		}
	}

	return &authModels.ValidationResponse{
		Valid:      true,
		UserID:     session.UserID,
		TenantID:   session.TenantID,
		SessionID:  session.ID,
		ExpiresAt:  &session.ExpiresAt,
		User:       user,
		Tenant:     tenant,
		Membership: membership,
	}, nil
}
