package services

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"
	"unicode"

	"golang.org/x/crypto/bcrypt"
)

// PasswordService handles password operations
type PasswordService interface {
	HashPassword(password string) (string, error)
	VerifyPassword(hashedPassword, password string) error
	ValidatePassword(password string) error
	GenerateRandomPassword(length int) string
}

type passwordService struct {
	minLength          int
	requireUppercase   bool
	requireLowercase   bool
	requireNumbers     bool
	requireSpecialChar bool
	bcryptCost         int
}

// PasswordConfig holds password policy configuration
type PasswordConfig struct {
	MinLength          int
	RequireUppercase   bool
	RequireLowercase   bool
	RequireNumbers     bool
	RequireSpecialChar bool
	BcryptCost         int
}

// NewPasswordService creates a new password service
func NewPasswordService(config *PasswordConfig) PasswordService {
	// Set defaults
	if config.MinLength < 8 {
		config.MinLength = 8
	}
	if config.BcryptCost < bcrypt.MinCost || config.BcryptCost > bcrypt.MaxCost {
		config.BcryptCost = bcrypt.DefaultCost
	}

	return &passwordService{
		minLength:          config.MinLength,
		requireUppercase:   config.RequireUppercase,
		requireLowercase:   config.RequireLowercase,
		requireNumbers:     config.RequireNumbers,
		requireSpecialChar: config.RequireSpecialChar,
		bcryptCost:         config.BcryptCost,
	}
}

// HashPassword hashes a password using bcrypt
func (s *passwordService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), s.bcryptCost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}
	return string(hashedBytes), nil
}

// VerifyPassword verifies a password against a hash
func (s *passwordService) VerifyPassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}

// ValidatePassword validates password strength
func (s *passwordService) ValidatePassword(password string) error {
	// Check minimum length
	if len(password) < s.minLength {
		return fmt.Errorf("password must be at least %d characters long", s.minLength)
	}

	// Check for common weak passwords
	if isCommonPassword(password) {
		return errors.New("password is too common, please choose a stronger password")
	}

	var (
		hasUppercase   bool
		hasLowercase   bool
		hasNumber      bool
		hasSpecialChar bool
	)

	// Check password composition
	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUppercase = true
		case unicode.IsLower(char):
			hasLowercase = true
		case unicode.IsDigit(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecialChar = true
		}
	}

	// Validate requirements
	if s.requireUppercase && !hasUppercase {
		return errors.New("password must contain at least one uppercase letter")
	}
	if s.requireLowercase && !hasLowercase {
		return errors.New("password must contain at least one lowercase letter")
	}
	if s.requireNumbers && !hasNumber {
		return errors.New("password must contain at least one number")
	}
	if s.requireSpecialChar && !hasSpecialChar {
		return errors.New("password must contain at least one special character")
	}

	// Check for patterns
	if hasRepeatingChars(password) {
		return errors.New("password contains too many repeating characters")
	}

	// if hasSequentialChars(password) {
	// 	return errors.New("password contains sequential characters")
	// }

	return nil
}

// GenerateRandomPassword generates a random password
func (s *passwordService) GenerateRandomPassword(length int) string {
	if length < s.minLength {
		length = s.minLength
	}

	const (
		uppercase   = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		lowercase   = "abcdefghijklmnopqrstuvwxyz"
		numbers     = "0123456789"
		specialChar = "!@#$%^&*()_+-=[]{}|;:,.<>?"
	)

	var charset strings.Builder
	var password strings.Builder

	// Build charset and ensure requirements
	if s.requireUppercase {
		charset.WriteString(uppercase)
		password.WriteByte(uppercase[randomInt(len(uppercase))])
	}
	if s.requireLowercase {
		charset.WriteString(lowercase)
		password.WriteByte(lowercase[randomInt(len(lowercase))])
	}
	if s.requireNumbers {
		charset.WriteString(numbers)
		password.WriteByte(numbers[randomInt(len(numbers))])
	}
	if s.requireSpecialChar {
		charset.WriteString(specialChar)
		password.WriteByte(specialChar[randomInt(len(specialChar))])
	}

	// If no requirements, use all character sets
	if charset.Len() == 0 {
		charset.WriteString(uppercase)
		charset.WriteString(lowercase)
		charset.WriteString(numbers)
		charset.WriteString(specialChar)
	}

	// Fill remaining length
	chars := charset.String()
	for password.Len() < length {
		password.WriteByte(chars[randomInt(len(chars))])
	}

	// Shuffle the password
	return shuffleString(password.String())
}

// Helper functions

// isCommonPassword checks against common passwords
func isCommonPassword(password string) bool {
	commonPasswords := []string{
		"password", "123456", "password123", "admin", "letmein",
		"welcome", "monkey", "dragon", "123456789", "qwerty",
		"abc123", "111111", "iloveyou", "master", "sunshine",
		"princess", "football", "shadow", "michael", "jennifer",
	}

	lowerPassword := strings.ToLower(password)
	for _, common := range commonPasswords {
		if lowerPassword == common {
			return true
		}
	}
	return false
}

// hasRepeatingChars checks for repeating characters
func hasRepeatingChars(password string) bool {
	for i := 0; i < len(password)-2; i++ {
		if password[i] == password[i+1] && password[i] == password[i+2] {
			return true
		}
	}
	return false
}

// hasSequentialChars checks for sequential characters
func hasSequentialChars(password string) bool {
	sequences := []string{
		"abc", "bcd", "cde", "def", "efg", "fgh", "ghi", "hij", "ijk", "jkl",
		"klm", "lmn", "mno", "nop", "opq", "pqr", "qrs", "rst", "stu", "tuv",
		"uvw", "vwx", "wxy", "xyz", "012", "123", "234", "345", "456", "567",
		"678", "789", "890", "qwe", "wer", "ert", "rty", "tyu", "yui", "uio",
		"iop", "asd", "sdf", "dfg", "fgh", "ghj", "hjk", "jkl", "zxc", "xcv",
		"cvb", "vbn", "bnm",
	}

	lowerPassword := strings.ToLower(password)
	for _, seq := range sequences {
		if strings.Contains(lowerPassword, seq) || strings.Contains(lowerPassword, reverseString(seq)) {
			return true
		}
	}
	return false
}

// reverseString reverses a string
func reverseString(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

// randomInt generates a random integer up to max
func randomInt(max int) int {
	// This is a simple implementation. In production, use crypto/rand
	return int(time.Now().UnixNano() % int64(max))
}

// shuffleString shuffles a string
func shuffleString(s string) string {
	runes := []rune(s)
	for i := len(runes) - 1; i > 0; i-- {
		j := randomInt(i + 1)
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

// PasswordStrength calculates password strength score
func PasswordStrength(password string) int {
	score := 0

	// Length score
	if len(password) >= 8 {
		score += 10
	}
	if len(password) >= 12 {
		score += 10
	}
	if len(password) >= 16 {
		score += 10
	}

	// Character variety score
	hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
	hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
	hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
	hasSpecial := regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?]`).MatchString(password)

	varietyCount := 0
	if hasLower {
		varietyCount++
	}
	if hasUpper {
		varietyCount++
	}
	if hasNumber {
		varietyCount++
	}
	if hasSpecial {
		varietyCount++
	}

	score += varietyCount * 10

	// Pattern penalty
	if hasRepeatingChars(password) {
		score -= 10
	}
	if hasSequentialChars(password) {
		score -= 10
	}
	if isCommonPassword(password) {
		score -= 20
	}

	// Ensure score is between 0 and 100
	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	return score
}
