package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	notificationModels "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type EmailTestHandler struct {
	notificationService notificationServices.NotificationService
	logger              utils.Logger
}

func NewEmailTestHandler(notificationService notificationServices.NotificationService, logger utils.Logger) *EmailTestHandler {
	return &EmailTestHandler{
		notificationService: notificationService,
		logger:              logger,
	}
}

type TestEmailRequest struct {
	To      string `json:"to" binding:"required,email"`
	Subject string `json:"subject" binding:"required"`
	Content string `json:"content" binding:"required"`
}

type TestEmailResponse struct {
	Data interface{} `json:"data,omitempty"`
}

// SendTestEmail sends a test email
// @Summary Send test email
// @Description Send a test email to verify email configuration
// @Tags Authentication - Email Test
// @Accept json
// @Produce json
// @Param request body TestEmailRequest true "Test email request"
// @Success 200 {object} TestEmailResponse "Test email sent successfully"
// @Failure 400 {object} TestEmailResponse "Invalid request"
// @Failure 500 {object} TestEmailResponse "Internal server error"
// @Router /auth/test-email [post]
func (h *EmailTestHandler) SendTestEmail(c *gin.Context) {
	var req TestEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		httpresponse.BadRequest(c.Writer, "Invalid request", err.Error())
		return
	}

	// Get tenant ID from header or use default
	tenantID := uint(1)
	if tenantIDStr := c.GetHeader("X-Tenant-ID"); tenantIDStr != "" {
		if id, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
			tenantID = uint(id)
		}
	}

	h.logger.WithFields(map[string]interface{}{
		"tenant_id": tenantID,
		"to":        req.To,
		"subject":   req.Subject,
	}).Info("Sending test email")

	// Create notification request
	notificationReq := notificationModels.CreateNotificationRequest{
		Type:    "test_email",
		Channel: notificationModels.ChannelEmail,
		Subject: req.Subject,
		Recipients: []notificationModels.CreateRecipientRequest{
			{
				RecipientType:    notificationModels.RecipientTypeEmail,
				RecipientAddress: req.To,
			},
		},
		TemplateData: map[string]interface{}{
			"content": req.Content,
			"user": map[string]interface{}{
				"name":  "Test User",
				"email": req.To,
			},
		},
		Priority: notificationModels.PriorityNormal,
	}

	// Send notification
	notification, err := h.notificationService.CreateNotification(tenantID, notificationReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create notification")
		httpresponse.InternalError(c.Writer, "Failed to send email", err.Error())
		return
	}

	// Try to send the notification immediately
	if err := h.notificationService.SendNotification(tenantID, notification.ID); err != nil {
		h.logger.WithError(err).Error("Failed to send notification")
		httpresponse.InternalError(c.Writer, "Failed to send notification", err.Error())
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"notification_id": notification.ID,
		"tenant_id":       tenantID,
		"to":              req.To,
	}).Info("Test email sent successfully")

	httpresponse.Success(c.Writer, map[string]interface{}{
		"notification_id": notification.ID,
		"tenant_id":       tenantID,
		"to":              req.To,
		"subject":         req.Subject,
		"status":          "sent",
	})
}

// TestSMTPConnection tests SMTP connection
// @Summary Test SMTP connection
// @Description Test SMTP server connection and configuration
// @Tags Authentication - Email Test
// @Accept json
// @Produce json
// @Success 200 {object} TestEmailResponse "SMTP connection successful"
// @Failure 500 {object} TestEmailResponse "SMTP connection failed"
// @Router /auth/test-smtp [post]
func (h *EmailTestHandler) TestSMTPConnection(c *gin.Context) {
	h.logger.Info("Testing SMTP connection")
	
	// Get tenant ID from header or use default
	tenantID := uint(1)
	if tenantIDStr := c.GetHeader("X-Tenant-ID"); tenantIDStr != "" {
		if id, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
			tenantID = uint(id)
		}
	}

	// Try to send a simple test email
	testReq := notificationModels.CreateNotificationRequest{
		Type:    "smtp_test",
		Channel: notificationModels.ChannelEmail,
		Subject: "SMTP Connection Test",
		Recipients: []notificationModels.CreateRecipientRequest{
			{
				RecipientType:    notificationModels.RecipientTypeEmail,
				RecipientAddress: "<EMAIL>",
			},
		},
		TemplateData: map[string]interface{}{
			"content": "This is a test email to verify SMTP connection with MailCatcher.",
			"user": map[string]interface{}{
				"name":  "SMTP Test",
				"email": "<EMAIL>",
			},
		},
		Priority: notificationModels.PriorityHigh,
	}

	notification, err := h.notificationService.CreateNotification(tenantID, testReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create SMTP test notification")
		httpresponse.InternalError(c.Writer, "SMTP test failed", err.Error())
		return
	}

	// Send the notification
	if err := h.notificationService.SendNotification(tenantID, notification.ID); err != nil {
		h.logger.WithError(err).Error("Failed to send SMTP test notification")
		httpresponse.InternalError(c.Writer, "SMTP send failed", err.Error())
		return
	}

	h.logger.WithField("notification_id", notification.ID).Info("SMTP test completed successfully")

	httpresponse.Success(c.Writer, map[string]interface{}{
		"notification_id": notification.ID,
		"tenant_id":       tenantID,
		"smtp_host":       "localhost",
		"smtp_port":       1025,
		"mailcatcher_ui":  "http://localhost:1080",
		"status":          "success",
	})
}