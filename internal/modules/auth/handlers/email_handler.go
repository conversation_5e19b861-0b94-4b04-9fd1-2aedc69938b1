package handlers

import (
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// EmailHandler handles email-related authentication operations
type EmailHandler struct {
	authService              services.AuthService
	emailVerificationService services.EmailVerificationService
	passwordResetService     services.PasswordResetService
	validator                validator.Validator
	logger                   utils.Logger
}

// NewEmailHandler creates a new email handler
func <PERSON>Email<PERSON>ler(
	authService services.AuthService,
	emailVerificationService services.EmailVerificationService,
	passwordResetService services.PasswordResetService,
	validator validator.Validator,
	logger utils.Logger,
) *EmailHandler {
	return &EmailHandler{
		authService:              authService,
		emailVerificationService: emailVerificationService,
		passwordResetService:     passwordResetService,
		validator:                validator,
		logger:                   logger,
	}
}

// VerifyEmail handles email verification
// @Summary Verify email address
// @Description Verify user's email address using verification token
// @Tags Authentication - Email
// @Accept json
// @Produce json
// @Param request body dto.VerifyEmailRequest true "Verification request"
// @Success 200 {object} response.Response{data=dto.VerifyEmailResponse} "Email verified successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /api/cms/v1/auth/verify-email [post]
func (h *EmailHandler) VerifyEmail(c *gin.Context) {
	var req dto.VerifyEmailRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Get client IP and user agent for logging
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// Verify email through auth service
	response, err := h.authService.VerifyEmail(c.Request.Context(), req.Token)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"token":      req.Token[:10] + "...", // Log only first 10 chars for security
			"ip_address": clientIP,
			"user_agent": userAgent,
		}).Error("Email verification failed")

		// Handle specific error cases
		if err.Error() == "invalid or expired verification token" {
			httpresponse.BadRequest(c.Writer, "Invalid or expired verification token")
			return
		}

		httpresponse.BadRequest(c.Writer, "Email verification failed", err.Error())
		return
	}

	// Successful email verification
	h.logger.WithFields(map[string]interface{}{
		"token":      req.Token[:10] + "...",
		"ip_address": clientIP,
		"user_agent": userAgent,
	}).Info("Email verified successfully")

	// Convert service response to DTO response
	verifyEmailResponse := &dto.VerifyEmailResponse{
		Status:        "verified",
		EmailVerified: response.EmailVerified,
		UserID:        response.UserID,
		Email:         response.Email,
	}

	httpresponse.Success(c.Writer, verifyEmailResponse)
}

// ResendVerificationEmail godoc
// @Summary Resend email verification
// @Description Resend verification email to user (rate limited)
// @Tags auth
// @Accept json
// @Produce json
// @Param body body dto.ResendVerificationEmailRequest true "Resend verification request"
// @Success 200 {object} response.Response{data=dto.ResendVerificationEmailResponse} "Verification email sent successfully"
// @Failure 400 {object} response.Response "Invalid request or rate limited"
// @Failure 404 {object} response.Response "User not found"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /api/cms/v1/auth/resend-verification [post]
func (h *EmailHandler) ResendVerificationEmail(c *gin.Context) {
	var req dto.ResendVerificationEmailRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Get client IP and user agent for logging
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// Resend verification email through auth service
	response, err := h.authService.ResendVerificationEmail(c.Request.Context(), req.Email)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"email":      req.Email,
			"ip_address": clientIP,
			"user_agent": userAgent,
		}).Error("Failed to resend verification email")

		// Handle specific error cases
		switch err {
		case services.ErrUserNotFound:
			httpresponse.NotFound(c.Writer, "User not found")
		default:
			// Handle rate limiting and other specific errors
			errMsg := err.Error()
			if strings.Contains(errMsg, "please wait") {
				httpresponse.TooManyRequests(c.Writer, errMsg)
			} else if strings.Contains(errMsg, "maximum resend attempts") {
				httpresponse.TooManyRequests(c.Writer, "Maximum resend attempts exceeded")
			} else if strings.Contains(errMsg, "already verified") {
				httpresponse.BadRequest(c.Writer, "Email is already verified")
			} else {
				httpresponse.InternalError(c.Writer, "Failed to resend verification email", errMsg)
			}
		}
		return
	}

	// Successful resend
	h.logger.WithFields(map[string]interface{}{
		"email":      req.Email,
		"ip_address": clientIP,
		"user_agent": userAgent,
	}).Info("Verification email resent successfully")

	// Convert service response to DTO response
	var nextResendAt string
	if response.NextResendAt != nil {
		nextResendAt = response.NextResendAt.Format("2006-01-02T15:04:05Z07:00")
	}
	
	resendResponse := &dto.ResendVerificationEmailResponse{
		Status:       "sent",
		Email:        response.Email,
		ResendCount:  int(response.ResendCount),
		MaxResends:   int(response.MaxResends),
		NextResendAt: nextResendAt,
	}

	httpresponse.Success(c.Writer, resendResponse)
}

// ForgotPassword godoc
// @Summary Send password reset email
// @Description Send password reset email to user
// @Tags auth
// @Accept json
// @Produce json
// @Param body body dto.ForgotPasswordRequest true "Forgot password request"
// @Success 200 {object} response.Response{data=dto.ForgotPasswordResponse} "Password reset email sent successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 404 {object} response.Response "User not found"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /api/cms/v1/auth/forgot-password [post]
func (h *EmailHandler) ForgotPassword(c *gin.Context) {
	var req dto.ForgotPasswordRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// For password reset, we need to determine the tenant context
	// If this is coming from a specific domain/website, use that context
	// Otherwise, use the default tenant ID
	var tenantID uint
	var websiteID uint
	
	// Try to get tenant ID from context (if available from domain detection)
	tenantIDValue, exists := c.Get("tenant_id")
	if exists {
		// Convert tenant ID to uint (could be string from header or uint from context)
		switch v := tenantIDValue.(type) {
		case uint:
			tenantID = v
		case string:
			// Parse string to uint
			if parsed, err := strconv.ParseUint(v, 10, 32); err == nil {
				tenantID = uint(parsed)
			}
		case int:
			tenantID = uint(v)
		}
	}
	
	// If no tenant ID from context, use default tenant ID
	if tenantID == 0 {
		tenantID = uint(utils.GetEnvAsInt("DEFAULT_TENANT_ID", 1))
	}
	
	// Get website ID if available, otherwise use tenant ID
	websiteIDValue, exists := c.Get("website_id")
	if exists {
		switch v := websiteIDValue.(type) {
		case uint:
			websiteID = v
		case string:
			if parsed, err := strconv.ParseUint(v, 10, 32); err == nil {
				websiteID = uint(parsed)
			}
		case int:
			websiteID = uint(v)
		}
	}
	
	// If no website ID, use tenant ID as fallback
	if websiteID == 0 {
		websiteID = tenantID
	}

	// Get client IP and user agent for security tracking
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// Create password reset token and send email
	err := h.passwordResetService.CreatePasswordResetToken(
		c.Request.Context(),
		tenantID,
		websiteID,
		req.Email,
		&userAgent,
		&clientIP,
	)

	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"email":      req.Email,
			"ip_address": clientIP,
			"user_agent": userAgent,
		}).Error("Failed to create password reset token")

		// Handle specific error cases
		errMsg := err.Error()
		if strings.Contains(errMsg, "please wait") || strings.Contains(errMsg, "too many") {
			httpresponse.TooManyRequests(c.Writer, errMsg)
			return
		}

		// For security, always return success message to prevent user enumeration
		// Even if there's an internal error, don't reveal it to the client
	}

	// Always return success to prevent user enumeration
	h.logger.WithFields(map[string]interface{}{
		"email":      req.Email,
		"ip_address": clientIP,
		"user_agent": userAgent,
	}).Info("Password reset requested")

	// Convert service response to DTO response
	forgotPasswordResponse := &dto.ForgotPasswordResponse{
		Status: "sent",
	}

	httpresponse.Success(c.Writer, forgotPasswordResponse)
}

// ResetPassword godoc
// @Summary Reset password with token
// @Description Reset user password using reset token
// @Tags auth
// @Accept json
// @Produce json
// @Param body body dto.ResetPasswordRequest true "Reset password request"
// @Success 200 {object} response.Response{data=dto.ResetPasswordResponse} "Password reset successfully"
// @Failure 400 {object} response.Response "Invalid request or token"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /api/cms/v1/auth/reset-password [post]
func (h *EmailHandler) ResetPassword(c *gin.Context) {
	var req dto.ResetPasswordRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Check if passwords match
	if req.NewPassword != req.ConfirmPassword {
		httpresponse.BadRequest(c.Writer, "Passwords do not match")
		return
	}

	// Get client IP and user agent for logging
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// Validate token and reset password
	passwordReset, err := h.passwordResetService.ValidateAndResetPassword(
		c.Request.Context(),
		req.Token,
		req.NewPassword,
	)

	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"token":      req.Token[:10] + "...",
			"ip_address": clientIP,
			"user_agent": userAgent,
		}).Error("Password reset failed")

		// Handle specific error cases
		errMsg := err.Error()
		if strings.Contains(errMsg, "invalid or expired") {
			httpresponse.BadRequest(c.Writer, "Invalid or expired reset token")
			return
		}

		if strings.Contains(errMsg, "validation failed") {
			httpresponse.BadRequest(c.Writer, "Password does not meet requirements", err.Error())
			return
		}

		if strings.Contains(errMsg, "user not found") {
			httpresponse.NotFound(c.Writer, "User not found")
			return
		}

		httpresponse.InternalError(c.Writer, "Password reset failed", errMsg)
		return
	}

	// Successful password reset
	h.logger.WithFields(map[string]interface{}{
		"reset_id":   passwordReset.ID,
		"user_id":    passwordReset.UserID,
		"ip_address": clientIP,
		"user_agent": userAgent,
	}).Info("Password reset completed successfully")

	// Convert service response to DTO response
	resetPasswordResponse := &dto.ResetPasswordResponse{
		Status: "completed",
	}

	httpresponse.Success(c.Writer, resetPasswordResponse)
}

// GetVerificationStatus godoc
// @Summary Get email verification status
// @Description Get the verification status for a user's email
// @Tags auth
// @Accept json
// @Produce json
// @Param email query string true "User email address"
// @Success 200 {object} response.Response{data=dto.VerificationStatusResponse} "Email verification status"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 404 {object} response.Response "User not found"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /verification-status [get]
func (h *EmailHandler) GetVerificationStatus(c *gin.Context) {
	// Get email from query parameter
	email := c.Query("email")
	if email == "" {
		httpresponse.BadRequest(c.Writer, "Email parameter is required")
		return
	}

	// Validate email format
	emailReq := struct {
		Email string `validate:"required,email"`
	}{Email: email}

	if err := h.validator.Validate(c.Request.Context(), &emailReq); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid email format", err.Error())
		return
	}

	// Get active token for email
	token, err := h.emailVerificationService.GetActiveTokenForEmail(c.Request.Context(), email)
	if err != nil {
		h.logger.WithError(err).WithField("email", email).Debug("No active token found for email")
		
		// Convert service response to DTO response
		statusResponse := &dto.VerificationStatusResponse{
			Email:      email,
			HasToken:   false,
			IsVerified: true, // Assume verified if no token exists
			CanResend:  false,
		}

		httpresponse.Success(c.Writer, statusResponse)
		return
	}

	// Calculate if resend is allowed
	config := h.emailVerificationService.GetConfig()
	canResend := token.CanResend(config.MaxResendAttempts, time.Duration(config.ResendIntervalMins)*time.Minute)

	// Get client IP for logging
	clientIP := c.ClientIP()
	
	h.logger.WithFields(map[string]interface{}{
		"email":        email,
		"token_id":     token.ID,
		"resend_count": token.ResendCount,
		"can_resend":   canResend,
		"ip_address":   clientIP,
	}).Debug("Retrieved verification status")

	// Convert service response to DTO response
	statusResponse := &dto.VerificationStatusResponse{
		Email:       email,
		HasToken:    true,
		IsVerified:  false,
		CanResend:   canResend,
		ResendCount: int(token.ResendCount),
		MaxResends:  int(config.MaxResendAttempts),
		ExpiresAt:   token.ExpiresAt.Format("2006-01-02T15:04:05Z07:00"),
		IsExpired:   token.IsExpired(),
	}

	httpresponse.Success(c.Writer, statusResponse)
}

// GetVerificationTokenStats handles getting token statistics for a user (protected endpoint)
// @Summary Get verification token statistics
// @Description Get email verification token statistics for the authenticated user
// @Tags Authentication - Email
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.TokenStats "Token statistics"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /auth/verification-token-stats [get]
func (h *EmailHandler) GetVerificationTokenStats(c *gin.Context) {
	// Get user ID from JWT claims (this should be a protected endpoint)
	userIDValue, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Authentication required")
		return
	}

	userID, ok := userIDValue.(uint)
	if !ok {
		httpresponse.InternalServerError(c.Writer, "Invalid user context")
		return
	}

	// Get token statistics
	stats, err := h.emailVerificationService.GetTokenStats(c.Request.Context(), userID)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get token statistics")
		
		httpresponse.InternalServerError(c.Writer, "Failed to retrieve token statistics")
		return
	}

	// Get recent tokens for additional context
	recentTokens, err := h.emailVerificationService.GetRecentTokensForUser(c.Request.Context(), userID, 5)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Warn("Failed to get recent tokens")
		recentTokens = []*models.EmailVerificationToken{} // Empty slice as fallback
	}

	// Convert recent tokens to response format
	var recentTokenResponses []map[string]interface{}
	for _, token := range recentTokens {
		recentTokenResponses = append(recentTokenResponses, map[string]interface{}{
			"id":            token.ID,
			"email":         token.Email,
			"is_used":       token.IsUsed,
			"is_expired":    token.IsExpired(),
			"resend_count":  token.ResendCount,
			"created_at":    token.CreatedAt,
			"expires_at":    token.ExpiresAt,
		})
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id":      userID,
		"total_tokens": len(recentTokens),
	}).Debug("Retrieved token statistics")

	httpresponse.Success(c.Writer, gin.H{
		"statistics":    stats,
		"recent_tokens": recentTokenResponses,
	})
}