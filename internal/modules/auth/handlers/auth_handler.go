package handlers

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	userrepos "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	authService                services.AuthService
	jwtService                 services.JWTService
	passwordService            services.PasswordService
	tenantMembershipRepository userrepos.TenantMembershipRepository
	validator                  validator.Validator
	logger                     utils.Logger
	tracer                     trace.Tracer
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(
	authService services.AuthService,
	jwtService services.JWTService,
	passwordService services.PasswordService,
	tenantMembershipRepository userrepos.TenantMembershipRepository,
	validator validator.Validator,
	logger utils.Logger,
) *AuthHandler {
	return &AuthHandler{
		authService:                authService,
		jwtService:                 jwtService,
		passwordService:            passwordService,
		tenantMembershipRepository: tenantMembershipRepository,
		validator:                  validator,
		logger:                     logger,
		tracer:                     otel.Tracer("auth-handler"),
	}
}

// Login godoc
// @Summary      User login
// @Description  Authenticates a user and returns access and refresh tokens
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        body body dto.LoginRequest true "Login credentials"
// @Success      200 {object} response.Response{data=dto.LoginResponse} "Login successful"
// @Failure      400 {object} response.Response "Invalid request format"
// @Failure      401 {object} response.Response "Invalid email or password"
// @Failure      403 {object} response.Response "Email not verified or account inactive/suspended"
// @Failure      429 {object} response.Response "Account locked due to too many failed attempts"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req dto.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Set IP address and user agent
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.Request.UserAgent()

	// Convert DTO to service request
	serviceReq := &services.LoginRequest{
		Email:         req.Email,
		Password:      req.Password,
		TwoFactorCode: req.TwoFactorCode,
		IPAddress:     req.IPAddress,
		UserAgent:     req.UserAgent,
		DeviceName:    req.DeviceName,
	}

	// In the new multi-tenant architecture, users are global
	// Tenant context is no longer required for login
	// The user can belong to multiple tenants through memberships

	// Perform login
	response, err := h.authService.Login(c.Request.Context(), serviceReq)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"email":      req.Email,
			"ip_address": req.IPAddress,
			"user_agent": req.UserAgent,
		}).Error("Login failed")

		// Map service errors to HTTP status codes
		switch err {
		case services.ErrInvalidCredentials:
			httpresponse.Unauthorized(c.Writer, "Invalid email or password")
		case services.ErrAccountLocked:
			httpresponse.TooManyRequests(c.Writer, "Account locked due to too many failed attempts")
		case services.ErrEmailNotVerified:
			httpresponse.Forbidden(c.Writer, "Email not verified")
		case services.ErrAccountInactive:
			httpresponse.Forbidden(c.Writer, "Account is inactive")
		case services.ErrAccountSuspended:
			httpresponse.Forbidden(c.Writer, "Account is suspended")
		default:
			httpresponse.InternalError(c.Writer, "Internal server error")
		}
		return
	}

	// Handle two-factor authentication requirement
	if response.RequiresTwoFactor {
		loginResponse := &dto.LoginResponse{
			RequiresTwoFactor: true,
		}
		httpresponse.Success(c.Writer, loginResponse)
		return
	}

	// Convert service response to DTO response
	loginResponse := &dto.LoginResponse{
		AccessToken:               response.AccessToken,
		RefreshToken:              response.RefreshToken,
		ExpiresIn:                 response.ExpiresIn,
		TokenType:                 response.TokenType,
		SessionID:                 response.SessionID,
		RequiresTwoFactor:         response.RequiresTwoFactor,
		RequiresEmailVerification: response.RequiresEmailVerification,
		RequiresOnboarding:        response.RequiresOnboarding,
	}

	// Successful login
	h.logger.WithFields(map[string]interface{}{
		"user_id":    response.User.ID,
		"email":      response.User.Email,
		"session_id": response.SessionID,
		"ip_address": req.IPAddress,
	}).Info("User logged in successfully")

	httpresponse.Success(c.Writer, loginResponse)
}

// Register godoc
// @Summary      Register a new user
// @Description  Registers a new user and sends an email verification
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        body body dto.RegisterRequest true "Register request body"
// @Success      201 {object} response.Response{data=dto.RegisterResponse} "Registration successful"
// @Failure      400 {object} response.Response "Invalid request format or validation error"
// @Failure      409 {object} response.Response "Email already exists"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	// Start tracing span for registration flow
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "auth.register")
	defer span.End()

	// Add span attributes for registration flow
	span.SetAttributes(
		attribute.String("auth.operation", "register"),
		attribute.String("auth.ip_address", c.ClientIP()),
		attribute.String("auth.user_agent", c.Request.UserAgent()),
		attribute.String("http.method", c.Request.Method),
		attribute.String("http.url", c.Request.URL.Path),
	)

	var req dto.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.SetStatus(codes.Error, "Invalid request format")
		span.RecordError(err)
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Add email to span attributes (without sensitive data)
	emailDomain := strings.Split(req.Email, "@")
	if len(emailDomain) == 2 {
		span.SetAttributes(
			attribute.String("auth.email_domain", emailDomain[1]),
			attribute.String("auth.email_local_length", fmt.Sprintf("%d", len(emailDomain[0]))),
		)
	}

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		span.SetStatus(codes.Error, "Validation failed")
		span.RecordError(err)
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Set IP address and user agent
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.Request.UserAgent()

	// Convert DTO to service request
	serviceReq := &services.RegisterRequest{
		Email:           req.Email,
		Username:        req.Username,
		Password:        req.Password,
		FirstName:       req.FirstName,
		LastName:        req.LastName,
		InvitationToken: req.InvitationToken,
		IPAddress:       req.IPAddress,
		UserAgent:       req.UserAgent,
	}

	// In the new multi-tenant architecture, users are global
	// Registration doesn't require tenant context
	// Users can be invited to tenants or create new tenants after registration

	// Perform registration with tracing
	span.AddEvent("auth.register.start")
	response, err := h.authService.Register(ctx, serviceReq)
	if err != nil {
		span.SetStatus(codes.Error, "Registration failed")
		span.RecordError(err)

		// Add error attributes to span
		span.SetAttributes(
			attribute.String("auth.error.type", err.Error()),
			attribute.Bool("auth.success", false),
		)

		h.logger.WithError(err).WithFields(map[string]interface{}{
			"email":      req.Email,
			"ip_address": req.IPAddress,
		}).Error("Registration failed")

		// Map service errors to HTTP status codes
		switch err {
		case services.ErrEmailAlreadyExists:
			span.SetAttributes(attribute.String("auth.error.reason", "email_already_exists"))
			httpresponse.NewResponse(c.Writer).Error(409, "Email already exists")
		case services.ErrUsernameAlreadyExists:
			span.SetAttributes(attribute.String("auth.error.reason", "username_already_exists"))
			httpresponse.NewResponse(c.Writer).Error(409, "Username already exists")
		case services.ErrWeakPassword:
			span.SetAttributes(attribute.String("auth.error.reason", "weak_password"))
			httpresponse.BadRequest(c.Writer, "Password does not meet requirements")
		default:
			// Check if it's a password validation error
			h.logger.WithError(err).Error("Password validation failed")
			if h.isPasswordValidationError(err) {
				span.SetAttributes(attribute.String("auth.error.reason", "password_validation_failed"))
				httpresponse.BadRequest(c.Writer, h.getPasswordErrorMessage(err))
			} else {
				span.SetAttributes(attribute.String("auth.error.reason", "internal_server_error"))
				httpresponse.InternalError(c.Writer, "Internal server error")
			}
		}
		return
	}

	// Successful registration - add success attributes to span
	span.AddEvent("auth.register.success")
	span.SetStatus(codes.Ok, "Registration successful")
	span.SetAttributes(
		attribute.Bool("auth.success", true),
		attribute.String("auth.user_id", fmt.Sprintf("%d", response.User.ID)),
		attribute.String("auth.session_id", fmt.Sprintf("%d", response.SessionID)),
		attribute.Bool("auth.requires_email_verification", response.RequiresEmailVerification),
		attribute.String("auth.user_status", string(response.User.Status)),
	)

	// Successful registration
	h.logger.WithFields(map[string]interface{}{
		"user_id":                     response.User.ID,
		"email":                       response.User.Email,
		"session_id":                  response.SessionID,
		"ip_address":                  req.IPAddress,
		"requires_email_verification": response.RequiresEmailVerification,
	}).Info("User registered successfully")

	// Convert service response to DTO response
	dtoResponse := &dto.RegisterResponse{
		RequiresEmailVerification: response.RequiresEmailVerification,
		EmailVerificationSent:     response.RequiresEmailVerification, // Assuming if verification is required, email was sent
	}

	// Handle different registration scenarios
	if response.RequiresEmailVerification {
		// Email verification required - return limited information for security
		httpresponse.Created(c.Writer, &dto.RegisterResponse{
			RequiresEmailVerification: true,
			EmailVerificationSent:     true,
		})
	} else {
		// Email verification not required - return response
		httpresponse.Created(c.Writer, dtoResponse)
	}
}

// Logout godoc
// @Summary      User logout
// @Description  Logs out the current user and invalidates their session
// @Tags         auth
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Success      200 {object} response.Response{data=object} "Logout successful"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// Get user ID from JWT claims
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Authentication required")
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		httpresponse.InternalError(c.Writer, "Invalid user context")
		return
	}

	// Get session ID from JWT claims
	claims, exists := c.Get(httpmiddleware.JWTClaimsKey)
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Invalid token claims")
		return
	}

	jwtClaims, ok := claims.(*models.JWTClaims)
	if !ok || jwtClaims.SessionID == nil {
		httpresponse.InternalServerError(c.Writer, "Invalid session context")
		return
	}

	// Perform logout
	response, err := h.authService.Logout(c.Request.Context(), uid, *jwtClaims.SessionID)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":    uid,
			"session_id": *jwtClaims.SessionID,
		}).Error("Logout failed")

		httpresponse.InternalServerError(c.Writer, "Logout failed")
		return
	}

	// Successful logout
	h.logger.WithFields(map[string]interface{}{
		"user_id":    uid,
		"session_id": *jwtClaims.SessionID,
	}).Info("User logged out successfully")

	httpresponse.Success(c.Writer, gin.H{
		"sessions_terminated": response.SessionsTerminated,
	})
}

// RefreshToken godoc
// @Summary      Refresh access token
// @Description  Exchanges a refresh token for new access and refresh tokens
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        body body dto.RefreshTokenRequest true "Refresh token request"
// @Success      200 {object} response.Response{data=dto.RefreshTokenResponse} "Token refresh successful"
// @Failure      401 {object} response.Response "Invalid refresh token"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req dto.RefreshTokenRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Refresh token
	response, err := h.authService.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"refresh_token": req.RefreshToken[:20] + "...", // Log only first 20 chars for security
		}).Error("Token refresh failed")

		// Map service errors to HTTP status codes
		switch err {
		case services.ErrInvalidRefreshToken:
			httpresponse.Unauthorized(c.Writer, "Invalid refresh token")
		case services.ErrSessionExpired:
			httpresponse.Unauthorized(c.Writer, "Session expired")
		default:
			httpresponse.InternalError(c.Writer, "Internal server error")
		}
		return
	}

	// Successful token refresh
	h.logger.Info("Token refreshed successfully")

	// Convert service response to DTO response
	refreshResponse := &dto.RefreshTokenResponse{
		AccessToken:  response.AccessToken,
		RefreshToken: response.RefreshToken,
		TokenType:    response.TokenType,
		ExpiresIn:    response.ExpiresIn,
	}

	httpresponse.Success(c.Writer, refreshResponse)
}

// GetProfile godoc
// @Summary      Get user profile
// @Description  Returns the current authenticated user's profile information
// @Tags         auth
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Success      200 {object} response.Response{data=dto.GetProfileResponse} "Profile retrieved successfully"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	// Get user ID from JWT claims
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Authentication required")
		return
	}

	_, ok := userID.(uint)
	if !ok {
		httpresponse.InternalError(c.Writer, "Invalid user context")
		return
	}

	// Get JWT claims for additional info
	claims, exists := c.Get(httpmiddleware.JWTClaimsKey)
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Invalid token claims")
		return
	}

	jwtClaims, ok := claims.(*models.JWTClaims)
	if !ok {
		httpresponse.InternalServerError(c.Writer, "Invalid token format")
		return
	}

	// Convert JWT claims to DTO response
	profileResponse := &dto.GetProfileResponse{
		UserID: jwtClaims.UserID,
		Email:  jwtClaims.Email,
		// Role, TenantID, WebsiteID no longer available in user-only JWT claims
		// These fields must be fetched separately via tenant membership endpoints
	}

	httpresponse.Success(c.Writer, profileResponse)
}

// GetActiveSessions godoc
// @Summary Get active sessions
// @Description Get list of user's active login sessions
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=dto.GetActiveSessionsResponse} "List of active sessions"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /sessions [get]
func (h *AuthHandler) GetActiveSessions(c *gin.Context) {
	// Get user ID from JWT claims
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Authentication required")
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		httpresponse.InternalError(c.Writer, "Invalid user context")
		return
	}

	// Get active sessions
	response, err := h.authService.GetActiveSessions(c.Request.Context(), uid)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": uid,
		}).Error("Failed to get active sessions")

		httpresponse.InternalServerError(c.Writer, "Failed to retrieve sessions")
		return
	}

	// Convert sessions to DTO response format
	var sessionResponses []dto.SessionResponse
	for _, session := range response.Sessions {
		var deviceName, ipAddress string
		var lastUsedAt time.Time

		if session.DeviceName != nil {
			deviceName = *session.DeviceName
		}
		if session.IPAddress != nil {
			ipAddress = *session.IPAddress
		}
		if session.LastUsedAt != nil {
			lastUsedAt = *session.LastUsedAt
		}

		sessionResponses = append(sessionResponses, dto.SessionResponse{
			ID:         session.ID,
			DeviceType: string(session.DeviceType),
			DeviceName: deviceName,
			IPAddress:  ipAddress,
			LastUsedAt: lastUsedAt,
			ExpiresAt:  session.ExpiresAt,
		})
	}

	activeSessionsResponse := &dto.GetActiveSessionsResponse{
		Sessions: sessionResponses,
	}

	httpresponse.Success(c.Writer, activeSessionsResponse)
}

// RevokeSession godoc
// @Summary Revoke session
// @Description Revoke a specific login session by session ID
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param sessionId path string true "Session ID to revoke"
// @Success 200 {object} response.Response{data=dto.RevokeSessionResponse} "Session revoked successfully"
// @Failure 400 {object} response.Response "Invalid session ID"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Session not found"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /sessions/{sessionId} [delete]
func (h *AuthHandler) RevokeSession(c *gin.Context) {
	// Get user ID from JWT claims
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Authentication required")
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		httpresponse.InternalError(c.Writer, "Invalid user context")
		return
	}

	// Get session ID from URL parameter
	sessionIDParam := c.Param("sessionId")
	sessionID, err := strconv.ParseUint(sessionIDParam, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid session ID")
		return
	}

	// Revoke session
	response, err := h.authService.RevokeSession(c.Request.Context(), uid, uint(sessionID))
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":    uid,
			"session_id": sessionID,
		}).Error("Failed to revoke session")

		switch err {
		case services.ErrUnauthorized:
			httpresponse.Forbidden(c.Writer, "Unauthorized to revoke this session")
		default:
			httpresponse.InternalServerError(c.Writer, "Failed to revoke session")
		}
		return
	}

	// Successful session revocation
	h.logger.WithFields(map[string]interface{}{
		"user_id":    uid,
		"session_id": sessionID,
	}).Info("Session revoked successfully")

	// Convert service response to DTO response
	revokeResponse := &dto.RevokeSessionResponse{
		Revoked:      response.Revoked,
		RevokedCount: response.RevokedCount,
	}

	httpresponse.Success(c.Writer, revokeResponse)
}

// LogoutAllDevices godoc
// @Summary Logout from all devices
// @Description Logout user from all active sessions/devices
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=dto.LogoutAllDevicesResponse} "Logged out from all devices successfully"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /api/cms/v1/auth/logout-all [post]
func (h *AuthHandler) LogoutAllDevices(c *gin.Context) {
	// Get user ID from JWT claims
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Authentication required")
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		httpresponse.InternalError(c.Writer, "Invalid user context")
		return
	}

	// Logout from all devices
	response, err := h.authService.LogoutAllDevices(c.Request.Context(), uid)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": uid,
		}).Error("Failed to logout from all devices")

		httpresponse.InternalServerError(c.Writer, "Failed to logout from all devices")
		return
	}

	// Successful logout from all devices
	h.logger.WithFields(map[string]interface{}{
		"user_id": uid,
	}).Info("User logged out from all devices successfully")

	// Convert service response to DTO response
	logoutAllResponse := &dto.LogoutAllDevicesResponse{
		SessionsTerminated: response.SessionsTerminated,
	}

	httpresponse.Success(c.Writer, logoutAllResponse)
}

// SwitchTenant handles tenant switching for authenticated users
// @Summary Switch tenant context
// @Description Switch the active tenant context for multi-tenant user
// @Tags Authentication - Multi-Tenant
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.SwitchTenantRequest true "Switch tenant request"
// @Success 200 {object} response.Response{data=dto.SwitchTenantResponse} "Tenant switched successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 403 {object} response.Response "Access denied to tenant"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /auth/switch-tenant [post]
func (h *AuthHandler) SwitchTenant(c *gin.Context) {
	var req dto.SwitchTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Get user ID from JWT claims
	userID, exists := c.Get(httpmiddleware.UserIDKey)
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Authentication required")
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		httpresponse.InternalError(c.Writer, "Invalid user context")
		return
	}

	// Get current JWT claims
	claims, exists := c.Get(httpmiddleware.JWTClaimsKey)
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Invalid token claims")
		return
	}

	jwtClaims, ok := claims.(*models.JWTClaims)
	if !ok {
		httpresponse.InternalError(c.Writer, "Invalid token format")
		return
	}

	// Validate that user has access to target tenant
	canAccess := false
	// for _, membership := range jwtClaims.TenantMemberships {
	// 	if membership.TenantID == req.TenantID && membership.Status == "active" {
	// 		canAccess = true
	// 		break
	// 	}
	// }

	if !canAccess {
		// Double check with database
		hasMembership, err := h.tenantMembershipRepository.UserBelongsToTenant(c.Request.Context(), uid, req.TenantID)
		if err != nil {
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id":   uid,
				"tenant_id": req.TenantID,
			}).Error("Failed to check tenant membership")
			httpresponse.InternalError(c.Writer, "Internal server error")
			return
		}

		if !hasMembership {
			httpresponse.Forbidden(c.Writer, "Access denied to tenant")
			return
		}
	}

	// Create new JWT claims with user-only information
	newClaims := &models.JWTClaims{
		UserID: uid,
		Email:  jwtClaims.Email,
		// Role and tenant info no longer stored in JWT claims
		Scopes:    jwtClaims.Scopes,
		SessionID: jwtClaims.SessionID,
	}

	// Generate new token pair
	accessToken, refreshToken, err := h.jwtService.GenerateTokenPair(newClaims)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   uid,
			"tenant_id": req.TenantID,
		}).Error("Failed to generate new tokens after tenant switch")
		httpresponse.InternalError(c.Writer, "Failed to generate tokens")
		return
	}

	// Get fresh tenant memberships for response
	memberships, err := h.tenantMembershipRepository.GetByUserID(c.Request.Context(), uid)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": uid,
		}).Error("Failed to fetch tenant memberships")
		httpresponse.InternalError(c.Writer, "Internal server error")
		return
	}

	// Convert to DTO response format
	var membershipResponses []dto.TenantMembershipResponse
	for _, membership := range memberships {
		if string(membership.Status) == "active" {
			membershipResponse := dto.TenantMembershipResponse{
				TenantID: membership.TenantID,
				Status:   string(membership.Status),
				JoinedAt: membership.JoinedAt.Unix(),
			}

			if membership.LocalUsername != nil {
				membershipResponse.LocalUsername = *membership.LocalUsername
			}

			if membership.DisplayName != nil {
				membershipResponse.DisplayName = *membership.DisplayName
			}

			membershipResponses = append(membershipResponses, membershipResponse)
		}
	}

	// Successful tenant switch
	h.logger.WithFields(map[string]interface{}{
		"user_id": uid,
		// "old_tenant_id": jwtClaims.CurrentTenantID,
		"new_tenant_id": req.TenantID,
	}).Info("User switched tenant successfully")

	// Convert service response to DTO response
	switchTenantResponse := &dto.SwitchTenantResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    newClaims.ExpiresAt,
	}

	httpresponse.Success(c.Writer, switchTenantResponse)
}

// RefreshTokenWithTenant handles token refresh while maintaining tenant context
// @Summary Refresh token with tenant context
// @Description Refresh access token while maintaining current tenant context
// @Tags Authentication - Multi-Tenant
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.TokenRefreshRequest true "Token refresh request"
// @Success 200 {object} response.Response{data=dto.TokenRefreshResponse} "Token refreshed successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Invalid refresh token"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /api/cms/v1/auth/refresh-with-tenant [post]
func (h *AuthHandler) RefreshTokenWithTenant(c *gin.Context) {
	var req dto.TokenRefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Validate refresh token
	refreshClaims, err := h.jwtService.ValidateRefreshToken(req.RefreshToken)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"refresh_token": req.RefreshToken[:20] + "...",
		}).Error("Token refresh failed")

		switch err {
		case services.ErrInvalidToken, services.ErrTokenExpired:
			httpresponse.Unauthorized(c.Writer, "Invalid or expired refresh token")
		default:
			httpresponse.InternalError(c.Writer, "Internal server error")
		}
		return
	}

	// Create new access token claims with user-only information
	newClaims := &models.JWTClaims{
		UserID:    refreshClaims.UserID,
		Email:     refreshClaims.Email,
		// Role and tenant info no longer stored in JWT claims
		SessionID: refreshClaims.SessionID,
	}

	// Generate new token pair
	accessToken, newRefreshToken, err := h.jwtService.GenerateTokenPair(newClaims)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": refreshClaims.UserID,
			//"tenant_id": refreshClaims.CurrentTenantID,
		}).Error("Failed to generate new tokens during refresh")
		httpresponse.InternalError(c.Writer, "Failed to generate tokens")
		return
	}

	// Get fresh tenant memberships for response
	memberships, err := h.tenantMembershipRepository.GetByUserID(c.Request.Context(), refreshClaims.UserID)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": refreshClaims.UserID,
		}).Error("Failed to fetch tenant memberships during refresh")
		httpresponse.InternalError(c.Writer, "Internal server error")
		return
	}

	// Convert to DTO response format
	var membershipResponses []dto.TenantMembershipResponse
	for _, membership := range memberships {
		if string(membership.Status) == "active" {
			membershipResponse := dto.TenantMembershipResponse{
				TenantID: membership.TenantID,
				Status:   string(membership.Status),
				JoinedAt: membership.JoinedAt.Unix(),
			}

			if membership.LocalUsername != nil {
				membershipResponse.LocalUsername = *membership.LocalUsername
			}

			if membership.DisplayName != nil {
				membershipResponse.DisplayName = *membership.DisplayName
			}

			membershipResponses = append(membershipResponses, membershipResponse)
		}
	}

	// Successful token refresh
	h.logger.WithFields(map[string]interface{}{
		"user_id": refreshClaims.UserID,
		//"tenant_id": refreshClaims.CurrentTenantID,
	}).Info("Token refreshed successfully with tenant context")

	// Convert service response to DTO response
	tokenRefreshResponse := &dto.TokenRefreshResponse{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    newClaims.ExpiresAt,
	}

	httpresponse.Success(c.Writer, tokenRefreshResponse)
}

// isPasswordValidationError checks if the error is a password validation error
func (h *AuthHandler) isPasswordValidationError(err error) bool {
	errMsg := err.Error()
	return strings.Contains(errMsg, "password must") ||
		strings.Contains(errMsg, "password contains") ||
		strings.Contains(errMsg, "characters long")
}

// getPasswordErrorMessage converts password validation errors to user-friendly messages
func (h *AuthHandler) getPasswordErrorMessage(err error) string {
	errMsg := err.Error()

	switch {
	case strings.Contains(errMsg, "must be at least") && strings.Contains(errMsg, "characters long"):
		return "Mật khẩu phải có ít nhất 8 ký tự"
	case strings.Contains(errMsg, "must contain at least one number"):
		return "Mật khẩu phải chứa ít nhất 1 chữ số"
	case strings.Contains(errMsg, "must contain at least one symbol"):
		return "Mật khẩu phải chứa ít nhất 1 ký tự đặc biệt (!@#$%^&*)"
	case strings.Contains(errMsg, "must contain at least one uppercase letter"):
		return "Mật khẩu phải chứa ít nhất 1 chữ cái viết hoa"
	case strings.Contains(errMsg, "must contain at least one lowercase letter"):
		return "Mật khẩu phải chứa ít nhất 1 chữ cái viết thường"
	case strings.Contains(errMsg, "contains common weak pattern"):
		return "Mật khẩu thuộc mật khẩu phổ biến. Vui lòng chọn mật khẩu khác"
	default:
		return "Mật khẩu không đáp ứng yêu cầu bảo mật"
	}
}
