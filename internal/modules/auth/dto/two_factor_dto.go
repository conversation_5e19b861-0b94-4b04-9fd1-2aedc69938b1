package dto

// EnableTwoFactorResponse represents the response payload for enabling 2FA
type EnableTwoFactorResponse struct {
	Secret      string   `json:"secret,omitempty"`
	QRCode      string   `json:"qr_code,omitempty"`
	BackupCodes []string `json:"backup_codes,omitempty"`
	Status      string   `json:"status,omitempty"`
}

// DisableTwoFactorRequest represents the request payload for disabling 2FA
type DisableTwoFactorRequest struct {
	Code string `json:"code" validate:"required,len=6" example:"123456"`
}

// DisableTwoFactorResponse represents the response payload for disabling 2FA
type DisableTwoFactorResponse struct {
	Verified bool   `json:"verified,omitempty"`
	Status   string `json:"status,omitempty"`
}

// VerifyTwoFactorRequest represents the request payload for verifying 2FA
type VerifyTwoFactorRequest struct {
	Code string `json:"code" validate:"required,len=6" example:"123456"`
}

// VerifyTwoFactorResponse represents the response payload for verifying 2FA
type VerifyTwoFactorResponse struct {
	Verified     bool     `json:"verified,omitempty"`
	BackupCodes  []string `json:"backup_codes,omitempty"`
	RecoveryUsed bool     `json:"recovery_used,omitempty"`
	Status       string   `json:"status,omitempty"`
}

// CompleteTwoFactorLoginRequest represents the request payload for completing 2FA login
type CompleteTwoFactorLoginRequest struct {
	Email         string `json:"email" validate:"required,email" example:"<EMAIL>"`
	TwoFactorCode string `json:"two_factor_code" validate:"required,len=6" example:"123456"`
}

// CompleteTwoFactorLoginResponse represents the response payload for completing 2FA login
type CompleteTwoFactorLoginResponse struct {
	Status string `json:"status,omitempty"`
}