package dto

// SwitchTenantRequest represents the request payload for switching tenant
type SwitchTenantRequest struct {
	TenantID uint `json:"tenant_id" validate:"required" example:"1"`
}

// TenantMembershipResponse represents a tenant membership in the response
// KEEP_OMITEMPTY: Optional fields (local_username, display_name) remain omitempty
type TenantMembershipResponse struct {
	TenantID      uint   `json:"tenant_id"`
	Status        string `json:"status"`
	JoinedAt      int64  `json:"joined_at"`
	LocalUsername string `json:"local_username,omitempty"`
	DisplayName   string `json:"display_name,omitempty"`
}

// SwitchTenantResponse represents the response payload for switching tenant
type SwitchTenantResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresAt    int64  `json:"expires_at"`
}

// TokenRefreshRequest represents the request payload for token refresh with tenant
type TokenRefreshRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
}

// TokenRefreshResponse represents the response payload for token refresh
type TokenRefreshResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresAt    int64  `json:"expires_at"`
}