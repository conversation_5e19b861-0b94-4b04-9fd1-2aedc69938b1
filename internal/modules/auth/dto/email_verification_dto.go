package dto

// VerifyEmailRequest represents the request payload for email verification
type VerifyEmailRequest struct {
	Token string `json:"token" validate:"required" example:"abc123def456"`
}

// VerifyEmailResponse represents the response payload for email verification
type VerifyEmailResponse struct {
	Status        string `json:"status"`
	EmailVerified bool   `json:"email_verified"`
	UserID        uint   `json:"user_id"`
	Email         string `json:"email"`
}

// ResendVerificationEmailRequest represents the request payload for resending verification email
type ResendVerificationEmailRequest struct {
	Email string `json:"email" validate:"required,email" example:"<EMAIL>"`
}

// ResendVerificationEmailResponse represents the response payload for resending verification email
// KEEP_OMITEMPTY: NextResendAt may be empty if no rate limit applies
type ResendVerificationEmailResponse struct {
	Status       string `json:"status"`
	Email        string `json:"email"`
	ResendCount  int    `json:"resend_count"`
	MaxResends   int    `json:"max_resends"`
	NextResendAt string `json:"next_resend_at,omitempty"`
}

// VerificationStatusResponse represents the response payload for verification status
// KEEP_OMITEMPTY: ExpiresAt may be empty if no token exists
type VerificationStatusResponse struct {
	Email       string `json:"email"`
	HasToken    bool   `json:"has_token"`
	IsVerified  bool   `json:"is_verified"`
	CanResend   bool   `json:"can_resend"`
	ResendCount int    `json:"resend_count"`
	MaxResends  int    `json:"max_resends"`
	ExpiresAt   string `json:"expires_at,omitempty"`
	IsExpired   bool   `json:"is_expired"`
}