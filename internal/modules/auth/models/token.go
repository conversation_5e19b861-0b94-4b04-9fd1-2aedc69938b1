package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// TokenType represents the type of token
// @Enum api,personal,app
type TokenType string

const (
	TokenTypeAPI      TokenType = "api"
	TokenTypePersonal TokenType = "personal"
	TokenTypeApp      TokenType = "app"
)

// <PERSON>an implements sql.Scanner interface
func (t *TokenType) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*t = TokenType(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (t TokenType) Value() (driver.Value, error) {
	return string(t), nil
}

// TokenScopes represents an array of token scopes
type TokenScopes []string

// Scan implements sql.Scanner interface
func (ts *TokenScopes) Scan(value interface{}) error {
	if value == nil {
		*ts = make(TokenScopes, 0)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, ts)
}

// Value implements driver.Valuer interface
func (ts TokenScopes) Value() (driver.Value, error) {
	if ts == nil {
		return "[]", nil
	}
	return json.Marshal(ts)
}

// HasScope checks if the token has a specific scope
func (ts TokenScopes) HasScope(scope string) bool {
	for _, s := range ts {
		if s == scope {
			return true
		}
	}
	return false
}

// Token represents an API token
type Token struct {
	ID        uint `json:"id" gorm:"primaryKey"`
	TenantID  uint `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint `json:"website_id" gorm:"not null;index"`
	UserID    uint `json:"user_id" gorm:"not null;index"`

	// Token Information
	Name      string    `json:"name" gorm:"not null" validate:"required,max=255"`
	Token     string    `json:"-" gorm:"type:varchar(255);not null;uniqueIndex"` // Don't expose token in JSON
	TokenType TokenType `json:"token_type" gorm:"type:enum('api','personal','app');default:'api';not null"`

	// Permissions and Scopes
	Scopes TokenScopes `json:"scopes" gorm:"type:json"`

	// Usage Tracking
	LastUsedAt *time.Time `json:"last_used_at,omitempty" gorm:"index"`
	UsageCount uint       `json:"usage_count" gorm:"default:0"`

	// Security
	IsRevoked  bool       `json:"is_revoked" gorm:"default:false;index"`
	RevokedAt  *time.Time `json:"revoked_at,omitempty"`
	RevokedBy  *uint      `json:"revoked_by,omitempty"`

	// Expiration
	ExpiresAt *time.Time `json:"expires_at,omitempty" gorm:"index"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations (loaded separately to avoid circular references)
	// Website   *models.Website `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// User      *User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
	// RevokedByUser *User       `json:"revoked_by_user,omitempty" gorm:"foreignKey:RevokedBy"`
}

// TableName specifies the table name for Token
func (Token) TableName() string {
	return "auth_tokens"
}

// IsExpired checks if token is expired
func (t *Token) IsExpired() bool {
	return t.ExpiresAt != nil && time.Now().After(*t.ExpiresAt)
}

// IsActive checks if token is active (not revoked and not expired)
func (t *Token) IsActive() bool {
	return !t.IsRevoked && !t.IsExpired()
}

// IsValid checks if token is valid for use
func (t *Token) IsValid() bool {
	return t.IsActive()
}

// UpdateLastUsed updates the last used timestamp and increments usage count
func (t *Token) UpdateLastUsed() {
	now := time.Now()
	t.LastUsedAt = &now
	t.UsageCount++
}

// Revoke marks the token as revoked
func (t *Token) Revoke(revokedBy *uint) {
	now := time.Now()
	t.IsRevoked = true
	t.RevokedAt = &now
	t.RevokedBy = revokedBy
}

// CanAccess checks if token can access a specific scope
func (t *Token) CanAccess(scope string) bool {
	if !t.IsValid() {
		return false
	}
	return t.Scopes.HasScope(scope) || t.Scopes.HasScope("*")
}

// BeforeCreate hook for Token
func (t *Token) BeforeCreate() error {
	if t.Scopes == nil {
		t.Scopes = make(TokenScopes, 0)
	}
	return nil
}

// TokenFilter represents filters for querying tokens
type TokenFilter struct {
	TenantID   uint      `json:"tenant_id,omitempty"`
	WebsiteID  uint      `json:"website_id,omitempty"`
	UserID     uint      `json:"user_id,omitempty"`
	TokenType  TokenType `json:"token_type,omitempty"`
	IsRevoked  *bool     `json:"is_revoked,omitempty"`
	IsExpired  *bool     `json:"is_expired,omitempty"`
	Search     string    `json:"search,omitempty"`
	Page       int       `json:"page,omitempty"`
	PageSize   int       `json:"page_size,omitempty"`
	SortBy     string    `json:"sort_by,omitempty"`
	SortOrder  string    `json:"sort_order,omitempty"`
}

// CreateTokenRequest represents the request to create a token
type CreateTokenRequest struct {
	TenantID    uint        `json:"tenant_id" validate:"required,min=1"`
	WebsiteID   uint        `json:"website_id" validate:"required,min=1"`
	UserID      uint        `json:"user_id" validate:"required,min=1"`
	Name        string      `json:"name" validate:"required,max=255"`
	TokenType   TokenType   `json:"token_type,omitempty" validate:"omitempty,oneof=api personal app"`
	Scopes      TokenScopes `json:"scopes,omitempty"`
	ExpiresAt   *time.Time  `json:"expires_at,omitempty"`
	ExpiresIn   *int        `json:"expires_in,omitempty"` // Duration in seconds (alternative to ExpiresAt)
}

// UpdateTokenRequest represents the request to update a token
type UpdateTokenRequest struct {
	Name      *string     `json:"name,omitempty" validate:"omitempty,max=255"`
	Scopes    TokenScopes `json:"scopes,omitempty"`
	ExpiresAt *time.Time  `json:"expires_at,omitempty"`
}

// TokenResponse represents the token response with safe information
type TokenResponse struct {
	ID         uint        `json:"id"`
	Name       string      `json:"name"`
	TokenType  TokenType   `json:"token_type"`
	Scopes     TokenScopes `json:"scopes"`
	LastUsedAt *time.Time  `json:"last_used_at,omitempty"`
	UsageCount uint        `json:"usage_count"`
	IsRevoked  bool        `json:"is_revoked"`
	RevokedAt  *time.Time  `json:"revoked_at,omitempty"`
	ExpiresAt  *time.Time  `json:"expires_at,omitempty"`
	CreatedAt  time.Time   `json:"created_at"`
	IsExpired  bool        `json:"is_expired"`    // Computed field
	DaysUntilExpiry *int   `json:"days_until_expiry,omitempty"` // Computed field
}

// ToResponse converts a Token to TokenResponse
func (t *Token) ToResponse() *TokenResponse {
	resp := &TokenResponse{
		ID:         t.ID,
		Name:       t.Name,
		TokenType:  t.TokenType,
		Scopes:     t.Scopes,
		LastUsedAt: t.LastUsedAt,
		UsageCount: t.UsageCount,
		IsRevoked:  t.IsRevoked,
		RevokedAt:  t.RevokedAt,
		ExpiresAt:  t.ExpiresAt,
		CreatedAt:  t.CreatedAt,
		IsExpired:  t.IsExpired(),
	}

	// Calculate days until expiry
	if t.ExpiresAt != nil && !t.IsExpired() {
		days := int(time.Until(*t.ExpiresAt).Hours() / 24)
		if days >= 0 {
			resp.DaysUntilExpiry = &days
		}
	}

	return resp
}

// CreateTokenResponse represents the response when creating a token (includes the actual token)
type CreateTokenResponse struct {
	*TokenResponse
	Token string `json:"token"` // Only returned during creation
}

// LegacyJWTClaims represents the old JWT token claims structure (deprecated)
// This is kept for backward compatibility during migration
type LegacyJWTClaims struct {
	UserID    uint        `json:"user_id"`
	WebsiteID uint        `json:"website_id"`
	TenantID  uint        `json:"tenant_id"`
	Email     string      `json:"email"`
	Role      UserRole    `json:"role"`
	Scopes    TokenScopes `json:"scopes,omitempty"`
	TokenType TokenType   `json:"token_type,omitempty"`
	SessionID *uint       `json:"session_id,omitempty"`
	
	// Standard JWT claims
	Issuer    string `json:"iss,omitempty"`
	Subject   string `json:"sub,omitempty"`
	Audience  string `json:"aud,omitempty"`
	ExpiresAt int64  `json:"exp,omitempty"`
	NotBefore int64  `json:"nbf,omitempty"`
	IssuedAt  int64  `json:"iat,omitempty"`
	ID        string `json:"jti,omitempty"`
}

// IsExpiredJWT checks if JWT token is expired
func (c *LegacyJWTClaims) IsExpiredJWT() bool {
	return c.ExpiresAt > 0 && time.Now().Unix() > c.ExpiresAt
}

// HasScopeJWT checks if JWT token has a specific scope
func (c *LegacyJWTClaims) HasScopeJWT(scope string) bool {
	return c.Scopes.HasScope(scope) || c.Scopes.HasScope("*")
}