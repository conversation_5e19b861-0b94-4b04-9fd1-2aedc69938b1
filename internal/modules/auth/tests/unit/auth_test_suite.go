package unit

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

// AuthTestSuite is the main test suite for auth module
type AuthTestSuite struct {
	suite.Suite
}

// SetupSuite runs before all tests
func (suite *AuthTestSuite) SetupSuite() {
	// Global setup for all auth tests
}

// TearDownSuite runs after all tests
func (suite *AuthTestSuite) TearDownSuite() {
	// Global teardown for all auth tests
}

// SetupTest runs before each test
func (suite *AuthTestSuite) SetupTest() {
	// Setup for each test
}

// TearDownTest runs after each test
func (suite *AuthTestSuite) TearDownTest() {
	// Teardown for each test
}

// TestAuthService runs all auth service tests
func (suite *AuthTestSuite) TestAuthService() {
	// Run auth service tests
	suite.Run("Login_Success", func() {
		TestAuthService_Login_Success(suite.T())
	})

	suite.Run("Login_InvalidCredentials", func() {
		TestAuthService_Login_InvalidCredentials(suite.T())
	})

	suite.Run("Login_UserNotFound", func() {
		TestAuthService_Login_UserNotFound(suite.T())
	})

	suite.Run("Login_EmailNotVerified", func() {
		TestAuthService_Login_EmailNotVerified(suite.T())
	})

	suite.Run("Register_Success", func() {
		TestAuthService_Register_Success(suite.T())
	})

	suite.Run("Register_EmailAlreadyExists", func() {
		TestAuthService_Register_EmailAlreadyExists(suite.T())
	})

	suite.Run("Register_WeakPassword", func() {
		TestAuthService_Register_WeakPassword(suite.T())
	})

	suite.Run("Logout_Success", func() {
		TestAuthService_Logout_Success(suite.T())
	})

	suite.Run("Logout_SessionNotFound", func() {
		TestAuthService_Logout_SessionNotFound(suite.T())
	})

	suite.Run("RefreshToken_Success", func() {
		TestAuthService_RefreshToken_Success(suite.T())
	})

	suite.Run("RefreshToken_InvalidToken", func() {
		TestAuthService_RefreshToken_InvalidToken(suite.T())
	})

	suite.Run("GetActiveSessions_Success", func() {
		TestAuthService_GetActiveSessions_Success(suite.T())
	})

	suite.Run("RevokeSession_Success", func() {
		TestAuthService_RevokeSession_Success(suite.T())
	})

	suite.Run("RevokeSession_Unauthorized", func() {
		TestAuthService_RevokeSession_Unauthorized(suite.T())
	})

	suite.Run("LogoutAllDevices_Success", func() {
		TestAuthService_LogoutAllDevices_Success(suite.T())
	})
}

// TestAuthHandler runs all auth handler tests
func (suite *AuthTestSuite) TestAuthHandler() {
	// Run auth handler tests
	suite.Run("Login_Success", func() {
		TestAuthHandler_Login_Success(suite.T())
	})

	suite.Run("Login_InvalidCredentials", func() {
		TestAuthHandler_Login_InvalidCredentials(suite.T())
	})

	suite.Run("Login_ValidationError", func() {
		TestAuthHandler_Login_ValidationError(suite.T())
	})

	suite.Run("Register_Success", func() {
		TestAuthHandler_Register_Success(suite.T())
	})

	suite.Run("Register_EmailAlreadyExists", func() {
		TestAuthHandler_Register_EmailAlreadyExists(suite.T())
	})

	suite.Run("Logout_Success", func() {
		TestAuthHandler_Logout_Success(suite.T())
	})

	suite.Run("Logout_MissingUserID", func() {
		TestAuthHandler_Logout_MissingUserID(suite.T())
	})

	suite.Run("RefreshToken_Success", func() {
		TestAuthHandler_RefreshToken_Success(suite.T())
	})

	suite.Run("RefreshToken_InvalidToken", func() {
		TestAuthHandler_RefreshToken_InvalidToken(suite.T())
	})

	suite.Run("GetProfile_Success", func() {
		TestAuthHandler_GetProfile_Success(suite.T())
	})

	suite.Run("GetActiveSessions_Success", func() {
		TestAuthHandler_GetActiveSessions_Success(suite.T())
	})

	suite.Run("RevokeSession_Success", func() {
		TestAuthHandler_RevokeSession_Success(suite.T())
	})

	suite.Run("RevokeSession_Unauthorized", func() {
		TestAuthHandler_RevokeSession_Unauthorized(suite.T())
	})

	suite.Run("LogoutAllDevices_Success", func() {
		TestAuthHandler_LogoutAllDevices_Success(suite.T())
	})
}

// TestAuthTestSuite runs the complete auth test suite
func TestAuthTestSuite(t *testing.T) {
	suite.Run(t, new(AuthTestSuite))
}