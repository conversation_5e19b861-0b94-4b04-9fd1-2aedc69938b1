package helpers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/onsi/gomega"
	
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
	"gorm.io/gorm"
)

// RegistrationTestHelper provides helper functions for registration testing
type RegistrationTestHelper struct {
	db     *gorm.DB
	router *gin.Engine
	helper *helpers.TestHelper
}

// NewRegistrationTestHelper creates a new registration test helper
func NewRegistrationTestHelper(db *gorm.DB, router *gin.Engine) *RegistrationTestHelper {
	return &RegistrationTestHelper{
		db:     db,
		router: router,
		helper: helpers.NewTestHelper(db),
	}
}

// RegistrationRequest represents a user registration request
type RegistrationRequest struct {
	Email     string `json:"email"`
	Username  string `json:"username,omitempty"`
	Password  string `json:"password"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	TenantID  uint   `json:"tenant_id"`
	WebsiteID uint   `json:"website_id,omitempty"`
}

// RegistrationResponse represents the response from registration API
type RegistrationResponse struct {
	Success bool                   `json:"success"`
	Data    *RegistrationData      `json:"data,omitempty"`
	Error   *RegistrationError     `json:"error,omitempty"`
}

// RegistrationData represents successful registration data
type RegistrationData struct {
	User         *models.User `json:"user"`
	AccessToken  string       `json:"access_token"`
	RefreshToken string       `json:"refresh_token"`
	TokenType    string       `json:"token_type"`
	ExpiresIn    int          `json:"expires_in"`
	SessionID    uint         `json:"session_id"`
}

// RegistrationError represents registration error response
type RegistrationError struct {
	Message          string                 `json:"message"`
	Code             string                 `json:"code,omitempty"`
	ValidationErrors map[string]interface{} `json:"validation_errors,omitempty"`
}

// RegisterUser performs user registration via API
func (h *RegistrationTestHelper) RegisterUser(req *RegistrationRequest) (*RegistrationResponse, int) {
	body, err := json.Marshal(req)
	gomega.Expect(err).NotTo(gomega.HaveOccurred())

	httpReq := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("User-Agent", "Test Browser Registration Helper")

	w := httptest.NewRecorder()
	h.router.ServeHTTP(w, httpReq)

	var response RegistrationResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	gomega.Expect(err).NotTo(gomega.HaveOccurred())

	return &response, w.Code
}

// CreateValidRegistrationRequest creates a valid registration request
func (h *RegistrationTestHelper) CreateValidRegistrationRequest(tenantID uint) *RegistrationRequest {
	timestamp := time.Now().UnixNano()
	return &RegistrationRequest{
		Email:     fmt.Sprintf("<EMAIL>", timestamp),
		Username:  fmt.Sprintf("user%d", timestamp),
		Password:  "SecurePass123",
		FirstName: "Test",
		LastName:  "User",
		TenantID:  tenantID,
		WebsiteID: 1,
	}
}

// CreateMinimalRegistrationRequest creates a minimal registration request
func (h *RegistrationTestHelper) CreateMinimalRegistrationRequest(tenantID uint) *RegistrationRequest {
	timestamp := time.Now().UnixNano()
	return &RegistrationRequest{
		Email:     fmt.Sprintf("<EMAIL>", timestamp),
		Password:  "SecurePass123",
		TenantID:  tenantID,
		WebsiteID: 1,
	}
}

// CreateInvalidRegistrationRequest creates an invalid registration request
func (h *RegistrationTestHelper) CreateInvalidRegistrationRequest(tenantID uint, invalidField string) *RegistrationRequest {
	req := h.CreateValidRegistrationRequest(tenantID)
	
	switch invalidField {
	case "email":
		req.Email = "invalid-email"
	case "password":
		req.Password = "weak"
	case "username":
		req.Username = "a" // Too short
	case "first_name":
		req.FirstName = string(make([]byte, 101)) // Too long
	case "last_name":
		req.LastName = string(make([]byte, 101)) // Too long
	}
	
	return req
}

// RegisterAndExpectSuccess registers a user and expects success
func (h *RegistrationTestHelper) RegisterAndExpectSuccess(req *RegistrationRequest) *RegistrationData {
	response, statusCode := h.RegisterUser(req)
	
	gomega.Expect(statusCode).To(gomega.Equal(http.StatusCreated))
	gomega.Expect(response.Data).NotTo(gomega.BeNil())
	gomega.Expect(response.Error).To(gomega.BeNil())
	
	return response.Data
}

// RegisterAndExpectError registers a user and expects an error
func (h *RegistrationTestHelper) RegisterAndExpectError(req *RegistrationRequest, expectedStatusCode int, expectedMessage string) *RegistrationError {
	response, statusCode := h.RegisterUser(req)
	
	gomega.Expect(statusCode).To(gomega.Equal(expectedStatusCode))
	gomega.Expect(response.Success).To(gomega.BeFalse())
	gomega.Expect(response.Data).To(gomega.BeNil())
	gomega.Expect(response.Error).NotTo(gomega.BeNil())
	
	if expectedMessage != "" {
		gomega.Expect(response.Error.Message).To(gomega.ContainSubstring(expectedMessage))
	}
	
	return response.Error
}

// VerifyUserInDatabase verifies that a user exists in the database
func (h *RegistrationTestHelper) VerifyUserInDatabase(email string) *models.User {
	var user models.User
	err := h.db.Where("email = ?", email).First(&user).Error
	gomega.Expect(err).NotTo(gomega.HaveOccurred())
	return &user
}

// VerifyUserNotInDatabase verifies that a user does not exist in the database
func (h *RegistrationTestHelper) VerifyUserNotInDatabase(email string) {
	var user models.User
	err := h.db.Where("email = ?", email).First(&user).Error
	gomega.Expect(err).To(gomega.Equal(gorm.ErrRecordNotFound))
}

// VerifySessionInDatabase verifies that a session exists in the database
func (h *RegistrationTestHelper) VerifySessionInDatabase(sessionID uint) *models.Session {
	var session models.Session
	err := h.db.Where("id = ?", sessionID).First(&session).Error
	gomega.Expect(err).NotTo(gomega.HaveOccurred())
	return &session
}

// VerifySessionActive verifies that a session is active
func (h *RegistrationTestHelper) VerifySessionActive(sessionID uint) {
	session := h.VerifySessionInDatabase(sessionID)
	gomega.Expect(session.Status).To(gomega.Equal(models.SessionStatusActive))
	gomega.Expect(session.ExpiresAt).To(gomega.BeTemporally(">", time.Now()))
}

// VerifyUserProperties verifies user properties
func (h *RegistrationTestHelper) VerifyUserProperties(user *models.User, expectedTenantID uint, expectedStatus models.UserStatus) {
	gomega.Expect(user.TenantID).To(gomega.Equal(expectedTenantID))
	gomega.Expect(user.Status).To(gomega.Equal(expectedStatus))
	gomega.Expect(user.Role).To(gomega.Equal(models.UserRoleUser))
	gomega.Expect(user.ID).NotTo(gomega.BeZero())
	gomega.Expect(user.CreatedAt).NotTo(gomega.BeZero())
	gomega.Expect(user.UpdatedAt).NotTo(gomega.BeZero())
}

// VerifyTokens verifies that tokens are valid
func (h *RegistrationTestHelper) VerifyTokens(data *RegistrationData) {
	gomega.Expect(data.AccessToken).NotTo(gomega.BeEmpty())
	gomega.Expect(data.RefreshToken).NotTo(gomega.BeEmpty())
	gomega.Expect(data.TokenType).To(gomega.Equal("Bearer"))
	gomega.Expect(data.ExpiresIn).To(gomega.BeNumerically(">", 0))
	gomega.Expect(data.SessionID).NotTo(gomega.BeZero())
}

// CreateExistingUser creates an existing user for testing conflicts
func (h *RegistrationTestHelper) CreateExistingUser(email, username string, tenantID uint) *models.User {
	user := &models.User{
		Email:         email,
		Username:      &username,
		TenantID:      tenantID,
		Status:        models.UserStatusActive,
		Role:          models.UserRoleUser,
		EmailVerified: true,
		PasswordHash:  "$2a$10$existing.user.password.hash",
	}
	
	err := h.db.Create(user).Error
	gomega.Expect(err).NotTo(gomega.HaveOccurred())
	
	return user
}

// TestRegistrationScenario represents a registration test scenario
type TestRegistrationScenario struct {
	Name              string
	Request           *RegistrationRequest
	ExpectedStatus    int
	ExpectedSuccess   bool
	ExpectedMessage   string
	SetupFunc         func(h *RegistrationTestHelper)
	VerifyFunc        func(h *RegistrationTestHelper, response *RegistrationResponse)
}

// ExecuteRegistrationScenario executes a registration test scenario
func (h *RegistrationTestHelper) ExecuteRegistrationScenario(scenario *TestRegistrationScenario) {
	// Run setup if provided
	if scenario.SetupFunc != nil {
		scenario.SetupFunc(h)
	}
	
	// Execute registration
	response, statusCode := h.RegisterUser(scenario.Request)
	
	// Verify basic expectations
	gomega.Expect(statusCode).To(gomega.Equal(scenario.ExpectedStatus))
	
	if scenario.ExpectedMessage != "" {
		if scenario.ExpectedSuccess {
			gomega.Expect(response.Data).NotTo(gomega.BeNil())
		} else {
			gomega.Expect(response.Error).NotTo(gomega.BeNil())
			gomega.Expect(response.Error.Message).To(gomega.ContainSubstring(scenario.ExpectedMessage))
		}
	}
	
	// Run custom verification if provided
	if scenario.VerifyFunc != nil {
		scenario.VerifyFunc(h, response)
	}
}

// LoginRequest represents a login request for testing
type LoginRequest struct {
	Email     string `json:"email"`
	Password  string `json:"password"`
	WebsiteID uint   `json:"website_id"`
}

// LoginUser performs user login via API
func (h *RegistrationTestHelper) LoginUser(req *LoginRequest) (*RegistrationResponse, int) {
	body, err := json.Marshal(req)
	gomega.Expect(err).NotTo(gomega.HaveOccurred())

	httpReq := httptest.NewRequest("POST", "/api/cms/v1/auth/login", bytes.NewBuffer(body))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("User-Agent", "Test Browser Login Helper")

	w := httptest.NewRecorder()
	h.router.ServeHTTP(w, httpReq)

	var response RegistrationResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	gomega.Expect(err).NotTo(gomega.HaveOccurred())

	return &response, w.Code
}

// TestLoginAfterRegistration tests that login works after registration
func (h *RegistrationTestHelper) TestLoginAfterRegistration(email, password string, websiteID uint) {
	loginReq := &LoginRequest{
		Email:     email,
		Password:  password,
		WebsiteID: websiteID,
	}
	
	response, statusCode := h.LoginUser(loginReq)
	
	gomega.Expect(statusCode).To(gomega.Equal(http.StatusOK))
	gomega.Expect(response.Data).NotTo(gomega.BeNil())
	gomega.Expect(response.Data.AccessToken).NotTo(gomega.BeEmpty())
}

// Performance tracking helpers
type PerformanceMetrics struct {
	RegistrationTime time.Duration
	DatabaseVerifyTime time.Duration
	LoginTime time.Duration
	TotalTime time.Duration
}

// MeasureRegistrationPerformance measures registration performance
func (h *RegistrationTestHelper) MeasureRegistrationPerformance(req *RegistrationRequest) *PerformanceMetrics {
	metrics := &PerformanceMetrics{}
	
	totalStart := time.Now()
	
	// Measure registration time
	regStart := time.Now()
	response, statusCode := h.RegisterUser(req)
	metrics.RegistrationTime = time.Since(regStart)
	
	gomega.Expect(statusCode).To(gomega.Equal(http.StatusCreated))
	gomega.Expect(response.Success).To(gomega.BeTrue())
	
	// Measure database verification time
	dbStart := time.Now()
	h.VerifyUserInDatabase(req.Email)
	metrics.DatabaseVerifyTime = time.Since(dbStart)
	
	// Measure login time
	loginStart := time.Now()
	loginReq := &LoginRequest{
		Email:     req.Email,
		Password:  req.Password,
		WebsiteID: req.WebsiteID,
	}
	loginResponse, loginStatus := h.LoginUser(loginReq)
	metrics.LoginTime = time.Since(loginStart)
	
	gomega.Expect(loginStatus).To(gomega.Equal(http.StatusOK))
	gomega.Expect(loginResponse.Data).NotTo(gomega.BeNil())
	
	metrics.TotalTime = time.Since(totalStart)
	
	return metrics
}

// Batch registration helpers
type BatchRegistrationResult struct {
	SuccessCount int
	ErrorCount   int
	Errors       []string
	TotalTime    time.Duration
}

// BatchRegisterUsers registers multiple users concurrently
func (h *RegistrationTestHelper) BatchRegisterUsers(requests []*RegistrationRequest) *BatchRegistrationResult {
	start := time.Now()
	
	results := make(chan error, len(requests))
	
	for _, req := range requests {
		go func(request *RegistrationRequest) {
			defer func() {
				if r := recover(); r != nil {
					results <- fmt.Errorf("panic: %v", r)
				}
			}()
			
			_, statusCode := h.RegisterUser(request)
			if statusCode != http.StatusCreated {
				results <- fmt.Errorf("registration failed with status %d", statusCode)
			} else {
				results <- nil
			}
		}(req)
	}
	
	result := &BatchRegistrationResult{
		TotalTime: time.Since(start),
	}
	
	// Collect results
	for i := 0; i < len(requests); i++ {
		select {
		case err := <-results:
			if err != nil {
				result.ErrorCount++
				result.Errors = append(result.Errors, err.Error())
			} else {
				result.SuccessCount++
			}
		case <-time.After(30 * time.Second):
			result.ErrorCount++
			result.Errors = append(result.Errors, "timeout")
		}
	}
	
	return result
}

// Helper to generate test data
func (h *RegistrationTestHelper) GenerateTestUsers(count int, tenantID uint) []*RegistrationRequest {
	users := make([]*RegistrationRequest, count)
	for i := 0; i < count; i++ {
		users[i] = &RegistrationRequest{
			Email:     fmt.Sprintf("<EMAIL>", i),
			Username:  fmt.Sprintf("testuser%d", i),
			Password:  "SecurePass123",
			FirstName: "Test",
			LastName:  fmt.Sprintf("User%d", i),
			TenantID:  tenantID,
			WebsiteID: 1,
		}
	}
	return users
}