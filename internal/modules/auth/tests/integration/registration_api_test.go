package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

var _ = Describe("Registration API Integration Tests", func() {
	var (
		router      *gin.Engine
		authHandler *handlers.AuthHandler
		testHelper  *helpers.TestHelper
		tenant      *tenantModels.Tenant
		ctx         context.Context
	)

	BeforeEach(func() {
		// Initialize test context
		ctx = context.Background()
		
		// Create test helper
		testHelper = helpers.NewTestHelper(db)
		
		// Create test tenant
		tenant = testHelper.CreateTenant("Test Tenant")
		
		// Initialize real repositories
		userRepo := mysql.NewUserRepository(db)
		sessionRepo := mysql.NewSessionRepository(db)
		loginAttemptRepo := mysql.NewLoginAttemptRepository(db)
		
		// Initialize real services
		jwtService := services.NewJWTService(&services.JWTConfig{
			SecretKey:        "test-secret-key-32-bytes-long-for-jwt",
			AccessTokenTTL:   time.Hour,
			RefreshTokenTTL:  time.Hour * 24 * 7,
			Issuer:          "test-issuer",
		}, nil)
		
		passwordService := services.NewPasswordService(&services.PasswordConfig{
			MinLength:        8,
			RequireUppercase: true,
			RequireLowercase: true,
			RequireNumbers:   true,
			RequireSymbols:   false,
		})
		
		emailService := services.NewEmailService(&services.EmailConfig{
			SMTPHost:     "localhost",
			SMTPPort:     587,
			SMTPUsername: "<EMAIL>",
			SMTPPassword: "test",
			FromAddress:  "<EMAIL>",
			FromName:     "Test",
		})
		
		rateLimiter := services.NewRateLimitingService(loginAttemptRepo, &services.RateLimitConfig{
			MaxAttempts:     5,
			LockoutDuration: time.Minute * 15,
			WindowDuration:  time.Minute * 5,
		})
		
		authService := services.NewAuthService(
			userRepo,
			sessionRepo,
			loginAttemptRepo,
			jwtService,
			passwordService,
			rateLimiter,
			emailService,
			logger,
			&services.AuthConfig{
				RequireEmailVerification: false,
				AllowRegistration:        true,
				MaxLoginAttempts:         5,
				LockoutDuration:          time.Minute * 15,
				SessionTimeout:           time.Hour * 24,
				RefreshTokenTTL:          time.Hour * 24 * 7,
				TwoFactorIssuer:          "test-app",
			},
		)
		
		// Initialize validator
		validatorInstance := validator.NewValidator()
		
		// Initialize handler
		authHandler = handlers.NewAuthHandler(
			authService,
			jwtService,
			passwordService,
			validatorInstance,
			logger,
		)
		
		// Setup router
		gin.SetMode(gin.TestMode)
		router = gin.New()
		
		// Add middleware to set tenant_id in context
		router.Use(func(c *gin.Context) {
			c.Set("tenant_id", tenant.ID)
			c.Next()
		})
		
		// Setup routes
		v1 := router.Group("/api/cms/v1")
		{
			v1.POST("/auth/register", authHandler.Register)
			v1.POST("/auth/login", authHandler.Login)
		}
	})

	Describe("POST /api/cms/v1/auth/register", func() {
		Context("with valid registration data", func() {
			It("should register user successfully and return 201", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email":      "<EMAIL>",
					"username":   "testuser",
					"password":   "SecurePass123",
					"first_name": "Test",
					"last_name":  "User",
					"tenant_id":  tenant.ID,
					"website_id": 1,
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("User-Agent", "Test Browser")
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusCreated))
				
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				Expect(err).NotTo(HaveOccurred())
				
				Expect(response["success"]).To(BeTrue())
				Expect(response["data"]).To(HaveKey("user"))
				Expect(response["data"]).To(HaveKey("access_token"))
				Expect(response["data"]).To(HaveKey("refresh_token"))
				Expect(response["data"]).To(HaveKey("session_id"))
				
				// Verify user data
				userData := response["data"].(map[string]interface{})["user"].(map[string]interface{})
				Expect(userData["email"]).To(Equal("<EMAIL>"))
				Expect(userData["status"]).To(Equal("active"))
				Expect(userData["email_verified"]).To(BeTrue())
				
				// Verify user was created in database
				var user models.User
				err = db.Where("email = ?", "<EMAIL>").First(&user).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(user.TenantID).To(Equal(tenant.ID))
				Expect(user.Status).To(Equal(models.UserStatusActive))
				
				// Verify session was created
				var session models.Session
				err = db.Where("user_id = ?", user.ID).First(&session).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(session.Status).To(Equal(models.SessionStatusActive))
			})
			
			It("should register user with minimal required fields", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email":      "<EMAIL>",
					"password":   "SecurePass123",
					"tenant_id":  tenant.ID,
					"website_id": 1,
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusCreated))
				
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				Expect(err).NotTo(HaveOccurred())
				
				Expect(response["success"]).To(BeTrue())
				userData := response["data"].(map[string]interface{})["user"].(map[string]interface{})
				Expect(userData["email"]).To(Equal("<EMAIL>"))
				
				// Verify user was created in database
				var user models.User
				err = db.Where("email = ?", "<EMAIL>").First(&user).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(user.Username).To(BeNil())
				Expect(user.FirstName).To(BeNil())
				Expect(user.LastName).To(BeNil())
			})
		})
		
		Context("with invalid request data", func() {
			It("should return 400 for missing required fields", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email": "<EMAIL>",
					// Missing password
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusBadRequest))
				
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				Expect(err).NotTo(HaveOccurred())
				
				Expect(response["success"]).To(BeFalse())
				Expect(response["error"]).To(HaveKey("validation_errors"))
			})
			
			It("should return 400 for invalid email format", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email":      "invalid-email",
					"password":   "SecurePass123",
					"tenant_id":  tenant.ID,
					"website_id": 1,
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusBadRequest))
				
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				Expect(err).NotTo(HaveOccurred())
				
				Expect(response["success"]).To(BeFalse())
				Expect(response["error"]).To(HaveKey("validation_errors"))
			})
			
			It("should return 400 for weak password", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email":      "<EMAIL>",
					"password":   "weak",
					"tenant_id":  tenant.ID,
					"website_id": 1,
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusBadRequest))
				
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				Expect(err).NotTo(HaveOccurred())
				
				Expect(response["success"]).To(BeFalse())
				Expect(response["error"].(map[string]interface{})["message"]).To(ContainSubstring("Mật khẩu"))
			})
			
			It("should return 400 for malformed JSON", func() {
				// Arrange
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer([]byte("invalid json")))
				req.Header.Set("Content-Type", "application/json")
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusBadRequest))
				
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				Expect(err).NotTo(HaveOccurred())
				
				Expect(response["success"]).To(BeFalse())
				Expect(response["error"].(map[string]interface{})["message"]).To(ContainSubstring("Invalid request format"))
			})
		})
		
		Context("with duplicate data", func() {
			BeforeEach(func() {
				// Create existing user
				existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)
				existingUserName := "existinguser"
				existingUser.Username = &existingUserName
				db.Save(existingUser)
			})
			
			It("should return 409 for duplicate email", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email":      "<EMAIL>",
					"password":   "SecurePass123",
					"tenant_id":  tenant.ID,
					"website_id": 1,
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusConflict))
				
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				Expect(err).NotTo(HaveOccurred())
				
				Expect(response["success"]).To(BeFalse())
				Expect(response["error"].(map[string]interface{})["message"]).To(Equal("Email already exists"))
			})
			
			It("should return 409 for duplicate username", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email":      "<EMAIL>",
					"username":   "existinguser",
					"password":   "SecurePass123",
					"tenant_id":  tenant.ID,
					"website_id": 1,
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusConflict))
				
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				Expect(err).NotTo(HaveOccurred())
				
				Expect(response["success"]).To(BeFalse())
				Expect(response["error"].(map[string]interface{})["message"]).To(Equal("Username already exists"))
			})
		})
		
		Context("without tenant context", func() {
			It("should return 400 when tenant_id is missing from context", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email":      "<EMAIL>",
					"password":   "SecurePass123",
					"tenant_id":  tenant.ID,
					"website_id": 1,
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				
				// Create router without tenant middleware
				testRouter := gin.New()
				testRouter.POST("/api/cms/v1/auth/register", authHandler.Register)
				
				// Act
				w := httptest.NewRecorder()
				testRouter.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusBadRequest))
				
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				Expect(err).NotTo(HaveOccurred())
				
				Expect(response["success"]).To(BeFalse())
				Expect(response["error"].(map[string]interface{})["message"]).To(Equal("Tenant context required"))
			})
		})
		
		Context("with content-type variations", func() {
			It("should handle application/json content type", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email":      "<EMAIL>",
					"password":   "SecurePass123",
					"tenant_id":  tenant.ID,
					"website_id": 1,
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusCreated))
			})
			
			It("should reject requests without content-type", func() {
				// Arrange
				registrationData := map[string]interface{}{
					"email":    "<EMAIL>",
					"password": "SecurePass123",
				}
				
				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				// No Content-Type header
				
				// Act
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				// Assert
				Expect(w.Code).To(Equal(http.StatusBadRequest))
			})
		})
	})
	
	Describe("Registration Response Format", func() {
		It("should return consistent response structure", func() {
			// Arrange
			registrationData := map[string]interface{}{
				"email":      "<EMAIL>",
				"password":   "SecurePass123",
				"tenant_id":  tenant.ID,
				"website_id": 1,
			}
			
			body, _ := json.Marshal(registrationData)
			req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			
			// Act
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			
			// Assert
			Expect(w.Code).To(Equal(http.StatusCreated))
			
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			Expect(err).NotTo(HaveOccurred())
			
			// Verify response structure
			Expect(response).To(HaveKey("success"))
			Expect(response).To(HaveKey("data"))
			Expect(response["success"]).To(BeTrue())
			
			data := response["data"].(map[string]interface{})
			Expect(data).To(HaveKey("user"))
			Expect(data).To(HaveKey("access_token"))
			Expect(data).To(HaveKey("refresh_token"))
			Expect(data).To(HaveKey("token_type"))
			Expect(data).To(HaveKey("expires_in"))
			Expect(data).To(HaveKey("session_id"))
			
			// Verify user structure
			user := data["user"].(map[string]interface{})
			Expect(user).To(HaveKey("id"))
			Expect(user).To(HaveKey("email"))
			Expect(user).To(HaveKey("status"))
			Expect(user).To(HaveKey("role"))
			Expect(user).To(HaveKey("email_verified"))
			Expect(user).To(HaveKey("tenant_id"))
			Expect(user).To(HaveKey("created_at"))
			Expect(user).To(HaveKey("updated_at"))
			
			// Verify token type
			Expect(data["token_type"]).To(Equal("Bearer"))
			
			// Verify expires_in is numeric
			Expect(data["expires_in"]).To(BeNumerically(">", 0))
		})
	})
})