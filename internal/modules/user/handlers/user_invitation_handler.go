package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// UserInvitationHandler handles HTTP requests for user invitations
type UserInvitationHandler struct {
	invitationService services.UserInvitationService
	validator         validator.Validator
	logger            utils.Logger
}

// NewUserInvitationHandler creates a new user invitation handler
func NewUserInvitationHandler(
	invitationService services.UserInvitationService,
	validator validator.Validator,
	logger utils.Logger,
) *UserInvitationHandler {
	return &UserInvitationHandler{
		invitationService: invitationService,
		validator:         validator,
		logger:            logger,
	}
}

// CreateInvitation creates a new user invitation
// @Summary Create user invitation
// @Description Create a new invitation for a user to join a tenant
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param invitation body services.CreateInvitationInput true "Invitation data"
// @Success 201 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 409 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations [post]
func (h *UserInvitationHandler) CreateInvitation(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Error("User ID not found in context")
		response.Unauthorized(c.Writer, "User authentication required")
		return
	}

	var input services.CreateInvitationInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind invitation input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate invitation input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Create invitation
	invitation, err := h.invitationService.CreateInvitation(c.Request.Context(), userID.(uint), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create invitation")
		if err.Error() == "user already has a pending invitation for this tenant" ||
			err.Error() == "user is already a member of this tenant" {
			response.Conflict(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to create invitation")
		return
	}

	response.Created(c.Writer, invitation)
}

// GetInvitation retrieves an invitation by ID
// @Summary Get invitation by ID
// @Description Retrieve a user invitation by its ID
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param id path int true "Invitation ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations/{id} [get]
func (h *UserInvitationHandler) GetInvitation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid invitation ID")
		response.BadRequest(c.Writer, "Invalid invitation ID")
		return
	}

	invitation, err := h.invitationService.GetInvitationByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get invitation")
		if err.Error() == "invitation not found" {
			response.NotFound(c.Writer, "Invitation not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get invitation")
		return
	}

	response.OK(c.Writer, invitation)
}

// GetInvitationByToken retrieves an invitation by token
// @Summary Get invitation by token
// @Description Retrieve a user invitation by its token
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param token path string true "Invitation token"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations/token/{token} [get]
func (h *UserInvitationHandler) GetInvitationByToken(c *gin.Context) {
	token := c.Param("token")
	if token == "" {
		h.logger.Error("Token is required")
		response.BadRequest(c.Writer, "Token is required")
		return
	}

	invitation, err := h.invitationService.GetInvitationByToken(c.Request.Context(), token)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get invitation by token")
		if err.Error() == "invitation not found" {
			response.NotFound(c.Writer, "Invitation not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get invitation")
		return
	}

	response.OK(c.Writer, invitation)
}

// ListInvitations lists invitations with filtering and pagination
// @Summary List invitations
// @Description Retrieve a list of invitations with filtering and pagination
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param tenant_id query int true "Tenant ID"
// @Param status query string false "Filter by status"
// @Param email query string false "Filter by email"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations [get]
func (h *UserInvitationHandler) ListInvitations(c *gin.Context) {
	// Get tenant ID from query parameter
	tenantIDStr := c.Query("tenant_id")
	if tenantIDStr == "" {
		h.logger.Error("Tenant ID is required")
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tenant ID")
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.Query("limit")
	limit := 20 // default
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 && parsedLimit <= 100 {
			limit = parsedLimit
		}
	}

	// Build filter
	filter := services.InvitationListFilter{
		TenantID: uint(tenantID),
		Email:    c.Query("email"),
		Search:   c.Query("search"),
		Pagination: &pagination.CursorPagination{
			Cursor: cursor,
			Limit:  limit,
		},
	}

	// Parse status filter if provided
	if statusStr := c.Query("status"); statusStr != "" {
		// Convert string to UserInvitationStatus
		filter.Status = models.UserInvitationStatus(statusStr)
	}

	// List invitations
	listResponse, err := h.invitationService.ListInvitations(c.Request.Context(), filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list invitations")
		response.InternalError(c.Writer, "Failed to list invitations")
		return
	}

	response.OK(c.Writer, listResponse)
}

// UpdateInvitation updates an existing invitation
// @Summary Update invitation
// @Description Update an existing user invitation
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param id path int true "Invitation ID"
// @Param invitation body services.UpdateInvitationInput true "Invitation update data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations/{id} [put]
func (h *UserInvitationHandler) UpdateInvitation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid invitation ID")
		response.BadRequest(c.Writer, "Invalid invitation ID")
		return
	}

	var input services.UpdateInvitationInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind invitation update input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate invitation update input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Update invitation
	invitation, err := h.invitationService.UpdateInvitation(c.Request.Context(), uint(id), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update invitation")
		if err.Error() == "invitation not found" {
			response.NotFound(c.Writer, "Invitation not found")
			return
		}
		if err.Error() == "only pending invitations can be updated" {
			response.BadRequest(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to update invitation")
		return
	}

	response.OK(c.Writer, invitation)
}

// AcceptInvitation accepts a user invitation
// @Summary Accept invitation
// @Description Accept a user invitation using the invitation token
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param invitation body services.AcceptInvitationInput true "Invitation acceptance data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 409 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations/accept [post]
func (h *UserInvitationHandler) AcceptInvitation(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Error("User ID not found in context")
		response.Unauthorized(c.Writer, "User authentication required")
		return
	}

	var input services.AcceptInvitationInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind invitation acceptance input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate invitation acceptance input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Accept invitation
	acceptResponse, err := h.invitationService.AcceptInvitation(c.Request.Context(), userID.(uint), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to accept invitation")
		if err.Error() == "invitation not found" {
			response.NotFound(c.Writer, "Invitation not found")
			return
		}
		if err.Error() == "invitation has expired" ||
			err.Error() == "invitation is not pending" ||
			err.Error() == "invitation email does not match user email" ||
			err.Error() == "user is already a member of this tenant" {
			response.BadRequest(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to accept invitation")
		return
	}

	response.OK(c.Writer, acceptResponse)
}

// RejectInvitation rejects a user invitation
// @Summary Reject invitation
// @Description Reject a user invitation using the invitation token
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param invitation body services.RejectInvitationInput true "Invitation rejection data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations/reject [post]
func (h *UserInvitationHandler) RejectInvitation(c *gin.Context) {
	var input services.RejectInvitationInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind invitation rejection input")
		response.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate invitation rejection input")
		response.ValidationError(c.Writer, err)
		return
	}

	// Reject invitation
	invitation, err := h.invitationService.RejectInvitation(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to reject invitation")
		if err.Error() == "invitation not found" {
			response.NotFound(c.Writer, "Invitation not found")
			return
		}
		if err.Error() == "invitation has expired" ||
			err.Error() == "invitation is not pending" {
			response.BadRequest(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to reject invitation")
		return
	}

	response.OK(c.Writer, invitation)
}

// RevokeInvitation revokes a user invitation
// @Summary Revoke invitation
// @Description Revoke a user invitation (only pending invitations can be revoked)
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param id path int true "Invitation ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations/{id}/revoke [post]
func (h *UserInvitationHandler) RevokeInvitation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid invitation ID")
		response.BadRequest(c.Writer, "Invalid invitation ID")
		return
	}

	// Revoke invitation
	invitation, err := h.invitationService.RevokeInvitation(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to revoke invitation")
		if err.Error() == "invitation not found" {
			response.NotFound(c.Writer, "Invitation not found")
			return
		}
		if err.Error() == "only pending invitations can be revoked" {
			response.BadRequest(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to revoke invitation")
		return
	}

	response.OK(c.Writer, invitation)
}

// ResendInvitation resends a user invitation
// @Summary Resend invitation
// @Description Resend a user invitation with a new token and expiration
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param id path int true "Invitation ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations/{id}/resend [post]
func (h *UserInvitationHandler) ResendInvitation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid invitation ID")
		response.BadRequest(c.Writer, "Invalid invitation ID")
		return
	}

	// Resend invitation
	invitation, err := h.invitationService.ResendInvitation(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to resend invitation")
		if err.Error() == "invitation not found" {
			response.NotFound(c.Writer, "Invitation not found")
			return
		}
		if err.Error() == "only pending invitations can be resent" {
			response.BadRequest(c.Writer, err.Error())
			return
		}
		response.InternalError(c.Writer, "Failed to resend invitation")
		return
	}

	response.OK(c.Writer, invitation)
}

// DeleteInvitation deletes a user invitation
// @Summary Delete invitation
// @Description Delete a user invitation permanently
// @Tags user-invitations
// @Accept json
// @Produce json
// @Param id path int true "Invitation ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-invitations/{id} [delete]
func (h *UserInvitationHandler) DeleteInvitation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid invitation ID")
		response.BadRequest(c.Writer, "Invalid invitation ID")
		return
	}

	// Delete invitation
	if err := h.invitationService.DeleteInvitation(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to delete invitation")
		if err.Error() == "invitation not found" {
			response.NotFound(c.Writer, "Invitation not found")
			return
		}
		response.InternalError(c.Writer, "Failed to delete invitation")
		return
	}

	response.NoContent(c.Writer)
}