package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// TenantMembershipHandler handles tenant membership-related HTTP requests
type TenantMembershipHandler struct {
	membershipService services.TenantMembershipService
	validator         validator.Validator
	logger            utils.Logger
}

// NewTenantMembershipHandler creates a new tenant membership handler
func NewTenantMembershipHandler(
	membershipService services.TenantMembershipService,
	validator validator.Validator,
	logger utils.Logger,
) *TenantMembershipHandler {
	return &TenantMembershipHandler{
		membershipService: membershipService,
		validator:         validator,
		logger:            logger,
	}
}

// GetUserMemberships retrieves all memberships for a user
// @Summary Get user memberships
// @Description Retrieve all tenant memberships for a specific user
// @Tags tenant-memberships
// @Accept json
// @Produce json
// @Param userId path int true "User ID"
// @Success 200 {array} models.TenantMembership
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /users/{userId}/memberships [get]
func (h *TenantMembershipHandler) GetUserMemberships(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	memberships, err := h.membershipService.GetUserMemberships(c.Request.Context(), uint(userID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user memberships")
		if err.Error() == "user not found" {
			httpresponse.NotFound(c.Writer, "User not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to get user memberships")
		return
	}

	httpresponse.OK(c.Writer, memberships)
}

// GetTenantMembers retrieves all members for a tenant
// @Summary Get tenant members
// @Description Retrieve all members for a specific tenant with filtering and pagination
// @Tags tenant-memberships
// @Accept json
// @Produce json
// @Param tenantId path int true "Tenant ID"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Param status query string false "Filter by status"
// @Param role query string false "Filter by role"
// @Param search query string false "Search query"
// @Param sort_by query string false "Sort by field"
// @Param sort_order query string false "Sort order (asc/desc)"
// @Success 200 {object} services.TenantMembershipListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tenants/{tenantId}/members [get]
func (h *TenantMembershipHandler) GetTenantMembers(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tenant ID")
		httpresponse.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	paginationObj := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Parse filter parameters
	filter := services.TenantMembershipFilter{
		TenantID:   uint(tenantID),
		Pagination: paginationObj,
	}

	// Parse optional filters
	if status := c.Query("status"); status != "" {
		filter.Status = models.TenantMembershipStatus(status)
	}

	if role := c.Query("role"); role != "" {
		filter.Role = role
	}

	if search := c.Query("search"); search != "" {
		filter.Search = search
	}

	if sortBy := c.Query("sort_by"); sortBy != "" {
		filter.SortBy = sortBy
	}

	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		filter.SortOrder = sortOrder
	}

	// Get tenant members
	response, err := h.membershipService.GetTenantMembers(c.Request.Context(), uint(tenantID), &filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tenant members")
		httpresponse.InternalError(c.Writer, "Failed to get tenant members")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// AddTenantMember adds a user to a tenant
// @Summary Add tenant member
// @Description Add a user as a member to a tenant
// @Tags tenant-memberships
// @Accept json
// @Produce json
// @Param tenantId path int true "Tenant ID"
// @Param member body services.AddTenantMemberInput true "Member data"
// @Success 201 {object} models.TenantMembership
// @Failure 400 {object} map[string]interface{}
// @Failure 409 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tenants/{tenantId}/members [post]
func (h *TenantMembershipHandler) AddTenantMember(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tenant ID")
		httpresponse.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	var input services.AddTenantMemberInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind member input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Set tenant ID from path parameter
	input.TenantID = uint(tenantID)

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate member input")
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Add member
	membership, err := h.membershipService.AddTenantMember(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to add tenant member")
		if err.Error() == "user not found" {
			httpresponse.NotFound(c.Writer, "User not found")
			return
		}
		if err.Error() == "user is already a member of this tenant" || err.Error() == "local username is already taken" {
			httpresponse.Conflict(c.Writer, err.Error())
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to add tenant member")
		return
	}

	httpresponse.Created(c.Writer, membership)
}

// UpdateMemberRole updates a member's role in a tenant
// @Summary Update member role
// @Description Update a member's role and details in a tenant
// @Tags tenant-memberships
// @Accept json
// @Produce json
// @Param tenantId path int true "Tenant ID"
// @Param userId path int true "User ID"
// @Param role body services.UpdateMemberRoleInput true "Role update data"
// @Success 200 {object} models.TenantMembership
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 409 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tenants/{tenantId}/members/{userId}/role [put]
func (h *TenantMembershipHandler) UpdateMemberRole(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tenant ID")
		httpresponse.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input services.UpdateMemberRoleInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind role input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate role input")
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Update member role
	membership, err := h.membershipService.UpdateMemberRole(c.Request.Context(), uint(tenantID), uint(userID), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update member role")
		if err.Error() == "membership not found" {
			httpresponse.NotFound(c.Writer, "Membership not found")
			return
		}
		if err.Error() == "cannot update deleted membership" {
			httpresponse.BadRequest(c.Writer, "Cannot update deleted membership")
			return
		}
		if err.Error() == "local username is already taken" {
			httpresponse.Conflict(c.Writer, err.Error())
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to update member role")
		return
	}

	httpresponse.OK(c.Writer, membership)
}

// RemoveTenantMember removes a user from a tenant
// @Summary Remove tenant member
// @Description Remove a user from a tenant (soft delete)
// @Tags tenant-memberships
// @Accept json
// @Produce json
// @Param tenantId path int true "Tenant ID"
// @Param userId path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tenants/{tenantId}/members/{userId} [delete]
func (h *TenantMembershipHandler) RemoveTenantMember(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tenant ID")
		httpresponse.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Remove member
	if err := h.membershipService.RemoveTenantMember(c.Request.Context(), uint(tenantID), uint(userID)); err != nil {
		h.logger.WithError(err).Error("Failed to remove tenant member")
		if err.Error() == "membership not found" {
			httpresponse.NotFound(c.Writer, "Membership not found")
			return
		}
		if err.Error() == "member is already removed" {
			httpresponse.BadRequest(c.Writer, "Member is already removed")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to remove tenant member")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// GetMembership retrieves a specific membership
// @Summary Get membership
// @Description Retrieve a specific membership by tenant and user IDs
// @Tags tenant-memberships
// @Accept json
// @Produce json
// @Param tenantId path int true "Tenant ID"
// @Param userId path int true "User ID"
// @Success 200 {object} models.TenantMembership
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tenants/{tenantId}/members/{userId} [get]
func (h *TenantMembershipHandler) GetMembership(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tenant ID")
		httpresponse.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Get membership
	membership, err := h.membershipService.GetMembership(c.Request.Context(), uint(tenantID), uint(userID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get membership")
		if err.Error() == "membership not found" {
			httpresponse.NotFound(c.Writer, "Membership not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to get membership")
		return
	}

	httpresponse.OK(c.Writer, membership)
}

// UpdateMembershipStatus updates membership status
// @Summary Update membership status
// @Description Update the status of a membership
// @Tags tenant-memberships
// @Accept json
// @Produce json
// @Param tenantId path int true "Tenant ID"
// @Param userId path int true "User ID"
// @Param status body map[string]string true "Status data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tenants/{tenantId}/members/{userId}/status [put]
func (h *TenantMembershipHandler) UpdateMembershipStatus(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tenant ID")
		httpresponse.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input struct {
		Status string `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind status input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Convert string to TenantMembershipStatus
	status := models.TenantMembershipStatus(input.Status)

	// Update membership status
	if err := h.membershipService.UpdateMembershipStatus(c.Request.Context(), uint(tenantID), uint(userID), status); err != nil {
		h.logger.WithError(err).Error("Failed to update membership status")
		if err.Error() == "membership not found" {
			httpresponse.NotFound(c.Writer, "Membership not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to update membership status")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// GetMembershipStats retrieves membership statistics
// @Summary Get membership statistics
// @Description Retrieve membership statistics for a tenant
// @Tags tenant-memberships
// @Accept json
// @Produce json
// @Param tenantId path int true "Tenant ID"
// @Success 200 {object} services.TenantMembershipStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tenants/{tenantId}/members/stats [get]
func (h *TenantMembershipHandler) GetMembershipStats(c *gin.Context) {
	tenantIDStr := c.Param("id")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid tenant ID")
		httpresponse.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	// Get membership statistics
	stats, err := h.membershipService.GetMembershipStats(c.Request.Context(), uint(tenantID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get membership statistics")
		httpresponse.InternalError(c.Writer, "Failed to get membership statistics")
		return
	}

	httpresponse.OK(c.Writer, stats)
}