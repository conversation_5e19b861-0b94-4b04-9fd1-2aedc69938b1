package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// UserProfileHandler handles user profile-related HTTP requests
type UserProfileHandler struct {
	profileService services.UserProfileService
	validator      validator.Validator
	logger         utils.Logger
}

// NewUserProfileHandler creates a new user profile handler
func NewUserProfileHandler(
	profileService services.UserProfileService,
	validator validator.Validator,
	logger utils.Logger,
) *UserProfileHandler {
	return &UserProfileHandler{
		profileService: profileService,
		validator:      validator,
		logger:         logger,
	}
}

// CreateUserProfile creates a new user profile
// @Summary Create a new user profile
// @Description Create a new user profile with the provided information
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param profile body services.CreateUserProfileInput true "User profile data"
// @Success 201 {object} models.UserProfile
// @Failure 400 {object} map[string]interface{}
// @Failure 409 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles [post]
func (h *UserProfileHandler) CreateUserProfile(c *gin.Context) {
	var input services.CreateUserProfileInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind profile input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate profile input")
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Create user profile
	profile, err := h.profileService.Create(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create user profile")
		if err.Error() == "profile already exists for user" {
			httpresponse.Conflict(c.Writer, err.Error())
			return
		}
		if err.Error() == "user not found" {
			httpresponse.BadRequest(c.Writer, err.Error())
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to create user profile")
		return
	}

	httpresponse.Created(c.Writer, profile)
}

// GetUserProfile retrieves a user profile by user ID
// @Summary Get a user profile by user ID
// @Description Retrieve a user profile by user ID
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 200 {object} models.UserProfile
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/user/{user_id} [get]
func (h *UserProfileHandler) GetUserProfile(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	profile, err := h.profileService.GetByUserID(c.Request.Context(), uint(userID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user profile")
		if err.Error() == "profile not found" {
			httpresponse.NotFound(c.Writer, "User profile not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to get user profile")
		return
	}

	httpresponse.OK(c.Writer, profile)
}

// UpdateUserProfile updates a user profile
// @Summary Update a user profile
// @Description Update a user profile's information
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param profile body services.UpdateUserProfileInput true "User profile update data"
// @Success 200 {object} models.UserProfile
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/user/{user_id} [put]
func (h *UserProfileHandler) UpdateUserProfile(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input services.UpdateUserProfileInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind profile input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate profile input")
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Update user profile
	profile, err := h.profileService.Update(c.Request.Context(), uint(userID), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update user profile")
		if err.Error() == "profile not found" {
			httpresponse.NotFound(c.Writer, "User profile not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to update user profile")
		return
	}

	httpresponse.OK(c.Writer, profile)
}

// UpdateCompletionStatus updates profile completion status
// @Summary Update profile completion status
// @Description Update the completion status of a user profile
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/user/{user_id}/completion [put]
func (h *UserProfileHandler) UpdateCompletionStatus(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Update completion status
	if err := h.profileService.UpdateCompletionStatus(c.Request.Context(), uint(userID)); err != nil {
		h.logger.WithError(err).Error("Failed to update completion status")
		httpresponse.InternalError(c.Writer, "Failed to update completion status")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// GetProfilesWithLowCompletion retrieves profiles with low completion
// @Summary Get profiles with low completion
// @Description Retrieve profiles with completion percentage below threshold
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param threshold query int false "Completion threshold percentage" default(50)
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserProfileListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/low-completion [get]
func (h *UserProfileHandler) GetProfilesWithLowCompletion(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse threshold parameter
	thresholdStr := c.DefaultQuery("threshold", "50")
	threshold, err := strconv.ParseUint(thresholdStr, 10, 8)
	if err != nil || threshold > 100 {
		h.logger.WithError(err).Error("Invalid threshold parameter")
		httpresponse.BadRequest(c.Writer, "Invalid threshold parameter (must be 0-100)")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Get profiles with low completion
	response, err := h.profileService.GetProfilesWithLowCompletion(c.Request.Context(), tenantID.(uint), uint8(threshold), pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get profiles with low completion")
		httpresponse.InternalError(c.Writer, "Failed to get profiles with low completion")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// SearchProfilesBySkills searches profiles by skills
// @Summary Search profiles by skills
// @Description Search user profiles by skills
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param skills query []string true "Skills to search for" collectionFormat(multi)
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserProfileListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/search/skills [get]
func (h *UserProfileHandler) SearchProfilesBySkills(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse skills parameter
	skills := c.QueryArray("skills")
	if len(skills) == 0 {
		h.logger.Error("Skills parameter is required")
		httpresponse.BadRequest(c.Writer, "Skills parameter is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Search profiles by skills
	response, err := h.profileService.SearchBySkills(c.Request.Context(), tenantID.(uint), skills, pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search profiles by skills")
		httpresponse.InternalError(c.Writer, "Failed to search profiles by skills")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// SearchProfilesByLocation searches profiles by location
// @Summary Search profiles by location
// @Description Search user profiles by location
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param location query string true "Location to search for"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserProfileListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/search/location [get]
func (h *UserProfileHandler) SearchProfilesByLocation(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse location parameter
	location := c.Query("location")
	if location == "" {
		h.logger.Error("Location parameter is required")
		httpresponse.BadRequest(c.Writer, "Location parameter is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Search profiles by location
	response, err := h.profileService.SearchByLocation(c.Request.Context(), tenantID.(uint), location, pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search profiles by location")
		httpresponse.InternalError(c.Writer, "Failed to search profiles by location")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// GetCompletionStats retrieves profile completion statistics
// @Summary Get profile completion statistics
// @Description Retrieve statistics about profile completion for the current tenant
// @Tags user-profiles
// @Accept json
// @Produce json
// @Success 200 {object} services.ProfileCompletionStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/stats/completion [get]
func (h *UserProfileHandler) GetCompletionStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get completion statistics
	stats, err := h.profileService.GetCompletionStats(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get completion statistics")
		httpresponse.InternalError(c.Writer, "Failed to get completion statistics")
		return
	}

	httpresponse.OK(c.Writer, stats)
}

// ValidateProfile validates profile data
// @Summary Validate profile data
// @Description Validate user profile data without saving
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param profile body services.UpdateUserProfileInput true "User profile data to validate"
// @Success 200 {object} map[string]bool
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/validate [post]
func (h *UserProfileHandler) ValidateProfile(c *gin.Context) {
	var input services.UpdateUserProfileInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind profile input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate profile data
	if err := h.profileService.ValidateProfile(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Profile validation failed")
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	httpresponse.OK(c.Writer, map[string]bool{"valid": true})
}

// UpdateCustomFields updates custom fields for a user profile
// @Summary Update custom fields
// @Description Update custom fields for a user profile
// @Tags user-profiles
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param custom_fields body map[string]interface{} true "Custom fields data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-profiles/user/{user_id}/custom-fields [put]
func (h *UserProfileHandler) UpdateCustomFields(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var customFields map[string]interface{}
	if err := c.ShouldBindJSON(&customFields); err != nil {
		h.logger.WithError(err).Error("Failed to bind custom fields input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Update custom fields
	if err := h.profileService.UpdateCustomFields(c.Request.Context(), uint(userID), customFields); err != nil {
		h.logger.WithError(err).Error("Failed to update custom fields")
		httpresponse.InternalError(c.Writer, "Failed to update custom fields")
		return
	}

	httpresponse.NoContent(c.Writer)
}