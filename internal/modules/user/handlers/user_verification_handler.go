package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// UserVerificationHandler handles user verification-related HTTP requests
type UserVerificationHandler struct {
	verificationService services.UserVerificationService
	validator           validator.Validator
	logger              utils.Logger
}

// NewUserVerificationHandler creates a new user verification handler
func NewUserVerificationHandler(
	verificationService services.UserVerificationService,
	validator validator.Validator,
	logger utils.Logger,
) *UserVerificationHandler {
	return &UserVerificationHandler{
		verificationService: verificationService,
		validator:           validator,
		logger:              logger,
	}
}

// SendEmailVerification sends email verification
// @Summary Send email verification
// @Description Send email verification to user
// @Tags user-verification
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-verification/{user_id}/email/send [post]
func (h *UserVerificationHandler) SendEmailVerification(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Send email verification
	if err := h.verificationService.SendEmailVerification(c.Request.Context(), uint(userID)); err != nil {
		h.logger.WithError(err).Error("Failed to send email verification")
		if err.Error() == "user not found" {
			httpresponse.NotFound(c.Writer, "User not found")
			return
		}
		if err.Error() == "email already verified" {
			httpresponse.BadRequest(c.Writer, "Email already verified")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to send email verification")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// VerifyEmail verifies email with token
// @Summary Verify email with token
// @Description Verify user email with verification token
// @Tags user-verification
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param token body map[string]string true "Verification token"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-verification/{user_id}/email/verify [post]
func (h *UserVerificationHandler) VerifyEmail(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input struct {
		Token string `json:"token" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind token input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Verify email
	if err := h.verificationService.VerifyEmail(c.Request.Context(), uint(userID), input.Token); err != nil {
		h.logger.WithError(err).Error("Failed to verify email")
		if err.Error() == "user not found" {
			httpresponse.NotFound(c.Writer, "User not found")
			return
		}
		if err.Error() == "email already verified" {
			httpresponse.BadRequest(c.Writer, "Email already verified")
			return
		}
		if err.Error() == "invalid verification token" {
			httpresponse.BadRequest(c.Writer, "Invalid verification token")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to verify email")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// SendPhoneVerification sends phone verification
// @Summary Send phone verification
// @Description Send phone verification to user
// @Tags user-verification
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-verification/{user_id}/phone/send [post]
func (h *UserVerificationHandler) SendPhoneVerification(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Send phone verification
	if err := h.verificationService.SendPhoneVerification(c.Request.Context(), uint(userID)); err != nil {
		h.logger.WithError(err).Error("Failed to send phone verification")
		if err.Error() == "user not found" {
			httpresponse.NotFound(c.Writer, "User not found")
			return
		}
		if err.Error() == "user has no phone number" {
			httpresponse.BadRequest(c.Writer, "User has no phone number")
			return
		}
		if err.Error() == "phone already verified" {
			httpresponse.BadRequest(c.Writer, "Phone already verified")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to send phone verification")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// VerifyPhone verifies phone with code
// @Summary Verify phone with code
// @Description Verify user phone with verification code
// @Tags user-verification
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param code body map[string]string true "Verification code"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-verification/{user_id}/phone/verify [post]
func (h *UserVerificationHandler) VerifyPhone(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input struct {
		Code string `json:"code" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind code input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Verify phone
	if err := h.verificationService.VerifyPhone(c.Request.Context(), uint(userID), input.Code); err != nil {
		h.logger.WithError(err).Error("Failed to verify phone")
		if err.Error() == "user not found" {
			httpresponse.NotFound(c.Writer, "User not found")
			return
		}
		if err.Error() == "phone already verified" {
			httpresponse.BadRequest(c.Writer, "Phone already verified")
			return
		}
		if err.Error() == "invalid verification code" {
			httpresponse.BadRequest(c.Writer, "Invalid verification code")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to verify phone")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// ResendVerification resends verification
// @Summary Resend verification
// @Description Resend verification for email or phone
// @Tags user-verification
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param type body map[string]string true "Verification type (email or phone)"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-verification/{user_id}/resend [post]
func (h *UserVerificationHandler) ResendVerification(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var input struct {
		Type string `json:"type" binding:"required,oneof=email phone"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind type input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Resend verification
	if err := h.verificationService.ResendVerification(c.Request.Context(), uint(userID), input.Type); err != nil {
		h.logger.WithError(err).Error("Failed to resend verification")
		if err.Error() == "user not found" {
			httpresponse.NotFound(c.Writer, "User not found")
			return
		}
		if err.Error() == "email already verified" || err.Error() == "phone already verified" {
			httpresponse.BadRequest(c.Writer, err.Error())
			return
		}
		if err.Error() == "user has no phone number" {
			httpresponse.BadRequest(c.Writer, "User has no phone number")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to resend verification")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// CheckVerificationStatus checks verification status
// @Summary Check verification status
// @Description Check the verification status of a user
// @Tags user-verification
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 200 {object} services.VerificationStatus
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-verification/{user_id}/status [get]
func (h *UserVerificationHandler) CheckVerificationStatus(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Check verification status
	status, err := h.verificationService.CheckVerificationStatus(c.Request.Context(), uint(userID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to check verification status")
		if err.Error() == "user not found" {
			httpresponse.NotFound(c.Writer, "User not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to check verification status")
		return
	}

	httpresponse.OK(c.Writer, status)
}