package handlers

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// UserSocialLinksHandler handles user social links-related HTTP requests
type UserSocialLinksHandler struct {
	socialLinksService services.UserSocialLinksService
	validator          validator.Validator
	logger             utils.Logger
}

// NewUserSocialLinksHandler creates a new user social links handler
func NewUserSocialLinksHandler(
	socialLinksService services.UserSocialLinksService,
	validator validator.Validator,
	logger utils.Logger,
) *UserSocialLinksHandler {
	return &UserSocialLinksHandler{
		socialLinksService: socialLinksService,
		validator:          validator,
		logger:             logger,
	}
}

// CreateSocialLink creates a new social link
// @Summary Create a new social link
// @Description Create a new social link for a user
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param link body services.CreateUserSocialLinkInput true "Social link data"
// @Success 201 {object} models.UserSocialLink
// @Failure 400 {object} map[string]interface{}
// @Failure 409 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links [post]
func (h *UserSocialLinksHandler) CreateSocialLink(c *gin.Context) {
	var input services.CreateUserSocialLinkInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind social link input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate social link input")
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Create social link
	socialLink, err := h.socialLinksService.Create(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create social link")
		if strings.Contains(err.Error(), "already exists") {
			httpresponse.Conflict(c.Writer, err.Error())
			return
		}
		if err.Error() == "user not found" {
			httpresponse.BadRequest(c.Writer, err.Error())
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to create social link")
		return
	}

	httpresponse.Created(c.Writer, socialLink)
}

// GetUserSocialLinks retrieves all social links for a user
// @Summary Get user social links
// @Description Retrieve all social links for a user
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 200 {array} models.UserSocialLink
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/user/{user_id} [get]
func (h *UserSocialLinksHandler) GetUserSocialLinks(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	socialLinks, err := h.socialLinksService.GetByUserID(c.Request.Context(), uint(userID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user social links")
		httpresponse.InternalError(c.Writer, "Failed to get user social links")
		return
	}

	httpresponse.OK(c.Writer, socialLinks)
}

// GetSocialLinkByPlatform retrieves a social link by user ID and platform
// @Summary Get social link by platform
// @Description Retrieve a social link by user ID and platform
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param platform path string true "Social platform"
// @Success 200 {object} models.UserSocialLink
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/user/{user_id}/platform/{platform} [get]
func (h *UserSocialLinksHandler) GetSocialLinkByPlatform(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	platform := models.SocialPlatform(c.Param("platform"))

	socialLink, err := h.socialLinksService.GetByUserIDAndPlatform(c.Request.Context(), uint(userID), platform)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get social link")
		if err.Error() == "social link not found" {
			httpresponse.NotFound(c.Writer, "Social link not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to get social link")
		return
	}

	httpresponse.OK(c.Writer, socialLink)
}

// UpdateSocialLink updates a social link
// @Summary Update a social link
// @Description Update a social link's information
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param id path int true "Social Link ID"
// @Param link body services.UpdateUserSocialLinkInput true "Social link update data"
// @Success 200 {object} models.UserSocialLink
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/{id} [put]
func (h *UserSocialLinksHandler) UpdateSocialLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid social link ID")
		httpresponse.BadRequest(c.Writer, "Invalid social link ID")
		return
	}

	var input services.UpdateUserSocialLinkInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind social link input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate social link input")
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Update social link
	socialLink, err := h.socialLinksService.Update(c.Request.Context(), uint(id), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update social link")
		if err.Error() == "social link not found" {
			httpresponse.NotFound(c.Writer, "Social link not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to update social link")
		return
	}

	httpresponse.OK(c.Writer, socialLink)
}

// DeleteSocialLink deletes a social link
// @Summary Delete a social link
// @Description Delete a social link
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param id path int true "Social Link ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/{id} [delete]
func (h *UserSocialLinksHandler) DeleteSocialLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid social link ID")
		httpresponse.BadRequest(c.Writer, "Invalid social link ID")
		return
	}

	// Delete social link
	if err := h.socialLinksService.Delete(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to delete social link")
		if err.Error() == "social link not found" {
			httpresponse.NotFound(c.Writer, "Social link not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to delete social link")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// VerifySocialLink verifies a social link
// @Summary Verify a social link
// @Description Verify a social link
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param id path int true "Social Link ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/{id}/verify [post]
func (h *UserSocialLinksHandler) VerifySocialLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid social link ID")
		httpresponse.BadRequest(c.Writer, "Invalid social link ID")
		return
	}

	// Verify social link
	if err := h.socialLinksService.VerifyLink(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to verify social link")
		if err.Error() == "social link not found" {
			httpresponse.NotFound(c.Writer, "Social link not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to verify social link")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// UnverifySocialLink unverifies a social link
// @Summary Unverify a social link
// @Description Unverify a social link
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param id path int true "Social Link ID"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/{id}/unverify [post]
func (h *UserSocialLinksHandler) UnverifySocialLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid social link ID")
		httpresponse.BadRequest(c.Writer, "Invalid social link ID")
		return
	}

	// Unverify social link
	if err := h.socialLinksService.UnverifyLink(c.Request.Context(), uint(id)); err != nil {
		h.logger.WithError(err).Error("Failed to unverify social link")
		if err.Error() == "social link not found" {
			httpresponse.NotFound(c.Writer, "Social link not found")
			return
		}
		httpresponse.InternalError(c.Writer, "Failed to unverify social link")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// ReorderSocialLinks reorders social links for a user
// @Summary Reorder social links
// @Description Reorder social links for a user
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param orders body []models.UserSocialLinkOrder true "Link order data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/user/{user_id}/reorder [post]
func (h *UserSocialLinksHandler) ReorderSocialLinks(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var linkOrders []models.UserSocialLinkOrder
	if err := c.ShouldBindJSON(&linkOrders); err != nil {
		h.logger.WithError(err).Error("Failed to bind link orders input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Reorder social links
	if err := h.socialLinksService.ReorderLinks(c.Request.Context(), uint(userID), linkOrders); err != nil {
		h.logger.WithError(err).Error("Failed to reorder social links")
		httpresponse.InternalError(c.Writer, "Failed to reorder social links")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// GetPublicSocialLinks retrieves public social links for a user
// @Summary Get public social links
// @Description Retrieve public social links for a user
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Success 200 {array} models.UserSocialLink
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/user/{user_id}/public [get]
func (h *UserSocialLinksHandler) GetPublicSocialLinks(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	socialLinks, err := h.socialLinksService.GetPublicLinks(c.Request.Context(), uint(userID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get public social links")
		httpresponse.InternalError(c.Writer, "Failed to get public social links")
		return
	}

	httpresponse.OK(c.Writer, socialLinks)
}

// ValidateSocialLink validates social link data
// @Summary Validate social link data
// @Description Validate social link data without saving
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param link body services.CreateUserSocialLinkInput true "Social link data to validate"
// @Success 200 {object} map[string]bool
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/validate [post]
func (h *UserSocialLinksHandler) ValidateSocialLink(c *gin.Context) {
	var input services.CreateUserSocialLinkInput
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind social link input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate social link data
	if err := h.socialLinksService.ValidateLink(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Social link validation failed")
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	httpresponse.OK(c.Writer, map[string]bool{"valid": true})
}

// GetPlatformStats retrieves platform statistics
// @Summary Get platform statistics
// @Description Retrieve statistics about social platform usage for the current tenant
// @Tags user-social-links
// @Accept json
// @Produce json
// @Success 200 {object} services.SocialPlatformStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/stats [get]
func (h *UserSocialLinksHandler) GetPlatformStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get platform statistics
	stats, err := h.socialLinksService.GetPlatformStats(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get platform statistics")
		httpresponse.InternalError(c.Writer, "Failed to get platform statistics")
		return
	}

	httpresponse.OK(c.Writer, stats)
}

// BulkCreateSocialLinks creates multiple social links
// @Summary Bulk create social links
// @Description Create multiple social links for a user at once
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param links body []services.CreateUserSocialLinkInput true "Social links data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/user/{user_id}/bulk [post]
func (h *UserSocialLinksHandler) BulkCreateSocialLinks(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	var links []services.CreateUserSocialLinkInput
	if err := c.ShouldBindJSON(&links); err != nil {
		h.logger.WithError(err).Error("Failed to bind social links input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Validate each link
	for i, link := range links {
		if err := h.validator.Validate(c.Request.Context(), link); err != nil {
			h.logger.WithError(err).Error("Failed to validate social link input")
			httpresponse.BadRequest(c.Writer, fmt.Sprintf("Invalid input data for link %d", i))
			return
		}
	}

	// Bulk create social links
	if err := h.socialLinksService.BulkCreate(c.Request.Context(), uint(userID), links); err != nil {
		h.logger.WithError(err).Error("Failed to bulk create social links")
		httpresponse.InternalError(c.Writer, "Failed to bulk create social links")
		return
	}

	httpresponse.NoContent(c.Writer)
}

// BulkUpdateVerificationStatus updates verification status for multiple links
// @Summary Bulk update verification status
// @Description Update verification status for multiple social links at once
// @Tags user-social-links
// @Accept json
// @Produce json
// @Param data body map[string]interface{} true "Bulk verification data"
// @Success 204 "No Content"
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-social-links/bulk/verify [put]
func (h *UserSocialLinksHandler) BulkUpdateVerificationStatus(c *gin.Context) {
	var input struct {
		IDs      []uint `json:"ids" binding:"required"`
		Verified bool   `json:"verified" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind bulk verification input")
		httpresponse.BadRequest(c.Writer, "Invalid input data")
		return
	}

	// Bulk update verification status
	if err := h.socialLinksService.BulkUpdateVerificationStatus(c.Request.Context(), input.IDs, input.Verified); err != nil {
		h.logger.WithError(err).Error("Failed to bulk update verification status")
		httpresponse.InternalError(c.Writer, "Failed to bulk update verification status")
		return
	}

	httpresponse.NoContent(c.Writer)
}