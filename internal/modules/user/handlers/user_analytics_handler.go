package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// UserAnalyticsHandler handles user analytics-related HTTP requests
type UserAnalyticsHandler struct {
	analyticsService services.UserAnalyticsService
	validator        validator.Validator
	logger           utils.Logger
}

// NewUserAnalyticsHandler creates a new user analytics handler
func NewUserAnalyticsHandler(
	analyticsService services.UserAnalyticsService,
	validator validator.Validator,
	logger utils.Logger,
) *UserAnalyticsHandler {
	return &UserAnalyticsHandler{
		analyticsService: analyticsService,
		validator:        validator,
		logger:           logger,
	}
}

// GetUserStats retrieves user statistics
// @Summary Get user statistics
// @Description Retrieve comprehensive user statistics for the current tenant
// @Tags user-analytics
// @Accept json
// @Produce json
// @Success 200 {object} services.UserStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-analytics/stats [get]
func (h *UserAnalyticsHandler) GetUserStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get user statistics
	stats, err := h.analyticsService.GetUserStats(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user statistics")
		httpresponse.InternalError(c.Writer, "Failed to get user statistics")
		return
	}

	httpresponse.OK(c.Writer, stats)
}

// GetUserActivity retrieves user activity for a specific user
// @Summary Get user activity
// @Description Retrieve activity data for a specific user
// @Tags user-analytics
// @Accept json
// @Produce json
// @Param user_id path int true "User ID"
// @Param days query int false "Number of days to look back" default(30)
// @Success 200 {object} services.UserActivity
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-analytics/activity/{user_id} [get]
func (h *UserAnalyticsHandler) GetUserActivity(c *gin.Context) {
	// Parse user ID
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID")
		httpresponse.BadRequest(c.Writer, "Invalid user ID")
		return
	}

	// Parse days parameter
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		h.logger.WithError(err).Error("Invalid days parameter")
		httpresponse.BadRequest(c.Writer, "Invalid days parameter (must be 1-365)")
		return
	}

	// Get user activity
	activity, err := h.analyticsService.GetUserActivity(c.Request.Context(), uint(userID), days)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user activity")
		httpresponse.InternalError(c.Writer, "Failed to get user activity")
		return
	}

	httpresponse.OK(c.Writer, activity)
}

// GetRegistrationStats retrieves registration statistics
// @Summary Get registration statistics
// @Description Retrieve registration statistics for the current tenant
// @Tags user-analytics
// @Accept json
// @Produce json
// @Param period query string false "Time period (day, week, month, year)" default(month)
// @Success 200 {object} services.RegistrationStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-analytics/registrations [get]
func (h *UserAnalyticsHandler) GetRegistrationStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse period parameter
	period := c.DefaultQuery("period", "month")
	validPeriods := map[string]bool{
		"day":   true,
		"week":  true,
		"month": true,
		"year":  true,
	}
	if !validPeriods[period] {
		h.logger.Error("Invalid period parameter")
		httpresponse.BadRequest(c.Writer, "Invalid period parameter (must be day, week, month, or year)")
		return
	}

	// Get registration statistics
	stats, err := h.analyticsService.GetRegistrationStats(c.Request.Context(), tenantID.(uint), period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get registration statistics")
		httpresponse.InternalError(c.Writer, "Failed to get registration statistics")
		return
	}

	httpresponse.OK(c.Writer, stats)
}

// GetEngagementStats retrieves engagement statistics
// @Summary Get engagement statistics
// @Description Retrieve engagement statistics for the current tenant
// @Tags user-analytics
// @Accept json
// @Produce json
// @Param period query string false "Time period (day, week, month, year)" default(month)
// @Success 200 {object} services.EngagementStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-analytics/engagement [get]
func (h *UserAnalyticsHandler) GetEngagementStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse period parameter
	period := c.DefaultQuery("period", "month")
	validPeriods := map[string]bool{
		"day":   true,
		"week":  true,
		"month": true,
		"year":  true,
	}
	if !validPeriods[period] {
		h.logger.Error("Invalid period parameter")
		httpresponse.BadRequest(c.Writer, "Invalid period parameter (must be day, week, month, or year)")
		return
	}

	// Get engagement statistics
	stats, err := h.analyticsService.GetEngagementStats(c.Request.Context(), tenantID.(uint), period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get engagement statistics")
		httpresponse.InternalError(c.Writer, "Failed to get engagement statistics")
		return
	}

	httpresponse.OK(c.Writer, stats)
}

// GetRetentionStats retrieves retention statistics
// @Summary Get retention statistics
// @Description Retrieve retention statistics for the current tenant
// @Tags user-analytics
// @Accept json
// @Produce json
// @Param period query string false "Time period (day, week, month, year)" default(month)
// @Success 200 {object} services.RetentionStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-analytics/retention [get]
func (h *UserAnalyticsHandler) GetRetentionStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse period parameter
	period := c.DefaultQuery("period", "month")
	validPeriods := map[string]bool{
		"day":   true,
		"week":  true,
		"month": true,
		"year":  true,
	}
	if !validPeriods[period] {
		h.logger.Error("Invalid period parameter")
		httpresponse.BadRequest(c.Writer, "Invalid period parameter (must be day, week, month, or year)")
		return
	}

	// Get retention statistics
	stats, err := h.analyticsService.GetRetentionStats(c.Request.Context(), tenantID.(uint), period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get retention statistics")
		httpresponse.InternalError(c.Writer, "Failed to get retention statistics")
		return
	}

	httpresponse.OK(c.Writer, stats)
}

// GetDemographicStats retrieves demographic statistics
// @Summary Get demographic statistics
// @Description Retrieve demographic statistics for the current tenant
// @Tags user-analytics
// @Accept json
// @Produce json
// @Success 200 {object} services.DemographicStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-analytics/demographics [get]
func (h *UserAnalyticsHandler) GetDemographicStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get demographic statistics
	stats, err := h.analyticsService.GetDemographicStats(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get demographic statistics")
		httpresponse.InternalError(c.Writer, "Failed to get demographic statistics")
		return
	}

	httpresponse.OK(c.Writer, stats)
}