package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// AddTenantMemberRequest represents input for adding a member to a tenant
type AddTenantMemberRequest struct {
	UserID        uint                              `json:"user_id" validate:"required" example:"1"`
	TenantID      uint                              `json:"tenant_id" validate:"required" example:"1"`
	Role          string                            `json:"role" validate:"required" example:"member"`
	LocalUsername *string                           `json:"local_username,omitempty" validate:"omitempty,min=3,max=30,alphanum" example:"john_local"`
	DisplayName   *string                           `json:"display_name,omitempty" validate:"omitempty,min=1,max=255" example:"<PERSON> (Local)"`
	InvitedBy     *uint                             `json:"invited_by,omitempty" example:"2"`
	Status        models.TenantMembershipStatus     `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended pending" example:"active"`
}

// UpdateMemberRoleRequest represents input for updating a member's role
type UpdateMemberRoleRequest struct {
	Role          string  `json:"role" validate:"required" example:"admin"`
	LocalUsername *string `json:"local_username,omitempty" validate:"omitempty,min=3,max=30,alphanum" example:"john_admin"`
	DisplayName   *string `json:"display_name,omitempty" validate:"omitempty,min=1,max=255" example:"John Doe (Admin)"`
}

// TenantMembershipFilter represents filter for querying tenant memberships
type TenantMembershipFilter struct {
	TenantID       uint                             `json:"tenant_id,omitempty" example:"1"`
	UserID         uint                             `json:"user_id,omitempty" example:"1"`
	Status         models.TenantMembershipStatus    `json:"status,omitempty" example:"active"`
	Role           string                           `json:"role,omitempty" example:"member"`
	InvitedBy      *uint                            `json:"invited_by,omitempty" example:"2"`
	Search         string                           `json:"search,omitempty" example:"john"`
	SortBy         string                           `json:"sort_by,omitempty" example:"created_at"`
	SortOrder      string                           `json:"sort_order,omitempty" example:"desc"`
	IncludeDeleted bool                             `json:"include_deleted,omitempty" example:"false"`
	Pagination     *pagination.CursorPagination     `json:"pagination,omitempty"`
}

// TenantMembershipResponse represents the response for tenant membership operations
// KEEP_OMITEMPTY: Optional personal customizations and nullable relationships
type TenantMembershipResponse struct {
	ID            uint                          `json:"id" example:"1"`                   
	UserID        uint                          `json:"user_id" example:"1"`              
	TenantID      uint                          `json:"tenant_id" example:"1"`            
	Role          string                        `json:"role" example:"member"`            
	LocalUsername *string                       `json:"local_username,omitempty" example:"john_local"` // KEEP_OMITEMPTY: Optional customization
	DisplayName   *string                       `json:"display_name,omitempty" example:"John Doe (Local)"` // KEEP_OMITEMPTY: Optional customization
	IsPrimary     bool                          `json:"is_primary" example:"false"`       
	InvitedBy     *uint                         `json:"invited_by,omitempty" example:"2"`  // KEEP_OMITEMPTY: Optional relationship
	Status        models.TenantMembershipStatus `json:"status" example:"active"`          
	JoinedAt      time.Time                     `json:"joined_at"`                       
	LastActivity  *time.Time                    `json:"last_activity,omitempty"`          // KEEP_OMITEMPTY: Optional activity timestamp
	CreatedAt     time.Time                     `json:"created_at"`                      
	UpdatedAt     time.Time                     `json:"updated_at"`                      
}

// TenantMembershipListResponse represents response for listing tenant memberships
type TenantMembershipListResponse struct {
	Memberships []TenantMembershipResponse   `json:"memberships"` 
	Pagination  *pagination.CursorResponse   `json:"pagination"`  
	Total       int64                        `json:"total" example:"25"`
}

// TenantMembershipStatsResponse represents tenant membership statistics
type TenantMembershipStatsResponse struct {
	TotalMembers       int64                                         `json:"total_members" example:"100"`  
	ActiveMembers      int64                                         `json:"active_members" example:"85"`  
	InactiveMembers    int64                                         `json:"inactive_members" example:"10"`
	SuspendedMembers   int64                                         `json:"suspended_members" example:"3"`
	PendingMembers     int64                                         `json:"pending_members" example:"2"`  
	DeletedMembers     int64                                         `json:"deleted_members" example:"5"`  
	StatusDistribution map[models.TenantMembershipStatus]int64       `json:"status_distribution"`           
	RoleDistribution   map[string]int64                              `json:"role_distribution"`             
	RecentJoins        []TenantMembershipResponse                    `json:"recent_joins"`                  
	RecentActivity     []TenantMembershipResponse                    `json:"recent_activity"`               
}

// BulkMembershipActionRequest represents input for bulk membership actions
type BulkMembershipActionRequest struct {
	MembershipIDs []uint `json:"membership_ids" validate:"required,min=1,max=100"`
	Action        string `json:"action" validate:"required,oneof=activate deactivate suspend delete" example:"activate"`
	Reason        string `json:"reason,omitempty" example:"Bulk activation for new team members"`
}

// MembershipTransferRequest represents input for transferring membership
type MembershipTransferRequest struct {
	FromTenantID uint   `json:"from_tenant_id" validate:"required" example:"1"`
	ToTenantID   uint   `json:"to_tenant_id" validate:"required" example:"2"`
	NewRole      string `json:"new_role,omitempty" example:"member"`
	KeepOldRole  bool   `json:"keep_old_role" example:"false"`
}

// MembershipInviteRequest represents input for inviting a user to membership
type MembershipInviteRequest struct {
	Email       string  `json:"email" validate:"required,email" example:"<EMAIL>"`
	Role        string  `json:"role" validate:"required" example:"member"`
	DisplayName *string `json:"display_name,omitempty" example:"New User"`
	Message     *string `json:"message,omitempty" example:"Welcome to our team!"`
}