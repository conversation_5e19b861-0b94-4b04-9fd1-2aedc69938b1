package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

// UserSocialLinkCreateRequest represents input for creating a social link
type UserSocialLinkCreateRequest struct {
	UserID       uint                   `json:"user_id" validate:"required" example:"1"`
	Platform     models.SocialPlatform  `json:"platform" validate:"required,oneof=twitter linkedin github facebook instagram youtube tiktok snapchat discord twitch reddit pinterest medium dev stackoverflow behance dribbble website other" example:"twitter"`
	Username     string                 `json:"username" validate:"required,min=1,max=255" example:"johndoe"`
	URL          string                 `json:"url" validate:"required,url" example:"https://twitter.com/johndoe"`
	DisplayOrder uint                   `json:"display_order" example:"1"`
	IsPublic     bool                   `json:"is_public" example:"true"`
	ProfileData  map[string]interface{} `json:"profile_data,omitempty"`
}

// UserSocialLinkUpdateRequest represents input for updating a social link
type UserSocialLinkUpdateRequest struct {
	Username     *string                `json:"username,omitempty" validate:"omitempty,min=1,max=255" example:"updated_user"`
	URL          *string                `json:"url,omitempty" validate:"omitempty,url" example:"https://twitter.com/updated_user"`
	DisplayOrder *uint                  `json:"display_order,omitempty" example:"2"`
	IsPublic     *bool                  `json:"is_public,omitempty" example:"false"`
	ProfileData  map[string]interface{} `json:"profile_data,omitempty"`
}

// UserSocialLinkBulkCreateRequest represents input for bulk creating social links
type UserSocialLinkBulkCreateRequest struct {
	UserID uint `json:"user_id" validate:"required" example:"1"`
	Links  []struct {
		Platform     models.SocialPlatform  `json:"platform" validate:"required" example:"linkedin"`
		Username     string                 `json:"username" validate:"required,min=1,max=255" example:"john-doe"`
		URL          string                 `json:"url" validate:"required,url" example:"https://linkedin.com/in/john-doe"`
		DisplayOrder uint                   `json:"display_order" example:"2"`
		IsPublic     bool                   `json:"is_public" example:"true"`
		ProfileData  map[string]interface{} `json:"profile_data,omitempty"`
	} `json:"links" validate:"required,min=1,max=20"`
}

// UserSocialLinkResponse represents the response for social link operations
type UserSocialLinkResponse struct {
	ID           uint                   `json:"id" example:"1"`
	UserID       uint                   `json:"user_id" example:"1"`
	TenantID     uint                   `json:"tenant_id" example:"1"`
	Platform     models.SocialPlatform  `json:"platform" example:"twitter"`
	Username     string                 `json:"username" example:"johndoe"`
	URL          string                 `json:"url" example:"https://twitter.com/johndoe"`
	DisplayOrder uint                   `json:"display_order" example:"1"`
	IsPublic     bool                   `json:"is_public" example:"true"`
	IsVerified   bool                   `json:"is_verified" example:"false"`
	VerifiedAt   *time.Time             `json:"verified_at,omitempty"`
	ProfileData  map[string]interface{} `json:"profile_data,omitempty"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// UserSocialLinkListResponse represents response for listing social links
type UserSocialLinkListResponse struct {
	SocialLinks []UserSocialLinkResponse `json:"social_links"`
	TotalCount  int64                    `json:"total_count" example:"5"`
}

// SocialPlatformStatsResponse represents social platform statistics
type SocialPlatformStatsResponse struct {
	TotalLinks          int64                               `json:"total_links" example:"500"`
	VerifiedLinks       int64                               `json:"verified_links" example:"150"`
	UnverifiedLinks     int64                               `json:"unverified_links" example:"350"`
	PublicLinks         int64                               `json:"public_links" example:"400"`
	PrivateLinks        int64                               `json:"private_links" example:"100"`
	PlatformCounts      map[models.SocialPlatform]int64     `json:"platform_counts"`
	VerificationRates   map[models.SocialPlatform]float64   `json:"verification_rates"`
	PublicRates         map[models.SocialPlatform]float64   `json:"public_rates"`
	UsersWithLinks      int64                               `json:"users_with_links" example:"200"`
	AverageLinksPerUser float64                             `json:"average_links_per_user" example:"2.5"`
}

// VerifySocialLinkRequest represents input for verifying a social link
type VerifySocialLinkRequest struct {
	VerificationCode string `json:"verification_code" validate:"required" example:"ABC123"`
}

// SocialLinkVerificationResponse represents response for social link verification
type SocialLinkVerificationResponse struct {
	IsVerified   bool       `json:"is_verified" example:"true"`
	VerifiedAt   *time.Time `json:"verified_at,omitempty"`
	Instructions string     `json:"instructions,omitempty" example:"Post this code on your profile: ABC123"`
}