package unit

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MockUserInvitationRepository is a mock implementation
type MockUserInvitationRepository struct {
	mock.Mock
}

func (m *MockUserInvitationRepository) CreateInvitation(ctx context.Context, tenantID uint, invitation *models.UserInvitation) (*models.UserInvitation, error) {
	args := m.Called(ctx, tenantID, invitation)
	return args.Get(0).(*models.UserInvitation), args.Error(1)
}

func (m *MockUserInvitationRepository) GetInvitationByID(ctx context.Context, tenantID, invitationID uint) (*models.UserInvitation, error) {
	args := m.Called(ctx, tenantID, invitationID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.UserInvitation), args.Error(1)
}

func (m *MockUserInvitationRepository) GetInvitationByToken(ctx context.Context, token string) (*models.UserInvitation, error) {
	args := m.Called(ctx, token)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.UserInvitation), args.Error(1)
}

func (m *MockUserInvitationRepository) UpdateInvitation(ctx context.Context, tenantID uint, invitation *models.UserInvitation) (*models.UserInvitation, error) {
	args := m.Called(ctx, tenantID, invitation)
	return args.Get(0).(*models.UserInvitation), args.Error(1)
}

func (m *MockUserInvitationRepository) DeleteInvitation(ctx context.Context, tenantID, invitationID uint) error {
	args := m.Called(ctx, tenantID, invitationID)
	return args.Error(0)
}

func (m *MockUserInvitationRepository) ListInvitations(ctx context.Context, tenantID uint, filter models.UserInvitationFilter) ([]*models.UserInvitation, int64, error) {
	args := m.Called(ctx, tenantID, filter)
	return args.Get(0).([]*models.UserInvitation), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserInvitationRepository) GetInvitationsByEmail(ctx context.Context, tenantID uint, email string) ([]*models.UserInvitation, error) {
	args := m.Called(ctx, tenantID, email)
	return args.Get(0).([]*models.UserInvitation), args.Error(1)
}

func (m *MockUserInvitationRepository) GetExpiredInvitations(ctx context.Context, tenantID uint) ([]*models.UserInvitation, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).([]*models.UserInvitation), args.Error(1)
}

func (m *MockUserInvitationRepository) CleanupExpiredInvitations(ctx context.Context, tenantID uint) (int64, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(int64), args.Error(1)
}

// MockNotificationService is a mock implementation
type MockNotificationService struct {
	mock.Mock
}

func (m *MockNotificationService) SendInvitationEmail(ctx context.Context, invitation *models.UserInvitation) error {
	args := m.Called(ctx, invitation)
	return args.Error(0)
}

// UserInvitationServiceTestSuite contains tests for user invitation service
type UserInvitationServiceTestSuite struct {
	suite.Suite
	ctx                 context.Context
	invitationRepo      *MockUserInvitationRepository
	notificationService *MockNotificationService
	logger              *MockLogger
	invitationService   services.UserInvitationService
	tenantID            uint
	inviterID           uint
}

func (suite *UserInvitationServiceTestSuite) SetupTest() {
	suite.ctx = context.Background()
	suite.invitationRepo = new(MockUserInvitationRepository)
	suite.notificationService = new(MockNotificationService)
	suite.logger = new(MockLogger)
	suite.invitationService = services.NewUserInvitationService(
		suite.invitationRepo,
		suite.notificationService,
		suite.logger,
	)
	suite.tenantID = 1
	suite.inviterID = 1
}

func (suite *UserInvitationServiceTestSuite) TearDownTest() {
	suite.invitationRepo.AssertExpectations(suite.T())
	suite.notificationService.AssertExpectations(suite.T())
	suite.logger.AssertExpectations(suite.T())
}

func (suite *UserInvitationServiceTestSuite) TestCreateInvitation_Success() {
	// Arrange
	createReq := &models.UserInvitationCreateRequest{
		TenantID:  suite.tenantID,
		Email:     "<EMAIL>",
		RoleID:    uintPtr(2),
		Message:   stringPtr("Welcome to our team!"),
		ExpiresIn: 72, // 72 hours
	}

	expectedInvitation := &models.UserInvitation{
		ID:        1,
		TenantID:  suite.tenantID,
		Token:     "generated-token-123",
		Email:     createReq.Email,
		RoleID:    createReq.RoleID,
		Message:   createReq.Message,
		Status:    models.UserInvitationStatusPending,
		InvitedBy: suite.inviterID,
		ExpiresAt: time.Now().Add(72 * time.Hour),
		CreatedAt: time.Now(),
	}

	suite.invitationRepo.On("GetInvitationsByEmail", suite.ctx, suite.tenantID, createReq.Email).Return([]*models.UserInvitation{}, nil)
	suite.invitationRepo.On("CreateInvitation", suite.ctx, suite.tenantID, mock.AnythingOfType("*models.UserInvitation")).Return(expectedInvitation, nil)
	suite.notificationService.On("SendInvitationEmail", suite.ctx, expectedInvitation).Return(nil)

	// Act
	result, err := suite.invitationService.CreateInvitation(suite.ctx, suite.tenantID, suite.inviterID, createReq)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedInvitation.Email, result.Email)
	assert.Equal(suite.T(), expectedInvitation.Status, result.Status)
	assert.Equal(suite.T(), suite.inviterID, result.InvitedBy)
}

func (suite *UserInvitationServiceTestSuite) TestCreateInvitation_PendingInvitationExists() {
	// Arrange
	createReq := &models.UserInvitationCreateRequest{
		TenantID: suite.tenantID,
		Email:    "<EMAIL>",
	}

	existingInvitations := []*models.UserInvitation{
		{
			ID:        1,
			Email:     createReq.Email,
			Status:    models.UserInvitationStatusPending,
			ExpiresAt: time.Now().Add(24 * time.Hour),
		},
	}

	suite.invitationRepo.On("GetInvitationsByEmail", suite.ctx, suite.tenantID, createReq.Email).Return(existingInvitations, nil)

	// Act
	result, err := suite.invitationService.CreateInvitation(suite.ctx, suite.tenantID, suite.inviterID, createReq)

	// Assert
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "pending invitation already exists")
}

func (suite *UserInvitationServiceTestSuite) TestAcceptInvitation_Success() {
	// Arrange
	token := "valid-token-123"
	userID := uint(2)
	acceptReq := &models.UserInvitationAcceptRequest{
		Token: token,
	}

	existingInvitation := &models.UserInvitation{
		ID:        1,
		TenantID:  suite.tenantID,
		Token:     token,
		Email:     "<EMAIL>",
		Status:    models.UserInvitationStatusPending,
		InvitedBy: suite.inviterID,
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}

	acceptedInvitation := &models.UserInvitation{
		ID:              1,
		TenantID:        suite.tenantID,
		Token:           token,
		Email:           "<EMAIL>",
		Status:          models.UserInvitationStatusAccepted,
		InvitedBy:       suite.inviterID,
		InvitedUserID:   &userID,
		ExpiresAt:       time.Now().Add(24 * time.Hour),
		AcceptedAt:      timePtr(time.Now()),
	}

	suite.invitationRepo.On("GetInvitationByToken", suite.ctx, token).Return(existingInvitation, nil)
	suite.invitationRepo.On("UpdateInvitation", suite.ctx, suite.tenantID, mock.AnythingOfType("*models.UserInvitation")).Return(acceptedInvitation, nil)

	// Act
	result, err := suite.invitationService.AcceptInvitation(suite.ctx, userID, acceptReq)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), models.UserInvitationStatusAccepted, result.Status)
	assert.Equal(suite.T(), userID, *result.InvitedUserID)
	assert.NotNil(suite.T(), result.AcceptedAt)
}

func (suite *UserInvitationServiceTestSuite) TestAcceptInvitation_ExpiredToken() {
	// Arrange
	token := "expired-token-123"
	userID := uint(2)
	acceptReq := &models.UserInvitationAcceptRequest{
		Token: token,
	}

	expiredInvitation := &models.UserInvitation{
		ID:        1,
		TenantID:  suite.tenantID,
		Token:     token,
		Email:     "<EMAIL>",
		Status:    models.UserInvitationStatusPending,
		InvitedBy: suite.inviterID,
		ExpiresAt: time.Now().Add(-24 * time.Hour), // Expired
	}

	suite.invitationRepo.On("GetInvitationByToken", suite.ctx, token).Return(expiredInvitation, nil)

	// Act
	result, err := suite.invitationService.AcceptInvitation(suite.ctx, userID, acceptReq)

	// Assert
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "expired")
}

func (suite *UserInvitationServiceTestSuite) TestRejectInvitation_Success() {
	// Arrange
	token := "valid-token-123"
	rejectReq := &models.UserInvitationRejectRequest{
		Token: token,
	}

	existingInvitation := &models.UserInvitation{
		ID:        1,
		TenantID:  suite.tenantID,
		Token:     token,
		Email:     "<EMAIL>",
		Status:    models.UserInvitationStatusPending,
		InvitedBy: suite.inviterID,
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}

	rejectedInvitation := &models.UserInvitation{
		ID:         1,
		TenantID:   suite.tenantID,
		Token:      token,
		Email:      "<EMAIL>",
		Status:     models.UserInvitationStatusRejected,
		InvitedBy:  suite.inviterID,
		ExpiresAt:  time.Now().Add(24 * time.Hour),
		RejectedAt: timePtr(time.Now()),
	}

	suite.invitationRepo.On("GetInvitationByToken", suite.ctx, token).Return(existingInvitation, nil)
	suite.invitationRepo.On("UpdateInvitation", suite.ctx, suite.tenantID, mock.AnythingOfType("*models.UserInvitation")).Return(rejectedInvitation, nil)

	// Act
	result, err := suite.invitationService.RejectInvitation(suite.ctx, rejectReq)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), models.UserInvitationStatusRejected, result.Status)
	assert.NotNil(suite.T(), result.RejectedAt)
}

func (suite *UserInvitationServiceTestSuite) TestRevokeInvitation_Success() {
	// Arrange
	invitationID := uint(1)

	existingInvitation := &models.UserInvitation{
		ID:        invitationID,
		TenantID:  suite.tenantID,
		Email:     "<EMAIL>",
		Status:    models.UserInvitationStatusPending,
		InvitedBy: suite.inviterID,
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}

	revokedInvitation := &models.UserInvitation{
		ID:        invitationID,
		TenantID:  suite.tenantID,
		Email:     "<EMAIL>",
		Status:    models.UserInvitationStatusRevoked,
		InvitedBy: suite.inviterID,
		ExpiresAt: time.Now().Add(24 * time.Hour),
		RevokedAt: timePtr(time.Now()),
	}

	suite.invitationRepo.On("GetInvitationByID", suite.ctx, suite.tenantID, invitationID).Return(existingInvitation, nil)
	suite.invitationRepo.On("UpdateInvitation", suite.ctx, suite.tenantID, mock.AnythingOfType("*models.UserInvitation")).Return(revokedInvitation, nil)

	// Act
	result, err := suite.invitationService.RevokeInvitation(suite.ctx, suite.tenantID, invitationID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), models.UserInvitationStatusRevoked, result.Status)
	assert.NotNil(suite.T(), result.RevokedAt)
}

func (suite *UserInvitationServiceTestSuite) TestListInvitations_Success() {
	// Arrange
	filter := models.UserInvitationFilter{
		Status: models.UserInvitationStatusPending,
		Page:   1,
		PageSize: 10,
	}

	expectedInvitations := []*models.UserInvitation{
		{
			ID:        1,
			TenantID:  suite.tenantID,
			Email:     "<EMAIL>",
			Status:    models.UserInvitationStatusPending,
			InvitedBy: suite.inviterID,
		},
		{
			ID:        2,
			TenantID:  suite.tenantID,
			Email:     "<EMAIL>",
			Status:    models.UserInvitationStatusPending,
			InvitedBy: suite.inviterID,
		},
	}
	totalCount := int64(2)

	suite.invitationRepo.On("ListInvitations", suite.ctx, suite.tenantID, filter).Return(expectedInvitations, totalCount, nil)

	// Act
	invitations, total, err := suite.invitationService.ListInvitations(suite.ctx, suite.tenantID, filter)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), len(expectedInvitations), len(invitations))
	assert.Equal(suite.T(), totalCount, total)
	assert.Equal(suite.T(), expectedInvitations[0].Email, invitations[0].Email)
}

func (suite *UserInvitationServiceTestSuite) TestResendInvitation_Success() {
	// Arrange
	invitationID := uint(1)

	existingInvitation := &models.UserInvitation{
		ID:        invitationID,
		TenantID:  suite.tenantID,
		Email:     "<EMAIL>",
		Status:    models.UserInvitationStatusPending,
		InvitedBy: suite.inviterID,
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}

	suite.invitationRepo.On("GetInvitationByID", suite.ctx, suite.tenantID, invitationID).Return(existingInvitation, nil)
	suite.notificationService.On("SendInvitationEmail", suite.ctx, existingInvitation).Return(nil)

	// Act
	err := suite.invitationService.ResendInvitation(suite.ctx, suite.tenantID, invitationID)

	// Assert
	assert.NoError(suite.T(), err)
}

func (suite *UserInvitationServiceTestSuite) TestCleanupExpiredInvitations_Success() {
	// Arrange
	expectedCount := int64(5)
	suite.invitationRepo.On("CleanupExpiredInvitations", suite.ctx, suite.tenantID).Return(expectedCount, nil)

	// Act
	result, err := suite.invitationService.CleanupExpiredInvitations(suite.ctx, suite.tenantID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), expectedCount, result)
}

// Helper functions
func uintPtr(u uint) *uint {
	return &u
}

func timePtr(t time.Time) *time.Time {
	return &t
}

// Run the test suite
func TestUserInvitationServiceTestSuite(t *testing.T) {
	suite.Run(t, new(UserInvitationServiceTestSuite))
}