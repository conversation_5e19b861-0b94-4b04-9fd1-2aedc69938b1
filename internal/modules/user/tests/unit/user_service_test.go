package unit

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MockUserRepository is a mock implementation of user repository
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) CreateUser(ctx context.Context, tenantID uint, user *models.User) (*models.User, error) {
	args := m.Called(ctx, tenantID, user)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByID(ctx context.Context, tenantID, userID uint) (*models.User, error) {
	args := m.Called(ctx, tenantID, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByEmail(ctx context.Context, tenantID uint, email string) (*models.User, error) {
	args := m.Called(ctx, tenantID, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) UpdateUser(ctx context.Context, tenantID uint, user *models.User) (*models.User, error) {
	args := m.Called(ctx, tenantID, user)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) DeleteUser(ctx context.Context, tenantID, userID uint) error {
	args := m.Called(ctx, tenantID, userID)
	return args.Error(0)
}

func (m *MockUserRepository) ListUsers(ctx context.Context, tenantID uint, filter models.UserFilter) ([]*models.User, int64, error) {
	args := m.Called(ctx, tenantID, filter)
	return args.Get(0).([]*models.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) ExistsUserByEmail(ctx context.Context, tenantID uint, email string, excludeUserID *uint) (bool, error) {
	args := m.Called(ctx, tenantID, email, excludeUserID)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) UpdateUserStatus(ctx context.Context, tenantID, userID uint, status models.UserStatus) error {
	args := m.Called(ctx, tenantID, userID, status)
	return args.Error(0)
}

func (m *MockUserRepository) GetUsersByStatus(ctx context.Context, tenantID uint, status models.UserStatus) ([]*models.User, error) {
	args := m.Called(ctx, tenantID, status)
	return args.Get(0).([]*models.User), args.Error(1)
}

func (m *MockUserRepository) SearchUsers(ctx context.Context, tenantID uint, query string, limit int) ([]*models.User, error) {
	args := m.Called(ctx, tenantID, query, limit)
	return args.Get(0).([]*models.User), args.Error(1)
}

// MockLogger implements utils.Logger
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Debug(msg string, keysAndValues ...interface{}) {
	m.Called(msg, keysAndValues)
}

func (m *MockLogger) Info(msg string, keysAndValues ...interface{}) {
	m.Called(msg, keysAndValues)
}

func (m *MockLogger) Warn(msg string, keysAndValues ...interface{}) {
	m.Called(msg, keysAndValues)
}

func (m *MockLogger) Error(msg string, keysAndValues ...interface{}) {
	m.Called(msg, keysAndValues)
}

func (m *MockLogger) Fatal(msg string, keysAndValues ...interface{}) {
	m.Called(msg, keysAndValues)
}

func (m *MockLogger) With(keysAndValues ...interface{}) utils.Logger {
	args := m.Called(keysAndValues)
	return args.Get(0).(utils.Logger)
}

// UserServiceTestSuite contains tests for user service
type UserServiceTestSuite struct {
	suite.Suite
	ctx        context.Context
	userRepo   *MockUserRepository
	logger     *MockLogger
	userService services.UserService
	tenantID   uint
}

func (suite *UserServiceTestSuite) SetupTest() {
	suite.ctx = context.Background()
	suite.userRepo = new(MockUserRepository)
	suite.logger = new(MockLogger)
	suite.userService = services.NewUserService(suite.userRepo, suite.logger)
	suite.tenantID = 1
}

func (suite *UserServiceTestSuite) TearDownTest() {
	suite.userRepo.AssertExpectations(suite.T())
	suite.logger.AssertExpectations(suite.T())
}

func (suite *UserServiceTestSuite) TestCreateUser_Success() {
	// Arrange
	createReq := &models.UserCreateRequest{
		Email:     "<EMAIL>",
		FirstName: "John",
		LastName:  "Doe",
		Status:    models.UserStatusActive,
	}

	expectedUser := &models.User{
		ID:        1,
		Email:     createReq.Email,
		FirstName: createReq.FirstName,
		LastName:  createReq.LastName,
		Status:    createReq.Status,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	suite.userRepo.On("ExistsUserByEmail", suite.ctx, suite.tenantID, createReq.Email, (*uint)(nil)).Return(false, nil)
	suite.userRepo.On("CreateUser", suite.ctx, suite.tenantID, mock.AnythingOfType("*models.User")).Return(expectedUser, nil)

	// Act
	result, err := suite.userService.CreateUser(suite.ctx, suite.tenantID, createReq)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedUser.Email, result.Email)
	assert.Equal(suite.T(), expectedUser.FirstName, result.FirstName)
	assert.Equal(suite.T(), expectedUser.LastName, result.LastName)
}

func (suite *UserServiceTestSuite) TestCreateUser_EmailAlreadyExists() {
	// Arrange
	createReq := &models.UserCreateRequest{
		Email:     "<EMAIL>",
		FirstName: "John",
		LastName:  "Doe",
		Status:    models.UserStatusActive,
	}

	suite.userRepo.On("ExistsUserByEmail", suite.ctx, suite.tenantID, createReq.Email, (*uint)(nil)).Return(true, nil)

	// Act
	result, err := suite.userService.CreateUser(suite.ctx, suite.tenantID, createReq)

	// Assert
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "email already exists")
}

func (suite *UserServiceTestSuite) TestGetUserByID_Success() {
	// Arrange
	userID := uint(1)
	expectedUser := &models.User{
		ID:        userID,
		Email:     "<EMAIL>",
		FirstName: "John",
		LastName:  "Doe",
		Status:    models.UserStatusActive,
	}

	suite.userRepo.On("GetUserByID", suite.ctx, suite.tenantID, userID).Return(expectedUser, nil)

	// Act
	result, err := suite.userService.GetUserByID(suite.ctx, suite.tenantID, userID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), expectedUser.ID, result.ID)
	assert.Equal(suite.T(), expectedUser.Email, result.Email)
}

func (suite *UserServiceTestSuite) TestGetUserByID_NotFound() {
	// Arrange
	userID := uint(999)
	suite.userRepo.On("GetUserByID", suite.ctx, suite.tenantID, userID).Return((*models.User)(nil), errors.New("user not found"))

	// Act
	result, err := suite.userService.GetUserByID(suite.ctx, suite.tenantID, userID)

	// Assert
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "user not found")
}

func (suite *UserServiceTestSuite) TestUpdateUser_Success() {
	// Arrange
	userID := uint(1)
	updateReq := &models.UserUpdateRequest{
		FirstName: "Jane",
		LastName:  "Smith",
		Status:    models.UserStatusInactive,
	}

	existingUser := &models.User{
		ID:        userID,
		Email:     "<EMAIL>",
		FirstName: "John",
		LastName:  "Doe",
		Status:    models.UserStatusActive,
	}

	updatedUser := &models.User{
		ID:        userID,
		Email:     "<EMAIL>",
		FirstName: updateReq.FirstName,
		LastName:  updateReq.LastName,
		Status:    updateReq.Status,
		UpdatedAt: time.Now(),
	}

	suite.userRepo.On("GetUserByID", suite.ctx, suite.tenantID, userID).Return(existingUser, nil)
	suite.userRepo.On("UpdateUser", suite.ctx, suite.tenantID, mock.AnythingOfType("*models.User")).Return(updatedUser, nil)

	// Act
	result, err := suite.userService.UpdateUser(suite.ctx, suite.tenantID, userID, updateReq)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), updateReq.FirstName, result.FirstName)
	assert.Equal(suite.T(), updateReq.LastName, result.LastName)
	assert.Equal(suite.T(), updateReq.Status, result.Status)
}

func (suite *UserServiceTestSuite) TestDeleteUser_Success() {
	// Arrange
	userID := uint(1)
	suite.userRepo.On("DeleteUser", suite.ctx, suite.tenantID, userID).Return(nil)

	// Act
	err := suite.userService.DeleteUser(suite.ctx, suite.tenantID, userID)

	// Assert
	assert.NoError(suite.T(), err)
}

func (suite *UserServiceTestSuite) TestListUsers_Success() {
	// Arrange
	filter := models.UserFilter{
		Status: models.UserStatusActive,
		Page:   1,
		Limit:  10,
	}

	expectedUsers := []*models.User{
		{
			ID:        1,
			Email:     "<EMAIL>",
			FirstName: "User",
			LastName:  "One",
			Status:    models.UserStatusActive,
		},
		{
			ID:        2,
			Email:     "<EMAIL>",
			FirstName: "User",
			LastName:  "Two",
			Status:    models.UserStatusActive,
		},
	}
	totalCount := int64(2)

	suite.userRepo.On("ListUsers", suite.ctx, suite.tenantID, filter).Return(expectedUsers, totalCount, nil)

	// Act
	users, total, err := suite.userService.ListUsers(suite.ctx, suite.tenantID, filter)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), len(expectedUsers), len(users))
	assert.Equal(suite.T(), totalCount, total)
	assert.Equal(suite.T(), expectedUsers[0].Email, users[0].Email)
}

func (suite *UserServiceTestSuite) TestUpdateUserStatus_Success() {
	// Arrange
	userID := uint(1)
	newStatus := models.UserStatusSuspended

	suite.userRepo.On("UpdateUserStatus", suite.ctx, suite.tenantID, userID, newStatus).Return(nil)

	// Act
	err := suite.userService.UpdateUserStatus(suite.ctx, suite.tenantID, userID, newStatus)

	// Assert
	assert.NoError(suite.T(), err)
}

func (suite *UserServiceTestSuite) TestSearchUsers_Success() {
	// Arrange
	query := "john"
	limit := 10

	expectedUsers := []*models.User{
		{
			ID:        1,
			Email:     "<EMAIL>",
			FirstName: "John",
			LastName:  "Doe",
			Status:    models.UserStatusActive,
		},
	}

	suite.userRepo.On("SearchUsers", suite.ctx, suite.tenantID, query, limit).Return(expectedUsers, nil)

	// Act
	result, err := suite.userService.SearchUsers(suite.ctx, suite.tenantID, query, limit)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), len(expectedUsers), len(result))
	assert.Equal(suite.T(), expectedUsers[0].FirstName, result[0].FirstName)
}

func (suite *UserServiceTestSuite) TestGetUsersByStatus_Success() {
	// Arrange
	status := models.UserStatusPendingVerification

	expectedUsers := []*models.User{
		{
			ID:     1,
			Email:  "<EMAIL>",
			Status: status,
		},
	}

	suite.userRepo.On("GetUsersByStatus", suite.ctx, suite.tenantID, status).Return(expectedUsers, nil)

	// Act
	result, err := suite.userService.GetUsersByStatus(suite.ctx, suite.tenantID, status)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), len(expectedUsers), len(result))
	assert.Equal(suite.T(), status, result[0].Status)
}

// Run the test suite
func TestUserServiceTestSuite(t *testing.T) {
	suite.Run(t, new(UserServiceTestSuite))
}