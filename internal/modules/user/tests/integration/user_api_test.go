package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

// UserAPITestSuite contains integration tests for user API endpoints
type UserAPITestSuite struct {
	suite.Suite
	router   *gin.Engine
	tenantID uint
	authToken string
}

func (suite *UserAPITestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()
	suite.tenantID = 1
	suite.authToken = "test-jwt-token"
	
	// Setup test routes here
	// This would typically involve setting up the actual user handlers
	// For now, we'll mock the responses
	suite.setupMockRoutes()
}

func (suite *UserAPITestSuite) setupMockRoutes() {
	api := suite.router.Group("/api/cms/v1")
	
	// Mock user routes
	users := api.Group("/users")
	{
		users.GET("", suite.mockListUsers)
		users.POST("", suite.mockCreateUser)
		users.GET("/:id", suite.mockGetUser)
		users.PUT("/:id", suite.mockUpdateUser)
		users.DELETE("/:id", suite.mockDeleteUser)
		users.GET("/:id/profile", suite.mockGetUserProfile)
		users.POST("/:id/profile", suite.mockCreateUserProfile)
		users.PUT("/:id/profile", suite.mockUpdateUserProfile)
		users.GET("/:id/social-links", suite.mockGetSocialLinks)
		users.POST("/:id/social-links", suite.mockCreateSocialLink)
	}
}

func (suite *UserAPITestSuite) mockListUsers(c *gin.Context) {
	users := []models.UserResponse{
		{
			ID:        1,
			Email:     "<EMAIL>",
			FirstName: "John",
			LastName:  "Doe",
			Status:    models.UserStatusActive,
		},
		{
			ID:        2,
			Email:     "<EMAIL>",
			FirstName: "Jane",
			LastName:  "Smith",
			Status:    models.UserStatusActive,
		},
	}
	
	response := map[string]interface{}{
		"status": "success",
		"data":   users,
		"meta": map[string]interface{}{
			"total": 2,
			"page":  1,
			"limit": 10,
		},
	}
	
	c.JSON(http.StatusOK, response)
}

func (suite *UserAPITestSuite) mockCreateUser(c *gin.Context) {
	var req models.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"status": "error",
			"message": "Invalid request",
		})
		return
	}
	
	user := models.UserResponse{
		ID:        3,
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Status:    req.Status,
	}
	
	c.JSON(http.StatusCreated, map[string]interface{}{
		"status": "success",
		"data":   user,
	})
}

func (suite *UserAPITestSuite) mockGetUser(c *gin.Context) {
	userID := c.Param("id")
	
	if userID == "999" {
		c.JSON(http.StatusNotFound, map[string]interface{}{
			"status": "error",
			"message": "User not found",
		})
		return
	}
	
	user := models.UserResponse{
		ID:        1,
		Email:     "<EMAIL>",
		FirstName: "John",
		LastName:  "Doe",
		Status:    models.UserStatusActive,
	}
	
	c.JSON(http.StatusOK, map[string]interface{}{
		"status": "success",
		"data":   user,
	})
}

func (suite *UserAPITestSuite) mockUpdateUser(c *gin.Context) {
	var req models.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"status": "error",
			"message": "Invalid request",
		})
		return
	}
	
	user := models.UserResponse{
		ID:        1,
		Email:     "<EMAIL>",
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Status:    req.Status,
	}
	
	c.JSON(http.StatusOK, map[string]interface{}{
		"status": "success",
		"data":   user,
	})
}

func (suite *UserAPITestSuite) mockDeleteUser(c *gin.Context) {
	c.JSON(http.StatusOK, map[string]interface{}{
		"status": "success",
		"message": "User deleted successfully",
	})
}

func (suite *UserAPITestSuite) mockGetUserProfile(c *gin.Context) {
	profile := models.UserProfileResponse{
		ID:                   1,
		UserID:               1,
		Bio:                  stringPtr("Software developer"),
		Title:                stringPtr("Senior Developer"),
		CompletionPercentage: 75,
		ProfileCompleted:     false,
	}
	
	c.JSON(http.StatusOK, map[string]interface{}{
		"status": "success",
		"data":   profile,
	})
}

func (suite *UserAPITestSuite) mockCreateUserProfile(c *gin.Context) {
	var req models.UserProfileCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"status": "error",
			"message": "Invalid request",
		})
		return
	}
	
	profile := models.UserProfileResponse{
		ID:                   1,
		UserID:               req.UserID,
		Bio:                  req.Bio,
		Title:                req.Title,
		CompletionPercentage: 80,
		ProfileCompleted:     true,
	}
	
	c.JSON(http.StatusCreated, map[string]interface{}{
		"status": "success",
		"data":   profile,
	})
}

func (suite *UserAPITestSuite) mockUpdateUserProfile(c *gin.Context) {
	var req models.UserProfileUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"status": "error",
			"message": "Invalid request",
		})
		return
	}
	
	profile := models.UserProfileResponse{
		ID:                   1,
		UserID:               1,
		Bio:                  req.Bio,
		Title:                req.Title,
		CompletionPercentage: 85,
		ProfileCompleted:     true,
	}
	
	c.JSON(http.StatusOK, map[string]interface{}{
		"status": "success",
		"data":   profile,
	})
}

func (suite *UserAPITestSuite) mockGetSocialLinks(c *gin.Context) {
	links := []models.UserSocialLinkResponse{
		{
			ID:       1,
			UserID:   1,
			Platform: models.PlatformGitHub,
			Username: "johndoe",
			URL:      "https://github.com/johndoe",
			IsPublic: true,
		},
	}
	
	c.JSON(http.StatusOK, map[string]interface{}{
		"status": "success",
		"data":   links,
	})
}

func (suite *UserAPITestSuite) mockCreateSocialLink(c *gin.Context) {
	var req models.UserSocialLinkCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"status": "error",
			"message": "Invalid request",
		})
		return
	}
	
	link := models.UserSocialLinkResponse{
		ID:       1,
		UserID:   req.UserID,
		Platform: req.Platform,
		Username: req.Username,
		URL:      req.URL,
		IsPublic: req.IsPublic,
	}
	
	c.JSON(http.StatusCreated, map[string]interface{}{
		"status": "success",
		"data":   link,
	})
}

func (suite *UserAPITestSuite) TestListUsers_Success() {
	// Arrange
	req, _ := http.NewRequest("GET", "/api/cms/v1/users", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	req.Header.Set("X-Tenant-ID", fmt.Sprintf("%d", suite.tenantID))
	
	w := httptest.NewRecorder()
	
	// Act
	suite.router.ServeHTTP(w, req)
	
	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "success", response["status"])
	
	data, ok := response["data"].([]interface{})
	assert.True(suite.T(), ok)
	assert.Len(suite.T(), data, 2)
}

func (suite *UserAPITestSuite) TestCreateUser_Success() {
	// Arrange
	createReq := models.UserCreateRequest{
		Email:     "<EMAIL>",
		FirstName: "New",
		LastName:  "User",
		Status:    models.UserStatusActive,
	}
	
	body, _ := json.Marshal(createReq)
	req, _ := http.NewRequest("POST", "/api/cms/v1/users", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	req.Header.Set("X-Tenant-ID", fmt.Sprintf("%d", suite.tenantID))
	
	w := httptest.NewRecorder()
	
	// Act
	suite.router.ServeHTTP(w, req)
	
	// Assert
	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "success", response["status"])
	
	data, ok := response["data"].(map[string]interface{})
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), createReq.Email, data["email"])
}

func (suite *UserAPITestSuite) TestCreateUser_InvalidRequest() {
	// Arrange
	invalidReq := map[string]interface{}{
		"email": "invalid-email",
		// Missing required fields
	}
	
	body, _ := json.Marshal(invalidReq)
	req, _ := http.NewRequest("POST", "/api/cms/v1/users", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	req.Header.Set("X-Tenant-ID", fmt.Sprintf("%d", suite.tenantID))
	
	w := httptest.NewRecorder()
	
	// Act
	suite.router.ServeHTTP(w, req)
	
	// Assert
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

func (suite *UserAPITestSuite) TestGetUser_Success() {
	// Arrange
	req, _ := http.NewRequest("GET", "/api/cms/v1/users/1", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	req.Header.Set("X-Tenant-ID", fmt.Sprintf("%d", suite.tenantID))
	
	w := httptest.NewRecorder()
	
	// Act
	suite.router.ServeHTTP(w, req)
	
	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "success", response["status"])
}

func (suite *UserAPITestSuite) TestGetUser_NotFound() {
	// Arrange
	req, _ := http.NewRequest("GET", "/api/cms/v1/users/999", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	req.Header.Set("X-Tenant-ID", fmt.Sprintf("%d", suite.tenantID))
	
	w := httptest.NewRecorder()
	
	// Act
	suite.router.ServeHTTP(w, req)
	
	// Assert
	assert.Equal(suite.T(), http.StatusNotFound, w.Code)
}

func (suite *UserAPITestSuite) TestUpdateUser_Success() {
	// Arrange
	updateReq := models.UserUpdateRequest{
		FirstName: "Updated",
		LastName:  "Name",
		Status:    models.UserStatusInactive,
	}
	
	body, _ := json.Marshal(updateReq)
	req, _ := http.NewRequest("PUT", "/api/cms/v1/users/1", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	req.Header.Set("X-Tenant-ID", fmt.Sprintf("%d", suite.tenantID))
	
	w := httptest.NewRecorder()
	
	// Act
	suite.router.ServeHTTP(w, req)
	
	// Assert
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "success", response["status"])
}

func (suite *UserAPITestSuite) TestCreateUserProfile_Success() {
	// Arrange
	createReq := models.UserProfileCreateRequest{
		UserID:  1,
		Bio:     stringPtr("Test bio"),
		Title:   stringPtr("Test title"),
		Skills:  []string{"Go", "React"},
	}
	
	body, _ := json.Marshal(createReq)
	req, _ := http.NewRequest("POST", "/api/cms/v1/users/1/profile", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	req.Header.Set("X-Tenant-ID", fmt.Sprintf("%d", suite.tenantID))
	
	w := httptest.NewRecorder()
	
	// Act
	suite.router.ServeHTTP(w, req)
	
	// Assert
	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "success", response["status"])
}

func (suite *UserAPITestSuite) TestCreateSocialLink_Success() {
	// Arrange
	createReq := models.UserSocialLinkCreateRequest{
		UserID:   1,
		Platform: models.PlatformGitHub,
		Username: "testuser",
		URL:      "https://github.com/testuser",
		IsPublic: true,
	}
	
	body, _ := json.Marshal(createReq)
	req, _ := http.NewRequest("POST", "/api/cms/v1/users/1/social-links", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	req.Header.Set("X-Tenant-ID", fmt.Sprintf("%d", suite.tenantID))
	
	w := httptest.NewRecorder()
	
	// Act
	suite.router.ServeHTTP(w, req)
	
	// Assert
	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "success", response["status"])
}

// Helper function
func stringPtr(s string) *string {
	return &s
}

// Run the test suite
func TestUserAPITestSuite(t *testing.T) {
	suite.Run(t, new(UserAPITestSuite))
}