package services

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"gorm.io/gorm"

	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	authServices "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userVerificationService implements UserVerificationService interface
type userVerificationService struct {
	userRepo                repositories.UserRepository
	emailVerificationService authServices.EmailVerificationService
	notificationService     notificationServices.NotificationService
	logger                  utils.Logger
	// Configuration
	maxPhoneResends     uint
	phoneResendInterval time.Duration
	phoneCodeExpiry     time.Duration
}

// NewUserVerificationService creates a new user verification service
func NewUserVerificationService(
	userRepo repositories.UserRepository,
	emailVerificationService authServices.EmailVerificationService,
	notificationService notificationServices.NotificationService,
	logger utils.Logger,
) UserVerificationService {
	return &userVerificationService{
		userRepo:                userRepo,
		emailVerificationService: emailVerificationService,
		notificationService:     notificationService,
		logger:                  logger,
		// Default configuration
		maxPhoneResends:     3,
		phoneResendInterval: 5 * time.Minute,
		phoneCodeExpiry:     15 * time.Minute,
	}
}

// SendEmailVerification sends email verification using the auth service
func (s *userVerificationService) SendEmailVerification(ctx context.Context, userID uint) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if email is already verified
	if user.EmailVerified {
		return fmt.Errorf("email already verified")
	}

	// Create verification token request
	req := &authModels.CreateEmailVerificationTokenRequest{
		UserID: userID,
		Email:  user.Email,
	}

	// Get tenant ID from context or use default approach
	// Since users are global, we need to get tenant from context
	tenantID := s.getTenantIDFromContext(ctx, userID)

	// Use the email verification service to create and send token
	token, err := s.emailVerificationService.CreateVerificationToken(ctx, tenantID, req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create email verification token", "user_id", userID)
		return fmt.Errorf("failed to create email verification token: %w", err)
	}

	s.logger.Info("Email verification sent successfully", "user_id", userID, "email", user.Email, "token_id", token.ID)
	return nil
}

// VerifyEmail verifies email with token using the auth service
func (s *userVerificationService) VerifyEmail(ctx context.Context, userID uint, token string) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if email is already verified
	if user.EmailVerified {
		return fmt.Errorf("email already verified")
	}

	// Create verification request
	req := &authModels.VerifyEmailRequest{
		Token: token,
	}

	// Use the email verification service to verify token
	result, err := s.emailVerificationService.VerifyEmail(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to verify email token", "user_id", userID)
		return fmt.Errorf("failed to verify email token: %w", err)
	}

	if !result.Success {
		return fmt.Errorf("email verification failed: %s", result.Message)
	}

	// Update user email verification status
	if err := s.userRepo.UpdateEmailVerification(ctx, userID, true); err != nil {
		s.logger.WithError(err).Error("Failed to update email verification status")
		return fmt.Errorf("failed to update email verification status: %w", err)
	}

	s.logger.Info("Email verification completed successfully", "user_id", userID, "email", result.Email)
	return nil
}

// SendPhoneVerification sends phone verification with rate limiting
func (s *userVerificationService) SendPhoneVerification(ctx context.Context, userID uint) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if user has phone number
	if user.Phone == nil || *user.Phone == "" {
		return fmt.Errorf("user has no phone number")
	}

	// Check if phone is already verified
	if user.PhoneVerified {
		return fmt.Errorf("phone already verified")
	}

	// Check rate limiting for phone verification
	if err := s.checkPhoneRateLimit(ctx, userID); err != nil {
		return err
	}

	// Generate verification code
	code := s.generateVerificationCode()

	// Store verification code with expiration
	if err := s.storePhoneVerificationCode(ctx, userID, *user.Phone, code); err != nil {
		s.logger.WithError(err).Error("Failed to store phone verification code", "user_id", userID)
		return fmt.Errorf("failed to store phone verification code: %w", err)
	}

	// Get tenant ID from context or use default approach
	tenantID := s.getTenantIDFromContext(ctx, userID)

	// Send SMS via notification service
	if err := s.sendPhoneVerificationSMS(ctx, tenantID, *user.Phone, code); err != nil {
		s.logger.WithError(err).Error("Failed to send phone verification SMS", "user_id", userID)
		return fmt.Errorf("failed to send phone verification SMS: %w", err)
	}

	s.logger.Info("Phone verification sent successfully", "user_id", userID, "phone", *user.Phone)
	return nil
}

// VerifyPhone verifies phone with code
func (s *userVerificationService) VerifyPhone(ctx context.Context, userID uint, code string) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if phone is already verified
	if user.PhoneVerified {
		return fmt.Errorf("phone already verified")
	}

	// Validate code against stored code
	if err := s.validateStoredPhoneVerificationCode(ctx, userID, code); err != nil {
		s.logger.WithError(err).Error("Invalid phone verification code", "user_id", userID)
		return fmt.Errorf("invalid verification code: %w", err)
	}

	// Update user phone verification status
	if err := s.userRepo.UpdatePhoneVerification(ctx, userID, true); err != nil {
		s.logger.WithError(err).Error("Failed to update phone verification status")
		return fmt.Errorf("failed to update phone verification status: %w", err)
	}

	// Clear verification code from storage
	if err := s.clearPhoneVerificationCode(ctx, userID); err != nil {
		s.logger.WithError(err).Warn("Failed to clear phone verification code", "user_id", userID)
	}

	s.logger.Info("Phone verification completed successfully", "user_id", userID)
	return nil
}

// ResendVerification resends verification with rate limiting
func (s *userVerificationService) ResendVerification(ctx context.Context, userID uint, verificationType string) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	switch verificationType {
	case "email":
		if user.EmailVerified {
			return fmt.Errorf("email already verified")
		}

		// Create resend request
		req := &authModels.ResendVerificationEmailRequest{
			Email: user.Email,
		}

		// Get tenant ID from context or use default approach
		tenantID := s.getTenantIDFromContext(ctx, userID)

		// Use the email verification service to resend
		token, err := s.emailVerificationService.ResendVerificationEmail(ctx, tenantID, req)
		if err != nil {
			s.logger.WithError(err).Error("Failed to resend email verification", "user_id", userID)
			return fmt.Errorf("failed to resend email verification: %w", err)
		}

		s.logger.Info("Email verification resent successfully", "user_id", userID, "email", user.Email, "token_id", token.ID)
		return nil

	case "phone":
		return s.SendPhoneVerification(ctx, userID)
	default:
		return fmt.Errorf("invalid verification type: %s", verificationType)
	}
}

// CheckVerificationStatus checks verification status with token information
func (s *userVerificationService) CheckVerificationStatus(ctx context.Context, userID uint) (*VerificationStatus, error) {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	status := &VerificationStatus{
		UserID:        userID,
		EmailVerified: user.EmailVerified,
		PhoneVerified: user.PhoneVerified,
	}

	if user.EmailVerifiedAt != nil {
		status.EmailVerifiedAt = user.EmailVerifiedAt
	}

	if user.PhoneVerifiedAt != nil {
		status.PhoneVerifiedAt = user.PhoneVerifiedAt
	}

	// Get last email verification token sent time
	if emailToken, err := s.emailVerificationService.GetActiveTokenForUser(ctx, userID); err == nil && emailToken != nil {
		status.LastEmailSent = &emailToken.CreatedAt
	}

	// Get last phone verification code sent time
	if phoneCodeSent, err := s.getLastPhoneVerificationSentTime(ctx, userID); err == nil && phoneCodeSent != nil {
		status.LastPhoneSent = phoneCodeSent
	}

	return status, nil
}

// Helper methods

// generateVerificationCode generates a 6-digit verification code
func (s *userVerificationService) generateVerificationCode() string {
	// Generate 6-digit code
	code := rand.Intn(900000) + 100000
	return strconv.Itoa(code)
}

// checkPhoneRateLimit checks if user can send another phone verification
func (s *userVerificationService) checkPhoneRateLimit(ctx context.Context, userID uint) error {
	// Get recent phone verification attempts
	recentAttempts, err := s.getRecentPhoneVerificationAttempts(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to check rate limit: %w", err)
	}

	// Check if exceeded max attempts
	if uint(len(recentAttempts)) >= s.maxPhoneResends {
		return fmt.Errorf("maximum phone verification attempts exceeded")
	}

	// Check if last attempt was too recent
	if len(recentAttempts) > 0 {
		lastAttempt := recentAttempts[0]
		if time.Since(lastAttempt) < s.phoneResendInterval {
			return fmt.Errorf("please wait before requesting another verification code")
		}
	}

	return nil
}

// storePhoneVerificationCode stores a phone verification code
func (s *userVerificationService) storePhoneVerificationCode(ctx context.Context, userID uint, phone, code string) error {
	// For now, we'll use a simple in-memory storage approach
	// In production, this should use Redis or database with TTL
	s.logger.Info("Storing phone verification code", "user_id", userID, "phone", phone, "code", code)

	// TODO: Implement proper storage with expiration
	// This should store in Redis with TTL or database with expiration timestamp
	return nil
}

// validateStoredPhoneVerificationCode validates a phone verification code
func (s *userVerificationService) validateStoredPhoneVerificationCode(ctx context.Context, userID uint, code string) error {
	// Basic validation
	if len(code) != 6 {
		return fmt.Errorf("code must be 6 digits")
	}

	// TODO: Implement proper validation against stored code
	// This should check against stored code with expiration
	s.logger.Info("Validating phone verification code", "user_id", userID, "code", code)

	// For now, we'll just validate format
	if _, err := strconv.Atoi(code); err != nil {
		return fmt.Errorf("code must be numeric")
	}

	return nil
}

// clearPhoneVerificationCode removes a phone verification code from storage
func (s *userVerificationService) clearPhoneVerificationCode(ctx context.Context, userID uint) error {
	// TODO: Implement proper code removal from storage
	s.logger.Info("Clearing phone verification code", "user_id", userID)
	return nil
}

// sendPhoneVerificationSMS sends SMS via notification service
func (s *userVerificationService) sendPhoneVerificationSMS(ctx context.Context, tenantID uint, phone, code string) error {
	// TODO: Implement SMS sending via notification service
	// This should use a phone verification template
	s.logger.Info("Sending phone verification SMS", "tenant_id", tenantID, "phone", phone, "code", code)

	// For now, we'll just log the code
	// In production, this should integrate with SMS provider
	return nil
}

// getRecentPhoneVerificationAttempts gets recent phone verification attempts
func (s *userVerificationService) getRecentPhoneVerificationAttempts(ctx context.Context, userID uint) ([]time.Time, error) {
	// TODO: Implement proper tracking of phone verification attempts
	// This should return timestamps of recent attempts within the rate limit window
	return []time.Time{}, nil
}

// getLastPhoneVerificationSentTime gets the last time a phone verification was sent
func (s *userVerificationService) getLastPhoneVerificationSentTime(ctx context.Context, userID uint) (*time.Time, error) {
	// TODO: Implement proper tracking of phone verification sent times
	// This should return the last time a phone verification was sent
	return nil, nil
}

// getTenantIDFromContext gets tenant ID from context or user memberships
func (s *userVerificationService) getTenantIDFromContext(ctx context.Context, userID uint) uint {
	// Try to get tenant ID from context first
	if tenantID, ok := ctx.Value("tenant_id").(uint); ok {
		return tenantID
	}

	// Fallback: Get user's primary tenant membership
	// This would need to be implemented with proper tenant membership lookup
	// For now, we'll use a default tenant ID of 1
	s.logger.Warn("No tenant ID found in context, using default", "user_id", userID)
	return 1
}