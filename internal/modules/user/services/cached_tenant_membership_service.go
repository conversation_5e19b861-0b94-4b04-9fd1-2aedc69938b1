package services

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/cache"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// CachedTenantMembershipService wraps the repository with caching functionality
type CachedTenantMembershipService struct {
	repo   repositories.TenantMembershipRepository
	cache  cache.TenantMembershipCache
	logger utils.Logger
}

// NewCachedTenantMembershipService creates a new cached tenant membership service
func NewCachedTenantMembershipService(
	repo repositories.TenantMembershipRepository,
	cache cache.TenantMembershipCache,
	logger utils.Logger,
) *CachedTenantMembershipService {
	return &CachedTenantMembershipService{
		repo:   repo,
		cache:  cache,
		logger: logger,
	}
}

// GetByUserAndTenant retrieves a tenant membership with caching
func (s *CachedTenantMembershipService) GetByUserAndTenant(ctx context.Context, userID, tenantID uint) (*models.TenantMembership, error) {
	// Try cache first
	membership, err := s.cache.GetMembership(ctx, userID, tenantID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get membership from cache")
	} else if membership != nil {
		return membership, nil
	}

	// Cache miss - fetch from database
	membership, err = s.repo.GetByUserAndTenant(ctx, userID, tenantID)
	if err != nil {
		return nil, err
	}

	// Cache the result
	if err := s.cache.SetMembership(ctx, userID, tenantID, membership); err != nil {
		s.logger.WithError(err).Warn("Failed to cache membership")
	}

	return membership, nil
}

// GetUserRole retrieves user role with caching
func (s *CachedTenantMembershipService) GetUserRole(ctx context.Context, userID, tenantID uint) (string, error) {
	// Try cache first
	role, err := s.cache.GetUserRole(ctx, userID, tenantID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get role from cache")
	} else if role != "" {
		return role, nil
	}

	// Cache miss - fetch from database
	role, err = s.repo.GetUserRole(ctx, userID, tenantID)
	if err != nil {
		return "", err
	}

	// Cache the result
	if err := s.cache.SetUserRole(ctx, userID, tenantID, role); err != nil {
		s.logger.WithError(err).Warn("Failed to cache role")
	}

	return role, nil
}

// IsUserActiveInTenant checks if user is active with caching
func (s *CachedTenantMembershipService) IsUserActiveInTenant(ctx context.Context, userID, tenantID uint) (bool, error) {
	// Try cache first
	isActive, err := s.cache.GetUserActiveStatus(ctx, userID, tenantID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get active status from cache")
	} else {
		// Note: We need to distinguish between cache miss and false value
		// For now, always check database if cache returns false
		if isActive {
			return true, nil
		}
	}

	// Fetch from database
	isActive, err = s.repo.IsUserActiveInTenant(ctx, userID, tenantID)
	if err != nil {
		return false, err
	}

	// Cache the result
	if err := s.cache.SetUserActiveStatus(ctx, userID, tenantID, isActive); err != nil {
		s.logger.WithError(err).Warn("Failed to cache active status")
	}

	return isActive, nil
}

// GetByUserID retrieves all user memberships with caching
func (s *CachedTenantMembershipService) GetByUserID(ctx context.Context, userID uint) ([]models.TenantMembership, error) {
	// Try cache first
	memberships, err := s.cache.GetUserTenants(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get user tenants from cache")
	} else if memberships != nil {
		return memberships, nil
	}

	// Cache miss - fetch from database
	memberships, err = s.repo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Cache the result
	if err := s.cache.SetUserTenants(ctx, userID, memberships); err != nil {
		s.logger.WithError(err).Warn("Failed to cache user tenants")
	}

	return memberships, nil
}

// GetPrimaryByUserID retrieves user's primary tenant membership with caching
func (s *CachedTenantMembershipService) GetPrimaryByUserID(ctx context.Context, userID uint) (*models.TenantMembership, error) {
	// For primary membership, we could cache it with a different key pattern
	// For now, delegate to repository directly since it's less frequently accessed
	return s.repo.GetPrimaryByUserID(ctx, userID)
}

// Create creates a new membership and invalidates cache
func (s *CachedTenantMembershipService) Create(ctx context.Context, membership *models.TenantMembership) error {
	if err := s.repo.Create(ctx, membership); err != nil {
		return err
	}

	// Invalidate related cache entries
	s.invalidateUserCache(ctx, membership.UserID)
	s.invalidateTenantCache(ctx, membership.TenantID)

	return nil
}

// Update updates a membership and invalidates cache
func (s *CachedTenantMembershipService) Update(ctx context.Context, membership *models.TenantMembership) error {
	if err := s.repo.Update(ctx, membership); err != nil {
		return err
	}

	// Invalidate related cache entries
	s.invalidateUserCache(ctx, membership.UserID)
	s.invalidateTenantCache(ctx, membership.TenantID)

	return nil
}

// UpdateStatus updates membership status and invalidates cache
func (s *CachedTenantMembershipService) UpdateStatus(ctx context.Context, id uint, status models.TenantMembershipStatus) error {
	// Get the membership first to know which caches to invalidate
	membership, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if err := s.repo.UpdateStatus(ctx, id, status); err != nil {
		return err
	}

	// Invalidate related cache entries
	s.invalidateUserCache(ctx, membership.UserID)
	s.invalidateTenantCache(ctx, membership.TenantID)

	return nil
}

// Delete deletes a membership and invalidates cache
func (s *CachedTenantMembershipService) Delete(ctx context.Context, id uint) error {
	// Get the membership first to know which caches to invalidate
	membership, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if err := s.repo.Delete(ctx, id); err != nil {
		return err
	}

	// Invalidate related cache entries
	s.invalidateUserCache(ctx, membership.UserID)
	s.invalidateTenantCache(ctx, membership.TenantID)

	return nil
}

// UserBelongsToTenant checks membership with caching
func (s *CachedTenantMembershipService) UserBelongsToTenant(ctx context.Context, userID, tenantID uint) (bool, error) {
	// Use the cached active status check
	return s.IsUserActiveInTenant(ctx, userID, tenantID)
}

// Helper methods for cache invalidation
func (s *CachedTenantMembershipService) invalidateUserCache(ctx context.Context, userID uint) {
	if err := s.cache.InvalidateUserCache(ctx, userID); err != nil {
		s.logger.WithError(err).Warnf("Failed to invalidate user cache for user %d", userID)
	}
}

func (s *CachedTenantMembershipService) invalidateTenantCache(ctx context.Context, tenantID uint) {
	if err := s.cache.InvalidateTenantCache(ctx, tenantID); err != nil {
		s.logger.WithError(err).Warnf("Failed to invalidate tenant cache for tenant %d", tenantID)
	}
}

// Delegate methods that don't need caching
func (s *CachedTenantMembershipService) GetByID(ctx context.Context, id uint) (*models.TenantMembership, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *CachedTenantMembershipService) GetByTenantID(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.TenantMembership, *pagination.CursorResponse, error) {
	return s.repo.GetByTenantID(ctx, tenantID, pagination)
}

func (s *CachedTenantMembershipService) GetByStatus(ctx context.Context, status models.TenantMembershipStatus, pagination *pagination.CursorPagination) ([]models.TenantMembership, *pagination.CursorResponse, error) {
	return s.repo.GetByStatus(ctx, status, pagination)
}

func (s *CachedTenantMembershipService) GetActiveMembers(ctx context.Context, tenantID uint) ([]models.TenantMembership, error) {
	return s.repo.GetActiveMembers(ctx, tenantID)
}

func (s *CachedTenantMembershipService) GetPendingMembers(ctx context.Context, tenantID uint) ([]models.TenantMembership, error) {
	return s.repo.GetPendingMembers(ctx, tenantID)
}

func (s *CachedTenantMembershipService) UpdateActivity(ctx context.Context, id uint) error {
	return s.repo.UpdateActivity(ctx, id)
}

func (s *CachedTenantMembershipService) GetRecentActivity(ctx context.Context, tenantID uint, limit int) ([]models.TenantMembership, error) {
	return s.repo.GetRecentActivity(ctx, tenantID, limit)
}

func (s *CachedTenantMembershipService) LocalUsernameExists(ctx context.Context, tenantID uint, username string) (bool, error) {
	return s.repo.LocalUsernameExists(ctx, tenantID, username)
}

func (s *CachedTenantMembershipService) LocalUsernameExistsExcludingUser(ctx context.Context, tenantID uint, username string, excludeUserID uint) (bool, error) {
	return s.repo.LocalUsernameExistsExcludingUser(ctx, tenantID, username, excludeUserID)
}

func (s *CachedTenantMembershipService) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	return s.repo.CountByTenant(ctx, tenantID)
}

func (s *CachedTenantMembershipService) CountByStatus(ctx context.Context, tenantID uint, status models.TenantMembershipStatus) (int64, error) {
	return s.repo.CountByStatus(ctx, tenantID, status)
}

func (s *CachedTenantMembershipService) CountByUser(ctx context.Context, userID uint) (int64, error) {
	return s.repo.CountByUser(ctx, userID)
}
