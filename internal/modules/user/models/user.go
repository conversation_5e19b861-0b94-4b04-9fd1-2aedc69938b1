package models

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/datatypes"
)

// UserStatus represents the status of a user
// @Enum active,suspended,inactive,pending_verification,deleted
type UserStatus string

const (
	UserStatusActive              UserStatus = "active"
	UserStatusSuspended           UserStatus = "suspended"
	UserStatusInactive            UserStatus = "inactive"
	UserStatusPendingVerification UserStatus = "pending_verification"
	UserStatusDeleted             UserStatus = "deleted"
)

// UserRole represents the basic role of a user
// @Enum admin,user,guest
type UserRole string

const (
	UserRoleAdmin UserRole = "admin"
	UserRoleUser  UserRole = "user"
	UserRoleGuest UserRole = "guest"
)

// User represents a global user in the system (independent of tenants)
type User struct {
	ID uint `gorm:"primaryKey;autoIncrement" json:"id"`

	// Basic Information
	Email       string  `gorm:"type:varchar(255);not null;uniqueIndex" json:"email" validate:"required,email"`
	Username    *string `gorm:"type:varchar(255);uniqueIndex" json:"username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	FirstName   *string `gorm:"type:varchar(255)" json:"first_name,omitempty" validate:"omitempty,min=1,max=255"`
	LastName    *string `gorm:"type:varchar(255)" json:"last_name,omitempty" validate:"omitempty,min=1,max=255"`
	DisplayName *string `gorm:"type:varchar(255)" json:"display_name,omitempty" validate:"omitempty,min=1,max=255"`

	// Authentication
	PasswordHash     string     `gorm:"type:varchar(255);not null" json:"-"` // Hide from JSON
	EmailVerified    bool       `gorm:"default:false;index" json:"email_verified"`
	EmailVerifiedAt  *time.Time `json:"email_verified_at,omitempty"`

	// Status and Configuration
	Status UserStatus `gorm:"type:varchar(50);default:'active';not null;index" json:"status" validate:"required,oneof=active suspended inactive pending_verification deleted"`

	// Contact Information
	Phone           *string    `gorm:"type:varchar(50)" json:"phone,omitempty" validate:"omitempty,e164"`
	PhoneVerified   bool       `gorm:"default:false" json:"phone_verified"`
	PhoneVerifiedAt *time.Time `json:"phone_verified_at,omitempty"`

	// Profile Information
	AvatarURL *string `gorm:"type:varchar(500)" json:"avatar_url,omitempty" validate:"omitempty,url"`
	Timezone  string  `gorm:"type:varchar(100);default:'UTC'" json:"timezone" validate:"required"`
	Language  string  `gorm:"type:varchar(10);default:'en'" json:"language" validate:"required,min=2,max=10"`

	// Security
	TwoFactorEnabled bool         `gorm:"default:false" json:"two_factor_enabled"`
	TwoFactorSecret  *string      `gorm:"type:varchar(255)" json:"-"` // Hide from JSON
	RecoveryCodes    datatypes.JSON `gorm:"type:json" json:"-" swaggertype:"array,string"` // Hide from JSON

	// Activity Tracking
	LastLoginAt *time.Time `json:"last_login_at,omitempty"`
	LastLoginIP *string    `gorm:"type:varchar(45)" json:"last_login_ip,omitempty"`
	LoginCount  uint       `gorm:"default:0" json:"login_count"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`

	// Relationships
	Profile     *UserProfile      `gorm:"foreignKey:UserID" json:"profile,omitempty"`
	Preferences *UserPreferences  `gorm:"foreignKey:UserID" json:"preferences,omitempty"`
	SocialLinks []UserSocialLink  `gorm:"foreignKey:UserID" json:"social_links,omitempty"`
	TenantMemberships []TenantMembership `gorm:"foreignKey:UserID" json:"tenant_memberships,omitempty"`
}

// TableName returns the table name for the User model
func (User) TableName() string {
	return "users"
}

// BeforeCreate hook to set default values
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// Set default timezone if not provided
	if u.Timezone == "" {
		u.Timezone = "UTC"
	}
	
	// Set default language if not provided
	if u.Language == "" {
		u.Language = "en"
	}
	
	// Set default status if not provided
	if u.Status == "" {
		u.Status = UserStatusActive
	}
	
	
	// Initialize RecoveryCodes if nil
	if u.RecoveryCodes == nil {
		u.RecoveryCodes = datatypes.JSON("[]")
	}
	
	return nil
}

// IsActive checks if the user is active
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsEmailVerified checks if the user's email is verified
func (u *User) IsEmailVerified() bool {
	return u.EmailVerified
}

// IsPhoneVerified checks if the user's phone is verified
func (u *User) IsPhoneVerified() bool {
	return u.PhoneVerified
}

// IsTwoFactorEnabled checks if two-factor authentication is enabled
func (u *User) IsTwoFactorEnabled() bool {
	return u.TwoFactorEnabled
}

// GetDisplayName returns the display name or constructs one from first/last name
func (u *User) GetDisplayName() string {
	if u.DisplayName != nil && *u.DisplayName != "" {
		return *u.DisplayName
	}
	
	if u.FirstName != nil && u.LastName != nil {
		return *u.FirstName + " " + *u.LastName
	}
	
	if u.FirstName != nil {
		return *u.FirstName
	}
	
	if u.Username != nil {
		return *u.Username
	}
	
	return u.Email
}

// CanLogin checks if the user can login
func (u *User) CanLogin() bool {
	return u.Status == UserStatusActive && u.EmailVerified
}

// UpdateLoginInfo updates the user's login information
func (u *User) UpdateLoginInfo(ip string) {
	u.LastLoginAt = &time.Time{}
	*u.LastLoginAt = time.Now()
	u.LastLoginIP = &ip
	u.LoginCount++
}

// UserCreateRequest represents the request to create a new user
type UserCreateRequest struct {
	Email       string  `json:"email" validate:"required,email"`
	Username    *string `json:"username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	FirstName   *string `json:"first_name,omitempty" validate:"omitempty,min=1,max=255"`
	LastName    *string `json:"last_name,omitempty" validate:"omitempty,min=1,max=255"`
	DisplayName *string `json:"display_name,omitempty" validate:"omitempty,min=1,max=255"`
	Password    string  `json:"password" validate:"required,min=8,max=128"`
	Phone       *string `json:"phone,omitempty" validate:"omitempty,e164"`
	AvatarURL   *string `json:"avatar_url,omitempty" validate:"omitempty,url"`
	Timezone    string  `json:"timezone" validate:"required"`
	Language    string  `json:"language" validate:"required,min=2,max=10"`
}

// UserUpdateRequest represents the request to update a user
type UserUpdateRequest struct {
	Username    *string `json:"username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	FirstName   *string `json:"first_name,omitempty" validate:"omitempty,min=1,max=255"`
	LastName    *string `json:"last_name,omitempty" validate:"omitempty,min=1,max=255"`
	DisplayName *string `json:"display_name,omitempty" validate:"omitempty,min=1,max=255"`
	Phone       *string `json:"phone,omitempty" validate:"omitempty,e164"`
	AvatarURL   *string `json:"avatar_url,omitempty" validate:"omitempty,url"`
	Timezone    string  `json:"timezone" validate:"required"`
	Language    string  `json:"language" validate:"required,min=2,max=10"`
	Status      UserStatus `json:"status" validate:"required,oneof=active suspended inactive pending_verification deleted"`
}

// UserResponse represents the response when returning user data
type UserResponse struct {
	ID              uint       `json:"id"`
	Email           string     `json:"email"`
	Username        *string    `json:"username,omitempty"`
	FirstName       *string    `json:"first_name,omitempty"`
	LastName        *string    `json:"last_name,omitempty"`
	DisplayName     *string    `json:"display_name,omitempty"`
	EmailVerified   bool       `json:"email_verified"`
	EmailVerifiedAt *time.Time `json:"email_verified_at,omitempty"`
	Status          UserStatus `json:"status"`
	Phone           *string    `json:"phone,omitempty"`
	PhoneVerified   bool       `json:"phone_verified"`
	PhoneVerifiedAt *time.Time `json:"phone_verified_at,omitempty"`
	AvatarURL       *string    `json:"avatar_url,omitempty"`
	Timezone        string     `json:"timezone"`
	Language        string     `json:"language"`
	TwoFactorEnabled bool      `json:"two_factor_enabled"`
	LastLoginAt     *time.Time `json:"last_login_at,omitempty"`
	LastLoginIP     *string    `json:"last_login_ip,omitempty"`
	LoginCount      uint       `json:"login_count"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	
	// Relationships
	Profile     *UserProfile      `json:"profile,omitempty"`
	Preferences *UserPreferences  `json:"preferences,omitempty"`
	SocialLinks []UserSocialLink  `json:"social_links,omitempty"`
	TenantMemberships []TenantMembership `json:"tenant_memberships,omitempty"`
}

// FromUser converts a User model to UserResponse
func (ur *UserResponse) FromUser(user *User) {
	ur.ID = user.ID
	ur.Email = user.Email
	ur.Username = user.Username
	ur.FirstName = user.FirstName
	ur.LastName = user.LastName
	ur.DisplayName = user.DisplayName
	ur.EmailVerified = user.EmailVerified
	ur.EmailVerifiedAt = user.EmailVerifiedAt
	ur.Status = user.Status
	ur.Phone = user.Phone
	ur.PhoneVerified = user.PhoneVerified
	ur.PhoneVerifiedAt = user.PhoneVerifiedAt
	ur.AvatarURL = user.AvatarURL
	ur.Timezone = user.Timezone
	ur.Language = user.Language
	ur.TwoFactorEnabled = user.TwoFactorEnabled
	ur.LastLoginAt = user.LastLoginAt
	ur.LastLoginIP = user.LastLoginIP
	ur.LoginCount = user.LoginCount
	ur.CreatedAt = user.CreatedAt
	ur.UpdatedAt = user.UpdatedAt
	ur.Profile = user.Profile
	ur.Preferences = user.Preferences
	ur.SocialLinks = user.SocialLinks
	ur.TenantMemberships = user.TenantMemberships
}