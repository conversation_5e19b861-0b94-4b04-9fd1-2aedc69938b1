package models

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
	"gorm.io/datatypes"
)

// ProfileVisibility represents the visibility level of a user profile
// @Enum public,private,friends_only
type ProfileVisibility string

const (
	ProfileVisibilityPublic      ProfileVisibility = "public"
	ProfileVisibilityPrivate     ProfileVisibility = "private"
	ProfileVisibilityFriendsOnly ProfileVisibility = "friends_only"
)

// Theme represents the UI theme preference
// @Enum light,dark,system
type Theme string

const (
	ThemeLight  Theme = "light"
	ThemeDark   Theme = "dark"
	ThemeSystem Theme = "system"
)

// UserPreferences represents the user preferences and settings
type UserPreferences struct {
	ID       uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID uint `gorm:"not null;index" json:"tenant_id"`
	UserID   uint `gorm:"not null;uniqueIndex:uk_user_preferences_tenant_user" json:"user_id"`

	// Notification Preferences
	EmailNotifications bool `gorm:"default:true" json:"email_notifications"`
	PushNotifications  bool `gorm:"default:true" json:"push_notifications"`
	SMSNotifications   bool `gorm:"default:false" json:"sms_notifications"`

	// Communication Preferences
	MarketingEmails        bool `gorm:"default:false" json:"marketing_emails"`
	NewsletterSubscription bool `gorm:"default:false" json:"newsletter_subscription"`
	ProductUpdates         bool `gorm:"default:true" json:"product_updates"`
	SecurityAlerts         bool `gorm:"default:true" json:"security_alerts"`

	// Privacy Settings
	ProfileVisibility     ProfileVisibility `gorm:"type:varchar(50);default:'public'" json:"profile_visibility" validate:"oneof=public private friends_only"`
	ShowOnlineStatus      bool              `gorm:"default:true" json:"show_online_status"`
	AllowSearch           bool              `gorm:"default:true" json:"allow_search"`
	DataProcessingConsent bool              `gorm:"default:false" json:"data_processing_consent"`

	// UI/UX Preferences
	Theme           Theme `gorm:"type:varchar(50);default:'light'" json:"theme" validate:"oneof=light dark system"`
	DashboardLayout string `gorm:"type:varchar(50);default:'default'" json:"dashboard_layout"`
	ItemsPerPage    uint   `gorm:"default:20" json:"items_per_page" validate:"min=10,max=100"`

	// Application Settings
	AutoSave           bool `gorm:"default:true" json:"auto_save"`
	KeyboardShortcuts  bool `gorm:"default:true" json:"keyboard_shortcuts"`
	TooltipsEnabled    bool `gorm:"default:true" json:"tooltips_enabled"`

	// Notification Types (JSON for flexibility)
	NotificationTypes datatypes.JSON `gorm:"type:json;default:'{}'" json:"notification_types,omitempty" swaggertype:"object"`

	// Feature Flags (JSON for tenant-specific features)
	FeaturePreferences datatypes.JSON `gorm:"type:json;default:'{}'" json:"feature_preferences,omitempty" swaggertype:"object"`

	// Custom Preferences (JSON for tenant-specific settings)
	CustomPreferences datatypes.JSON `gorm:"type:json;default:'{}'" json:"custom_preferences,omitempty" swaggertype:"object"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the UserPreferences model
func (UserPreferences) TableName() string {
	return "user_preferences"
}

// BeforeCreate hook to set default values
func (up *UserPreferences) BeforeCreate(tx *gorm.DB) error {
	// Initialize JSON fields if they are nil
	if up.NotificationTypes == nil {
		up.NotificationTypes = datatypes.JSON("{}")
	}
	if up.FeaturePreferences == nil {
		up.FeaturePreferences = datatypes.JSON("{}")
	}
	if up.CustomPreferences == nil {
		up.CustomPreferences = datatypes.JSON("{}")
	}
	
	// Set default values
	if up.ProfileVisibility == "" {
		up.ProfileVisibility = ProfileVisibilityPublic
	}
	if up.Theme == "" {
		up.Theme = ThemeLight
	}
	if up.DashboardLayout == "" {
		up.DashboardLayout = "default"
	}
	if up.ItemsPerPage == 0 {
		up.ItemsPerPage = 20
	}
	
	return nil
}

// GetNotificationTypes returns the notification types as a map
func (up *UserPreferences) GetNotificationTypes() map[string]bool {
	if up.NotificationTypes == nil {
		return make(map[string]bool)
	}
	
	var notificationTypes map[string]bool
	if err := json.Unmarshal(up.NotificationTypes, &notificationTypes); err != nil {
		return make(map[string]bool)
	}
	
	return notificationTypes
}

// SetNotificationTypes sets the notification types from a map
func (up *UserPreferences) SetNotificationTypes(notificationTypes map[string]bool) error {
	if notificationTypes == nil {
		notificationTypes = make(map[string]bool)
	}
	
	jsonData, err := json.Marshal(notificationTypes)
	if err != nil {
		return err
	}
	
	up.NotificationTypes = datatypes.JSON(jsonData)
	return nil
}

// GetFeaturePreferences returns the feature preferences as a map
func (up *UserPreferences) GetFeaturePreferences() map[string]interface{} {
	if up.FeaturePreferences == nil {
		return make(map[string]interface{})
	}
	
	var featurePreferences map[string]interface{}
	if err := json.Unmarshal(up.FeaturePreferences, &featurePreferences); err != nil {
		return make(map[string]interface{})
	}
	
	return featurePreferences
}

// SetFeaturePreferences sets the feature preferences from a map
func (up *UserPreferences) SetFeaturePreferences(featurePreferences map[string]interface{}) error {
	if featurePreferences == nil {
		featurePreferences = make(map[string]interface{})
	}
	
	jsonData, err := json.Marshal(featurePreferences)
	if err != nil {
		return err
	}
	
	up.FeaturePreferences = datatypes.JSON(jsonData)
	return nil
}

// GetCustomPreferences returns the custom preferences as a map
func (up *UserPreferences) GetCustomPreferences() map[string]interface{} {
	if up.CustomPreferences == nil {
		return make(map[string]interface{})
	}
	
	var customPreferences map[string]interface{}
	if err := json.Unmarshal(up.CustomPreferences, &customPreferences); err != nil {
		return make(map[string]interface{})
	}
	
	return customPreferences
}

// SetCustomPreferences sets the custom preferences from a map
func (up *UserPreferences) SetCustomPreferences(customPreferences map[string]interface{}) error {
	if customPreferences == nil {
		customPreferences = make(map[string]interface{})
	}
	
	jsonData, err := json.Marshal(customPreferences)
	if err != nil {
		return err
	}
	
	up.CustomPreferences = datatypes.JSON(jsonData)
	return nil
}

// IsNotificationEnabled checks if a specific notification type is enabled
func (up *UserPreferences) IsNotificationEnabled(notificationType string) bool {
	notificationTypes := up.GetNotificationTypes()
	if enabled, exists := notificationTypes[notificationType]; exists {
		return enabled
	}
	
	// Default to true for unknown notification types
	return true
}

// EnableNotification enables a specific notification type
func (up *UserPreferences) EnableNotification(notificationType string) error {
	notificationTypes := up.GetNotificationTypes()
	notificationTypes[notificationType] = true
	return up.SetNotificationTypes(notificationTypes)
}

// DisableNotification disables a specific notification type
func (up *UserPreferences) DisableNotification(notificationType string) error {
	notificationTypes := up.GetNotificationTypes()
	notificationTypes[notificationType] = false
	return up.SetNotificationTypes(notificationTypes)
}

// GetFeaturePreference gets a specific feature preference
func (up *UserPreferences) GetFeaturePreference(feature string) (interface{}, bool) {
	featurePreferences := up.GetFeaturePreferences()
	value, exists := featurePreferences[feature]
	return value, exists
}

// SetFeaturePreference sets a specific feature preference
func (up *UserPreferences) SetFeaturePreference(feature string, value interface{}) error {
	featurePreferences := up.GetFeaturePreferences()
	featurePreferences[feature] = value
	return up.SetFeaturePreferences(featurePreferences)
}

// GetCustomPreference gets a specific custom preference
func (up *UserPreferences) GetCustomPreference(key string) (interface{}, bool) {
	customPreferences := up.GetCustomPreferences()
	value, exists := customPreferences[key]
	return value, exists
}

// SetCustomPreference sets a specific custom preference
func (up *UserPreferences) SetCustomPreference(key string, value interface{}) error {
	customPreferences := up.GetCustomPreferences()
	customPreferences[key] = value
	return up.SetCustomPreferences(customPreferences)
}

// UserPreferencesCreateRequest represents the request to create user preferences
type UserPreferencesCreateRequest struct {
	TenantID               uint                           `json:"tenant_id" validate:"required,min=1"`
	UserID                 uint                           `json:"user_id" validate:"required,min=1"`
	EmailNotifications     bool                           `json:"email_notifications"`
	PushNotifications      bool                           `json:"push_notifications"`
	SMSNotifications       bool                           `json:"sms_notifications"`
	MarketingEmails        bool                           `json:"marketing_emails"`
	NewsletterSubscription bool                           `json:"newsletter_subscription"`
	ProductUpdates         bool                           `json:"product_updates"`
	SecurityAlerts         bool                           `json:"security_alerts"`
	ProfileVisibility      ProfileVisibility              `json:"profile_visibility" validate:"oneof=public private friends_only"`
	ShowOnlineStatus       bool                           `json:"show_online_status"`
	AllowSearch            bool                           `json:"allow_search"`
	DataProcessingConsent  bool                           `json:"data_processing_consent"`
	Theme                  Theme                          `json:"theme" validate:"oneof=light dark system"`
	DashboardLayout        string                         `json:"dashboard_layout"`
	ItemsPerPage           uint                           `json:"items_per_page" validate:"min=10,max=100"`
	AutoSave               bool                           `json:"auto_save"`
	KeyboardShortcuts      bool                           `json:"keyboard_shortcuts"`
	TooltipsEnabled        bool                           `json:"tooltips_enabled"`
	NotificationTypes      map[string]bool                `json:"notification_types,omitempty"`
	FeaturePreferences     map[string]interface{}         `json:"feature_preferences,omitempty"`
	CustomPreferences      map[string]interface{}         `json:"custom_preferences,omitempty"`
}

// UserPreferencesUpdateRequest represents the request to update user preferences
type UserPreferencesUpdateRequest struct {
	EmailNotifications     bool                           `json:"email_notifications"`
	PushNotifications      bool                           `json:"push_notifications"`
	SMSNotifications       bool                           `json:"sms_notifications"`
	MarketingEmails        bool                           `json:"marketing_emails"`
	NewsletterSubscription bool                           `json:"newsletter_subscription"`
	ProductUpdates         bool                           `json:"product_updates"`
	SecurityAlerts         bool                           `json:"security_alerts"`
	ProfileVisibility      ProfileVisibility              `json:"profile_visibility" validate:"oneof=public private friends_only"`
	ShowOnlineStatus       bool                           `json:"show_online_status"`
	AllowSearch            bool                           `json:"allow_search"`
	DataProcessingConsent  bool                           `json:"data_processing_consent"`
	Theme                  Theme                          `json:"theme" validate:"oneof=light dark system"`
	DashboardLayout        string                         `json:"dashboard_layout"`
	ItemsPerPage           uint                           `json:"items_per_page" validate:"min=10,max=100"`
	AutoSave               bool                           `json:"auto_save"`
	KeyboardShortcuts      bool                           `json:"keyboard_shortcuts"`
	TooltipsEnabled        bool                           `json:"tooltips_enabled"`
	NotificationTypes      map[string]bool                `json:"notification_types,omitempty"`
	FeaturePreferences     map[string]interface{}         `json:"feature_preferences,omitempty"`
	CustomPreferences      map[string]interface{}         `json:"custom_preferences,omitempty"`
}

// UserPreferencesResponse represents the response when returning user preferences data
type UserPreferencesResponse struct {
	ID                     uint                           `json:"id"`
	TenantID               uint                           `json:"tenant_id"`
	UserID                 uint                           `json:"user_id"`
	EmailNotifications     bool                           `json:"email_notifications"`
	PushNotifications      bool                           `json:"push_notifications"`
	SMSNotifications       bool                           `json:"sms_notifications"`
	MarketingEmails        bool                           `json:"marketing_emails"`
	NewsletterSubscription bool                           `json:"newsletter_subscription"`
	ProductUpdates         bool                           `json:"product_updates"`
	SecurityAlerts         bool                           `json:"security_alerts"`
	ProfileVisibility      ProfileVisibility              `json:"profile_visibility"`
	ShowOnlineStatus       bool                           `json:"show_online_status"`
	AllowSearch            bool                           `json:"allow_search"`
	DataProcessingConsent  bool                           `json:"data_processing_consent"`
	Theme                  Theme                          `json:"theme"`
	DashboardLayout        string                         `json:"dashboard_layout"`
	ItemsPerPage           uint                           `json:"items_per_page"`
	AutoSave               bool                           `json:"auto_save"`
	KeyboardShortcuts      bool                           `json:"keyboard_shortcuts"`
	TooltipsEnabled        bool                           `json:"tooltips_enabled"`
	NotificationTypes      map[string]bool                `json:"notification_types,omitempty"`
	FeaturePreferences     map[string]interface{}         `json:"feature_preferences,omitempty"`
	CustomPreferences      map[string]interface{}         `json:"custom_preferences,omitempty"`
	CreatedAt              time.Time                      `json:"created_at"`
	UpdatedAt              time.Time                      `json:"updated_at"`
}

// FromUserPreferences converts a UserPreferences model to UserPreferencesResponse
func (upr *UserPreferencesResponse) FromUserPreferences(preferences *UserPreferences) {
	upr.ID = preferences.ID
	upr.TenantID = preferences.TenantID
	upr.UserID = preferences.UserID
	upr.EmailNotifications = preferences.EmailNotifications
	upr.PushNotifications = preferences.PushNotifications
	upr.SMSNotifications = preferences.SMSNotifications
	upr.MarketingEmails = preferences.MarketingEmails
	upr.NewsletterSubscription = preferences.NewsletterSubscription
	upr.ProductUpdates = preferences.ProductUpdates
	upr.SecurityAlerts = preferences.SecurityAlerts
	upr.ProfileVisibility = preferences.ProfileVisibility
	upr.ShowOnlineStatus = preferences.ShowOnlineStatus
	upr.AllowSearch = preferences.AllowSearch
	upr.DataProcessingConsent = preferences.DataProcessingConsent
	upr.Theme = preferences.Theme
	upr.DashboardLayout = preferences.DashboardLayout
	upr.ItemsPerPage = preferences.ItemsPerPage
	upr.AutoSave = preferences.AutoSave
	upr.KeyboardShortcuts = preferences.KeyboardShortcuts
	upr.TooltipsEnabled = preferences.TooltipsEnabled
	upr.NotificationTypes = preferences.GetNotificationTypes()
	upr.FeaturePreferences = preferences.GetFeaturePreferences()
	upr.CustomPreferences = preferences.GetCustomPreferences()
	upr.CreatedAt = preferences.CreatedAt
	upr.UpdatedAt = preferences.UpdatedAt
}

// NotificationPreferencesUpdateRequest represents partial update for notification preferences
type NotificationPreferencesUpdateRequest struct {
	EmailNotifications     *bool                  `json:"email_notifications,omitempty"`
	PushNotifications      *bool                  `json:"push_notifications,omitempty"`
	SMSNotifications       *bool                  `json:"sms_notifications,omitempty"`
	MarketingEmails        *bool                  `json:"marketing_emails,omitempty"`
	NewsletterSubscription *bool                  `json:"newsletter_subscription,omitempty"`
	ProductUpdates         *bool                  `json:"product_updates,omitempty"`
	SecurityAlerts         *bool                  `json:"security_alerts,omitempty"`
	NotificationTypes      map[string]bool        `json:"notification_types,omitempty"`
}

// PrivacyPreferencesUpdateRequest represents partial update for privacy preferences
type PrivacyPreferencesUpdateRequest struct {
	ProfileVisibility     *ProfileVisibility `json:"profile_visibility,omitempty" validate:"omitempty,oneof=public private friends_only"`
	ShowOnlineStatus      *bool              `json:"show_online_status,omitempty"`
	AllowSearch           *bool              `json:"allow_search,omitempty"`
	DataProcessingConsent *bool              `json:"data_processing_consent,omitempty"`
}

// UIPreferencesUpdateRequest represents partial update for UI preferences
type UIPreferencesUpdateRequest struct {
	Theme             *Theme  `json:"theme,omitempty" validate:"omitempty,oneof=light dark system"`
	DashboardLayout   *string `json:"dashboard_layout,omitempty"`
	ItemsPerPage      *uint   `json:"items_per_page,omitempty" validate:"omitempty,min=10,max=100"`
	AutoSave          *bool   `json:"auto_save,omitempty"`
	KeyboardShortcuts *bool   `json:"keyboard_shortcuts,omitempty"`
	TooltipsEnabled   *bool   `json:"tooltips_enabled,omitempty"`
}