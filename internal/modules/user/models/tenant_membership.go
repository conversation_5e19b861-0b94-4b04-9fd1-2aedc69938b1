package models

import (
	"time"
)

// TenantMembershipStatus represents the status of a tenant membership
// @Enum active,inactive,suspended,pending,deleted
type TenantMembershipStatus string

const (
	TenantMembershipStatusActive    TenantMembershipStatus = "active"
	TenantMembershipStatusInactive  TenantMembershipStatus = "inactive"
	TenantMembershipStatusSuspended TenantMembershipStatus = "suspended"
	TenantMembershipStatusPending   TenantMembershipStatus = "pending"
	TenantMembershipStatusDeleted   TenantMembershipStatus = "deleted"
)

// TenantMembership represents a user's membership in a tenant
type TenantMembership struct {
	ID       uint `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID   uint `gorm:"not null;index" json:"user_id"`
	TenantID uint `gorm:"not null;index" json:"tenant_id"`

	// Membership Details
	Status        TenantMembershipStatus `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	IsPrimary     bool                   `gorm:"default:false;index" json:"is_primary"`
	LocalUsername *string                `gorm:"type:varchar(255)" json:"local_username,omitempty"`
	DisplayName   *string                `gorm:"type:varchar(255)" json:"display_name,omitempty"`

	// Invitation Details
	InvitedBy            *uint      `gorm:"index" json:"invited_by,omitempty"`
	InvitationToken      *string    `gorm:"type:varchar(255)" json:"-"` // Hidden from JSON
	InvitationAcceptedAt *time.Time `json:"invitation_accepted_at,omitempty"`

	// Activity Tracking
	JoinedAt       time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"joined_at"`
	LastActivityAt *time.Time `json:"last_activity_at,omitempty"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User          *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
	InvitedByUser *User `gorm:"foreignKey:InvitedBy" json:"invited_by_user,omitempty"`
}

// TableName returns the table name for the TenantMembership model
func (TenantMembership) TableName() string {
	return "tenant_memberships"
}

// IsActive checks if the membership is active
func (tm *TenantMembership) IsActive() bool {
	return tm.Status == TenantMembershipStatusActive
}

// IsPending checks if the membership is pending
func (tm *TenantMembership) IsPending() bool {
	return tm.Status == TenantMembershipStatusPending
}

// IsPrimaryTenant checks if this is the user's primary tenant
func (tm *TenantMembership) IsPrimaryTenant() bool {
	return tm.IsPrimary
}

// IsDeleted checks if the membership is deleted
func (tm *TenantMembership) IsDeleted() bool {
	return tm.Status == TenantMembershipStatusDeleted
}

// UpdateActivity updates the last activity timestamp
func (tm *TenantMembership) UpdateActivity() {
	now := time.Now()
	tm.LastActivityAt = &now
}

// TenantMembershipFilter represents filters for querying tenant memberships
type TenantMembershipFilter struct {
	UserID         uint                   `json:"user_id,omitempty"`
	TenantID       uint                   `json:"tenant_id,omitempty"`
	Status         TenantMembershipStatus `json:"status,omitempty"`
	InvitedBy      *uint                  `json:"invited_by,omitempty"`
	Search         string                 `json:"search,omitempty"`
	Page           int                    `json:"page,omitempty"`
	PageSize       int                    `json:"page_size,omitempty"`
	SortBy         string                 `json:"sort_by,omitempty"`
	SortOrder      string                 `json:"sort_order,omitempty"`
	IncludeDeleted bool                   `json:"include_deleted,omitempty"`
}

// TenantMembershipCreateRequest represents the request to create a tenant membership
type TenantMembershipCreateRequest struct {
	UserID        uint    `json:"user_id" validate:"required,min=1"`
	TenantID      uint    `json:"tenant_id" validate:"required,min=1"`
	IsPrimary     bool    `json:"is_primary"`
	LocalUsername *string `json:"local_username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	DisplayName   *string `json:"display_name,omitempty" validate:"omitempty,min=1,max=255"`
	InvitedBy     *uint   `json:"invited_by,omitempty" validate:"omitempty,min=1"`
}

// TenantMembershipUpdateRequest represents the request to update a tenant membership
type TenantMembershipUpdateRequest struct {
	Status        TenantMembershipStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended"`
	LocalUsername *string                `json:"local_username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	DisplayName   *string                `json:"display_name,omitempty" validate:"omitempty,min=1,max=255"`
}

// TenantMembershipResponse represents the response when returning tenant membership data
type TenantMembershipResponse struct {
	ID                   uint                   `json:"id"`
	UserID               uint                   `json:"user_id"`
	TenantID             uint                   `json:"tenant_id"`
	Status               TenantMembershipStatus `json:"status"`
	IsPrimary            bool                   `json:"is_primary"`
	LocalUsername        *string                `json:"local_username,omitempty"`
	DisplayName          *string                `json:"display_name,omitempty"`
	InvitedBy            *uint                  `json:"invited_by,omitempty"`
	InvitationAcceptedAt *time.Time             `json:"invitation_accepted_at,omitempty"`
	JoinedAt             time.Time              `json:"joined_at"`
	LastActivityAt       *time.Time             `json:"last_activity_at,omitempty"`
	CreatedAt            time.Time              `json:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at"`

	// Relationships
	User          *UserResponse `json:"user,omitempty"`
	InvitedByUser *UserResponse `json:"invited_by_user,omitempty"`
}

// FromTenantMembership converts a TenantMembership model to TenantMembershipResponse
func (tmr *TenantMembershipResponse) FromTenantMembership(tm *TenantMembership) {
	tmr.ID = tm.ID
	tmr.UserID = tm.UserID
	tmr.TenantID = tm.TenantID
	tmr.Status = tm.Status
	tmr.IsPrimary = tm.IsPrimary
	tmr.LocalUsername = tm.LocalUsername
	tmr.DisplayName = tm.DisplayName
	tmr.InvitedBy = tm.InvitedBy
	tmr.InvitationAcceptedAt = tm.InvitationAcceptedAt
	tmr.JoinedAt = tm.JoinedAt
	tmr.LastActivityAt = tm.LastActivityAt
	tmr.CreatedAt = tm.CreatedAt
	tmr.UpdatedAt = tm.UpdatedAt

	// Convert relationships
	if tm.User != nil {
		userResponse := &UserResponse{}
		userResponse.FromUser(tm.User)
		tmr.User = userResponse
	}
	if tm.InvitedByUser != nil {
		invitedByResponse := &UserResponse{}
		invitedByResponse.FromUser(tm.InvitedByUser)
		tmr.InvitedByUser = invitedByResponse
	}
}
