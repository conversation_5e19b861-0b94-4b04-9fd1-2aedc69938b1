package models

import (
	"database/sql/driver"
	"time"

	"gorm.io/gorm"
)

// ChatMessageRole represents the role of a chat message
// @Enum user,assistant,system
type ChatMessageRole string

const (
	ChatMessageRoleUser      ChatMessageRole = "user"
	ChatMessageRoleAssistant ChatMessageRole = "assistant"
	ChatMessageRoleSystem    ChatMessageRole = "system"
)

// Scan implements sql.Scanner interface
func (r *ChatMessageRole) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*r = ChatMessageRole(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (r ChatMessageRole) Value() (driver.Value, error) {
	return string(r), nil
}

// AIChatMessage represents a message in an AI chat session
type AIChatMessage struct {
	ID         uint            `json:"id" gorm:"primaryKey"`
	SessionID  uint            `json:"session_id" gorm:"not null;index"`
	Role       ChatMessageRole `json:"role" gorm:"type:enum('user','assistant','system');not null"`
	Content    string          `json:"content" gorm:"type:text;not null" validate:"required,min=1"`
	Metadata   JSONMap         `json:"metadata,omitempty" gorm:"type:json"`
	TokensUsed int             `json:"tokens_used" gorm:"default:0"`
	CreatedAt  time.Time       `json:"created_at" gorm:"autoCreateTime"`

	// Associations (loaded separately to avoid circular references)
	// Session *AIChatSession `json:"session,omitempty" gorm:"foreignKey:SessionID"`
}

// TableName specifies the table name for AIChatMessage
func (AIChatMessage) TableName() string {
	return "ai_chat_messages"
}

// IsUserMessage checks if message is from user
func (m *AIChatMessage) IsUserMessage() bool {
	return m.Role == ChatMessageRoleUser
}

// IsAssistantMessage checks if message is from assistant
func (m *AIChatMessage) IsAssistantMessage() bool {
	return m.Role == ChatMessageRoleAssistant
}

// IsSystemMessage checks if message is system message
func (m *AIChatMessage) IsSystemMessage() bool {
	return m.Role == ChatMessageRoleSystem
}

// BeforeCreate hook for AIChatMessage
func (m *AIChatMessage) BeforeCreate(tx *gorm.DB) error {
	if m.Metadata == nil {
		m.Metadata = make(JSONMap)
	}
	return nil
}

// GetMetadataValue gets a value from the message metadata
func (m *AIChatMessage) GetMetadataValue(key string) interface{} {
	if m.Metadata == nil {
		return nil
	}
	return m.Metadata[key]
}

// SetMetadataValue sets a value in the message metadata
func (m *AIChatMessage) SetMetadataValue(key string, value interface{}) {
	if m.Metadata == nil {
		m.Metadata = make(JSONMap)
	}
	m.Metadata[key] = value
}

// UpdateTokensUsed updates the tokens used count
func (m *AIChatMessage) UpdateTokensUsed(tokens int) {
	m.TokensUsed = tokens
}

// GetContentLength returns the length of the content
func (m *AIChatMessage) GetContentLength() int {
	return len(m.Content)
}

// TruncateContent truncates content to specified length
func (m *AIChatMessage) TruncateContent(maxLength int) {
	if len(m.Content) > maxLength {
		m.Content = m.Content[:maxLength]
	}
}

// ChatMessageFilter represents filters for querying chat messages
type ChatMessageFilter struct {
	SessionID  uint              `json:"session_id,omitempty"`
	Role       ChatMessageRole   `json:"role,omitempty"`
	Roles      []ChatMessageRole `json:"roles,omitempty"`
	Search     string            `json:"search,omitempty"`
	MinTokens  int               `json:"min_tokens,omitempty"`
	MaxTokens  int               `json:"max_tokens,omitempty"`
	From       *time.Time        `json:"from,omitempty"`
	To         *time.Time        `json:"to,omitempty"`
	Page       int               `json:"page,omitempty"`
	PageSize   int               `json:"page_size,omitempty"`
	SortBy     string            `json:"sort_by,omitempty"`
	SortOrder  string            `json:"sort_order,omitempty"`
}

// CreateChatMessageRequest represents the request to create a chat message
type CreateChatMessageRequest struct {
	SessionID  uint            `json:"session_id" validate:"required,min=1"`
	Role       ChatMessageRole `json:"role" validate:"required,oneof=user assistant system"`
	Content    string          `json:"content" validate:"required,min=1,max=100000"`
	Metadata   JSONMap         `json:"metadata,omitempty"`
	TokensUsed int             `json:"tokens_used,omitempty" validate:"omitempty,min=0"`
}

// UpdateChatMessageRequest represents the request to update a chat message
type UpdateChatMessageRequest struct {
	Content    string  `json:"content,omitempty" validate:"omitempty,min=1,max=100000"`
	Metadata   JSONMap `json:"metadata,omitempty"`
	TokensUsed int     `json:"tokens_used,omitempty" validate:"omitempty,min=0"`
}

// ChatMessageResponse represents the chat message response
type ChatMessageResponse struct {
	ID         uint            `json:"id"`
	SessionID  uint            `json:"session_id"`
	Role       ChatMessageRole `json:"role"`
	Content    string          `json:"content"`
	Metadata   JSONMap         `json:"metadata,omitempty"`
	TokensUsed int             `json:"tokens_used"`
	CreatedAt  time.Time       `json:"created_at"`
}

// ToResponse converts an AIChatMessage to ChatMessageResponse
func (m *AIChatMessage) ToResponse() *ChatMessageResponse {
	return &ChatMessageResponse{
		ID:         m.ID,
		SessionID:  m.SessionID,
		Role:       m.Role,
		Content:    m.Content,
		Metadata:   m.Metadata,
		TokensUsed: m.TokensUsed,
		CreatedAt:  m.CreatedAt,
	}
}

// ListChatMessagesResponse represents the response for listing chat messages
type ListChatMessagesResponse struct {
	Messages   []ChatMessageResponse `json:"messages"`
	TotalCount int64                 `json:"total_count"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"page_size"`
	TotalPages int                   `json:"total_pages"`
}

// ChatConversationResponse represents a conversation view of messages
type ChatConversationResponse struct {
	SessionID    uint                  `json:"session_id"`
	SessionTitle string                `json:"session_title"`
	Messages     []ChatMessageResponse `json:"messages"`
	TotalTokens  int                   `json:"total_tokens"`
	CreatedAt    time.Time             `json:"created_at"`
	UpdatedAt    time.Time             `json:"updated_at"`
}

// MessageStats represents statistics for messages
type MessageStats struct {
	TotalMessages    int64 `json:"total_messages"`
	UserMessages     int64 `json:"user_messages"`
	AssistantMessages int64 `json:"assistant_messages"`
	SystemMessages   int64 `json:"system_messages"`
	TotalTokens      int64 `json:"total_tokens"`
	AverageTokens    float64 `json:"average_tokens"`
}

// BulkCreateChatMessagesRequest represents the request to create multiple chat messages
type BulkCreateChatMessagesRequest struct {
	SessionID uint                      `json:"session_id" validate:"required,min=1"`
	Messages  []CreateChatMessageRequest `json:"messages" validate:"required,min=1,max=100"`
}

// SearchChatMessagesRequest represents the request to search chat messages
type SearchChatMessagesRequest struct {
	TenantID   uint              `json:"tenant_id" validate:"required,min=1"`
	WebsiteID  *uint             `json:"website_id,omitempty" validate:"omitempty,min=1"`
	UserID     *uint             `json:"user_id,omitempty" validate:"omitempty,min=1"`
	SessionID  *uint             `json:"session_id,omitempty" validate:"omitempty,min=1"`
	Query      string            `json:"query" validate:"required,min=1,max=500"`
	Role       ChatMessageRole   `json:"role,omitempty" validate:"omitempty,oneof=user assistant system"`
	Roles      []ChatMessageRole `json:"roles,omitempty" validate:"omitempty,dive,oneof=user assistant system"`
	From       *time.Time        `json:"from,omitempty"`
	To         *time.Time        `json:"to,omitempty"`
	Page       int               `json:"page,omitempty" validate:"omitempty,min=1"`
	PageSize   int               `json:"page_size,omitempty" validate:"omitempty,min=1,max=100"`
}

// ExportChatMessagesRequest represents the request to export chat messages
type ExportChatMessagesRequest struct {
	SessionID uint              `json:"session_id" validate:"required,min=1"`
	Format    string            `json:"format" validate:"required,oneof=json csv markdown"`
	Role      ChatMessageRole   `json:"role,omitempty" validate:"omitempty,oneof=user assistant system"`
	Roles     []ChatMessageRole `json:"roles,omitempty" validate:"omitempty,dive,oneof=user assistant system"`
	From      *time.Time        `json:"from,omitempty"`
	To        *time.Time        `json:"to,omitempty"`
}