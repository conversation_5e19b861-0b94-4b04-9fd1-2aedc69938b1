package models

import (
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)


// AIRequestType represents the type of AI request
// @Enum content_generation,chat,design,optimization,analysis
type AIRequestType string

const (
	AIRequestTypeContentGeneration AIRequestType = "content_generation"
	AIRequestTypeChat             AIRequestType = "chat"
	AIRequestTypeDesign           AIRequestType = "design"
	AIRequestTypeOptimization     AIRequestType = "optimization"
	AIRequestTypeAnalysis         AIRequestType = "analysis"
)

// AIRequestStatus represents the status of an AI request
// @Enum pending,completed,failed,timeout
type AIRequestStatus string

const (
	AIRequestStatusPending   AIRequestStatus = "pending"
	AIRequestStatusCompleted AIRequestStatus = "completed"
	AIRequestStatusFailed    AIRequestStatus = "failed"
	AIRequestStatusTimeout   AIRequestStatus = "timeout"
)

// AIRequest represents a request to an AI service
type AIRequest struct {
	ID             uint            `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID       uint            `gorm:"not null;index:idx_ai_requests_tenant_type" json:"tenant_id" validate:"required"`
	WebsiteID      *uint           `gorm:"index:idx_ai_requests_website;index:idx_ai_requests_tenant_website" json:"website_id"`
	UserID         *uint           `gorm:"index:idx_ai_requests_user_created" json:"user_id"`
	ModelID        uint            `gorm:"not null;index:idx_ai_requests_model_created" json:"model_id" validate:"required"`
	
	// Request Details
	RequestType    AIRequestType   `gorm:"type:varchar(50);not null;index:idx_ai_requests_tenant_type" json:"request_type" validate:"required,oneof=content_generation chat design optimization analysis"`
	PromptText     string          `gorm:"type:text;not null" json:"prompt_text" validate:"required,min=1,max=10000"`
	ResponseText   *string         `gorm:"type:longtext" json:"response_text,omitempty"`
	
	// Usage and Performance Metrics
	TokensUsed        int             `gorm:"default:0" json:"tokens_used"`
	ProcessingTimeMs  int             `gorm:"default:0" json:"processing_time_ms"`
	CostCents         int             `gorm:"default:0" json:"cost_cents"`
	
	// Status and Error Handling
	Status         AIRequestStatus `gorm:"type:varchar(50);default:'pending';index:idx_ai_requests_status;index:idx_ai_requests_tenant_status" json:"status" validate:"oneof=pending completed failed timeout"`
	ErrorMessage   *string         `gorm:"type:text" json:"error_message,omitempty"`
	
	// Metadata for analytics and tracking
	Metadata       JSONMap         `gorm:"type:json" json:"metadata"`
	
	// Timestamps
	CreatedAt      time.Time       `json:"created_at"`
	
	// Relationships
	Tenant         *Tenant         `gorm:"foreignKey:TenantID" json:"tenant,omitempty"`
	Website        *Website        `gorm:"foreignKey:WebsiteID" json:"website,omitempty"`
	User           *User           `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Model          *AIModel        `gorm:"foreignKey:ModelID" json:"model,omitempty"`
}

// TableName returns the table name for the AIRequest model
func (AIRequest) TableName() string {
	return "ai_requests"
}

// BeforeCreate hook to set default values
func (r *AIRequest) BeforeCreate(tx *gorm.DB) error {
	// Set default status if not provided
	if r.Status == "" {
		r.Status = AIRequestStatusPending
	}
	
	// Initialize metadata if nil
	if r.Metadata == nil {
		r.Metadata = make(JSONMap)
	}
	
	// Set current timestamp for tracking
	r.Metadata["created_at"] = time.Now().UTC()
	
	return nil
}

// BeforeUpdate hook to update metadata
func (r *AIRequest) BeforeUpdate(tx *gorm.DB) error {
	// Update metadata with last modified timestamp
	if r.Metadata == nil {
		r.Metadata = make(JSONMap)
	}
	r.Metadata["updated_at"] = time.Now().UTC()
	
	return nil
}

// IsPending checks if the request is pending
func (r *AIRequest) IsPending() bool {
	return r.Status == AIRequestStatusPending
}

// IsCompleted checks if the request is completed
func (r *AIRequest) IsCompleted() bool {
	return r.Status == AIRequestStatusCompleted
}

// IsFailed checks if the request failed
func (r *AIRequest) IsFailed() bool {
	return r.Status == AIRequestStatusFailed
}

// IsTimeout checks if the request timed out
func (r *AIRequest) IsTimeout() bool {
	return r.Status == AIRequestStatusTimeout
}

// GetCostDollars returns the cost in dollars
func (r *AIRequest) GetCostDollars() float64 {
	return float64(r.CostCents) / 100.0
}

// GetProcessingTimeSeconds returns the processing time in seconds
func (r *AIRequest) GetProcessingTimeSeconds() float64 {
	return float64(r.ProcessingTimeMs) / 1000.0
}

// MarkAsCompleted marks the request as completed with response
func (r *AIRequest) MarkAsCompleted(response string, tokensUsed int, processingTimeMs int, costCents int) {
	r.Status = AIRequestStatusCompleted
	r.ResponseText = &response
	r.TokensUsed = tokensUsed
	r.ProcessingTimeMs = processingTimeMs
	r.CostCents = costCents
	r.ErrorMessage = nil
	
	// Update metadata
	if r.Metadata == nil {
		r.Metadata = make(JSONMap)
	}
	r.Metadata["completed_at"] = time.Now().UTC()
	r.Metadata["success"] = true
}

// MarkAsFailed marks the request as failed with error
func (r *AIRequest) MarkAsFailed(errorMessage string) {
	r.Status = AIRequestStatusFailed
	r.ErrorMessage = &errorMessage
	
	// Update metadata
	if r.Metadata == nil {
		r.Metadata = make(JSONMap)
	}
	r.Metadata["failed_at"] = time.Now().UTC()
	r.Metadata["success"] = false
}

// MarkAsTimeout marks the request as timed out
func (r *AIRequest) MarkAsTimeout() {
	r.Status = AIRequestStatusTimeout
	timeoutMsg := "Request timed out"
	r.ErrorMessage = &timeoutMsg
	
	// Update metadata
	if r.Metadata == nil {
		r.Metadata = make(JSONMap)
	}
	r.Metadata["timeout_at"] = time.Now().UTC()
	r.Metadata["success"] = false
}

// AddMetadata adds a key-value pair to the metadata
func (r *AIRequest) AddMetadata(key string, value interface{}) {
	if r.Metadata == nil {
		r.Metadata = make(JSONMap)
	}
	r.Metadata[key] = value
}

// GetMetadata retrieves a value from metadata
func (r *AIRequest) GetMetadata(key string) (interface{}, bool) {
	if r.Metadata == nil {
		return nil, false
	}
	value, exists := r.Metadata[key]
	return value, exists
}

// Validate validates the AI request
func (r *AIRequest) Validate() error {
	if r.TenantID == 0 {
		return errors.New("tenant_id is required")
	}
	
	if r.ModelID == 0 {
		return errors.New("model_id is required")
	}
	
	if r.RequestType == "" {
		return errors.New("request_type is required")
	}
	
	if r.PromptText == "" {
		return errors.New("prompt_text is required")
	}
	
	if len(r.PromptText) > 10000 {
		return errors.New("prompt_text must be less than 10000 characters")
	}
	
	// Validate request type
	validTypes := []AIRequestType{
		AIRequestTypeContentGeneration,
		AIRequestTypeChat,
		AIRequestTypeDesign,
		AIRequestTypeOptimization,
		AIRequestTypeAnalysis,
	}
	
	isValidType := false
	for _, validType := range validTypes {
		if r.RequestType == validType {
			isValidType = true
			break
		}
	}
	
	if !isValidType {
		return fmt.Errorf("invalid request_type: %s", r.RequestType)
	}
	
	return nil
}

// AIRequestCreateRequest represents the request to create a new AI request
type AIRequestCreateRequest struct {
	TenantID    uint          `json:"tenant_id" validate:"required"`
	WebsiteID   *uint         `json:"website_id,omitempty"`
	UserID      *uint         `json:"user_id,omitempty"`
	ModelID     uint          `json:"model_id" validate:"required"`
	RequestType AIRequestType `json:"request_type" validate:"required,oneof=content_generation chat design optimization analysis"`
	PromptText  string        `json:"prompt_text" validate:"required,min=1,max=10000"`
	Metadata    JSONMap       `json:"metadata,omitempty"`
}

// AIRequestUpdateRequest represents the request to update an AI request
type AIRequestUpdateRequest struct {
	Status       *AIRequestStatus `json:"status,omitempty" validate:"omitempty,oneof=pending completed failed timeout"`
	ResponseText *string          `json:"response_text,omitempty"`
	TokensUsed   *int             `json:"tokens_used,omitempty"`
	ProcessingTimeMs *int         `json:"processing_time_ms,omitempty"`
	CostCents    *int             `json:"cost_cents,omitempty"`
	ErrorMessage *string          `json:"error_message,omitempty"`
	Metadata     JSONMap          `json:"metadata,omitempty"`
}

// AIRequestResponse represents the response when returning AI request data
type AIRequestResponse struct {
	ID               uint            `json:"id"`
	TenantID         uint            `json:"tenant_id"`
	WebsiteID        *uint           `json:"website_id,omitempty"`
	UserID           *uint           `json:"user_id,omitempty"`
	ModelID          uint            `json:"model_id"`
	RequestType      AIRequestType   `json:"request_type"`
	PromptText       string          `json:"prompt_text"`
	ResponseText     *string         `json:"response_text,omitempty"`
	TokensUsed       int             `json:"tokens_used"`
	ProcessingTimeMs int             `json:"processing_time_ms"`
	CostCents        int             `json:"cost_cents"`
	CostDollars      float64         `json:"cost_dollars"`
	Status           AIRequestStatus `json:"status"`
	ErrorMessage     *string         `json:"error_message,omitempty"`
	Metadata         JSONMap         `json:"metadata"`
	CreatedAt        time.Time       `json:"created_at"`
	
	// Relationships
	Tenant   *TenantResponse  `json:"tenant,omitempty"`
	Website  *WebsiteResponse `json:"website,omitempty"`
	User     *UserResponse    `json:"user,omitempty"`
	Model    *AIModelResponse `json:"model,omitempty"`
}

// FromAIRequest converts an AIRequest model to AIRequestResponse
func (r *AIRequestResponse) FromAIRequest(req *AIRequest) {
	r.ID = req.ID
	r.TenantID = req.TenantID
	r.WebsiteID = req.WebsiteID
	r.UserID = req.UserID
	r.ModelID = req.ModelID
	r.RequestType = req.RequestType
	r.PromptText = req.PromptText
	r.ResponseText = req.ResponseText
	r.TokensUsed = req.TokensUsed
	r.ProcessingTimeMs = req.ProcessingTimeMs
	r.CostCents = req.CostCents
	r.CostDollars = req.GetCostDollars()
	r.Status = req.Status
	r.ErrorMessage = req.ErrorMessage
	r.Metadata = req.Metadata
	r.CreatedAt = req.CreatedAt
	
	// Convert relationships if present
	if req.Tenant != nil {
		r.Tenant = &TenantResponse{}
		r.Tenant.FromTenant(req.Tenant)
	}
	if req.Website != nil {
		r.Website = &WebsiteResponse{}
		r.Website.FromWebsite(req.Website)
	}
	if req.User != nil {
		r.User = &UserResponse{}
		r.User.FromUser(req.User)
	}
	if req.Model != nil {
		r.Model = &AIModelResponse{}
		r.Model.FromAIModel(req.Model)
	}
}

// AIRequestFilter represents filter options for AI request queries
type AIRequestFilter struct {
	TenantID       *uint             `json:"tenant_id,omitempty"`
	WebsiteID      *uint             `json:"website_id,omitempty"`
	UserID         *uint             `json:"user_id,omitempty"`
	ModelID        *uint             `json:"model_id,omitempty"`
	RequestType    *AIRequestType    `json:"request_type,omitempty"`
	Status         *AIRequestStatus  `json:"status,omitempty"`
	CreatedAfter   *time.Time        `json:"created_after,omitempty"`
	CreatedBefore  *time.Time        `json:"created_before,omitempty"`
	MinTokens      *int              `json:"min_tokens,omitempty"`
	MaxTokens      *int              `json:"max_tokens,omitempty"`
	MinCost        *int              `json:"min_cost,omitempty"`
	MaxCost        *int              `json:"max_cost,omitempty"`
	MinProcessingTime *int           `json:"min_processing_time,omitempty"`
	MaxProcessingTime *int           `json:"max_processing_time,omitempty"`
}

// AIRequestAnalytics represents analytics data for AI requests
type AIRequestAnalytics struct {
	TotalRequests     int64              `json:"total_requests"`
	CompletedRequests int64              `json:"completed_requests"`
	FailedRequests    int64              `json:"failed_requests"`
	TimeoutRequests   int64              `json:"timeout_requests"`
	TotalTokensUsed   int64              `json:"total_tokens_used"`
	TotalCostCents    int64              `json:"total_cost_cents"`
	TotalCostDollars  float64            `json:"total_cost_dollars"`
	AverageProcessingTime float64        `json:"average_processing_time_ms"`
	RequestsByType    map[string]int64   `json:"requests_by_type"`
	RequestsByStatus  map[string]int64   `json:"requests_by_status"`
	RequestsByHour    map[string]int64   `json:"requests_by_hour"`
	RequestsByDay     map[string]int64   `json:"requests_by_day"`
	TopUsers          []UserUsageStats   `json:"top_users"`
	TopModels         []ModelUsageStats  `json:"top_models"`
}

// UserUsageStats represents usage statistics for a user
type UserUsageStats struct {
	UserID       uint    `json:"user_id"`
	Username     string  `json:"username"`
	RequestCount int64   `json:"request_count"`
	TokensUsed   int64   `json:"tokens_used"`
	CostCents    int64   `json:"cost_cents"`
	CostDollars  float64 `json:"cost_dollars"`
}

// ModelUsageStats represents usage statistics for a model
type ModelUsageStats struct {
	ModelID      uint    `json:"model_id"`
	ModelName    string  `json:"model_name"`
	Provider     string  `json:"provider"`
	RequestCount int64   `json:"request_count"`
	TokensUsed   int64   `json:"tokens_used"`
	CostCents    int64   `json:"cost_cents"`
	CostDollars  float64 `json:"cost_dollars"`
}

// Placeholder types for relationships (these would be defined in their respective modules)
type Tenant struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

type Website struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

type User struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
}

type AIModel struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Provider string `json:"provider"`
}

type TenantResponse struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

func (t *TenantResponse) FromTenant(tenant *Tenant) {
	t.ID = tenant.ID
	t.Name = tenant.Name
}

type WebsiteResponse struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

func (w *WebsiteResponse) FromWebsite(website *Website) {
	w.ID = website.ID
	w.Name = website.Name
}

type UserResponse struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
}

func (u *UserResponse) FromUser(user *User) {
	u.ID = user.ID
	u.Username = user.Username
	u.Email = user.Email
}

type AIModelResponse struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Provider string `json:"provider"`
}

func (m *AIModelResponse) FromAIModel(model *AIModel) {
	m.ID = model.ID
	m.Name = model.Name
	m.Provider = model.Provider
}