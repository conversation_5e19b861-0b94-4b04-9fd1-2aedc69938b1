package mysql

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
)

// mediaFileRepository implements the MediaFileRepository interface
type mediaFileRepository struct {
	db *gorm.DB
}

// NewMediaFileRepository creates a new media file repository
func NewMediaFileRepository(db *gorm.DB) repositories.MediaFileRepository {
	return &mediaFileRepository{db: db}
}

// Create creates a new media file
func (r *mediaFileRepository) Create(ctx context.Context, file *models.MediaFile) error {
	if err := r.db.WithContext(ctx).Create(file).Error; err != nil {
		return fmt.Errorf("failed to create media file: %w", err)
	}
	return nil
}

// GetByID gets a media file by ID
func (r *mediaFileRepository) GetByID(ctx context.Context, id uint) (*models.MediaFile, error) {
	var file models.MediaFile
	if err := r.db.WithContext(ctx).
		Where("id = ? AND status != ?", id, models.FileStatusDeleted).
		First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("media file not found")
		}
		return nil, fmt.Errorf("failed to get media file: %w", err)
	}
	return &file, nil
}

// GetBySlug gets a media file by slug
func (r *mediaFileRepository) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.MediaFile, error) {
	var file models.MediaFile
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND slug = ? AND status != ?", tenantID, websiteID, slug, models.FileStatusDeleted).
		First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("media file not found")
		}
		return nil, fmt.Errorf("failed to get media file: %w", err)
	}
	return &file, nil
}

// GetByHash gets a media file by hash
func (r *mediaFileRepository) GetByHash(ctx context.Context, tenantID, websiteID uint, hash string) (*models.MediaFile, error) {
	var file models.MediaFile
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND file_hash = ? AND status != ?", tenantID, websiteID, hash, models.FileStatusDeleted).
		First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("media file not found")
		}
		return nil, fmt.Errorf("failed to get media file: %w", err)
	}
	return &file, nil
}

// Update updates a media file
func (r *mediaFileRepository) Update(ctx context.Context, file *models.MediaFile) error {
	if err := r.db.WithContext(ctx).Save(file).Error; err != nil {
		return fmt.Errorf("failed to update media file: %w", err)
	}
	return nil
}

// Delete permanently deletes a media file
func (r *mediaFileRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.MediaFile{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete media file: %w", err)
	}
	return nil
}

// SoftDelete soft deletes a media file
func (r *mediaFileRepository) SoftDelete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", id).
		Update("status", models.FileStatusDeleted).Error; err != nil {
		return fmt.Errorf("failed to soft delete media file: %w", err)
	}
	return nil
}

// GetFilesByFolder gets files in a specific folder
func (r *mediaFileRepository) GetFilesByFolder(ctx context.Context, folderID uint, page, pageSize int) ([]models.MediaFile, int64, error) {
	var files []models.MediaFile
	var total int64

	query := r.db.WithContext(ctx).
		Where("folder_id = ? AND status != ?", folderID, models.FileStatusDeleted)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count files in folder: %w", err)
	}

	// Add pagination
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	if err := query.Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get files in folder: %w", err)
	}

	return files, total, nil
}

// GetFilesByUser gets files uploaded by a specific user
func (r *mediaFileRepository) GetFilesByUser(ctx context.Context, userID uint, page, pageSize int) ([]models.MediaFile, int64, error) {
	var files []models.MediaFile
	var total int64

	query := r.db.WithContext(ctx).
		Where("user_id = ? AND status != ?", userID, models.FileStatusDeleted)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count user files: %w", err)
	}

	// Add pagination
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	if err := query.Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get user files: %w", err)
	}

	return files, total, nil
}

// GetFilesByType gets files by type
func (r *mediaFileRepository) GetFilesByType(ctx context.Context, tenantID, websiteID uint, fileType models.FileType, page, pageSize int) ([]models.MediaFile, int64, error) {
	var files []models.MediaFile
	var total int64

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND file_type = ? AND status != ?", tenantID, websiteID, fileType, models.FileStatusDeleted)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count files by type: %w", err)
	}

	// Add pagination
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	if err := query.Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get files by type: %w", err)
	}

	return files, total, nil
}

// GetFilesByStatus gets files by status
func (r *mediaFileRepository) GetFilesByStatus(ctx context.Context, tenantID, websiteID uint, status models.FileStatus) ([]models.MediaFile, error) {
	var files []models.MediaFile
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, status).
		Order("created_at DESC").
		Find(&files).Error; err != nil {
		return nil, fmt.Errorf("failed to get files by status: %w", err)
	}
	return files, nil
}

// Search searches for files based on criteria
func (r *mediaFileRepository) Search(ctx context.Context, req *models.MediaFileSearchRequest) ([]models.MediaFile, int64, error) {
	var files []models.MediaFile
	var total int64

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ?", req.TenantID, req.WebsiteID, models.FileStatusDeleted)

	// Add filters
	if req.FolderID != nil {
		query = query.Where("folder_id = ?", *req.FolderID)
	}
	if req.Query != nil && *req.Query != "" {
		query = query.Where("filename LIKE ? OR original_filename LIKE ? OR alt_text LIKE ? OR title LIKE ? OR description LIKE ?", 
			"%"+*req.Query+"%", "%"+*req.Query+"%", "%"+*req.Query+"%", "%"+*req.Query+"%", "%"+*req.Query+"%")
	}
	if req.FileType != nil {
		query = query.Where("file_type = ?", *req.FileType)
	}
	if req.Category != nil {
		query = query.Where("category = ?", *req.Category)
	}
	if req.MimeType != nil {
		query = query.Where("mime_type = ?", *req.MimeType)
	}
	if req.MinSize != nil {
		query = query.Where("file_size >= ?", *req.MinSize)
	}
	if req.MaxSize != nil {
		query = query.Where("file_size <= ?", *req.MaxSize)
	}
	if req.CreatedAfter != nil {
		query = query.Where("created_at >= ?", *req.CreatedAfter)
	}
	if req.CreatedBefore != nil {
		query = query.Where("created_at <= ?", *req.CreatedBefore)
	}

	// Handle tag filtering
	if len(req.Tags) > 0 {
		query = query.Joins("JOIN media_file_tags ON media_files.id = media_file_tags.file_id").
			Joins("JOIN media_tags ON media_file_tags.tag_id = media_tags.id").
			Where("media_tags.name IN ?", req.Tags).
			Group("media_files.id")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count files: %w", err)
	}

	// Add sorting
	sortBy := "created_at"
	if req.SortBy != nil && *req.SortBy != "" {
		sortBy = *req.SortBy
	}
	sortOrder := "DESC"
	if req.SortOrder != nil && strings.ToUpper(*req.SortOrder) == "ASC" {
		sortOrder = "ASC"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Add pagination
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	if err := query.Find(&files).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to search files: %w", err)
	}

	return files, total, nil
}

// SearchByTags searches for files by tags
func (r *mediaFileRepository) SearchByTags(ctx context.Context, tenantID, websiteID uint, tagIDs []uint, page, pageSize int) ([]models.MediaFile, int64, error) {
	var files []models.MediaFile
	var total int64

	query := r.db.WithContext(ctx).
		Joins("JOIN media_file_tags ON media_files.id = media_file_tags.file_id").
		Where("media_files.tenant_id = ? AND media_files.website_id = ? AND media_files.status != ? AND media_file_tags.tag_id IN ?", 
			tenantID, websiteID, models.FileStatusDeleted, tagIDs).
		Group("media_files.id")

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count files by tags: %w", err)
	}

	// Add pagination
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	if err := query.Order("media_files.created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to search files by tags: %w", err)
	}

	return files, total, nil
}

// GetSimilarFiles gets similar files based on tags and metadata
func (r *mediaFileRepository) GetSimilarFiles(ctx context.Context, fileID uint, minSimilarity float64, limit int) ([]models.MediaFile, error) {
	var files []models.MediaFile
	
	// This is a simplified implementation - in production you'd want more sophisticated similarity matching
	query := r.db.WithContext(ctx).
		Table("media_files").
		Joins("JOIN media_file_tags mft1 ON media_files.id = mft1.file_id").
		Joins("JOIN media_file_tags mft2 ON mft1.tag_id = mft2.tag_id").
		Where("mft2.file_id = ? AND media_files.id != ? AND media_files.status != ?", fileID, fileID, models.FileStatusDeleted).
		Group("media_files.id").
		Order("COUNT(mft1.tag_id) DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&files).Error; err != nil {
		return nil, fmt.Errorf("failed to get similar files: %w", err)
	}

	return files, nil
}

// MoveFileTo moves a file to a different folder
func (r *mediaFileRepository) MoveFileTo(ctx context.Context, fileID, newFolderID uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", fileID).
		Update("folder_id", newFolderID).Error; err != nil {
		return fmt.Errorf("failed to move file: %w", err)
	}
	return nil
}

// IncrementViewCount increments the view count for a file
func (r *mediaFileRepository) IncrementViewCount(ctx context.Context, fileID uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", fileID).
		Update("view_count", gorm.Expr("view_count + 1")).Error; err != nil {
		return fmt.Errorf("failed to increment view count: %w", err)
	}
	return nil
}

// IncrementDownloadCount increments the download count for a file
func (r *mediaFileRepository) IncrementDownloadCount(ctx context.Context, fileID uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", fileID).
		Update("download_count", gorm.Expr("download_count + 1")).Error; err != nil {
		return fmt.Errorf("failed to increment download count: %w", err)
	}
	return nil
}

// UpdateLastAccessed updates the last accessed time for a file
func (r *mediaFileRepository) UpdateLastAccessed(ctx context.Context, fileID uint) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id = ?", fileID).
		Update("last_accessed_at", now).Error; err != nil {
		return fmt.Errorf("failed to update last accessed time: %w", err)
	}
	return nil
}

// GetFileStats gets file statistics
func (r *mediaFileRepository) GetFileStats(ctx context.Context, tenantID, websiteID uint) (*models.MediaFileStats, error) {
	var stats models.MediaFileStats

	// Get total files
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.FileStatusDeleted).
		Count(&stats.TotalFiles).Error; err != nil {
		return nil, fmt.Errorf("failed to count total files: %w", err)
	}

	// Get total size
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.FileStatusDeleted).
		Select("COALESCE(SUM(file_size), 0)").
		Scan(&stats.TotalSize).Error; err != nil {
		return nil, fmt.Errorf("failed to sum file sizes: %w", err)
	}

	// Get file counts by type
	var typeStats []struct {
		FileType models.FileType `json:"file_type"`
		Count    uint           `json:"count"`
	}
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Select("file_type, COUNT(*) as count").
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.FileStatusDeleted).
		Group("file_type").
		Find(&typeStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get file type stats: %w", err)
	}

	for _, stat := range typeStats {
		switch stat.FileType {
		case models.FileTypeImage:
			stats.ImageFiles = stat.Count
		case models.FileTypeVideo:
			stats.VideoFiles = stat.Count
		case models.FileTypeAudio:
			stats.AudioFiles = stat.Count
		case models.FileTypeDocument:
			stats.DocumentFiles = stat.Count
		case models.FileTypeArchive:
			stats.ArchiveFiles = stat.Count
		case models.FileTypeOther:
			stats.OtherFiles = stat.Count
		}
	}

	stats.StorageUsed = stats.TotalSize
	// You would set StorageLimit based on tenant/website plan
	stats.StorageLimit = 1024 * 1024 * 1024 * 10 // 10GB default
	if stats.StorageLimit > 0 {
		stats.StoragePercent = float64(stats.StorageUsed) / float64(stats.StorageLimit) * 100
	}

	return &stats, nil
}

// GetStorageUsage gets storage usage for a tenant/website
func (r *mediaFileRepository) GetStorageUsage(ctx context.Context, tenantID, websiteID uint) (uint64, error) {
	var totalSize uint64
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.FileStatusDeleted).
		Select("COALESCE(SUM(file_size), 0)").
		Scan(&totalSize).Error; err != nil {
		return 0, fmt.Errorf("failed to get storage usage: %w", err)
	}
	return totalSize, nil
}

// GetFileCountByType gets file count by type
func (r *mediaFileRepository) GetFileCountByType(ctx context.Context, tenantID, websiteID uint) (map[string]uint, error) {
	var typeStats []struct {
		FileType string `json:"file_type"`
		Count    uint   `json:"count"`
	}
	
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Select("file_type, COUNT(*) as count").
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.FileStatusDeleted).
		Group("file_type").
		Find(&typeStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get file count by type: %w", err)
	}

	result := make(map[string]uint)
	for _, stat := range typeStats {
		result[stat.FileType] = stat.Count
	}

	return result, nil
}

// BulkDelete bulk deletes files
func (r *mediaFileRepository) BulkDelete(ctx context.Context, fileIDs []uint) error {
	if len(fileIDs) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Where("id IN ?", fileIDs).
		Delete(&models.MediaFile{}).Error; err != nil {
		return fmt.Errorf("failed to bulk delete files: %w", err)
	}
	return nil
}

// BulkUpdateStatus bulk updates file status
func (r *mediaFileRepository) BulkUpdateStatus(ctx context.Context, fileIDs []uint, status models.FileStatus) error {
	if len(fileIDs) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id IN ?", fileIDs).
		Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to bulk update file status: %w", err)
	}
	return nil
}

// BulkMoveFiles bulk moves files to a new folder
func (r *mediaFileRepository) BulkMoveFiles(ctx context.Context, fileIDs []uint, newFolderID uint) error {
	if len(fileIDs) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id IN ?", fileIDs).
		Update("folder_id", newFolderID).Error; err != nil {
		return fmt.Errorf("failed to bulk move files: %w", err)
	}
	return nil
}

// BulkUpdateMetadata bulk updates file metadata
func (r *mediaFileRepository) BulkUpdateMetadata(ctx context.Context, fileIDs []uint, metadata map[string]interface{}) error {
	if len(fileIDs) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("id IN ?", fileIDs).
		Update("metadata", metadata).Error; err != nil {
		return fmt.Errorf("failed to bulk update metadata: %w", err)
	}
	return nil
}

// GetDuplicateFiles gets duplicate files based on hash
func (r *mediaFileRepository) GetDuplicateFiles(ctx context.Context, tenantID, websiteID uint) ([]models.MediaFile, error) {
	var files []models.MediaFile
	
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ? AND file_hash IN (?)", 
			tenantID, websiteID, models.FileStatusDeleted,
			r.db.Model(&models.MediaFile{}).
				Select("file_hash").
				Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.FileStatusDeleted).
				Group("file_hash").
				Having("COUNT(*) > 1")).
		Order("file_hash, created_at").
		Find(&files).Error; err != nil {
		return nil, fmt.Errorf("failed to get duplicate files: %w", err)
	}
	
	return files, nil
}

// GetFilesByHashes gets files by their hashes
func (r *mediaFileRepository) GetFilesByHashes(ctx context.Context, tenantID, websiteID uint, hashes []string) ([]models.MediaFile, error) {
	var files []models.MediaFile
	
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND file_hash IN ? AND status != ?", 
			tenantID, websiteID, hashes, models.FileStatusDeleted).
		Find(&files).Error; err != nil {
		return nil, fmt.Errorf("failed to get files by hashes: %w", err)
	}
	
	return files, nil
}