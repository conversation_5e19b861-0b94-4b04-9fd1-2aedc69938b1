package services

import (
	"context"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"path/filepath"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/storage"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/sirupsen/logrus"
)

// ThumbnailSize represents predefined thumbnail sizes
type ThumbnailSize struct {
	Name   string
	Width  int
	Height int
}

// Predefined thumbnail sizes
var (
	ThumbnailSizeSmall  = ThumbnailSize{"small", 150, 150}
	ThumbnailSizeMedium = ThumbnailSize{"medium", 300, 300}
	ThumbnailSizeLarge  = ThumbnailSize{"large", 600, 600}
)

// MediaThumbnailService handles thumbnail generation and management
type MediaThumbnailService struct {
	thumbnailRepo repositories.MediaThumbnailRepository
	fileRepo      repositories.MediaFileRepository
	storageClient storage.StorageClient
	logger        *logrus.Logger
}

// NewMediaThumbnailService creates a new media thumbnail service
func NewMediaThumbnailService(
	thumbnailRepo repositories.MediaThumbnailRepository,
	fileRepo repositories.MediaFileRepository,
	storageClient storage.StorageClient,
	logger *logrus.Logger,
) *MediaThumbnailService {
	return &MediaThumbnailService{
		thumbnailRepo: thumbnailRepo,
		fileRepo:      fileRepo,
		storageClient: storageClient,
		logger:        logger,
	}
}

// GenerateThumbnails generates thumbnails for a media file
func (s *MediaThumbnailService) GenerateThumbnails(ctx context.Context, fileID uint, sizes []ThumbnailSize) ([]*models.MediaThumbnail, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get the original file
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get media file: %w", err)
	}

	// Check if file is an image
	if !utils.IsImageFile(file.Filename) {
		return nil, fmt.Errorf("file is not an image: %s", file.Filename)
	}

	// Download original file
	reader, err := s.storageClient.Download(ctx, file.StoragePath)
	if err != nil {
		return nil, fmt.Errorf("failed to download original file: %w", err)
	}
	defer reader.Close()

	// Decode image
	img, format, err := image.Decode(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	var thumbnails []*models.MediaThumbnail

	// Generate thumbnails for each size
	for _, size := range sizes {
		thumbnail, err := s.generateThumbnail(ctx, file, img, format, size)
		if err != nil {
			s.logger.WithError(err).WithFields(logrus.Fields{
				"file_id": fileID,
				"size":    size.Name,
			}).Error("Failed to generate thumbnail")
			continue
		}

		thumbnails = append(thumbnails, thumbnail)
	}

	s.logger.WithFields(logrus.Fields{
		"file_id":          fileID,
		"thumbnails_count": len(thumbnails),
	}).Info("Thumbnails generated successfully")

	return thumbnails, nil
}

// GenerateStandardThumbnails generates standard thumbnails (small, medium, large)
func (s *MediaThumbnailService) GenerateStandardThumbnails(ctx context.Context, fileID uint) ([]*models.MediaThumbnail, error) {
	sizes := []ThumbnailSize{
		ThumbnailSizeSmall,
		ThumbnailSizeMedium,
		ThumbnailSizeLarge,
	}

	return s.GenerateThumbnails(ctx, fileID, sizes)
}

// GenerateCustomThumbnail generates a custom-sized thumbnail
func (s *MediaThumbnailService) GenerateCustomThumbnail(ctx context.Context, fileID uint, width, height int, name string) (*models.MediaThumbnail, error) {
	size := ThumbnailSize{
		Name:   name,
		Width:  width,
		Height: height,
	}

	thumbnails, err := s.GenerateThumbnails(ctx, fileID, []ThumbnailSize{size})
	if err != nil {
		return nil, err
	}

	if len(thumbnails) == 0 {
		return nil, fmt.Errorf("failed to generate custom thumbnail")
	}

	return thumbnails[0], nil
}

// GetThumbnail retrieves a thumbnail by ID
func (s *MediaThumbnailService) GetThumbnail(ctx context.Context, id uint) (*models.MediaThumbnail, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	thumbnail, err := s.thumbnailRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnail: %w", err)
	}

	return thumbnail, nil
}

// GetThumbnailsByFileID retrieves all thumbnails for a media file
func (s *MediaThumbnailService) GetThumbnailsByFileID(ctx context.Context, fileID uint) ([]*models.MediaThumbnail, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	thumbnails, err := s.thumbnailRepo.GetByFileID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnails: %w", err)
	}

	return thumbnails, nil
}

// GetThumbnailBySize retrieves a thumbnail by file ID and size
func (s *MediaThumbnailService) GetThumbnailBySize(ctx context.Context, fileID uint, size string) (*models.MediaThumbnail, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	thumbnail, err := s.thumbnailRepo.GetByFileIDAndSize(ctx, tenantCtx.TenantID, fileID, size)
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnail: %w", err)
	}

	return thumbnail, nil
}

// UpdateThumbnail updates a thumbnail's metadata
func (s *MediaThumbnailService) UpdateThumbnail(ctx context.Context, id uint, req *models.UpdateThumbnailRequest) (*models.MediaThumbnail, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	thumbnail, err := s.thumbnailRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnail: %w", err)
	}

	// Update fields
	if req.Size != nil {
		thumbnail.Size = *req.Size
	}
	if req.Width != nil {
		thumbnail.Width = *req.Width
	}
	if req.Height != nil {
		thumbnail.Height = *req.Height
	}
	if req.Quality != nil {
		thumbnail.Quality = *req.Quality
	}
	if req.Format != nil {
		thumbnail.Format = *req.Format
	}

	thumbnail.UpdatedAt = time.Now()

	err = s.thumbnailRepo.Update(ctx, thumbnail)
	if err != nil {
		return nil, fmt.Errorf("failed to update thumbnail: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":    tenantCtx.TenantID,
		"thumbnail_id": id,
	}).Info("Thumbnail updated successfully")

	return thumbnail, nil
}

// DeleteThumbnail deletes a thumbnail
func (s *MediaThumbnailService) DeleteThumbnail(ctx context.Context, id uint) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	thumbnail, err := s.thumbnailRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get thumbnail: %w", err)
	}

	// Delete from storage
	err = s.storageClient.Delete(ctx, thumbnail.StoragePath)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to delete thumbnail from storage")
	}

	// Delete from database
	err = s.thumbnailRepo.Delete(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return fmt.Errorf("failed to delete thumbnail: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":    tenantCtx.TenantID,
		"thumbnail_id": id,
	}).Info("Thumbnail deleted successfully")

	return nil
}

// DeleteThumbnailsByFileID deletes all thumbnails for a media file
func (s *MediaThumbnailService) DeleteThumbnailsByFileID(ctx context.Context, fileID uint) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	thumbnails, err := s.thumbnailRepo.GetByFileID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return fmt.Errorf("failed to get thumbnails: %w", err)
	}

	for _, thumbnail := range thumbnails {
		err = s.DeleteThumbnail(ctx, thumbnail.ID)
		if err != nil {
			s.logger.WithError(err).WithField("thumbnail_id", thumbnail.ID).Error("Failed to delete thumbnail")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
		"count":     len(thumbnails),
	}).Info("Thumbnails deleted successfully")

	return nil
}

// RegenerateThumbnails regenerates all thumbnails for a media file
func (s *MediaThumbnailService) RegenerateThumbnails(ctx context.Context, fileID uint) ([]*models.MediaThumbnail, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get existing thumbnails to determine sizes
	existingThumbnails, err := s.thumbnailRepo.GetByFileID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing thumbnails: %w", err)
	}

	// Delete existing thumbnails
	for _, thumbnail := range existingThumbnails {
		err = s.DeleteThumbnail(ctx, thumbnail.ID)
		if err != nil {
			s.logger.WithError(err).WithField("thumbnail_id", thumbnail.ID).Error("Failed to delete existing thumbnail")
		}
	}

	// Generate new thumbnails
	var sizes []ThumbnailSize
	if len(existingThumbnails) == 0 {
		// If no existing thumbnails, generate standard sizes
		sizes = []ThumbnailSize{
			ThumbnailSizeSmall,
			ThumbnailSizeMedium,
			ThumbnailSizeLarge,
		}
	} else {
		// Use existing sizes
		for _, thumbnail := range existingThumbnails {
			sizes = append(sizes, ThumbnailSize{
				Name:   thumbnail.Size,
				Width:  thumbnail.Width,
				Height: thumbnail.Height,
			})
		}
	}

	thumbnails, err := s.GenerateThumbnails(ctx, fileID, sizes)
	if err != nil {
		return nil, fmt.Errorf("failed to regenerate thumbnails: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
		"count":     len(thumbnails),
	}).Info("Thumbnails regenerated successfully")

	return thumbnails, nil
}

// ListThumbnails lists thumbnails with filtering and pagination
func (s *MediaThumbnailService) ListThumbnails(ctx context.Context, filter *models.ThumbnailFilter, limit, offset int) ([]*models.MediaThumbnail, int, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, 0, fmt.Errorf("tenant context is required")
	}

	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	thumbnails, total, err := s.thumbnailRepo.List(ctx, tenantCtx.TenantID, filter, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list thumbnails: %w", err)
	}

	return thumbnails, total, nil
}

// OptimizeThumbnails optimizes thumbnails by recompressing with better quality/size ratio
func (s *MediaThumbnailService) OptimizeThumbnails(ctx context.Context, fileID uint, quality int) ([]*models.MediaThumbnail, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	if quality < 1 || quality > 100 {
		quality = 80 // Default quality
	}

	thumbnails, err := s.thumbnailRepo.GetByFileID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnails: %w", err)
	}

	var optimizedThumbnails []*models.MediaThumbnail

	for _, thumbnail := range thumbnails {
		optimized, err := s.optimizeThumbnail(ctx, thumbnail, quality)
		if err != nil {
			s.logger.WithError(err).WithField("thumbnail_id", thumbnail.ID).Error("Failed to optimize thumbnail")
			continue
		}

		optimizedThumbnails = append(optimizedThumbnails, optimized)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
		"count":     len(optimizedThumbnails),
		"quality":   quality,
	}).Info("Thumbnails optimized successfully")

	return optimizedThumbnails, nil
}

// Private helper methods

// generateThumbnail generates a single thumbnail
func (s *MediaThumbnailService) generateThumbnail(ctx context.Context, file *models.MediaFile, img image.Image, format string, size ThumbnailSize) (*models.MediaThumbnail, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Resize image
	resizedImg := s.resizeImage(img, size.Width, size.Height)

	// Generate thumbnail filename
	ext := filepath.Ext(file.Filename)
	baseName := strings.TrimSuffix(file.Filename, ext)
	thumbnailFilename := fmt.Sprintf("%s_%s%s", baseName, size.Name, ext)

	// Encode image
	var buf strings.Builder
	quality := 80 // Default quality

	switch strings.ToLower(format) {
	case "jpeg", "jpg":
		err := jpeg.Encode(&buf, resizedImg, &jpeg.Options{Quality: quality})
		if err != nil {
			return nil, fmt.Errorf("failed to encode JPEG: %w", err)
		}
	case "png":
		err := png.Encode(&buf, resizedImg)
		if err != nil {
			return nil, fmt.Errorf("failed to encode PNG: %w", err)
		}
	default:
		// Default to JPEG
		err := jpeg.Encode(&buf, resizedImg, &jpeg.Options{Quality: quality})
		if err != nil {
			return nil, fmt.Errorf("failed to encode image: %w", err)
		}
		format = "jpeg"
	}

	// Upload thumbnail
	storagePath := fmt.Sprintf("thumbnails/%d/%s", tenantCtx.TenantID, thumbnailFilename)
	mimeType := utils.GetMimeType(thumbnailFilename)
	
	reader := strings.NewReader(buf.String())
	uploadedFile, err := s.storageClient.Upload(ctx, storagePath, reader, mimeType)
	if err != nil {
		return nil, fmt.Errorf("failed to upload thumbnail: %w", err)
	}

	// Create thumbnail record
	thumbnail := &models.MediaThumbnail{
		TenantID:    tenantCtx.TenantID,
		FileID:      file.ID,
		Size:        size.Name,
		Width:       size.Width,
		Height:      size.Height,
		FileSize:    int64(len(buf.String())),
		Format:      format,
		Quality:     quality,
		StoragePath: storagePath,
		URL:         uploadedFile.URL,
		CDNUrl:      uploadedFile.CDNURL,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = s.thumbnailRepo.Create(ctx, thumbnail)
	if err != nil {
		// Clean up uploaded file
		s.storageClient.Delete(ctx, storagePath)
		return nil, fmt.Errorf("failed to create thumbnail record: %w", err)
	}

	return thumbnail, nil
}

// resizeImage resizes an image to the specified dimensions
func (s *MediaThumbnailService) resizeImage(img image.Image, width, height int) image.Image {
	// Simple resize implementation
	// In production, you would use a proper image processing library
	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	// Calculate aspect ratio
	aspectRatio := float64(originalWidth) / float64(originalHeight)
	targetAspect := float64(width) / float64(height)

	var newWidth, newHeight int
	if aspectRatio > targetAspect {
		// Original is wider
		newWidth = width
		newHeight = int(float64(width) / aspectRatio)
	} else {
		// Original is taller
		newHeight = height
		newWidth = int(float64(height) * aspectRatio)
	}

	// For now, return the original image
	// In production, implement actual resizing
	return img
}

// optimizeThumbnail optimizes a thumbnail by recompressing
func (s *MediaThumbnailService) optimizeThumbnail(ctx context.Context, thumbnail *models.MediaThumbnail, quality int) (*models.MediaThumbnail, error) {
	// Download existing thumbnail
	reader, err := s.storageClient.Download(ctx, thumbnail.StoragePath)
	if err != nil {
		return nil, fmt.Errorf("failed to download thumbnail: %w", err)
	}
	defer reader.Close()

	// Decode image
	img, _, err := image.Decode(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to decode thumbnail: %w", err)
	}

	// Re-encode with new quality
	var buf strings.Builder
	if thumbnail.Format == "jpeg" || thumbnail.Format == "jpg" {
		err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
	} else if thumbnail.Format == "png" {
		err = png.Encode(&buf, img)
	} else {
		err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
	}

	if err != nil {
		return nil, fmt.Errorf("failed to encode optimized thumbnail: %w", err)
	}

	// Upload optimized thumbnail
	mimeType := utils.GetMimeType(thumbnail.StoragePath)
	optimizedReader := strings.NewReader(buf.String())
	uploadedFile, err := s.storageClient.Upload(ctx, thumbnail.StoragePath, optimizedReader, mimeType)
	if err != nil {
		return nil, fmt.Errorf("failed to upload optimized thumbnail: %w", err)
	}

	// Update thumbnail record
	thumbnail.FileSize = int64(len(buf.String()))
	thumbnail.Quality = quality
	thumbnail.URL = uploadedFile.URL
	thumbnail.CDNUrl = uploadedFile.CDNURL
	thumbnail.UpdatedAt = time.Now()

	err = s.thumbnailRepo.Update(ctx, thumbnail)
	if err != nil {
		return nil, fmt.Errorf("failed to update optimized thumbnail: %w", err)
	}

	return thumbnail, nil
}