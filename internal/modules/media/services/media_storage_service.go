package services

import (
	"context"
	"fmt"
	"io"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/storage"
	"github.com/sirupsen/logrus"
)

// MediaStorageService handles storage operations with multiple backends
type MediaStorageService struct {
	storageProviders map[string]storage.Storage
	defaultProvider  string
	fileRepo         repositories.MediaFileRepository
	logger           *logrus.Logger
}

// NewMediaStorageService creates a new media storage service
func NewMediaStorageService(
	providers map[string]storage.Storage,
	defaultProvider string,
	fileRepo repositories.MediaFileRepository,
	logger *logrus.Logger,
) *MediaStorageService {
	return &MediaStorageService{
		storageProviders: providers,
		defaultProvider:  defaultProvider,
		fileRepo:         fileRepo,
		logger:           logger,
	}
}

// Upload uploads a file to the specified storage backend
func (s *MediaStorageService) Upload(ctx context.Context, req *models.UploadRequest) (*models.StorageResult, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Validate request
	if req.Reader == nil {
		return nil, fmt.Errorf("file reader is required")
	}
	if req.Path == "" {
		return nil, fmt.Errorf("storage path is required")
	}

	// Get storage provider
	provider := s.getStorageProvider(req.StorageProvider)
	if provider == nil {
		return nil, fmt.Errorf("storage provider not found: %s", req.StorageProvider)
	}

	// Upload file
	uploadedFile, err := provider.Upload(ctx, req.Path, req.Reader, req.MimeType)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	result := &models.StorageResult{
		Path:            req.Path,
		URL:             uploadedFile.URL,
		CDNURL:          uploadedFile.CDNURL,
		Size:            uploadedFile.Size,
		StorageProvider: req.StorageProvider,
		Metadata:        uploadedFile.Metadata,
		UploadedAt:      time.Now(),
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":        tenantCtx.TenantID,
		"storage_provider": req.StorageProvider,
		"path":             req.Path,
		"size":             uploadedFile.Size,
	}).Info("File uploaded successfully")

	return result, nil
}

// Download downloads a file from storage
func (s *MediaStorageService) Download(ctx context.Context, fileID uint) (io.ReadCloser, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get file record
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	// Get storage provider
	provider := s.getStorageProvider(file.StorageProvider)
	if provider == nil {
		provider = s.getStorageProvider(s.defaultProvider)
	}

	if provider == nil {
		return nil, fmt.Errorf("no storage provider available")
	}

	// Download file
	reader, err := provider.Download(ctx, file.StoragePath)
	if err != nil {
		return nil, fmt.Errorf("failed to download file: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
		"path":      file.StoragePath,
	}).Info("File downloaded successfully")

	return reader, nil
}

// Delete deletes a file from storage
func (s *MediaStorageService) Delete(ctx context.Context, fileID uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Get file record
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return fmt.Errorf("failed to get file: %w", err)
	}

	// Get storage provider
	provider := s.getStorageProvider(file.StorageProvider)
	if provider == nil {
		provider = s.getStorageProvider(s.defaultProvider)
	}

	if provider == nil {
		return fmt.Errorf("no storage provider available")
	}

	// Delete file
	err = provider.Delete(ctx, file.StoragePath)
	if err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
		"path":      file.StoragePath,
	}).Info("File deleted successfully")

	return nil
}

// Move moves a file between storage providers
func (s *MediaStorageService) Move(ctx context.Context, fileID uint, targetProvider string) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Get file record
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return fmt.Errorf("failed to get file: %w", err)
	}

	// Check if already on target provider
	if file.StorageProvider == targetProvider {
		return nil
	}

	// Get source provider
	sourceProvider := s.getStorageProvider(file.StorageProvider)
	if sourceProvider == nil {
		sourceProvider = s.getStorageProvider(s.defaultProvider)
	}

	// Get target provider
	targetProviderClient := s.getStorageProvider(targetProvider)
	if targetProviderClient == nil {
		return fmt.Errorf("target storage provider not found: %s", targetProvider)
	}

	// Download from source
	reader, err := sourceProvider.Download(ctx, file.StoragePath)
	if err != nil {
		return fmt.Errorf("failed to download from source: %w", err)
	}
	defer reader.Close()

	// Upload to target
	uploadedFile, err := targetProviderClient.Upload(ctx, file.StoragePath, reader, file.MimeType)
	if err != nil {
		return fmt.Errorf("failed to upload to target: %w", err)
	}

	// Update file record
	file.StorageProvider = targetProvider
	file.URL = uploadedFile.URL
	file.CDNUrl = uploadedFile.CDNURL
	file.UpdatedAt = time.Now()

	err = s.fileRepo.Update(ctx, file)
	if err != nil {
		return fmt.Errorf("failed to update file record: %w", err)
	}

	// Delete from source
	err = sourceProvider.Delete(ctx, file.StoragePath)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to delete from source after move")
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":       tenantCtx.TenantID,
		"file_id":         fileID,
		"source_provider": file.StorageProvider,
		"target_provider": targetProvider,
	}).Info("File moved successfully")

	return nil
}

// GetStorageInfo gets storage information and usage statistics
func (s *MediaStorageService) GetStorageInfo(ctx context.Context) (*models.StorageInfo, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get file statistics
	stats, err := s.fileRepo.GetStatistics(ctx, tenantCtx.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file statistics: %w", err)
	}

	// Get provider usage
	providerUsage, err := s.fileRepo.GetStorageUsageByProvider(ctx, tenantCtx.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get provider usage: %w", err)
	}

	info := &models.StorageInfo{
		TotalFiles:     stats.TotalFiles,
		TotalSize:      stats.TotalSize,
		ProviderUsage:  providerUsage,
		Providers:      s.getAvailableProviders(),
		DefaultProvider: s.defaultProvider,
	}

	return info, nil
}

// CleanupOrphanedFiles removes files that exist in storage but not in database
func (s *MediaStorageService) CleanupOrphanedFiles(ctx context.Context, provider string) (int, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return 0, fmt.Errorf("tenant context is required")
	}

	// Get storage provider
	storageProvider := s.getStorageProvider(provider)
	if storageProvider == nil {
		return 0, fmt.Errorf("storage provider not found: %s", provider)
	}

	// This would typically involve:
	// 1. Listing all files in storage
	// 2. Comparing with database records
	// 3. Deleting orphaned files
	// For now, return 0 as this requires storage provider list capabilities

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"provider":  provider,
	}).Info("Cleanup orphaned files completed")

	return 0, nil
}

// SyncStorageWithDatabase syncs storage state with database
func (s *MediaStorageService) SyncStorageWithDatabase(ctx context.Context, provider string) (*models.SyncResult, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get storage provider
	storageProvider := s.getStorageProvider(provider)
	if storageProvider == nil {
		return nil, fmt.Errorf("storage provider not found: %s", provider)
	}

	// Get all files from database for this provider
	filter := &models.MediaFileFilter{
		StorageProvider: &provider,
		Status:          stringPtr("active"),
	}

	files, _, err := s.fileRepo.List(ctx, tenantCtx.TenantID, filter, 1000, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get files: %w", err)
	}

	result := &models.SyncResult{
		TotalFiles:     len(files),
		ValidFiles:     0,
		InvalidFiles:   0,
		MissingFiles:   0,
		OrphanedFiles:  0,
		Provider:       provider,
		SyncedAt:       time.Now(),
	}

	// Check each file
	for _, file := range files {
		// Try to get file info from storage
		exists, err := s.fileExists(ctx, storageProvider, file.StoragePath)
		if err != nil {
			s.logger.WithError(err).WithField("file_id", file.ID).Warn("Failed to check file existence")
			continue
		}

		if exists {
			result.ValidFiles++
		} else {
			result.MissingFiles++
			// Mark file as invalid
			file.Status = "invalid"
			file.UpdatedAt = time.Now()
			s.fileRepo.Update(ctx, file)
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":     tenantCtx.TenantID,
		"provider":      provider,
		"total_files":   result.TotalFiles,
		"valid_files":   result.ValidFiles,
		"missing_files": result.MissingFiles,
	}).Info("Storage sync completed")

	return result, nil
}

// Private helper methods

// getStorageProvider gets a storage provider by name
func (s *MediaStorageService) getStorageProvider(name string) storage.Storage {
	if name == "" {
		name = s.defaultProvider
	}
	return s.storageProviders[name]
}

// getAvailableProviders returns list of available storage providers
func (s *MediaStorageService) getAvailableProviders() []string {
	providers := make([]string, 0, len(s.storageProviders))
	for name := range s.storageProviders {
		providers = append(providers, name)
	}
	return providers
}

// fileExists checks if a file exists in storage
func (s *MediaStorageService) fileExists(ctx context.Context, provider storage.Storage, path string) (bool, error) {
	// This would use storage provider's exists method if available
	// For now, try to get file info
	_, err := provider.Download(ctx, path)
	if err != nil {
		return false, nil
	}
	return true, nil
}

// Helper function
func stringPtr(s string) *string {
	return &s
}