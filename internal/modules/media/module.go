package media

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/storage"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// Module represents the media module
type Module struct {
	db                     *gorm.DB
	storageProviders       map[string]storage.Storage
	defaultStorageProvider string
	validator              validator.Validator
	logger                 *logrus.Logger
}

// NewModule creates a new media module instance
func NewModule(
	db *gorm.DB,
	storageProviders map[string]storage.Storage,
	defaultStorageProvider string,
	validator validator.Validator,
	logger *logrus.Logger,
) *Module {
	return &Module{
		db:                     db,
		storageProviders:       storageProviders,
		defaultStorageProvider: defaultStorageProvider,
		validator:              validator,
		logger:                 logger,
	}
}

// RegisterRoutes registers the media module routes
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	RegisterRoutes(
		router,
		m.db,
		m.storageProviders,
		m.defaultStorageProvider,
		m.validator,
		m.logger,
	)
}

// Migrate runs the database migrations for the media module
func (m *Module) Migrate() error {
	// Auto-migrate media tables
	if err := m.db.AutoMigrate(
		&models.MediaFile{},
		&models.MediaFolder{},
		&models.MediaThumbnail{},
	); err != nil {
		return err
	}

	return nil
}

// GetName returns the module name
func (m *Module) GetName() string {
	return "media"
}

// GetVersion returns the module version
func (m *Module) GetVersion() string {
	return "1.0.0"
}

// GetDescription returns the module description
func (m *Module) GetDescription() string {
	return "Media file management module with multi-tenant and multi-website support"
}

// GetRepositories returns the module repositories
func (m *Module) GetRepositories() map[string]interface{} {
	return map[string]interface{}{
		"file":      repositories.NewMediaFileRepository(m.db),
		"folder":    repositories.NewMediaFolderRepository(m.db),
		"thumbnail": repositories.NewMediaThumbnailRepository(m.db),
	}
}

// GetFeatures returns the list of features supported by this module
func (m *Module) GetFeatures() []string {
	return []string{
		"file_upload",
		"multi_file_upload",
		"folder_management",
		"thumbnail_generation",
		"storage_abstraction",
		"multi_tenant_isolation",
		"multi_website_support",
		"file_optimization",
		"access_control",
		"storage_statistics",
		"cdn_integration",
	}
}

// GetStorageProviders returns the available storage providers
func (m *Module) GetStorageProviders() []string {
	providers := make([]string, 0, len(m.storageProviders))
	for name := range m.storageProviders {
		providers = append(providers, name)
	}
	return providers
}

// GetDefaultStorageProvider returns the default storage provider
func (m *Module) GetDefaultStorageProvider() string {
	return m.defaultStorageProvider
}

// ValidateConfig validates the module configuration
func (m *Module) ValidateConfig() error {
	if m.db == nil {
		return fmt.Errorf("database connection is required")
	}
	
	if len(m.storageProviders) == 0 {
		return fmt.Errorf("at least one storage provider is required")
	}
	
	if m.defaultStorageProvider == "" {
		return fmt.Errorf("default storage provider must be specified")
	}
	
	if _, exists := m.storageProviders[m.defaultStorageProvider]; !exists {
		return fmt.Errorf("default storage provider '%s' not found in available providers", m.defaultStorageProvider)
	}
	
	if m.validator == nil {
		return fmt.Errorf("validator is required")
	}
	
	if m.logger == nil {
		return fmt.Errorf("logger is required")
	}
	
	return nil
}