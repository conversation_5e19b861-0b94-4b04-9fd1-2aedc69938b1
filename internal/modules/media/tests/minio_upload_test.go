package tests

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/storage"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"github.com/sirupsen/logrus"
)

// TestMinIOUpload tests file upload to MinIO storage
func TestMinIOUpload(t *testing.T) {
	// Skip test if MinIO credentials are not available
	minioEndpoint := os.Getenv("MINIO_ENDPOINT")
	minioAccessKey := os.Getenv("MINIO_ACCESS_KEY")
	minioSecretKey := os.Getenv("MINIO_SECRET_KEY")
	minioBucket := os.Getenv("MINIO_BUCKET")

	if minioEndpoint == "" || minioAccessKey == "" || minioSecretKey == "" || minioBucket == "" {
		t.Skip("MinIO credentials not provided, skipping MinIO upload test")
	}

	// Setup test database
	db := setupTestDB(t)
	
	// Setup MinIO storage
	minioConfig := &storage.Config{
		Provider:  storage.StorageTypeMinIO,
		Endpoint:  minioEndpoint,
		Bucket:    minioBucket,
		AccessKey: minioAccessKey,
		SecretKey: minioSecretKey,
		UseSSL:    false,
		BasePath:  "test-uploads",
	}

	minioStorage, err := storage.NewMinIOStorage(minioConfig)
	require.NoError(t, err)

	// Test connection
	ctx := context.Background()
	err = minioStorage.Connect()
	require.NoError(t, err)

	// Setup services
	storageProviders := map[string]storage.Storage{
		"minio": minioStorage,
	}
	
	fileRepo := repositories.NewMediaFileRepository(db)
	folderRepo := repositories.NewMediaFolderRepository(db)
	thumbnailRepo := repositories.NewMediaThumbnailRepository(db)
	
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	
	storageService := services.NewMediaStorageService(
		storageProviders,
		"minio",
		fileRepo,
		logger,
	)
	
	fileService := services.NewMediaFileService(
		fileRepo,
		folderRepo,
		storageService,
		logger,
	)
	
	folderService := services.NewMediaFolderService(
		folderRepo,
		fileRepo,
		logger,
	)
	
	thumbnailService := services.NewMediaThumbnailService(
		thumbnailRepo,
		storageService,
		logger,
	)

	validator := validator.NewValidator()
	
	handler := handlers.NewMediaHandler(
		fileService,
		storageService,
		folderService,
		thumbnailService,
		validator,
	)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// Add middleware to set tenant and user context
	router.Use(func(c *gin.Context) {
		c.Set("tenant_id", uint(1))
		c.Set("user_id", uint(1))
		c.Next()
	})
	
	router.POST("/media/upload", handler.UploadFile)
	
	// Test file upload
	t.Run("Upload Image to MinIO", func(t *testing.T) {
		// Create test image
		imageData := createTestImage(t)
		
		// Create multipart form
		body := &bytes.Buffer{}
		writer := createMultipartForm(t, body, map[string]string{
			"website_id":  "1",
			"alt_text":    "Test image",
			"title":       "Test Upload",
			"description": "Testing MinIO upload",
			"category":    "test",
			"visibility":  "public",
		}, map[string]fileData{
			"file": {
				filename: "test-image.jpg",
				content:  imageData,
				mimeType: "image/jpeg",
			},
		})
		
		// Create request
		req := httptest.NewRequest("POST", "/media/upload", body)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		
		// Create response recorder
		w := httptest.NewRecorder()
		
		// Process request
		router.ServeHTTP(w, req)
		
		// Check response
		assert.Equal(t, http.StatusCreated, w.Code)
		
		// Verify response contains file information
		responseBody := w.Body.String()
		assert.Contains(t, responseBody, "test-image.jpg")
		assert.Contains(t, responseBody, "File uploaded successfully")
		
		t.Logf("Upload response: %s", responseBody)
	})
	
	t.Run("Upload Multiple Files to MinIO", func(t *testing.T) {
		// Create test files
		imageData1 := createTestImage(t)
		imageData2 := createTestImage(t)
		
		// Create multipart form with multiple files
		body := &bytes.Buffer{}
		writer := createMultipartForm(t, body, map[string]string{
			"website_id": "1",
			"category":   "test",
			"visibility": "public",
		}, map[string]fileData{
			"file": {
				filename: "multi-test-1.jpg",
				content:  imageData1,
				mimeType: "image/jpeg",
			},
			"file": {
				filename: "multi-test-2.jpg",
				content:  imageData2,
				mimeType: "image/jpeg",
			},
		})
		
		// Create request
		req := httptest.NewRequest("POST", "/media/upload", body)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		
		// Create response recorder
		w := httptest.NewRecorder()
		
		// Process request
		router.ServeHTTP(w, req)
		
		// Check response
		assert.Equal(t, http.StatusCreated, w.Code)
		
		responseBody := w.Body.String()
		assert.Contains(t, responseBody, "Files uploaded successfully")
		
		t.Logf("Multi-upload response: %s", responseBody)
	})
	
	t.Run("Direct MinIO Storage Test", func(t *testing.T) {
		// Test direct storage operations
		testKey := fmt.Sprintf("test-uploads/direct-test-%d.txt", time.Now().Unix())
		testContent := "This is a direct MinIO storage test"
		
		// Upload
		err := minioStorage.Put(ctx, testKey, strings.NewReader(testContent), &storage.PutOptions{
			ContentType: "text/plain",
		})
		require.NoError(t, err)
		
		// Check existence
		exists, err := minioStorage.Exists(ctx, testKey)
		require.NoError(t, err)
		assert.True(t, exists)
		
		// Download
		reader, err := minioStorage.Get(ctx, testKey)
		require.NoError(t, err)
		defer reader.Close()
		
		downloadedContent, err := io.ReadAll(reader)
		require.NoError(t, err)
		assert.Equal(t, testContent, string(downloadedContent))
		
		// Get file info
		fileInfo, err := minioStorage.GetInfo(ctx, testKey)
		require.NoError(t, err)
		assert.Equal(t, testKey, fileInfo.Key)
		assert.Equal(t, "text/plain", fileInfo.ContentType)
		assert.Equal(t, int64(len(testContent)), fileInfo.Size)
		
		// Get public URL
		publicURL, err := minioStorage.GetPublicURL(ctx, testKey)
		require.NoError(t, err)
		assert.Contains(t, publicURL, testKey)
		
		// Get presigned URL
		presignedURL, err := minioStorage.GetURL(ctx, testKey, time.Hour)
		require.NoError(t, err)
		assert.Contains(t, presignedURL, testKey)
		
		t.Logf("Public URL: %s", publicURL)
		t.Logf("Presigned URL: %s", presignedURL)
		
		// Clean up
		err = minioStorage.Delete(ctx, testKey)
		require.NoError(t, err)
		
		// Verify deletion
		exists, err = minioStorage.Exists(ctx, testKey)
		require.NoError(t, err)
		assert.False(t, exists)
	})
	
	t.Run("MinIO Multi-Tenant Path Test", func(t *testing.T) {
		// Test tenant-specific paths
		tenantID := uint(123)
		websiteID := uint(456)
		filename := "tenant-test.jpg"
		
		// Generate tenant path
		tenantPath := fmt.Sprintf("tenant-%d/website-%d/%s", tenantID, websiteID, filename)
		
		imageData := createTestImage(t)
		
		// Upload with tenant path
		err := minioStorage.Put(ctx, tenantPath, bytes.NewReader(imageData), &storage.PutOptions{
			ContentType: "image/jpeg",
		})
		require.NoError(t, err)
		
		// Verify upload
		exists, err := minioStorage.Exists(ctx, tenantPath)
		require.NoError(t, err)
		assert.True(t, exists)
		
		// List files with tenant prefix
		listResult, err := minioStorage.List(ctx, fmt.Sprintf("tenant-%d", tenantID), &storage.ListOptions{
			MaxKeys: 100,
		})
		require.NoError(t, err)
		
		// Should find our uploaded file
		found := false
		for _, file := range listResult.Files {
			if file.Key == tenantPath {
				found = true
				break
			}
		}
		assert.True(t, found, "Uploaded file should be found in tenant listing")
		
		// Clean up
		err = minioStorage.Delete(ctx, tenantPath)
		require.NoError(t, err)
		
		t.Logf("Successfully tested multi-tenant path: %s", tenantPath)
	})
}

// TestMinIOConfiguration tests MinIO configuration and connection
func TestMinIOConfiguration(t *testing.T) {
	t.Run("Invalid Configuration", func(t *testing.T) {
		// Test with missing endpoint
		config := &storage.Config{
			Provider:  storage.StorageTypeMinIO,
			AccessKey: "test",
			SecretKey: "test",
			Bucket:    "test",
		}
		
		_, err := storage.NewMinIOStorage(config)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "endpoint is required")
	})
	
	t.Run("Valid Configuration", func(t *testing.T) {
		config := storage.DefaultMinIOConfig("localhost:9000", "test-bucket", "minioadmin", "minioadmin")
		
		err := storage.ValidateConfig(config)
		assert.NoError(t, err)
		
		// Should create storage instance (won't test connection without actual MinIO)
		_, err = storage.NewMinIOStorage(config)
		assert.NoError(t, err)
	})
}

// Helper functions

func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)
	
	// Auto-migrate tables
	err = db.AutoMigrate(
		&models.MediaFile{},
		&models.MediaFolder{},
		&models.MediaThumbnail{},
	)
	require.NoError(t, err)
	
	return db
}

func createTestImage(t *testing.T) []byte {
	// Create a minimal JPEG image (1x1 pixel)
	jpegData := []byte{
		0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
		0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
		0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
		0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
		0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
		0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
		0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
		0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
		0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
		0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xC4,
		0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
		0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x00, 0xFF, 0xD9,
	}
	return jpegData
}

type fileData struct {
	filename string
	content  []byte
	mimeType string
}

func createMultipartForm(t *testing.T, body *bytes.Buffer, fields map[string]string, files map[string]fileData) *multipart.Writer {
	writer := multipart.NewWriter(body)
	
	// Add form fields
	for key, value := range fields {
		err := writer.WriteField(key, value)
		require.NoError(t, err)
	}
	
	// Add files
	for fieldName, file := range files {
		part, err := writer.CreateFormFile(fieldName, file.filename)
		require.NoError(t, err)
		
		_, err = part.Write(file.content)
		require.NoError(t, err)
	}
	
	err := writer.Close()
	require.NoError(t, err)
	
	return writer
}