package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// MediaHandler handles media file operations
type MediaHandler struct {
	fileService     services.MediaFileService
	storageService  services.MediaStorageService
	folderService   services.MediaFolderService
	thumbnailService services.MediaThumbnailService
	validator       validator.Validator
}

// NewMediaHandler creates a new media handler
func NewMediaHandler(
	fileService services.MediaFileService,
	storageService services.MediaStorageService,
	folderService services.MediaFolderService,
	thumbnailService services.MediaThumbnailService,
	validator validator.Validator,
) *MediaHandler {
	return &MediaHandler{
		fileService:     fileService,
		storageService:  storageService,
		folderService:   folderService,
		thumbnailService: thumbnailService,
		validator:       validator,
	}
}

// @Summary Upload media file
// @Description Upload a media file to storage
// @Tags Media
// @Accept multipart/form-data
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param website_id formData int true "Website ID"
// @Param folder_id formData int false "Folder ID"
// @Param file formData file true "Media file"
// @Param alt_text formData string false "Alt text for accessibility"
// @Param title formData string false "File title"
// @Param description formData string false "File description"
// @Param category formData string false "File category"
// @Param visibility formData string false "File visibility (public, private, shared)"
// @Success 201 {object} response.Response{data=models.UploadResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/upload [post]
func (h *MediaHandler) UploadFile(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")
	userID := c.GetUint("user_id")

	// Parse form data
	form, err := c.MultipartForm()
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid form data", err)
		return
	}

	// Get website ID
	websiteIDStr := c.PostForm("website_id")
	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid website ID", err)
		return
	}

	// Get optional folder ID
	var folderID *uint
	if folderIDStr := c.PostForm("folder_id"); folderIDStr != "" {
		id, err := strconv.ParseUint(folderIDStr, 10, 32)
		if err == nil {
			folderIDUint := uint(id)
			folderID = &folderIDUint
		}
	}

	// Get files
	files := form.File["file"]
	if len(files) == 0 {
		response.ErrorResponse(c, http.StatusBadRequest, "No files provided", nil)
		return
	}

	// Single file upload
	if len(files) == 1 {
		file := files[0]
		
		// Open file
		src, err := file.Open()
		if err != nil {
			response.ErrorResponse(c, http.StatusBadRequest, "Failed to open file", err)
			return
		}
		defer src.Close()

		// Create upload request
		uploadReq := &models.UploadRequest{
			TenantID:     tenantID,
			WebsiteID:    uint(websiteID),
			FolderID:     folderID,
			UserID:       userID,
			Reader:       src,
			Filename:     file.Filename,
			OriginalName: file.Filename,
			MimeType:     file.Header.Get("Content-Type"),
			Size:         file.Size,
			AltText:      getStringPtr(c.PostForm("alt_text")),
			Title:        getStringPtr(c.PostForm("title")),
			Description:  getStringPtr(c.PostForm("description")),
			Category:     c.DefaultPostForm("category", "general"),
			Visibility:   models.FileVisibility(c.DefaultPostForm("visibility", string(models.FileVisibilityPublic))),
		}

		// Validate request
		if err := h.validator.Validate(uploadReq); err != nil {
			response.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
			return
		}

		// Upload file
		result, err := h.fileService.UploadFile(c.Request.Context(), uploadReq)
		if err != nil {
			response.ErrorResponse(c, http.StatusInternalServerError, "Failed to upload file", err)
			return
		}

		response.SuccessResponse(c, http.StatusCreated, "File uploaded successfully", result)
		return
	}

	// Multi-file upload
	multiUploadReq := &models.MultiUploadRequest{
		TenantID:   tenantID,
		WebsiteID:  uint(websiteID),
		FolderID:   folderID,
		UserID:     userID,
		Category:   c.DefaultPostForm("category", "general"),
		Visibility: models.FileVisibility(c.DefaultPostForm("visibility", string(models.FileVisibilityPublic))),
	}

	// Process each file
	for _, file := range files {
		src, err := file.Open()
		if err != nil {
			continue // Skip invalid files
		}

		fileInfo := models.UploadFileInfo{
			Reader:       src,
			Filename:     file.Filename,
			OriginalName: file.Filename,
			MimeType:     file.Header.Get("Content-Type"),
			Size:         file.Size,
		}
		multiUploadReq.Files = append(multiUploadReq.Files, fileInfo)
	}

	// Validate request
	if err := h.validator.Validate(multiUploadReq); err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Upload files
	result, err := h.fileService.UploadMultipleFiles(c.Request.Context(), multiUploadReq)
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to upload files", err)
		return
	}

	response.SuccessResponse(c, http.StatusCreated, "Files uploaded successfully", result)
}

// @Summary List media files
// @Description Get a list of media files with filtering and pagination
// @Tags Media
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param website_id query int true "Website ID"
// @Param folder_id query int false "Folder ID"
// @Param file_type query string false "File type (image, video, audio, document, archive, other)"
// @Param category query string false "File category"
// @Param mime_type query string false "MIME type"
// @Param visibility query string false "File visibility (public, private, shared)"
// @Param query query string false "Search query"
// @Param tags query string false "Tags (comma-separated)"
// @Param min_size query int false "Minimum file size"
// @Param max_size query int false "Maximum file size"
// @Param sort_by query string false "Sort by (name, size, created_at, updated_at)"
// @Param sort_order query string false "Sort order (asc, desc)"
// @Param page query int false "Page number" default(1)
// @Param page_size query int false "Page size" default(20)
// @Success 200 {object} response.Response{data=response.PaginatedResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/files [get]
func (h *MediaHandler) ListFiles(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse query parameters
	searchReq := &models.MediaFileSearchRequest{
		TenantID: tenantID,
	}

	if websiteIDStr := c.Query("website_id"); websiteIDStr != "" {
		websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
		if err != nil {
			response.ErrorResponse(c, http.StatusBadRequest, "Invalid website ID", err)
			return
		}
		searchReq.WebsiteID = uint(websiteID)
	} else {
		response.ErrorResponse(c, http.StatusBadRequest, "Website ID is required", nil)
		return
	}

	if folderIDStr := c.Query("folder_id"); folderIDStr != "" {
		folderID, err := strconv.ParseUint(folderIDStr, 10, 32)
		if err == nil {
			folderIDUint := uint(folderID)
			searchReq.FolderID = &folderIDUint
		}
	}

	if fileTypeStr := c.Query("file_type"); fileTypeStr != "" {
		fileType := models.FileType(fileTypeStr)
		searchReq.FileType = &fileType
	}

	if category := c.Query("category"); category != "" {
		searchReq.Category = &category
	}

	if mimeType := c.Query("mime_type"); mimeType != "" {
		searchReq.MimeType = &mimeType
	}

	if query := c.Query("query"); query != "" {
		searchReq.Query = &query
	}

	if tagsStr := c.Query("tags"); tagsStr != "" {
		searchReq.Tags = strings.Split(tagsStr, ",")
	}

	if minSizeStr := c.Query("min_size"); minSizeStr != "" {
		minSize, err := strconv.ParseUint(minSizeStr, 10, 64)
		if err == nil {
			searchReq.MinSize = &minSize
		}
	}

	if maxSizeStr := c.Query("max_size"); maxSizeStr != "" {
		maxSize, err := strconv.ParseUint(maxSizeStr, 10, 64)
		if err == nil {
			searchReq.MaxSize = &maxSize
		}
	}

	if sortBy := c.Query("sort_by"); sortBy != "" {
		searchReq.SortBy = &sortBy
	}

	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		searchReq.SortOrder = &sortOrder
	}

	// Pagination
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	searchReq.Page = page
	searchReq.PageSize = pageSize

	// Validate request
	if err := h.validator.Validate(searchReq); err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Search files
	files, total, err := h.fileService.SearchFiles(c.Request.Context(), searchReq)
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to search files", err)
		return
	}

	// Convert to response format
	fileResponses := make([]models.MediaFileListResponse, len(files))
	for i, file := range files {
		fileResponses[i] = models.MediaFileListResponse{
			ID:               file.ID,
			Filename:         file.Filename,
			OriginalFilename: file.OriginalFilename,
			MimeType:         file.MimeType,
			FileSize:         file.FileSize,
			FileType:         file.FileType,
			PublicURL:        file.PublicURL,
			Width:            file.Width,
			Height:           file.Height,
			Duration:         file.Duration,
			AltText:          file.AltText,
			Title:            file.Title,
			Status:           file.Status,
			CreatedAt:        file.CreatedAt,
			UpdatedAt:        file.UpdatedAt,
			// Add thumbnails if needed
		}
	}

	paginatedResponse := response.PaginatedResponse{
		Items:    fileResponses,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Pages:    (int(total) + pageSize - 1) / pageSize,
	}

	response.SuccessResponse(c, http.StatusOK, "Files retrieved successfully", paginatedResponse)
}

// @Summary Get media file by ID
// @Description Get a media file by its ID
// @Tags Media
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param id path int true "File ID"
// @Success 200 {object} response.Response{data=models.MediaFileResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/files/{id} [get]
func (h *MediaHandler) GetFile(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse file ID
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid file ID", err)
		return
	}

	// Get file
	file, err := h.fileService.GetFileByID(c.Request.Context(), tenantID, uint(fileID))
	if err != nil {
		response.ErrorResponse(c, http.StatusNotFound, "File not found", err)
		return
	}

	// Convert to response format
	fileResponse := h.convertToFileResponse(file)

	response.SuccessResponse(c, http.StatusOK, "File retrieved successfully", fileResponse)
}

// @Summary Update media file
// @Description Update a media file's metadata
// @Tags Media
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param id path int true "File ID"
// @Param request body models.UpdateMediaFileRequest true "Update request"
// @Success 200 {object} response.Response{data=models.MediaFileResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/files/{id} [put]
func (h *MediaHandler) UpdateFile(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse file ID
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid file ID", err)
		return
	}

	// Parse request body
	var updateReq models.UpdateMediaFileRequest
	if err := c.ShouldBindJSON(&updateReq); err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate request
	if err := h.validator.Validate(updateReq); err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Update file
	file, err := h.fileService.UpdateFile(c.Request.Context(), tenantID, uint(fileID), &updateReq)
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to update file", err)
		return
	}

	// Convert to response format
	fileResponse := h.convertToFileResponse(file)

	response.SuccessResponse(c, http.StatusOK, "File updated successfully", fileResponse)
}

// @Summary Delete media file
// @Description Delete a media file
// @Tags Media
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param id path int true "File ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/files/{id} [delete]
func (h *MediaHandler) DeleteFile(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse file ID
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid file ID", err)
		return
	}

	// Delete file
	err = h.fileService.DeleteFile(c.Request.Context(), tenantID, uint(fileID))
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete file", err)
		return
	}

	response.SuccessResponse(c, http.StatusOK, "File deleted successfully", nil)
}

// @Summary Download media file
// @Description Download a media file
// @Tags Media
// @Accept json
// @Produce application/octet-stream
// @Param tenant_id header int true "Tenant ID"
// @Param id path int true "File ID"
// @Success 200 {file} binary
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/files/{id}/download [get]
func (h *MediaHandler) DownloadFile(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse file ID
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid file ID", err)
		return
	}

	// Get file metadata
	file, err := h.fileService.GetFileByID(c.Request.Context(), tenantID, uint(fileID))
	if err != nil {
		response.ErrorResponse(c, http.StatusNotFound, "File not found", err)
		return
	}

	// Download file content
	reader, err := h.storageService.Download(c.Request.Context(), uint(fileID))
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to download file", err)
		return
	}
	defer reader.Close()

	// Set headers
	c.Header("Content-Disposition", "attachment; filename="+file.Filename)
	c.Header("Content-Type", file.MimeType)
	c.Header("Content-Length", strconv.FormatUint(file.FileSize, 10))

	// Stream file content
	c.DataFromReader(http.StatusOK, int64(file.FileSize), file.MimeType, reader, nil)
}

// @Summary Get media statistics
// @Description Get media usage statistics
// @Tags Media
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param website_id query int false "Website ID"
// @Success 200 {object} response.Response{data=models.MediaFileStats}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/stats [get]
func (h *MediaHandler) GetStats(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	var websiteID *uint
	if websiteIDStr := c.Query("website_id"); websiteIDStr != "" {
		id, err := strconv.ParseUint(websiteIDStr, 10, 32)
		if err == nil {
			websiteIDUint := uint(id)
			websiteID = &websiteIDUint
		}
	}

	// Get statistics
	stats, err := h.fileService.GetStatistics(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to get statistics", err)
		return
	}

	response.SuccessResponse(c, http.StatusOK, "Statistics retrieved successfully", stats)
}

// Helper methods

func (h *MediaHandler) convertToFileResponse(file *models.MediaFile) *models.MediaFileResponse {
	return &models.MediaFileResponse{
		ID:               file.ID,
		TenantID:         file.TenantID,
		WebsiteID:        file.WebsiteID,
		FolderID:         file.FolderID,
		UserID:           file.UserID,
		Filename:         file.Filename,
		OriginalFilename: file.OriginalFilename,
		Slug:             file.Slug,
		MimeType:         file.MimeType,
		FileSize:         file.FileSize,
		FileHash:         file.FileHash,
		StorageType:      file.StorageType,
		StoragePath:      file.StoragePath,
		PublicURL:        file.PublicURL,
		StorageProvider:  file.StorageProvider,
		CDNUrl:           file.CDNUrl,
		CDNProvider:      file.CDNProvider,
		OptimizedPath:    file.OptimizedPath,
		OptimizedSize:    file.OptimizedSize,
		OptimizedURL:     file.OptimizedURL,
		OptimizedAt:      file.OptimizedAt,
		Width:            file.Width,
		Height:           file.Height,
		Duration:         file.Duration,
		FileType:         file.FileType,
		Category:         file.Category,
		AltText:          file.AltText,
		Title:            file.Title,
		Description:      file.Description,
		Visibility:       file.Visibility,
		ViewCount:        file.ViewCount,
		DownloadCount:    file.DownloadCount,
		LastAccessedAt:   file.LastAccessedAt,
		Status:           file.Status,
		CreatedAt:        file.CreatedAt,
		UpdatedAt:        file.UpdatedAt,
	}
}

func getStringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}