package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// MediaFolderHandler handles media folder operations
type MediaFolderHandler struct {
	folderService services.MediaFolderService
	validator     validator.Validator
}

// NewMediaFolderHandler creates a new media folder handler
func NewMediaFolderHandler(
	folderService services.MediaFolderService,
	validator validator.Validator,
) *MediaFolderHandler {
	return &MediaFolderHandler{
		folderService: folderService,
		validator:     validator,
	}
}

// @Summary Create media folder
// @Description Create a new media folder
// @Tags Media Folders
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param request body models.CreateMediaFolderRequest true "Create folder request"
// @Success 201 {object} response.Response{data=models.MediaFolderResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/folders [post]
func (h *MediaFolderHandler) CreateFolder(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")
	userID := c.GetUint("user_id")

	// Parse request body
	var createReq models.CreateMediaFolderRequest
	if err := c.ShouldBindJSON(&createReq); err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Set tenant ID from context
	createReq.TenantID = tenantID

	// Validate request
	if err := h.validator.Validate(createReq); err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Create folder
	folder, err := h.folderService.CreateFolder(c.Request.Context(), userID, &createReq)
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to create folder", err)
		return
	}

	// Convert to response format
	folderResponse := h.convertToFolderResponse(folder)

	response.SuccessResponse(c, http.StatusCreated, "Folder created successfully", folderResponse)
}

// @Summary List media folders
// @Description Get a list of media folders
// @Tags Media Folders
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param website_id query int true "Website ID"
// @Param parent_id query int false "Parent folder ID"
// @Param type query string false "Folder type (user, system, public, private, shared)"
// @Param visibility query string false "Folder visibility (public, private, shared)"
// @Param tree query bool false "Return as tree structure" default(false)
// @Success 200 {object} response.Response{data=[]models.MediaFolderResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/folders [get]
func (h *MediaFolderHandler) ListFolders(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse query parameters
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		response.ErrorResponse(c, http.StatusBadRequest, "Website ID is required", nil)
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid website ID", err)
		return
	}

	var parentID *uint
	if parentIDStr := c.Query("parent_id"); parentIDStr != "" {
		id, err := strconv.ParseUint(parentIDStr, 10, 32)
		if err == nil {
			parentIDUint := uint(id)
			parentID = &parentIDUint
		}
	}

	var folderType *models.FolderType
	if typeStr := c.Query("type"); typeStr != "" {
		fType := models.FolderType(typeStr)
		folderType = &fType
	}

	var visibility *models.FolderVisibility
	if visibilityStr := c.Query("visibility"); visibilityStr != "" {
		vis := models.FolderVisibility(visibilityStr)
		visibility = &vis
	}

	tree := c.Query("tree") == "true"

	if tree {
		// Get folder tree
		folders, err := h.folderService.GetFolderTree(c.Request.Context(), tenantID, uint(websiteID), parentID)
		if err != nil {
			response.ErrorResponse(c, http.StatusInternalServerError, "Failed to get folder tree", err)
			return
		}

		// Convert to tree response format
		treeResponse := h.convertToTreeResponse(folders)
		response.SuccessResponse(c, http.StatusOK, "Folder tree retrieved successfully", treeResponse)
	} else {
		// Get folder list
		folders, err := h.folderService.ListFolders(c.Request.Context(), tenantID, uint(websiteID), parentID, folderType, visibility)
		if err != nil {
			response.ErrorResponse(c, http.StatusInternalServerError, "Failed to list folders", err)
			return
		}

		// Convert to response format
		folderResponses := make([]models.MediaFolderResponse, len(folders))
		for i, folder := range folders {
			folderResponses[i] = *h.convertToFolderResponse(folder)
		}

		response.SuccessResponse(c, http.StatusOK, "Folders retrieved successfully", folderResponses)
	}
}

// @Summary Get media folder by ID
// @Description Get a media folder by its ID
// @Tags Media Folders
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param id path int true "Folder ID"
// @Param include_files query bool false "Include files in response" default(false)
// @Success 200 {object} response.Response{data=models.MediaFolderResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/folders/{id} [get]
func (h *MediaFolderHandler) GetFolder(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse folder ID
	folderIDStr := c.Param("id")
	folderID, err := strconv.ParseUint(folderIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid folder ID", err)
		return
	}

	includeFiles := c.Query("include_files") == "true"

	// Get folder
	folder, err := h.folderService.GetFolderByID(c.Request.Context(), tenantID, uint(folderID), includeFiles)
	if err != nil {
		response.ErrorResponse(c, http.StatusNotFound, "Folder not found", err)
		return
	}

	// Convert to response format
	folderResponse := h.convertToFolderResponse(folder)

	response.SuccessResponse(c, http.StatusOK, "Folder retrieved successfully", folderResponse)
}

// @Summary Update media folder
// @Description Update a media folder
// @Tags Media Folders
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param id path int true "Folder ID"
// @Param request body models.UpdateMediaFolderRequest true "Update folder request"
// @Success 200 {object} response.Response{data=models.MediaFolderResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/folders/{id} [put]
func (h *MediaFolderHandler) UpdateFolder(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse folder ID
	folderIDStr := c.Param("id")
	folderID, err := strconv.ParseUint(folderIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid folder ID", err)
		return
	}

	// Parse request body
	var updateReq models.UpdateMediaFolderRequest
	if err := c.ShouldBindJSON(&updateReq); err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate request
	if err := h.validator.Validate(updateReq); err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Update folder
	folder, err := h.folderService.UpdateFolder(c.Request.Context(), tenantID, uint(folderID), &updateReq)
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to update folder", err)
		return
	}

	// Convert to response format
	folderResponse := h.convertToFolderResponse(folder)

	response.SuccessResponse(c, http.StatusOK, "Folder updated successfully", folderResponse)
}

// @Summary Delete media folder
// @Description Delete a media folder
// @Tags Media Folders
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param id path int true "Folder ID"
// @Param force query bool false "Force delete (delete files too)" default(false)
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/folders/{id} [delete]
func (h *MediaFolderHandler) DeleteFolder(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse folder ID
	folderIDStr := c.Param("id")
	folderID, err := strconv.ParseUint(folderIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid folder ID", err)
		return
	}

	force := c.Query("force") == "true"

	// Delete folder
	err = h.folderService.DeleteFolder(c.Request.Context(), tenantID, uint(folderID), force)
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete folder", err)
		return
	}

	response.SuccessResponse(c, http.StatusOK, "Folder deleted successfully", nil)
}

// @Summary Move media folder
// @Description Move a media folder to a different parent
// @Tags Media Folders
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param id path int true "Folder ID"
// @Param parent_id formData int false "New parent folder ID (null for root)"
// @Success 200 {object} response.Response{data=models.MediaFolderResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/folders/{id}/move [post]
func (h *MediaFolderHandler) MoveFolder(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse folder ID
	folderIDStr := c.Param("id")
	folderID, err := strconv.ParseUint(folderIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid folder ID", err)
		return
	}

	var newParentID *uint
	if parentIDStr := c.PostForm("parent_id"); parentIDStr != "" {
		id, err := strconv.ParseUint(parentIDStr, 10, 32)
		if err == nil {
			parentIDUint := uint(id)
			newParentID = &parentIDUint
		}
	}

	// Move folder
	folder, err := h.folderService.MoveFolder(c.Request.Context(), tenantID, uint(folderID), newParentID)
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to move folder", err)
		return
	}

	// Convert to response format
	folderResponse := h.convertToFolderResponse(folder)

	response.SuccessResponse(c, http.StatusOK, "Folder moved successfully", folderResponse)
}

// @Summary Get folder breadcrumb
// @Description Get folder breadcrumb path
// @Tags Media Folders
// @Accept json
// @Produce json
// @Param tenant_id header int true "Tenant ID"
// @Param id path int true "Folder ID"
// @Success 200 {object} response.Response{data=[]models.MediaFolderResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /media/folders/{id}/breadcrumb [get]
func (h *MediaFolderHandler) GetFolderBreadcrumb(c *gin.Context) {
	tenantID := c.GetUint("tenant_id")

	// Parse folder ID
	folderIDStr := c.Param("id")
	folderID, err := strconv.ParseUint(folderIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, http.StatusBadRequest, "Invalid folder ID", err)
		return
	}

	// Get breadcrumb
	breadcrumb, err := h.folderService.GetFolderBreadcrumb(c.Request.Context(), tenantID, uint(folderID))
	if err != nil {
		response.ErrorResponse(c, http.StatusInternalServerError, "Failed to get folder breadcrumb", err)
		return
	}

	// Convert to response format
	breadcrumbResponse := make([]models.MediaFolderResponse, len(breadcrumb))
	for i, folder := range breadcrumb {
		breadcrumbResponse[i] = *h.convertToFolderResponse(folder)
	}

	response.SuccessResponse(c, http.StatusOK, "Folder breadcrumb retrieved successfully", breadcrumbResponse)
}

// Helper methods

func (h *MediaFolderHandler) convertToFolderResponse(folder *models.MediaFolder) *models.MediaFolderResponse {
	response := &models.MediaFolderResponse{
		ID:          folder.ID,
		TenantID:    folder.TenantID,
		WebsiteID:   folder.WebsiteID,
		ParentID:    folder.ParentID,
		UserID:      folder.UserID,
		Name:        folder.Name,
		Slug:        folder.Slug,
		Path:        folder.Path,
		Description: folder.Description,
		Type:        folder.Type,
		Color:       folder.Color,
		Icon:        folder.Icon,
		Visibility:  folder.Visibility,
		Level:       folder.Level,
		SortOrder:   folder.SortOrder,
		FileCount:   folder.FileCount,
		TotalSize:   folder.TotalSize,
		Status:      folder.Status,
		CreatedAt:   folder.CreatedAt,
		UpdatedAt:   folder.UpdatedAt,
	}

	// Add children if present
	if len(folder.Children) > 0 {
		response.Children = make([]models.MediaFolderResponse, len(folder.Children))
		for i, child := range folder.Children {
			response.Children[i] = *h.convertToFolderResponse(&child)
		}
	}

	// Add files if present
	if len(folder.Files) > 0 {
		response.Files = make([]models.MediaFileResponse, len(folder.Files))
		for i, file := range folder.Files {
			response.Files[i] = models.MediaFileResponse{
				ID:               file.ID,
				TenantID:         file.TenantID,
				WebsiteID:        file.WebsiteID,
				FolderID:         file.FolderID,
				UserID:           file.UserID,
				Filename:         file.Filename,
				OriginalFilename: file.OriginalFilename,
				Slug:             file.Slug,
				MimeType:         file.MimeType,
				FileSize:         file.FileSize,
				FileHash:         file.FileHash,
				StorageType:      file.StorageType,
				StoragePath:      file.StoragePath,
				PublicURL:        file.PublicURL,
				StorageProvider:  file.StorageProvider,
				CDNUrl:           file.CDNUrl,
				CDNProvider:      file.CDNProvider,
				Width:            file.Width,
				Height:           file.Height,
				Duration:         file.Duration,
				FileType:         file.FileType,
				Category:         file.Category,
				AltText:          file.AltText,
				Title:            file.Title,
				Description:      file.Description,
				Visibility:       file.Visibility,
				ViewCount:        file.ViewCount,
				DownloadCount:    file.DownloadCount,
				LastAccessedAt:   file.LastAccessedAt,
				Status:           file.Status,
				CreatedAt:        file.CreatedAt,
				UpdatedAt:        file.UpdatedAt,
			}
		}
	}

	return response
}

func (h *MediaFolderHandler) convertToTreeResponse(folders []*models.MediaFolder) []models.MediaFolderTreeResponse {
	if len(folders) == 0 {
		return []models.MediaFolderTreeResponse{}
	}

	response := make([]models.MediaFolderTreeResponse, len(folders))
	for i, folder := range folders {
		response[i] = models.MediaFolderTreeResponse{
			ID:        folder.ID,
			Name:      folder.Name,
			Path:      folder.Path,
			Icon:      folder.Icon,
			Color:     folder.Color,
			FileCount: folder.FileCount,
			TotalSize: folder.TotalSize,
			Level:     folder.Level,
		}

		// Add children recursively
		if len(folder.Children) > 0 {
			childFolders := make([]*models.MediaFolder, len(folder.Children))
			for j, child := range folder.Children {
				childFolders[j] = &child
			}
			response[i].Children = h.convertToTreeResponse(childFolders)
		}
	}

	return response
}