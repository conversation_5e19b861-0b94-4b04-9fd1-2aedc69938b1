package models

import (
	"time"

	"gorm.io/gorm"
)

// MediaFolder represents a media folder for organizing media files
type MediaFolder struct {
	ID       uint `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID uint `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint `json:"website_id" gorm:"not null;index"`
	ParentID *uint `json:"parent_id,omitempty" gorm:"index"`
	UserID   uint `json:"user_id" gorm:"not null;index"`

	// Folder information
	Name        string `json:"name" gorm:"size:255;not null"`
	Slug        string `json:"slug" gorm:"size:255;not null;index"`
	Path        string `json:"path" gorm:"size:500;not null;index"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Folder type and settings
	Type FolderType `json:"type" gorm:"type:enum('user','system','public','private','shared');not null;default:'user'"`
	Color string `json:"color" gorm:"size:7;default:'#6b7280'"`
	Icon  string `json:"icon" gorm:"size:50;default:'folder'"`

	// Access control
	Visibility        FolderVisibility `json:"visibility" gorm:"type:enum('public','private','shared');not null;default:'public'"`
	AccessPermissions []AccessPermission `json:"access_permissions" gorm:"type:json"`

	// Nested set model for hierarchy
	Lft   uint `json:"lft" gorm:"not null;default:1"`
	Rgt   uint `json:"rgt" gorm:"not null;default:2"`
	Level uint `json:"level" gorm:"not null;default:0"`

	// Folder settings
	SortOrder uint `json:"sort_order" gorm:"default:0"`
	Settings  map[string]interface{} `json:"settings" gorm:"type:json"`

	// Usage statistics
	FileCount uint  `json:"file_count" gorm:"default:0"`
	TotalSize uint64 `json:"total_size" gorm:"default:0"`

	// Status and timestamps
	Status    FolderStatus `json:"status" gorm:"type:enum('active','archived','deleted');not null;default:'active'"`
	CreatedAt time.Time    `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time    `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Parent   *MediaFolder   `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children []MediaFolder  `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Files    []MediaFile    `json:"files,omitempty" gorm:"foreignKey:FolderID"`
	Creator  User           `json:"creator,omitempty" gorm:"foreignKey:UserID"`
	Tenant   Tenant         `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Website  Website        `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
}

// FolderType defines the type of folder
type FolderType string

const (
	FolderTypeUser    FolderType = "user"
	FolderTypeSystem  FolderType = "system"
	FolderTypePublic  FolderType = "public"
	FolderTypePrivate FolderType = "private"
	FolderTypeShared  FolderType = "shared"
)

// FolderVisibility defines the visibility of folder
type FolderVisibility string

const (
	FolderVisibilityPublic  FolderVisibility = "public"
	FolderVisibilityPrivate FolderVisibility = "private"
	FolderVisibilityShared  FolderVisibility = "shared"
)

// FolderStatus defines the status of folder
type FolderStatus string

const (
	FolderStatusActive   FolderStatus = "active"
	FolderStatusArchived FolderStatus = "archived"
	FolderStatusDeleted  FolderStatus = "deleted"
)

// AccessPermission defines access permissions for folders
type AccessPermission struct {
	UserID     uint   `json:"user_id"`
	Permission string `json:"permission"` // read, write, delete
}

// TableName returns the table name for MediaFolder
func (MediaFolder) TableName() string {
	return "media_folders"
}

// BeforeCreate hook for GORM
func (mf *MediaFolder) BeforeCreate(tx *gorm.DB) error {
	if mf.Settings == nil {
		mf.Settings = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (mf *MediaFolder) BeforeUpdate(tx *gorm.DB) error {
	if mf.Settings == nil {
		mf.Settings = make(map[string]interface{})
	}
	return nil
}

// IsRoot returns true if this is a root folder (no parent)
func (mf *MediaFolder) IsRoot() bool {
	return mf.ParentID == nil
}

// GetFullPath returns the full path of the folder
func (mf *MediaFolder) GetFullPath() string {
	return mf.Path
}

// CanAccess checks if a user can access this folder
func (mf *MediaFolder) CanAccess(userID uint) bool {
	if mf.Visibility == FolderVisibilityPublic {
		return true
	}
	
	if mf.UserID == userID {
		return true
	}
	
	// Check access permissions
	for _, perm := range mf.AccessPermissions {
		if perm.UserID == userID {
			return true
		}
	}
	
	return false
}

// Request/Response DTOs
type CreateMediaFolderRequest struct {
	TenantID    uint     `json:"tenant_id" validate:"required"`
	WebsiteID   uint     `json:"website_id" validate:"required"`
	ParentID    *uint    `json:"parent_id,omitempty"`
	Name        string   `json:"name" validate:"required,min=1,max=255"`
	Description *string  `json:"description,omitempty"`
	Type        FolderType `json:"type,omitempty"`
	Color       string   `json:"color,omitempty"`
	Icon        string   `json:"icon,omitempty"`
	Visibility  FolderVisibility `json:"visibility,omitempty"`
	Settings    map[string]interface{} `json:"settings,omitempty"`
}

type UpdateMediaFolderRequest struct {
	Name        *string  `json:"name,omitempty" validate:"omitempty,min=1,max=255"`
	Description *string  `json:"description,omitempty"`
	ParentID    *uint    `json:"parent_id,omitempty"`
	Color       *string  `json:"color,omitempty"`
	Icon        *string  `json:"icon,omitempty"`
	Visibility  *FolderVisibility `json:"visibility,omitempty"`
	Settings    map[string]interface{} `json:"settings,omitempty"`
}

type MediaFolderResponse struct {
	ID          uint         `json:"id"`
	TenantID    uint         `json:"tenant_id"`
	WebsiteID   uint         `json:"website_id"`
	ParentID    *uint        `json:"parent_id,omitempty"`
	UserID      uint         `json:"user_id"`
	Name        string       `json:"name"`
	Slug        string       `json:"slug"`
	Path        string       `json:"path"`
	Description *string      `json:"description,omitempty"`
	Type        FolderType   `json:"type"`
	Color       string       `json:"color"`
	Icon        string       `json:"icon"`
	Visibility  FolderVisibility `json:"visibility"`
	Level       uint         `json:"level"`
	SortOrder   uint         `json:"sort_order"`
	FileCount   uint         `json:"file_count"`
	TotalSize   uint64       `json:"total_size"`
	Status      FolderStatus `json:"status"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	Children    []MediaFolderResponse `json:"children,omitempty"`
	Files       []MediaFileResponse   `json:"files,omitempty"`
}

type MediaFolderTreeResponse struct {
	ID        uint                      `json:"id"`
	Name      string                    `json:"name"`
	Path      string                    `json:"path"`
	Icon      string                    `json:"icon"`
	Color     string                    `json:"color"`
	FileCount uint                      `json:"file_count"`
	TotalSize uint64                    `json:"total_size"`
	Level     uint                      `json:"level"`
	Children  []MediaFolderTreeResponse `json:"children,omitempty"`
}

// Additional request/response types
type MediaFolderSearchRequest struct {
	TenantID   uint                `json:"tenant_id"`
	WebsiteID  uint                `json:"website_id"`
	ParentID   *uint               `json:"parent_id,omitempty"`
	Type       *FolderType         `json:"type,omitempty"`
	Visibility *FolderVisibility   `json:"visibility,omitempty"`
	Query      *string             `json:"query,omitempty"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
}

type MediaFolderStats struct {
	TotalFolders uint   `json:"total_folders"`
	TotalFiles   uint   `json:"total_files"`
	TotalSize    uint64 `json:"total_size"`
}