package models

import (
	"time"

	"gorm.io/gorm"
)

// MediaTag represents a tag for categorizing media files
type MediaTag struct {
	ID        uint `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID  uint `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint `json:"website_id" gorm:"not null;index"`
	UserID    uint `json:"user_id" gorm:"not null;index"`

	// Tag information
	Name        string  `json:"name" gorm:"size:100;not null;index"`
	Slug        string  `json:"slug" gorm:"size:100;not null;index"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Tag appearance
	Color string  `json:"color" gorm:"size:7;default:'#3b82f6'"`
	Icon  *string `json:"icon,omitempty" gorm:"size:50"`

	// Tag categorization
	Category string  `json:"category" gorm:"size:50;default:'general'"`
	Type     TagType `json:"type" gorm:"type:enum('user','system','auto');not null;default:'user'"`

	// Usage statistics
	UsageCount       uint    `json:"usage_count" gorm:"default:0"`
	PopularityScore  float64 `json:"popularity_score" gorm:"default:0.0"`

	// Tag settings
	IsFeatured bool                   `json:"is_featured" gorm:"default:false"`
	IsPrivate  bool                   `json:"is_private" gorm:"default:false"`
	Settings   map[string]interface{} `json:"settings" gorm:"type:json"`

	// Status and timestamps
	Status    TagStatus `json:"status" gorm:"type:enum('active','archived','deleted');not null;default:'active'"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Creator   User                `json:"creator,omitempty" gorm:"foreignKey:UserID"`
	Tenant    Tenant              `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Website   Website             `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	Files     []MediaFile         `json:"files,omitempty" gorm:"many2many:media_file_tags"`
	FileTags  []MediaFileTag      `json:"file_tags,omitempty" gorm:"foreignKey:TagID"`
}

// TagUsageStats represents tag usage statistics
type TagUsageStats struct {
	TagID     uint   `json:"tag_id"`
	TagName   string `json:"tag_name"`
	FileCount uint   `json:"file_count"`
}

// TagType defines the type of tag
// @Enum user,system,auto
type TagType string

const (
	TagTypeUser   TagType = "user"
	TagTypeSystem TagType = "system"
	TagTypeAuto   TagType = "auto"
)

// TagStatus defines the status of tag
// @Enum active,archived,deleted
type TagStatus string

const (
	TagStatusActive   TagStatus = "active"
	TagStatusArchived TagStatus = "archived"
	TagStatusDeleted  TagStatus = "deleted"
)

// TableName returns the table name for MediaTag
func (MediaTag) TableName() string {
	return "media_tags"
}

// BeforeCreate hook for GORM
func (mt *MediaTag) BeforeCreate(tx *gorm.DB) error {
	if mt.Settings == nil {
		mt.Settings = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (mt *MediaTag) BeforeUpdate(tx *gorm.DB) error {
	if mt.Settings == nil {
		mt.Settings = make(map[string]interface{})
	}
	return nil
}

// IsSystemTag returns true if this is a system tag
func (mt *MediaTag) IsSystemTag() bool {
	return mt.Type == TagTypeSystem
}

// IsAutoTag returns true if this is an auto-generated tag
func (mt *MediaTag) IsAutoTag() bool {
	return mt.Type == TagTypeAuto
}

// IsUserTag returns true if this is a user-created tag
func (mt *MediaTag) IsUserTag() bool {
	return mt.Type == TagTypeUser
}

// CanEdit checks if a user can edit this tag
func (mt *MediaTag) CanEdit(userID uint) bool {
	if mt.Type == TagTypeSystem {
		return false
	}
	
	if mt.UserID == userID {
		return true
	}
	
	// Additional permission checks can be added here
	return false
}

// CanDelete checks if a user can delete this tag
func (mt *MediaTag) CanDelete(userID uint) bool {
	if mt.Type == TagTypeSystem {
		return false
	}
	
	if mt.UserID == userID {
		return true
	}
	
	// Additional permission checks can be added here
	return false
}

// Request/Response DTOs
type CreateMediaTagRequest struct {
	TenantID    uint    `json:"tenant_id" validate:"required"`
	WebsiteID   uint    `json:"website_id" validate:"required"`
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Description *string `json:"description,omitempty"`
	Color       string  `json:"color,omitempty"`
	Icon        *string `json:"icon,omitempty"`
	Category    string  `json:"category,omitempty"`
	Type        TagType `json:"type,omitempty"`
	IsFeatured  bool    `json:"is_featured,omitempty"`
	IsPrivate   bool    `json:"is_private,omitempty"`
	Settings    map[string]interface{} `json:"settings,omitempty"`
}

type UpdateMediaTagRequest struct {
	Name        *string `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Description *string `json:"description,omitempty"`
	Color       *string `json:"color,omitempty"`
	Icon        *string `json:"icon,omitempty"`
	Category    *string `json:"category,omitempty"`
	IsFeatured  *bool   `json:"is_featured,omitempty"`
	IsPrivate   *bool   `json:"is_private,omitempty"`
	Settings    map[string]interface{} `json:"settings,omitempty"`
}

type MediaTagResponse struct {
	ID              uint      `json:"id"`
	TenantID        uint      `json:"tenant_id"`
	WebsiteID       uint      `json:"website_id"`
	UserID          uint      `json:"user_id"`
	Name            string    `json:"name"`
	Slug            string    `json:"slug"`
	Description     *string   `json:"description,omitempty"`
	Color           string    `json:"color"`
	Icon            *string   `json:"icon,omitempty"`
	Category        string    `json:"category"`
	Type            TagType   `json:"type"`
	UsageCount      uint      `json:"usage_count"`
	PopularityScore float64   `json:"popularity_score"`
	IsFeatured      bool      `json:"is_featured"`
	IsPrivate       bool      `json:"is_private"`
	Status          TagStatus `json:"status"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

type MediaTagListResponse struct {
	ID              uint      `json:"id"`
	Name            string    `json:"name"`
	Slug            string    `json:"slug"`
	Description     *string   `json:"description,omitempty"`
	Color           string    `json:"color"`
	Icon            *string   `json:"icon,omitempty"`
	Category        string    `json:"category"`
	Type            TagType   `json:"type"`
	UsageCount      uint      `json:"usage_count"`
	PopularityScore float64   `json:"popularity_score"`
	IsFeatured      bool      `json:"is_featured"`
	IsPrivate       bool      `json:"is_private"`
	Status          TagStatus `json:"status"`
	CreatedAt       time.Time `json:"created_at"`
}

type MediaTagSearchRequest struct {
	TenantID   uint     `json:"tenant_id" validate:"required"`
	WebsiteID  uint     `json:"website_id" validate:"required"`
	Query      *string  `json:"query,omitempty"`
	Category   *string  `json:"category,omitempty"`
	Type       *TagType `json:"type,omitempty"`
	IsFeatured *bool    `json:"is_featured,omitempty"`
	IsPrivate  *bool    `json:"is_private,omitempty"`
	SortBy     *string  `json:"sort_by,omitempty"`   // name, usage_count, popularity_score, created_at
	SortOrder  *string  `json:"sort_order,omitempty"` // asc, desc
	Page       int      `json:"page,omitempty"`
	PageSize   int      `json:"page_size,omitempty"`
}

type MediaTagStats struct {
	TotalTags        uint            `json:"total_tags"`
	ActiveTags       uint            `json:"active_tags"`
	ArchivedTags     uint            `json:"archived_tags"`
	UserTags         uint            `json:"user_tags"`
	SystemTags       uint            `json:"system_tags"`
	AutoTags         uint            `json:"auto_tags"`
	FeaturedTags     uint            `json:"featured_tags"`
	PrivateTags      uint            `json:"private_tags"`
	TagsByCategory   map[string]uint `json:"tags_by_category"`
	MostUsedTags     []MediaTagResponse `json:"most_used_tags"`
	RecentTags       []MediaTagResponse `json:"recent_tags"`
	TotalUsage       uint            `json:"total_usage"`
	AverageUsage     float64         `json:"average_usage"`
}

type BulkTagOperationRequest struct {
	TagIDs    []uint `json:"tag_ids" validate:"required,min=1"`
	Operation string `json:"operation" validate:"required,oneof=delete archive activate feature unfeature"`
}

type BulkTagOperationResponse struct {
	TotalTags     uint   `json:"total_tags"`
	ProcessedTags uint   `json:"processed_tags"`
	FailedTags    uint   `json:"failed_tags"`
	Status        string `json:"status"`
	Message       string `json:"message"`
}

type MediaTagSuggestionsRequest struct {
	TenantID  uint   `json:"tenant_id" validate:"required"`
	WebsiteID uint   `json:"website_id" validate:"required"`
	Query     string `json:"query" validate:"required,min=1"`
	Limit     int    `json:"limit,omitempty"`
}

type MediaTagSuggestionsResponse struct {
	Query       string               `json:"query"`
	Suggestions []MediaTagResponse   `json:"suggestions"`
	Total       uint                 `json:"total"`
}

type MediaTagCloudRequest struct {
	TenantID  uint    `json:"tenant_id" validate:"required"`
	WebsiteID uint    `json:"website_id" validate:"required"`
	MinUsage  *uint   `json:"min_usage,omitempty"`
	MaxTags   *uint   `json:"max_tags,omitempty"`
	Category  *string `json:"category,omitempty"`
}

type MediaTagCloudResponse struct {
	Tags []MediaTagCloudItem `json:"tags"`
	Total uint               `json:"total"`
}

type MediaTagCloudItem struct {
	ID          uint    `json:"id"`
	Name        string  `json:"name"`
	Slug        string  `json:"slug"`
	Color       string  `json:"color"`
	UsageCount  uint    `json:"usage_count"`
	Weight      float64 `json:"weight"` // Normalized weight for display
	FontSize    uint    `json:"font_size"` // Suggested font size
}