package models

import (
	"time"

	"gorm.io/gorm"
)

// MediaFile represents a media file in the system
type MediaFile struct {
	ID         uint  `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID   uint  `json:"tenant_id" gorm:"not null;index"`
	WebsiteID  uint  `json:"website_id" gorm:"not null;index"`
	FolderID   *uint `json:"folder_id,omitempty" gorm:"index"`
	UserID     uint  `json:"user_id" gorm:"not null;index"`

	// File information
	Filename         string `json:"filename" gorm:"size:255;not null;index"`
	OriginalFilename string `json:"original_filename" gorm:"size:255;not null"`
	Slug             string `json:"slug" gorm:"size:255;not null;index"`
	MimeType         string `json:"mime_type" gorm:"size:100;not null;index"`
	FileSize         uint64 `json:"file_size" gorm:"not null"`
	FileHash         string `json:"file_hash" gorm:"size:64;not null;index"`

	// Storage information
	StorageType     StorageType `json:"storage_type" gorm:"type:enum('local','minio','s3','gcs');not null;default:'local'"`
	StoragePath     string      `json:"storage_path" gorm:"size:500;not null"`
	PublicURL       string      `json:"public_url" gorm:"size:500;not null"`
	StorageProvider *string     `json:"storage_provider,omitempty" gorm:"size:100"`
	
	// CDN information
	CDNUrl      *string `json:"cdn_url,omitempty" gorm:"size:500"`
	CDNProvider *string `json:"cdn_provider,omitempty" gorm:"size:100"`
	
	// Optimization information
	OptimizedPath *string    `json:"optimized_path,omitempty" gorm:"size:500"`
	OptimizedSize *int64     `json:"optimized_size,omitempty"`
	OptimizedURL  *string    `json:"optimized_url,omitempty" gorm:"size:500"`
	OptimizedAt   *time.Time `json:"optimized_at,omitempty"`

	// Media metadata
	Width    *uint                  `json:"width,omitempty"`
	Height   *uint                  `json:"height,omitempty"`
	Duration *uint                  `json:"duration,omitempty"` // in seconds for video/audio
	Metadata map[string]interface{} `json:"metadata" gorm:"type:json"`

	// File categorization
	FileType FileType `json:"file_type" gorm:"type:enum('image','video','audio','document','archive','other');not null"`
	Category string   `json:"category" gorm:"size:100;default:'general'"`

	// SEO and accessibility
	AltText     *string `json:"alt_text,omitempty" gorm:"size:255"`
	Title       *string `json:"title,omitempty" gorm:"size:255"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Access control
	Visibility        FileVisibility     `json:"visibility" gorm:"type:enum('public','private','shared');not null;default:'public'"`
	AccessPermissions []AccessPermission `json:"access_permissions" gorm:"type:json"`

	// Usage tracking
	ViewCount       uint       `json:"view_count" gorm:"default:0"`
	DownloadCount   uint       `json:"download_count" gorm:"default:0"`
	LastAccessedAt  *time.Time `json:"last_accessed_at,omitempty"`

	// Processing status
	Status           FileStatus                 `json:"status" gorm:"type:enum('uploading','processing','ready','error','deleted');not null;default:'uploading'"`
	ProcessingStatus map[string]interface{}     `json:"processing_status" gorm:"type:json"`
	ErrorMessage     *string                    `json:"error_message,omitempty" gorm:"type:text"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Folder     *MediaFolder    `json:"folder,omitempty" gorm:"foreignKey:FolderID"`
	Uploader   User            `json:"uploader,omitempty" gorm:"foreignKey:UserID"`
	Tenant     Tenant          `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Website    Website         `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	Thumbnails []MediaThumbnail `json:"thumbnails,omitempty" gorm:"foreignKey:FileID"`
	Tags       []MediaTag      `json:"tags,omitempty" gorm:"many2many:media_file_tags"`
}

// StorageType defines the storage type for files
// @Enum local,minio,s3,gcs
type StorageType string

const (
	StorageTypeLocal StorageType = "local"
	StorageTypeMinio StorageType = "minio"
	StorageTypeS3    StorageType = "s3"
	StorageTypeGCS   StorageType = "gcs"
)

// FileType defines the type of file
// @Enum image,video,audio,document,archive,other
type FileType string

const (
	FileTypeImage    FileType = "image"
	FileTypeVideo    FileType = "video"
	FileTypeAudio    FileType = "audio"
	FileTypeDocument FileType = "document"
	FileTypeArchive  FileType = "archive"
	FileTypeOther    FileType = "other"
)

// FileVisibility defines the visibility of file
// @Enum public,private,shared
type FileVisibility string

const (
	FileVisibilityPublic  FileVisibility = "public"
	FileVisibilityPrivate FileVisibility = "private"
	FileVisibilityShared  FileVisibility = "shared"
)

// FileStatus defines the status of file processing
// @Enum uploading,processing,ready,error,deleted
type FileStatus string

const (
	FileStatusUploading  FileStatus = "uploading"
	FileStatusProcessing FileStatus = "processing"
	FileStatusReady      FileStatus = "ready"
	FileStatusError      FileStatus = "error"
	FileStatusDeleted    FileStatus = "deleted"
)

// TableName returns the table name for MediaFile
func (MediaFile) TableName() string {
	return "media_files"
}

// BeforeCreate hook for GORM
func (mf *MediaFile) BeforeCreate(tx *gorm.DB) error {
	if mf.Metadata == nil {
		mf.Metadata = make(map[string]interface{})
	}
	if mf.ProcessingStatus == nil {
		mf.ProcessingStatus = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (mf *MediaFile) BeforeUpdate(tx *gorm.DB) error {
	if mf.Metadata == nil {
		mf.Metadata = make(map[string]interface{})
	}
	if mf.ProcessingStatus == nil {
		mf.ProcessingStatus = make(map[string]interface{})
	}
	return nil
}

// IsImage returns true if the file is an image
func (mf *MediaFile) IsImage() bool {
	return mf.FileType == FileTypeImage
}

// IsVideo returns true if the file is a video
func (mf *MediaFile) IsVideo() bool {
	return mf.FileType == FileTypeVideo
}

// IsAudio returns true if the file is an audio
func (mf *MediaFile) IsAudio() bool {
	return mf.FileType == FileTypeAudio
}

// IsDocument returns true if the file is a document
func (mf *MediaFile) IsDocument() bool {
	return mf.FileType == FileTypeDocument
}

// HasThumbnails returns true if the file can have thumbnails
func (mf *MediaFile) HasThumbnails() bool {
	return mf.FileType == FileTypeImage || mf.FileType == FileTypeVideo
}

// CanAccess checks if a user can access this file
func (mf *MediaFile) CanAccess(userID uint) bool {
	if mf.Visibility == FileVisibilityPublic {
		return true
	}
	
	if mf.UserID == userID {
		return true
	}
	
	// Check access permissions
	for _, perm := range mf.AccessPermissions {
		if perm.UserID == userID {
			return true
		}
	}
	
	return false
}

// GetFileExtension returns the file extension
func (mf *MediaFile) GetFileExtension() string {
	if len(mf.Filename) > 0 {
		for i := len(mf.Filename) - 1; i >= 0; i-- {
			if mf.Filename[i] == '.' {
				return mf.Filename[i:]
			}
		}
	}
	return ""
}

// Request/Response DTOs
type CreateMediaFileRequest struct {
	TenantID    uint         `json:"tenant_id" validate:"required"`
	WebsiteID   uint         `json:"website_id" validate:"required"`
	FolderID    *uint        `json:"folder_id,omitempty"`
	Filename    string       `json:"filename" validate:"required,min=1,max=255"`
	MimeType    string       `json:"mime_type" validate:"required"`
	FileSize    uint64       `json:"file_size" validate:"required"`
	StorageType StorageType  `json:"storage_type,omitempty"`
	StoragePath string       `json:"storage_path" validate:"required"`
	PublicURL   string       `json:"public_url" validate:"required"`
	Width       *uint        `json:"width,omitempty"`
	Height      *uint        `json:"height,omitempty"`
	Duration    *uint        `json:"duration,omitempty"`
	AltText     *string      `json:"alt_text,omitempty"`
	Title       *string      `json:"title,omitempty"`
	Description *string      `json:"description,omitempty"`
	Category    string       `json:"category,omitempty"`
	Visibility  FileVisibility `json:"visibility,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

type UpdateMediaFileRequest struct {
	FolderID    *uint        `json:"folder_id,omitempty"`
	Filename    *string      `json:"filename,omitempty" validate:"omitempty,min=1,max=255"`
	AltText     *string      `json:"alt_text,omitempty"`
	Title       *string      `json:"title,omitempty"`
	Description *string      `json:"description,omitempty"`
	Category    *string      `json:"category,omitempty"`
	Visibility  *FileVisibility `json:"visibility,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

type MediaFileResponse struct {
	ID               uint           `json:"id"`
	TenantID         uint           `json:"tenant_id"`
	WebsiteID        uint           `json:"website_id"`
	FolderID         *uint          `json:"folder_id,omitempty"`
	UserID           uint           `json:"user_id"`
	Filename         string         `json:"filename"`
	OriginalFilename string         `json:"original_filename"`
	Slug             string         `json:"slug"`
	MimeType         string         `json:"mime_type"`
	FileSize         uint64         `json:"file_size"`
	FileHash         string         `json:"file_hash"`
	StorageType      StorageType    `json:"storage_type"`
	StoragePath      string         `json:"storage_path"`
	PublicURL        string         `json:"public_url"`
	StorageProvider  *string        `json:"storage_provider,omitempty"`
	CDNUrl           *string        `json:"cdn_url,omitempty"`
	CDNProvider      *string        `json:"cdn_provider,omitempty"`
	OptimizedPath    *string        `json:"optimized_path,omitempty"`
	OptimizedSize    *int64         `json:"optimized_size,omitempty"`
	OptimizedURL     *string        `json:"optimized_url,omitempty"`
	OptimizedAt      *time.Time     `json:"optimized_at,omitempty"`
	Width            *uint          `json:"width,omitempty"`
	Height           *uint          `json:"height,omitempty"`
	Duration         *uint          `json:"duration,omitempty"`
	FileType         FileType       `json:"file_type"`
	Category         string         `json:"category"`
	AltText          *string        `json:"alt_text,omitempty"`
	Title            *string        `json:"title,omitempty"`
	Description      *string        `json:"description,omitempty"`
	Visibility       FileVisibility `json:"visibility"`
	ViewCount        uint           `json:"view_count"`
	DownloadCount    uint           `json:"download_count"`
	LastAccessedAt   *time.Time     `json:"last_accessed_at,omitempty"`
	Status           FileStatus     `json:"status"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	Folder           *MediaFolderResponse `json:"folder,omitempty"`
	Thumbnails       []MediaThumbnailResponse `json:"thumbnails,omitempty"`
	Tags             []MediaTagResponse `json:"tags,omitempty"`
}

type MediaFileListResponse struct {
	ID               uint           `json:"id"`
	Filename         string         `json:"filename"`
	OriginalFilename string         `json:"original_filename"`
	MimeType         string         `json:"mime_type"`
	FileSize         uint64         `json:"file_size"`
	FileType         FileType       `json:"file_type"`
	PublicURL        string         `json:"public_url"`
	Width            *uint          `json:"width,omitempty"`
	Height           *uint          `json:"height,omitempty"`
	Duration         *uint          `json:"duration,omitempty"`
	AltText          *string        `json:"alt_text,omitempty"`
	Title            *string        `json:"title,omitempty"`
	Status           FileStatus     `json:"status"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	Thumbnails       []MediaThumbnailResponse `json:"thumbnails,omitempty"`
}

type MediaFileUploadResponse struct {
	ID        uint        `json:"id"`
	Filename  string      `json:"filename"`
	PublicURL string      `json:"public_url"`
	Status    FileStatus  `json:"status"`
	CreatedAt time.Time   `json:"created_at"`
}

type MediaFileSearchRequest struct {
	TenantID   uint       `json:"tenant_id" validate:"required"`
	WebsiteID  uint       `json:"website_id" validate:"required"`
	FolderID   *uint      `json:"folder_id,omitempty"`
	Query      *string    `json:"query,omitempty"`
	FileType   *FileType  `json:"file_type,omitempty"`
	Category   *string    `json:"category,omitempty"`
	MimeType   *string    `json:"mime_type,omitempty"`
	Tags       []string   `json:"tags,omitempty"`
	MinSize    *uint64    `json:"min_size,omitempty"`
	MaxSize    *uint64    `json:"max_size,omitempty"`
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`
	SortBy     *string    `json:"sort_by,omitempty"`   // name, size, created_at, updated_at
	SortOrder  *string    `json:"sort_order,omitempty"` // asc, desc
	Page       int        `json:"page,omitempty"`
	PageSize   int        `json:"page_size,omitempty"`
}

type MediaFileStats struct {
	TotalFiles      uint   `json:"total_files"`
	TotalSize       uint64 `json:"total_size"`
	TotalFolders    uint   `json:"total_folders"`
	ImageFiles      uint   `json:"image_files"`
	VideoFiles      uint   `json:"video_files"`
	AudioFiles      uint   `json:"audio_files"`
	DocumentFiles   uint   `json:"document_files"`
	ArchiveFiles    uint   `json:"archive_files"`
	OtherFiles      uint   `json:"other_files"`
	StorageUsed     uint64 `json:"storage_used"`
	StorageLimit    uint64 `json:"storage_limit"`
	StoragePercent  float64 `json:"storage_percent"`
}