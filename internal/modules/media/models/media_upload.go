package models

import (
	"io"
	"time"
)

// UploadRequest represents a file upload request
type UploadRequest struct {
	TenantID        uint                   `json:"tenant_id" validate:"required"`
	WebsiteID       uint                   `json:"website_id" validate:"required"`
	FolderID        *uint                  `json:"folder_id,omitempty"`
	UserID          uint                   `json:"user_id" validate:"required"`
	Reader          io.Reader              `json:"-"`
	Filename        string                 `json:"filename" validate:"required"`
	OriginalName    string                 `json:"original_name" validate:"required"`
	MimeType        string                 `json:"mime_type" validate:"required"`
	Size            int64                  `json:"size" validate:"required,min=1"`
	Path            string                 `json:"path" validate:"required"`
	StorageProvider string                 `json:"storage_provider,omitempty"`
	AltText         *string                `json:"alt_text,omitempty"`
	Title           *string                `json:"title,omitempty"`
	Description     *string                `json:"description,omitempty"`
	Category        string                 `json:"category,omitempty"`
	Visibility      FileVisibility         `json:"visibility,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// MultiUploadRequest represents a multi-file upload request
type MultiUploadRequest struct {
	TenantID        uint                   `json:"tenant_id" validate:"required"`
	WebsiteID       uint                   `json:"website_id" validate:"required"`
	FolderID        *uint                  `json:"folder_id,omitempty"`
	UserID          uint                   `json:"user_id" validate:"required"`
	Files           []UploadFileInfo       `json:"files" validate:"required,dive"`
	StorageProvider string                 `json:"storage_provider,omitempty"`
	Category        string                 `json:"category,omitempty"`
	Visibility      FileVisibility         `json:"visibility,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// UploadFileInfo represents information about a file to upload
type UploadFileInfo struct {
	Reader       io.Reader `json:"-"`
	Filename     string    `json:"filename" validate:"required"`
	OriginalName string    `json:"original_name" validate:"required"`
	MimeType     string    `json:"mime_type" validate:"required"`
	Size         int64     `json:"size" validate:"required,min=1"`
	AltText      *string   `json:"alt_text,omitempty"`
	Title        *string   `json:"title,omitempty"`
	Description  *string   `json:"description,omitempty"`
}

// StorageResult represents the result of a storage operation
type StorageResult struct {
	Path            string                 `json:"path"`
	URL             string                 `json:"url"`
	CDNURL          *string                `json:"cdn_url,omitempty"`
	Size            int64                  `json:"size"`
	StorageProvider string                 `json:"storage_provider"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	UploadedAt      time.Time              `json:"uploaded_at"`
	Hash            string                 `json:"hash,omitempty"`
	ETag            string                 `json:"etag,omitempty"`
}

// UploadResult represents the result of an upload operation
type UploadResult struct {
	File        *MediaFile     `json:"file"`
	Storage     *StorageResult `json:"storage"`
	Thumbnails  []MediaThumbnail `json:"thumbnails,omitempty"`
	ProcessingJobs []ProcessingJob `json:"processing_jobs,omitempty"`
}

// MultiUploadResult represents the result of a multi-file upload
type MultiUploadResult struct {
	SuccessfulUploads []UploadResult `json:"successful_uploads"`
	FailedUploads     []UploadError  `json:"failed_uploads"`
	TotalFiles        int            `json:"total_files"`
	SuccessCount      int            `json:"success_count"`
	FailureCount      int            `json:"failure_count"`
}

// UploadError represents an upload error
type UploadError struct {
	Filename string `json:"filename"`
	Error    string `json:"error"`
	Code     string `json:"code,omitempty"`
}

// ProcessingJob represents a background processing job
type ProcessingJob struct {
	ID     string `json:"id"`
	Type   string `json:"type"`   // thumbnail, optimization, analysis
	Status string `json:"status"` // queued, processing, completed, failed
	FileID uint   `json:"file_id"`
}

// StorageInfo represents storage information and statistics
type StorageInfo struct {
	TotalFiles      uint                            `json:"total_files"`
	TotalSize       uint64                          `json:"total_size"`
	ProviderUsage   map[string]StorageProviderUsage `json:"provider_usage"`
	Providers       []string                        `json:"providers"`
	DefaultProvider string                          `json:"default_provider"`
	Quotas          StorageQuotas                   `json:"quotas"`
}

// StorageProviderUsage represents usage statistics for a storage provider
type StorageProviderUsage struct {
	Provider  string `json:"provider"`
	FileCount uint   `json:"file_count"`
	TotalSize uint64 `json:"total_size"`
	Percentage float64 `json:"percentage"`
}

// StorageQuotas represents storage quotas and limits
type StorageQuotas struct {
	TotalQuota     uint64  `json:"total_quota"`     // in bytes
	UsedSpace      uint64  `json:"used_space"`      // in bytes
	AvailableSpace uint64  `json:"available_space"` // in bytes
	UsagePercent   float64 `json:"usage_percent"`
	FileLimit      uint    `json:"file_limit"`      // max number of files
	MaxFileSize    uint64  `json:"max_file_size"`   // max file size in bytes
}

// SyncResult represents the result of a storage sync operation
type SyncResult struct {
	TotalFiles    int       `json:"total_files"`
	ValidFiles    int       `json:"valid_files"`
	InvalidFiles  int       `json:"invalid_files"`
	MissingFiles  int       `json:"missing_files"`
	OrphanedFiles int       `json:"orphaned_files"`
	Provider      string    `json:"provider"`
	SyncedAt      time.Time `json:"synced_at"`
	Errors        []string  `json:"errors,omitempty"`
}

// MediaFileFilter represents filter options for file listing
type MediaFileFilter struct {
	TenantID        *uint       `json:"tenant_id,omitempty"`
	WebsiteID       *uint       `json:"website_id,omitempty"`
	FolderID        *uint       `json:"folder_id,omitempty"`
	UserID          *uint       `json:"user_id,omitempty"`
	FileType        *FileType   `json:"file_type,omitempty"`
	MimeType        *string     `json:"mime_type,omitempty"`
	Category        *string     `json:"category,omitempty"`
	Visibility      *FileVisibility `json:"visibility,omitempty"`
	Status          *string     `json:"status,omitempty"`
	StorageProvider *string     `json:"storage_provider,omitempty"`
	Tags            []string    `json:"tags,omitempty"`
	Query           *string     `json:"query,omitempty"`
	MinSize         *uint64     `json:"min_size,omitempty"`
	MaxSize         *uint64     `json:"max_size,omitempty"`
	CreatedAfter    *time.Time  `json:"created_after,omitempty"`
	CreatedBefore   *time.Time  `json:"created_before,omitempty"`
	SortBy          *string     `json:"sort_by,omitempty"`   // name, size, created_at, updated_at
	SortOrder       *string     `json:"sort_order,omitempty"` // asc, desc
}

// UploadConfig represents upload configuration for a tenant/website
type UploadConfig struct {
	MaxFileSize        uint64   `json:"max_file_size"`        // in bytes
	MaxFilesPerUpload  int      `json:"max_files_per_upload"`
	AllowedMimeTypes   []string `json:"allowed_mime_types"`
	AllowedExtensions  []string `json:"allowed_extensions"`
	RequireAuth        bool     `json:"require_auth"`
	DefaultVisibility  FileVisibility `json:"default_visibility"`
	DefaultStorageProvider string `json:"default_storage_provider"`
	EnableThumbnails   bool     `json:"enable_thumbnails"`
	EnableOptimization bool     `json:"enable_optimization"`
	EnableVirusScanning bool    `json:"enable_virus_scanning"`
	EnableWatermark    bool     `json:"enable_watermark"`
	WatermarkConfig    *WatermarkConfig `json:"watermark_config,omitempty"`
}

// WatermarkConfig represents watermark configuration
type WatermarkConfig struct {
	Enabled     bool    `json:"enabled"`
	Text        string  `json:"text,omitempty"`
	ImagePath   string  `json:"image_path,omitempty"`
	Position    string  `json:"position"`    // top-left, top-right, bottom-left, bottom-right, center
	Opacity     float64 `json:"opacity"`     // 0.0 to 1.0
	Scale       float64 `json:"scale"`       // 0.1 to 1.0
	Margin      int     `json:"margin"`      // margin from edge in pixels
}

// UploadPolicy represents upload policies and restrictions
type UploadPolicy struct {
	MaxFileSize       uint64   `json:"max_file_size"`
	MaxTotalSize      uint64   `json:"max_total_size"`
	AllowedMimeTypes  []string `json:"allowed_mime_types"`
	BlockedMimeTypes  []string `json:"blocked_mime_types"`
	AllowedExtensions []string `json:"allowed_extensions"`
	BlockedExtensions []string `json:"blocked_extensions"`
	RequireAuth       bool     `json:"require_auth"`
	AllowAnonymous    bool     `json:"allow_anonymous"`
	RateLimits        RateLimits `json:"rate_limits"`
}

// RateLimits represents rate limiting configuration
type RateLimits struct {
	UploadsPerMinute int `json:"uploads_per_minute"`
	UploadsPerHour   int `json:"uploads_per_hour"`
	UploadsPerDay    int `json:"uploads_per_day"`
	BytesPerMinute   uint64 `json:"bytes_per_minute"`
	BytesPerHour     uint64 `json:"bytes_per_hour"`
	BytesPerDay      uint64 `json:"bytes_per_day"`
}

// ChunkUploadRequest represents a chunked upload request
type ChunkUploadRequest struct {
	TenantID      uint      `json:"tenant_id" validate:"required"`
	WebsiteID     uint      `json:"website_id" validate:"required"`
	UserID        uint      `json:"user_id" validate:"required"`
	UploadID      string    `json:"upload_id" validate:"required"`
	ChunkNumber   int       `json:"chunk_number" validate:"required,min=1"`
	TotalChunks   int       `json:"total_chunks" validate:"required,min=1"`
	ChunkSize     int64     `json:"chunk_size" validate:"required,min=1"`
	TotalSize     int64     `json:"total_size" validate:"required,min=1"`
	Filename      string    `json:"filename" validate:"required"`
	MimeType      string    `json:"mime_type" validate:"required"`
	ChunkData     io.Reader `json:"-"`
	Hash          string    `json:"hash,omitempty"`
}

// ChunkUploadResult represents the result of a chunk upload
type ChunkUploadResult struct {
	UploadID      string  `json:"upload_id"`
	ChunkNumber   int     `json:"chunk_number"`
	TotalChunks   int     `json:"total_chunks"`
	IsComplete    bool    `json:"is_complete"`
	PercentComplete float64 `json:"percent_complete"`
	NextChunk     *int    `json:"next_chunk,omitempty"`
	File          *MediaFile `json:"file,omitempty"` // Only set when upload is complete
}

// ResumableUpload represents a resumable upload session
type ResumableUpload struct {
	ID            string                 `json:"id"`
	TenantID      uint                   `json:"tenant_id"`
	WebsiteID     uint                   `json:"website_id"`
	UserID        uint                   `json:"user_id"`
	Filename      string                 `json:"filename"`
	MimeType      string                 `json:"mime_type"`
	TotalSize     int64                  `json:"total_size"`
	ChunkSize     int64                  `json:"chunk_size"`
	TotalChunks   int                    `json:"total_chunks"`
	UploadedChunks []int                 `json:"uploaded_chunks"`
	Status        string                 `json:"status"` // active, paused, completed, failed, expired
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	ExpiresAt     time.Time              `json:"expires_at"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}