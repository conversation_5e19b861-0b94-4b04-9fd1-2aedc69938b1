package models

import (
	"time"

	"gorm.io/gorm"
)

// MediaFileTag represents the many-to-many relationship between files and tags
type MediaFileTag struct {
	ID     uint `json:"id" gorm:"primaryKey;autoIncrement"`
	FileID uint `json:"file_id" gorm:"not null;index"`
	TagID  uint `json:"tag_id" gorm:"not null;index"`
	UserID uint `json:"user_id" gorm:"not null;index"`

	// Tagging information
	TaggedAt      time.Time `json:"tagged_at" gorm:"autoCreateTime"`
	TagSource     TagSource `json:"tag_source" gorm:"type:enum('manual','auto','ai','import');not null;default:'manual'"`
	ConfidenceScore float64 `json:"confidence_score" gorm:"default:1.0"` // For AI-generated tags (0.0 to 1.0)

	// Tag metadata
	TagContext map[string]interface{} `json:"tag_context" gorm:"type:json"`

	// Relationships
	File    MediaFile `json:"file,omitempty" gorm:"foreignKey:FileID"`
	Tag     MediaTag  `json:"tag,omitempty" gorm:"foreignKey:TagID"`
	Tagger  User      `json:"tagger,omitempty" gorm:"foreignKey:UserID"`
}

// TagSource defines how a tag was applied to a file
type TagSource string

const (
	TagSourceManual TagSource = "manual"
	TagSourceAuto   TagSource = "auto"
	TagSourceAI     TagSource = "ai"
	TagSourceImport TagSource = "import"
)

// TableName returns the table name for MediaFileTag
func (MediaFileTag) TableName() string {
	return "media_file_tags"
}

// BeforeCreate hook for GORM
func (mft *MediaFileTag) BeforeCreate(tx *gorm.DB) error {
	if mft.TagContext == nil {
		mft.TagContext = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (mft *MediaFileTag) BeforeUpdate(tx *gorm.DB) error {
	if mft.TagContext == nil {
		mft.TagContext = make(map[string]interface{})
	}
	return nil
}

// IsManualTag returns true if this tag was manually applied
func (mft *MediaFileTag) IsManualTag() bool {
	return mft.TagSource == TagSourceManual
}

// IsAutoTag returns true if this tag was auto-generated
func (mft *MediaFileTag) IsAutoTag() bool {
	return mft.TagSource == TagSourceAuto || mft.TagSource == TagSourceAI
}

// IsAITag returns true if this tag was generated by AI
func (mft *MediaFileTag) IsAITag() bool {
	return mft.TagSource == TagSourceAI
}

// IsImportedTag returns true if this tag was imported
func (mft *MediaFileTag) IsImportedTag() bool {
	return mft.TagSource == TagSourceImport
}

// GetConfidenceLevel returns a string representation of confidence level
func (mft *MediaFileTag) GetConfidenceLevel() string {
	if mft.ConfidenceScore >= 0.9 {
		return "high"
	} else if mft.ConfidenceScore >= 0.7 {
		return "medium"
	} else if mft.ConfidenceScore >= 0.5 {
		return "low"
	}
	return "very_low"
}

// IsHighConfidence returns true if the confidence score is high
func (mft *MediaFileTag) IsHighConfidence() bool {
	return mft.ConfidenceScore >= 0.8
}

// Request/Response DTOs
type CreateFileTagRequest struct {
	FileID          uint                   `json:"file_id" validate:"required"`
	TagID           uint                   `json:"tag_id" validate:"required"`
	TagSource       TagSource              `json:"tag_source,omitempty"`
	ConfidenceScore float64                `json:"confidence_score,omitempty"`
	TagContext      map[string]interface{} `json:"tag_context,omitempty"`
}

type UpdateFileTagRequest struct {
	ConfidenceScore *float64               `json:"confidence_score,omitempty"`
	TagContext      map[string]interface{} `json:"tag_context,omitempty"`
}

type MediaFileTagResponse struct {
	ID              uint                   `json:"id"`
	FileID          uint                   `json:"file_id"`
	TagID           uint                   `json:"tag_id"`
	UserID          uint                   `json:"user_id"`
	TaggedAt        time.Time              `json:"tagged_at"`
	TagSource       TagSource              `json:"tag_source"`
	ConfidenceScore float64                `json:"confidence_score"`
	TagContext      map[string]interface{} `json:"tag_context"`
	Tag             *MediaTagResponse      `json:"tag,omitempty"`
	Tagger          *User                  `json:"tagger,omitempty"`
}

type BulkTagFilesRequest struct {
	FileIDs         []uint                 `json:"file_ids" validate:"required,min=1"`
	TagIDs          []uint                 `json:"tag_ids" validate:"required,min=1"`
	TagSource       TagSource              `json:"tag_source,omitempty"`
	ConfidenceScore float64                `json:"confidence_score,omitempty"`
	TagContext      map[string]interface{} `json:"tag_context,omitempty"`
}

type BulkTagFilesResponse struct {
	TotalFiles     uint   `json:"total_files"`
	ProcessedFiles uint   `json:"processed_files"`
	FailedFiles    uint   `json:"failed_files"`
	TotalTags      uint   `json:"total_tags"`
	ProcessedTags  uint   `json:"processed_tags"`
	Status         string `json:"status"`
	Message        string `json:"message"`
}

type BulkUntagFilesRequest struct {
	FileIDs []uint `json:"file_ids" validate:"required,min=1"`
	TagIDs  []uint `json:"tag_ids" validate:"required,min=1"`
}

type BulkUntagFilesResponse struct {
	TotalFiles     uint   `json:"total_files"`
	ProcessedFiles uint   `json:"processed_files"`
	FailedFiles    uint   `json:"failed_files"`
	TotalTags      uint   `json:"total_tags"`
	RemovedTags    uint   `json:"removed_tags"`
	Status         string `json:"status"`
	Message        string `json:"message"`
}

type FileTagsRequest struct {
	FileID uint `json:"file_id" validate:"required"`
}

type FileTagsResponse struct {
	FileID uint                   `json:"file_id"`
	Tags   []MediaFileTagResponse `json:"tags"`
	Total  uint                   `json:"total"`
}

type TagFilesRequest struct {
	TagID uint `json:"tag_id" validate:"required"`
	Page  int  `json:"page,omitempty"`
	PageSize int `json:"page_size,omitempty"`
}

type TagFilesResponse struct {
	TagID uint                 `json:"tag_id"`
	Files []MediaFileResponse  `json:"files"`
	Total uint                 `json:"total"`
	Page  int                  `json:"page"`
	PageSize int               `json:"page_size"`
}

type AutoTagRequest struct {
	FileIDs         []uint  `json:"file_ids" validate:"required,min=1"`
	MinConfidence   float64 `json:"min_confidence,omitempty"`
	MaxTagsPerFile  uint    `json:"max_tags_per_file,omitempty"`
	EnableAI        bool    `json:"enable_ai,omitempty"`
	TagCategories   []string `json:"tag_categories,omitempty"`
}

type AutoTagResponse struct {
	TotalFiles     uint                 `json:"total_files"`
	ProcessedFiles uint                 `json:"processed_files"`
	FailedFiles    uint                 `json:"failed_files"`
	TotalTags      uint                 `json:"total_tags"`
	CreatedTags    uint                 `json:"created_tags"`
	Results        []AutoTagFileResult  `json:"results"`
	Status         string               `json:"status"`
	Message        string               `json:"message"`
}

type AutoTagFileResult struct {
	FileID      uint                   `json:"file_id"`
	Filename    string                 `json:"filename"`
	Tags        []MediaFileTagResponse `json:"tags"`
	Status      string                 `json:"status"`
	Error       *string                `json:"error,omitempty"`
}

type FileTagAnalyticsRequest struct {
	TenantID    uint       `json:"tenant_id" validate:"required"`
	WebsiteID   uint       `json:"website_id" validate:"required"`
	FileIDs     []uint     `json:"file_ids,omitempty"`
	TagIDs      []uint     `json:"tag_ids,omitempty"`
	TagSources  []TagSource `json:"tag_sources,omitempty"`
	DateFrom    *time.Time `json:"date_from,omitempty"`
	DateTo      *time.Time `json:"date_to,omitempty"`
}

type FileTagAnalyticsResponse struct {
	TotalFileTags       uint                    `json:"total_file_tags"`
	ManualTags          uint                    `json:"manual_tags"`
	AutoTags            uint                    `json:"auto_tags"`
	AITags              uint                    `json:"ai_tags"`
	ImportedTags        uint                    `json:"imported_tags"`
	AverageConfidence   float64                 `json:"average_confidence"`
	TagsBySource        map[string]uint         `json:"tags_by_source"`
	TagsByConfidence    map[string]uint         `json:"tags_by_confidence"`
	MostUsedTags        []MediaTagResponse      `json:"most_used_tags"`
	RecentlyTaggedFiles []MediaFileTagResponse  `json:"recently_tagged_files"`
	TaggingActivity     []TaggingActivityPoint  `json:"tagging_activity"`
}

type TaggingActivityPoint struct {
	Date  time.Time `json:"date"`
	Count uint      `json:"count"`
}

type SimilarFilesRequest struct {
	FileID      uint    `json:"file_id" validate:"required"`
	MinSimilarity float64 `json:"min_similarity,omitempty"`
	Limit       int     `json:"limit,omitempty"`
}

type SimilarFilesResponse struct {
	FileID       uint                   `json:"file_id"`
	SimilarFiles []SimilarFileResult    `json:"similar_files"`
	Total        uint                   `json:"total"`
}

type SimilarFileResult struct {
	File           MediaFileResponse `json:"file"`
	SimilarityScore float64          `json:"similarity_score"`
	CommonTags     []MediaTagResponse `json:"common_tags"`
	CommonTagCount uint              `json:"common_tag_count"`
}