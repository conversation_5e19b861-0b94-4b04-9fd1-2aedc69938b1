package models

import (
	"time"

	"gorm.io/gorm"
)

// MediaThumbnail represents a thumbnail for a media file
type MediaThumbnail struct {
	ID     uint `json:"id" gorm:"primaryKey;autoIncrement"`
	FileID uint `json:"file_id" gorm:"not null;index"`

	// Thumbnail information
	SizeName string `json:"size_name" gorm:"size:50;not null;index"` // small, medium, large, xl, custom
	Width    uint   `json:"width" gorm:"not null"`
	Height   uint   `json:"height" gorm:"not null"`
	Quality  uint   `json:"quality" gorm:"default:85"` // JPEG quality 1-100

	// Storage information
	StorageType StorageType `json:"storage_type" gorm:"type:enum('local','minio','s3','gcs');not null;default:'local'"`
	StoragePath string      `json:"storage_path" gorm:"size:500;not null"`
	PublicURL   string      `json:"public_url" gorm:"size:500;not null"`

	// File details
	FileSize uint64 `json:"file_size" gorm:"not null"`
	FileHash string `json:"file_hash" gorm:"size:64;not null;index"`
	MimeType string `json:"mime_type" gorm:"size:100;not null"`

	// Processing information
	ProcessingMethod  ProcessingMethod           `json:"processing_method" gorm:"type:enum('crop','resize','fit','fill');not null;default:'resize'"`
	ProcessingOptions map[string]interface{}     `json:"processing_options" gorm:"type:json"`

	// Optimization settings
	Format               string  `json:"format" gorm:"size:10;not null"` // jpg, png, webp, avif
	IsOptimized          bool    `json:"is_optimized" gorm:"default:false"`
	OptimizationSavings  float64 `json:"optimization_savings" gorm:"default:0.0"` // percentage saved

	// Status and timestamps
	Status    ThumbnailStatus `json:"status" gorm:"type:enum('generating','ready','error','deleted');not null;default:'generating'"`
	CreatedAt time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	File MediaFile `json:"file,omitempty" gorm:"foreignKey:FileID"`
}

// ProcessingMethod defines how thumbnails are processed
// @Enum crop,resize,fit,fill
type ProcessingMethod string

const (
	ProcessingMethodCrop   ProcessingMethod = "crop"
	ProcessingMethodResize ProcessingMethod = "resize"
	ProcessingMethodFit    ProcessingMethod = "fit"
	ProcessingMethodFill   ProcessingMethod = "fill"
)

// ThumbnailStatus defines the status of thumbnail generation
// @Enum generating,ready,error,deleted
type ThumbnailStatus string

const (
	ThumbnailStatusGenerating ThumbnailStatus = "generating"
	ThumbnailStatusReady      ThumbnailStatus = "ready"
	ThumbnailStatusError      ThumbnailStatus = "error"
	ThumbnailStatusDeleted    ThumbnailStatus = "deleted"
)

// ThumbnailSize defines standard thumbnail sizes
type ThumbnailSize struct {
	Name   string `json:"name"`
	Width  uint   `json:"width"`
	Height uint   `json:"height"`
}

// Standard thumbnail sizes
var StandardThumbnailSizes = []ThumbnailSize{
	{Name: "small", Width: 150, Height: 150},
	{Name: "medium", Width: 300, Height: 300},
	{Name: "large", Width: 600, Height: 600},
	{Name: "xl", Width: 1200, Height: 1200},
}

// TableName returns the table name for MediaThumbnail
func (MediaThumbnail) TableName() string {
	return "media_thumbnails"
}

// BeforeCreate hook for GORM
func (mt *MediaThumbnail) BeforeCreate(tx *gorm.DB) error {
	if mt.ProcessingOptions == nil {
		mt.ProcessingOptions = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (mt *MediaThumbnail) BeforeUpdate(tx *gorm.DB) error {
	if mt.ProcessingOptions == nil {
		mt.ProcessingOptions = make(map[string]interface{})
	}
	return nil
}

// GetAspectRatio returns the aspect ratio of the thumbnail
func (mt *MediaThumbnail) GetAspectRatio() float64 {
	if mt.Height == 0 {
		return 0
	}
	return float64(mt.Width) / float64(mt.Height)
}

// IsSquare returns true if the thumbnail is square
func (mt *MediaThumbnail) IsSquare() bool {
	return mt.Width == mt.Height
}

// IsLandscape returns true if the thumbnail is landscape
func (mt *MediaThumbnail) IsLandscape() bool {
	return mt.Width > mt.Height
}

// IsPortrait returns true if the thumbnail is portrait
func (mt *MediaThumbnail) IsPortrait() bool {
	return mt.Width < mt.Height
}

// Request/Response DTOs
type CreateThumbnailRequest struct {
	FileID            uint                       `json:"file_id" validate:"required"`
	SizeName          string                     `json:"size_name" validate:"required"`
	Width             uint                       `json:"width" validate:"required"`
	Height            uint                       `json:"height" validate:"required"`
	Quality           uint                       `json:"quality,omitempty"`
	ProcessingMethod  ProcessingMethod           `json:"processing_method,omitempty"`
	ProcessingOptions map[string]interface{}     `json:"processing_options,omitempty"`
	Format            string                     `json:"format,omitempty"`
	StorageType       StorageType                `json:"storage_type,omitempty"`
}

type UpdateThumbnailRequest struct {
	Quality           *uint                      `json:"quality,omitempty"`
	ProcessingMethod  *ProcessingMethod          `json:"processing_method,omitempty"`
	ProcessingOptions map[string]interface{}     `json:"processing_options,omitempty"`
	Format            *string                    `json:"format,omitempty"`
	IsOptimized       *bool                      `json:"is_optimized,omitempty"`
}

type MediaThumbnailResponse struct {
	ID                  uint             `json:"id"`
	FileID              uint             `json:"file_id"`
	SizeName            string           `json:"size_name"`
	Width               uint             `json:"width"`
	Height              uint             `json:"height"`
	Quality             uint             `json:"quality"`
	StorageType         StorageType      `json:"storage_type"`
	StoragePath         string           `json:"storage_path"`
	PublicURL           string           `json:"public_url"`
	FileSize            uint64           `json:"file_size"`
	FileHash            string           `json:"file_hash"`
	MimeType            string           `json:"mime_type"`
	ProcessingMethod    ProcessingMethod `json:"processing_method"`
	Format              string           `json:"format"`
	IsOptimized         bool             `json:"is_optimized"`
	OptimizationSavings float64          `json:"optimization_savings"`
	Status              ThumbnailStatus  `json:"status"`
	CreatedAt           time.Time        `json:"created_at"`
	UpdatedAt           time.Time        `json:"updated_at"`
}

type ThumbnailGenerationRequest struct {
	FileID    uint                   `json:"file_id" validate:"required"`
	Sizes     []ThumbnailSizeRequest `json:"sizes" validate:"required,min=1"`
	Format    string                 `json:"format,omitempty"`
	Quality   uint                   `json:"quality,omitempty"`
	Optimize  bool                   `json:"optimize,omitempty"`
}

type ThumbnailSizeRequest struct {
	Name             string           `json:"name" validate:"required"`
	Width            uint             `json:"width" validate:"required"`
	Height           uint             `json:"height" validate:"required"`
	ProcessingMethod ProcessingMethod `json:"processing_method,omitempty"`
}

type ThumbnailGenerationResponse struct {
	FileID     uint                     `json:"file_id"`
	Thumbnails []MediaThumbnailResponse `json:"thumbnails"`
	Status     string                   `json:"status"`
	Message    string                   `json:"message"`
}

type ThumbnailBatchRequest struct {
	FileIDs []uint                 `json:"file_ids" validate:"required,min=1"`
	Sizes   []ThumbnailSizeRequest `json:"sizes" validate:"required,min=1"`
	Format  string                 `json:"format,omitempty"`
	Quality uint                   `json:"quality,omitempty"`
	Optimize bool                  `json:"optimize,omitempty"`
}

type ThumbnailBatchResponse struct {
	TotalFiles     uint                          `json:"total_files"`
	ProcessedFiles uint                          `json:"processed_files"`
	FailedFiles    uint                          `json:"failed_files"`
	Results        []ThumbnailGenerationResponse `json:"results"`
	Status         string                        `json:"status"`
	Message        string                        `json:"message"`
}

type ThumbnailStats struct {
	TotalThumbnails        uint    `json:"total_thumbnails"`
	TotalSize              uint64  `json:"total_size"`
	OptimizedThumbnails    uint    `json:"optimized_thumbnails"`
	AverageOptimization    float64 `json:"average_optimization"`
	TotalSavings           uint64  `json:"total_savings"`
	ThumbnailsBySize       map[string]uint `json:"thumbnails_by_size"`
	ThumbnailsByFormat     map[string]uint `json:"thumbnails_by_format"`
	ThumbnailsByStatus     map[string]uint `json:"thumbnails_by_status"`
}