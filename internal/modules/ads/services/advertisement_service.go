package services

import (
	"context"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
)

// advertisementService implements the AdvertisementService interface
type advertisementService struct {
	repoManager repositories.RepositoryManager
}

// NewAdvertisementService creates a new advertisement service instance
func NewAdvertisementService(repoManager repositories.RepositoryManager) AdvertisementService {
	return &advertisementService{
		repoManager: repoManager,
	}
}

// CreateAdvertisement creates a new advertisement
func (s *advertisementService) CreateAdvertisement(ctx context.Context, tenantID uint, req *models.AdvertisementCreateRequest) (*models.Advertisement, error) {
	// Validate request
	if err := s.validateAdvertisementCreateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid advertisement request: %w", err)
	}

	// Check if campaign exists and belongs to tenant
	campaign, err := s.repoManager.Campaign().GetByID(ctx, tenantID, req.CampaignID)
	if err != nil {
		return nil, fmt.Errorf("campaign not found: %w", err)
	}

	// Check if campaign is active or scheduled
	if campaign.Status != "active" && campaign.Status != "scheduled" && campaign.Status != "draft" {
		return nil, fmt.Errorf("cannot create advertisement for campaign with status: %s", campaign.Status)
	}

	advertisement := &models.Advertisement{
		TenantID:        tenantID,
		CampaignID:      req.CampaignID,
		Title:           req.Title,
		Description:     req.Description,
		ImageURL:        req.ImageURL,
		LinkURL:         req.LinkURL,
		AdType:          req.AdType,
		Position:        req.Position,
		Priority:        req.Priority,
		DeviceTargeting: req.DeviceTargeting,
		Status:          "draft", // Default to draft status
	}

	// Create advertisement in database
	err = s.repoManager.Advertisement().Create(ctx, advertisement)
	if err != nil {
		return nil, fmt.Errorf("failed to create advertisement: %w", err)
	}

	return advertisement, nil
}

// GetAdvertisementByID retrieves an advertisement by ID
func (s *advertisementService) GetAdvertisementByID(ctx context.Context, tenantID uint, adID uint) (*models.Advertisement, error) {
	advertisement, err := s.repoManager.Advertisement().GetByID(ctx, tenantID, adID)
	if err != nil {
		return nil, fmt.Errorf("failed to get advertisement: %w", err)
	}

	return advertisement, nil
}

// UpdateAdvertisement updates an existing advertisement
func (s *advertisementService) UpdateAdvertisement(ctx context.Context, tenantID uint, adID uint, req *models.AdvertisementUpdateRequest) (*models.Advertisement, error) {
	// Get existing advertisement
	existingAd, err := s.GetAdvertisementByID(ctx, tenantID, adID)
	if err != nil {
		return nil, err
	}

	// Apply updates
	if req.Title != nil {
		existingAd.Title = *req.Title
	}
	if req.Description != nil {
		existingAd.Description = *req.Description
	}
	if req.ImageURL != nil {
		existingAd.ImageURL = *req.ImageURL
	}
	if req.LinkURL != nil {
		existingAd.LinkURL = *req.LinkURL
	}
	if req.AdType != nil {
		existingAd.AdType = *req.AdType
	}
	if req.Position != nil {
		existingAd.Position = *req.Position
	}
	if req.Priority != nil {
		existingAd.Priority = *req.Priority
	}
	if req.DeviceTargeting != nil {
		existingAd.DeviceTargeting = *req.DeviceTargeting
	}
	if req.Status != nil {
		existingAd.Status = *req.Status
	}

	// Update in database
	err = s.repoManager.Advertisement().Update(ctx, existingAd)
	if err != nil {
		return nil, fmt.Errorf("failed to update advertisement: %w", err)
	}

	return existingAd, nil
}

// DeleteAdvertisement deletes an advertisement (soft delete)
func (s *advertisementService) DeleteAdvertisement(ctx context.Context, tenantID uint, adID uint) error {
	// Check if advertisement exists
	_, err := s.GetAdvertisementByID(ctx, tenantID, adID)
	if err != nil {
		return err
	}

	// Soft delete advertisement
	err = s.repoManager.Advertisement().Delete(ctx, tenantID, adID)
	if err != nil {
		return fmt.Errorf("failed to delete advertisement: %w", err)
	}

	return nil
}

// ListAdvertisements lists advertisements with filters
func (s *advertisementService) ListAdvertisements(ctx context.Context, tenantID uint, filters *AdvertisementFilters) ([]*models.Advertisement, int64, error) {
	if filters == nil {
		filters = &AdvertisementFilters{}
	}

	// Set default pagination
	if filters.Limit <= 0 {
		filters.Limit = 20
	}
	if filters.Limit > 100 {
		filters.Limit = 100
	}

	// Convert filters to map
	filterMap := make(map[string]interface{})
	if filters.CampaignID != nil {
		filterMap["campaign_id"] = *filters.CampaignID
	}
	if filters.Status != nil {
		filterMap["status"] = *filters.Status
	}
	if filters.AdType != nil {
		filterMap["ad_type"] = *filters.AdType
	}
	if filters.Position != nil {
		filterMap["position"] = *filters.Position
	}
	if filters.DeviceTargeting != nil {
		filterMap["device_targeting"] = *filters.DeviceTargeting
	}

	advertisements, err := s.repoManager.Advertisement().GetByTenantID(ctx, tenantID, filters.Limit, filters.Offset, filterMap)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list advertisements: %w", err)
	}

	// Get total count
	total, err := s.repoManager.Advertisement().Count(ctx, tenantID, filterMap)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count advertisements: %w", err)
	}

	return advertisements, total, nil
}

// ListAdvertisementsByCampaign lists advertisements for a specific campaign
func (s *advertisementService) ListAdvertisementsByCampaign(ctx context.Context, tenantID uint, campaignID uint) ([]*models.Advertisement, error) {
	// Check if campaign exists
	_, err := s.repoManager.Campaign().GetByID(ctx, tenantID, campaignID)
	if err != nil {
		return nil, fmt.Errorf("campaign not found: %w", err)
	}

	advertisements, err := s.repoManager.Advertisement().GetByCampaignID(ctx, tenantID, campaignID, 100, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to list advertisements for campaign: %w", err)
	}

	return advertisements, nil
}

// GetAdvertisementsForPlacement retrieves advertisements suitable for a placement
func (s *advertisementService) GetAdvertisementsForPlacement(ctx context.Context, tenantID uint, placement *models.Placement, context *models.TargetingContext) ([]*models.Advertisement, error) {
	// Get all active advertisements
	advertisements, err := s.repoManager.Advertisement().GetActiveAds(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active advertisements: %w", err)
	}

	var suitableAds []*models.Advertisement
	for _, ad := range advertisements {
		// Check if ad matches placement position
		if string(ad.Position) != placement.Position {
			continue
		}

		// Check if ad passes targeting validation
		isValidTarget, err := s.ValidateTargeting(ctx, ad, context)
		if err != nil {
			// Log error but don't fail entire operation
			continue
		}

		if isValidTarget {
			suitableAds = append(suitableAds, ad)
		}
	}

	// Sort by priority (higher priority first)
	for i := 0; i < len(suitableAds); i++ {
		for j := i + 1; j < len(suitableAds); j++ {
			if suitableAds[i].Priority < suitableAds[j].Priority {
				suitableAds[i], suitableAds[j] = suitableAds[j], suitableAds[i]
			}
		}
	}

	// Limit to placement's max ads
	if len(suitableAds) > placement.MaxAds {
		suitableAds = suitableAds[:placement.MaxAds]
	}

	return suitableAds, nil
}

// ValidateTargeting validates if an advertisement matches targeting context
func (s *advertisementService) ValidateTargeting(ctx context.Context, ad *models.Advertisement, context *models.TargetingContext) (bool, error) {
	// Check device targeting
	if ad.DeviceTargeting != "" && string(ad.DeviceTargeting) != context.DeviceType {
		return false, nil
	}

	// Get targeting rules for this advertisement
	rules, err := s.repoManager.TargetingRule().GetByAdvertisementID(ctx, ad.TenantID, ad.ID)
	if err != nil {
		return false, fmt.Errorf("failed to get targeting rules: %w", err)
	}

	// If no rules, consider it valid
	if len(rules) == 0 {
		return true, nil
	}

	// All rules must pass for targeting to be valid
	for _, rule := range rules {
		if !s.evaluateTargetingRule(rule, context) {
			return false, nil
		}
	}

	return true, nil
}

// ActivateAdvertisement activates an advertisement
func (s *advertisementService) ActivateAdvertisement(ctx context.Context, tenantID uint, adID uint) error {
	advertisement, err := s.GetAdvertisementByID(ctx, tenantID, adID)
	if err != nil {
		return err
	}

	if advertisement.Status == "active" {
		return fmt.Errorf("advertisement is already active")
	}

	// Check if campaign is active
	campaign, err := s.repoManager.Campaign().GetByID(ctx, tenantID, advertisement.CampaignID)
	if err != nil {
		return fmt.Errorf("failed to get campaign: %w", err)
	}

	if campaign.Status != "active" {
		return fmt.Errorf("cannot activate advertisement for inactive campaign")
	}

	// Update status
	advertisement.Status = "active"
	err = s.repoManager.Advertisement().Update(ctx, advertisement)
	if err != nil {
		return fmt.Errorf("failed to activate advertisement: %w", err)
	}

	return nil
}

// PauseAdvertisement pauses an advertisement
func (s *advertisementService) PauseAdvertisement(ctx context.Context, tenantID uint, adID uint) error {
	advertisement, err := s.GetAdvertisementByID(ctx, tenantID, adID)
	if err != nil {
		return err
	}

	if advertisement.Status != "active" {
		return fmt.Errorf("can only pause active advertisements")
	}

	// Update status
	advertisement.Status = "paused"
	err = s.repoManager.Advertisement().Update(ctx, advertisement)
	if err != nil {
		return fmt.Errorf("failed to pause advertisement: %w", err)
	}

	return nil
}

// validateAdvertisementCreateRequest validates the advertisement creation request
func (s *advertisementService) validateAdvertisementCreateRequest(req *models.AdvertisementCreateRequest) error {
	if req.Title == "" {
		return fmt.Errorf("advertisement title is required")
	}

	if len(req.Title) > 255 {
		return fmt.Errorf("advertisement title cannot exceed 255 characters")
	}

	if req.CampaignID == 0 {
		return fmt.Errorf("campaign ID is required")
	}

	if req.AdType == "" {
		return fmt.Errorf("advertisement type is required")
	}

	if req.Description == "" {
		return fmt.Errorf("advertisement description is required")
	}

	if req.Position == "" {
		return fmt.Errorf("advertisement position is required")
	}

	if req.Priority < 1 || req.Priority > 10 {
		return fmt.Errorf("advertisement priority must be between 1 and 10")
	}

	return nil
}

// applyAdvertisementFilters applies additional filtering to advertisements
func (s *advertisementService) applyAdvertisementFilters(advertisements []*models.Advertisement, filters *AdvertisementFilters) []*models.Advertisement {
	var filtered []*models.Advertisement

	for _, ad := range advertisements {
		// Campaign ID filter
		if filters.CampaignID != nil && ad.CampaignID != *filters.CampaignID {
			continue
		}

		// Status filter
		if filters.Status != nil && string(ad.Status) != *filters.Status {
			continue
		}

		// Ad type filter
		if filters.AdType != nil && string(ad.AdType) != *filters.AdType {
			continue
		}

		// Position filter
		if filters.Position != nil && string(ad.Position) != *filters.Position {
			continue
		}

		// Device targeting filter
		if filters.DeviceTargeting != nil && string(ad.DeviceTargeting) != *filters.DeviceTargeting {
			continue
		}

		// Priority filters
		if filters.MinPriority != nil && ad.Priority < *filters.MinPriority {
			continue
		}
		if filters.MaxPriority != nil && ad.Priority > *filters.MaxPriority {
			continue
		}

		filtered = append(filtered, ad)
	}

	return filtered
}

// evaluateTargetingRule evaluates a single targeting rule against context
func (s *advertisementService) evaluateTargetingRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	switch rule.RuleType {
	case "location":
		return s.evaluateLocationRule(rule, context)
	case "time":
		return s.evaluateTimeRule(rule, context)
	case "device":
		return s.evaluateDeviceRule(rule, context)
	case "user_agent":
		return s.evaluateUserAgentRule(rule, context)
	case "referrer":
		return s.evaluateReferrerRule(rule, context)
	default:
		// Unknown rule type, consider it invalid
		return false
	}
}

// evaluateLocationRule evaluates location-based targeting
func (s *advertisementService) evaluateLocationRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	if context.IPAddress == "" {
		return false
	}

	// Simple string matching for now
	// In production, this would use more sophisticated geolocation matching
	return context.IPAddress == rule.RuleValue
}

// evaluateTimeRule evaluates time-based targeting
func (s *advertisementService) evaluateTimeRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	// Parse target value as hour range (e.g., "9-17" for 9 AM to 5 PM)
	// This is a simplified implementation
	return true // For now, always return true
}

// evaluateDeviceRule evaluates device-based targeting
func (s *advertisementService) evaluateDeviceRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	if context.DeviceType == "" {
		return false
	}

	return context.DeviceType == rule.RuleValue
}

// evaluateUserAgentRule evaluates user agent-based targeting
func (s *advertisementService) evaluateUserAgentRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	if context.UserAgent == "" {
		return false
	}

	// Simple substring matching
	return len(context.UserAgent) > 0 && rule.RuleValue != ""
}

// evaluateReferrerRule evaluates referrer-based targeting
func (s *advertisementService) evaluateReferrerRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	if context.Referrer == "" {
		return false
	}

	// Simple substring matching
	return len(context.Referrer) > 0 && rule.RuleValue != ""
}