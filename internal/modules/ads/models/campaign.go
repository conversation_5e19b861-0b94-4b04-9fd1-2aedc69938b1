package models

import (
	"database/sql/driver"
	"time"

	"gorm.io/gorm"
)

// CampaignStatus represents the status of an advertising campaign
// @Enum draft,active,paused,completed,cancelled,deleted
type CampaignStatus string

const (
	CampaignStatusDraft     CampaignStatus = "draft"
	CampaignStatusActive    CampaignStatus = "active"
	CampaignStatusPaused    CampaignStatus = "paused"
	CampaignStatusCompleted CampaignStatus = "completed"
	CampaignStatusCancelled CampaignStatus = "cancelled"
	CampaignStatusDeleted   CampaignStatus = "deleted"
)

// Scan implements sql.Scanner interface
func (s *CampaignStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = CampaignStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s CampaignStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// Campaign represents an advertising campaign
type Campaign struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	TenantID    uint      `json:"tenant_id" gorm:"not null;index"`
	Name        string    `json:"name" gorm:"not null;size:255" validate:"required,min=1,max=255"`
	Description string    `json:"description,omitempty" gorm:"type:text"`
	Budget      float64   `json:"budget" gorm:"type:decimal(10,2);default:0.00" validate:"min=0"`
	Status      CampaignStatus `json:"status" gorm:"type:enum('draft','active','paused','completed','cancelled','deleted');default:'draft'" validate:"oneof=draft active paused completed cancelled deleted"`
	StartDate   time.Time `json:"start_date" gorm:"not null" validate:"required"`
	EndDate     time.Time `json:"end_date" gorm:"not null" validate:"required"`
	CreatedAt   time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`

	// Relationships
	Advertisements []Advertisement `json:"advertisements,omitempty" gorm:"foreignKey:CampaignID"`
}

// TableName returns the table name for the Campaign model
func (Campaign) TableName() string {
	return "ads_campaigns"
}

// BeforeCreate is a GORM hook that runs before creating a campaign
func (c *Campaign) BeforeCreate(tx *gorm.DB) error {
	if c.Status == "" {
		c.Status = CampaignStatusDraft
	}
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a campaign
func (c *Campaign) BeforeUpdate(tx *gorm.DB) error {
	// Validate date range
	if c.EndDate.Before(c.StartDate) {
		return gorm.ErrInvalidData
	}
	return nil
}

// IsActive returns true if the campaign is active
func (c *Campaign) IsActive() bool {
	return c.Status == CampaignStatusActive
}

// IsRunning returns true if the campaign is currently running
func (c *Campaign) IsRunning() bool {
	now := time.Now()
	return c.IsActive() && c.StartDate.Before(now) && c.EndDate.After(now)
}

// CampaignCreateRequest represents the request to create a campaign
type CampaignCreateRequest struct {
	Name        string    `json:"name" validate:"required,min=1,max=255"`
	Description string    `json:"description,omitempty" validate:"max=1000"`
	Budget      float64   `json:"budget" validate:"min=0"`
	StartDate   time.Time `json:"start_date" validate:"required"`
	EndDate     time.Time `json:"end_date" validate:"required"`
}

// CampaignUpdateRequest represents the request to update a campaign
type CampaignUpdateRequest struct {
	Name        *string    `json:"name,omitempty" validate:"omitempty,min=1,max=255"`
	Description *string    `json:"description,omitempty" validate:"omitempty,max=1000"`
	Budget      *float64   `json:"budget,omitempty" validate:"omitempty,min=0"`
	Status      *CampaignStatus `json:"status,omitempty" validate:"omitempty,oneof=draft active paused completed cancelled deleted"`
	StartDate   *time.Time `json:"start_date,omitempty"`
	EndDate     *time.Time `json:"end_date,omitempty"`
}

// CampaignResponse represents the response structure for campaign
type CampaignResponse struct {
	ID          uint      `json:"id"`
	TenantID    uint      `json:"tenant_id"`
	Name        string    `json:"name"`
	Description string    `json:"description,omitempty"`
	Budget      float64   `json:"budget"`
	Status      CampaignStatus `json:"status"`
	StartDate   time.Time `json:"start_date"`
	EndDate     time.Time `json:"end_date"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	IsActive    bool      `json:"is_active"`
	IsRunning   bool      `json:"is_running"`
}

// ToCampaignResponse converts Campaign to CampaignResponse
func (c *Campaign) ToCampaignResponse() *CampaignResponse {
	return &CampaignResponse{
		ID:          c.ID,
		TenantID:    c.TenantID,
		Name:        c.Name,
		Description: c.Description,
		Budget:      c.Budget,
		Status:      c.Status,
		StartDate:   c.StartDate,
		EndDate:     c.EndDate,
		CreatedAt:   c.CreatedAt,
		UpdatedAt:   c.UpdatedAt,
		IsActive:    c.IsActive(),
		IsRunning:   c.IsRunning(),
	}
}