package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

// AdvertisementHandler handles advertisement-related HTTP requests
type AdvertisementHandler struct {
	serviceManager services.ServiceManager
}

// NewAdvertisementHandler creates a new advertisement handler
func NewAdvertisementHandler(serviceManager services.ServiceManager) *AdvertisementHandler {
	return &AdvertisementHandler{
		serviceManager: serviceManager,
	}
}

// CreateAdvertisement handles POST /advertisements
func (h *AdvertisementHandler) CreateAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.<PERSON>, "Tenant not found in context")
		return
	}

	var req models.AdvertisementCreateRequest
	if err := c.ShouldBind<PERSON>N(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Create advertisement using service
	advertisement, err := h.serviceManager.AdvertisementService().CreateAdvertisement(c.Request.Context(), tenantID.(uint), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to create advertisement: "+err.Error())
		return
	}

	response.Created(c.Writer, advertisement)
}

// GetAdvertisement handles GET /advertisements/:id
func (h *AdvertisementHandler) GetAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Get advertisement using service
	advertisement, err := h.serviceManager.AdvertisementService().GetAdvertisementByID(c.Request.Context(), tenantID.(uint), uint(adID))
	if err != nil {
		response.NotFound(c.Writer, "Advertisement not found: "+err.Error())
		return
	}

	response.Success(c.Writer, advertisement)
}

// UpdateAdvertisement handles PUT /advertisements/:id
func (h *AdvertisementHandler) UpdateAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	var req models.AdvertisementUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Update advertisement using service
	advertisement, err := h.serviceManager.AdvertisementService().UpdateAdvertisement(c.Request.Context(), tenantID.(uint), uint(adID), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to update advertisement: "+err.Error())
		return
	}

	response.Success(c.Writer, advertisement)
}

// DeleteAdvertisement handles DELETE /advertisements/:id
func (h *AdvertisementHandler) DeleteAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Delete advertisement using service
	err = h.serviceManager.AdvertisementService().DeleteAdvertisement(c.Request.Context(), tenantID.(uint), uint(adID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to delete advertisement: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Advertisement deleted successfully"})
}

// ListAdvertisements handles GET /advertisements
func (h *AdvertisementHandler) ListAdvertisements(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse query parameters
	filters := &services.AdvertisementFilters{}
	
	// Parse limit
	if limitStr := c.Query("limit"); limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid limit parameter")
			return
		}
		filters.Limit = limit
	}

	// Parse offset
	if offsetStr := c.Query("offset"); offsetStr != "" {
		offset, err := strconv.Atoi(offsetStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid offset parameter")
			return
		}
		filters.Offset = offset
	}

	// Parse campaign ID filter
	if campaignIDStr := c.Query("campaign_id"); campaignIDStr != "" {
		campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid campaign_id parameter")
			return
		}
		campaignIDUint := uint(campaignID)
		filters.CampaignID = &campaignIDUint
	}

	// Parse status filter
	if status := c.Query("status"); status != "" {
		filters.Status = &status
	}

	// Parse ad type filter
	if adType := c.Query("ad_type"); adType != "" {
		filters.AdType = &adType
	}

	// Parse position filter
	if position := c.Query("position"); position != "" {
		filters.Position = &position
	}

	// Parse device targeting filter
	if deviceTargeting := c.Query("device_targeting"); deviceTargeting != "" {
		filters.DeviceTargeting = &deviceTargeting
	}

	// Parse priority filters
	if minPriorityStr := c.Query("min_priority"); minPriorityStr != "" {
		minPriority, err := strconv.Atoi(minPriorityStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid min_priority parameter")
			return
		}
		filters.MinPriority = &minPriority
	}

	if maxPriorityStr := c.Query("max_priority"); maxPriorityStr != "" {
		maxPriority, err := strconv.Atoi(maxPriorityStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid max_priority parameter")
			return
		}
		filters.MaxPriority = &maxPriority
	}

	// Get advertisements using service
	advertisements, total, err := h.serviceManager.AdvertisementService().ListAdvertisements(c.Request.Context(), tenantID.(uint), filters)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list advertisements: "+err.Error())
		return
	}

	// Prepare response data
	responseData := gin.H{
		"advertisements": advertisements,
		"total":          total,
		"limit":          filters.Limit,
		"offset":         filters.Offset,
	}

	response.Success(c.Writer, responseData)
}

// ListAdvertisementsByCampaign handles GET /campaigns/:id/advertisements
func (h *AdvertisementHandler) ListAdvertisementsByCampaign(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse campaign ID from URL parameter
	campaignIDStr := c.Param("id")
	campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid campaign ID")
		return
	}

	// Get advertisements for campaign using service
	advertisements, err := h.serviceManager.AdvertisementService().ListAdvertisementsByCampaign(c.Request.Context(), tenantID.(uint), uint(campaignID))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list advertisements for campaign: "+err.Error())
		return
	}

	response.Success(c.Writer, advertisements)
}

// ActivateAdvertisement handles POST /advertisements/:id/activate
func (h *AdvertisementHandler) ActivateAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Activate advertisement using service
	err = h.serviceManager.AdvertisementService().ActivateAdvertisement(c.Request.Context(), tenantID.(uint), uint(adID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to activate advertisement: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Advertisement activated successfully"})
}

// PauseAdvertisement handles POST /advertisements/:id/pause
func (h *AdvertisementHandler) PauseAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Pause advertisement using service
	err = h.serviceManager.AdvertisementService().PauseAdvertisement(c.Request.Context(), tenantID.(uint), uint(adID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to pause advertisement: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Advertisement paused successfully"})
}

// ServeAd handles GET /serve - public endpoint for serving ads
func (h *AdvertisementHandler) ServeAd(c *gin.Context) {
	// This endpoint might not require tenant authentication as it's for public ad serving
	// Parse query parameters for targeting
	var context models.TargetingContext
	
	// Get targeting parameters
	context.DeviceType = c.Query("device_type")
	context.Location = c.Query("location")
	context.UserAgent = c.GetHeader("User-Agent")
	context.Referrer = c.GetHeader("Referer")
	context.IPAddress = c.ClientIP()
	context.PageType = c.Query("page_type")
	context.PageURL = c.Query("page_url")
	context.Language = c.Query("language")

	// Parse placement ID (required for ad serving)
	placementIDStr := c.Query("placement_id")
	if placementIDStr == "" {
		response.BadRequest(c.Writer, "Placement ID is required")
		return
	}

	placementID, err := strconv.ParseUint(placementIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid placement ID")
		return
	}

	// Parse tenant ID (required for ad serving)
	tenantIDStr := c.Query("tenant_id")
	if tenantIDStr == "" {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	// Get placement info
	placement, err := h.serviceManager.PlacementService().GetPlacementByID(c.Request.Context(), uint(tenantID), uint(placementID))
	if err != nil {
		response.NotFound(c.Writer, "Placement not found: "+err.Error())
		return
	}

	// Get suitable advertisements for placement
	advertisements, err := h.serviceManager.AdvertisementService().GetAdvertisementsForPlacement(c.Request.Context(), uint(tenantID), placement, &context)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get advertisements: "+err.Error())
		return
	}

	// Return advertisements (or empty if no matches)
	responseData := gin.H{
		"placement_id":    placementID,
		"advertisements": advertisements,
		"context":        context,
	}

	response.Success(c.Writer, responseData)
}