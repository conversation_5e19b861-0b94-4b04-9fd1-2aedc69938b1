package handlers

import (

	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

// TargetingHandler handles targeting rule related HTTP requests
type TargetingHandler struct {
	serviceManager services.ServiceManager
}

// NewTargetingHandler creates a new targeting handler
func NewTargetingHandler(serviceManager services.ServiceManager) *TargetingHandler {
	return &TargetingHandler{
		serviceManager: serviceManager,
	}
}

// CreateTargetingRule handles POST /targeting-rules
func (h *TargetingHandler) CreateTargetingRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	var req models.TargetingRuleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Create targeting rule using service
	targetingRule, err := h.serviceManager.TargetingService().CreateTargetingRule(c.Request.Context(), tenantID.(uint), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to create targeting rule: "+err.Error())
		return
	}

	response.Created(c.Writer, targetingRule)
}

// GetTargetingRule handles GET /targeting-rules/:id
func (h *TargetingHandler) GetTargetingRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse targeting rule ID from URL parameter
	ruleIDStr := c.Param("id")
	ruleID, err := strconv.ParseUint(ruleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid targeting rule ID")
		return
	}

	// Get targeting rule using service
	targetingRule, err := h.serviceManager.TargetingService().GetTargetingRuleByID(c.Request.Context(), tenantID.(uint), uint(ruleID))
	if err != nil {
		response.NotFound(c.Writer, "Targeting rule not found: "+err.Error())
		return
	}

	response.Success(c.Writer, targetingRule)
}

// UpdateTargetingRule handles PUT /targeting-rules/:id
func (h *TargetingHandler) UpdateTargetingRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse targeting rule ID from URL parameter
	ruleIDStr := c.Param("id")
	ruleID, err := strconv.ParseUint(ruleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid targeting rule ID")
		return
	}

	var req models.TargetingRuleUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Update targeting rule using service
	targetingRule, err := h.serviceManager.TargetingService().UpdateTargetingRule(c.Request.Context(), tenantID.(uint), uint(ruleID), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to update targeting rule: "+err.Error())
		return
	}

	response.Success(c.Writer, targetingRule)
}

// DeleteTargetingRule handles DELETE /targeting-rules/:id
func (h *TargetingHandler) DeleteTargetingRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse targeting rule ID from URL parameter
	ruleIDStr := c.Param("id")
	ruleID, err := strconv.ParseUint(ruleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid targeting rule ID")
		return
	}

	// Delete targeting rule using service
	err = h.serviceManager.TargetingService().DeleteTargetingRule(c.Request.Context(), tenantID.(uint), uint(ruleID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to delete targeting rule: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Operation successful"})
}

// ListTargetingRules handles GET /targeting-rules
func (h *TargetingHandler) ListTargetingRules(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse query parameters
	filters := &services.TargetingRuleFilters{}
	
	// Parse limit
	if limitStr := c.Query("limit"); limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid limit parameter")
			return
		}
		filters.Limit = limit
	}

	// Parse offset
	if offsetStr := c.Query("offset"); offsetStr != "" {
		offset, err := strconv.Atoi(offsetStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid offset parameter")
			return
		}
		filters.Offset = offset
	}

	// Parse advertisement ID filter
	if adIDStr := c.Query("advertisement_id"); adIDStr != "" {
		adID, err := strconv.ParseUint(adIDStr, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid advertisement_id parameter")
			return
		}
		adIDUint := uint(adID)
		filters.AdvertisementID = &adIDUint
	}

	// Parse rule type filter
	if ruleType := c.Query("rule_type"); ruleType != "" {
		filters.RuleType = &ruleType
	}

	// Parse status filter
	if status := c.Query("status"); status != "" {
		filters.Status = &status
	}

	// Parse priority filters
	if minPriorityStr := c.Query("min_priority"); minPriorityStr != "" {
		minPriority, err := strconv.Atoi(minPriorityStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid min_priority parameter")
			return
		}
		filters.MinPriority = &minPriority
	}

	if maxPriorityStr := c.Query("max_priority"); maxPriorityStr != "" {
		maxPriority, err := strconv.Atoi(maxPriorityStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid max_priority parameter")
			return
		}
		filters.MaxPriority = &maxPriority
	}

	// Get targeting rules using service
	targetingRules, total, err := h.serviceManager.TargetingService().ListTargetingRules(c.Request.Context(), tenantID.(uint), filters)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list targeting rules: "+err.Error())
		return
	}

	// Prepare response data
	responseData := gin.H{
		"targeting_rules": targetingRules,
		"total":           total,
		"limit":           filters.Limit,
		"offset":          filters.Offset,
	}

	response.Success(c.Writer, responseData)
}

// GetRulesByAdvertisement handles GET /advertisements/:id/targeting-rules
func (h *TargetingHandler) GetRulesByAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Get targeting rules for advertisement using service
	targetingRules, err := h.serviceManager.TargetingService().GetRulesByAdvertisement(c.Request.Context(), tenantID.(uint), uint(adID))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get targeting rules for advertisement: "+err.Error())
		return
	}

	responseData := gin.H{
		"advertisement_id": adID,
		"targeting_rules":  targetingRules,
	}

	response.Success(c.Writer, responseData)
}

// EvaluateRules handles POST /targeting-rules/evaluate
func (h *TargetingHandler) EvaluateRules(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse request body
	var req struct {
		AdvertisementID uint                     `json:"advertisement_id" binding:"required"`
		Context         models.TargetingContext `json:"context" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Get targeting rules for advertisement
	targetingRules, err := h.serviceManager.TargetingService().GetRulesByAdvertisement(c.Request.Context(), tenantID.(uint), req.AdvertisementID)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get targeting rules: "+err.Error())
		return
	}

	// Evaluate rules using service
	result, err := h.serviceManager.TargetingService().EvaluateRules(c.Request.Context(), targetingRules, &req.Context)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to evaluate targeting rules: "+err.Error())
		return
	}

	responseData := gin.H{
		"advertisement_id": req.AdvertisementID,
		"context":          req.Context,
		"rules_matched":    result,
		"rules_count":      len(targetingRules),
	}

	response.Success(c.Writer, responseData)
}

// ValidateRule handles POST /targeting-rules/validate
func (h *TargetingHandler) ValidateRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	var req models.TargetingRuleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Create temporary targeting rule for validation
	tempRule := &models.TargetingRule{
		TenantID:        tenantID.(uint),
		AdvertisementID: req.AdvertisementID,
		RuleType:        req.RuleType,
		RuleKey:         req.RuleKey,
		RuleValue:       req.RuleValue,
		Operator:        req.Operator,
		Priority:        req.Priority,
		Status:          "active",
	}

	// Validate targeting rule using service
	err := h.serviceManager.TargetingService().ValidateRule(c.Request.Context(), tempRule)
	if err != nil {
		response.BadRequest(c.Writer, "Targeting rule validation failed: "+err.Error())
		return
	}

	responseData := gin.H{
		"valid": true,
		"rule":  tempRule,
	}

	response.Success(c.Writer, responseData)
}