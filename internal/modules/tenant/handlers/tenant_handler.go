package handlers

import (
	"strconv"
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

type TenantHandler struct {
	tenantService services.TenantService
	validator     validator.Validator
}

func NewTenantHandler(tenantService services.TenantService, validator validator.Validator) *TenantHandler {
	return &TenantHandler{
		tenantService: tenantService,
		validator:     validator,
	}
}

// CreateTenant handles POST /api/v1/tenants
// @Summary      Create a new tenant
// @Description  Create a new tenant with the provided information
// @Tags         Tenants
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        request body dto.TenantCreateRequest true "Tenant creation data"
// @Success      201 {object} response.Response{data=dto.TenantResponse} "Tenant created successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to create tenant"
// @Router       /api/cms/v1/tenants [post]
func (h *TenantHandler) CreateTenant(c *gin.Context) {
	var req dto.TenantCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert DTO to service input
	serviceInput := h.convertToCreateTenantInput(req)

	tenant, err := h.tenantService.Create(c.Request.Context(), serviceInput)
	if err != nil {
		response.InternalError(c.Writer, "Failed to create tenant", err.Error())
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToTenantResponse(tenant)
	response.Created(c.Writer, responseDTO)
}

// GetTenant handles GET /api/v1/tenants/{id}
// @Summary      Get tenant by ID
// @Description  Get detailed information about a specific tenant
// @Tags         Tenants
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Tenant ID"
// @Success      200 {object} response.Response{data=dto.TenantResponse} "Tenant retrieved successfully"
// @Failure      400 {object} response.Response "Invalid tenant ID"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Tenant not found"
// @Failure      500 {object} response.Response "Failed to get tenant"
// @Router       /api/cms/v1/tenants/{id} [get]
func (h *TenantHandler) GetTenant(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	tenant, err := h.tenantService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Tenant not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get tenant", err.Error())
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToTenantResponse(tenant)
	response.Success(c.Writer, responseDTO)
}

// GetTenantByDomain handles GET /api/v1/tenants/domain/{domain}
// @Summary      Get tenant by domain
// @Description  Get tenant information by domain name
// @Tags         Tenants
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        domain path string true "Domain name"
// @Success      200 {object} response.Response{data=dto.TenantResponse} "Tenant retrieved successfully"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Tenant not found"
// @Failure      500 {object} response.Response "Failed to get tenant"
// @Router       /api/cms/v1/tenants/domain/{domain} [get]
func (h *TenantHandler) GetTenantByDomain(c *gin.Context) {
	domain := c.Param("domain")

	tenant, err := h.tenantService.GetByDomain(c.Request.Context(), domain)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Tenant not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get tenant", err.Error())
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToTenantResponse(tenant)
	response.Success(c.Writer, responseDTO)
}

// UpdateTenant handles PUT /api/v1/tenants/{id}
// @Summary      Update a tenant
// @Description  Update tenant information
// @Tags         Tenants
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Tenant ID"
// @Param        request body dto.TenantUpdateRequest true "Tenant update data"
// @Success      200 {object} response.Response{data=dto.TenantResponse} "Tenant updated successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Tenant not found"
// @Failure      500 {object} response.Response "Failed to update tenant"
// @Router       /api/cms/v1/tenants/{id} [put]
func (h *TenantHandler) UpdateTenant(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	var req dto.TenantUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert DTO to service input
	serviceInput := h.convertToUpdateTenantInput(req)

	tenant, err := h.tenantService.Update(c.Request.Context(), uint(id), serviceInput)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Tenant not found")
			return
		}
		response.InternalError(c.Writer, "Failed to update tenant", err.Error())
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToTenantResponse(tenant)
	response.Success(c.Writer, responseDTO)
}

// UpdateTenantStatus handles PATCH /api/v1/tenants/{id}/status
// @Summary      Update tenant status
// @Description  Update the status of a tenant
// @Tags         Tenants
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Tenant ID"
// @Param        request body dto.TenantUpdateStatusRequest true "Status update data"
// @Success      200 {object} response.Response{data=map[string]string} "Status updated successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Tenant not found"
// @Failure      500 {object} response.Response "Failed to update tenant status"
// @Router       /api/cms/v1/tenants/{id}/status [patch]
func (h *TenantHandler) UpdateTenantStatus(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	var req dto.TenantUpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	err = h.tenantService.UpdateStatus(c.Request.Context(), uint(id), models.TenantStatus(req.Status))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Tenant not found")
			return
		}
		response.InternalError(c.Writer, "Failed to update tenant status", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"message": "Status updated successfully"})
}

// DeleteTenant handles DELETE /api/v1/tenants/{id}
func (h *TenantHandler) DeleteTenant(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	err = h.tenantService.Delete(c.Request.Context(), uint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Tenant not found")
			return
		}
		response.InternalError(c.Writer, "Failed to delete tenant", err.Error())
		return
	}

	response.NoContent(c.Writer)
}

// ListTenants handles GET /api/v1/tenants
func (h *TenantHandler) ListTenants(c *gin.Context) {
	// Parse query parameters for filter
	filter := services.ListTenantFilter{
		PageSize:  20,
		Page:      1,
		SortBy:    "created_at",
		SortOrder: "DESC",
	}

	// Parse per_page
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if perPage, err := strconv.Atoi(perPageStr); err == nil && perPage > 0 {
			filter.PageSize = perPage
		}
	}

	// Parse page
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filter.Page = page
		}
	}

	// Parse order_by
	if orderBy := c.Query("order_by"); orderBy != "" {
		filter.SortBy = orderBy
	}

	// Parse order
	if order := c.Query("order"); order != "" {
		filter.SortOrder = order
	}

	// Parse filters
	if status := c.Query("status"); status != "" {
		filter.Status = models.TenantStatus(status)
	}
	if planID := c.Query("plan_id"); planID != "" {
		if id, err := strconv.ParseUint(planID, 10, 32); err == nil {
			filter.PlanID = uint(id)
		}
	}
	if search := c.Query("search"); search != "" {
		filter.Search = search
	}

	result, err := h.tenantService.List(c.Request.Context(), filter)
	if err != nil {
		response.InternalError(c.Writer, "Failed to list tenants", err.Error())
		return
	}

	response.Success(c.Writer, result)
}

// AssignPlan handles POST /api/v1/tenants/{id}/assign-plan
// @Summary      Assign plan to tenant
// @Description  Assign a specific plan to a tenant
// @Tags         Tenants
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Tenant ID"
// @Param        request body dto.TenantAssignPlanRequest true "Plan assignment data"
// @Success      200 {object} response.Response{data=map[string]string} "Plan assigned successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Tenant or plan not found"
// @Failure      500 {object} response.Response "Failed to assign plan"
// @Router       /api/cms/v1/tenants/{id}/assign-plan [post]
func (h *TenantHandler) AssignPlan(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	var req dto.TenantAssignPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	err = h.tenantService.AssignPlan(c.Request.Context(), uint(id), req.PlanID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Tenant or plan not found")
			return
		}
		response.InternalError(c.Writer, "Failed to assign plan", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"message": "Plan assigned successfully"})
}

// GetTenantStats handles GET /api/v1/tenants/{id}/stats
func (h *TenantHandler) GetTenantStats(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	stats, err := h.tenantService.GetTenantStats(c.Request.Context(), uint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Tenant not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get tenant stats", err.Error())
		return
	}

	response.Success(c.Writer, stats)
}

// Conversion functions

// convertToCreateTenantInput converts DTO to service input
func (h *TenantHandler) convertToCreateTenantInput(req dto.TenantCreateRequest) services.CreateTenantInput {
	// Map request fields to service input
	settings := make(map[string]interface{})
	if req.CompanyName != "" {
		settings["company_name"] = req.CompanyName
	}
	if req.CompanyAddress != "" {
		settings["company_address"] = req.CompanyAddress
	}
	if req.CompanyTaxID != "" {
		settings["company_tax_id"] = req.CompanyTaxID
	}
	if req.ContactPhone != "" {
		settings["contact_phone"] = req.ContactPhone
	}
	settings["contact_email"] = req.ContactEmail

	return services.CreateTenantInput{
		Name:        req.Name,
		Domain:      req.Domain,
		PlanID:      req.PlanID,
		AdminEmail:  req.ContactEmail, // Map ContactEmail to AdminEmail
		AdminName:   req.CompanyName,  // Use CompanyName as AdminName
		CompanyInfo: settings,
	}
}

// convertToUpdateTenantInput converts DTO to service input
func (h *TenantHandler) convertToUpdateTenantInput(req dto.TenantUpdateRequest) services.UpdateTenantInput {
	settings := make(map[string]interface{})

	// Handle optional fields
	if req.LogoURL != nil {
		settings["logo_url"] = *req.LogoURL
	}
	if req.FaviconURL != nil {
		settings["favicon_url"] = *req.FaviconURL
	}
	if req.PrimaryColor != nil {
		settings["primary_color"] = *req.PrimaryColor
	}
	if req.CustomCSS != nil {
		settings["custom_css"] = *req.CustomCSS
	}
	if req.CompanyName != nil {
		settings["company_name"] = *req.CompanyName
	}
	if req.CompanyAddress != nil {
		settings["company_address"] = *req.CompanyAddress
	}
	if req.CompanyTaxID != nil {
		settings["company_tax_id"] = *req.CompanyTaxID
	}
	if req.ContactEmail != "" {
		settings["contact_email"] = req.ContactEmail
	}
	if req.ContactPhone != "" {
		settings["contact_phone"] = req.ContactPhone
	}

	input := services.UpdateTenantInput{
		Settings:    settings,
		CompanyInfo: make(map[string]interface{}), // Empty for now
	}

	// Handle direct fields
	if req.Name != "" {
		input.Name = &req.Name
	}

	return input
}

// convertToTenantResponse converts models.Tenant to DTO response
func (h *TenantHandler) convertToTenantResponse(tenant *models.Tenant) *dto.TenantResponse {
	response := &dto.TenantResponse{
		ID:        tenant.ID,
		Name:      tenant.Name,
		Domain:    tenant.Domain,
		Status:    tenant.Status,
		PlanID:    tenant.PlanID,
		TrialEnds: tenant.TrialEndsAt,
		CreatedAt: tenant.CreatedAt,
		UpdatedAt: tenant.UpdatedAt,
	}

	// Extract company info from settings if available
	if tenant.Settings != nil {
		if logoURL, ok := tenant.Settings["logo_url"].(string); ok {
			response.LogoURL = logoURL
		}
		if faviconURL, ok := tenant.Settings["favicon_url"].(string); ok {
			response.FaviconURL = faviconURL
		}
		if primaryColor, ok := tenant.Settings["primary_color"].(string); ok {
			response.PrimaryColor = primaryColor
		}
		if customCSS, ok := tenant.Settings["custom_css"].(string); ok {
			response.CustomCSS = customCSS
		}
		if contactEmail, ok := tenant.Settings["contact_email"].(string); ok {
			response.ContactEmail = contactEmail
		}
		if contactPhone, ok := tenant.Settings["contact_phone"].(string); ok {
			response.ContactPhone = contactPhone
		}
		if companyName, ok := tenant.Settings["company_name"].(string); ok {
			response.CompanyName = companyName
		}
		if companyAddress, ok := tenant.Settings["company_address"].(string); ok {
			response.CompanyAddress = companyAddress
		}
		if companyTaxID, ok := tenant.Settings["company_tax_id"].(string); ok {
			response.CompanyTaxID = companyTaxID
		}
	}

	return response
}
