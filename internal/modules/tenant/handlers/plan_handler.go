package handlers

import (
	"net/http"
	"strconv"
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

type PlanHandler struct {
	planService services.TenantPlanService
	validator   validator.Validator
}

func NewPlanHandler(planService services.TenantPlanService, validator validator.Validator) *PlanHandler {
	return &PlanHandler{
		planService: planService,
		validator:   validator,
	}
}

// CreatePlan handles POST /api/v1/tenant-plans
// @Summary      Create a new tenant plan
// @Description  Create a new tenant plan with the provided information
// @Tags         Tenant Plans
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        request body dto.TenantPlanCreateRequest true "Plan creation data"
// @Success      201 {object} response.Response{data=dto.TenantPlanResponse} "Plan created successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to create plan"
// @Router       /api/cms/v1/tenant-plans [post]
func (h *PlanHandler) CreatePlan(c *gin.Context) {
	var req dto.TenantPlanCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert DTO to service input
	serviceInput := h.convertToCreatePlanInput(req)

	plan, err := h.planService.Create(c.Request.Context(), serviceInput)
	if err != nil {
		response.InternalError(c.Writer, "Failed to create plan", err.Error())
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToPlanResponse(plan)
	response.Created(c.Writer, responseDTO)
}

// GetPlan handles GET /api/v1/tenant-plans/{id}
// @Summary      Get tenant plan by ID
// @Description  Get detailed information about a specific tenant plan
// @Tags         Tenant Plans
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Plan ID"
// @Success      200 {object} response.Response{data=dto.TenantPlanResponse} "Plan retrieved successfully"
// @Failure      400 {object} response.Response "Invalid plan ID"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Plan not found"
// @Failure      500 {object} response.Response "Failed to get plan"
// @Router       /api/cms/v1/tenant-plans/{id} [get]
func (h *PlanHandler) GetPlan(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid plan ID", err.Error())
		return
	}

	plan, err := h.planService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Plan not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get plan", err.Error())
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToPlanResponse(plan)
	response.Success(c.Writer, responseDTO)
}

// GetPlanBySlug handles GET /api/v1/tenant-plans/slug/{slug}
func (h *PlanHandler) GetPlanBySlug(c *gin.Context) {
	slug := c.Param("slug")

	plan, err := h.planService.GetBySlug(c.Request.Context(), slug)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Plan not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get plan", err.Error())
		return
	}

	response.Success(c.Writer, plan)
}

// UpdatePlan handles PUT /api/v1/tenant-plans/{id}
// @Summary      Update a tenant plan
// @Description  Update tenant plan information
// @Tags         Tenant Plans
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Plan ID"
// @Param        request body dto.TenantPlanUpdateRequest true "Plan update data"
// @Success      200 {object} response.Response{data=dto.TenantPlanResponse} "Plan updated successfully"
// @Failure      400 {object} response.Response "Invalid request body or validation error"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Plan not found"
// @Failure      500 {object} response.Response "Failed to update plan"
// @Router       /api/cms/v1/tenant-plans/{id} [put]
func (h *PlanHandler) UpdatePlan(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid plan ID", err.Error())
		return
	}

	var req dto.TenantPlanUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert DTO to service input
	serviceInput := h.convertToUpdatePlanInput(req)

	plan, err := h.planService.Update(c.Request.Context(), uint(id), serviceInput)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Plan not found")
			return
		}
		response.InternalError(c.Writer, "Failed to update plan", err.Error())
		return
	}

	// Convert response to DTO
	responseDTO := h.convertToPlanResponse(plan)
	response.Success(c.Writer, responseDTO)
}

// DeletePlan handles DELETE /api/v1/tenant-plans/{id}
func (h *PlanHandler) DeletePlan(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid plan ID", err.Error())
		return
	}

	err = h.planService.Delete(c.Request.Context(), uint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Plan not found")
			return
		}
		response.InternalError(c.Writer, "Failed to delete plan", err.Error())
		return
	}

	c.Writer.WriteHeader(http.StatusNoContent)
}

// ListPlans handles GET /api/v1/tenant-plans
func (h *PlanHandler) ListPlans(c *gin.Context) {
	// Parse query parameters
	perPage := 20
	orderBy := "sort_order"
	order := "ASC"
	page := 1

	// Parse per_page
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if parsed, err := strconv.Atoi(perPageStr); err == nil && parsed > 0 {
			perPage = parsed
		}
	}

	// Parse page
	if pageStr := c.Query("page"); pageStr != "" {
		if parsed, err := strconv.Atoi(pageStr); err == nil && parsed > 0 {
			page = parsed
		}
	}

	// Parse order_by
	if orderByParam := c.Query("order_by"); orderByParam != "" {
		orderBy = orderByParam
	}

	// Parse order
	if orderParam := c.Query("order"); orderParam != "" {
		order = orderParam
	}

	// Parse filters
	var isVisible *bool
	var isFeatured *bool
	if visible := c.Query("is_visible"); visible != "" {
		if vis, err := strconv.ParseBool(visible); err == nil {
			isVisible = &vis
		}
	}
	if featured := c.Query("is_featured"); featured != "" {
		if feat, err := strconv.ParseBool(featured); err == nil {
			isFeatured = &feat
		}
	}

	filter := models.PlanFilter{
		IsVisible:  isVisible,
		IsFeatured: isFeatured,
		Page:       page,
		PageSize:   perPage,
		SortBy:     orderBy,
		SortOrder:  order,
	}

	result, err := h.planService.List(c.Request.Context(), filter)
	if err != nil {
		response.InternalError(c.Writer, "Failed to list plans", err.Error())
		return
	}

	response.Success(c.Writer, result)
}

// GetActivePlans handles GET /api/v1/tenant-plans/active
func (h *PlanHandler) GetActivePlans(c *gin.Context) {
	plans, err := h.planService.GetActivePlans(c.Request.Context())
	if err != nil {
		response.InternalError(c.Writer, "Failed to get active plans", err.Error())
		return
	}

	response.Success(c.Writer, plans)
}

// GetPlanTenantCount handles GET /api/v1/tenant-plans/{id}/tenant-count
func (h *PlanHandler) GetPlanTenantCount(c *gin.Context) {
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid plan ID", err.Error())
		return
	}

	count, err := h.planService.GetPlanTenantCount(c.Request.Context(), uint(id))
	if err != nil {
		response.InternalError(c.Writer, "Failed to get tenant count", err.Error())
		return
	}

	response.Success(c.Writer, map[string]int64{"tenant_count": count})
}

// Conversion functions

// convertToCreatePlanInput converts DTO to service input
func (h *PlanHandler) convertToCreatePlanInput(req dto.TenantPlanCreateRequest) services.CreatePlanInput {
	// Map request fields to service input
	var monthlyPrice, yearlyPrice float64
	if req.BillingPeriod == "monthly" {
		monthlyPrice = req.Price
	} else if req.BillingPeriod == "yearly" {
		yearlyPrice = req.Price
	}

	return services.CreatePlanInput{
		Name:              req.Name,
		Slug:              req.Slug,
		Description:       req.Description,
		MonthlyPrice:      monthlyPrice,
		YearlyPrice:       yearlyPrice,
		TrialDays:         req.TrialDays,
		MaxUsers:          req.MaxUsers,
		MaxProjects:       req.MaxProjects,
		MaxStorage:        req.MaxStorage,
		MaxAPICallsPerDay: int(req.MaxAPICalls),
		Features:          req.Features,
		IsFeatured:        req.IsFeatured,
		IsVisible:         req.IsVisible,
		DisplayOrder:      req.DisplayOrder,
	}
}

// convertToUpdatePlanInput converts DTO to service input
func (h *PlanHandler) convertToUpdatePlanInput(req dto.TenantPlanUpdateRequest) services.UpdatePlanInput {
	// Convert strings to pointers if provided
	var name, desc *string
	if req.Name != "" {
		name = &req.Name
	}
	if req.Description != "" {
		desc = &req.Description
	}

	// Map max API calls from *int to *int
	var maxAPICalls *int
	if req.MaxAPICallsPerDay != nil {
		maxAPICalls = req.MaxAPICallsPerDay
	}

	return services.UpdatePlanInput{
		Name:              name,
		Description:       desc,
		MonthlyPrice:      req.MonthlyPrice,
		YearlyPrice:       req.YearlyPrice,
		TrialDays:         req.TrialDays,
		MaxUsers:          req.MaxUsers,
		MaxProjects:       req.MaxProjects,
		MaxStorage:        req.MaxStorage,
		MaxAPICallsPerDay: maxAPICalls,
		Features:          req.Features,
		IsFeatured:        req.IsFeatured,
		IsVisible:         req.IsVisible,
		DisplayOrder:      req.DisplayOrder,
		Status:            req.Status,
	}
}

// convertToPlanResponse converts models.TenantPlan to DTO response
func (h *PlanHandler) convertToPlanResponse(plan *models.TenantPlan) *dto.TenantPlanResponse {
	// Convert JSONMap features to slice
	var featuresSlice []string
	if plan.Features != nil {
		for feature := range plan.Features {
			featuresSlice = append(featuresSlice, feature)
		}
	}

	return &dto.TenantPlanResponse{
		ID:                plan.ID,
		Name:              plan.Name,
		Slug:              plan.Slug,
		Description:       plan.Description,
		MonthlyPrice:      plan.MonthlyPrice,
		YearlyPrice:       plan.YearlyPrice,
		MaxUsers:          plan.MaxUsers,
		MaxProjects:       plan.MaxWebsites, // Map MaxWebsites to MaxProjects
		MaxStorage:        int64(plan.MaxStorageGB) * 1024 * 1024 * 1024, // Convert GB to bytes
		MaxAPICallsPerDay: plan.MaxAPICallsPerDay,
		Features:          featuresSlice,
		IsFeatured:        plan.IsFeatured,
		IsVisible:         plan.IsVisible,
		DisplayOrder:      plan.DisplayOrder,
		Status:            plan.Status,
		CreatedAt:         plan.CreatedAt,
		UpdatedAt:         plan.UpdatedAt,
	}
}
