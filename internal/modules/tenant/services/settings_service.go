package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/services"
	"gorm.io/gorm"
)

// tenantSettingService implements TenantSettingService
type tenantSettingService struct {
	settingRepo       repositories.TenantSettingRepository
	tenantRepo        repositories.TenantRepository
	encryptionService services.EncryptionService
}

// NewTenantSettingService creates a new tenant setting service
func NewTenantSettingService(settingRepo repositories.TenantSettingRepository, tenantRepo repositories.TenantRepository, encryptionService services.EncryptionService) TenantSettingService {
	return &tenantSettingService{
		settingRepo:       settingRepo,
		tenantRepo:        tenantRepo,
		encryptionService: encryptionService,
	}
}

// Get retrieves a setting value
func (s *tenantSettingService) Get(ctx context.Context, tenantID uint, category, key string) (interface{}, error) {
	// Validate tenant exists
	if err := s.validateTenantExists(ctx, tenantID); err != nil {
		return nil, err
	}

	setting, err := s.settingRepo.GetByKey(ctx, tenantID, category, key)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("setting not found")
		}
		return nil, fmt.Errorf("failed to get setting: %w", err)
	}

	// Decrypt if encrypted
	var rawValue []byte = setting.Value
	if setting.IsEncrypted && s.encryptionService != nil {
		decryptedValue, err := s.encryptionService.Decrypt(ctx, setting.Value)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt setting value: %w", err)
		}
		rawValue = decryptedValue
	}

	// Parse JSON value
	var value interface{}
	if err := json.Unmarshal(rawValue, &value); err != nil {
		return nil, fmt.Errorf("failed to parse setting value: %w", err)
	}

	return value, nil
}

// Set updates or creates a setting
func (s *tenantSettingService) Set(ctx context.Context, tenantID uint, category, key string, value interface{}) error {
	// Validate tenant exists
	if err := s.validateTenantExists(ctx, tenantID); err != nil {
		return err
	}

	// Validate setting value
	if err := s.ValidateSetting(ctx, category, key, value); err != nil {
		return err
	}

	// Convert value to JSON
	valueBytes, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal setting value: %w", err)
	}

	// Create setting object
	setting := &models.TenantSetting{
		TenantID: tenantID,
		Category: category,
		Key:      key,
		Value:    json.RawMessage(valueBytes),
		DataType: s.getDataType(value),
	}

	// Check if this is a sensitive setting that should be encrypted
	if s.shouldEncrypt(category, key) {
		setting.IsEncrypted = true
		
		// Encrypt the value before storing
		if s.encryptionService != nil {
			encryptedValue, err := s.encryptionService.Encrypt(ctx, valueBytes)
			if err != nil {
				return fmt.Errorf("failed to encrypt setting value: %w", err)
			}
			setting.Value = encryptedValue
		}
	}

	// Upsert setting
	if err := s.settingRepo.Upsert(ctx, setting); err != nil {
		return fmt.Errorf("failed to set setting: %w", err)
	}

	return nil
}

// Delete removes a setting
func (s *tenantSettingService) Delete(ctx context.Context, tenantID uint, category, key string) error {
	// Validate tenant exists
	if err := s.validateTenantExists(ctx, tenantID); err != nil {
		return err
	}

	// Check if setting is protected
	if s.isProtectedSetting(category, key) {
		return fmt.Errorf("cannot delete protected setting")
	}

	if err := s.settingRepo.DeleteByKey(ctx, tenantID, category, key); err != nil {
		return fmt.Errorf("failed to delete setting: %w", err)
	}

	return nil
}

// GetByCategory retrieves all settings in a category
func (s *tenantSettingService) GetByCategory(ctx context.Context, tenantID uint, category string) (map[string]interface{}, error) {
	// Validate tenant exists
	if err := s.validateTenantExists(ctx, tenantID); err != nil {
		return nil, err
	}

	settings, err := s.settingRepo.GetByCategory(ctx, tenantID, category)
	if err != nil {
		return nil, fmt.Errorf("failed to get settings: %w", err)
	}

	// Convert to map
	result := make(map[string]interface{})
	for _, setting := range settings {
		var value interface{}
		if err := json.Unmarshal(setting.Value, &value); err != nil {
			continue // Skip invalid values
		}
		result[setting.Key] = value
	}

	return result, nil
}

// BulkUpdate updates multiple settings
func (s *tenantSettingService) BulkUpdate(ctx context.Context, tenantID uint, updates []SettingUpdate) error {
	// Validate tenant exists
	if err := s.validateTenantExists(ctx, tenantID); err != nil {
		return err
	}

	// Validate all settings first
	for _, update := range updates {
		if err := s.ValidateSetting(ctx, update.Category, update.Key, update.Value); err != nil {
			return fmt.Errorf("validation failed for %s.%s: %w", update.Category, update.Key, err)
		}
	}

	// Convert to repository format
	repoUpdates := make([]models.SettingUpdate, len(updates))
	for i, update := range updates {
		repoUpdates[i] = models.SettingUpdate{
			Category: update.Category,
			Key:      update.Key,
			Value:    update.Value,
		}
	}

	if err := s.settingRepo.BulkUpsert(ctx, tenantID, repoUpdates); err != nil {
		return fmt.Errorf("failed to bulk update settings: %w", err)
	}

	return nil
}

// GetPublicSettings retrieves public settings for a tenant
func (s *tenantSettingService) GetPublicSettings(ctx context.Context, tenantID uint) (map[string]map[string]interface{}, error) {
	// Validate tenant exists
	if err := s.validateTenantExists(ctx, tenantID); err != nil {
		return nil, err
	}

	// Get all public settings
	filter := models.SettingFilter{
		TenantID: tenantID,
		IsPublic: &[]bool{true}[0],
	}

	settings, err := s.settingRepo.List(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get public settings: %w", err)
	}

	// Organize by category
	result := make(map[string]map[string]interface{})
	for _, setting := range settings {
		if _, ok := result[setting.Category]; !ok {
			result[setting.Category] = make(map[string]interface{})
		}

		var value interface{}
		if err := json.Unmarshal(setting.Value, &value); err != nil {
			continue // Skip invalid values
		}
		result[setting.Category][setting.Key] = value
	}

	return result, nil
}

// ValidateSetting validates a setting value against rules
func (s *tenantSettingService) ValidateSetting(ctx context.Context, category, key string, value interface{}) error {
	// Get validation rules for this setting
	rules := s.getValidationRules(category, key)
	if rules == nil {
		return nil // No validation rules
	}

	// Validate based on data type
	switch rules.DataType {
	case models.DataTypeString:
		strVal, ok := value.(string)
		if !ok {
			return fmt.Errorf("value must be a string")
		}
		return s.validateString(strVal, rules)

	case models.DataTypeNumber:
		numVal, err := s.toNumber(value)
		if err != nil {
			return fmt.Errorf("value must be a number")
		}
		return s.validateNumber(numVal, rules)

	case models.DataTypeBoolean:
		_, ok := value.(bool)
		if !ok {
			return fmt.Errorf("value must be a boolean")
		}
		return nil

	case models.DataTypeJSON:
		// Ensure value can be marshaled to JSON
		_, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("value must be valid JSON")
		}
		return nil

	case models.DataTypeArray:
		_, ok := value.([]interface{})
		if !ok {
			return fmt.Errorf("value must be an array")
		}
		return nil

	default:
		return nil
	}
}

// Helper methods

func (s *tenantSettingService) validateTenantExists(ctx context.Context, tenantID uint) error {
	_, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("tenant not found")
		}
		return fmt.Errorf("failed to validate tenant: %w", err)
	}
	return nil
}

func (s *tenantSettingService) getDataType(value interface{}) models.DataType {
	switch value.(type) {
	case string:
		return models.DataTypeString
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
		return models.DataTypeNumber
	case bool:
		return models.DataTypeBoolean
	case []interface{}:
		return models.DataTypeArray
	default:
		return models.DataTypeJSON
	}
}

func (s *tenantSettingService) shouldEncrypt(category, key string) bool {
	// Define which settings should be encrypted
	encryptedSettings := map[string][]string{
		"security": {"api_key", "secret_key", "encryption_key"},
		"payment":  {"stripe_key", "paypal_secret"},
		"email":    {"smtp_password"},
	}

	if keys, ok := encryptedSettings[category]; ok {
		for _, k := range keys {
			if k == key {
				return true
			}
		}
	}

	return false
}

func (s *tenantSettingService) isProtectedSetting(category, key string) bool {
	// Define which settings cannot be deleted
	protectedSettings := map[string][]string{
		"core": {"tenant_id", "created_at"},
	}

	if keys, ok := protectedSettings[category]; ok {
		for _, k := range keys {
			if k == key {
				return true
			}
		}
	}

	return false
}

type validationRule struct {
	DataType models.DataType
	MinLen   int
	MaxLen   int
	Pattern  string
	MinValue float64
	MaxValue float64
	Options  []string
}

func (s *tenantSettingService) getValidationRules(category, key string) *validationRule {
	// Define validation rules for known settings
	rules := map[string]map[string]*validationRule{
		"general": {
			"site_name": {
				DataType: models.DataTypeString,
				MinLen:   1,
				MaxLen:   100,
			},
			"site_url": {
				DataType: models.DataTypeString,
				Pattern:  `^https?://`,
			},
			"timezone": {
				DataType: models.DataTypeString,
				Options:  []string{"UTC", "America/New_York", "Europe/London", "Asia/Tokyo"},
			},
		},
		"limits": {
			"max_file_size": {
				DataType: models.DataTypeNumber,
				MinValue: 0,
				MaxValue: 1073741824, // 1GB
			},
			"rate_limit": {
				DataType: models.DataTypeNumber,
				MinValue: 1,
				MaxValue: 10000,
			},
		},
	}

	if catRules, ok := rules[category]; ok {
		if rule, ok := catRules[key]; ok {
			return rule
		}
	}

	return nil
}

func (s *tenantSettingService) validateString(value string, rules *validationRule) error {
	if rules.MinLen > 0 && len(value) < rules.MinLen {
		return fmt.Errorf("value must be at least %d characters", rules.MinLen)
	}

	if rules.MaxLen > 0 && len(value) > rules.MaxLen {
		return fmt.Errorf("value must be at most %d characters", rules.MaxLen)
	}

	if rules.Pattern != "" {
		matched, err := regexp.MatchString(rules.Pattern, value)
		if err != nil || !matched {
			return fmt.Errorf("value does not match required pattern")
		}
	}

	if len(rules.Options) > 0 {
		valid := false
		for _, opt := range rules.Options {
			if opt == value {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("value must be one of: %s", strings.Join(rules.Options, ", "))
		}
	}

	return nil
}

func (s *tenantSettingService) validateNumber(value float64, rules *validationRule) error {
	if value < rules.MinValue {
		return fmt.Errorf("value must be at least %v", rules.MinValue)
	}

	if value > rules.MaxValue {
		return fmt.Errorf("value must be at most %v", rules.MaxValue)
	}

	return nil
}

func (s *tenantSettingService) toNumber(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert to number")
	}
}
