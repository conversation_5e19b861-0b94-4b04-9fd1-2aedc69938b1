package models

import (
	"encoding/json"
	"time"
)

// TenantFeature represents a feature flag for a specific tenant
type TenantFeature struct {
	ID                 uint            `json:"id" gorm:"primaryKey"`
	TenantID           uint            `json:"tenant_id" gorm:"not null;uniqueIndex:idx_tenant_feature"`
	FeatureKey         string          `json:"feature_key" gorm:"type:varchar(100);not null;uniqueIndex:idx_tenant_feature" validate:"required,max=100"`
	FeatureName        string          `json:"feature_name" gorm:"not null" validate:"required,max=255"`
	Description        string          `json:"description,omitempty" validate:"max=500"`
	Enabled            bool            `json:"enabled" gorm:"not null;default:false"`
	Configuration      json.RawMessage `json:"configuration,omitempty" gorm:"type:json" swaggertype:"object"`
	RolloutPercentage  int             `json:"rollout_percentage" gorm:"not null;default:100" validate:"min=0,max=100"`
	RolloutGroups      json.RawMessage `json:"rollout_groups,omitempty" gorm:"type:json" swaggertype:"array,string"`
	AvailableFrom      *time.Time      `json:"available_from,omitempty"`
	AvailableUntil     *time.Time      `json:"available_until,omitempty"`
	RequiresFeatures   json.RawMessage `json:"requires_features,omitempty" gorm:"type:json" swaggertype:"array,string"`
	ConflictsWith      json.RawMessage `json:"conflicts_with,omitempty" gorm:"type:json" swaggertype:"array,string"`
	EnabledAt          *time.Time      `json:"enabled_at,omitempty"`
	EnabledBy          uint            `json:"enabled_by,omitempty"`
	DisabledAt         *time.Time      `json:"disabled_at,omitempty"`
	DisabledBy         uint            `json:"disabled_by,omitempty"`
	CreatedAt          time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt          time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Tenant         *Tenant         `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	FeatureCatalog *FeatureCatalog `json:"feature_catalog,omitempty" gorm:"foreignKey:FeatureKey;references:FeatureKey"`
}

// TableName specifies the table name for TenantFeature
func (TenantFeature) TableName() string {
	return "tenant_features"
}

// IsAvailable checks if the feature is currently available based on time constraints
func (f *TenantFeature) IsAvailable() bool {
	now := time.Now()
	
	if f.AvailableFrom != nil && now.Before(*f.AvailableFrom) {
		return false
	}
	
	if f.AvailableUntil != nil && now.After(*f.AvailableUntil) {
		return false
	}
	
	return true
}

// IsEnabledForUser checks if the feature is enabled for a specific user based on rollout
func (f *TenantFeature) IsEnabledForUser(userID string) bool {
	if !f.Enabled || !f.IsAvailable() {
		return false
	}
	
	// If rollout is 100%, feature is enabled for all
	if f.RolloutPercentage >= 100 {
		return true
	}
	
	// If rollout is 0%, feature is disabled for all
	if f.RolloutPercentage <= 0 {
		return false
	}
	
	// Check if user is in rollout groups
	if f.RolloutGroups != nil {
		var groups []string
		if err := json.Unmarshal(f.RolloutGroups, &groups); err == nil {
			for _, group := range groups {
				if group == userID {
					return true
				}
			}
		}
	}
	
	// Simple hash-based rollout (deterministic)
	hash := 0
	for _, char := range userID {
		hash = (hash*31 + int(char)) % 100
	}
	
	return hash < f.RolloutPercentage
}

// GetRequiredFeatures returns the list of required features
func (f *TenantFeature) GetRequiredFeatures() []string {
	var features []string
	if f.RequiresFeatures != nil {
		json.Unmarshal(f.RequiresFeatures, &features)
	}
	return features
}

// GetConflictingFeatures returns the list of conflicting features
func (f *TenantFeature) GetConflictingFeatures() []string {
	var features []string
	if f.ConflictsWith != nil {
		json.Unmarshal(f.ConflictsWith, &features)
	}
	return features
}

// GetConfiguration returns the configuration as a map
func (f *TenantFeature) GetConfiguration() (map[string]interface{}, error) {
	if f.Configuration == nil {
		return make(map[string]interface{}), nil
	}
	
	var config map[string]interface{}
	err := json.Unmarshal(f.Configuration, &config)
	return config, err
}

// SetConfiguration sets the configuration from a map
func (f *TenantFeature) SetConfiguration(config map[string]interface{}) error {
	bytes, err := json.Marshal(config)
	if err != nil {
		return err
	}
	f.Configuration = bytes
	return nil
}

// FeatureFilter represents filters for querying features
type FeatureFilter struct {
	TenantID   uint     `json:"tenant_id,omitempty"`
	Enabled    *bool    `json:"enabled,omitempty"`
	Search     string   `json:"search,omitempty"`
	Categories []string `json:"categories,omitempty"`
}

// FeatureToggleRequest represents the request to enable/disable a feature
type FeatureToggleRequest struct {
	Enabled           bool                   `json:"enabled"`
	Configuration     map[string]interface{} `json:"configuration,omitempty"`
	RolloutPercentage int                    `json:"rollout_percentage,omitempty" validate:"min=0,max=100"`
	RolloutGroups     []string               `json:"rollout_groups,omitempty"`
	AvailableFrom     *time.Time             `json:"available_from,omitempty"`
	AvailableUntil    *time.Time             `json:"available_until,omitempty"`
}

// FeatureCheckRequest represents the request to check if a feature is enabled
type FeatureCheckRequest struct {
	FeatureKey string `json:"feature_key" validate:"required"`
	UserID     string `json:"user_id,omitempty"`
	Context    map[string]interface{} `json:"context,omitempty"`
}

// FeatureCheckResponse represents the response for feature check
type FeatureCheckResponse struct {
	Enabled       bool                   `json:"enabled"`
	Reason        string                 `json:"reason,omitempty"`
	Configuration map[string]interface{} `json:"configuration,omitempty"`
}