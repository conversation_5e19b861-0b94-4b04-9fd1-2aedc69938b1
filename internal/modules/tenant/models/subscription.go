package models

import (
	"database/sql/driver"
	"time"
)

// SubscriptionStatus represents the status of a subscription
// @Enum active,trialing,past_due,canceled,expired,suspended
type SubscriptionStatus string

const (
	SubscriptionStatusActive    SubscriptionStatus = "active"
	SubscriptionStatusTrialing  SubscriptionStatus = "trialing"
	SubscriptionStatusPastDue   SubscriptionStatus = "past_due"
	SubscriptionStatusCanceled  SubscriptionStatus = "canceled"
	SubscriptionStatusExpired   SubscriptionStatus = "expired"
	SubscriptionStatusSuspended SubscriptionStatus = "suspended"
)

// Scan implements sql.Scanner interface
func (s *SubscriptionStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = SubscriptionStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s SubscriptionStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// BillingCycle represents billing frequency
// @Enum monthly,yearly
type BillingCycle string

const (
	BillingCycleMonthly BillingCycle = "monthly"
	BillingCycleYearly  BillingCycle = "yearly"
)

// <PERSON>an implements sql.Scanner interface
func (b *BillingCycle) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*b = BillingCycle(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (b BillingCycle) Value() (driver.Value, error) {
	return string(b), nil
}

// Subscription represents a tenant's subscription to a plan
type Subscription struct {
	ID                   uint               `json:"id" gorm:"primaryKey"`
	TenantID             uint               `json:"tenant_id" gorm:"not null;index"`
	PlanID               uint               `json:"plan_id" gorm:"not null"`
	Status               SubscriptionStatus `json:"status" gorm:"type:enum('active','trialing','past_due','canceled','expired','suspended');default:'trialing';not null"`
	BillingCycle         BillingCycle       `json:"billing_cycle" gorm:"type:enum('monthly','yearly');default:'monthly';not null"`
	Amount               float64            `json:"amount" gorm:"not null"`
	Currency             string             `json:"currency" gorm:"size:3;default:'USD';not null"`
	TrialEndsAt          *time.Time         `json:"trial_ends_at,omitempty"`
	CurrentPeriodStart   time.Time          `json:"current_period_start" gorm:"not null"`
	CurrentPeriodEnd     time.Time          `json:"current_period_end" gorm:"not null"`
	NextBillingDate      *time.Time         `json:"next_billing_date,omitempty"`
	CanceledAt           *time.Time         `json:"canceled_at,omitempty"`
	CancelAtPeriodEnd    bool               `json:"cancel_at_period_end" gorm:"default:false"`
	PaymentFailureCount  int                `json:"payment_failure_count" gorm:"default:0"`
	LastPaymentFailureAt *time.Time         `json:"last_payment_failure_at,omitempty"`
	ExternalSubscriptionID string           `json:"external_subscription_id,omitempty" gorm:"index"`
	PaymentProviderData  JSONMap            `json:"payment_provider_data,omitempty" gorm:"type:json"`
	CreatedAt            time.Time          `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt            time.Time          `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Tenant *Tenant     `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Plan   *TenantPlan `json:"plan,omitempty" gorm:"foreignKey:PlanID"`
}

// TableName specifies the table name for Subscription
func (Subscription) TableName() string {
	return "subscriptions"
}

// IsActive checks if subscription is active
func (s *Subscription) IsActive() bool {
	return s.Status == SubscriptionStatusActive || s.Status == SubscriptionStatusTrialing
}

// IsTrialing checks if subscription is in trial period
func (s *Subscription) IsTrialing() bool {
	return s.Status == SubscriptionStatusTrialing && 
		   s.TrialEndsAt != nil && 
		   s.TrialEndsAt.After(time.Now())
}

// IsExpired checks if subscription is expired
func (s *Subscription) IsExpired() bool {
	return s.Status == SubscriptionStatusExpired || 
		   (s.Status != SubscriptionStatusCanceled && s.CurrentPeriodEnd.Before(time.Now()))
}

// DaysUntilExpiry returns days until subscription expires
func (s *Subscription) DaysUntilExpiry() int {
	if s.IsExpired() {
		return 0
	}
	
	expiryDate := s.CurrentPeriodEnd
	if s.IsTrialing() && s.TrialEndsAt != nil {
		expiryDate = *s.TrialEndsAt
	}
	
	days := int(time.Until(expiryDate).Hours() / 24)
	if days < 0 {
		return 0
	}
	return days
}

// CanUpgrade checks if subscription can be upgraded
func (s *Subscription) CanUpgrade() bool {
	return s.IsActive() && s.Status != SubscriptionStatusCanceled
}

// CanDowngrade checks if subscription can be downgraded
func (s *Subscription) CanDowngrade() bool {
	return s.IsActive() && s.Status != SubscriptionStatusCanceled
}

// PaymentFailure represents a payment failure record
type PaymentFailure struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	TenantID       uint      `json:"tenant_id" gorm:"not null;index"`
	SubscriptionID uint      `json:"subscription_id" gorm:"not null;index"`
	Amount         float64   `json:"amount" gorm:"not null"`
	Currency       string    `json:"currency" gorm:"size:3;not null"`
	FailureReason  string    `json:"failure_reason" gorm:"type:text"`
	ExternalID     string    `json:"external_id,omitempty" gorm:"index"`
	RetryCount     int       `json:"retry_count" gorm:"default:0"`
	NextRetryAt    *time.Time `json:"next_retry_at,omitempty"`
	ResolvedAt     *time.Time `json:"resolved_at,omitempty"`
	CreatedAt      time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Tenant       *Tenant       `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Subscription *Subscription `json:"subscription,omitempty" gorm:"foreignKey:SubscriptionID"`
}

// TableName specifies the table name for PaymentFailure
func (PaymentFailure) TableName() string {
	return "payment_failures"
}

// IsResolved checks if payment failure has been resolved
func (pf *PaymentFailure) IsResolved() bool {
	return pf.ResolvedAt != nil
}

// CanRetry checks if payment can be retried
func (pf *PaymentFailure) CanRetry() bool {
	return !pf.IsResolved() && 
		   pf.RetryCount < 3 && 
		   (pf.NextRetryAt == nil || pf.NextRetryAt.Before(time.Now()))
}

// PlanTransition represents a plan upgrade/downgrade transition
type PlanTransition struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	TenantID       uint      `json:"tenant_id" gorm:"not null;index"`
	SubscriptionID uint      `json:"subscription_id" gorm:"not null;index"`
	FromPlanID     uint      `json:"from_plan_id" gorm:"not null"`
	ToPlanID       uint      `json:"to_plan_id" gorm:"not null"`
	TransitionType string    `json:"transition_type" gorm:"not null"` // upgrade, downgrade, change
	EffectiveDate  time.Time `json:"effective_date" gorm:"not null"`
	ProratedAmount float64   `json:"prorated_amount" gorm:"default:0"`
	Notes          string    `json:"notes,omitempty" gorm:"type:text"`
	ProcessedAt    *time.Time `json:"processed_at,omitempty"`
	CreatedAt      time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Tenant       *Tenant       `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Subscription *Subscription `json:"subscription,omitempty" gorm:"foreignKey:SubscriptionID"`
	FromPlan     *TenantPlan   `json:"from_plan,omitempty" gorm:"foreignKey:FromPlanID"`
	ToPlan       *TenantPlan   `json:"to_plan,omitempty" gorm:"foreignKey:ToPlanID"`
}

// TableName specifies the table name for PlanTransition
func (PlanTransition) TableName() string {
	return "plan_transitions"
}

// IsProcessed checks if transition has been processed
func (pt *PlanTransition) IsProcessed() bool {
	return pt.ProcessedAt != nil
}

// IsEffective checks if transition is now effective
func (pt *PlanTransition) IsEffective() bool {
	return pt.EffectiveDate.Before(time.Now()) || pt.EffectiveDate.Equal(time.Now())
}