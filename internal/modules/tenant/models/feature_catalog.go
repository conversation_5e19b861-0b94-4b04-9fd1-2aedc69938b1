package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// FeatureStatus represents the status of a feature in the catalog
// @Enum active,inactive,deprecated
type FeatureStatus string

const (
	FeatureStatusActive     FeatureStatus = "active"
	FeatureStatusInactive   FeatureStatus = "inactive"
	FeatureStatusDeprecated FeatureStatus = "deprecated"
)

// Scan implements sql.Scanner interface
func (s *FeatureStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = FeatureStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s FeatureStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// FeatureCatalog represents a global feature definition
type FeatureCatalog struct {
	ID                    uint            `json:"id" gorm:"primaryKey"`
	FeatureKey            string          `json:"feature_key" gorm:"type:varchar(100);uniqueIndex;not null" validate:"required,max=100"`
	FeatureName           string          `json:"feature_name" gorm:"not null" validate:"required,max=255"`
	Description           string          `json:"description,omitempty" validate:"max=1000"`
	Category              string          `json:"category" gorm:"type:varchar(50);not null;index" validate:"required,max=50"`
	IsBeta                bool            `json:"is_beta" gorm:"not null;default:false"`
	IsExperimental        bool            `json:"is_experimental" gorm:"not null;default:false"`
	IsDeprecated          bool            `json:"is_deprecated" gorm:"not null;default:false"`
	RequiredPlans         json.RawMessage `json:"required_plans,omitempty" gorm:"type:json" swaggertype:"array,integer"`
	ExcludedPlans         json.RawMessage `json:"excluded_plans,omitempty" gorm:"type:json" swaggertype:"array,integer"`
	DefaultEnabled        bool            `json:"default_enabled" gorm:"not null;default:false"`
	DefaultConfiguration  json.RawMessage `json:"default_configuration,omitempty" gorm:"type:json" swaggertype:"object"`
	DocumentationURL      string          `json:"documentation_url,omitempty" validate:"omitempty,url"`
	ChangelogURL          string          `json:"changelog_url,omitempty" validate:"omitempty,url"`
	Status                FeatureStatus   `json:"status" gorm:"type:enum('active','inactive','deprecated');not null;default:'active'"`
	CreatedAt             time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt             time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Virtual fields (not stored in DB)
	EnabledTenantsCount int `json:"enabled_tenants_count,omitempty" gorm:"-"`
	AdoptionRate        float64 `json:"adoption_rate,omitempty" gorm:"-"`
}

// TableName specifies the table name for FeatureCatalog
func (FeatureCatalog) TableName() string {
	return "feature_catalog"
}

// IsActive checks if the feature is active
func (f *FeatureCatalog) IsActive() bool {
	return f.Status == FeatureStatusActive && !f.IsDeprecated
}

// GetRequiredPlans returns the list of required plan IDs
func (f *FeatureCatalog) GetRequiredPlans() []uint {
	var plans []uint
	if f.RequiredPlans != nil {
		json.Unmarshal(f.RequiredPlans, &plans)
	}
	return plans
}

// GetExcludedPlans returns the list of excluded plan IDs
func (f *FeatureCatalog) GetExcludedPlans() []uint {
	var plans []uint
	if f.ExcludedPlans != nil {
		json.Unmarshal(f.ExcludedPlans, &plans)
	}
	return plans
}

// IsAvailableForPlan checks if the feature is available for a specific plan
func (f *FeatureCatalog) IsAvailableForPlan(planID uint) bool {
	// Check if plan is in excluded list
	excludedPlans := f.GetExcludedPlans()
	for _, excluded := range excludedPlans {
		if excluded == planID {
			return false
		}
	}
	
	// Check if plan is in required list (if any)
	requiredPlans := f.GetRequiredPlans()
	if len(requiredPlans) == 0 {
		return true // No specific plan required
	}
	
	for _, required := range requiredPlans {
		if required == planID {
			return true
		}
	}
	
	return false
}

// GetDefaultConfiguration returns the default configuration as a map
func (f *FeatureCatalog) GetDefaultConfiguration() (map[string]interface{}, error) {
	if f.DefaultConfiguration == nil {
		return make(map[string]interface{}), nil
	}
	
	var config map[string]interface{}
	err := json.Unmarshal(f.DefaultConfiguration, &config)
	return config, err
}

// SetDefaultConfiguration sets the default configuration from a map
func (f *FeatureCatalog) SetDefaultConfiguration(config map[string]interface{}) error {
	bytes, err := json.Marshal(config)
	if err != nil {
		return err
	}
	f.DefaultConfiguration = bytes
	return nil
}

// FeatureCatalogFilter represents filters for querying feature catalog
type FeatureCatalogFilter struct {
	Category       string        `json:"category,omitempty"`
	Categories     []string      `json:"categories,omitempty"`
	Status         FeatureStatus `json:"status,omitempty"`
	IsBeta         *bool         `json:"is_beta,omitempty"`
	IsExperimental *bool         `json:"is_experimental,omitempty"`
	IsDeprecated   *bool         `json:"is_deprecated,omitempty"`
	PlanID         uint          `json:"plan_id,omitempty"`
	Search         string        `json:"search,omitempty"`
	Page           int           `json:"page,omitempty"`
	PageSize       int           `json:"page_size,omitempty"`
	SortBy         string        `json:"sort_by,omitempty"`
	SortOrder      string        `json:"sort_order,omitempty"`
}

// FeatureCatalogCreateRequest represents the request to create a feature in catalog
type FeatureCatalogCreateRequest struct {
	FeatureKey           string                 `json:"feature_key" validate:"required,max=100"`
	FeatureName          string                 `json:"feature_name" validate:"required,max=255"`
	Description          string                 `json:"description,omitempty" validate:"max=1000"`
	Category             string                 `json:"category" validate:"required,max=50"`
	IsBeta               bool                   `json:"is_beta"`
	IsExperimental       bool                   `json:"is_experimental"`
	RequiredPlans        []uint                 `json:"required_plans,omitempty"`
	ExcludedPlans        []uint                 `json:"excluded_plans,omitempty"`
	DefaultEnabled       bool                   `json:"default_enabled"`
	DefaultConfiguration map[string]interface{} `json:"default_configuration,omitempty"`
	DocumentationURL     string                 `json:"documentation_url,omitempty" validate:"omitempty,url"`
	ChangelogURL         string                 `json:"changelog_url,omitempty" validate:"omitempty,url"`
}

// FeatureCatalogUpdateRequest represents the request to update a feature in catalog
type FeatureCatalogUpdateRequest struct {
	FeatureName          string                 `json:"feature_name,omitempty" validate:"omitempty,max=255"`
	Description          string                 `json:"description,omitempty" validate:"max=1000"`
	Category             string                 `json:"category,omitempty" validate:"omitempty,max=50"`
	IsBeta               *bool                  `json:"is_beta,omitempty"`
	IsExperimental       *bool                  `json:"is_experimental,omitempty"`
	IsDeprecated         *bool                  `json:"is_deprecated,omitempty"`
	RequiredPlans        []uint                 `json:"required_plans,omitempty"`
	ExcludedPlans        []uint                 `json:"excluded_plans,omitempty"`
	DefaultEnabled       *bool                  `json:"default_enabled,omitempty"`
	DefaultConfiguration map[string]interface{} `json:"default_configuration,omitempty"`
	DocumentationURL     string                 `json:"documentation_url,omitempty" validate:"omitempty,url"`
	ChangelogURL         string                 `json:"changelog_url,omitempty" validate:"omitempty,url"`
	Status               FeatureStatus          `json:"status,omitempty" validate:"omitempty,oneof=active inactive deprecated"`
}

// FeatureCategory represents a category of features
type FeatureCategory struct {
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Description string `json:"description"`
	Icon        string `json:"icon,omitempty"`
	Order       int    `json:"order"`
}

// PredefinedFeatureCategories returns the list of predefined feature categories
func PredefinedFeatureCategories() []FeatureCategory {
	return []FeatureCategory{
		{Name: "core", DisplayName: "Core Features", Description: "Essential platform features", Order: 1},
		{Name: "content", DisplayName: "Content Management", Description: "Content creation and management features", Order: 2},
		{Name: "commerce", DisplayName: "E-Commerce", Description: "Online store and payment features", Order: 3},
		{Name: "marketing", DisplayName: "Marketing", Description: "Marketing and SEO features", Order: 4},
		{Name: "analytics", DisplayName: "Analytics", Description: "Data and analytics features", Order: 5},
		{Name: "integration", DisplayName: "Integrations", Description: "Third-party integrations", Order: 6},
		{Name: "security", DisplayName: "Security", Description: "Security and compliance features", Order: 7},
		{Name: "experimental", DisplayName: "Experimental", Description: "Beta and experimental features", Order: 8},
	}
}