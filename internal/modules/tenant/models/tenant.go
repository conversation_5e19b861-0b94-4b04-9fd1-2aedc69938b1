package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
	
	"gorm.io/gorm"
)

// TenantStatus represents the status of a tenant
// @Enum active,suspended,inactive,trial,deleted
type TenantStatus string

const (
	TenantStatusActive    TenantStatus = "active"
	TenantStatusSuspended TenantStatus = "suspended"
	TenantStatusInactive  TenantStatus = "inactive"
	TenantStatusTrial     TenantStatus = "trial"
	TenantStatusDeleted   TenantStatus = "deleted"
)

// Scan implements sql.Scanner interface
func (s *TenantStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = TenantStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s TenantStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// JSONMap is a custom type for JSON fields
type JSONMap map[string]interface{}

// Scan implements sql.Scanner interface
func (m *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*m = make(JSONMap)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, m)
}

// Value implements driver.Valuer interface
func (m JSONMap) Value() (driver.Value, error) {
	if m == nil {
		return "{}", nil
	}
	return json.Marshal(m)
}

// Tenant represents a tenant/organization in the system
type Tenant struct {
	ID           uint         `json:"id" gorm:"primaryKey"`
	Name         string       `json:"name" gorm:"not null" validate:"required,min=2,max=255"`
	Slug         string       `json:"slug" gorm:"type:varchar(255);uniqueIndex;not null" validate:"required,min=2,max=100,slug"`
	Domain       string       `json:"domain,omitempty" gorm:"type:varchar(255);uniqueIndex" validate:"omitempty,fqdn"`
	PlanID       uint         `json:"plan_id" gorm:"not null;default:1" validate:"required,min=1"`
	Status       TenantStatus `json:"status" gorm:"type:enum('active','suspended','inactive','trial','deleted');default:'trial';not null"`
	OwnerEmail   string       `json:"owner_email" gorm:"not null" validate:"required,email"`
	BillingEmail string       `json:"billing_email,omitempty" validate:"omitempty,email"`
	SupportEmail string       `json:"support_email,omitempty" validate:"omitempty,email"`
	Settings     JSONMap      `json:"settings,omitempty" gorm:"type:json"`
	Metadata     JSONMap      `json:"metadata,omitempty" gorm:"type:json"`
	TrialEndsAt  *time.Time   `json:"trial_ends_at,omitempty"`
	CreatedAt    time.Time    `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time    `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Plan            *TenantPlan      `json:"plan,omitempty" gorm:"foreignKey:PlanID"`
	TenantSettings  *[]TenantSetting `json:"tenant_settings,omitempty" gorm:"foreignKey:TenantID"`
	Features        *[]TenantFeature `json:"features,omitempty" gorm:"foreignKey:TenantID"`
}

// TableName specifies the table name for Tenant
func (Tenant) TableName() string {
	return "tenants"
}

// IsActive checks if tenant is active
func (t *Tenant) IsActive() bool {
	return t.Status == TenantStatusActive || t.Status == TenantStatusTrial
}

// IsTrial checks if tenant is in trial period
func (t *Tenant) IsTrial() bool {
	return t.Status == TenantStatusTrial && (t.TrialEndsAt == nil || t.TrialEndsAt.After(time.Now()))
}

// IsDeleted checks if tenant is soft deleted
func (t *Tenant) IsDeleted() bool {
	return t.Status == TenantStatusDeleted
}

// BeforeCreate hook for Tenant
func (t *Tenant) BeforeCreate(tx *gorm.DB) error {
	if t.Settings == nil {
		t.Settings = make(JSONMap)
	}
	if t.Metadata == nil {
		t.Metadata = make(JSONMap)
	}
	return nil
}

// TenantFilter represents filters for querying tenants
type TenantFilter struct {
	Status       TenantStatus `json:"status,omitempty"`
	PlanID       uint         `json:"plan_id,omitempty"`
	Search       string       `json:"search,omitempty"`
	IncludeDeleted bool       `json:"include_deleted,omitempty"`
	Page         int          `json:"page,omitempty"`
	PageSize     int          `json:"page_size,omitempty"`
	SortBy       string       `json:"sort_by,omitempty"`
	SortOrder    string       `json:"sort_order,omitempty"`
}

// TenantCreateRequest represents the request to create a tenant
type TenantCreateRequest struct {
	Name         string  `json:"name" validate:"required,min=2,max=255"`
	Slug         string  `json:"slug,omitempty" validate:"omitempty,min=2,max=100,slug"`
	Domain       string  `json:"domain,omitempty" validate:"omitempty,fqdn"`
	PlanID       uint    `json:"plan_id,omitempty" validate:"omitempty,min=1"`
	OwnerEmail   string  `json:"owner_email" validate:"required,email"`
	BillingEmail string  `json:"billing_email,omitempty" validate:"omitempty,email"`
	SupportEmail string  `json:"support_email,omitempty" validate:"omitempty,email"`
	Settings     JSONMap `json:"settings,omitempty"`
	Metadata     JSONMap `json:"metadata,omitempty"`
}

// TenantUpdateRequest represents the request to update a tenant
type TenantUpdateRequest struct {
	Name         string       `json:"name,omitempty" validate:"omitempty,min=2,max=255"`
	Domain       string       `json:"domain,omitempty" validate:"omitempty,fqdn"`
	PlanID       uint         `json:"plan_id,omitempty" validate:"omitempty,min=1"`
	Status       TenantStatus `json:"status,omitempty" validate:"omitempty,oneof=active suspended inactive"`
	BillingEmail string       `json:"billing_email,omitempty" validate:"omitempty,email"`
	SupportEmail string       `json:"support_email,omitempty" validate:"omitempty,email"`
	Settings     JSONMap      `json:"settings,omitempty"`
	Metadata     JSONMap      `json:"metadata,omitempty"`
}