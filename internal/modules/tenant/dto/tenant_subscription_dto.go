package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

// TenantSubscriptionCreateRequest represents request to create a subscription
type TenantSubscriptionCreateRequest struct {
	PlanID       uint   `json:"plan_id" validate:"required" example:"2"`
	BillingCycle string `json:"billing_cycle" validate:"required,oneof=monthly yearly" example:"monthly"`
	StartDate    *time.Time `json:"start_date,omitempty"`
	PaymentMethodID string `json:"payment_method_id,omitempty" example:"pm_1234567890"`
	CouponCode   string `json:"coupon_code,omitempty" example:"SUMMER20"`
}

// TenantSubscriptionUpdateRequest represents request to update subscription
type TenantSubscriptionUpdateRequest struct {
	PlanID          *uint      `json:"plan_id,omitempty" example:"3"`
	BillingCycle    *string    `json:"billing_cycle,omitempty" validate:"omitempty,oneof=monthly yearly" example:"yearly"`
	PaymentMethodID *string    `json:"payment_method_id,omitempty" example:"pm_0987654321"`
	AutoRenew       *bool      `json:"auto_renew,omitempty" example:"true"`
	RenewalDate     *time.Time `json:"renewal_date,omitempty"`
}

// TenantSubscriptionCancelRequest represents request to cancel subscription
type TenantSubscriptionCancelRequest struct {
	Reason       string     `json:"reason,omitempty" example:"No longer needed"`
	CancelAt     *time.Time `json:"cancel_at,omitempty"`
	Immediately  bool       `json:"immediately" example:"false"`
	Feedback     string     `json:"feedback,omitempty" example:"Service was great but we're downsizing"`
}

// TenantSubscriptionReactivateRequest represents request to reactivate subscription
type TenantSubscriptionReactivateRequest struct {
	PlanID          *uint   `json:"plan_id,omitempty" example:"2"`
	BillingCycle    *string `json:"billing_cycle,omitempty" validate:"omitempty,oneof=monthly yearly" example:"monthly"`
	PaymentMethodID string  `json:"payment_method_id" validate:"required" example:"pm_1234567890"`
}

// TenantSubscriptionResponse represents subscription information
type TenantSubscriptionResponse struct {
	ID                 uint                           `json:"id" example:"1"`
	TenantID           uint                           `json:"tenant_id" example:"1"`
	PlanID             uint                           `json:"plan_id" example:"2"`
	Plan               *TenantPlanResponse            `json:"plan,omitempty"`
	Status             models.SubscriptionStatus      `json:"status" example:"active"`
	BillingCycle       string                         `json:"billing_cycle" example:"monthly"`
	CurrentPeriodStart time.Time                      `json:"current_period_start"`
	CurrentPeriodEnd   time.Time                      `json:"current_period_end"`
	NextBillingDate    *time.Time                     `json:"next_billing_date,omitempty"`
	TrialStart         *time.Time                     `json:"trial_start,omitempty"`
	TrialEnd           *time.Time                     `json:"trial_end,omitempty"`
	CanceledAt         *time.Time                     `json:"canceled_at,omitempty"`
	CancelAtPeriodEnd  bool                           `json:"cancel_at_period_end" example:"false"`
	AutoRenew          bool                           `json:"auto_renew" example:"true"`
	PaymentMethodID    string                         `json:"payment_method_id,omitempty" example:"pm_1234567890"`
	LastPaymentDate    *time.Time                     `json:"last_payment_date,omitempty"`
	NextPaymentAmount  float64                        `json:"next_payment_amount" example:"29.99"`
	Currency           string                         `json:"currency" example:"USD"`
	CreatedAt          time.Time                      `json:"created_at"`
	UpdatedAt          time.Time                      `json:"updated_at"`
}

// TenantSubscriptionListResponse represents response for listing subscriptions
type TenantSubscriptionListResponse struct {
	Subscriptions []TenantSubscriptionResponse `json:"subscriptions"`
	Total         int64                        `json:"total" example:"5"`
	Page          int                          `json:"page" example:"1"`
	PageSize      int                          `json:"page_size" example:"20"`
	TotalPages    int                          `json:"total_pages" example:"1"`
}

// TenantSubscriptionHistoryResponse represents subscription history
type TenantSubscriptionHistoryResponse struct {
	ID          uint                      `json:"id" example:"1"`
	TenantID    uint                      `json:"tenant_id" example:"1"`
	Action      string                    `json:"action" example:"plan_changed"`
	OldPlanID   *uint                     `json:"old_plan_id,omitempty" example:"1"`
	NewPlanID   *uint                     `json:"new_plan_id,omitempty" example:"2"`
	OldPlan     *TenantPlanResponse       `json:"old_plan,omitempty"`
	NewPlan     *TenantPlanResponse       `json:"new_plan,omitempty"`
	Amount      *float64                  `json:"amount,omitempty" example:"29.99"`
	Currency    string                    `json:"currency" example:"USD"`
	Reason      string                    `json:"reason,omitempty" example:"User upgrade"`
	PerformedBy *uint                     `json:"performed_by,omitempty" example:"123"`
	Metadata    map[string]interface{}    `json:"metadata,omitempty"`
	CreatedAt   time.Time                 `json:"created_at"`
}

// TenantSubscriptionUsageResponse represents subscription usage metrics
type TenantSubscriptionUsageResponse struct {
	TenantID       uint                   `json:"tenant_id" example:"1"`
	SubscriptionID uint                   `json:"subscription_id" example:"1"`
	PlanID         uint                   `json:"plan_id" example:"2"`
	BillingPeriod  string                 `json:"billing_period" example:"2024-01"`
	Usage          map[string]interface{} `json:"usage"`
	Limits         map[string]interface{} `json:"limits"`
	Overages       map[string]interface{} `json:"overages,omitempty"`
	BillableAmount float64                `json:"billable_amount" example:"29.99"`
	OverageAmount  float64                `json:"overage_amount" example:"5.00"`
	TotalAmount    float64                `json:"total_amount" example:"34.99"`
}

// TenantSubscriptionFilter represents filter for listing subscriptions
type TenantSubscriptionFilter struct {
	Status        *models.SubscriptionStatus `json:"status,omitempty" example:"active"`
	PlanID        *uint                      `json:"plan_id,omitempty" example:"2"`
	BillingCycle  *string                    `json:"billing_cycle,omitempty" example:"monthly"`
	IsTrialing    *bool                      `json:"is_trialing,omitempty" example:"false"`
	IsCanceled    *bool                      `json:"is_canceled,omitempty" example:"false"`
	CreatedAfter  *time.Time                 `json:"created_after,omitempty"`
	CreatedBefore *time.Time                 `json:"created_before,omitempty"`
	Page          int                        `json:"page,omitempty" example:"1"`
	PageSize      int                        `json:"page_size,omitempty" example:"20"`
	SortBy        string                     `json:"sort_by,omitempty" example:"created_at"`
	SortOrder     string                     `json:"sort_order,omitempty" example:"desc"`
}

// TenantSubscriptionPreviewRequest represents request to preview subscription changes
type TenantSubscriptionPreviewRequest struct {
	NewPlanID       uint   `json:"new_plan_id" validate:"required" example:"3"`
	NewBillingCycle string `json:"new_billing_cycle" validate:"required,oneof=monthly yearly" example:"yearly"`
	ProrationMode   string `json:"proration_mode" validate:"omitempty,oneof=immediate next_cycle" example:"immediate"`
	CouponCode      string `json:"coupon_code,omitempty" example:"UPGRADE20"`
}

// TenantSubscriptionPreviewResponse represents preview of subscription changes
type TenantSubscriptionPreviewResponse struct {
	CurrentPlan          *TenantPlanResponse `json:"current_plan"`
	NewPlan              *TenantPlanResponse `json:"new_plan"`
	ChangeType           string              `json:"change_type" example:"upgrade"`
	EffectiveDate        time.Time           `json:"effective_date"`
	ProrationCredit      float64             `json:"proration_credit" example:"15.50"`
	UpgradeCharge        float64             `json:"upgrade_charge" example:"29.99"`
	NextBillingAmount    float64             `json:"next_billing_amount" example:"39.99"`
	NextBillingDate      time.Time           `json:"next_billing_date"`
	SavingsComparison    float64             `json:"savings_comparison,omitempty" example:"120.00"`
	RecommendedAction    string              `json:"recommended_action,omitempty" example:"Upgrade now to save 25% with yearly billing"`
}