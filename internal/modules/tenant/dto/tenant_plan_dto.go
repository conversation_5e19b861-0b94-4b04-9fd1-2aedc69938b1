package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

// TenantPlanCreateRequest represents the request to create a new tenant plan
type TenantPlanCreateRequest struct {
	Name              string                 `json:"name" validate:"required,min=3,max=100" example:"Professional Plan"`
	Slug              string                 `json:"slug" validate:"required,min=3,max=50,alphanum" example:"pro"`
	Description       string                 `json:"description" validate:"omitempty,max=500" example:"Perfect for growing businesses"`
	Price             float64                `json:"price" validate:"required,min=0" example:"29.99"`
	BillingPeriod     string                 `json:"billing_period" validate:"required,oneof=monthly yearly lifetime" example:"monthly"`
	TrialDays         int                    `json:"trial_days" validate:"min=0,max=365" example:"14"`
	Features          []string               `json:"features" validate:"omitempty" example:"feature1,feature2,feature3"`
	MaxUsers          int                    `json:"max_users" validate:"required,min=1" example:"10"`
	MaxProjects       int                    `json:"max_projects" validate:"required,min=1" example:"5"`
	MaxStorage        int64                  `json:"max_storage" validate:"required,min=0" example:"***********"`
	MaxAPICalls       int64                  `json:"max_api_calls" validate:"required,min=0" example:"10000"`
	CustomSettings    map[string]interface{} `json:"custom_settings" validate:"omitempty"`
	IsFeatured        bool                   `json:"is_featured" example:"false"`
	IsVisible         bool                   `json:"is_visible" example:"true"`
	DisplayOrder      int                    `json:"display_order" example:"1"`
}

// TenantPlanUpdateRequest represents the request to update a tenant plan
type TenantPlanUpdateRequest struct {
	Name              string                 `json:"name" validate:"omitempty,min=3,max=100" example:"Updated Professional Plan"`
	Description       string                 `json:"description" validate:"omitempty,max=500" example:"Updated description"`
	MonthlyPrice      *float64               `json:"monthly_price" validate:"omitempty,min=0" example:"24.99"`
	YearlyPrice       *float64               `json:"yearly_price" validate:"omitempty,min=0" example:"249.99"`
	TrialDays         *int                   `json:"trial_days" validate:"omitempty,min=0,max=365" example:"30"`
	Features          []string               `json:"features" validate:"omitempty" example:"feature1,feature2,feature4"`
	MaxUsers          *int                   `json:"max_users" validate:"omitempty,min=1" example:"20"`
	MaxProjects       *int                   `json:"max_projects" validate:"omitempty,min=1" example:"10"`
	MaxStorage        *int64                 `json:"max_storage" validate:"omitempty,min=0" example:"21474836480"`
	MaxAPICallsPerDay *int                   `json:"max_api_calls_per_day" validate:"omitempty,min=0" example:"20000"`
	CustomSettings    map[string]interface{} `json:"custom_settings" validate:"omitempty"`
	IsFeatured        *bool                  `json:"is_featured" example:"true"`
	IsVisible         *bool                  `json:"is_visible" example:"true"`
	DisplayOrder      *int                   `json:"display_order" example:"0"`
	Status            *models.PlanStatus     `json:"status" validate:"omitempty,oneof=active inactive deprecated" example:"active"`
}

// TenantPlanResponse represents the response for tenant plan operations
type TenantPlanResponse struct {
	ID                uint               `json:"id" example:"1"`
	Name              string             `json:"name" example:"Professional Plan"`
	Slug              string             `json:"slug" example:"pro"`
	Description       string             `json:"description" example:"Perfect for growing businesses"`
	MonthlyPrice      float64            `json:"monthly_price" example:"29.99"`
	YearlyPrice       float64            `json:"yearly_price" example:"299.99"`
	TrialDays         int                `json:"trial_days" example:"14"`
	MaxUsers          int                `json:"max_users" example:"10"`
	MaxProjects       int                `json:"max_projects" example:"5"`
	MaxStorage        int64              `json:"max_storage" example:"***********"`
	MaxAPICallsPerDay int                `json:"max_api_calls_per_day" example:"10000"`
	Features          []string           `json:"features" example:"feature1,feature2,feature3"`
	IsFeatured        bool               `json:"is_featured" example:"false"`
	IsVisible         bool               `json:"is_visible" example:"true"`
	DisplayOrder      int                `json:"display_order" example:"1"`
	Status            models.PlanStatus  `json:"status" example:"active"`
	TenantCount       int64              `json:"tenant_count,omitempty" example:"25"`
	CreatedAt         time.Time          `json:"created_at"`
	UpdatedAt         time.Time          `json:"updated_at"`
}

// TenantPlanListResponse represents the response for listing tenant plans
type TenantPlanListResponse struct {
	Plans      []TenantPlanResponse `json:"plans"`
	Total      int64                `json:"total" example:"10"`
	Page       int                  `json:"page" example:"1"`
	PageSize   int                  `json:"page_size" example:"20"`
	TotalPages int                  `json:"total_pages" example:"1"`
}

// TenantPlanFilter represents the filter for listing tenant plans
type TenantPlanFilter struct {
	Status     *models.PlanStatus `json:"status,omitempty" example:"active"`
	IsFeatured *bool              `json:"is_featured,omitempty" example:"true"`
	IsVisible  *bool              `json:"is_visible,omitempty" example:"true"`
	Page       int                `json:"page,omitempty" example:"1"`
	PageSize   int                `json:"page_size,omitempty" example:"20"`
	SortBy     string             `json:"sort_by,omitempty" example:"display_order"`
	SortOrder  string             `json:"sort_order,omitempty" example:"asc"`
}

// TenantPlanComparisonResponse represents plan comparison data
type TenantPlanComparisonResponse struct {
	Plans []struct {
		ID                uint     `json:"id" example:"1"`
		Name              string   `json:"name" example:"Basic"`
		MonthlyPrice      float64  `json:"monthly_price" example:"9.99"`
		YearlyPrice       float64  `json:"yearly_price" example:"99.99"`
		MaxUsers          int      `json:"max_users" example:"5"`
		MaxProjects       int      `json:"max_projects" example:"3"`
		MaxStorage        int64    `json:"max_storage" example:"5368709120"`
		MaxAPICallsPerDay int      `json:"max_api_calls_per_day" example:"5000"`
		Features          []string `json:"features"`
		IsFeatured        bool     `json:"is_featured" example:"false"`
		Popular           bool     `json:"popular" example:"false"`
	} `json:"plans"`
}

// PlanUpgradeRequest represents request to upgrade/downgrade plan
type PlanUpgradeRequest struct {
	NewPlanID     uint   `json:"new_plan_id" validate:"required" example:"2"`
	BillingCycle  string `json:"billing_cycle" validate:"required,oneof=monthly yearly" example:"monthly"`
	ProrationMode string `json:"proration_mode" validate:"omitempty,oneof=immediate next_cycle" example:"immediate"`
}

// PlanUpgradeResponse represents response for plan upgrade
type PlanUpgradeResponse struct {
	Success       bool                `json:"success" example:"true"`
	Message       string              `json:"message" example:"Plan upgraded successfully"`
	OldPlan       *TenantPlanResponse `json:"old_plan"`
	NewPlan       *TenantPlanResponse `json:"new_plan"`
	EffectiveDate time.Time           `json:"effective_date"`
	ProrationInfo *struct {
		CreditAmount   float64 `json:"credit_amount" example:"15.50"`
		ChargeAmount   float64 `json:"charge_amount" example:"29.99"`
		NextBillingDate time.Time `json:"next_billing_date"`
	} `json:"proration_info,omitempty"`
}