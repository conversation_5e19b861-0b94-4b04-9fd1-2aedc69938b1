package dto

import (
	"time"
)

// TenantSettingGetRequest represents request to get a specific setting
type TenantSettingGetRequest struct {
	Category string `json:"category" validate:"required" example:"general"`
	Key      string `json:"key" validate:"required" example:"site_name"`
}

// TenantSettingSetRequest represents request to set a setting value
type TenantSettingSetRequest struct {
	Category string      `json:"category" validate:"required" example:"general"`
	Key      string      `json:"key" validate:"required" example:"site_name"`
	Value    interface{} `json:"value" validate:"required" example:"My Company Site"`
}

// TenantSettingBulkUpdateRequest represents request for bulk setting updates
type TenantSettingBulkUpdateRequest struct {
	Settings []SettingUpdateItem `json:"settings" validate:"required,min=1"`
}

// SettingUpdateItem represents a single setting update item
type SettingUpdateItem struct {
	Category string      `json:"category" validate:"required" example:"general"`
	Key      string      `json:"key" validate:"required" example:"site_name"`
	Value    interface{} `json:"value" validate:"required" example:"Updated Site Name"`
}

// TenantSettingDeleteRequest represents request to delete a setting
type TenantSettingDeleteRequest struct {
	Category string `json:"category" validate:"required" example:"general"`
	Key      string `json:"key" validate:"required" example:"old_setting"`
}

// TenantSettingResponse represents a tenant setting
type TenantSettingResponse struct {
	ID          uint        `json:"id" example:"1"`
	TenantID    uint        `json:"tenant_id" example:"1"`
	Category    string      `json:"category" example:"general"`
	Key         string      `json:"key" example:"site_name"`
	Value       interface{} `json:"value" example:"My Company Site"`
	DataType    string      `json:"data_type" example:"string"`
	IsPublic    bool        `json:"is_public" example:"true"`
	Description string      `json:"description,omitempty" example:"The display name of the site"`
	CreatedAt   time.Time   `json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

// TenantSettingCategoryResponse represents settings grouped by category
type TenantSettingCategoryResponse struct {
	Category string                           `json:"category" example:"general"`
	Settings map[string]TenantSettingResponse `json:"settings"`
}

// TenantSettingListResponse represents response for listing tenant settings
type TenantSettingListResponse struct {
	Settings   []TenantSettingResponse `json:"settings"`
	Categories []string                `json:"categories" example:"general,security,notifications"`
	TenantID   uint                    `json:"tenant_id" example:"1"`
	Total      int64                   `json:"total" example:"25"`
}

// TenantPublicSettingsResponse represents public settings for a tenant
type TenantPublicSettingsResponse struct {
	TenantID   uint                              `json:"tenant_id" example:"1"`
	Categories map[string]map[string]interface{} `json:"categories"`
}

// TenantSettingValidationRequest represents request to validate a setting
type TenantSettingValidationRequest struct {
	Category string      `json:"category" validate:"required" example:"general"`
	Key      string      `json:"key" validate:"required" example:"max_users"`
	Value    interface{} `json:"value" validate:"required" example:"50"`
}

// TenantSettingValidationResponse represents response for setting validation
type TenantSettingValidationResponse struct {
	Valid   bool     `json:"valid" example:"true"`
	Errors  []string `json:"errors,omitempty"`
	Message string   `json:"message,omitempty" example:"Setting value is valid"`
}

// TenantSettingFilter represents filter for listing settings
type TenantSettingFilter struct {
	Category  string `json:"category,omitempty" example:"general"`
	IsPublic  *bool  `json:"is_public,omitempty" example:"true"`
	DataType  string `json:"data_type,omitempty" example:"string"`
	Search    string `json:"search,omitempty" example:"site"`
	Page      int    `json:"page,omitempty" example:"1"`
	PageSize  int    `json:"page_size,omitempty" example:"20"`
	SortBy    string `json:"sort_by,omitempty" example:"category"`
	SortOrder string `json:"sort_order,omitempty" example:"asc"`
}

// TenantSettingTemplate represents a setting template/schema
type TenantSettingTemplateResponse struct {
	Category         string                 `json:"category" example:"general"`
	Key              string                 `json:"key" example:"site_name"`
	Name             string                 `json:"name" example:"Site Name"`
	Description      string                 `json:"description" example:"The display name of your site"`
	DataType         string                 `json:"data_type" example:"string"`
	DefaultValue     interface{}            `json:"default_value" example:"My Site"`
	IsRequired       bool                   `json:"is_required" example:"true"`
	IsPublic         bool                   `json:"is_public" example:"true"`
	ValidationRules  map[string]interface{} `json:"validation_rules,omitempty"`
	PossibleValues   []interface{}          `json:"possible_values,omitempty"`
	DependsOn        []string               `json:"depends_on,omitempty"`
	AffectedFeatures []string               `json:"affected_features,omitempty"`
}

// TenantSettingBackupRequest represents request to backup tenant settings
type TenantSettingBackupRequest struct {
	Categories     []string `json:"categories,omitempty" example:"general,security"`
	IncludeValues  bool     `json:"include_values" example:"true"`
	BackupName     string   `json:"backup_name,omitempty" example:"pre-migration-backup"`
	BackupLocation string   `json:"backup_location,omitempty" example:"s3://backups/tenant-1/"`
}

// TenantSettingRestoreRequest represents request to restore tenant settings
type TenantSettingRestoreRequest struct {
	BackupID       string   `json:"backup_id" validate:"required" example:"backup-123456"`
	Categories     []string `json:"categories,omitempty" example:"general,security"`
	OverwriteMode  string   `json:"overwrite_mode" validate:"omitempty,oneof=merge replace skip" example:"merge"`
	DryRun         bool     `json:"dry_run" example:"false"`
}

// TenantSettingBackupResponse represents response for setting backup operations
type TenantSettingBackupResponse struct {
	BackupID       string    `json:"backup_id" example:"backup-123456"`
	TenantID       uint      `json:"tenant_id" example:"1"`
	BackupName     string    `json:"backup_name" example:"pre-migration-backup"`
	Categories     []string  `json:"categories" example:"general,security"`
	SettingsCount  int       `json:"settings_count" example:"25"`
	BackupLocation string    `json:"backup_location" example:"s3://backups/tenant-1/backup-123456.json"`
	CreatedAt      time.Time `json:"created_at"`
	ExpiresAt      time.Time `json:"expires_at"`
}