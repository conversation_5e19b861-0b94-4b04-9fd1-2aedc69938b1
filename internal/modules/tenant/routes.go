package tenant

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	settingServices "github.com/tranthanhloi/wn-api-v3/internal/modules/settings/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all tenant module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, validator validator.Validator) {
	// Initialize repositories
	tenantRepo := repositories.NewMySQLTenantRepository(db)
	planRepo := repositories.NewMySQLTenantPlanRepository(db)
	settingRepo := repositories.NewMySQLTenantSettingRepository(db)
	featureRepo := repositories.NewMySQLTenantFeatureRepository(db)
	catalogRepo := repositories.NewMySQLFeatureCatalogRepository(db)
	subscriptionRepo := repositories.NewMySQLSubscriptionRepository(db)

	// Initialize encryption service
	encryptionService, err := settingServices.NewEncryptionService()
	if err != nil {
		panic("Failed to initialize encryption service: " + err.Error())
	}

	// Initialize services
	tenantService := services.NewTenantService(tenantRepo, planRepo)
	planService := services.NewTenantPlanService(planRepo, tenantRepo)
	settingService := services.NewTenantSettingService(settingRepo, tenantRepo, encryptionService)
	featureService := services.NewTenantFeatureService(featureRepo, catalogRepo, tenantRepo, planRepo)
	catalogService := services.NewFeatureCatalogService(catalogRepo, featureRepo, tenantRepo)
	subscriptionService := services.NewSubscriptionService(subscriptionRepo, tenantRepo, planRepo)

	// Initialize handlers
	tenantHandler := handlers.NewTenantHandler(tenantService, validator)
	planHandler := handlers.NewPlanHandler(planService, validator)
	settingsHandler := handlers.NewSettingsHandler(settingService, validator)
	featureHandler := handlers.NewFeatureHandler(featureService, catalogService, validator)
	subscriptionHandler := handlers.NewSubscriptionHandler(subscriptionService, validator)

	// Tenant routes with rate limiting
	tenantRoutes := router.Group("/tenants")
	tenantRoutes.Use(middleware.RateLimitByTenantMiddleware(100, 200)) // 100 requests per minute per tenant
	
	tenantRoutes.POST("", tenantHandler.CreateTenant)
	tenantRoutes.GET("", tenantHandler.ListTenants)
	tenantRoutes.GET("/:id", tenantHandler.GetTenant)
	tenantRoutes.PUT("/:id", tenantHandler.UpdateTenant)
	tenantRoutes.DELETE("/:id", tenantHandler.DeleteTenant)
	tenantRoutes.PATCH("/:id/status", tenantHandler.UpdateTenantStatus)
	tenantRoutes.POST("/:id/assign-plan", tenantHandler.AssignPlan)
	tenantRoutes.GET("/:id/stats", tenantHandler.GetTenantStats)
	tenantRoutes.GET("/domain/:domain", tenantHandler.GetTenantByDomain)

	// Tenant Plan routes
	router.POST("/tenant-plans", planHandler.CreatePlan)
	router.GET("/tenant-plans", planHandler.ListPlans)
	router.GET("/tenant-plans/active", planHandler.GetActivePlans)
	router.GET("/tenant-plans/:id", planHandler.GetPlan)
	router.PUT("/tenant-plans/:id", planHandler.UpdatePlan)
	router.DELETE("/tenant-plans/:id", planHandler.DeletePlan)
	router.GET("/tenant-plans/:id/tenant-count", planHandler.GetPlanTenantCount)
	router.GET("/tenant-plans/slug/:slug", planHandler.GetPlanBySlug)

	// Tenant Settings routes - Apply tenant isolation middleware
	tenantSettings := router.Group("/tenants/:id/settings")
	tenantSettings.Use(middleware.TenantIsolationMiddleware(tenantService))
	tenantSettings.Use(middleware.PreventCrossTenantAccess())
	tenantSettings.Use(middleware.MultiTenantDatabaseScope())
	tenantSettings.POST("", settingsHandler.SetSetting)
	tenantSettings.GET("/:key", settingsHandler.GetSetting)
	tenantSettings.DELETE("/:key", settingsHandler.DeleteSetting)
	tenantSettings.GET("/category/:category", settingsHandler.GetSettingsByCategory)
	tenantSettings.PUT("/bulk", settingsHandler.BulkUpdateSettings)
	tenantSettings.GET("/public", settingsHandler.GetPublicSettings)
	tenantSettings.POST("/validate", settingsHandler.ValidateSetting)

	// Tenant Feature routes - Apply tenant isolation middleware
	tenantFeatures := router.Group("/tenants/:id/features")
	tenantFeatures.Use(middleware.TenantIsolationMiddleware(tenantService))
	tenantFeatures.Use(middleware.PreventCrossTenantAccess())
	tenantFeatures.Use(middleware.MultiTenantDatabaseScope())
	tenantFeatures.POST("/:featureKey/enable", featureHandler.EnableFeature)
	tenantFeatures.POST("/:featureKey/disable", featureHandler.DisableFeature)
	tenantFeatures.POST("/:featureKey/toggle", featureHandler.ToggleFeature)
	tenantFeatures.GET("/:featureKey/enabled", featureHandler.IsFeatureEnabled)
	tenantFeatures.PUT("/:featureKey/config", featureHandler.UpdateFeatureConfiguration)
	tenantFeatures.GET("/:featureKey/usage", featureHandler.GetFeatureUsage)
	tenantFeatures.GET("/enabled", featureHandler.GetEnabledFeatures)
	tenantFeatures.POST("/bulk-toggle", featureHandler.BulkToggleFeatures)

	// Feature Catalog routes
	router.POST("/feature-catalog", featureHandler.CreateFeatureCatalog)
	router.GET("/feature-catalog", featureHandler.ListFeatureCatalog)
	router.GET("/feature-catalog/:key", featureHandler.GetFeatureCatalog)
	router.PUT("/feature-catalog/:key", featureHandler.UpdateFeatureCatalog)
	router.POST("/feature-catalog/:key/deprecate", featureHandler.DeprecateFeatureCatalog)
	router.GET("/feature-catalog/:key/adoption", featureHandler.GetFeatureAdoption)
	router.GET("/feature-catalog/plan/:planId", featureHandler.GetFeaturesForPlan)

	// Subscription routes
	router.POST("/subscriptions", subscriptionHandler.CreateSubscription)
	
	// Tenant-scoped subscription routes
	tenantSubscriptions := router.Group("/tenants/:id")
	tenantSubscriptions.Use(middleware.TenantIsolationMiddleware(tenantService))
	tenantSubscriptions.Use(middleware.PreventCrossTenantAccess())
	tenantSubscriptions.Use(middleware.MultiTenantDatabaseScope())
	tenantSubscriptions.GET("/subscription", subscriptionHandler.GetSubscriptionByTenant)
	
	// Subscription management routes (require tenant context)
	subscriptionRoutes := router.Group("/subscriptions/:id")
	subscriptionRoutes.Use(middleware.TenantResourceOwnershipMiddleware("subscription"))
	subscriptionRoutes.POST("/upgrade", subscriptionHandler.UpgradePlan)
	subscriptionRoutes.POST("/downgrade", subscriptionHandler.DowngradePlan)
	subscriptionRoutes.POST("/cancel", subscriptionHandler.CancelSubscription)
	subscriptionRoutes.POST("/payment-failure", subscriptionHandler.ProcessPaymentFailure)
	subscriptionRoutes.POST("/process-trial-expiration", subscriptionHandler.ProcessTrialExpiration)
	
	// Admin subscription management routes
	router.GET("/admin/subscriptions/expired-trials", subscriptionHandler.GetExpiredTrials)
	router.POST("/admin/subscriptions/process-pending-transitions", subscriptionHandler.ProcessPendingTransitions)
}
