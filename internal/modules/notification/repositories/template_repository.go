package repositories

import (
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type TemplateRepository interface {
	Create(template *models.NotificationTemplate) error
	GetByID(tenantID, id uint) (*models.NotificationTemplate, error)
	GetByCode(tenantID uint, code string) (*models.NotificationTemplate, error)
	GetWithVersions(tenantID, id uint) (*models.NotificationTemplate, error)
	List(tenantID uint, filters models.TemplateFilters) ([]models.NotificationTemplate, int64, error)
	Update(template *models.NotificationTemplate) error
	Delete(tenantID, id uint) error
	SetActiveVersion(tenantID, templateID, versionID uint) error
	
	// Template Version methods
	CreateVersion(version *models.NotificationTemplateVersion) error
	GetVersionByID(tenantID, id uint) (*models.NotificationTemplateVersion, error)
	GetVersions(tenantID, templateID uint) ([]models.NotificationTemplateVersion, error)
	GetActiveVersion(tenantID, templateID uint) (*models.NotificationTemplateVersion, error)
	GetVersionByLanguage(tenantID, templateID uint, language string) (*models.NotificationTemplateVersion, error)
	UpdateVersion(version *models.NotificationTemplateVersion) error
	DeleteVersion(tenantID, id uint) error
	ActivateVersion(tenantID, templateID, versionID uint) error
}

type templateRepository struct {
	db *gorm.DB
}

func NewTemplateRepository(db *gorm.DB) TemplateRepository {
	return &templateRepository{db: db}
}

// Template methods
func (r *templateRepository) Create(template *models.NotificationTemplate) error {
	return r.db.Create(template).Error
}

func (r *templateRepository) GetByID(tenantID, id uint) (*models.NotificationTemplate, error) {
	var template models.NotificationTemplate
	err := r.db.Where("tenant_id = ? AND id = ?", tenantID, id).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *templateRepository) GetByCode(tenantID uint, code string) (*models.NotificationTemplate, error) {
	var template models.NotificationTemplate
	err := r.db.Where("tenant_id = ? AND code = ?", tenantID, code).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *templateRepository) GetWithVersions(tenantID, id uint) (*models.NotificationTemplate, error) {
	var template models.NotificationTemplate
	err := r.db.
		Preload("Versions", func(db *gorm.DB) *gorm.DB {
			return db.Order("version_number DESC")
		}).
		Preload("ActiveVersion").
		Where("tenant_id = ? AND id = ?", tenantID, id).
		First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *templateRepository) List(tenantID uint, filters models.TemplateFilters) ([]models.NotificationTemplate, int64, error) {
	query := r.db.Where("tenant_id = ?", tenantID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Channel != "" {
		query = query.Where("channel = ?", filters.Channel)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		searchPattern := "%" + filters.Search + "%"
		query = query.Where("(name LIKE ? OR code LIKE ? OR description LIKE ?)", 
			searchPattern, searchPattern, searchPattern)
	}

	// Count total
	var total int64
	if err := query.Model(&models.NotificationTemplate{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	offset := (filters.Page - 1) * filters.Limit
	orderBy := fmt.Sprintf("%s %s", filters.SortBy, filters.SortOrder)
	
	var templates []models.NotificationTemplate
	err := query.
		Preload("ActiveVersion").
		Offset(offset).
		Limit(filters.Limit).
		Order(orderBy).
		Find(&templates).Error

	return templates, total, err
}

func (r *templateRepository) Update(template *models.NotificationTemplate) error {
	return r.db.Save(template).Error
}

func (r *templateRepository) Delete(tenantID, id uint) error {
	// Delete template versions first
	if err := r.db.Where("tenant_id = ? AND template_id = ?", tenantID, id).
		Delete(&models.NotificationTemplateVersion{}).Error; err != nil {
		return err
	}

	// Delete template
	return r.db.Where("tenant_id = ? AND id = ?", tenantID, id).
		Delete(&models.NotificationTemplate{}).Error
}

func (r *templateRepository) SetActiveVersion(tenantID, templateID, versionID uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Deactivate all versions for this template
		if err := tx.Model(&models.NotificationTemplateVersion{}).
			Where("tenant_id = ? AND template_id = ?", tenantID, templateID).
			Update("is_active", false).Error; err != nil {
			return err
		}

		// Activate the specified version
		if err := tx.Model(&models.NotificationTemplateVersion{}).
			Where("tenant_id = ? AND id = ?", tenantID, versionID).
			Update("is_active", true).Error; err != nil {
			return err
		}

		// Update template's active_version_id
		return tx.Model(&models.NotificationTemplate{}).
			Where("tenant_id = ? AND id = ?", tenantID, templateID).
			Update("active_version_id", versionID).Error
	})
}

// Template Version methods
func (r *templateRepository) CreateVersion(version *models.NotificationTemplateVersion) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Create the version
		if err := tx.Create(version).Error; err != nil {
			return err
		}

		// Increment template version count
		return tx.Model(&models.NotificationTemplate{}).
			Where("tenant_id = ? AND id = ?", version.TenantID, version.TemplateID).
			UpdateColumn("version_count", gorm.Expr("version_count + 1")).Error
	})
}

func (r *templateRepository) GetVersionByID(tenantID, id uint) (*models.NotificationTemplateVersion, error) {
	var version models.NotificationTemplateVersion
	err := r.db.Where("tenant_id = ? AND id = ?", tenantID, id).First(&version).Error
	if err != nil {
		return nil, err
	}
	return &version, nil
}

func (r *templateRepository) GetVersions(tenantID, templateID uint) ([]models.NotificationTemplateVersion, error) {
	var versions []models.NotificationTemplateVersion
	err := r.db.
		Where("tenant_id = ? AND template_id = ?", tenantID, templateID).
		Order("version_number DESC").
		Find(&versions).Error
	return versions, err
}

func (r *templateRepository) GetActiveVersion(tenantID, templateID uint) (*models.NotificationTemplateVersion, error) {
	var version models.NotificationTemplateVersion
	
	// Log the query parameters for debugging
	logrus.WithFields(logrus.Fields{
		"tenant_id":   tenantID,
		"template_id": templateID,
		"is_active":   true,
	}).Debug("Searching for active template version")
	
	err := r.db.
		Where("tenant_id = ? AND template_id = ? AND is_active = ?", tenantID, templateID, 1).
		First(&version).Error
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"tenant_id":   tenantID,
			"template_id": templateID,
			"error":       err.Error(),
		}).Error("Failed to find active template version")
		return nil, err
	}
	
	logrus.WithFields(logrus.Fields{
		"tenant_id":   tenantID,
		"template_id": templateID,
		"version_id":  version.ID,
		"language":    version.Language,
	}).Debug("Found active template version")
	
	return &version, nil
}

func (r *templateRepository) GetVersionByLanguage(tenantID, templateID uint, language string) (*models.NotificationTemplateVersion, error) {
	var version models.NotificationTemplateVersion
	err := r.db.
		Where("tenant_id = ? AND template_id = ? AND language = ? AND is_active = ?", 
			tenantID, templateID, language, 1).
		First(&version).Error
	if err != nil {
		return nil, err
	}
	return &version, nil
}

func (r *templateRepository) UpdateVersion(version *models.NotificationTemplateVersion) error {
	return r.db.Save(version).Error
}

func (r *templateRepository) DeleteVersion(tenantID, id uint) error {
	return r.db.Where("tenant_id = ? AND id = ?", tenantID, id).
		Delete(&models.NotificationTemplateVersion{}).Error
}

func (r *templateRepository) ActivateVersion(tenantID, templateID, versionID uint) error {
	return r.SetActiveVersion(tenantID, templateID, versionID)
}