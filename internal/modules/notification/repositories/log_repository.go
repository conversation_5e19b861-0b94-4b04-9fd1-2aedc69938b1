package repositories

import (
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"gorm.io/gorm"
)

type LogRepository interface {
	Create(log *models.NotificationLog) error
	CreateBatch(logs []models.NotificationLog) error
	GetByID(tenantID, id uint) (*models.NotificationLog, error)
	List(tenantID uint, filters models.LogFilters) ([]models.NotificationLog, int64, error)
	GetByNotificationID(tenantID, notificationID uint) ([]models.NotificationLog, error)
	GetByRecipientID(tenantID, recipientID uint) ([]models.NotificationLog, error)
	GetByTrackingID(trackingID string) (*models.NotificationLog, error)
	Delete(tenantID, id uint) error
	DeleteOlderThan(days int) error
}

type logRepository struct {
	db *gorm.DB
}

func NewLogRepository(db *gorm.DB) LogRepository {
	return &logRepository{db: db}
}

func (r *logRepository) Create(log *models.NotificationLog) error {
	return r.db.Create(log).Error
}

func (r *logRepository) CreateBatch(logs []models.NotificationLog) error {
	if len(logs) == 0 {
		return nil
	}
	return r.db.CreateInBatches(logs, 100).Error
}

func (r *logRepository) GetByID(tenantID, id uint) (*models.NotificationLog, error) {
	var log models.NotificationLog
	err := r.db.Where("tenant_id = ? AND id = ?", tenantID, id).First(&log).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

func (r *logRepository) List(tenantID uint, filters models.LogFilters) ([]models.NotificationLog, int64, error) {
	query := r.db.Where("tenant_id = ?", tenantID)

	// Apply filters
	if filters.NotificationID != 0 {
		query = query.Where("notification_id = ?", filters.NotificationID)
	}
	if filters.RecipientID != nil {
		query = query.Where("recipient_id = ?", *filters.RecipientID)
	}
	if filters.EventType != "" {
		query = query.Where("event_type = ?", filters.EventType)
	}
	if filters.TrackingID != "" {
		query = query.Where("tracking_id = ?", filters.TrackingID)
	}
	if filters.ExternalID != "" {
		query = query.Where("external_id = ?", filters.ExternalID)
	}
	if filters.IPAddress != "" {
		query = query.Where("ip_address = ?", filters.IPAddress)
	}
	if filters.DateFrom != nil {
		query = query.Where("occurred_at >= ?", filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("occurred_at <= ?", filters.DateTo)
	}

	// Count total
	var total int64
	if err := query.Model(&models.NotificationLog{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	offset := (filters.Page - 1) * filters.Limit
	orderBy := fmt.Sprintf("%s %s", filters.SortBy, filters.SortOrder)
	
	var logs []models.NotificationLog
	err := query.
		Preload("Notification").
		Preload("Recipient").
		Offset(offset).
		Limit(filters.Limit).
		Order(orderBy).
		Find(&logs).Error

	return logs, total, err
}

func (r *logRepository) GetByNotificationID(tenantID, notificationID uint) ([]models.NotificationLog, error) {
	var logs []models.NotificationLog
	err := r.db.
		Where("tenant_id = ? AND notification_id = ?", tenantID, notificationID).
		Order("occurred_at ASC").
		Find(&logs).Error
	return logs, err
}

func (r *logRepository) GetByRecipientID(tenantID, recipientID uint) ([]models.NotificationLog, error) {
	var logs []models.NotificationLog
	err := r.db.
		Where("tenant_id = ? AND recipient_id = ?", tenantID, recipientID).
		Order("occurred_at ASC").
		Find(&logs).Error
	return logs, err
}

func (r *logRepository) GetByTrackingID(trackingID string) (*models.NotificationLog, error) {
	var log models.NotificationLog
	err := r.db.Where("tracking_id = ?", trackingID).First(&log).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

func (r *logRepository) Delete(tenantID, id uint) error {
	return r.db.Where("tenant_id = ? AND id = ?", tenantID, id).
		Delete(&models.NotificationLog{}).Error
}

func (r *logRepository) DeleteOlderThan(days int) error {
	return r.db.Where("created_at < DATE_SUB(NOW(), INTERVAL ? DAY)", days).
		Delete(&models.NotificationLog{}).Error
}