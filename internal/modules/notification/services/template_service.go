package services

import (
	"bytes"
	"errors"
	"fmt"
	"html/template"
	"strings"
	texttemplate "text/template"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
)

type TemplateService interface {
	CreateTemplate(tenantID uint, req models.CreateTemplateRequest) (*models.NotificationTemplate, error)
	GetTemplate(tenantID, id uint) (*models.NotificationTemplate, error)
	GetTemplateWithVersions(tenantID, id uint) (*models.NotificationTemplate, error)
	GetTemplateByCode(tenantID uint, code string) (*models.NotificationTemplate, error)
	ListTemplates(tenantID uint, filters models.TemplateFilters) ([]models.NotificationTemplate, int64, error)
	UpdateTemplate(tenantID, id uint, req models.UpdateTemplateRequest) (*models.NotificationTemplate, error)
	DeleteTemplate(tenantID, id uint) error
	ActivateTemplate(tenantID, id uint) error
	DeactivateTemplate(tenantID, id uint) error

	// Template Version methods
	CreateTemplateVersion(tenantID, templateID uint, req models.CreateTemplateVersionRequest) (*models.NotificationTemplateVersion, error)
	GetTemplateVersion(tenantID, id uint) (*models.NotificationTemplateVersion, error)
	ListTemplateVersions(tenantID, templateID uint) ([]models.NotificationTemplateVersion, error)
	UpdateTemplateVersion(tenantID, id uint, req models.UpdateTemplateVersionRequest) (*models.NotificationTemplateVersion, error)
	DeleteTemplateVersion(tenantID, id uint) error
	ActivateTemplateVersion(tenantID, templateID, versionID uint) error

	// Template rendering
	RenderTemplate(version *models.NotificationTemplateVersion, data map[string]interface{}) (string, error)
	PreviewTemplate(tenantID, versionID uint, data map[string]interface{}) (string, error)
	ValidateTemplate(content string, variables []string) error
}

type templateService struct {
	templateRepo repositories.TemplateRepository
}

func NewTemplateService(templateRepo repositories.TemplateRepository) TemplateService {
	return &templateService{
		templateRepo: templateRepo,
	}
}

func (s *templateService) CreateTemplate(tenantID uint, req models.CreateTemplateRequest) (*models.NotificationTemplate, error) {
	// Check if code already exists
	existing, err := s.templateRepo.GetByCode(tenantID, req.Code)
	if err == nil && existing != nil {
		return nil, errors.New("template code already exists")
	}

	template := &models.NotificationTemplate{
		TenantID:     tenantID,
		Code:         req.Code,
		Name:         req.Name,
		Type:         req.Type,
		Channel:      req.Channel,
		Description:  req.Description,
		IsActive:     false, // Start inactive
		VersionCount: 0,
	}

	if req.Variables != nil {
		if err := template.SetVariables(req.Variables); err != nil {
			return nil, err
		}
	}

	if err := s.templateRepo.Create(template); err != nil {
		return nil, err
	}

	return template, nil
}

func (s *templateService) GetTemplate(tenantID, id uint) (*models.NotificationTemplate, error) {
	return s.templateRepo.GetByID(tenantID, id)
}

func (s *templateService) GetTemplateWithVersions(tenantID, id uint) (*models.NotificationTemplate, error) {
	return s.templateRepo.GetWithVersions(tenantID, id)
}

func (s *templateService) GetTemplateByCode(tenantID uint, code string) (*models.NotificationTemplate, error) {
	return s.templateRepo.GetByCode(tenantID, code)
}

func (s *templateService) ListTemplates(tenantID uint, filters models.TemplateFilters) ([]models.NotificationTemplate, int64, error) {
	return s.templateRepo.List(tenantID, filters)
}

func (s *templateService) UpdateTemplate(tenantID, id uint, req models.UpdateTemplateRequest) (*models.NotificationTemplate, error) {
	template, err := s.templateRepo.GetByID(tenantID, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Name != nil {
		template.Name = *req.Name
	}
	if req.Type != nil {
		template.Type = *req.Type
	}
	if req.Description != nil {
		template.Description = req.Description
	}
	if req.Variables != nil {
		if err := template.SetVariables(req.Variables); err != nil {
			return nil, err
		}
	}
	if req.IsActive != nil {
		template.IsActive = *req.IsActive
	}

	if err := s.templateRepo.Update(template); err != nil {
		return nil, err
	}

	return template, nil
}

func (s *templateService) DeleteTemplate(tenantID, id uint) error {
	return s.templateRepo.Delete(tenantID, id)
}

func (s *templateService) ActivateTemplate(tenantID, id uint) error {
	template, err := s.templateRepo.GetByID(tenantID, id)
	if err != nil {
		return err
	}

	template.Activate()
	return s.templateRepo.Update(template)
}

func (s *templateService) DeactivateTemplate(tenantID, id uint) error {
	template, err := s.templateRepo.GetByID(tenantID, id)
	if err != nil {
		return err
	}

	template.Deactivate()
	return s.templateRepo.Update(template)
}

// Template Version methods
func (s *templateService) CreateTemplateVersion(tenantID, templateID uint, req models.CreateTemplateVersionRequest) (*models.NotificationTemplateVersion, error) {
	// Get template to validate and increment version count
	template, err := s.templateRepo.GetByID(tenantID, templateID)
	if err != nil {
		return nil, err
	}

	// Validate template content
	if err := s.ValidateTemplate(req.BodyHTML, req.Variables); err != nil {
		return nil, err
	}

	// Create version
	version := &models.NotificationTemplateVersion{
		TenantID:      tenantID,
		TemplateID:    templateID,
		VersionNumber: template.VersionCount + 1,
		Language:      req.Language,
		Subject:       req.Subject,
		BodyHTML:      req.BodyHTML,
		BodyText:      req.BodyText,
		IsActive:      false, // Start inactive
		IsApproved:    false, // Needs approval
	}

	if req.Variables != nil {
		if err := version.SetVariables(req.Variables); err != nil {
			return nil, err
		}
	}

	if err := s.templateRepo.CreateVersion(version); err != nil {
		return nil, err
	}

	return version, nil
}

func (s *templateService) GetTemplateVersion(tenantID, id uint) (*models.NotificationTemplateVersion, error) {
	return s.templateRepo.GetVersionByID(tenantID, id)
}

func (s *templateService) ListTemplateVersions(tenantID, templateID uint) ([]models.NotificationTemplateVersion, error) {
	return s.templateRepo.GetVersions(tenantID, templateID)
}

func (s *templateService) UpdateTemplateVersion(tenantID, id uint, req models.UpdateTemplateVersionRequest) (*models.NotificationTemplateVersion, error) {
	version, err := s.templateRepo.GetVersionByID(tenantID, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Subject != nil {
		version.Subject = *req.Subject
	}
	if req.BodyHTML != nil {
		version.BodyHTML = *req.BodyHTML
	}
	if req.BodyText != nil {
		version.BodyText = req.BodyText
	}
	if req.Variables != nil {
		if err := version.SetVariables(req.Variables); err != nil {
			return nil, err
		}
	}
	if req.IsActive != nil {
		version.IsActive = *req.IsActive
	}

	// Validate if content was updated
	if req.BodyHTML != nil {
		variables := req.Variables
		if variables == nil {
			if existingVars, err := version.GetVariablesList(); err == nil {
				variables = existingVars
			}
		}
		if err := s.ValidateTemplate(*req.BodyHTML, variables); err != nil {
			return nil, err
		}
	}

	if err := s.templateRepo.UpdateVersion(version); err != nil {
		return nil, err
	}

	return version, nil
}

func (s *templateService) DeleteTemplateVersion(tenantID, id uint) error {
	return s.templateRepo.DeleteVersion(tenantID, id)
}

func (s *templateService) ActivateTemplateVersion(tenantID, templateID, versionID uint) error {
	return s.templateRepo.ActivateVersion(tenantID, templateID, versionID)
}

// Template rendering using Go's template packages
func (s *templateService) RenderTemplate(version *models.NotificationTemplateVersion, data map[string]interface{}) (string, error) {
	// Add common variables
	if data == nil {
		data = make(map[string]interface{})
	}
	data["current_year"] = time.Now().Year()
	
	// Determine if this is HTML or text template based on content
	isHTML := s.isHTMLTemplate(version.BodyHTML)
	
	if isHTML {
		return s.renderHTMLTemplate(version.BodyHTML, data)
	} else {
		return s.renderTextTemplate(version.BodyHTML, data)
	}
}

// renderHTMLTemplate uses html/template for HTML content with XSS protection
func (s *templateService) renderHTMLTemplate(templateContent string, data map[string]interface{}) (string, error) {
	// Create HTML template with custom functions
	tmpl, err := template.New("email").Funcs(s.getTemplateFunctions()).Parse(templateContent)
	if err != nil {
		return "", fmt.Errorf("failed to parse HTML template: %w", err)
	}
	
	// Execute template
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute HTML template: %w", err)
	}
	
	return buf.String(), nil
}

// renderTextTemplate uses text/template for plain text content
func (s *templateService) renderTextTemplate(templateContent string, data map[string]interface{}) (string, error) {
	// Create text template with custom functions
	tmpl, err := texttemplate.New("email").Funcs(s.getTextTemplateFunctions()).Parse(templateContent)
	if err != nil {
		return "", fmt.Errorf("failed to parse text template: %w", err)
	}
	
	// Execute template
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute text template: %w", err)
	}
	
	return buf.String(), nil
}

// isHTMLTemplate checks if template content contains HTML tags
func (s *templateService) isHTMLTemplate(content string) bool {
	htmlIndicators := []string{"<html", "<body", "<div", "<p>", "<h1", "<h2", "<h3", "<a ", "<br", "<img", "<!DOCTYPE"}
	lowerContent := strings.ToLower(content)
	
	for _, indicator := range htmlIndicators {
		if strings.Contains(lowerContent, indicator) {
			return true
		}
	}
	return false
}

// getTemplateFunctions returns custom functions for HTML templates
func (s *templateService) getTemplateFunctions() template.FuncMap {
	return template.FuncMap{
		// Date formatting functions
		"formatDate": func(t time.Time, layout string) string {
			return t.Format(layout)
		},
		"now": func() time.Time {
			return time.Now()
		},
		"currentYear": func() int {
			return time.Now().Year()
		},
		
		// String manipulation functions
		"upper":     strings.ToUpper,
		"lower":     strings.ToLower,
		"title":     strings.Title,
		"trimSpace": strings.TrimSpace,
		
		// Utility functions
		"default": func(defaultValue interface{}, value interface{}) interface{} {
			if value == nil || value == "" {
				return defaultValue
			}
			return value
		},
		"safe": func(s string) template.HTML {
			return template.HTML(s)
		},
		"url": func(s string) template.URL {
			return template.URL(s)
		},
		
		// Conditional functions
		"eq": func(a, b interface{}) bool { return a == b },
		"ne": func(a, b interface{}) bool { return a != b },
		"lt": func(a, b int) bool { return a < b },
		"le": func(a, b int) bool { return a <= b },
		"gt": func(a, b int) bool { return a > b },
		"ge": func(a, b int) bool { return a >= b },
	}
}

// getTextTemplateFunctions returns custom functions for text templates
func (s *templateService) getTextTemplateFunctions() texttemplate.FuncMap {
	return texttemplate.FuncMap{
		// Date formatting functions
		"formatDate": func(t time.Time, layout string) string {
			return t.Format(layout)
		},
		"now": func() time.Time {
			return time.Now()
		},
		"currentYear": func() int {
			return time.Now().Year()
		},
		
		// String manipulation functions
		"upper":     strings.ToUpper,
		"lower":     strings.ToLower,
		"title":     strings.Title,
		"trimSpace": strings.TrimSpace,
		
		// Utility functions
		"default": func(defaultValue interface{}, value interface{}) interface{} {
			if value == nil || value == "" {
				return defaultValue
			}
			return value
		},
		
		// Conditional functions
		"eq": func(a, b interface{}) bool { return a == b },
		"ne": func(a, b interface{}) bool { return a != b },
		"lt": func(a, b int) bool { return a < b },
		"le": func(a, b int) bool { return a <= b },
		"gt": func(a, b int) bool { return a > b },
		"ge": func(a, b int) bool { return a >= b },
	}
}

func (s *templateService) PreviewTemplate(tenantID, versionID uint, data map[string]interface{}) (string, error) {
	version, err := s.templateRepo.GetVersionByID(tenantID, versionID)
	if err != nil {
		return "", err
	}

	return s.RenderTemplate(version, data)
}

func (s *templateService) ValidateTemplate(content string, variables []string) error {
	// Basic validation - check for required variables
	requiredVars := []string{"unsubscribe_url"}
	
	for _, reqVar := range requiredVars {
		placeholder := "{{" + reqVar + "}}"
		if !strings.Contains(content, placeholder) {
			return errors.New("missing required variable: " + reqVar)
		}
	}
	
	// Check that declared variables exist in content
	for _, variable := range variables {
		placeholder := "{{" + variable + "}}"
		if !strings.Contains(content, placeholder) {
			// Warning: declared variable not used (not an error)
		}
	}
	
	// TODO: Add more sophisticated validation
	// - HTML validation
	// - CSS validation
	// - Security checks (XSS prevention)
	// - Template syntax validation
	
	return nil
}