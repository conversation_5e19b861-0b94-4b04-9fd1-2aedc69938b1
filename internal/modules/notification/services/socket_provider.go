package services

import (
	"encoding/json"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SocketProvider interface for different Socket.IO delivery providers
type SocketProvider interface {
	SendNotification(recipient *models.NotificationRecipient, notification *models.Notification, content string, metadata map[string]interface{}) error
	GetProviderName() string
}

// SocketIOProvider implements SocketProvider for Socket.IO real-time notifications
type SocketIOProvider struct {
	ServerURL   string
	Namespace   string
	logger      utils.Logger
}

// NewSocketIOProvider creates a new Socket.IO notification provider
func NewSocketIOProvider(serverURL, namespace string, logger utils.Logger) SocketProvider {
	return &SocketIOProvider{
		ServerURL: serverURL,
		Namespace: namespace,
		logger:    logger,
	}
}

func (p *SocketIOProvider) SendNotification(recipient *models.NotificationRecipient, notification *models.Notification, content string, metadata map[string]interface{}) error {
	// Create notification payload for Socket.IO
	payload := map[string]interface{}{
		"id":           notification.ID,
		"type":         notification.Type,
		"title":        notification.Subject,
		"content":      content,
		"priority":     notification.Priority,
		"timestamp":    time.Now().Unix(),
		"metadata":     metadata,
		"read":         false,
		"tenant_id":    notification.TenantID,
	}

	// Convert to JSON for logging
	payloadJSON, _ := json.MarshalIndent(payload, "", "  ")

	// TODO: Implement actual Socket.IO client integration
	// For now, log the notification that would be sent
	p.logger.WithFields(map[string]interface{}{
		"provider":        "socket.io",
		"server_url":      p.ServerURL,
		"namespace":       p.Namespace,
		"recipient_type":  recipient.RecipientType,
		"recipient_id":    recipient.UserID,
		"notification_id": notification.ID,
		"subject":         notification.Subject,
		"payload":         string(payloadJSON),
	}).Info("Socket.IO notification sent successfully")

	// Simulate real-time delivery
	time.Sleep(10 * time.Millisecond)

	return nil
}

func (p *SocketIOProvider) GetProviderName() string {
	return "socket.io"
}

// WebSocketProvider implements SocketProvider for native WebSocket connections
type WebSocketProvider struct {
	ServerURL string
	logger    utils.Logger
}

// NewWebSocketProvider creates a new WebSocket notification provider
func NewWebSocketProvider(serverURL string, logger utils.Logger) SocketProvider {
	return &WebSocketProvider{
		ServerURL: serverURL,
		logger:    logger,
	}
}

func (p *WebSocketProvider) SendNotification(recipient *models.NotificationRecipient, notification *models.Notification, content string, metadata map[string]interface{}) error {
	// Create notification payload for WebSocket
	payload := map[string]interface{}{
		"event": "notification",
		"data": map[string]interface{}{
			"id":           notification.ID,
			"type":         notification.Type,
			"title":        notification.Subject,
			"content":      content,
			"priority":     notification.Priority,
			"timestamp":    time.Now().Unix(),
			"metadata":     metadata,
			"read":         false,
			"tenant_id":    notification.TenantID,
			"recipient_id": recipient.UserID,
		},
	}

	// Convert to JSON for logging
	payloadJSON, _ := json.MarshalIndent(payload, "", "  ")

	// TODO: Implement actual WebSocket client integration
	// For now, log the notification that would be sent
	p.logger.WithFields(map[string]interface{}{
		"provider":        "websocket",
		"server_url":      p.ServerURL,
		"recipient_type":  recipient.RecipientType,
		"recipient_id":    recipient.UserID,
		"notification_id": notification.ID,
		"subject":         notification.Subject,
		"payload":         string(payloadJSON),
	}).Info("WebSocket notification sent successfully")

	// Simulate real-time delivery
	time.Sleep(5 * time.Millisecond)

	return nil
}

func (p *WebSocketProvider) GetProviderName() string {
	return "websocket"
}

// MockSocketProvider implements SocketProvider for testing
type MockSocketProvider struct {
	logger utils.Logger
}

// NewMockSocketProvider creates a new mock socket provider for testing
func NewMockSocketProvider(logger utils.Logger) SocketProvider {
	return &MockSocketProvider{
		logger: logger,
	}
}

func (p *MockSocketProvider) SendNotification(recipient *models.NotificationRecipient, notification *models.Notification, content string, metadata map[string]interface{}) error {
	// Log notification details for debugging
	p.logger.WithFields(map[string]interface{}{
		"provider":        "mock_socket",
		"recipient_type":  recipient.RecipientType,
		"recipient_id":    recipient.UserID,
		"notification_id": notification.ID,
		"subject":         notification.Subject,
		"type":           notification.Type,
		"priority":       notification.Priority,
	}).Info("Socket notification sent successfully via Mock provider")

	// Simulate real-time delivery
	time.Sleep(1 * time.Millisecond)

	return nil
}

func (p *MockSocketProvider) GetProviderName() string {
	return "mock_socket"
}