package services

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"net/smtp"
	"text/template"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// EmailProvider interface for different email delivery providers
type EmailProvider interface {
	SendEmail(recipient *models.NotificationRecipient, subject, content string, metadata map[string]interface{}) error
	GetProviderName() string
}

// SMTPProvider implements EmailProvider for SMTP delivery
type SMTPProvider struct {
	Host     string
	Port     int
	Username string
	Password string
	FromName string
	FromEmail string
	logger   utils.Logger
}

// NewSMTPProvider creates a new SMTP email provider
func NewSMTPProvider(host string, port int, username, password, fromName, fromEmail string, logger utils.Logger) EmailProvider {
	return &SMTPProvider{
		Host:      host,
		Port:      port,
		Username:  username,
		Password:  password,
		FromName:  fromName,
		FromEmail: fromEmail,
		logger:    logger,
	}
}

func (p *SMTPProvider) SendEmail(recipient *models.NotificationRecipient, subject, content string, metadata map[string]interface{}) error {
	// Create message
	message, err := p.createEmailMessage(recipient.RecipientAddress, subject, content)
	if err != nil {
		return fmt.Errorf("failed to create email message: %w", err)
	}

	// For MailCatcher, use simple SMTP without TLS
	if p.Port == 1025 {
		// Use simple SMTP connection for MailCatcher
		addr := fmt.Sprintf("%s:%d", p.Host, p.Port)
		return smtp.SendMail(addr, nil, p.FromEmail, []string{recipient.RecipientAddress}, message)
	}

	// Setup SMTP authentication for real SMTP servers
	var auth smtp.Auth
	if p.Username != "" && p.Password != "" {
		auth = smtp.PlainAuth("", p.Username, p.Password, p.Host)
	}

	// Setup TLS config
	tlsConfig := &tls.Config{
		InsecureSkipVerify: false,
		ServerName:         p.Host,
	}

	// Create connection
	conn, err := tls.Dial("tcp", fmt.Sprintf("%s:%d", p.Host, p.Port), tlsConfig)
	if err != nil {
		return fmt.Errorf("failed to connect to SMTP server: %w", err)
	}
	defer conn.Close()

	// Create SMTP client
	client, err := smtp.NewClient(conn, p.Host)
	if err != nil {
		return fmt.Errorf("failed to create SMTP client: %w", err)
	}
	defer client.Quit()

	// Authenticate if credentials provided
	if auth != nil {
		if err = client.Auth(auth); err != nil {
			return fmt.Errorf("SMTP authentication failed: %w", err)
		}
	}

	// Set sender
	if err = client.Mail(p.FromEmail); err != nil {
		return fmt.Errorf("failed to set sender: %w", err)
	}

	// Set recipient
	if err = client.Rcpt(recipient.RecipientAddress); err != nil {
		return fmt.Errorf("failed to set recipient: %w", err)
	}

	// Send email body
	writer, err := client.Data()
	if err != nil {
		return fmt.Errorf("failed to create data writer: %w", err)
	}

	_, err = writer.Write(message)
	if err != nil {
		return fmt.Errorf("failed to write email data: %w", err)
	}

	err = writer.Close()
	if err != nil {
		return fmt.Errorf("failed to close email data writer: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"provider":  "smtp",
		"recipient": recipient.RecipientAddress,
		"subject":   subject,
	}).Info("Email sent successfully via SMTP")

	return nil
}

func (p *SMTPProvider) GetProviderName() string {
	return "smtp"
}

func (p *SMTPProvider) createEmailMessage(to, subject, content string) ([]byte, error) {
	// Create email headers and body
	emailTemplate := `From: {{.FromName}} <{{.FromEmail}}>
To: {{.To}}
Subject: {{.Subject}}
MIME-Version: 1.0
Content-Type: text/html; charset=UTF-8

{{.Content}}`

	tmpl, err := template.New("email").Parse(emailTemplate)
	if err != nil {
		return nil, err
	}

	data := struct {
		FromName  string
		FromEmail string
		To        string
		Subject   string
		Content   string
	}{
		FromName:  p.FromName,
		FromEmail: p.FromEmail,
		To:        to,
		Subject:   subject,
		Content:   content,
	}

	var buffer bytes.Buffer
	if err := tmpl.Execute(&buffer, data); err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

// SendGridProvider implements EmailProvider for SendGrid API
type SendGridProvider struct {
	APIKey    string
	FromName  string
	FromEmail string
	logger    utils.Logger
}

// NewSendGridProvider creates a new SendGrid email provider
func NewSendGridProvider(apiKey, fromName, fromEmail string, logger utils.Logger) EmailProvider {
	return &SendGridProvider{
		APIKey:    apiKey,
		FromName:  fromName,
		FromEmail: fromEmail,
		logger:    logger,
	}
}

func (p *SendGridProvider) SendEmail(recipient *models.NotificationRecipient, subject, content string, metadata map[string]interface{}) error {
	// TODO: Implement SendGrid API integration
	// For now, log the email details
	p.logger.WithFields(map[string]interface{}{
		"provider":  "sendgrid",
		"recipient": recipient.RecipientAddress,
		"subject":   subject,
		"api_key":   p.APIKey[:8] + "...", // Only log first 8 characters
	}).Info("Email sent successfully via SendGrid (mock)")

	// Simulate API call delay
	time.Sleep(200 * time.Millisecond)

	return nil
}

func (p *SendGridProvider) GetProviderName() string {
	return "sendgrid"
}

// MockEmailProvider implements EmailProvider for testing
type MockEmailProvider struct {
	logger utils.Logger
}

// NewMockEmailProvider creates a new mock email provider for testing
func NewMockEmailProvider(logger utils.Logger) EmailProvider {
	return &MockEmailProvider{
		logger: logger,
	}
}

func (p *MockEmailProvider) SendEmail(recipient *models.NotificationRecipient, subject, content string, metadata map[string]interface{}) error {
	// Log email details for debugging
	p.logger.WithFields(map[string]interface{}{
		"provider":  "mock",
		"recipient": recipient.RecipientAddress,
		"subject":   subject,
		"content":   content[:min(100, len(content))] + "...",
	}).Info("Email sent successfully via Mock provider")

	// Simulate delivery delay
	time.Sleep(50 * time.Millisecond)

	return nil
}

func (p *MockEmailProvider) GetProviderName() string {
	return "mock"
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}