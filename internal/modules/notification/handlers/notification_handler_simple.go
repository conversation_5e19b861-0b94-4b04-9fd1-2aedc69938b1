package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

type SimpleNotificationHandler struct {
	notificationService services.NotificationService
}

func NewSimpleNotificationHandler(notificationService services.NotificationService) *SimpleNotificationHandler {
	return &SimpleNotificationHandler{
		notificationService: notificationService,
	}
}

// CreateNotification creates a new notification
func (h *SimpleNotificationHandler) CreateNotification(c *gin.Context) {
	var req models.CreateNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request data")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	notification, err := h.notificationService.CreateNotification(tenantID.(uint), req)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to create notification")
		return
	}

	response.Created(c.Writer, notification.ToResponse())
}

// GetNotification gets a notification by ID
func (h *SimpleNotificationHandler) GetNotification(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid notification ID")
		return
	}

	tenantID, _ := c.Get("tenant_id")

	notification, err := h.notificationService.GetNotificationWithRelations(tenantID.(uint), uint(id))
	if err != nil {
		response.NotFound(c.Writer, "Notification not found")
		return
	}

	response.Success(c.Writer, notification.ToResponse())
}

// ListNotifications lists notifications with filters
func (h *SimpleNotificationHandler) ListNotifications(c *gin.Context) {
	var filters models.NotificationFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		response.BadRequest(c.Writer, "Invalid query parameters")
		return
	}

	tenantID, _ := c.Get("tenant_id")

	notifications, total, err := h.notificationService.ListNotifications(tenantID.(uint), filters)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list notifications")
		return
	}

	// Convert to response format
	responseData := make([]models.NotificationResponse, len(notifications))
	for i, notification := range notifications {
		responseData[i] = *notification.ToResponse()
	}

	// Return paginated response
	result := map[string]interface{}{
		"data": responseData,
		"meta": map[string]interface{}{
			"total":      total,
			"page":       filters.Page,
			"limit":      filters.Limit,
			"total_pages": ((total - 1) / int64(filters.Limit)) + 1,
		},
	}

	response.Success(c.Writer, result)
}

// SendNotification manually sends a notification
func (h *SimpleNotificationHandler) SendNotification(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid notification ID")
		return
	}

	tenantID, _ := c.Get("tenant_id")

	err = h.notificationService.SendNotification(tenantID.(uint), uint(id))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to send notification")
		return
	}

	response.Success(c.Writer, map[string]string{"message": "Notification sent successfully"})
}