package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

// ResponseHelper provides convenient response methods for notification handlers
type ResponseHelper struct{}

var R = &ResponseHelper{}

// Success sends a success response
func (r *ResponseHelper) Success(c *gin.Context, status int, message string, data interface{}) {
	response.Success(c.Writer, data)
}

// Created sends a created response
func (r *ResponseHelper) Created(c *gin.Context, message string, data interface{}) {
	response.Created(c.Writer, data)
}

// Error sends an error response
func (r *ResponseHelper) Error(c *gin.Context, status int, message string, err error) {
	switch status {
	case http.StatusBadRequest:
		response.BadRequest(c.Writer, message)
	case http.StatusUnauthorized:
		response.Unauthorized(c.Writer, message)
	case http.StatusForbidden:
		response.Forbidden(c.Writer, message)
	case http.StatusNotFound:
		response.NotFound(c.Writer, message)
	case http.StatusConflict:
		response.Conflict(c.Writer, message)
	case http.StatusUnprocessableEntity:
		if err != nil {
			response.ValidationError(c.Writer, err)
		} else {
			response.BadRequest(c.Writer, message)
		}
	case http.StatusTooManyRequests:
		response.TooManyRequests(c.Writer, message)
	default:
		response.InternalServerError(c.Writer, message)
	}
}

// Paginated sends a paginated response
func (r *ResponseHelper) Paginated(c *gin.Context, status int, message string, data interface{}, total int64, page, limit int) {
	// For now, just return the data as success
	// TODO: Implement proper pagination response
	response.Success(c.Writer, map[string]interface{}{
		"data": data,
		"meta": map[string]interface{}{
			"total":      total,
			"page":       page,
			"limit":      limit,
			"total_pages": ((total - 1) / int64(limit)) + 1,
		},
	})
}