package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
)

type TemplateHandler struct {
	templateService services.TemplateService
}

func NewTemplateHandler(templateService services.TemplateService) *TemplateHandler {
	return &TemplateHandler{
		templateService: templateService,
	}
}

// CreateTemplate creates a new notification template
// @Summary Create template
// @Description Create a new notification template
// @Tags templates
// @Accept json
// @Produce json
// @Param template body dto.NotificationTemplateCreateRequest true "Template data"
// @Success 201 {object} map[string]interface{}// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates [post]
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	var req dto.NotificationTemplateCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	template, err := h.templateService.CreateTemplate(tenantID.(uint), req.ToServiceModel())
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to create template", err)
		return
	}

	R.Success(c, http.StatusCreated, "Template created successfully", template.ToResponse())
}

// GetTemplate gets a template by ID
// @Summary Get template
// @Description Get template details by ID with versions
// @Tags templates
// @Produce json
// @Param id path int true "Template ID"
// @Success 200 {object} map[string]interface{}// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id} [get]
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid template ID", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	template, err := h.templateService.GetTemplateWithVersions(tenantID.(uint), uint(id))
	if err != nil {
		R.Error(c, http.StatusNotFound, "Template not found", err)
		return
	}

	R.Success(c, http.StatusOK, "Template retrieved successfully", template.ToResponse())
}

// ListTemplates lists templates with filters
// @Summary List templates
// @Description Get paginated list of templates with filters
// @Tags templates
// @Produce json
// @Param type query string false "Template type" Enums(transactional,marketing,system,custom)
// @Param channel query string false "Channel" Enums(email,socket,push,sms)
// @Param is_active query bool false "Active status"
// @Param search query string false "Search in name, code, description"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort by field" default(created_at)
// @Param sort_order query string false "Sort order" Enums(asc,desc) default(desc)
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates [get]
func (h *TemplateHandler) ListTemplates(c *gin.Context) {
	var filters models.TemplateFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid query parameters", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	templates, total, err := h.templateService.ListTemplates(tenantID.(uint), filters)
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to list templates", err)
		return
	}

	// Convert to response format
	responseData := make([]models.NotificationTemplateResponse, len(templates))
	for i, template := range templates {
		responseData[i] = *template.ToResponse()
	}

	R.Paginated(c, http.StatusOK, "Templates retrieved successfully", responseData, 
		total, filters.Page, filters.Limit)
}

// UpdateTemplate updates a template
// @Summary Update template
// @Description Update template details
// @Tags templates
// @Accept json
// @Produce json
// @Param id path int true "Template ID"
// @Param template body models.UpdateTemplateRequest true "Update data"
// @Success 200 {object} map[string]interface{}// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id} [put]
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid template ID", err)
		return
	}

	var req models.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	template, err := h.templateService.UpdateTemplate(tenantID.(uint), uint(id), req)
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to update template", err)
		return
	}

	R.Success(c, http.StatusOK, "Template updated successfully", template.ToResponse())
}

// DeleteTemplate deletes a template
// @Summary Delete template
// @Description Delete a template and all its versions
// @Tags templates
// @Produce json
// @Param id path int true "Template ID"
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id} [delete]
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid template ID", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	err = h.templateService.DeleteTemplate(tenantID.(uint), uint(id))
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to delete template", err)
		return
	}

	R.Success(c, http.StatusOK, "Template deleted successfully", nil)
}

// ActivateTemplate activates a template
// @Summary Activate template
// @Description Activate a template for use
// @Tags templates
// @Produce json
// @Param id path int true "Template ID"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id}/activate [post]
func (h *TemplateHandler) ActivateTemplate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid template ID", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	err = h.templateService.ActivateTemplate(tenantID.(uint), uint(id))
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to activate template", err)
		return
	}

	R.Success(c, http.StatusOK, "Template activated successfully", nil)
}

// DeactivateTemplate deactivates a template
// @Summary Deactivate template
// @Description Deactivate a template
// @Tags templates
// @Produce json
// @Param id path int true "Template ID"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id}/deactivate [post]
func (h *TemplateHandler) DeactivateTemplate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid template ID", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	err = h.templateService.DeactivateTemplate(tenantID.(uint), uint(id))
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to deactivate template", err)
		return
	}

	R.Success(c, http.StatusOK, "Template deactivated successfully", nil)
}

// CreateTemplateVersion creates a new template version
// @Summary Create template version
// @Description Create a new version of a template
// @Tags templates
// @Accept json
// @Produce json
// @Param id path int true "Template ID"
// @Param version body models.CreateTemplateVersionRequest true "Version data"
// @Success 201 {object} map[string]interface{}// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id}/versions [post]
func (h *TemplateHandler) CreateTemplateVersion(c *gin.Context) {
	templateID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid template ID", err)
		return
	}

	var req models.CreateTemplateVersionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	version, err := h.templateService.CreateTemplateVersion(tenantID.(uint), uint(templateID), req)
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to create template version", err)
		return
	}

	R.Success(c, http.StatusCreated, "Template version created successfully", version.ToResponse())
}

// ListTemplateVersions lists versions of a template
// @Summary List template versions
// @Description Get all versions of a template
// @Tags templates
// @Produce json
// @Param id path int true "Template ID"
// @Success 200 {object} map[string]interface{}// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id}/versions [get]
func (h *TemplateHandler) ListTemplateVersions(c *gin.Context) {
	templateID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid template ID", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	versions, err := h.templateService.ListTemplateVersions(tenantID.(uint), uint(templateID))
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to list template versions", err)
		return
	}

	// Convert to response format
	responseData := make([]models.NotificationTemplateVersionResponse, len(versions))
	for i, version := range versions {
		responseData[i] = *version.ToResponse()
	}

	R.Success(c, http.StatusOK, "Template versions retrieved successfully", responseData)
}

// UpdateTemplateVersion updates a template version
// @Summary Update template version
// @Description Update template version content
// @Tags templates
// @Accept json
// @Produce json
// @Param id path int true "Template ID"
// @Param version_id path int true "Version ID"
// @Param version body models.UpdateTemplateVersionRequest true "Update data"
// @Success 200 {object} map[string]interface{}// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id}/versions/{version_id} [put]
func (h *TemplateHandler) UpdateTemplateVersion(c *gin.Context) {
	versionID, err := strconv.ParseUint(c.Param("version_id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid version ID", err)
		return
	}

	var req models.UpdateTemplateVersionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	version, err := h.templateService.UpdateTemplateVersion(tenantID.(uint), uint(versionID), req)
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to update template version", err)
		return
	}

	R.Success(c, http.StatusOK, "Template version updated successfully", version.ToResponse())
}

// ActivateTemplateVersion activates a template version
// @Summary Activate template version
// @Description Activate a specific version of a template
// @Tags templates
// @Produce json
// @Param id path int true "Template ID"
// @Param version_id path int true "Version ID"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id}/versions/{version_id}/activate [post]
func (h *TemplateHandler) ActivateTemplateVersion(c *gin.Context) {
	templateID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid template ID", err)
		return
	}

	versionID, err := strconv.ParseUint(c.Param("version_id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid version ID", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	err = h.templateService.ActivateTemplateVersion(tenantID.(uint), uint(templateID), uint(versionID))
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to activate template version", err)
		return
	}

	R.Success(c, http.StatusOK, "Template version activated successfully", nil)
}

// PreviewTemplate previews a template with test data
// @Summary Preview template
// @Description Preview template rendering with test data
// @Tags templates
// @Accept json
// @Produce json
// @Param id path int true "Template ID"
// @Param version_id path int true "Version ID"
// @Param data body map[string]interface{} true "Test data for template variables"
// @Success 200 {object} map[string]interface{}// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /templates/{id}/versions/{version_id}/preview [post]
func (h *TemplateHandler) PreviewTemplate(c *gin.Context) {
	versionID, err := strconv.ParseUint(c.Param("version_id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid version ID", err)
		return
	}

	var data map[string]interface{}
	if err := c.ShouldBindJSON(&data); err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid preview data", err)
		return
	}

	tenantID, _ := c.Get("tenant_id")

	rendered, err := h.templateService.PreviewTemplate(tenantID.(uint), uint(versionID), data)
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to preview template", err)
		return
	}

	R.Success(c, http.StatusOK, "Template preview generated", map[string]string{
		"rendered_content": rendered,
	})
}