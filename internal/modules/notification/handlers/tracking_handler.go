package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
)

type TrackingHandler struct {
	recipientRepo repositories.RecipientRepository
	logRepo       repositories.LogRepository
}

func NewTrackingHandler(recipientRepo repositories.RecipientRepository, logRepo repositories.LogRepository) *TrackingHandler {
	return &TrackingHandler{
		recipientRepo: recipientRepo,
		logRepo:       logRepo,
	}
}

// TrackOpen tracks email/notification open events
// @Summary Track open
// @Description Track when a notification is opened by recipient
// @Tags tracking
// @Produce json
// @Param tenant_id path int true "Tenant ID"
// @Param recipient_id path int true "Recipient ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tracking/{tenant_id}/recipients/{recipient_id}/open [post]
func (h *TrackingHandler) TrackOpen(c *gin.Context) {
	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid tenant ID", err)
		return
	}

	recipientID, err := strconv.ParseUint(c.Param("recipient_id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid recipient ID", err)
		return
	}

	// Get user agent and IP
	userAgent := c.GetHeader("User-Agent")
	ipAddress := c.ClientIP()

	err = h.recipientRepo.TrackOpen(uint(tenantID), uint(recipientID), &userAgent, &ipAddress)
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to track open", err)
		return
	}

	R.Success(c, http.StatusOK, "Open tracked successfully", nil)
}

// TrackClick tracks click events in notifications
// @Summary Track click
// @Description Track when a link in notification is clicked
// @Tags tracking
// @Accept json
// @Produce json
// @Param tenant_id path int true "Tenant ID"
// @Param recipient_id path int true "Recipient ID"
// @Param data body dto.NotificationTrackClickRequest true "Click data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tracking/{tenant_id}/recipients/{recipient_id}/click [post]
func (h *TrackingHandler) TrackClick(c *gin.Context) {
	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid tenant ID", err)
		return
	}

	recipientID, err := strconv.ParseUint(c.Param("recipient_id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid recipient ID", err)
		return
	}

	var req dto.NotificationTrackClickRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Get user agent and IP
	userAgent := c.GetHeader("User-Agent")
	ipAddress := c.ClientIP()

	err = h.recipientRepo.TrackClick(uint(tenantID), uint(recipientID), &req.URL, &userAgent, &ipAddress)
	if err != nil {
		R.Error(c, http.StatusInternalServerError, "Failed to track click", err)
		return
	}

	R.Success(c, http.StatusOK, "Click tracked successfully", nil)
}

// GetPixel serves tracking pixel for email open tracking
// @Summary Get tracking pixel
// @Description Serve 1x1 pixel image for email open tracking
// @Tags tracking
// @Produce image/gif
// @Param tenant_id path int true "Tenant ID"
// @Param recipient_id path int true "Recipient ID"
// @Success 200 {file} image/gif
// @Router /tracking/{tenant_id}/recipients/{recipient_id}/pixel.gif [get]
func (h *TrackingHandler) GetPixel(c *gin.Context) {
	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.Status(http.StatusBadRequest)
		return
	}

	recipientID, err := strconv.ParseUint(c.Param("recipient_id"), 10, 32)
	if err != nil {
		c.Status(http.StatusBadRequest)
		return
	}

	// Track the open asynchronously
	go func() {
		userAgent := c.GetHeader("User-Agent")
		ipAddress := c.ClientIP()
		h.recipientRepo.TrackOpen(uint(tenantID), uint(recipientID), &userAgent, &ipAddress)
	}()

	// Return 1x1 transparent pixel
	pixel := []byte{
		0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
		0xff, 0xff, 0xff, 0x21, 0xf9, 0x04, 0x01, 0x00, 0x00, 0x00, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00,
		0x01, 0x00, 0x01, 0x00, 0x00, 0x02, 0x02, 0x44, 0x01, 0x00, 0x3b,
	}

	c.Header("Content-Type", "image/gif")
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")
	c.Data(http.StatusOK, "image/gif", pixel)
}

// RedirectAndTrack redirects to URL and tracks click
// @Summary Redirect and track
// @Description Redirect to target URL while tracking the click
// @Tags tracking
// @Produce json
// @Param tenant_id path int true "Tenant ID"
// @Param recipient_id path int true "Recipient ID"
// @Param url query string true "Target URL to redirect to"
// @Success 302 "Redirect to target URL"
// @Failure 400 {object} map[string]interface{}
// @Router /tracking/{tenant_id}/recipients/{recipient_id}/redirect [get]
func (h *TrackingHandler) RedirectAndTrack(c *gin.Context) {
	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid tenant ID", err)
		return
	}

	recipientID, err := strconv.ParseUint(c.Param("recipient_id"), 10, 32)
	if err != nil {
		R.Error(c, http.StatusBadRequest, "Invalid recipient ID", err)
		return
	}

	targetURL := c.Query("url")
	if targetURL == "" {
		R.Error(c, http.StatusBadRequest, "Target URL is required", nil)
		return
	}

	// Track the click asynchronously
	go func() {
		userAgent := c.GetHeader("User-Agent")
		ipAddress := c.ClientIP()
		h.recipientRepo.TrackClick(uint(tenantID), uint(recipientID), &targetURL, &userAgent, &ipAddress)
	}()

	// Redirect to target URL
	c.Redirect(http.StatusFound, targetURL)
}

