package dto

import (
	"encoding/json"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
)

// NotificationTemplateCreateRequest represents request to create a notification template
type NotificationTemplateCreateRequest struct {
	Code        string                      `json:"code" validate:"required,max=100,alphanum" example:"welcome_email_v1"`
	Name        string                      `json:"name" validate:"required,max=255" example:"Welcome Email Template"`
	Type        models.TemplateType         `json:"type" validate:"required,oneof=transactional marketing system custom" example:"transactional"`
	Channel     models.NotificationChannel  `json:"channel" validate:"required,oneof=email socket push sms" example:"email"`
	Description *string                     `json:"description" example:"Welcome email sent to new users upon registration"`
	Variables   []string                    `json:"variables" example:"name,company,activation_link"`
	IsActive    bool                        `json:"is_active" example:"false"`
}

// NotificationTemplateUpdateRequest represents request to update a notification template
type NotificationTemplateUpdateRequest struct {
	Name        *string             `json:"name" validate:"omitempty,max=255" example:"Updated Welcome Email Template"`
	Type        *models.TemplateType `json:"type" validate:"omitempty,oneof=transactional marketing system custom" example:"marketing"`
	Description *string             `json:"description" example:"Updated description for welcome email template"`
	Variables   []string            `json:"variables" example:"name,company,activation_link,support_email"`
	IsActive    *bool               `json:"is_active" example:"true"`
}

// NotificationTemplateResponse represents notification template information in responses
type NotificationTemplateResponse struct {
	ID              uint                        `json:"id" example:"1"`
	TenantID        uint                        `json:"tenant_id" example:"1"`
	Code            string                      `json:"code" example:"welcome_email_v1"`
	Name            string                      `json:"name" example:"Welcome Email Template"`
	Type            models.TemplateType         `json:"type" example:"transactional"`
	Channel         models.NotificationChannel  `json:"channel" example:"email"`
	Description     *string                     `json:"description,omitempty"`
	Variables       json.RawMessage             `json:"variables,omitempty"`
	IsActive        bool                        `json:"is_active" example:"true"`
	VersionCount    uint                        `json:"version_count" example:"3"`
	ActiveVersionID *uint                       `json:"active_version_id" example:"5"`
	CreatedBy       *uint                       `json:"created_by" example:"123"`
	UpdatedBy       *uint                       `json:"updated_by" example:"124"`
	CreatedAt       time.Time                   `json:"created_at"`
	UpdatedAt       time.Time                   `json:"updated_at"`

	// Optional relationships
	Versions      []NotificationTemplateVersionResponse `json:"versions,omitempty"`
	ActiveVersion *NotificationTemplateVersionResponse  `json:"active_version,omitempty"`
}

// NotificationTemplateListResponse represents response for listing notification templates
type NotificationTemplateListResponse struct {
	Templates  []NotificationTemplateResponse `json:"templates"`
	Total      int64                          `json:"total" example:"25"`
	Page       int                            `json:"page" example:"1"`
	PageSize   int                            `json:"page_size" example:"20"`
	TotalPages int                            `json:"total_pages" example:"2"`
}

// NotificationTemplateFilter represents filter parameters for listing templates
type NotificationTemplateFilter struct {
	Type      models.TemplateType        `json:"type,omitempty" validate:"omitempty,oneof=transactional marketing system custom" example:"transactional"`
	Channel   models.NotificationChannel `json:"channel,omitempty" validate:"omitempty,oneof=email socket push sms" example:"email"`
	IsActive  *bool                      `json:"is_active,omitempty" example:"true"`
	Search    string                     `json:"search,omitempty" example:"welcome"`
	Page      int                        `json:"page,omitempty" validate:"min=1" example:"1"`
	PageSize  int                        `json:"page_size,omitempty" validate:"min=1,max=100" example:"20"`
	SortBy    string                     `json:"sort_by,omitempty" validate:"omitempty,oneof=id name code created_at updated_at" example:"created_at"`
	SortOrder string                     `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// NotificationTemplateVersionCreateRequest represents request to create template version
type NotificationTemplateVersionCreateRequest struct {
	Language  string   `json:"language" validate:"required,max=10" example:"en"`
	Subject   string   `json:"subject" validate:"required,max=255" example:"Welcome to {{company}}!"`
	BodyHTML  string   `json:"body_html" validate:"required" example:"<h1>Welcome {{name}}!</h1><p>Thanks for joining {{company}}.</p>"`
	BodyText  *string  `json:"body_text" example:"Welcome {{name}}! Thanks for joining {{company}}."`
	Variables []string `json:"variables" example:"name,company"`
	IsActive  bool     `json:"is_active" example:"false"`
}

// NotificationTemplateVersionUpdateRequest represents request to update template version
type NotificationTemplateVersionUpdateRequest struct {
	Subject   *string  `json:"subject" validate:"omitempty,max=255" example:"Updated: Welcome to {{company}}!"`
	BodyHTML  *string  `json:"body_html" example:"<h1>Welcome {{name}}!</h1><p>Thanks for joining {{company}}. <a href='{{activation_link}}'>Activate your account</a></p>"`
	BodyText  *string  `json:"body_text" example:"Welcome {{name}}! Thanks for joining {{company}}. Activate: {{activation_link}}"`
	Variables []string `json:"variables" example:"name,company,activation_link"`
	IsActive  *bool    `json:"is_active" example:"true"`
}

// NotificationTemplateVersionResponse represents template version information in responses
type NotificationTemplateVersionResponse struct {
	ID            uint            `json:"id" example:"1"`
	TenantID      uint            `json:"tenant_id" example:"1"`
	TemplateID    uint            `json:"template_id" example:"1"`
	VersionNumber uint            `json:"version_number" example:"2"`
	Language      string          `json:"language" example:"en"`
	Subject       string          `json:"subject" example:"Welcome to {{company}}!"`
	BodyHTML      string          `json:"body_html" example:"<h1>Welcome {{name}}!</h1>"`
	BodyText      *string         `json:"body_text,omitempty"`
	Variables     json.RawMessage `json:"variables,omitempty"`
	IsActive      bool            `json:"is_active" example:"true"`
	IsApproved    bool            `json:"is_approved" example:"true"`
	ApprovedBy    *uint           `json:"approved_by" example:"125"`
	ApprovedAt    *time.Time      `json:"approved_at,omitempty"`
	CreatedBy     *uint           `json:"created_by" example:"123"`
	CreatedAt     time.Time       `json:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at"`
}

// NotificationTemplateVersionListResponse represents response for listing template versions
type NotificationTemplateVersionListResponse struct {
	Versions   []NotificationTemplateVersionResponse `json:"versions"`
	TemplateID uint                                  `json:"template_id" example:"1"`
	Total      int64                                 `json:"total" example:"5"`
}

// NotificationTemplatePreviewRequest represents request to preview template
type NotificationTemplatePreviewRequest struct {
	Data map[string]interface{} `json:"data" validate:"required" example:"{\"name\":\"John Doe\",\"company\":\"ACME Corp\",\"activation_link\":\"https://example.com/activate/123\"}"`
}

// NotificationTemplatePreviewResponse represents response for template preview
type NotificationTemplatePreviewResponse struct {
	VersionID       uint   `json:"version_id" example:"5"`
	RenderedSubject string `json:"rendered_subject" example:"Welcome to ACME Corp!"`
	RenderedHTML    string `json:"rendered_html" example:"<h1>Welcome John Doe!</h1><p>Thanks for joining ACME Corp.</p>"`
	RenderedText    string `json:"rendered_text" example:"Welcome John Doe! Thanks for joining ACME Corp."`
}

// NotificationTemplateBulkActionRequest represents request for bulk operations on templates
type NotificationTemplateBulkActionRequest struct {
	TemplateIDs []uint `json:"template_ids" validate:"required,min=1" example:"1,2,3"`
	Action      string `json:"action" validate:"required,oneof=activate deactivate delete" example:"activate"`
	Reason      string `json:"reason,omitempty" example:"Bulk activation for campaign launch"`
}

// NotificationTemplateCloneRequest represents request to clone a template
type NotificationTemplateCloneRequest struct {
	NewCode        string  `json:"new_code" validate:"required,max=100,alphanum" example:"welcome_email_v2"`
	NewName        string  `json:"new_name" validate:"required,max=255" example:"Welcome Email Template V2"`
	CloneVersions  bool    `json:"clone_versions" example:"true"`
	IncludeContent bool    `json:"include_content" example:"true"`
	Description    *string `json:"description" example:"Cloned version of welcome email template"`
}

// NotificationTemplateCloneResponse represents response for template cloning
type NotificationTemplateCloneResponse struct {
	Success         bool                          `json:"success" example:"true"`
	Message         string                        `json:"message" example:"Template cloned successfully"`
	OriginalTemplate *NotificationTemplateResponse `json:"original_template"`
	ClonedTemplate   *NotificationTemplateResponse `json:"cloned_template"`
	ClonesVersions   int                          `json:"cloned_versions" example:"3"`
}

// NotificationTemplateValidationRequest represents request to validate template content
type NotificationTemplateValidationRequest struct {
	Subject   string                 `json:"subject" validate:"required" example:"Welcome {{name}}!"`
	BodyHTML  string                 `json:"body_html" validate:"required" example:"<h1>Hello {{name}}</h1>"`
	BodyText  *string                `json:"body_text" example:"Hello {{name}}"`
	Variables []string               `json:"variables" example:"name,company"`
	TestData  map[string]interface{} `json:"test_data" example:"{\"name\":\"Test User\",\"company\":\"Test Corp\"}"`
}

// NotificationTemplateValidationResponse represents response for template validation
type NotificationTemplateValidationResponse struct {
	Valid             bool                             `json:"valid" example:"true"`
	Errors            []string                         `json:"errors,omitempty"`
	Warnings          []string                         `json:"warnings,omitempty"`
	MissingVariables  []string                         `json:"missing_variables,omitempty"`
	UnusedVariables   []string                         `json:"unused_variables,omitempty"`
	RenderedPreview   *NotificationTemplatePreviewResponse `json:"rendered_preview,omitempty"`
}

// NotificationTemplateExportRequest represents request to export templates
type NotificationTemplateExportRequest struct {
	TemplateIDs     []uint `json:"template_ids" validate:"required,min=1" example:"1,2,3"`
	IncludeVersions bool   `json:"include_versions" example:"true"`
	IncludeMetadata bool   `json:"include_metadata" example:"true"`
	Format          string `json:"format" validate:"required,oneof=json yaml xml" example:"json"`
}

// NotificationTemplateExportResponse represents response for template export
type NotificationTemplateExportResponse struct {
	Success     bool   `json:"success" example:"true"`
	Message     string `json:"message" example:"Templates exported successfully"`
	ExportID    string `json:"export_id" example:"export_abc123"`
	DownloadURL string `json:"download_url" example:"https://api.example.com/exports/abc123/download"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// NotificationTemplateImportRequest represents request to import templates
type NotificationTemplateImportRequest struct {
	Data              json.RawMessage `json:"data" validate:"required"`
	OverwriteExisting bool            `json:"overwrite_existing" example:"false"`
	CreateVersions    bool            `json:"create_versions" example:"true"`
	ValidateOnly      bool            `json:"validate_only" example:"false"`
}

// NotificationTemplateImportResponse represents response for template import
type NotificationTemplateImportResponse struct {
	Success          bool                            `json:"success" example:"true"`
	Message          string                          `json:"message" example:"Templates imported successfully"`
	ImportedCount    int                             `json:"imported_count" example:"5"`
	SkippedCount     int                             `json:"skipped_count" example:"1"`
	ErrorCount       int                             `json:"error_count" example:"0"`
	ImportedTemplates []NotificationTemplateResponse `json:"imported_templates,omitempty"`
	Errors           []string                        `json:"errors,omitempty"`
}

// NotificationTemplateStatsResponse represents template usage statistics
type NotificationTemplateStatsResponse struct {
	TemplateID       uint    `json:"template_id" example:"1"`
	TotalUsage       int64   `json:"total_usage" example:"1500"`
	RecentUsage      int64   `json:"recent_usage" example:"150"`
	AverageOpenRate  float64 `json:"average_open_rate" example:"32.5"`
	AverageClickRate float64 `json:"average_click_rate" example:"8.7"`
	LastUsed         *time.Time `json:"last_used,omitempty"`
	PopularVariables []string `json:"popular_variables" example:"name,company,activation_link"`
}

// ToServiceModel converts NotificationTemplateCreateRequest to models.CreateTemplateRequest
func (r *NotificationTemplateCreateRequest) ToServiceModel() models.CreateTemplateRequest {
	return models.CreateTemplateRequest{
		Code:        r.Code,
		Name:        r.Name,
		Type:        r.Type,
		Channel:     r.Channel,
		Description: r.Description,
		Variables:   r.Variables,
	}
}

// ToServiceModel converts NotificationTemplateUpdateRequest to models.UpdateTemplateRequest
func (r *NotificationTemplateUpdateRequest) ToServiceModel() models.UpdateTemplateRequest {
	return models.UpdateTemplateRequest{
		Name:        r.Name,
		Type:        r.Type,
		Description: r.Description,
		Variables:   r.Variables,
		IsActive:    r.IsActive,
	}
}

// ToServiceModel converts NotificationTemplateVersionCreateRequest to models.CreateTemplateVersionRequest
func (r *NotificationTemplateVersionCreateRequest) ToServiceModel() models.CreateTemplateVersionRequest {
	return models.CreateTemplateVersionRequest{
		Language:  r.Language,
		Subject:   r.Subject,
		BodyHTML:  r.BodyHTML,
		BodyText:  r.BodyText,
		Variables: r.Variables,
	}
}

// ToServiceModel converts NotificationTemplateVersionUpdateRequest to models.UpdateTemplateVersionRequest
func (r *NotificationTemplateVersionUpdateRequest) ToServiceModel() models.UpdateTemplateVersionRequest {
	return models.UpdateTemplateVersionRequest{
		Subject:   r.Subject,
		BodyHTML:  r.BodyHTML,
		BodyText:  r.BodyText,
		Variables: r.Variables,
		IsActive:  r.IsActive,
	}
}