package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
)

// NotificationTrackOpenRequest represents request to track notification open
type NotificationTrackOpenRequest struct {
	UserAgent *string `json:"user_agent" example:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"`
	IPAddress *string `json:"ip_address" example:"*************"`
	Metadata  map[string]interface{} `json:"metadata" example:"{\"source\":\"email_client\"}"`
}

// NotificationTrackClickRequest represents request to track notification click
type NotificationTrackClickRequest struct {
	URL       string                 `json:"url" validate:"required,url" example:"https://example.com/activate"`
	LinkText  *string                `json:"link_text" example:"Activate Account"`
	UserAgent *string                `json:"user_agent" example:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"`
	IPAddress *string                `json:"ip_address" example:"*************"`
	Metadata  map[string]interface{} `json:"metadata" example:"{\"button_id\":\"cta-primary\"}"`
}

// NotificationTrackingResponse represents tracking event response
type NotificationTrackingResponse struct {
	Success     bool   `json:"success" example:"true"`
	Message     string `json:"message" example:"Event tracked successfully"`
	TrackingID  string `json:"tracking_id" example:"track_abc123"`
	RecipientID uint   `json:"recipient_id" example:"1"`
	EventType   string `json:"event_type" example:"click"`
	Timestamp   time.Time `json:"timestamp"`
}

// NotificationTrackingStatsRequest represents request for tracking statistics
type NotificationTrackingStatsRequest struct {
	NotificationID *uint      `json:"notification_id" example:"1"`
	RecipientID    *uint      `json:"recipient_id" example:"1"`
	EventType      *string    `json:"event_type" validate:"omitempty,oneof=open click bounce delivery" example:"click"`
	DateFrom       *time.Time `json:"date_from" example:"2024-01-01T00:00:00Z"`
	DateTo         *time.Time `json:"date_to" example:"2024-01-31T23:59:59Z"`
	GroupBy        string     `json:"group_by" validate:"omitempty,oneof=day week month notification recipient" example:"day"`
}

// NotificationTrackingStatsResponse represents tracking statistics
type NotificationTrackingStatsResponse struct {
	TotalEvents      int64                           `json:"total_events" example:"1500"`
	OpenEvents       int64                           `json:"open_events" example:"800"`
	ClickEvents      int64                           `json:"click_events" example:"300"`
	BounceEvents     int64                           `json:"bounce_events" example:"50"`
	DeliveryEvents   int64                           `json:"delivery_events" example:"1450"`
	OpenRate         float64                         `json:"open_rate" example:"55.2"`
	ClickRate        float64                         `json:"click_rate" example:"20.7"`
	BounceRate       float64                         `json:"bounce_rate" example:"3.4"`
	ByEventType      map[string]int64                `json:"by_event_type"`
	ByNotification   map[string]NotificationEventStats `json:"by_notification,omitempty"`
	ByRecipient      map[string]RecipientEventStats    `json:"by_recipient,omitempty"`
	Timeline         []TrackingTimelineStats         `json:"timeline,omitempty"`
}

// NotificationEventStats represents statistics by notification
type NotificationEventStats struct {
	NotificationID uint    `json:"notification_id" example:"1"`
	NotificationSubject string `json:"notification_subject" example:"Welcome Email"`
	TotalEvents    int64   `json:"total_events" example:"500"`
	OpenEvents     int64   `json:"open_events" example:"250"`
	ClickEvents    int64   `json:"click_events" example:"75"`
	BounceEvents   int64   `json:"bounce_events" example:"10"`
	OpenRate       float64 `json:"open_rate" example:"50.0"`
	ClickRate      float64 `json:"click_rate" example:"15.0"`
	BounceRate     float64 `json:"bounce_rate" example:"2.0"`
}

// RecipientEventStats represents statistics by recipient
type RecipientEventStats struct {
	RecipientID      uint    `json:"recipient_id" example:"1"`
	RecipientAddress string  `json:"recipient_address" example:"<EMAIL>"`
	TotalEvents      int64   `json:"total_events" example:"25"`
	OpenEvents       int64   `json:"open_events" example:"15"`
	ClickEvents      int64   `json:"click_events" example:"5"`
	BounceEvents     int64   `json:"bounce_events" example:"0"`
	LastActivity     *time.Time `json:"last_activity,omitempty"`
}

// TrackingTimelineStats represents tracking statistics over time
type TrackingTimelineStats struct {
	Date         time.Time `json:"date" example:"2024-01-15T00:00:00Z"`
	TotalEvents  int64     `json:"total_events" example:"100"`
	OpenEvents   int64     `json:"open_events" example:"60"`
	ClickEvents  int64     `json:"click_events" example:"20"`
	BounceEvents int64     `json:"bounce_events" example:"3"`
	DeliveryEvents int64   `json:"delivery_events" example:"97"`
}

// NotificationLogResponse represents notification log information
type NotificationLogResponse struct {
	ID             uint                    `json:"id" example:"1"`
	TenantID       uint                    `json:"tenant_id" example:"1"`
	NotificationID uint                    `json:"notification_id" example:"1"`
	RecipientID    *uint                   `json:"recipient_id" example:"1"`
	EventType      string                  `json:"event_type" example:"click"`
	EventData      map[string]interface{}  `json:"event_data"`
	UserAgent      *string                 `json:"user_agent,omitempty"`
	IPAddress      *string                 `json:"ip_address,omitempty"`
	Timestamp      time.Time               `json:"timestamp"`
	CreatedAt      time.Time               `json:"created_at"`
}

// NotificationLogListResponse represents response for listing notification logs
type NotificationLogListResponse struct {
	Logs       []NotificationLogResponse `json:"logs"`
	Total      int64                     `json:"total" example:"150"`
	Page       int                       `json:"page" example:"1"`
	PageSize   int                       `json:"page_size" example:"20"`
	TotalPages int                       `json:"total_pages" example:"8"`
}

// NotificationLogFilter represents filter parameters for listing logs
type NotificationLogFilter struct {
	NotificationID *uint      `json:"notification_id" example:"1"`
	RecipientID    *uint      `json:"recipient_id" example:"1"`
	EventType      string     `json:"event_type" validate:"omitempty,oneof=open click bounce delivery failed" example:"click"`
	DateFrom       *time.Time `json:"date_from" example:"2024-01-01T00:00:00Z"`
	DateTo         *time.Time `json:"date_to" example:"2024-01-31T23:59:59Z"`
	Page           int        `json:"page" validate:"min=1" example:"1"`
	PageSize       int        `json:"page_size" validate:"min=1,max=100" example:"20"`
	SortBy         string     `json:"sort_by" validate:"omitempty,oneof=id timestamp event_type created_at" example:"timestamp"`
	SortOrder      string     `json:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// NotificationPixelResponse represents tracking pixel response metadata
type NotificationPixelResponse struct {
	Success       bool      `json:"success" example:"true"`
	RecipientID   uint      `json:"recipient_id" example:"1"`
	TrackingID    string    `json:"tracking_id" example:"pixel_abc123"`
	FirstOpen     bool      `json:"first_open" example:"true"`
	OpenCount     int       `json:"open_count" example:"1"`
	Timestamp     time.Time `json:"timestamp"`
}

// NotificationRedirectRequest represents redirect tracking request
type NotificationRedirectRequest struct {
	TargetURL string                 `json:"target_url" validate:"required,url" example:"https://example.com/welcome"`
	LinkText  *string                `json:"link_text" example:"Visit Our Website"`
	Metadata  map[string]interface{} `json:"metadata" example:"{\"campaign\":\"welcome-series\"}"`
}

// NotificationRedirectResponse represents redirect tracking response
type NotificationRedirectResponse struct {
	Success     bool      `json:"success" example:"true"`
	Message     string    `json:"message" example:"Redirect tracked successfully"`
	TargetURL   string    `json:"target_url" example:"https://example.com/welcome"`
	TrackingID  string    `json:"tracking_id" example:"redirect_abc123"`
	RecipientID uint      `json:"recipient_id" example:"1"`
	Timestamp   time.Time `json:"timestamp"`
}

// NotificationEngagementResponse represents recipient engagement metrics
type NotificationEngagementResponse struct {
	RecipientID      uint                   `json:"recipient_id" example:"1"`
	RecipientAddress string                 `json:"recipient_address" example:"<EMAIL>"`
	TotalNotifications int64                `json:"total_notifications" example:"50"`
	OpenedNotifications int64               `json:"opened_notifications" example:"35"`
	ClickedNotifications int64              `json:"clicked_notifications" example:"15"`
	BouncedNotifications int64              `json:"bounced_notifications" example:"2"`
	OpenRate            float64             `json:"open_rate" example:"70.0"`
	ClickRate           float64             `json:"click_rate" example:"30.0"`
	BounceRate          float64             `json:"bounce_rate" example:"4.0"`
	AverageOpenTime     *float64            `json:"average_open_time" example:"2.5"`
	FirstActivity       *time.Time          `json:"first_activity,omitempty"`
	LastActivity        *time.Time          `json:"last_activity,omitempty"`
	PreferredChannel    models.NotificationChannel `json:"preferred_channel" example:"email"`
	EngagementScore     float64             `json:"engagement_score" example:"85.2"`
}

// NotificationHeatmapRequest represents request for click heatmap data
type NotificationHeatmapRequest struct {
	NotificationID uint      `json:"notification_id" validate:"required" example:"1"`
	DateFrom       *time.Time `json:"date_from" example:"2024-01-01T00:00:00Z"`
	DateTo         *time.Time `json:"date_to" example:"2024-01-31T23:59:59Z"`
}

// NotificationHeatmapResponse represents click heatmap data
type NotificationHeatmapResponse struct {
	NotificationID uint                    `json:"notification_id" example:"1"`
	TotalClicks    int64                   `json:"total_clicks" example:"150"`
	ClicksByURL    map[string]ClickURLStats `json:"clicks_by_url"`
	ClicksByTime   []ClickTimeStats        `json:"clicks_by_time"`
	TopLinks       []TopLinkStats          `json:"top_links"`
}

// ClickURLStats represents click statistics by URL
type ClickURLStats struct {
	URL        string  `json:"url" example:"https://example.com/product"`
	Clicks     int64   `json:"clicks" example:"45"`
	UniqueClicks int64 `json:"unique_clicks" example:"38"`
	ClickRate  float64 `json:"click_rate" example:"30.0"`
	LinkText   *string `json:"link_text,omitempty" example:"View Product"`
}

// ClickTimeStats represents click statistics by time period
type ClickTimeStats struct {
	TimeSlot time.Time `json:"time_slot" example:"2024-01-15T10:00:00Z"`
	Clicks   int64     `json:"clicks" example:"12"`
	UniqueClicks int64 `json:"unique_clicks" example:"10"`
}

// TopLinkStats represents most clicked links
type TopLinkStats struct {
	Rank       int     `json:"rank" example:"1"`
	URL        string  `json:"url" example:"https://example.com/cta"`
	LinkText   *string `json:"link_text,omitempty" example:"Get Started"`
	Clicks     int64   `json:"clicks" example:"85"`
	UniqueClicks int64 `json:"unique_clicks" example:"72"`
	ClickRate  float64 `json:"click_rate" example:"48.0"`
}