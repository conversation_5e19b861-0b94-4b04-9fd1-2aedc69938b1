package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

// OnboardingHandler handles onboarding-related HTTP requests
type OnboardingHandler struct {
	onboardingService services.OnboardingService
	validator         validator.Validator
	logger            utils.Logger
	tracer            trace.Tracer
}

// NewOnboardingHandler creates a new onboarding handler
func NewOnboardingHandler(
	onboardingService services.OnboardingService,
	validator validator.Validator,
	logger utils.Logger,
) *OnboardingHandler {
	return &OnboardingHandler{
		onboardingService: onboardingService,
		validator:         validator,
		logger:            logger,
		tracer:            otel.Tracer("onboarding-handler"),
	}
}

// GetProgress godoc
// @Summary      Get onboarding progress
// @Description  Retrieves the onboarding progress for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.GetOnboardingProgressResponse} "Onboarding progress retrieved successfully"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      404 {object} response.Response "Onboarding progress not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/progress [get]
func (h *OnboardingHandler) GetProgress(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.GetProgress")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	response, err := h.onboardingService.GetProgress(ctx, userIDUint)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
		}).Error("Failed to get onboarding progress")

		if err.Error() == "failed to get onboarding progress: onboarding progress not found for user" {
			httpresponse.NotFound(c.Writer, "Onboarding progress not found")
			return
		}

		httpresponse.InternalServerError(c.Writer, "Failed to get onboarding progress")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// StartOnboarding godoc
// @Summary      Start onboarding
// @Description  Starts the onboarding process for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.StartOnboardingResponse} "Onboarding started successfully"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      409 {object} response.Response "Onboarding already completed"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/start [post]
func (h *OnboardingHandler) StartOnboarding(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.StartOnboarding")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	response, err := h.onboardingService.StartOnboarding(ctx, userIDUint)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userIDUint,
		}).Error("Failed to start onboarding")

		if err.Error() == "onboarding already completed" {
			httpresponse.Conflict(c.Writer, "Onboarding already completed")
			return
		}

		httpresponse.InternalServerError(c.Writer, "Failed to start onboarding")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// UpdateStep godoc
// @Summary      Update onboarding step
// @Description  Updates the current step in the onboarding process for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Param        body body dto.UpdateOnboardingStepRequest true "Update step request"
// @Success      200 {object} response.Response{data=dto.UpdateOnboardingStepResponse} "Onboarding step updated successfully"
// @Failure      400 {object} response.Response "Invalid request format"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      404 {object} response.Response "Onboarding progress not found"
// @Failure      409 {object} response.Response "Onboarding already completed"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/step [put]
func (h *OnboardingHandler) UpdateStep(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.UpdateStep")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	var req dto.UpdateOnboardingStepRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Set user ID from auth context
	req.UserID = userIDUint

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	response, err := h.onboardingService.UpdateStep(ctx, &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": req.UserID,
			"step":    req.Step,
		}).Error("Failed to update onboarding step")

		if err.Error() == "onboarding already completed" {
			httpresponse.Conflict(c.Writer, "Onboarding already completed")
			return
		}

		if err.Error() == "failed to get onboarding progress: onboarding progress not found for user" {
			httpresponse.NotFound(c.Writer, "Onboarding progress not found")
			return
		}

		httpresponse.InternalServerError(c.Writer, "Failed to update onboarding step")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// CompleteOnboarding godoc
// @Summary      Complete onboarding
// @Description  Marks the onboarding process as completed for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Param        body body dto.CompleteOnboardingRequest true "Complete onboarding request"
// @Success      200 {object} response.Response{data=dto.CompleteOnboardingResponse} "Onboarding completed successfully"
// @Failure      400 {object} response.Response "Invalid request format"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      404 {object} response.Response "Onboarding progress not found"
// @Failure      409 {object} response.Response "Onboarding already completed"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/complete [post]
func (h *OnboardingHandler) CompleteOnboarding(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.CompleteOnboarding")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	var req dto.CompleteOnboardingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format", err.Error())
		return
	}

	// Set user ID from auth context
	req.UserID = userIDUint

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	response, err := h.onboardingService.CompleteOnboarding(ctx, &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": req.UserID,
		}).Error("Failed to complete onboarding")

		if err.Error() == "onboarding already completed" {
			httpresponse.Conflict(c.Writer, "Onboarding already completed")
			return
		}

		if err.Error() == "failed to get onboarding progress: onboarding progress not found for user" {
			httpresponse.NotFound(c.Writer, "Onboarding progress not found")
			return
		}

		httpresponse.InternalServerError(c.Writer, "Failed to complete onboarding")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// ResetOnboarding godoc
// @Summary      Reset onboarding
// @Description  Resets the onboarding process for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.ResetOnboardingResponse} "Onboarding reset successfully"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      404 {object} response.Response "Onboarding progress not found"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/reset [post]
func (h *OnboardingHandler) ResetOnboarding(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.ResetOnboarding")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	response, err := h.onboardingService.ResetOnboarding(ctx, userIDUint)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
		}).Error("Failed to reset onboarding")

		if err.Error() == "failed to get onboarding progress: onboarding progress not found for user" {
			httpresponse.NotFound(c.Writer, "Onboarding progress not found")
			return
		}

		httpresponse.InternalServerError(c.Writer, "Failed to reset onboarding")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// ListProgress godoc
// @Summary      List onboarding progress
// @Description  Lists onboarding progress with optional filters
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Param        status query string false "Filter by status (pending, processing, completed)"
// @Param        step query string false "Filter by step (create_tenant, create_website, completed)"
// @Param        limit query int false "Limit number of results (default: 10, max: 100)"
// @Param        offset query int false "Offset for pagination (default: 0)"
// @Success      200 {object} response.Response{data=dto.ListOnboardingProgressResponse} "Onboarding progress list retrieved successfully"
// @Failure      400 {object} response.Response "Invalid query parameters"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/progress [get]
func (h *OnboardingHandler) ListProgress(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.ListProgress")
	defer span.End()

	var req dto.ListOnboardingProgressRequest

	// Parse query parameters
	req.Status = c.Query("status")
	req.Step = c.Query("step")

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			req.Limit = limit
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			req.Offset = offset
		}
	}

	// Validate request
	if err := h.validator.Validate(ctx, &req); err != nil {
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	response, err := h.onboardingService.ListProgress(ctx, &req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"status": req.Status,
			"step":   req.Step,
			"limit":  req.Limit,
			"offset": req.Offset,
		}).Error("Failed to list onboarding progress")

		httpresponse.InternalServerError(c.Writer, "Failed to list onboarding progress")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// GetStats godoc
// @Summary      Get onboarding statistics
// @Description  Retrieves statistics about onboarding progress
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=dto.OnboardingStatsResponse} "Onboarding statistics retrieved successfully"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/stats [get]
func (h *OnboardingHandler) GetStats(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.GetStats")
	defer span.End()

	response, err := h.onboardingService.GetStats(ctx)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get onboarding statistics")
		httpresponse.InternalServerError(c.Writer, "Failed to get onboarding statistics")
		return
	}

	httpresponse.Success(c.Writer, response)
}

// IsOnboardingRequired godoc
// @Summary      Check if onboarding is required
// @Description  Checks if onboarding is required for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=map[string]bool} "Onboarding requirement status retrieved successfully"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/required [get]
func (h *OnboardingHandler) IsOnboardingRequired(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.IsOnboardingRequired")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	required, err := h.onboardingService.IsOnboardingRequired(ctx, userIDUint)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
		}).Error("Failed to check if onboarding is required")

		httpresponse.InternalServerError(c.Writer, "Failed to check onboarding requirement")
		return
	}

	response := map[string]bool{
		"required": required,
	}

	httpresponse.Success(c.Writer, response)
}

// GetCurrentStep godoc
// @Summary      Get current onboarding step
// @Description  Retrieves the current onboarding step for the authenticated user
// @Tags         onboarding
// @Accept       json
// @Produce      json
// @Success      200 {object} response.Response{data=map[string]string} "Current onboarding step retrieved successfully"
// @Failure      401 {object} response.Response "Unauthorized"
// @Failure      500 {object} response.Response "Internal server error"
// @Router       /api/cms/v1/onboarding/step [get]
func (h *OnboardingHandler) GetCurrentStep(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OnboardingHandler.GetCurrentStep")
	defer span.End()

	// Get user ID from JWT context
	userID, exists := c.Get("user_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		httpresponse.Unauthorized(c.Writer, "Invalid user context")
		return
	}

	step, err := h.onboardingService.GetCurrentStep(ctx, userIDUint)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
		}).Error("Failed to get current onboarding step")

		httpresponse.InternalServerError(c.Writer, "Failed to get current onboarding step")
		return
	}

	response := map[string]string{
		"step": step,
	}

	httpresponse.Success(c.Writer, response)
}
