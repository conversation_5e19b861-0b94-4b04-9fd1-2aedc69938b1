package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// OnboardingStatus represents the status of onboarding progress
// @Enum pending,processing,completed
type OnboardingStatus string

const (
	OnboardingStatusPending    OnboardingStatus = "pending"
	OnboardingStatusProcessing OnboardingStatus = "processing"
	OnboardingStatusCompleted  OnboardingStatus = "completed"
)

// <PERSON>an implements sql.Scanner interface
func (s *OnboardingStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = OnboardingStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s OnboardingStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// OnboardingStep represents the current step in the onboarding process
// @Enum create_tenant,create_website,completed
type OnboardingStep string

const (
	OnboardingStepCreateTenant  OnboardingStep = "create_tenant"
	OnboardingStepCreateWebsite OnboardingStep = "create_website"
	OnboardingStepCompleted     OnboardingStep = "completed"
)

// <PERSON><PERSON> implements sql.Scanner interface
func (s *OnboardingStep) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = OnboardingStep(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s OnboardingStep) Value() (driver.Value, error) {
	return string(s), nil
}

// OnboardingMetadata represents additional metadata for onboarding progress
type OnboardingMetadata map[string]interface{}

// Scan implements sql.Scanner interface
func (m *OnboardingMetadata) Scan(value interface{}) error {
	if value == nil {
		*m = make(OnboardingMetadata)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		*m = make(OnboardingMetadata)
		return nil
	}
	
	return json.Unmarshal(bytes, m)
}

// Value implements driver.Valuer interface
func (m OnboardingMetadata) Value() (driver.Value, error) {
	if m == nil {
		return "{}", nil
	}
	return json.Marshal(m)
}

// OnboardingProgress represents a user's progress through the onboarding process
type OnboardingProgress struct {
	ID     uint `json:"id" gorm:"primaryKey"`
	UserID uint `json:"user_id" gorm:"not null;uniqueIndex" validate:"required"`

	// Progress Information
	Status OnboardingStatus `json:"status" gorm:"type:enum('pending','processing','completed');default:'pending';not null"`
	Step   OnboardingStep   `json:"step" gorm:"type:enum('create_tenant','create_website','completed');default:'create_tenant';not null"`

	// Progress Metadata
	StartedAt   *time.Time `json:"started_at,omitempty"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`

	// Additional Context
	Metadata OnboardingMetadata `json:"metadata,omitempty" gorm:"type:json"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations (commented out to avoid circular dependencies)
	// User *userModels.User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName sets the table name for OnboardingProgress
func (OnboardingProgress) TableName() string {
	return "onboarding_progress"
}

// BeforeCreate hook to set started_at when creating a new record
func (op *OnboardingProgress) BeforeCreate(tx *gorm.DB) error {
	if op.Status == OnboardingStatusProcessing && op.StartedAt == nil {
		now := time.Now()
		op.StartedAt = &now
	}
	return nil
}

// BeforeUpdate hook to set completed_at when status changes to completed
func (op *OnboardingProgress) BeforeUpdate(tx *gorm.DB) error {
	if op.Status == OnboardingStatusCompleted && op.CompletedAt == nil {
		now := time.Now()
		op.CompletedAt = &now
		op.Step = OnboardingStepCompleted
	}
	return nil
}

// IsCompleted returns true if the onboarding process is completed
func (op *OnboardingProgress) IsCompleted() bool {
	return op.Status == OnboardingStatusCompleted
}

// IsInProgress returns true if the onboarding process is in progress
func (op *OnboardingProgress) IsInProgress() bool {
	return op.Status == OnboardingStatusProcessing
}

// IsPending returns true if the onboarding process is pending
func (op *OnboardingProgress) IsPending() bool {
	return op.Status == OnboardingStatusPending
}

// GetCurrentStep returns the current step as a string
func (op *OnboardingProgress) GetCurrentStep() string {
	return string(op.Step)
}

// GetStatus returns the current status as a string
func (op *OnboardingProgress) GetStatus() string {
	return string(op.Status)
}

// MarkAsStarted marks the onboarding as started
func (op *OnboardingProgress) MarkAsStarted() {
	if op.Status == OnboardingStatusPending {
		op.Status = OnboardingStatusProcessing
		if op.StartedAt == nil {
			now := time.Now()
			op.StartedAt = &now
		}
	}
}

// MarkAsCompleted marks the onboarding as completed
func (op *OnboardingProgress) MarkAsCompleted() {
	op.Status = OnboardingStatusCompleted
	op.Step = OnboardingStepCompleted
	if op.CompletedAt == nil {
		now := time.Now()
		op.CompletedAt = &now
	}
}

// AdvanceToNextStep advances to the next step in the onboarding process
func (op *OnboardingProgress) AdvanceToNextStep() {
	switch op.Step {
	case OnboardingStepCreateTenant:
		op.Step = OnboardingStepCreateWebsite
	case OnboardingStepCreateWebsite:
		op.MarkAsCompleted()
	}
}

// SetMetadata sets a metadata value
func (op *OnboardingProgress) SetMetadata(key string, value interface{}) {
	if op.Metadata == nil {
		op.Metadata = make(OnboardingMetadata)
	}
	op.Metadata[key] = value
}

// GetMetadata gets a metadata value
func (op *OnboardingProgress) GetMetadata(key string) (interface{}, bool) {
	if op.Metadata == nil {
		return nil, false
	}
	value, exists := op.Metadata[key]
	return value, exists
}

// GetMetadataString gets a metadata value as string
func (op *OnboardingProgress) GetMetadataString(key string) string {
	if value, exists := op.GetMetadata(key); exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// GetMetadataBool gets a metadata value as boolean
func (op *OnboardingProgress) GetMetadataBool(key string) bool {
	if value, exists := op.GetMetadata(key); exists {
		if b, ok := value.(bool); ok {
			return b
		}
	}
	return false
}
