package onboarding

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/handlers"
	onboardingrepos "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories"
	onboardingmysql "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all onboarding module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger) {
	// Initialize repositories
	progressRepo := onboardingmysql.NewOnboardingProgressRepository(db, logger)

	// Initialize services
	onboardingService := services.NewOnboardingService(
		progressRepo,
		logger,
		&services.OnboardingConfig{
			AutoStart:           true,
			RequiredSteps:       []string{"create_tenant", "create_website"},
			OptionalSteps:       []string{},
			DefaultTimeout:      7 * 24 * time.Hour, // 7 days
			AllowSkip:           false,
			EnableNotifications: true,
		},
	)

	// Initialize handlers
	onboardingHandler := handlers.NewOnboardingHandler(
		onboardingService,
		v,
		logger,
	)

	// Create onboarding routes group
	onboardingRoutes := router.Group("/onboarding")
	onboardingRoutes.Use(httpmiddleware.EnhancedCORSMiddleware())
	onboardingRoutes.Use(httpmiddleware.SecurityHeadersMiddleware())
	onboardingRoutes.Use(httpmiddleware.AuthRateLimitingMiddleware(logger))
	onboardingRoutes.Use(httpmiddleware.ValidateJSONContentType())
	onboardingRoutes.Use(httpmiddleware.TenantContextMiddleware())
	onboardingRoutes.Use(httpmiddleware.RequestLoggingMiddleware(logger))

	// Public onboarding endpoints (require authentication but not specific roles)
	publicRoutes := onboardingRoutes.Group("")
	// Note: JWT middleware should be added by the parent router if authentication is required
	{
		// Progress management endpoints
		publicRoutes.GET("/progress", onboardingHandler.GetProgress)
		publicRoutes.POST("/start", onboardingHandler.StartOnboarding)
		publicRoutes.PUT("/step", onboardingHandler.UpdateStep)
		publicRoutes.POST("/complete", onboardingHandler.CompleteOnboarding)
		publicRoutes.POST("/reset", onboardingHandler.ResetOnboarding)

		// Utility endpoints
		publicRoutes.GET("/required", onboardingHandler.IsOnboardingRequired)
		publicRoutes.GET("/step", onboardingHandler.GetCurrentStep)
	}

	// Admin onboarding endpoints (require admin role)
	adminRoutes := onboardingRoutes.Group("/admin")
	adminRoutes.Use(httpmiddleware.RequireAdminRole())
	{
		// Administrative endpoints
		adminRoutes.GET("/progress", onboardingHandler.ListProgress)
		adminRoutes.GET("/stats", onboardingHandler.GetStats)
	}

	// Health check for onboarding module
	onboardingRoutes.GET("/health", func(c *gin.Context) {
		httpresponse.Success(c.Writer, gin.H{
			"module":  "onboarding",
			"status":  "healthy",
			"version": "1.0.0",
			"checks": gin.H{
				"database":           db != nil,
				"progress_repo":      progressRepo != nil,
				"onboarding_service": onboardingService != nil,
			},
		})
	})
}

// GetOnboardingService creates and returns an onboarding service instance
// This function can be used by other modules that need to interact with onboarding
func GetOnboardingService(db *gorm.DB, logger utils.Logger) services.OnboardingService {
	progressRepo := onboardingmysql.NewOnboardingProgressRepository(db, logger)

	return services.NewOnboardingService(
		progressRepo,
		logger,
		&services.OnboardingConfig{
			AutoStart:           true,
			RequiredSteps:       []string{"create_tenant", "create_website"},
			OptionalSteps:       []string{},
			DefaultTimeout:      7 * 24 * time.Hour, // 7 days
			AllowSkip:           false,
			EnableNotifications: true,
		},
	)
}

// GetOnboardingRepository creates and returns an onboarding progress repository instance
// This function can be used by other modules that need direct repository access
func GetOnboardingRepository(db *gorm.DB, logger utils.Logger) onboardingrepos.OnboardingProgressRepository {
	return onboardingmysql.NewOnboardingProgressRepository(db, logger)
}
