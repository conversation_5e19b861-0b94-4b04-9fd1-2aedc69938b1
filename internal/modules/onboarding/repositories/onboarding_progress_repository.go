package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/models"
)

// OnboardingProgressRepository defines the interface for onboarding progress database operations
type OnboardingProgressRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, progress *models.OnboardingProgress) error
	GetByID(ctx context.Context, id uint) (*models.OnboardingProgress, error)
	GetByUserID(ctx context.Context, userID uint) (*models.OnboardingProgress, error)
	Update(ctx context.Context, progress *models.OnboardingProgress) error
	Delete(ctx context.Context, id uint) error

	// Status and step operations
	UpdateStatus(ctx context.Context, userID uint, status models.OnboardingStatus) error
	UpdateStep(ctx context.Context, userID uint, step models.OnboardingStep) error
	UpdateMetadata(ctx context.Context, userID uint, metadata models.OnboardingMetadata) error

	// Bulk operations
	GetByIDs(ctx context.Context, ids []uint) ([]*models.OnboardingProgress, error)
	GetByUserIDs(ctx context.Context, userIDs []uint) ([]*models.OnboardingProgress, error)

	// Search and filtering
	List(ctx context.Context, filters *OnboardingProgressFilters) ([]*models.OnboardingProgress, error)
	Count(ctx context.Context, filters *OnboardingProgressFilters) (int64, error)
	GetByStatus(ctx context.Context, status models.OnboardingStatus) ([]*models.OnboardingProgress, error)
	GetByStep(ctx context.Context, step models.OnboardingStep) ([]*models.OnboardingProgress, error)

	// Statistics
	GetStats(ctx context.Context) (*OnboardingStats, error)
	GetCompletionRate(ctx context.Context) (float64, error)

	// Existence checks
	Exists(ctx context.Context, userID uint) (bool, error)
	ExistsByID(ctx context.Context, id uint) (bool, error)
}

// OnboardingProgressFilters represents filters for querying onboarding progress
type OnboardingProgressFilters struct {
	Status    *models.OnboardingStatus `json:"status,omitempty"`
	Step      *models.OnboardingStep   `json:"step,omitempty"`
	UserIDs   []uint                   `json:"user_ids,omitempty"`
	StartDate *string                  `json:"start_date,omitempty"`
	EndDate   *string                  `json:"end_date,omitempty"`
	Limit     int                      `json:"limit,omitempty"`
	Offset    int                      `json:"offset,omitempty"`
	OrderBy   string                   `json:"order_by,omitempty"`
	OrderDir  string                   `json:"order_dir,omitempty"`
}

// OnboardingStats represents statistics about onboarding progress
type OnboardingStats struct {
	TotalUsers      uint    `json:"total_users"`
	PendingUsers    uint    `json:"pending_users"`
	ProcessingUsers uint    `json:"processing_users"`
	CompletedUsers  uint    `json:"completed_users"`
	CompletionRate  float64 `json:"completion_rate"`
}
