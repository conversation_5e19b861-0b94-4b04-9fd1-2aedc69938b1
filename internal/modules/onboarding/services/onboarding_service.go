package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// OnboardingService handles onboarding business logic
type OnboardingService interface {
	// Progress management
	GetProgress(ctx context.Context, userID uint) (*dto.GetOnboardingProgressResponse, error)
	StartOnboarding(ctx context.Context, userID uint) (*dto.StartOnboardingResponse, error)
	UpdateStep(ctx context.Context, req *dto.UpdateOnboardingStepRequest) (*dto.UpdateOnboardingStepResponse, error)
	CompleteOnboarding(ctx context.Context, req *dto.CompleteOnboardingRequest) (*dto.CompleteOnboardingResponse, error)
	ResetOnboarding(ctx context.Context, userID uint) (*dto.ResetOnboardingResponse, error)

	// Listing and statistics
	ListProgress(ctx context.Context, req *dto.ListOnboardingProgressRequest) (*dto.ListOnboardingProgressResponse, error)
	GetStats(ctx context.Context) (*dto.OnboardingStatsResponse, error)

	// Validation and utilities
	IsOnboardingRequired(ctx context.Context, userID uint) (bool, error)
	IsOnboardingCompleted(ctx context.Context, userID uint) (bool, error)
	GetCurrentStep(ctx context.Context, userID uint) (string, error)
}

type onboardingService struct {
	progressRepo repositories.OnboardingProgressRepository
	logger       utils.Logger
	config       *OnboardingConfig
}

// OnboardingConfig holds onboarding configuration
type OnboardingConfig struct {
	AutoStart           bool          `json:"auto_start"`
	RequiredSteps       []string      `json:"required_steps"`
	OptionalSteps       []string      `json:"optional_steps"`
	DefaultTimeout      time.Duration `json:"default_timeout"`
	AllowSkip           bool          `json:"allow_skip"`
	EnableNotifications bool          `json:"enable_notifications"`
}

// NewOnboardingService creates a new onboarding service
func NewOnboardingService(
	progressRepo repositories.OnboardingProgressRepository,
	logger utils.Logger,
	config *OnboardingConfig,
) OnboardingService {
	if config == nil {
		config = &OnboardingConfig{
			AutoStart:           true,
			RequiredSteps:       []string{"create_tenant", "create_website"},
			OptionalSteps:       []string{},
			DefaultTimeout:      7 * 24 * time.Hour, // 7 days
			AllowSkip:           false,
			EnableNotifications: true,
		}
	}

	return &onboardingService{
		progressRepo: progressRepo,
		logger:       logger,
		config:       config,
	}
}

// GetProgress retrieves onboarding progress for a user
func (s *onboardingService) GetProgress(ctx context.Context, userID uint) (*dto.GetOnboardingProgressResponse, error) {
	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get onboarding progress")
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	return &dto.GetOnboardingProgressResponse{
		ID:          progress.ID,
		UserID:      progress.UserID,
		Status:      progress.GetStatus(),
		Step:        progress.GetCurrentStep(),
		StartedAt:   progress.StartedAt,
		CompletedAt: progress.CompletedAt,
		Metadata:    progress.Metadata,
		CreatedAt:   progress.CreatedAt,
		UpdatedAt:   progress.UpdatedAt,
	}, nil
}

// StartOnboarding starts the onboarding process for a user
func (s *onboardingService) StartOnboarding(ctx context.Context, userID uint) (*dto.StartOnboardingResponse, error) {
	// Check if onboarding already exists
	exists, err := s.progressRepo.Exists(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to check if onboarding exists")
		return nil, fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	if exists {
		// Get existing progress
		progress, err := s.progressRepo.GetByUserID(ctx, userID)
		if err != nil {
			return nil, fmt.Errorf("failed to get existing onboarding progress: %w", err)
		}

		// If already completed, return error
		if progress.IsCompleted() {
			return nil, fmt.Errorf("onboarding already completed")
		}

		// If pending, start it
		if progress.IsPending() {
			progress.MarkAsStarted()
			if err := s.progressRepo.Update(ctx, progress); err != nil {
				return nil, fmt.Errorf("failed to start existing onboarding: %w", err)
			}
		}

		return &dto.StartOnboardingResponse{
			ID:        progress.ID,
			UserID:    progress.UserID,
			Status:    progress.GetStatus(),
			Step:      progress.GetCurrentStep(),
			StartedAt: *progress.StartedAt,
			Message:   "Onboarding started successfully",
		}, nil
	}

	// Create new onboarding progress
	now := time.Now()
	progress := &models.OnboardingProgress{
		UserID:    userID,
		Status:    models.OnboardingStatusProcessing,
		Step:      models.OnboardingStepCreateTenant,
		StartedAt: &now,
		Metadata: models.OnboardingMetadata{
			"started_at":      now.Format(time.RFC3339),
			"auto_started":    s.config.AutoStart,
			"required_steps":  s.config.RequiredSteps,
			"optional_steps":  s.config.OptionalSteps,
			"steps_completed": []string{},
		},
	}

	if err := s.progressRepo.Create(ctx, progress); err != nil {
		s.logger.WithError(err).Error("Failed to create onboarding progress")
		return nil, fmt.Errorf("failed to create onboarding progress: %w", err)
	}

	return &dto.StartOnboardingResponse{
		ID:        progress.ID,
		UserID:    progress.UserID,
		Status:    progress.GetStatus(),
		Step:      progress.GetCurrentStep(),
		StartedAt: *progress.StartedAt,
		Message:   "Onboarding started successfully",
	}, nil
}

// UpdateStep updates the current step of onboarding
func (s *onboardingService) UpdateStep(ctx context.Context, req *dto.UpdateOnboardingStepRequest) (*dto.UpdateOnboardingStepResponse, error) {
	progress, err := s.progressRepo.GetByUserID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	if progress.IsCompleted() {
		return nil, fmt.Errorf("onboarding already completed")
	}

	// Update step
	switch req.Step {
	case "create_tenant":
		progress.Step = models.OnboardingStepCreateTenant
	case "create_website":
		progress.Step = models.OnboardingStepCreateWebsite
	case "completed":
		progress.MarkAsCompleted()
	default:
		return nil, fmt.Errorf("invalid step: %s", req.Step)
	}

	// Update metadata if provided
	if req.Metadata != nil {
		for key, value := range req.Metadata {
			progress.SetMetadata(key, value)
		}
	}

	// Mark as processing if not already
	if progress.IsPending() {
		progress.MarkAsStarted()
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return nil, fmt.Errorf("failed to update onboarding step: %w", err)
	}

	return &dto.UpdateOnboardingStepResponse{
		ID:          progress.ID,
		UserID:      progress.UserID,
		Status:      progress.GetStatus(),
		Step:        progress.GetCurrentStep(),
		StartedAt:   progress.StartedAt,
		CompletedAt: progress.CompletedAt,
		Metadata:    progress.Metadata,
		UpdatedAt:   progress.UpdatedAt,
		Message:     fmt.Sprintf("Onboarding step updated to %s", req.Step),
	}, nil
}

// CompleteOnboarding marks the onboarding as completed
func (s *onboardingService) CompleteOnboarding(ctx context.Context, req *dto.CompleteOnboardingRequest) (*dto.CompleteOnboardingResponse, error) {
	progress, err := s.progressRepo.GetByUserID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	if progress.IsCompleted() {
		return nil, fmt.Errorf("onboarding already completed")
	}

	// Mark as completed
	progress.MarkAsCompleted()

	// Update metadata if provided
	if req.Metadata != nil {
		for key, value := range req.Metadata {
			progress.SetMetadata(key, value)
		}
	}

	// Add completion metadata
	progress.SetMetadata("completed_at", time.Now().Format(time.RFC3339))
	progress.SetMetadata("completion_method", "manual")

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return nil, fmt.Errorf("failed to complete onboarding: %w", err)
	}

	return &dto.CompleteOnboardingResponse{
		ID:          progress.ID,
		UserID:      progress.UserID,
		Status:      progress.GetStatus(),
		Step:        progress.GetCurrentStep(),
		StartedAt:   progress.StartedAt,
		CompletedAt: *progress.CompletedAt,
		Metadata:    progress.Metadata,
		Message:     "Onboarding completed successfully",
	}, nil
}

// ResetOnboarding resets the onboarding process for a user
func (s *onboardingService) ResetOnboarding(ctx context.Context, userID uint) (*dto.ResetOnboardingResponse, error) {
	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	// Reset to initial state
	progress.Status = models.OnboardingStatusPending
	progress.Step = models.OnboardingStepCreateTenant
	progress.StartedAt = nil
	progress.CompletedAt = nil
	progress.Metadata = models.OnboardingMetadata{
		"reset_at":        time.Now().Format(time.RFC3339),
		"required_steps":  s.config.RequiredSteps,
		"optional_steps":  s.config.OptionalSteps,
		"steps_completed": []string{},
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return nil, fmt.Errorf("failed to reset onboarding: %w", err)
	}

	return &dto.ResetOnboardingResponse{
		ID:      progress.ID,
		UserID:  progress.UserID,
		Status:  progress.GetStatus(),
		Step:    progress.GetCurrentStep(),
		Message: "Onboarding reset successfully",
	}, nil
}

// ListProgress lists onboarding progress with filters
func (s *onboardingService) ListProgress(ctx context.Context, req *dto.ListOnboardingProgressRequest) (*dto.ListOnboardingProgressResponse, error) {
	filters := &repositories.OnboardingProgressFilters{
		Limit:  req.Limit,
		Offset: req.Offset,
	}

	if req.Status != "" {
		status := models.OnboardingStatus(req.Status)
		filters.Status = &status
	}

	if req.Step != "" {
		step := models.OnboardingStep(req.Step)
		filters.Step = &step
	}

	// Set default limit if not provided
	if filters.Limit == 0 {
		filters.Limit = 10
	}

	progressList, err := s.progressRepo.List(ctx, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list onboarding progress: %w", err)
	}

	total, err := s.progressRepo.Count(ctx, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to count onboarding progress: %w", err)
	}

	// Convert to DTOs
	progressDTOs := make([]dto.GetOnboardingProgressResponse, len(progressList))
	for i, progress := range progressList {
		progressDTOs[i] = dto.GetOnboardingProgressResponse{
			ID:          progress.ID,
			UserID:      progress.UserID,
			Status:      progress.GetStatus(),
			Step:        progress.GetCurrentStep(),
			StartedAt:   progress.StartedAt,
			CompletedAt: progress.CompletedAt,
			Metadata:    progress.Metadata,
			CreatedAt:   progress.CreatedAt,
			UpdatedAt:   progress.UpdatedAt,
		}
	}

	return &dto.ListOnboardingProgressResponse{
		Progress: progressDTOs,
		Total:    total,
		Limit:    req.Limit,
		Offset:   req.Offset,
	}, nil
}

// GetStats retrieves onboarding statistics
func (s *onboardingService) GetStats(ctx context.Context) (*dto.OnboardingStatsResponse, error) {
	stats, err := s.progressRepo.GetStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding stats: %w", err)
	}

	return &dto.OnboardingStatsResponse{
		TotalUsers:      stats.TotalUsers,
		PendingUsers:    stats.PendingUsers,
		ProcessingUsers: stats.ProcessingUsers,
		CompletedUsers:  stats.CompletedUsers,
		CompletionRate:  stats.CompletionRate,
	}, nil
}

// IsOnboardingRequired checks if onboarding is required for a user
func (s *onboardingService) IsOnboardingRequired(ctx context.Context, userID uint) (bool, error) {
	exists, err := s.progressRepo.Exists(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	if !exists {
		return true, nil // No onboarding record means it's required
	}

	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	return !progress.IsCompleted(), nil
}

// IsOnboardingCompleted checks if onboarding is completed for a user
func (s *onboardingService) IsOnboardingCompleted(ctx context.Context, userID uint) (bool, error) {
	exists, err := s.progressRepo.Exists(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	if !exists {
		return false, nil // No onboarding record means not completed
	}

	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	return progress.IsCompleted(), nil
}

// GetCurrentStep retrieves the current step for a user
func (s *onboardingService) GetCurrentStep(ctx context.Context, userID uint) (string, error) {
	exists, err := s.progressRepo.Exists(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	if !exists {
		return string(models.OnboardingStepCreateTenant), nil // Default first step
	}

	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	return progress.GetCurrentStep(), nil
}
