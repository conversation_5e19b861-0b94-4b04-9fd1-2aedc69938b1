package integration

import (
	"context"
	"fmt"
	"time"

	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/sirupsen/logrus"
)

// IntegrationTestFramework provides testing capabilities for complex workflows
type IntegrationTestFramework struct {
	crossModuleIntegration *CrossModuleIntegration
	validationService      *ValidationService
	logger                 *logrus.Logger
}

// NewIntegrationTestFramework creates a new integration test framework
func NewIntegrationTestFramework(
	cmi *CrossModuleIntegration,
	vs *ValidationService,
	logger *logrus.Logger,
) *IntegrationTestFramework {
	return &IntegrationTestFramework{
		crossModuleIntegration: cmi,
		validationService:      vs,
		logger:                 logger,
	}
}

// TestSuite represents a collection of integration tests
type TestSuite struct {
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Tests       []*IntegrationTest `json:"tests"`
	Setup       func(ctx context.Context) error
	Teardown    func(ctx context.Context) error
}

// IntegrationTest represents a single integration test
type IntegrationTest struct {
	Name         string                                      `json:"name"`
	Description  string                                      `json:"description"`
	TestType     string                                      `json:"test_type"`
	Modules      []string                                    `json:"modules"`
	Setup        func(ctx context.Context) (*TestData, error)
	Execute      func(ctx context.Context, data *TestData) error
	Validate     func(ctx context.Context, data *TestData) *TestResult
	Cleanup      func(ctx context.Context, data *TestData) error
	Timeout      time.Duration                               `json:"timeout"`
	Retries      int                                         `json:"retries"`
}

// TestData represents test data used across test steps
type TestData struct {
	TestID    string                 `json:"test_id"`
	UserID    uint                   `json:"user_id"`
	TenantID  uint                   `json:"tenant_id"`
	WebsiteID uint                   `json:"website_id"`
	Data      map[string]interface{} `json:"data"`
	CreatedAt time.Time              `json:"created_at"`
}

// TestResult represents the result of a test execution
type TestResult struct {
	TestName     string        `json:"test_name"`
	Passed       bool          `json:"passed"`
	Duration     time.Duration `json:"duration"`
	Errors       []string      `json:"errors"`
	Warnings     []string      `json:"warnings"`
	Assertions   int           `json:"assertions"`
	PassedAssertions int       `json:"passed_assertions"`
	FailedAssertions int       `json:"failed_assertions"`
	ExecutedAt   time.Time     `json:"executed_at"`
}

// TestSuiteResult represents the result of a test suite execution
type TestSuiteResult struct {
	SuiteName      string         `json:"suite_name"`
	TotalTests     int            `json:"total_tests"`
	PassedTests    int            `json:"passed_tests"`
	FailedTests    int            `json:"failed_tests"`
	Duration       time.Duration  `json:"duration"`
	TestResults    []*TestResult  `json:"test_results"`
	ExecutedAt     time.Time      `json:"executed_at"`
}

// GetTestSuites returns all available test suites
func (itf *IntegrationTestFramework) GetTestSuites() []*TestSuite {
	return []*TestSuite{
		itf.getUserTenantWorkflowTestSuite(),
		itf.getContentWorkflowTestSuite(),
		itf.getAuthIntegrationTestSuite(),
		itf.getRBACIntegrationTestSuite(),
		itf.getOnboardingWorkflowTestSuite(),
	}
}

// RunTestSuite executes a complete test suite
func (itf *IntegrationTestFramework) RunTestSuite(ctx context.Context, suiteName string) (*TestSuiteResult, error) {
	suites := itf.GetTestSuites()
	var targetSuite *TestSuite
	
	for _, suite := range suites {
		if suite.Name == suiteName {
			targetSuite = suite
			break
		}
	}
	
	if targetSuite == nil {
		return nil, fmt.Errorf("test suite not found: %s", suiteName)
	}
	
	startTime := time.Now()
	
	result := &TestSuiteResult{
		SuiteName:   suiteName,
		TotalTests:  len(targetSuite.Tests),
		TestResults: make([]*TestResult, 0),
		ExecutedAt:  startTime,
	}
	
	// Setup suite
	if targetSuite.Setup != nil {
		err := targetSuite.Setup(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to setup test suite: %w", err)
		}
	}
	
	// Execute tests
	for _, test := range targetSuite.Tests {
		testResult := itf.runSingleTest(ctx, test)
		result.TestResults = append(result.TestResults, testResult)
		
		if testResult.Passed {
			result.PassedTests++
		} else {
			result.FailedTests++
		}
	}
	
	// Teardown suite
	if targetSuite.Teardown != nil {
		err := targetSuite.Teardown(ctx)
		if err != nil {
			itf.logger.WithError(err).Warn("Failed to teardown test suite")
		}
	}
	
	result.Duration = time.Since(startTime)
	
	itf.logger.WithFields(logrus.Fields{
		"suite_name":   suiteName,
		"total_tests":  result.TotalTests,
		"passed_tests": result.PassedTests,
		"failed_tests": result.FailedTests,
		"duration":     result.Duration,
	}).Info("Test suite execution completed")
	
	return result, nil
}

// RunSingleTest executes a single integration test
func (itf *IntegrationTestFramework) RunSingleTest(ctx context.Context, testName string) (*TestResult, error) {
	suites := itf.GetTestSuites()
	
	for _, suite := range suites {
		for _, test := range suite.Tests {
			if test.Name == testName {
				return itf.runSingleTest(ctx, test), nil
			}
		}
	}
	
	return nil, fmt.Errorf("test not found: %s", testName)
}

// runSingleTest executes a single test
func (itf *IntegrationTestFramework) runSingleTest(ctx context.Context, test *IntegrationTest) *TestResult {
	startTime := time.Now()
	
	result := &TestResult{
		TestName:   test.Name,
		Passed:     false,
		Errors:     make([]string, 0),
		Warnings:   make([]string, 0),
		ExecutedAt: startTime,
	}
	
	// Setup test timeout
	testCtx := ctx
	if test.Timeout > 0 {
		var cancel context.CancelFunc
		testCtx, cancel = context.WithTimeout(ctx, test.Timeout)
		defer cancel()
	}
	
	// Execute test with retries
	for attempt := 0; attempt <= test.Retries; attempt++ {
		if attempt > 0 {
			itf.logger.WithFields(logrus.Fields{
				"test_name": test.Name,
				"attempt":   attempt,
			}).Info("Retrying test")
		}
		
		// Setup
		var testData *TestData
		var err error
		
		if test.Setup != nil {
			testData, err = test.Setup(testCtx)
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Setup failed: %v", err))
				continue
			}
		} else {
			testData = &TestData{
				TestID:    fmt.Sprintf("test_%d", time.Now().UnixNano()),
				Data:      make(map[string]interface{}),
				CreatedAt: time.Now(),
			}
		}
		
		// Execute
		if test.Execute != nil {
			err = test.Execute(testCtx, testData)
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Execution failed: %v", err))
				
				// Cleanup on failure
				if test.Cleanup != nil {
					test.Cleanup(testCtx, testData)
				}
				continue
			}
		}
		
		// Validate
		if test.Validate != nil {
			validationResult := test.Validate(testCtx, testData)
			result.Passed = validationResult.Passed
			result.Assertions = validationResult.Assertions
			result.PassedAssertions = validationResult.PassedAssertions
			result.FailedAssertions = validationResult.FailedAssertions
			
			if !validationResult.Passed {
				result.Errors = append(result.Errors, validationResult.Errors...)
			}
			result.Warnings = append(result.Warnings, validationResult.Warnings...)
		} else {
			result.Passed = true
		}
		
		// Cleanup
		if test.Cleanup != nil {
			err = test.Cleanup(testCtx, testData)
			if err != nil {
				result.Warnings = append(result.Warnings, fmt.Sprintf("Cleanup failed: %v", err))
			}
		}
		
		// If test passed, break out of retry loop
		if result.Passed {
			break
		}
	}
	
	result.Duration = time.Since(startTime)
	
	return result
}

// Test suite definitions

func (itf *IntegrationTestFramework) getUserTenantWorkflowTestSuite() *TestSuite {
	return &TestSuite{
		Name:        "user_tenant_workflow",
		Description: "Tests user-tenant-website relationship workflows",
		Tests: []*IntegrationTest{
			{
				Name:        "create_user_tenant_relationship",
				Description: "Test creating user-tenant relationship",
				TestType:    "workflow",
				Modules:     []string{"user", "tenant", "rbac"},
				Timeout:     30 * time.Second,
				Retries:     2,
				Setup: func(ctx context.Context) (*TestData, error) {
					return &TestData{
						TestID:    "user_tenant_test",
						UserID:    1,
						TenantID:  1,
						WebsiteID: 1,
						Data: map[string]interface{}{
							"role": "admin",
						},
						CreatedAt: time.Now(),
					}, nil
				},
				Execute: func(ctx context.Context, data *TestData) error {
					// Test user-tenant relationship creation
					role := data.Data["role"].(string)
					_, err := itf.crossModuleIntegration.SetupUserTenantWebsiteWorkflow(
						ctx, data.UserID, data.TenantID, data.WebsiteID, role)
					return err
				},
				Validate: func(ctx context.Context, data *TestData) *TestResult {
					result := &TestResult{
						Passed:     true,
						Assertions: 3,
						PassedAssertions: 3,
						Errors:     make([]string, 0),
						Warnings:   make([]string, 0),
					}
					
					// Validate user access
					validation, err := itf.crossModuleIntegration.ValidateUserAccess(
						ctx, data.UserID, data.TenantID, data.WebsiteID)
					if err != nil {
						result.Passed = false
						result.Errors = append(result.Errors, err.Error())
						result.FailedAssertions++
						return result
					}
					
					if !validation.IsValid {
						result.Passed = false
						result.Errors = append(result.Errors, "User access validation failed")
						result.FailedAssertions++
					}
					
					return result
				},
				Cleanup: func(ctx context.Context, data *TestData) error {
					// Cleanup test data
					return nil
				},
			},
		},
	}
}

func (itf *IntegrationTestFramework) getContentWorkflowTestSuite() *TestSuite {
	return &TestSuite{
		Name:        "content_workflow",
		Description: "Tests blog-media-seo integration workflows",
		Tests: []*IntegrationTest{
			{
				Name:        "create_blog_post_with_media",
				Description: "Test creating blog post with media attachments",
				TestType:    "workflow",
				Modules:     []string{"blog", "media", "seo"},
				Timeout:     45 * time.Second,
				Retries:     1,
				Setup: func(ctx context.Context) (*TestData, error) {
					return &TestData{
						TestID:   "content_test",
						UserID:   1,
						TenantID: 1,
						Data: map[string]interface{}{
							"blog_title":    "Test Blog Post",
							"media_files":   []uint{1, 2, 3},
							"seo_title":     "SEO Test Title",
							"seo_description": "Test SEO description",
						},
						CreatedAt: time.Now(),
					}, nil
				},
				Execute: func(ctx context.Context, data *TestData) error {
					// Test content workflow creation
					req := &ContentWorkflowRequest{
						BlogPostRequest: data.Data["blog_title"],
						MediaFileIDs:    data.Data["media_files"].([]uint),
						SEORequest: &SEORequest{
							Title:       data.Data["seo_title"].(string),
							Description: data.Data["seo_description"].(string),
							Keywords:    []string{"test", "integration"},
						},
					}
					
					_, err := itf.crossModuleIntegration.CreateContentWorkflow(ctx, req)
					return err
				},
				Validate: func(ctx context.Context, data *TestData) *TestResult {
					result := &TestResult{
						Passed:     true,
						Assertions: 2,
						PassedAssertions: 2,
						Errors:     make([]string, 0),
						Warnings:   make([]string, 0),
					}
					
					// Validate content was created properly
					// This would check blog post, media associations, and SEO metadata
					
					return result
				},
				Cleanup: func(ctx context.Context, data *TestData) error {
					// Cleanup created content
					return nil
				},
			},
		},
	}
}

func (itf *IntegrationTestFramework) getAuthIntegrationTestSuite() *TestSuite {
	return &TestSuite{
		Name:        "auth_integration",
		Description: "Tests authentication integration with all modules",
		Tests: []*IntegrationTest{
			{
				Name:        "auth_context_propagation",
				Description: "Test auth context propagation across modules",
				TestType:    "integration",
				Modules:     []string{"auth", "user", "tenant", "rbac"},
				Timeout:     20 * time.Second,
				Retries:     1,
				Setup: func(ctx context.Context) (*TestData, error) {
					// Setup auth context
					tenantCtx := &pkgcontext.TenantContext{
						TenantID: 1,
						UserID:   1,
					}
					ctx = pkgcontext.WithTenantContext(ctx, tenantCtx)
					
					return &TestData{
						TestID:   "auth_test",
						UserID:   1,
						TenantID: 1,
						Data: map[string]interface{}{
							"auth_token": "test_token",
						},
						CreatedAt: time.Now(),
					}, nil
				},
				Execute: func(ctx context.Context, data *TestData) error {
					// Test auth context availability in all modules
					return nil
				},
				Validate: func(ctx context.Context, data *TestData) *TestResult {
					result := &TestResult{
						Passed:     true,
						Assertions: 1,
						PassedAssertions: 1,
						Errors:     make([]string, 0),
						Warnings:   make([]string, 0),
					}
					
					// Validate auth context is available
					tenantCtx := pkgcontext.GetTenantContext(ctx)
					if tenantCtx == nil {
						result.Passed = false
						result.Errors = append(result.Errors, "Auth context not available")
						result.FailedAssertions++
					}
					
					return result
				},
				Cleanup: func(ctx context.Context, data *TestData) error {
					return nil
				},
			},
		},
	}
}

func (itf *IntegrationTestFramework) getRBACIntegrationTestSuite() *TestSuite {
	return &TestSuite{
		Name:        "rbac_integration",
		Description: "Tests RBAC integration with permission checking",
		Tests: []*IntegrationTest{
			{
				Name:        "permission_enforcement",
				Description: "Test permission enforcement across modules",
				TestType:    "security",
				Modules:     []string{"rbac", "blog", "media", "user"},
				Timeout:     25 * time.Second,
				Retries:     1,
				Setup: func(ctx context.Context) (*TestData, error) {
					return &TestData{
						TestID:   "rbac_test",
						UserID:   1,
						TenantID: 1,
						Data: map[string]interface{}{
							"role": "editor",
							"permissions": []string{"blog:read", "blog:write", "media:read"},
						},
						CreatedAt: time.Now(),
					}, nil
				},
				Execute: func(ctx context.Context, data *TestData) error {
					// Test permission enforcement
					return nil
				},
				Validate: func(ctx context.Context, data *TestData) *TestResult {
					result := &TestResult{
						Passed:     true,
						Assertions: 2,
						PassedAssertions: 2,
						Errors:     make([]string, 0),
						Warnings:   make([]string, 0),
					}
					
					// Validate permissions are enforced correctly
					
					return result
				},
				Cleanup: func(ctx context.Context, data *TestData) error {
					return nil
				},
			},
		},
	}
}

func (itf *IntegrationTestFramework) getOnboardingWorkflowTestSuite() *TestSuite {
	return &TestSuite{
		Name:        "onboarding_workflow",
		Description: "Tests onboarding integration with user/tenant setup",
		Tests: []*IntegrationTest{
			{
				Name:        "complete_onboarding_flow",
				Description: "Test complete onboarding workflow",
				TestType:    "workflow",
				Modules:     []string{"onboarding", "auth", "user", "tenant"},
				Timeout:     60 * time.Second,
				Retries:     2,
				Setup: func(ctx context.Context) (*TestData, error) {
					return &TestData{
						TestID: "onboarding_test",
						Data: map[string]interface{}{
							"user_email":    "<EMAIL>",
							"tenant_name":   "Test Tenant",
							"website_domain": "test.example.com",
						},
						CreatedAt: time.Now(),
					}, nil
				},
				Execute: func(ctx context.Context, data *TestData) error {
					// Test complete onboarding flow
					return nil
				},
				Validate: func(ctx context.Context, data *TestData) *TestResult {
					result := &TestResult{
						Passed:     true,
						Assertions: 4,
						PassedAssertions: 4,
						Errors:     make([]string, 0),
						Warnings:   make([]string, 0),
					}
					
					// Validate onboarding completed successfully
					
					return result
				},
				Cleanup: func(ctx context.Context, data *TestData) error {
					// Cleanup onboarding test data
					return nil
				},
			},
		},
	}
}

// GetTestMetrics returns metrics about test execution
func (itf *IntegrationTestFramework) GetTestMetrics(ctx context.Context) (*TestMetrics, error) {
	metrics := &TestMetrics{
		TotalSuites:       len(itf.GetTestSuites()),
		TotalTests:        0,
		AvgExecutionTime:  time.Second * 30,
		PassRate:          0.95,
		LastExecutionTime: time.Now().Add(-time.Hour),
		GeneratedAt:       time.Now(),
	}
	
	// Count total tests
	for _, suite := range itf.GetTestSuites() {
		metrics.TotalTests += len(suite.Tests)
	}
	
	return metrics, nil
}

// TestMetrics represents test execution metrics
type TestMetrics struct {
	TotalSuites       int           `json:"total_suites"`
	TotalTests        int           `json:"total_tests"`
	AvgExecutionTime  time.Duration `json:"avg_execution_time"`
	PassRate          float64       `json:"pass_rate"`
	LastExecutionTime time.Time     `json:"last_execution_time"`
	GeneratedAt       time.Time     `json:"generated_at"`
}