package config

import (
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/database"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig    `json:"server"`
	Database database.Config `json:"database"`
	Log      LogConfig       `json:"log"`
	Auth     AuthConfig      `json:"auth"`
	Storage  StorageConfig   `json:"storage"`
	Features FeatureConfig   `json:"features"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port            int      `json:"port"`
	Host            string   `json:"host"`
	ReadTimeout     int      `json:"read_timeout"`
	WriteTimeout    int      `json:"write_timeout"`
	IdleTimeout     int      `json:"idle_timeout"`
	AllowedOrigins  []string `json:"allowed_origins"`
	AllowedMethods  []string `json:"allowed_methods"`
	AllowedHeaders  []string `json:"allowed_headers"`
	TrustedProxies  []string `json:"trusted_proxies"`
	EnableProfiling bool     `json:"enable_profiling"`
}

// LogConfig holds logging configuration
type LogConfig struct {
	Level  string `json:"level"`
	Format string `json:"format"`
	Output string `json:"output"`
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	JWTSecret           string        `json:"jwt_secret"`
	JWTIssuer           string        `json:"jwt_issuer"`
	JWTExpirationHours  int           `json:"jwt_expiration_hours"`
	RefreshTokenDays    int           `json:"refresh_token_days"`
	AccessTokenTTL      time.Duration `json:"access_token_ttl"`
	RefreshTokenTTL     time.Duration `json:"refresh_token_ttl"`
	SessionTimeoutHours int           `json:"session_timeout_hours"`
	EnableTwoFactor     bool          `json:"enable_two_factor"`
	PasswordMinLength   int           `json:"password_min_length"`
	MaxLoginAttempts    int           `json:"max_login_attempts"`
	LockoutDurationMins int           `json:"lockout_duration_mins"`
	RequireEmailVerification bool     `json:"require_email_verification"`
	SkipOnboarding      bool          `json:"skip_onboarding"`
}

// StorageConfig holds storage configuration
type StorageConfig struct {
	Provider    string `json:"provider"` // local, s3, gcs
	LocalPath   string `json:"local_path"`
	S3Bucket    string `json:"s3_bucket"`
	S3Region    string `json:"s3_region"`
	S3AccessKey string `json:"s3_access_key"`
	S3SecretKey string `json:"s3_secret_key"`
	GCSBucket   string `json:"gcs_bucket"`
	GCSKeyFile  string `json:"gcs_key_file"`
	MaxFileSize int64  `json:"max_file_size"` // bytes
}

// FeatureConfig holds feature flags
type FeatureConfig struct {
	EnableRegistration  bool                  `json:"enable_registration"`
	EnableComments      bool                  `json:"enable_comments"`
	EnableNotifications bool                  `json:"enable_notifications"`
	EnableAnalytics     bool                  `json:"enable_analytics"`
	EnableCache         bool                  `json:"enable_cache"`
	EnableRateLimit     bool                  `json:"enable_rate_limit"`
	EnableSwagger       bool                  `json:"enable_swagger"`
	EnableMetrics       bool                  `json:"enable_metrics"`
	EnableHealth        bool                  `json:"enable_health"`
	BlogScheduler       BlogSchedulerConfig   `json:"blog_scheduler"`
}

// BlogSchedulerConfig holds blog scheduler configuration
type BlogSchedulerConfig struct {
	Enabled       bool          `json:"enabled"`
	Interval      time.Duration `json:"interval"`
	BatchSize     int           `json:"batch_size"`
	RetryAttempts int           `json:"retry_attempts"`
	RetryDelay    time.Duration `json:"retry_delay"`
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	cfg := &Config{
		Server: ServerConfig{
			Port:            getEnvAsInt("APP_PORT", 9077),
			Host:            getEnv("APP_HOST", "0.0.0.0"),
			ReadTimeout:     getEnvAsInt("READ_timeout", 10),
			WriteTimeout:    getEnvAsInt("write_timeout", 10),
			IdleTimeout:     getEnvAsInt("idle_timeout", 60),
			AllowedOrigins:  getEnvAsSlice("ALLOWED_ORIGINS", []string{"*"}),
			AllowedMethods:  getEnvAsSlice("ALLOWED_METHODS", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}),
			AllowedHeaders:  getEnvAsSlice("ALLOWED_HEADERS", []string{"*"}),
			TrustedProxies:  getEnvAsSlice("TRUSTED_PROXIES", []string{}),
			EnableProfiling: getEnvAsBool("ENABLE_PROFILING", false),
		},
		Database: database.Config{
			Host:            getEnv("DB_HOST", "localhost"),
			Port:            getEnvAsInt("DB_PORT", 3306),
			Database:        getEnv("DB_NAME", "blog_api_v3"),
			Username:        getEnv("DB_USER", "root"),
			Password:        getEnv("DB_PASSWORD", ""),
			SSLMode:         getEnv("DB_SSL_MODE", "disable"),
			Charset:         getEnv("DB_CHARSET", "utf8mb4"),
			MaxIdleConns:    getEnvAsInt("DB_MAX_IDLE_CONNS", 10),
			MaxOpenConns:    getEnvAsInt("DB_MAX_OPEN_CONNS", 100),
			ConnMaxLifetime: time.Duration(getEnvAsInt("DB_CONN_MAX_LIFETIME", 3600)) * time.Second,
			MigrationsPath:  getEnv("DB_MIGRATIONS_PATH", "./migrations"),
			ParseTime:       true,
			Loc:             getEnv("DB_TIMEZONE", "UTC"),
			DebugSQL:        getEnvAsBool("DB_DEBUG_SQL", false),
		},
		Log: LogConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
			Output: getEnv("LOG_OUTPUT", "stdout"),
		},
		Auth: AuthConfig{
			JWTSecret:           getEnv("JWT_SECRET", "your-secret-key-here"),
			JWTIssuer:           getEnv("JWT_ISSUER", "blog-api-v3"),
			JWTExpirationHours:  getEnvAsInt("JWT_EXPIRATION_HOURS", 24),
			RefreshTokenDays:    getEnvAsInt("REFRESH_TOKEN_DAYS", 30),
			AccessTokenTTL:      time.Duration(getEnvAsInt("JWT_EXPIRATION_HOURS", 24)) * time.Hour,
			RefreshTokenTTL:     time.Duration(getEnvAsInt("REFRESH_TOKEN_DAYS", 30)) * 24 * time.Hour,
			SessionTimeoutHours: getEnvAsInt("SESSION_TIMEOUT_HOURS", 24),
			EnableTwoFactor:     getEnvAsBool("ENABLE_TWO_FACTOR", false),
			PasswordMinLength:   getEnvAsInt("PASSWORD_MIN_LENGTH", 8),
			MaxLoginAttempts:    getEnvAsInt("MAX_LOGIN_ATTEMPTS", 5),
			LockoutDurationMins: getEnvAsInt("LOCKOUT_DURATION_MINS", 30),
			RequireEmailVerification: getEnvAsBool("REQUIRE_EMAIL_VERIFICATION", true),
			SkipOnboarding:      getEnvAsBool("SKIP_ONBOARDING", false),
		},
		Storage: StorageConfig{
			Provider:    getEnv("STORAGE_PROVIDER", "local"),
			LocalPath:   getEnv("STORAGE_LOCAL_PATH", "./uploads"),
			S3Bucket:    getEnv("S3_BUCKET", ""),
			S3Region:    getEnv("S3_REGION", "us-east-1"),
			S3AccessKey: getEnv("S3_ACCESS_KEY", ""),
			S3SecretKey: getEnv("S3_SECRET_KEY", ""),
			GCSBucket:   getEnv("GCS_BUCKET", ""),
			GCSKeyFile:  getEnv("GCS_KEY_FILE", ""),
			MaxFileSize: getEnvAsInt64("MAX_FILE_SIZE", 10*1024*1024), // 10MB
		},
		Features: FeatureConfig{
			EnableRegistration:  getEnvAsBool("ENABLE_REGISTRATION", true),
			EnableComments:      getEnvAsBool("ENABLE_COMMENTS", true),
			EnableNotifications: getEnvAsBool("ENABLE_NOTIFICATIONS", true),
			EnableAnalytics:     getEnvAsBool("ENABLE_ANALYTICS", true),
			EnableCache:         getEnvAsBool("ENABLE_CACHE", true),
			EnableRateLimit:     getEnvAsBool("ENABLE_RATE_LIMIT", true),
			EnableSwagger:       getEnvAsBool("ENABLE_SWAGGER", true),
			EnableMetrics:       getEnvAsBool("ENABLE_METRICS", true),
			EnableHealth:        getEnvAsBool("ENABLE_HEALTH", true),
			BlogScheduler: BlogSchedulerConfig{
				Enabled:       getEnvAsBool("BLOG_SCHEDULER_ENABLED", true),
				Interval:      time.Duration(getEnvAsInt("BLOG_SCHEDULER_INTERVAL", 60)) * time.Second,
				BatchSize:     getEnvAsInt("BLOG_SCHEDULER_BATCH_SIZE", 100),
				RetryAttempts: getEnvAsInt("BLOG_SCHEDULER_RETRY_ATTEMPTS", 3),
				RetryDelay:    time.Duration(getEnvAsInt("BLOG_SCHEDULER_RETRY_DELAY", 300)) * time.Second,
			},
		},
	}

	return cfg, nil
}

// Helper functions to read environment variables

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	valueStr := getEnv(key, "")
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultValue
}

func getEnvAsInt64(key string, defaultValue int64) int64 {
	valueStr := getEnv(key, "")
	if value, err := strconv.ParseInt(valueStr, 10, 64); err == nil {
		return value
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	valueStr := getEnv(key, "")
	if value, err := strconv.ParseBool(valueStr); err == nil {
		return value
	}
	return defaultValue
}

func getEnvAsSlice(key string, defaultValue []string) []string {
	valueStr := getEnv(key, "")
	if valueStr == "" {
		return defaultValue
	}

	values := strings.Split(valueStr, ",")
	for i, v := range values {
		values[i] = strings.TrimSpace(v)
	}

	return values
}
