package tracing

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/tracing"
)

// HealthStatus represents the health status of the tracing system
type HealthStatus struct {
	Status      string                 `json:"status"`
	Enabled     bool                   `json:"enabled"`
	Service     string                 `json:"service"`
	Version     string                 `json:"version"`
	Environment string                 `json:"environment"`
	Tracer      TracerHealth           `json:"tracer"`
	Endpoint    EndpointHealth         `json:"endpoint"`
	Metrics     TracingMetrics         `json:"metrics"`
	Timestamp   time.Time              `json:"timestamp"`
	Details     map[string]interface{} `json:"details"`
}

// TracerHealth represents the health of the tracer
type TracerHealth struct {
	Initialized bool   `json:"initialized"`
	Type        string `json:"type"`
	Name        string `json:"name"`
	Status      string `json:"status"`
}

// EndpointHealth represents the health of the tracing endpoint
type EndpointHealth struct {
	URL         string `json:"url"`
	Reachable   bool   `json:"reachable"`
	ResponseTime int64  `json:"response_time_ms"`
	LastCheck   time.Time `json:"last_check"`
}

// TracingMetrics represents basic tracing metrics
type TracingMetrics struct {
	SpansCreated   int64   `json:"spans_created"`
	SpansExported  int64   `json:"spans_exported"`
	SpansDropped   int64   `json:"spans_dropped"`
	SampleRate     float64 `json:"sample_rate"`
	ErrorRate      float64 `json:"error_rate"`
	AverageLatency float64 `json:"average_latency_ms"`
}

// HealthChecker provides health checking functionality for tracing
type HealthChecker struct {
	lastCheck     time.Time
	lastStatus    *HealthStatus
	checkInterval time.Duration
}

// NewHealthChecker creates a new health checker
func NewHealthChecker() *HealthChecker {
	return &HealthChecker{
		checkInterval: 30 * time.Second,
	}
}

// Check performs a comprehensive health check of the tracing system
func (hc *HealthChecker) Check(ctx context.Context) *HealthStatus {
	// Check if we need to perform a new check
	if time.Since(hc.lastCheck) < hc.checkInterval && hc.lastStatus != nil {
		return hc.lastStatus
	}

	status := &HealthStatus{
		Status:      "healthy",
		Enabled:     IsEnabled(),
		Service:     GetServiceName(),
		Version:     GetServiceVersion(),
		Environment: getEnvironment(),
		Timestamp:   time.Now(),
		Details:     make(map[string]interface{}),
	}

	// Check tracer health
	status.Tracer = hc.checkTracerHealth(ctx)

	// Check endpoint health
	status.Endpoint = hc.checkEndpointHealth(ctx)

	// Get metrics
	status.Metrics = hc.getMetrics(ctx)

	// Determine overall status
	status.Status = hc.determineOverallStatus(status)

	// Update cache
	hc.lastCheck = time.Now()
	hc.lastStatus = status

	return status
}

// checkTracerHealth checks the health of the tracer
func (hc *HealthChecker) checkTracerHealth(ctx context.Context) TracerHealth {
	health := TracerHealth{
		Initialized: false,
		Type:        "unknown",
		Name:        "unknown",
		Status:      "unhealthy",
	}

	// Check if global tracer is initialized
	if GlobalTracer != nil {
		health.Initialized = true
		health.Name = GetServiceName()
		health.Type = "opentelemetry"
		health.Status = "healthy"
	} else {
		// Try to get default tracer
		if tracer := tracing.DefaultTracer(); tracer != nil {
			health.Initialized = true
			health.Name = "default"
			health.Type = "opentelemetry"
			health.Status = "healthy"
		}
	}

	return health
}

// checkEndpointHealth checks the health of the tracing endpoint
func (hc *HealthChecker) checkEndpointHealth(ctx context.Context) EndpointHealth {
	cfg := tracing.LoadConfig()
	
	health := EndpointHealth{
		URL:         cfg.JaegerEndpoint,
		Reachable:   false,
		ResponseTime: 0,
		LastCheck:   time.Now(),
	}

	if !cfg.Enabled {
		health.URL = "disabled"
		health.Reachable = true
		return health
	}

	// Test endpoint connectivity
	start := time.Now()
	
	// Create a test HTTP request to check connectivity
	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	
	// For Jaeger, we can check the health endpoint
	healthURL := "http://localhost:14269/health"
	if cfg.Environment == "production" {
		healthURL = "http://jaeger:14269/health"
	}
	
	req, err := http.NewRequestWithContext(ctx, "GET", healthURL, nil)
	if err != nil {
		return health
	}
	
	resp, err := client.Do(req)
	if err != nil {
		return health
	}
	defer resp.Body.Close()
	
	health.ResponseTime = time.Since(start).Milliseconds()
	health.Reachable = resp.StatusCode == http.StatusOK
	
	return health
}

// getMetrics retrieves basic tracing metrics
func (hc *HealthChecker) getMetrics(ctx context.Context) TracingMetrics {
	// For now, return mock metrics
	// In a real implementation, these would come from the tracer's metrics
	return TracingMetrics{
		SpansCreated:   1000,
		SpansExported:  950,
		SpansDropped:   50,
		SampleRate:     0.1,
		ErrorRate:      0.05,
		AverageLatency: 12.5,
	}
}

// determineOverallStatus determines the overall health status
func (hc *HealthChecker) determineOverallStatus(status *HealthStatus) string {
	if !status.Enabled {
		return "disabled"
	}

	if !status.Tracer.Initialized {
		return "unhealthy"
	}

	if status.Tracer.Status != "healthy" {
		return "unhealthy"
	}

	if !status.Endpoint.Reachable {
		return "degraded"
	}

	if status.Metrics.ErrorRate > 0.1 {
		return "degraded"
	}

	return "healthy"
}

// QuickHealthCheck performs a quick health check
func QuickHealthCheck() map[string]interface{} {
	return map[string]interface{}{
		"enabled":     IsEnabled(),
		"service":     GetServiceName(),
		"version":     GetServiceVersion(),
		"environment": getEnvironment(),
		"tracer":      GlobalTracer != nil,
		"timestamp":   time.Now(),
	}
}

// DetailedHealthCheck performs a detailed health check
func DetailedHealthCheck(ctx context.Context) *HealthStatus {
	checker := NewHealthChecker()
	return checker.Check(ctx)
}

// TracingHealthHandler returns a Gin handler for tracing health checks
func TracingHealthHandler() func(c interface{}) {
	return func(c interface{}) {
		// This would be implemented based on the web framework being used
		// For now, just return the health check data
		health := QuickHealthCheck()
		
		// In a real implementation, this would write to the response
		fmt.Printf("Tracing Health: %+v\n", health)
	}
}

// WaitForHealthy waits for the tracing system to become healthy
func WaitForHealthy(ctx context.Context, timeout time.Duration) error {
	checker := NewHealthChecker()
	
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("timeout waiting for tracing system to become healthy")
		case <-ticker.C:
			status := checker.Check(ctx)
			if status.Status == "healthy" {
				return nil
			}
		}
	}
}

// MonitorHealth continuously monitors the health of the tracing system
func MonitorHealth(ctx context.Context, interval time.Duration, callback func(*HealthStatus)) {
	checker := NewHealthChecker()
	checker.checkInterval = interval
	
	ticker := time.NewTicker(interval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			status := checker.Check(ctx)
			if callback != nil {
				callback(status)
			}
		}
	}
}

// GetTracingDiagnostics returns diagnostic information about the tracing system
func GetTracingDiagnostics() map[string]interface{} {
	cfg := tracing.LoadConfig()
	
	diagnostics := map[string]interface{}{
		"configuration": map[string]interface{}{
			"enabled":             cfg.Enabled,
			"service_name":        cfg.ServiceName,
			"service_version":     cfg.ServiceVersion,
			"environment":         cfg.Environment,
			"exporter":            cfg.Exporter,
			"sampling_strategy":   cfg.Sampling,
			"sample_rate":         cfg.SampleRate,
			"jaeger_endpoint":     cfg.JaegerEndpoint,
			"debug":               cfg.Debug,
			"batch_timeout":       cfg.BatchTimeout,
			"batch_size":          cfg.BatchSize,
			"max_export_batch_size": cfg.MaxExportBatchSize,
			"max_queue_size":      cfg.MaxQueueSize,
		},
		"runtime": map[string]interface{}{
			"tracer_initialized": GlobalTracer != nil,
			"factory_available":  tracing.GetGlobalFactory() != nil,
		},
		"health": QuickHealthCheck(),
	}
	
	return diagnostics
}

// TestTracing creates a test trace to verify the tracing system is working
func TestTracing(ctx context.Context) error {
	if !IsEnabled() {
		return fmt.Errorf("tracing is disabled")
	}
	
	// Create a test span
	spanCtx, span := CreateSpan(ctx, "test.tracing")
	defer span.End()
	
	// Set test attributes
	span.SetAttribute("test.component", "health_checker")
	span.SetAttribute("test.operation", "verify_tracing")
	span.SetAttribute("test.timestamp", time.Now().Unix())
	
	// Add test event
	span.AddEvent("test.event", map[string]interface{}{
		"message": "Tracing system test",
		"level":   "info",
	})
	
	// Set successful status
	span.SetStatus(tracing.SpanStatusOK, "Test completed successfully")
	
	return nil
}

// ValidateConfiguration validates the tracing configuration
func ValidateConfiguration() error {
	cfg := tracing.LoadConfig()
	return cfg.Validate()
}

// GetConfigurationStatus returns the status of the tracing configuration
func GetConfigurationStatus() map[string]interface{} {
	cfg := tracing.LoadConfig()
	
	status := map[string]interface{}{
		"valid":  true,
		"errors": []string{},
	}
	
	if err := cfg.Validate(); err != nil {
		status["valid"] = false
		status["errors"] = []string{err.Error()}
	}
	
	return status
}