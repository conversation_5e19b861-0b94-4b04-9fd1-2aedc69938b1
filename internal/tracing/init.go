package tracing

import (
	"context"
	"log"
	"os"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/tracing"
)

// GlobalTracer holds the global tracer instance
var GlobalTracer tracing.Tracer

// Initialize initializes the global tracing system for the application
func Initialize() error {
	// Load configuration from environment variables
	cfg := tracing.LoadConfig()
	
	// Override default values for blog-api-v3
	cfg.ServiceName = "blog-api-v3"
	cfg.ServiceVersion = "1.0.0"
	cfg.Environment = getEnvironment()
	
	// Set Jaeger endpoint based on environment
	if cfg.Environment == "development" {
		cfg.JaegerEndpoint = "http://localhost:14268/api/traces"
		cfg.Enabled = true
		cfg.Debug = true
		cfg.Sampling = tracing.AlwaysSample
		cfg.SampleRate = 1.0
	} else if cfg.Environment == "production" {
		cfg.JaegerEndpoint = "http://jaeger:14268/api/traces"
		cfg.Enabled = true
		cfg.Debug = false
		cfg.Sampling = tracing.TraceIDRatio
		cfg.SampleRate = 0.1
	}
	
	// Validate configuration
	if err := cfg.Validate(); err != nil {
		return err
	}
	
	// Create the tracer
	tracer, err := tracing.CreateTracerWithName("blog-api-v3", cfg)
	if err != nil {
		return err
	}
	
	// Set as global tracer
	GlobalTracer = tracer
	tracing.SetDefaultTracer(tracer)
	
	log.Printf("Tracing initialized: enabled=%t, service=%s, environment=%s, endpoint=%s", 
		cfg.Enabled, cfg.ServiceName, cfg.Environment, cfg.JaegerEndpoint)
	
	return nil
}

// Shutdown shuts down the global tracing system
func Shutdown(ctx context.Context) error {
	if GlobalTracer != nil {
		return GlobalTracer.Shutdown(ctx)
	}
	return nil
}

// ShutdownWithTimeout shuts down the global tracing system with timeout
func ShutdownWithTimeout(timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	return Shutdown(ctx)
}

// GetTracer returns the global tracer instance
func GetTracer() tracing.Tracer {
	if GlobalTracer != nil {
		return GlobalTracer
	}
	return tracing.DefaultTracer()
}

// MustInitialize initializes tracing and panics if it fails
func MustInitialize() {
	if err := Initialize(); err != nil {
		log.Fatalf("Failed to initialize tracing: %v", err)
	}
}

// getEnvironment returns the current environment
func getEnvironment() string {
	env := os.Getenv("ENVIRONMENT")
	if env == "" {
		env = os.Getenv("ENV")
	}
	if env == "" {
		env = "development"
	}
	return env
}

// InitializeForModule initializes tracing for a specific module
func InitializeForModule(moduleName string) (tracing.Tracer, error) {
	cfg := tracing.LoadConfig()
	cfg.ServiceName = "blog-api-v3"
	cfg.ServiceVersion = "1.0.0"
	cfg.Environment = getEnvironment()
	
	// Add module-specific attributes
	if cfg.ResourceAttributes == nil {
		cfg.ResourceAttributes = make(map[string]string)
	}
	cfg.ResourceAttributes["module"] = moduleName
	
	// Configure based on environment
	if cfg.Environment == "development" {
		cfg.JaegerEndpoint = "http://localhost:14268/api/traces"
		cfg.Enabled = true
		cfg.Debug = true
		cfg.Sampling = tracing.AlwaysSample
		cfg.SampleRate = 1.0
	} else if cfg.Environment == "production" {
		cfg.JaegerEndpoint = "http://jaeger:14268/api/traces"
		cfg.Enabled = true
		cfg.Debug = false
		cfg.Sampling = tracing.TraceIDRatio
		cfg.SampleRate = 0.1
	}
	
	// Create module-specific tracer
	tracerName := "blog-api-v3-" + moduleName
	return tracing.CreateTracerWithName(tracerName, cfg)
}

// GetEnvironmentConfig returns configuration for the current environment
func GetEnvironmentConfig() *tracing.Config {
	env := getEnvironment()
	
	switch env {
	case "production":
		return tracing.ProductionConfig()
	case "testing":
		return tracing.TestingConfig()
	default:
		return tracing.DevelopmentConfig()
	}
}

// IsEnabled returns true if tracing is enabled
func IsEnabled() bool {
	cfg := tracing.LoadConfig()
	return cfg.Enabled
}

// GetServiceName returns the service name
func GetServiceName() string {
	return "blog-api-v3"
}

// GetServiceVersion returns the service version
func GetServiceVersion() string {
	return "1.0.0"
}

// CreateSpan creates a new span with the global tracer
func CreateSpan(ctx context.Context, name string) (context.Context, tracing.Span) {
	tracer := GetTracer()
	return tracer.StartSpan(ctx, name)
}

// CreateSpanWithOptions creates a new span with options
func CreateSpanWithOptions(ctx context.Context, name string, options ...tracing.SpanOption) (context.Context, tracing.Span) {
	tracer := GetTracer()
	return tracer.StartSpanWithOptions(ctx, name, options...)
}

// AddEvent adds an event to the current span
func AddEvent(ctx context.Context, name string, attributes map[string]interface{}) {
	if span := tracing.SpanFromContext(ctx); span != nil {
		span.AddEvent(name, attributes)
	}
}

// SetAttribute sets an attribute on the current span
func SetAttribute(ctx context.Context, key string, value interface{}) {
	if span := tracing.SpanFromContext(ctx); span != nil {
		span.SetAttribute(key, value)
	}
}

// SetStatus sets the status of the current span
func SetStatus(ctx context.Context, code tracing.SpanStatusCode, description string) {
	if span := tracing.SpanFromContext(ctx); span != nil {
		span.SetStatus(code, description)
	}
}

// RecordError records an error on the current span
func RecordError(ctx context.Context, err error) {
	if span := tracing.SpanFromContext(ctx); span != nil {
		span.RecordError(err)
	}
}

// FinishSpan finishes the current span
func FinishSpan(ctx context.Context) {
	if span := tracing.SpanFromContext(ctx); span != nil {
		span.End()
	}
}

// WithSpan executes a function within a span
func WithSpan(ctx context.Context, name string, fn func(context.Context) error) error {
	spanCtx, span := CreateSpan(ctx, name)
	defer span.End()
	
	if err := fn(spanCtx); err != nil {
		span.RecordError(err)
		span.SetStatus(tracing.SpanStatusError, err.Error())
		return err
	}
	
	span.SetStatus(tracing.SpanStatusOK, "")
	return nil
}

// WithSpanAsync executes a function within a span asynchronously
func WithSpanAsync(ctx context.Context, name string, fn func(context.Context)) {
	spanCtx, span := CreateSpan(ctx, name)
	
	go func() {
		defer span.End()
		fn(spanCtx)
		span.SetStatus(tracing.SpanStatusOK, "")
	}()
}

// InjectHeaders injects tracing headers into a map
func InjectHeaders(ctx context.Context, headers map[string]string) {
	tracer := GetTracer()
	tracer.InjectHeaders(ctx, headers)
}

// ExtractHeaders extracts tracing context from headers
func ExtractHeaders(ctx context.Context, headers map[string]string) context.Context {
	tracer := GetTracer()
	return tracer.ExtractHeaders(ctx, headers)
}

// GetTraceID returns the trace ID from context
func GetTraceID(ctx context.Context) string {
	if span := tracing.SpanFromContext(ctx); span != nil {
		return span.TraceID()
	}
	return ""
}

// GetSpanID returns the span ID from context
func GetSpanID(ctx context.Context) string {
	if span := tracing.SpanFromContext(ctx); span != nil {
		return span.SpanID()
	}
	return ""
}

// Health check for tracing
func HealthCheck() map[string]interface{} {
	status := map[string]interface{}{
		"enabled":     IsEnabled(),
		"service":     GetServiceName(),
		"version":     GetServiceVersion(),
		"environment": getEnvironment(),
	}
	
	if GlobalTracer != nil {
		status["tracer"] = "initialized"
	} else {
		status["tracer"] = "not_initialized"
	}
	
	return status
}

// GetTracingMetrics returns basic tracing metrics
func GetTracingMetrics() map[string]interface{} {
	metrics := map[string]interface{}{
		"enabled":     IsEnabled(),
		"service":     GetServiceName(),
		"version":     GetServiceVersion(),
		"environment": getEnvironment(),
	}
	
	if GlobalTracer != nil {
		metrics["tracer_initialized"] = true
	} else {
		metrics["tracer_initialized"] = false
	}
	
	return metrics
}