package tracing

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/pkg/tracing"
)

// TracingMiddleware returns a Gin middleware that adds tracing to HTTP requests
func TracingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip if tracing is disabled
		if !IsEnabled() {
			c.Next()
			return
		}
		
		// Get tracer
		tracer := GetTracer()
		if tracer == nil {
			c.Next()
			return
		}
		
		// Create span name
		spanName := c.Request.Method + " " + c.FullPath()
		if spanName == "" {
			spanName = c.Request.Method + " " + c.Request.URL.Path
		}
		
		// Extract tracing context from headers
		headers := make(map[string]string)
		for key, values := range c.Request.Header {
			if len(values) > 0 {
				headers[key] = values[0]
			}
		}
		
		// Extract context from headers
		ctx := tracer.ExtractHeaders(c.Request.Context(), headers)
		
		// Start span
		spanCtx, span := tracer.StartSpan(ctx, spanName)
		defer span.End()
		
		// Set span attributes
		span.SetAttribute("http.method", c.Request.Method)
		span.SetAttribute("http.url", c.Request.URL.String())
		span.SetAttribute("http.route", c.FullPath())
		span.SetAttribute("http.scheme", c.Request.URL.Scheme)
		span.SetAttribute("http.host", c.Request.Host)
		span.SetAttribute("http.target", c.Request.URL.Path)
		if c.Request.URL.RawQuery != "" {
			span.SetAttribute("http.query", c.Request.URL.RawQuery)
		}
		span.SetAttribute("http.user_agent", c.Request.UserAgent())
		span.SetAttribute("http.client_ip", c.ClientIP())
		span.SetAttribute("http.remote_addr", c.Request.RemoteAddr)
		
		// Set service attributes
		span.SetAttribute("service.name", GetServiceName())
		span.SetAttribute("service.version", GetServiceVersion())
		span.SetAttribute("service.environment", getEnvironment())
		
		// Add tenant and user context if available
		if tenantID, exists := c.Get("tenant_id"); exists {
			span.SetAttribute("tenant.id", tenantID)
		}
		if userID, exists := c.Get("user_id"); exists {
			span.SetAttribute("user.id", userID)
		}
		if websiteID, exists := c.Get("website_id"); exists {
			span.SetAttribute("website.id", websiteID)
		}
		
		// Add request ID if available
		if requestID := c.GetHeader("X-Request-ID"); requestID != "" {
			span.SetAttribute("request.id", requestID)
		}
		
		// Add correlation ID if available
		if correlationID := c.GetHeader("X-Correlation-ID"); correlationID != "" {
			span.SetAttribute("correlation.id", correlationID)
		}
		
		// Store span context in gin context
		c.Request = c.Request.WithContext(spanCtx)
		
		// Add tracing headers to response
		responseHeaders := make(map[string]string)
		tracer.InjectHeaders(spanCtx, responseHeaders)
		for key, value := range responseHeaders {
			c.Header(key, value)
		}
		
		// Add trace ID to response headers
		if traceID := GetTraceID(spanCtx); traceID != "" {
			c.Header("X-Trace-ID", traceID)
		}
		
		// Record start time
		startTime := time.Now()
		
		// Process request
		c.Next()
		
		// Record response attributes
		duration := time.Since(startTime)
		span.SetAttribute("http.status_code", c.Writer.Status())
		span.SetAttribute("http.response_size", c.Writer.Size())
		span.SetAttribute("http.duration_ms", float64(duration.Nanoseconds())/1000000.0)
		
		// Set span status based on HTTP status code
		statusCode := c.Writer.Status()
		if statusCode >= 400 {
			span.SetStatus(tracing.SpanStatusError, "HTTP "+strconv.Itoa(statusCode))
			
			// Record error details if available
			if len(c.Errors) > 0 {
				span.RecordError(c.Errors.Last())
				span.SetAttribute("error.message", c.Errors.Last().Error())
			}
		} else {
			span.SetStatus(tracing.SpanStatusOK, "")
		}
		
		// Add response headers to span
		for key, values := range c.Writer.Header() {
			if len(values) > 0 {
				span.SetAttribute("http.response_header."+key, values[0])
			}
		}
		
		// Record timing metrics
		span.AddEvent("request.completed", map[string]interface{}{
			"duration_ms": float64(duration.Nanoseconds()) / 1000000.0,
			"status_code": statusCode,
		})
	}
}

// DatabaseTracingMiddleware returns a middleware for database operations
func DatabaseTracingMiddleware(operation string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !IsEnabled() {
			c.Next()
			return
		}
		
		ctx := c.Request.Context()
		spanName := "db." + operation
		
		spanCtx, span := CreateSpan(ctx, spanName)
		defer span.End()
		
		// Set database attributes
		span.SetAttribute("db.operation", operation)
		span.SetAttribute("db.system", "mysql")
		span.SetAttribute("db.name", "wnapi")
		
		// Add tenant context if available
		if tenantID, exists := c.Get("tenant_id"); exists {
			span.SetAttribute("tenant.id", tenantID)
		}
		
		// Update request context
		c.Request = c.Request.WithContext(spanCtx)
		
		// Process request
		startTime := time.Now()
		c.Next()
		duration := time.Since(startTime)
		
		// Record timing
		span.SetAttribute("db.duration_ms", float64(duration.Nanoseconds())/1000000.0)
		
		// Set status based on errors
		if len(c.Errors) > 0 {
			span.RecordError(c.Errors.Last())
			span.SetStatus(tracing.SpanStatusError, c.Errors.Last().Error())
		} else {
			span.SetStatus(tracing.SpanStatusOK, "")
		}
	}
}

// ServiceTracingMiddleware returns a middleware for service layer operations
func ServiceTracingMiddleware(service, operation string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !IsEnabled() {
			c.Next()
			return
		}
		
		ctx := c.Request.Context()
		spanName := service + "." + operation
		
		spanCtx, span := CreateSpan(ctx, spanName)
		defer span.End()
		
		// Set service attributes
		span.SetAttribute("service.layer", "business")
		span.SetAttribute("service.name", service)
		span.SetAttribute("service.operation", operation)
		
		// Add context attributes
		if tenantID, exists := c.Get("tenant_id"); exists {
			span.SetAttribute("tenant.id", tenantID)
		}
		if userID, exists := c.Get("user_id"); exists {
			span.SetAttribute("user.id", userID)
		}
		
		// Update request context
		c.Request = c.Request.WithContext(spanCtx)
		
		// Process request
		startTime := time.Now()
		c.Next()
		duration := time.Since(startTime)
		
		// Record timing
		span.SetAttribute("service.duration_ms", float64(duration.Nanoseconds())/1000000.0)
		
		// Set status based on errors
		if len(c.Errors) > 0 {
			span.RecordError(c.Errors.Last())
			span.SetStatus(tracing.SpanStatusError, c.Errors.Last().Error())
		} else {
			span.SetStatus(tracing.SpanStatusOK, "")
		}
	}
}

// ExternalServiceTracingMiddleware returns a middleware for external service calls
func ExternalServiceTracingMiddleware(serviceName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !IsEnabled() {
			c.Next()
			return
		}
		
		ctx := c.Request.Context()
		spanName := "external." + serviceName
		
		spanCtx, span := CreateSpan(ctx, spanName)
		defer span.End()
		
		// Set external service attributes
		span.SetAttribute("service.type", "external")
		span.SetAttribute("service.name", serviceName)
		span.SetAttribute("component", "http-client")
		
		// Update request context
		c.Request = c.Request.WithContext(spanCtx)
		
		// Process request
		startTime := time.Now()
		c.Next()
		duration := time.Since(startTime)
		
		// Record timing
		span.SetAttribute("external.duration_ms", float64(duration.Nanoseconds())/1000000.0)
		
		// Set status based on errors
		if len(c.Errors) > 0 {
			span.RecordError(c.Errors.Last())
			span.SetStatus(tracing.SpanStatusError, c.Errors.Last().Error())
		} else {
			span.SetStatus(tracing.SpanStatusOK, "")
		}
	}
}

// APIKeyTracingMiddleware returns a middleware for API key operations
func APIKeyTracingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !IsEnabled() {
			c.Next()
			return
		}
		
		ctx := c.Request.Context()
		spanName := "apikey.validation"
		
		spanCtx, span := CreateSpan(ctx, spanName)
		defer span.End()
		
		// Set API key attributes
		span.SetAttribute("component", "api-key-middleware")
		span.SetAttribute("operation", "validate")
		
		// Check if API key is provided
		if apiKey := c.GetHeader("X-API-Key"); apiKey != "" {
			span.SetAttribute("apikey.provided", true)
			span.SetAttribute("apikey.prefix", apiKey[:min(len(apiKey), 8)]+"...")
		} else if authHeader := c.GetHeader("Authorization"); authHeader != "" {
			span.SetAttribute("apikey.provided", true)
			span.SetAttribute("apikey.method", "bearer")
		} else {
			span.SetAttribute("apikey.provided", false)
		}
		
		// Update request context
		c.Request = c.Request.WithContext(spanCtx)
		
		// Process request
		startTime := time.Now()
		c.Next()
		duration := time.Since(startTime)
		
		// Record timing
		span.SetAttribute("apikey.duration_ms", float64(duration.Nanoseconds())/1000000.0)
		
		// Check if API key validation was successful
		if apiKeyData, exists := c.Get("api_key"); exists {
			span.SetAttribute("apikey.valid", true)
			if apiKey, ok := apiKeyData.(map[string]interface{}); ok {
				if id, exists := apiKey["id"]; exists {
					span.SetAttribute("apikey.id", id)
				}
				if name, exists := apiKey["name"]; exists {
					span.SetAttribute("apikey.name", name)
				}
			}
		} else {
			span.SetAttribute("apikey.valid", false)
		}
		
		// Set status based on HTTP status code
		statusCode := c.Writer.Status()
		if statusCode == 401 || statusCode == 403 {
			span.SetStatus(tracing.SpanStatusError, "API key validation failed")
		} else {
			span.SetStatus(tracing.SpanStatusOK, "")
		}
	}
}

// Helper function
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}