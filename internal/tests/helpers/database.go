package helpers

import (
	"fmt"
	"os"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	websiteModels "github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	onboardingModels "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/models"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func SetupTestDatabase() (*gorm.DB, error) {
	// Get database configuration from environment or use defaults
	host := getEnv("TEST_DB_HOST", "localhost")
	port := getEnv("TEST_DB_PORT", "3306")
	user := getEnv("TEST_DB_USER", "root")
	password := getEnv("TEST_DB_PASSWORD", "root")
	database := getEnv("TEST_DB_NAME", "blogapi_test")
	
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, database,
	)
	
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Silent mode for tests
	})
	
	if err != nil {
		return nil, fmt.Errorf("failed to connect to test database: %w", err)
	}
	
	return db, nil
}

func RunMigrations(db *gorm.DB) error {
	// Auto-migrate all models
	err := db.AutoMigrate(
		// Tenant models (base layer)
		&tenantModels.TenantPlan{},
		&tenantModels.Tenant{},
		&tenantModels.TenantSetting{},
		&tenantModels.TenantFeature{},
		&tenantModels.FeatureCatalog{},
		
		// Auth models
		&models.User{},
		&models.Session{},
		&models.LoginAttempt{},
		&models.PasswordReset{},
		&models.OAuthProvider{},
		&models.OAuthConnection{},
		// &models.AuthToken{}, // This model doesn't exist
		&models.TokenBlacklist{},
		&models.PasswordHistory{},
		
		// User models (depends on auth)
		&userModels.UserProfile{},
		&userModels.UserPreferences{},
		&userModels.UserSocialLink{},
		&userModels.TenantMembership{},
		&userModels.UserInvitation{},
		
		// Website models (depends on tenant)
		&websiteModels.Website{},
		&websiteModels.WebsiteSetting{},
		&websiteModels.WebsiteTheme{},
		
		// Onboarding models (depends on tenant and user)
		&onboardingModels.OnboardingTemplate{},
		&onboardingModels.OnboardingJourney{},
		&onboardingModels.OnboardingStep{},
		&onboardingModels.OnboardingProgress{},
		&onboardingModels.OnboardingAnalytics{},
	)
	
	if err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}
	
	return nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}