package notification

import (
	"fmt"
	"log"

	"gorm.io/gorm"
)

type NotificationTemplateSeeder struct {
	db *gorm.DB
}

func NewNotificationTemplateSeeder(db *gorm.DB) *NotificationTemplateSeeder {
	return &NotificationTemplateSeeder{db: db}
}

func (s *NotificationTemplateSeeder) Seed() error {
	// Get first tenant for seeding
	var tenantID uint = 1

	templates := []map[string]interface{}{
		{
			"tenant_id":          tenantID,
			"code":               "user_welcome_email",
			"name":               "User Welcome Email",
			"type":               "transactional",
			"channel":            "email",
			"description":        "Welcome email sent to new users after registration",
			"variables":          `["user.name", "user.email", "activation_url", "brand.name", "brand.logo_url", "brand.support_email"]`,
			"is_active":          true,
			"version_count":      2,
			"active_version_id":  nil, // Will be updated after creating versions
		},
		{
			"tenant_id":          tenantID,
			"code":               "password_reset_email",
			"name":               "Password Reset Email",
			"type":               "transactional",
			"channel":            "email",
			"description":        "Email sent when user requests password reset",
			"variables":          `["user.name", "reset_url", "expiry_time", "brand.name", "brand.support_email"]`,
			"is_active":          true,
			"version_count":      1,
			"active_version_id":  nil,
		},
		{
			"tenant_id":          tenantID,
			"code":               "blog_post_published",
			"name":               "Blog Post Published Notification",
			"type":               "marketing",
			"channel":            "email",
			"description":        "Notification sent to subscribers when new blog post is published",
			"variables":          `["post.title", "post.excerpt", "post.url", "post.author", "user.name", "unsubscribe_url"]`,
			"is_active":          true,
			"version_count":      1,
			"active_version_id":  nil,
		},
		{
			"tenant_id":          tenantID,
			"code":               "system_maintenance",
			"name":               "System Maintenance Alert",
			"type":               "system",
			"channel":            "email",
			"description":        "Alert sent to users about system maintenance",
			"variables":          `["maintenance.start_time", "maintenance.end_time", "maintenance.description", "user.name"]`,
			"is_active":          false,
			"version_count":      1,
			"active_version_id":  nil,
		},
		{
			"tenant_id":          tenantID,
			"code":               "welcome_socket",
			"name":               "Welcome Socket Notification",
			"type":               "transactional",
			"channel":            "socket",
			"description":        "Real-time welcome notification via WebSocket",
			"variables":          `["user.name", "user.avatar_url", "dashboard_url"]`,
			"is_active":          true,
			"version_count":      1,
			"active_version_id":  nil,
		},
		{
			"tenant_id":          tenantID,
			"code":               "email_verification",
			"name":               "Email Verification",
			"type":               "transactional",
			"channel":            "email",
			"description":        "Email verification link sent to users for email confirmation",
			"variables":          `["user.name", "user.email", "verification_url", "brand.name", "brand.logo_url", "brand.support_email", "expiry_time"]`,
			"is_active":          true,
			"version_count":      2,
			"active_version_id":  nil,
		},
		{
			"tenant_id":          tenantID,
			"code":               "email_verification_resend",
			"name":               "Resend Email Verification",
			"type":               "transactional",
			"channel":            "email",
			"description":        "Resent email verification link with rate limiting info",
			"variables":          `["user.name", "user.email", "verification_url", "brand.name", "brand.support_email", "expiry_time", "resend_count", "max_resends"]`,
			"is_active":          true,
			"version_count":      1,
			"active_version_id":  nil,
		},
	}

	for _, template := range templates {
		// Check if template exists
		var count int64
		s.db.Table("notification_templates").
			Where("tenant_id = ? AND code = ?", template["tenant_id"], template["code"]).
			Count(&count)
		
		if count == 0 {
			// Create new template
			if err := s.db.Table("notification_templates").Create(&template).Error; err != nil {
				log.Printf("Error seeding notification template %s: %v", template["code"], err)
				return err
			}
		} else {
			// Update existing template
			if err := s.db.Table("notification_templates").
				Where("tenant_id = ? AND code = ?", template["tenant_id"], template["code"]).
				Updates(&template).Error; err != nil {
				log.Printf("Error updating notification template %s: %v", template["code"], err)
				return err
			}
		}
		log.Printf("✅ Seeded notification template: %s", template["code"])
	}

	return nil
}

func (s *NotificationTemplateSeeder) createTemplateVersions() error {
	var tenantID uint = 1

	// Get template IDs by code
	templateIDs := make(map[string]uint)
	var templates []struct {
		ID   uint
		Code string
	}
	
	if err := s.db.Table("notification_templates").
		Where("tenant_id = ?", tenantID).
		Select("id, code").
		Find(&templates).Error; err != nil {
		return err
	}
	
	for _, template := range templates {
		templateIDs[template.Code] = template.ID
	}

	versions := []map[string]interface{}{
		// Welcome Email - English Version
		{
			"tenant_id":      tenantID,
			"template_id":    templateIDs["user_welcome_email"],
			"version_number": 1,
			"language":       "en",
			"subject":        "Welcome to {{.brand.name}}, {{.user.name}}! 🎉",
			"body_html": `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{.brand.name}}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
        .header img { height: 50px; margin-bottom: 20px; }
        .header h1 { color: #ffffff; margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .welcome-message { text-align: center; margin-bottom: 30px; }
        .welcome-message h2 { color: #333; font-size: 24px; margin-bottom: 15px; }
        .welcome-message p { color: #666; font-size: 16px; line-height: 1.6; }
        .cta-button { text-align: center; margin: 30px 0; }
        .cta-button a { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            display: inline-block;
            transition: transform 0.2s ease;
        }
        .cta-button a:hover { transform: translateY(-2px); }
        .features { margin: 30px 0; }
        .feature { display: flex; align-items: center; margin-bottom: 20px; }
        .feature-icon { background: #f0f0f0; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-right: 15px; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #eee; }
        .footer p { color: #666; font-size: 14px; margin: 5px 0; }
        .footer a { color: #667eea; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            {{if .brand.logo_url}}
            <img src="{{.brand.logo_url}}" alt="{{.brand.name}}" />
            {{end}}
            <h1>Welcome to {{.brand.name}}</h1>
        </div>
        
        <div class="content">
            <div class="welcome-message">
                <h2>Hello {{.user.name}}! 👋</h2>
                <p>We're thrilled to have you join our community. Your account has been created successfully and you're just one step away from exploring all the amazing features we have to offer.</p>
            </div>
            
            <div class="cta-button">
                <a href="{{.activation_url}}">Verify Your Email Address</a>
            </div>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">📝</div>
                    <div>
                        <strong>Create & Publish</strong><br>
                        Write and publish engaging blog posts with our intuitive editor
                    </div>
                </div>
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <div>
                        <strong>Analytics & Insights</strong><br>
                        Track your content performance with detailed analytics
                    </div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎨</div>
                    <div>
                        <strong>Custom Themes</strong><br>
                        Personalize your blog with beautiful, responsive themes
                    </div>
                </div>
            </div>
            
            <p style="color: #666; line-height: 1.6;">
                If you have any questions or need assistance getting started, don't hesitate to reach out to our support team at 
                <a href="mailto:{{.brand.support_email}}" style="color: #667eea;">{{.brand.support_email}}</a>
            </p>
            
            <p style="color: #666; line-height: 1.6;">
                Welcome aboard!<br>
                <strong>The {{.brand.name}} Team</strong>
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; {{.current_year}} {{.brand.name}}. All rights reserved.</p>
            <p>
                <a href="{{.unsubscribe_url}}">Unsubscribe</a> | 
                <a href="{{.preferences_url}}">Email Preferences</a>
            </p>
        </div>
    </div>
</body>
</html>`,
			"body_text": `Welcome to {{.brand.name}}, {{.user.name}}!

We're thrilled to have you join our community. Your account has been created successfully and you're just one step away from exploring all the amazing features we have to offer.

To get started, please verify your email address by clicking this link:
{{.activation_url}}

What you can do with {{.brand.name}}:
• Create & Publish: Write and publish engaging blog posts
• Analytics & Insights: Track your content performance  
• Custom Themes: Personalize your blog with beautiful themes

If you have any questions, contact us at {{.brand.support_email}}

Welcome aboard!
The {{.brand.name}} Team

© {{.current_year}} {{.brand.name}}. All rights reserved.
Unsubscribe: {{.unsubscribe_url}} | Preferences: {{.preferences_url}}`,
			"is_active":   true,
			"is_approved": true,
		},
		// Welcome Email - Vietnamese Version
		{
			"tenant_id":      tenantID,
			"template_id":    templateIDs["user_welcome_email"],
			"version_number": 2,
			"language":       "vi",
			"subject":        "Chào mừng bạn đến với {{.brand.name}}, {{.user.name}}! 🎉",
			"body_html": `<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào mừng đến với {{.brand.name}}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
        .header img { height: 50px; margin-bottom: 20px; }
        .header h1 { color: #ffffff; margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .welcome-message { text-align: center; margin-bottom: 30px; }
        .welcome-message h2 { color: #333; font-size: 24px; margin-bottom: 15px; }
        .welcome-message p { color: #666; font-size: 16px; line-height: 1.6; }
        .cta-button { text-align: center; margin: 30px 0; }
        .cta-button a { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            display: inline-block;
            transition: transform 0.2s ease;
        }
        .cta-button a:hover { transform: translateY(-2px); }
        .features { margin: 30px 0; }
        .feature { display: flex; align-items: center; margin-bottom: 20px; }
        .feature-icon { background: #f0f0f0; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-right: 15px; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #eee; }
        .footer p { color: #666; font-size: 14px; margin: 5px 0; }
        .footer a { color: #667eea; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            {{if .brand.logo_url}}
            <img src="{{.brand.logo_url}}" alt="{{.brand.name}}" />
            {{end}}
            <h1>Chào mừng đến với {{.brand.name}}</h1>
        </div>
        
        <div class="content">
            <div class="welcome-message">
                <h2>Xin chào {{.user.name}}! 👋</h2>
                <p>Chúng tôi rất vui mừng khi bạn tham gia cộng đồng của chúng tôi. Tài khoản của bạn đã được tạo thành công và bạn chỉ còn một bước nữa để khám phá tất cả các tính năng tuyệt vời mà chúng tôi cung cấp.</p>
            </div>
            
            <div class="cta-button">
                <a href="{{.activation_url}}">Xác Minh Địa Chỉ Email</a>
            </div>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">📝</div>
                    <div>
                        <strong>Tạo & Xuất Bản</strong><br>
                        Viết và xuất bản các bài blog hấp dẫn với trình soạn thảo trực quan
                    </div>
                </div>
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <div>
                        <strong>Phân Tích & Thông Tin Chi Tiết</strong><br>
                        Theo dõi hiệu suất nội dung với phân tích chi tiết
                    </div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎨</div>
                    <div>
                        <strong>Giao Diện Tùy Chỉnh</strong><br>
                        Cá nhân hóa blog của bạn với các giao diện đẹp, responsive
                    </div>
                </div>
            </div>
            
            <p style="color: #666; line-height: 1.6;">
                Nếu bạn có bất kỳ câu hỏi nào hoặc cần hỗ trợ để bắt đầu, đừng ngần ngại liên hệ với đội ngũ hỗ trợ của chúng tôi tại 
                <a href="mailto:{{.brand.support_email}}" style="color: #667eea;">{{.brand.support_email}}</a>
            </p>
            
            <p style="color: #666; line-height: 1.6;">
                Chào mừng bạn!<br>
                <strong>Đội ngũ {{.brand.name}}</strong>
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; {{.current_year}} {{.brand.name}}. Tất cả quyền được bảo lưu.</p>
            <p>
                <a href="{{.unsubscribe_url}}">Hủy đăng ký</a> | 
                <a href="{{.preferences_url}}">Tùy chọn Email</a>
            </p>
        </div>
    </div>
</body>
</html>`,
			"body_text": `Chào mừng bạn đến với {{.brand.name}}, {{.user.name}}!

Chúng tôi rất vui mừng khi bạn tham gia cộng đồng của chúng tôi. Tài khoản của bạn đã được tạo thành công và bạn chỉ còn một bước nữa để khám phá tất cả các tính năng tuyệt vời.

Để bắt đầu, vui lòng xác minh địa chỉ email của bạn bằng cách nhấp vào liên kết này:
{{.activation_url}}

Những gì bạn có thể làm với {{.brand.name}}:
• Tạo & Xuất Bản: Viết và xuất bản các bài blog hấp dẫn
• Phân Tích & Thông Tin: Theo dõi hiệu suất nội dung
• Giao Diện Tùy Chỉnh: Cá nhân hóa blog với giao diện đẹp

Nếu bạn có câu hỏi, liên hệ với chúng tôi tại {{.brand.support_email}}

Chào mừng bạn!
Đội ngũ {{.brand.name}}

© {{.current_year}} {{.brand.name}}. Tất cả quyền được bảo lưu.
Hủy đăng ký: {{.unsubscribe_url}} | Tùy chọn: {{.preferences_url}}`,
			"is_active":   false,
			"is_approved": true,
		},
		// Password Reset Email
		{
			"tenant_id":      tenantID,
			"template_id":    templateIDs["password_reset_email"],
			"version_number": 1,
			"language":       "en",
			"subject":        "Reset Your {{.brand.name}} Password",
			"body_html": `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #dc3545; padding: 30px 20px; text-align: center; }
        .header h1 { color: #ffffff; margin: 0; font-size: 24px; }
        .content { padding: 40px 30px; text-align: center; }
        .content h2 { color: #333; font-size: 22px; margin-bottom: 20px; }
        .content p { color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 20px; }
        .cta-button { margin: 30px 0; }
        .cta-button a { 
            background-color: #dc3545;
            color: white; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 5px;
            font-weight: 600;
            display: inline-block;
        }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee; }
        .footer p { color: #666; font-size: 14px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Reset Request</h1>
        </div>
        
        <div class="content">
            <h2>Hello {{.user.name}}</h2>
            <p>We received a request to reset your password for your {{.brand.name}} account.</p>
            
            <div class="cta-button">
                <a href="{{.reset_url}}">Reset Your Password</a>
            </div>
            
            <div class="warning">
                <strong>⚠️ Security Notice:</strong><br>
                This password reset link will expire in {{.expiry_time}}. If you didn't request this reset, please ignore this email or contact our support team.
            </div>
            
            <p>If the button above doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #007bff;">{{.reset_url}}</p>
            
            <p>If you need help, contact us at <a href="mailto:{{.brand.support_email}}">{{.brand.support_email}}</a></p>
        </div>
        
        <div class="footer">
            <p>&copy; {{.current_year}} {{.brand.name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`,
			"body_text": `Password Reset Request

Hello {{.user.name}},

We received a request to reset your password for your {{.brand.name}} account.

Click this link to reset your password: {{.reset_url}}

⚠️ Security Notice: This link will expire in {{.expiry_time}}. If you didn't request this reset, please ignore this email.

If you need help, contact us at {{.brand.support_email}}

© {{.current_year}} {{.brand.name}}. All rights reserved.`,
			"is_active":   true,
			"is_approved": true,
		},
		// Email Verification - English Version
		{
			"tenant_id":      tenantID,
			"template_id":    templateIDs["email_verification"],
			"version_number": 1,
			"language":       "en",
			"subject":        "Verify Your Email Address - {{.brand.name}}",
			"body_html": `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email Address</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 40px 20px; text-align: center; }
        .header img { height: 50px; margin-bottom: 20px; }
        .header h1 { color: #ffffff; margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; text-align: center; }
        .verification-message { margin-bottom: 30px; }
        .verification-message h2 { color: #333; font-size: 24px; margin-bottom: 15px; }
        .verification-message p { color: #666; font-size: 16px; line-height: 1.6; }
        .cta-button { margin: 30px 0; }
        .cta-button a { 
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            display: inline-block;
            transition: transform 0.2s ease;
        }
        .cta-button a:hover { transform: translateY(-2px); }
        .info-box { background-color: #e8f4fd; border: 1px solid #bee5eb; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #eee; }
        .footer p { color: #666; font-size: 14px; margin: 5px 0; }
        .footer a { color: #28a745; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            {{if .brand.logo_url}}
            <img src="{{.brand.logo_url}}" alt="{{.brand.name}}" />
            {{end}}
            <h1>Verify Your Email</h1>
        </div>
        
        <div class="content">
            <div class="verification-message">
                <h2>Hello {{.user.name}}! 📧</h2>
                <p>Thank you for signing up with {{.brand.name}}. To complete your registration and start using your account, please verify your email address.</p>
            </div>
            
            <div class="cta-button">
                <a href="{{.verification_url}}">Verify Email Address</a>
            </div>
            
            <div class="info-box">
                <strong>📍 Important Information:</strong><br>
                • This verification link will expire in {{.expiry_time}}<br>
                • For security, this link can only be used once<br>
                • If you didn't create this account, please ignore this email
            </div>
            
            <p style="color: #666; line-height: 1.6;">
                If the button above doesn't work, copy and paste this link into your browser:
            </p>
            <p style="word-break: break-all; color: #007bff; font-size: 14px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                {{.verification_url}}
            </p>
            
            <p style="color: #666; line-height: 1.6; margin-top: 30px;">
                Need help? Contact our support team at 
                <a href="mailto:{{.brand.support_email}}" style="color: #28a745;">{{.brand.support_email}}</a>
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; {{.current_year}} {{.brand.name}}. All rights reserved.</p>
            <p>You received this email because you signed up for {{.brand.name}}</p>
        </div>
    </div>
</body>
</html>`,
			"body_text": `Verify Your Email Address - {{.brand.name}}

Hello {{.user.name}}!

Thank you for signing up with {{.brand.name}}. To complete your registration and start using your account, please verify your email address.

Click this link to verify your email: {{.verification_url}}

Important Information:
• This verification link will expire in {{.expiry_time}}
• For security, this link can only be used once  
• If you didn't create this account, please ignore this email

If the link doesn't work, copy and paste it into your browser:
{{.verification_url}}

Need help? Contact our support team at {{.brand.support_email}}

© {{.current_year}} {{.brand.name}}. All rights reserved.
You received this email because you signed up for {{.brand.name}}`,
			"is_active":   true,
			"is_approved": true,
		},
		// Email Verification - Vietnamese Version
		{
			"tenant_id":      tenantID,
			"template_id":    templateIDs["email_verification"],
			"version_number": 2,
			"language":       "vi",
			"subject":        "Xác Minh Địa Chỉ Email - {{.brand.name}}",
			"body_html": `<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xác Minh Địa Chỉ Email</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 40px 20px; text-align: center; }
        .header img { height: 50px; margin-bottom: 20px; }
        .header h1 { color: #ffffff; margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; text-align: center; }
        .verification-message { margin-bottom: 30px; }
        .verification-message h2 { color: #333; font-size: 24px; margin-bottom: 15px; }
        .verification-message p { color: #666; font-size: 16px; line-height: 1.6; }
        .cta-button { margin: 30px 0; }
        .cta-button a { 
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            display: inline-block;
            transition: transform 0.2s ease;
        }
        .cta-button a:hover { transform: translateY(-2px); }
        .info-box { background-color: #e8f4fd; border: 1px solid #bee5eb; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #eee; }
        .footer p { color: #666; font-size: 14px; margin: 5px 0; }
        .footer a { color: #28a745; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            {{if .brand.logo_url}}
            <img src="{{.brand.logo_url}}" alt="{{.brand.name}}" />
            {{end}}
            <h1>Xác Minh Email</h1>
        </div>
        
        <div class="content">
            <div class="verification-message">
                <h2>Xin chào {{.user.name}}! 📧</h2>
                <p>Cảm ơn bạn đã đăng ký với {{.brand.name}}. Để hoàn tất đăng ký và bắt đầu sử dụng tài khoản, vui lòng xác minh địa chỉ email của bạn.</p>
            </div>
            
            <div class="cta-button">
                <a href="{{.verification_url}}">Xác Minh Địa Chỉ Email</a>
            </div>
            
            <div class="info-box">
                <strong>📍 Thông Tin Quan Trọng:</strong><br>
                • Liên kết xác minh này sẽ hết hạn trong {{.expiry_time}}<br>
                • Vì lý do bảo mật, liên kết này chỉ có thể sử dụng một lần<br>
                • Nếu bạn không tạo tài khoản này, vui lòng bỏ qua email này
            </div>
            
            <p style="color: #666; line-height: 1.6;">
                Nếu nút ở trên không hoạt động, hãy sao chép và dán liên kết này vào trình duyệt của bạn:
            </p>
            <p style="word-break: break-all; color: #007bff; font-size: 14px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                {{.verification_url}}
            </p>
            
            <p style="color: #666; line-height: 1.6; margin-top: 30px;">
                Cần hỗ trợ? Liên hệ đội ngũ hỗ trợ của chúng tôi tại 
                <a href="mailto:{{.brand.support_email}}" style="color: #28a745;">{{.brand.support_email}}</a>
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; {{.current_year}} {{.brand.name}}. Tất cả quyền được bảo lưu.</p>
            <p>Bạn nhận được email này vì bạn đã đăng ký {{.brand.name}}</p>
        </div>
    </div>
</body>
</html>`,
			"body_text": `Xác Minh Địa Chỉ Email - {{.brand.name}}

Xin chào {{.user.name}}!

Cảm ơn bạn đã đăng ký với {{.brand.name}}. Để hoàn tất đăng ký và bắt đầu sử dụng tài khoản, vui lòng xác minh địa chỉ email của bạn.

Nhấp vào liên kết này để xác minh email: {{.verification_url}}

Thông Tin Quan Trọng:
• Liên kết xác minh này sẽ hết hạn trong {{.expiry_time}}
• Vì lý do bảo mật, liên kết này chỉ có thể sử dụng một lần
• Nếu bạn không tạo tài khoản này, vui lòng bỏ qua email này

Nếu liên kết không hoạt động, hãy sao chép và dán vào trình duyệt:
{{.verification_url}}

Cần hỗ trợ? Liên hệ đội ngũ hỗ trợ tại {{.brand.support_email}}

© {{.current_year}} {{.brand.name}}. Tất cả quyền được bảo lưu.
Bạn nhận được email này vì bạn đã đăng ký {{.brand.name}}`,
			"is_active":   false,
			"is_approved": true,
		},
		// Email Verification Resend Template
		{
			"tenant_id":      tenantID,
			"template_id":    templateIDs["email_verification_resend"],
			"version_number": 1,
			"language":       "en",
			"subject":        "Resent: Verify Your Email Address - {{.brand.name}}",
			"body_html": `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email Address (Resent)</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: #ffffff; margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; text-align: center; }
        .verification-message { margin-bottom: 30px; }
        .verification-message h2 { color: #333; font-size: 24px; margin-bottom: 15px; }
        .verification-message p { color: #666; font-size: 16px; line-height: 1.6; }
        .cta-button { margin: 30px 0; }
        .cta-button a { 
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            display: inline-block;
            transition: transform 0.2s ease;
        }
        .cta-button a:hover { transform: translateY(-2px); }
        .resend-info { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #eee; }
        .footer p { color: #666; font-size: 14px; margin: 5px 0; }
        .footer a { color: #ffc107; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Email Verification (Resent)</h1>
        </div>
        
        <div class="content">
            <div class="verification-message">
                <h2>Hello {{.user.name}}! 🔄</h2>
                <p>You requested another verification email for your {{.brand.name}} account. Here's your new verification link.</p>
            </div>
            
            <div class="cta-button">
                <a href="{{.verification_url}}">Verify Email Address</a>
            </div>
            
            <div class="resend-info">
                <strong>📊 Resend Information:</strong><br>
                • This is resend #{{.resend_count}} of {{.max_resends}} allowed<br>
                • This verification link will expire in {{.expiry_time}}<br>
                • Previous verification links are now invalid
            </div>
            
            <p style="color: #666; line-height: 1.6;">
                If the button above doesn't work, copy and paste this link into your browser:
            </p>
            <p style="word-break: break-all; color: #007bff; font-size: 14px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                {{.verification_url}}
            </p>
            
            <p style="color: #666; line-height: 1.6; margin-top: 30px;">
                Need help? Contact our support team at 
                <a href="mailto:{{.brand.support_email}}" style="color: #ffc107;">{{.brand.support_email}}</a>
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; {{.current_year}} {{.brand.name}}. All rights reserved.</p>
            <p>You received this email because you requested a new verification link</p>
        </div>
    </div>
</body>
</html>`,
			"body_text": `Email Verification (Resent) - {{.brand.name}}

Hello {{.user.name}}!

You requested another verification email for your {{.brand.name}} account. Here's your new verification link.

Click this link to verify your email: {{.verification_url}}

Resend Information:
• This is resend #{{.resend_count}} of {{.max_resends}} allowed
• This verification link will expire in {{.expiry_time}}
• Previous verification links are now invalid

If the link doesn't work, copy and paste it into your browser:
{{.verification_url}}

Need help? Contact our support team at {{.brand.support_email}}

© {{.current_year}} {{.brand.name}}. All rights reserved.
You received this email because you requested a new verification link`,
			"is_active":   true,
			"is_approved": true,
		},
	}

	for _, version := range versions {
		// Check if version exists
		var count int64
		s.db.Table("notification_template_versions").
			Where("tenant_id = ? AND template_id = ? AND version_number = ? AND language = ?", 
				version["tenant_id"], version["template_id"], version["version_number"], version["language"]).
			Count(&count)
		
		if count == 0 {
			// Create new version
			if err := s.db.Table("notification_template_versions").Create(&version).Error; err != nil {
				log.Printf("Error seeding template version: %v", err)
				return err
			}
		} else {
			// Update existing version
			if err := s.db.Table("notification_template_versions").
				Where("tenant_id = ? AND template_id = ? AND version_number = ? AND language = ?", 
					version["tenant_id"], version["template_id"], version["version_number"], version["language"]).
				Updates(&version).Error; err != nil {
				log.Printf("Error updating template version: %v", err)
				return err
			}
		}
	}

	// Update active_version_id for templates
	// First, get the version IDs that were created
	var templateVersions []struct {
		ID         uint
		TemplateID uint
		Language   string
	}
	
	if err := s.db.Table("notification_template_versions").
		Where("tenant_id = ?", tenantID).
		Select("id, template_id, language").
		Find(&templateVersions).Error; err != nil {
		return err
	}
	
	// Create mapping of template_id + language -> version_id
	versionMap := make(map[string]uint)
	for _, version := range templateVersions {
		key := fmt.Sprintf("%d_%s", version.TemplateID, version.Language)
		versionMap[key] = version.ID
	}
	
	// Update active_version_id for templates (using English versions as active)
	updates := []struct {
		templateCode string
		language     string
	}{
		{"user_welcome_email", "en"},
		{"password_reset_email", "en"},
		{"email_verification", "en"},
		{"email_verification_resend", "en"},
	}

	for _, update := range updates {
		templateID := templateIDs[update.templateCode]
		versionKey := fmt.Sprintf("%d_%s", templateID, update.language)
		activeVersionID := versionMap[versionKey]
		
		if activeVersionID > 0 {
			if err := s.db.Table("notification_templates").
				Where("id = ?", templateID).
				Update("active_version_id", activeVersionID).Error; err != nil {
				log.Printf("Error updating active_version_id: %v", err)
				return err
			}
		}
	}

	log.Printf("✅ Seeded notification template versions")
	return nil
}

func (s *NotificationTemplateSeeder) SeedAll() error {
	if err := s.Seed(); err != nil {
		return err
	}
	return s.createTemplateVersions()
}