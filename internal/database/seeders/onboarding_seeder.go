package seeders

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	onboardingModels "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// OnboardingSeeder handles seeding onboarding data
type OnboardingSeeder struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewOnboardingSeeder creates a new onboarding seeder
func NewOnboardingSeeder(db *gorm.DB, logger utils.Logger) *OnboardingSeeder {
	return &OnboardingSeeder{
		db:     db,
		logger: logger,
	}
}

// SeedOnboardingTemplates creates default onboarding templates
func (s *OnboardingSeeder) SeedOnboardingTemplates(ctx context.Context) error {
	s.logger.WithContext(ctx).Info("Seeding onboarding templates")

	templates := []*onboardingModels.OnboardingTemplate{
		// User Onboarding Template
		{
			TenantID:      0, // Global template
			Name:          "Standard User Onboarding",
			Slug:          "standard-user-onboarding",
			DisplayName:   "Standard User Onboarding",
			Description:   "Complete user onboarding flow for new registrations",
			TemplateType:  "journey",
			Category:      "user_registration",
			UseCase:       "new_user_registration",
			Version:       "1.0",
			Author:        "System",
			TemplateData:  `{"flow_type":"linear","completion_reward":{"type":"points","amount":100},"gamification":{"enabled":true,"points_per_step":10,"badges":["onboarding_complete","profile_complete"]},"email_notifications":{"enabled":true,"reminder_after":"24h","completion_email":true},"analytics":{"track_progress":true,"track_time":true,"track_dropoff":true}}`,
			DefaultConfig: `{"auto_start":true,"allow_skip":false,"timeout":"7d"}`,
			IsPublic:      true,
			IsSystemTemplate: true,
			Status:        "active",
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		// Tenant Setup Template
		{
			TenantID:      0, // Global template
			Name:          "Tenant Setup Onboarding",
			Slug:          "tenant-setup-onboarding",
			DisplayName:   "Tenant Setup Onboarding",
			Description:   "Guide new tenant owners through initial setup",
			TemplateType:  "journey",
			Category:      "tenant_management",
			UseCase:       "tenant_setup",
			Version:       "1.0",
			Author:        "System",
			TemplateData:  `{"flow_type":"guided","completion_reward":{"type":"feature_unlock","features":["advanced_analytics","custom_branding"]},"required_steps":["tenant_info","website_creation","payment_setup"],"optional_steps":["team_invite","integration_setup"]}`,
			DefaultConfig: `{"auto_start":false,"allow_skip":true,"timeout":"30d"}`,
			IsPublic:      true,
			IsSystemTemplate: true,
			Status:        "active",
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		// Website Setup Template
		{
			TenantID:      0, // Global template
			Name:          "Website Configuration",
			Slug:          "website-configuration",
			DisplayName:   "Website Configuration",
			Description:   "Setup and configure website for new tenants",
			TemplateType:  "journey",
			Category:      "website_management",
			UseCase:       "website_setup",
			Version:       "1.0",
			Author:        "System",
			TemplateData:  `{"flow_type":"checklist","steps":{"basic_info":{"required":true,"order":1},"domain_setup":{"required":true,"order":2},"theme_selection":{"required":false,"order":3},"content_setup":{"required":false,"order":4}}}`,
			DefaultConfig: `{"auto_start":false,"allow_skip":true,"timeout":"14d"}`,
			IsPublic:      true,
			IsSystemTemplate: true,
			Status:        "active",
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
	}

	for _, template := range templates {
		// Check if template already exists
		var existing onboardingModels.OnboardingTemplate
		err := s.db.Where("name = ? AND template_type = ?", template.Name, template.TemplateType).First(&existing).Error
		if err == gorm.ErrRecordNotFound {
			if err := s.db.Create(template).Error; err != nil {
				s.logger.WithContext(ctx).WithError(err).Error("Failed to seed onboarding template", map[string]interface{}{
					"template_name": template.Name,
				})
				return fmt.Errorf("failed to seed onboarding template %s: %w", template.Name, err)
			}
			s.logger.WithContext(ctx).Info("Created onboarding template", map[string]interface{}{
				"template_id":   template.ID,
				"template_name": template.Name,
				"template_type": template.TemplateType,
			})
		}
	}

	s.logger.WithContext(ctx).Info("Successfully seeded onboarding templates")
	return nil
}

// SeedDefaultJourneys creates default onboarding journeys for test tenants
func (s *OnboardingSeeder) SeedDefaultJourneys(ctx context.Context, tenantID uint) error {
	s.logger.WithContext(ctx).Info("Seeding default onboarding journeys", map[string]interface{}{
		"tenant_id": tenantID,
	})

	// User Registration Journey
	userJourney := &onboardingModels.OnboardingJourney{
		TenantID:    tenantID,
		Name:        "User Registration Flow",
		Slug:        "user-registration-flow",
		DisplayName: "User Registration Flow",
		Description: "Complete onboarding for new users",
		JourneyType: "user",
		TriggerType: "automatic",
		Status:      "active",
		IsActive:    true,
		IsRequired:  true,
		IsSkippable: false,
		EstimatedDuration: func() *uint { d := uint(30); return &d }(), // 30 minutes
		StartCondition: `{"auto_start":true,"trigger_event":"user_registration"}`,
		CompletionCriteria: `{"completion_url":"/dashboard","skip_allowed":false,"max_duration":"7d"}`,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Skip template association for now

	if err := s.db.Create(userJourney).Error; err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to create user journey")
		return fmt.Errorf("failed to create user journey: %w", err)
	}

	// Create steps for user journey
	userSteps := []*onboardingModels.OnboardingStep{
		{
			JourneyID:         userJourney.ID,
			Name:              "Welcome & Profile Setup",
			Slug:              "welcome-profile-setup",
			DisplayName:       "Welcome & Profile Setup",
			Description:       "Complete your profile information",
			StepOrder:         1,
			StepType:          "form",
			Title:             "Complete Your Profile",
			IsRequired:        true,
			IsSkippable:       false,
			IsBlocking:        true,
			AutoAdvance:       false,
			EstimatedDuration: func() *uint { d := uint(5); return &d }(),
			Content: `{"form":{"title":"Complete Your Profile","fields":[{"name":"first_name","type":"text","label":"First Name","required":true,"placeholder":"Enter your first name"},{"name":"last_name","type":"text","label":"Last Name","required":true,"placeholder":"Enter your last name"},{"name":"bio","type":"textarea","label":"Bio","required":false,"placeholder":"Tell us about yourself"}]}}`,
			Status:            "active",
		},
		{
			JourneyID:         userJourney.ID,
			Name:              "Choose Your Preferences",
			Slug:              "choose-preferences",
			DisplayName:       "Choose Your Preferences",
			Description:       "Set up your account preferences",
			StepOrder:         2,
			StepType:          "form",
			Title:             "Account Preferences",
			IsRequired:        true,
			IsSkippable:       false,
			IsBlocking:        true,
			AutoAdvance:       false,
			EstimatedDuration: func() *uint { d := uint(3); return &d }(),
			Content: `{"preferences":{"title":"Account Preferences","options":[{"name":"email_notifications","type":"boolean","label":"Email Notifications","default":true},{"name":"theme","type":"select","label":"Theme","options":["light","dark","auto"],"default":"light"},{"name":"timezone","type":"timezone","label":"Timezone","default":"UTC"}]}}`,
			Status:            "active",
		},
		{
			JourneyID:         userJourney.ID,
			Name:              "Tour the Dashboard",
			Slug:              "tour-dashboard",
			DisplayName:       "Tour the Dashboard",
			Description:       "Learn about key features",
			StepOrder:         3,
			StepType:          "tutorial",
			Title:             "Dashboard Tour",
			IsRequired:        false,
			IsSkippable:       true,
			IsBlocking:        false,
			AutoAdvance:       true,
			EstimatedDuration: func() *uint { d := uint(8); return &d }(),
			Content: `{"tutorial":{"type":"guided_tour","steps":[{"target":"#navigation","title":"Navigation","description":"Use the navigation menu to access different sections","position":"bottom"},{"target":"#dashboard-widgets","title":"Dashboard Widgets","description":"Your dashboard shows key metrics and activities","position":"top"},{"target":"#settings-menu","title":"Settings","description":"Access your account settings here","position":"left"}]}}`,
			Status:            "active",
		},
	}

	for _, step := range userSteps {
		if err := s.db.Create(step).Error; err != nil {
			s.logger.WithContext(ctx).WithError(err).Error("Failed to create onboarding step")
			return fmt.Errorf("failed to create onboarding step: %w", err)
		}
	}

	s.logger.WithContext(ctx).Info("Successfully seeded default onboarding journeys", map[string]interface{}{
		"tenant_id": tenantID,
		"journeys":  1,
		"steps":     len(userSteps),
	})

	return nil
}

// SeedTenantSetupJourney creates a tenant setup journey
func (s *OnboardingSeeder) SeedTenantSetupJourney(ctx context.Context, tenantID uint) error {
	s.logger.WithContext(ctx).Info("Seeding tenant setup journey", map[string]interface{}{
		"tenant_id": tenantID,
	})

	// Tenant Setup Journey
	tenantJourney := &onboardingModels.OnboardingJourney{
		TenantID:    tenantID,
		Name:        "Tenant Setup Guide",
		Slug:        "tenant-setup-guide",
		DisplayName: "Tenant Setup Guide",
		Description: "Complete setup for your organization",
		JourneyType: "admin",
		TriggerType: "manual",
		Status:      "active",
		IsActive:    true,
		IsRequired:  false,
		IsSkippable: true,
		EstimatedDuration: func() *uint { d := uint(45); return &d }(), // 45 minutes
		StartCondition: `{"auto_start":false,"trigger_event":"tenant_creation"}`,
		CompletionCriteria: `{"completion_url":"/admin/dashboard","skip_allowed":true,"max_duration":"30d"}`,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.db.Create(tenantJourney).Error; err != nil {
		return fmt.Errorf("failed to create tenant journey: %w", err)
	}

	// Create steps for tenant setup
	tenantSteps := []*onboardingModels.OnboardingStep{
		{
			JourneyID:         tenantJourney.ID,
			Name:              "Organization Information",
			Slug:              "organization-information",
			DisplayName:       "Organization Information",
			Description:       "Set up your organization details",
			StepOrder:         1,
			StepType:          "form",
			Title:             "Organization Setup",
			IsRequired:        true,
			IsSkippable:       false,
			IsBlocking:        true,
			AutoAdvance:       false,
			EstimatedDuration: func() *uint { d := uint(10); return &d }(),
			Content: `{"form":{"title":"Organization Setup","fields":[{"name":"org_name","type":"text","label":"Organization Name","required":true},{"name":"org_type","type":"select","label":"Organization Type","options":["startup","small_business","enterprise","non_profit"],"required":true},{"name":"industry","type":"select","label":"Industry","options":["technology","healthcare","finance","education","retail","other"],"required":false}]}}`,
			Status:            "active",
		},
		{
			JourneyID:         tenantJourney.ID,
			Name:              "Create Your First Website",
			Slug:              "create-first-website",
			DisplayName:       "Create Your First Website",
			Description:       "Set up your primary website",
			StepOrder:         2,
			StepType:          "action",
			Title:             "Website Creation",
			IsRequired:        true,
			IsSkippable:       false,
			IsBlocking:        true,
			AutoAdvance:       false,
			EstimatedDuration: func() *uint { d := uint(15); return &d }(),
			Content: `{"action":{"action_type":"create_website","redirect_url":"/websites/create","success_check":"website_created","instructions":"Click the button below to create your first website"}}`,
			Status:            "active",
		},
		{
			JourneyID:         tenantJourney.ID,
			Name:              "Invite Team Members",
			Slug:              "invite-team-members",
			DisplayName:       "Invite Team Members",
			Description:       "Add team members to your organization",
			StepOrder:         3,
			StepType:          "action",
			Title:             "Team Invitation",
			IsRequired:        false,
			IsSkippable:       true,
			IsBlocking:        false,
			AutoAdvance:       false,
			EstimatedDuration: func() *uint { d := uint(5); return &d }(),
			Content: `{"action":{"action_type":"invite_members","redirect_url":"/team/invite","instructions":"Invite your team members to collaborate"}}`,
			Status:            "active",
		},
	}

	for _, step := range tenantSteps {
		if err := s.db.Create(step).Error; err != nil {
			return fmt.Errorf("failed to create tenant setup step: %w", err)
		}
	}

	s.logger.WithContext(ctx).Info("Successfully seeded tenant setup journey")
	return nil
}

// SeedAll seeds all onboarding data
func (s *OnboardingSeeder) SeedAll(ctx context.Context) error {
	s.logger.WithContext(ctx).Info("Starting complete onboarding data seeding")

	// Seed templates first
	if err := s.SeedOnboardingTemplates(ctx); err != nil {
		return fmt.Errorf("failed to seed onboarding templates: %w", err)
	}

	s.logger.WithContext(ctx).Info("Onboarding seeding completed successfully")
	return nil
}

// CleanAll removes all seeded onboarding data
func (s *OnboardingSeeder) CleanAll(ctx context.Context) error {
	s.logger.WithContext(ctx).Info("Cleaning onboarding seed data")

	// Clean in reverse order of dependencies
	tables := []string{
		"onboarding_analytics",
		"onboarding_progress", 
		"onboarding_steps",
		"onboarding_journeys",
		"onboarding_templates",
	}

	for _, table := range tables {
		if err := s.db.Exec(fmt.Sprintf("DELETE FROM %s WHERE 1=1", table)).Error; err != nil {
			s.logger.WithContext(ctx).WithError(err).Warn("Failed to clean table", map[string]interface{}{
				"table": table,
			})
		}
	}

	s.logger.WithContext(ctx).Info("Onboarding seed data cleaned")
	return nil
}