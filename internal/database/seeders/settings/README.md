# Settings Module - Database Seeders

This directory contains seed data for the Settings Module, which provides multi-level configuration management for the WN API v3 system.

## Files

### 001_setting_schemas.sql
Creates default setting schemas that define:
- JSON validation schemas for each setting
- UI metadata for admin interfaces
- Default values and constraints
- Documentation and examples

### 002_default_settings.sql
Creates default system settings based on the schemas, including:
- System general settings (app name, timezone, locale)
- Security settings (session timeout, password requirements, 2FA)
- Auth module settings (login attempts, OAuth providers)
- Email configuration (SMTP, from address)
- Media settings (upload limits, storage drivers)

## Usage

These seeders should be run after the settings tables are created:

```bash
# Run migrations first
make migrate-up MODULE=n_settings

# Then run seeders
mysql -u username -p database_name < internal/database/seeders/settings/001_setting_schemas.sql
mysql -u username -p database_name < internal/database/seeders/settings/002_default_settings.sql
```

## Settings Hierarchy

The Settings Module supports hierarchical configuration with the following scopes:

1. **System** - Global application settings
2. **Tenant** - Tenant-specific overrides
3. **Module** - Module-specific configuration
4. **User** - User-specific preferences
5. **Feature** - Feature-specific settings

Settings are resolved in hierarchical order, with more specific scopes overriding general ones.

## Adding New Settings

To add new settings:

1. Create a schema in `setting_schemas` table
2. Add the default setting in `settings` table
3. Update the appropriate seeder files
4. Document the setting purpose and usage