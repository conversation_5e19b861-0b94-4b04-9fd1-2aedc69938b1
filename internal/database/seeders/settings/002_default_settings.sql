-- Seeder: 002_default_settings.sql
-- Description: Seed default system settings
-- Author: System
-- Date: 2025-01-18

-- Default system settings based on the schemas
INSERT INTO settings (
    scope_type, scope_id, module_name, group_name, setting_key, setting_value,
    display_name, description, data_type, is_public, is_required,
    default_value, ui_component, ui_order, ui_group, is_override
) VALUES

-- System General Settings
('system', NULL, 'system', 'general', 'app_name', 
 JSON_OBJECT('value', 'WN API v3'),
 'Application Name', 'The name of your application', 'string', true, true,
 JSON_OBJECT('value', 'WN API v3'), 'input', 1, 'general', false),

('system', NULL, 'system', 'general', 'app_description',
 JSON_OBJECT('value', 'Multi-tenant blog and CMS platform'),
 'Application Description', 'Brief description of your application', 'string', true, false,
 JSON_OBJECT('value', 'Multi-tenant blog and CMS platform'), 'textarea', 2, 'general', false),

('system', NULL, 'system', 'general', 'timezone',
 JSON_OBJECT('value', 'UTC'),
 'Default Timezone', 'Default timezone for the application', 'enum', true, true,
 JSON_OBJECT('value', 'UTC'), 'select', 3, 'general', false),

('system', NULL, 'system', 'general', 'locale',
 JSON_OBJECT('value', 'en'),
 'Default Locale', 'Default language for the application', 'enum', true, true,
 JSON_OBJECT('value', 'en'), 'select', 4, 'general', false),

-- System Security Settings
('system', NULL, 'system', 'security', 'session_timeout',
 JSON_OBJECT('value', 3600),
 'Session Timeout', 'Session timeout in seconds', 'number', false, true,
 JSON_OBJECT('value', 3600), 'input', 2, 'security', false),

('system', NULL, 'system', 'security', 'password_min_length',
 JSON_OBJECT('value', 8),
 'Minimum Password Length', 'Minimum required password length', 'number', true, true,
 JSON_OBJECT('value', 8), 'input', 3, 'security', false),

('system', NULL, 'system', 'security', 'require_2fa',
 JSON_OBJECT('value', false),
 'Require 2FA', 'Require two-factor authentication for all users', 'boolean', true, false,
 JSON_OBJECT('value', false), 'checkbox', 4, 'security', false),

-- Auth Module Settings
('system', NULL, 'auth', 'security', 'max_login_attempts',
 JSON_OBJECT('value', 5),
 'Max Login Attempts', 'Maximum login attempts before lockout', 'number', false, true,
 JSON_OBJECT('value', 5), 'input', 1, 'security', false),

('system', NULL, 'auth', 'security', 'lockout_duration',
 JSON_OBJECT('value', 900),
 'Lockout Duration', 'Account lockout duration in seconds', 'number', false, true,
 JSON_OBJECT('value', 900), 'input', 2, 'security', false),

('system', NULL, 'auth', 'oauth', 'enable_google',
 JSON_OBJECT('value', false),
 'Enable Google OAuth', 'Allow users to login with Google', 'boolean', true, false,
 JSON_OBJECT('value', false), 'checkbox', 1, 'oauth', false),

('system', NULL, 'auth', 'oauth', 'enable_github',
 JSON_OBJECT('value', false),
 'Enable GitHub OAuth', 'Allow users to login with GitHub', 'boolean', true, false,
 JSON_OBJECT('value', false), 'checkbox', 2, 'oauth', false),

-- Email Settings
('system', NULL, 'email', 'smtp', 'host',
 JSON_OBJECT('value', 'localhost'),
 'SMTP Host', 'SMTP server hostname', 'string', false, true,
 JSON_OBJECT('value', 'localhost'), 'input', 1, 'smtp', false),

('system', NULL, 'email', 'smtp', 'port',
 JSON_OBJECT('value', 587),
 'SMTP Port', 'SMTP server port', 'number', false, true,
 JSON_OBJECT('value', 587), 'input', 2, 'smtp', false),

('system', NULL, 'email', 'general', 'from_name',
 JSON_OBJECT('value', 'WN API v3'),
 'From Name', 'Default sender name for emails', 'string', true, true,
 JSON_OBJECT('value', 'WN API v3'), 'input', 1, 'general', false),

('system', NULL, 'email', 'general', 'from_address',
 JSON_OBJECT('value', '<EMAIL>'),
 'From Address', 'Default sender email address', 'string', true, true,
 JSON_OBJECT('value', '<EMAIL>'), 'input', 2, 'general', false),

-- Media Settings
('system', NULL, 'media', 'upload', 'max_file_size',
 JSON_OBJECT('value', 10485760),
 'Max Upload Size', 'Maximum file upload size in bytes', 'number', true, true,
 JSON_OBJECT('value', 10485760), 'input', 1, 'upload', false),

('system', NULL, 'media', 'upload', 'allowed_types',
 JSON_OBJECT('value', JSON_ARRAY('image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf')),
 'Allowed File Types', 'MIME types allowed for file uploads', 'array', true, true,
 JSON_OBJECT('value', JSON_ARRAY('image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf')), 'select', 2, 'upload', false),

('system', NULL, 'media', 'storage', 'driver',
 JSON_OBJECT('value', 'local'),
 'Storage Driver', 'File storage driver to use', 'enum', false, true,
 JSON_OBJECT('value', 'local'), 'select', 1, 'storage', false);