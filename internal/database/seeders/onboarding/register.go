package onboarding

import (
	"context"
	"database/sql"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// OnboardingModuleSeeder wraps all onboarding seeders
type OnboardingModuleSeeder struct {
	db             *sql.DB
	progressSeeder *OnboardingProgressSeeder
}

// NewOnboardingModuleSeeder creates a new onboarding module seeder
func NewOnboardingModuleSeeder(db *sql.DB) *OnboardingModuleSeeder {
	return &OnboardingModuleSeeder{
		db:             db,
		progressSeeder: &OnboardingProgressSeeder{},
	}
}

// Name returns the name of this seeder
func (s *OnboardingModuleSeeder) Name() string {
	return "onboarding_module"
}

// Seed runs all onboarding seeders in the correct order
func (s *OnboardingModuleSeeder) Seed(ctx context.Context) error {
	// Convert sql.DB to gorm.DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: s.db,
	}), &gorm.Config{})
	if err != nil {
		return err
	}

	// Run seeders in dependency order
	seeders := []interface {
		Seed(*gorm.DB) error
	}{
		s.progressSeeder, // Onboarding progress records
	}

	for _, seeder := range seeders {
		if err := seeder.Seed(gormDB); err != nil {
			return err
		}
	}

	return nil
}

// GetAllSeeders returns all individual onboarding seeders for registration
func GetAllSeeders() map[string]interface{} {
	return map[string]interface{}{
		"onboarding_progress": &OnboardingProgressSeeder{},
	}
}
