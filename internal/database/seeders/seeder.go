package seeders

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MasterSeederInterface interface for master seeder functionality
type MasterSeederInterface interface {
	SeedAll(ctx context.Context) error
	CleanAll(ctx context.Context) error
}

// MasterSeeder manages all application seeders
type MasterSeeder struct {
	db               *gorm.DB
	logger           utils.Logger
	onboardingSeeder *OnboardingSeeder
}

// NewMasterSeeder creates a new master seeder
func NewMasterSeeder(db *gorm.DB, logger utils.Logger) *MasterSeeder {
	return &MasterSeeder{
		db:               db,
		logger:           logger,
		onboardingSeeder: NewOnboardingSeeder(db, logger),
	}
}

// SeedAll runs all seeders
func (s *MasterSeeder) SeedAll(ctx context.Context) error {
	s.logger.WithContext(ctx).Info("Starting complete database seeding")

	seeders := []struct {
		name   string
		seeder MasterSeederInterface
	}{
		{"onboarding", s.onboardingSeeder},
	}

	for _, seederInfo := range seeders {
		s.logger.WithContext(ctx).Info("Running seeder", map[string]interface{}{
			"seeder": seederInfo.name,
		})

		if err := seederInfo.seeder.SeedAll(ctx); err != nil {
			s.logger.WithContext(ctx).WithError(err).Error("Seeder failed", map[string]interface{}{
				"seeder": seederInfo.name,
			})
			return fmt.Errorf("failed to run %s seeder: %w", seederInfo.name, err)
		}

		s.logger.WithContext(ctx).Info("Seeder completed", map[string]interface{}{
			"seeder": seederInfo.name,
		})
	}

	s.logger.WithContext(ctx).Info("Database seeding completed successfully")
	return nil
}

// CleanAll removes all seeded data
func (s *MasterSeeder) CleanAll(ctx context.Context) error {
	s.logger.WithContext(ctx).Info("Starting complete database cleanup")

	seeders := []struct {
		name   string
		seeder MasterSeederInterface
	}{
		{"onboarding", s.onboardingSeeder},
	}

	// Clean in reverse order
	for i := len(seeders) - 1; i >= 0; i-- {
		seederInfo := seeders[i]
		s.logger.WithContext(ctx).Info("Cleaning seeder data", map[string]interface{}{
			"seeder": seederInfo.name,
		})

		if err := seederInfo.seeder.CleanAll(ctx); err != nil {
			s.logger.WithContext(ctx).WithError(err).Warn("Failed to clean seeder data", map[string]interface{}{
				"seeder": seederInfo.name,
			})
		}
	}

	s.logger.WithContext(ctx).Info("Database cleanup completed")
	return nil
}

// SeedOnboarding seeds only onboarding data
func (s *MasterSeeder) SeedOnboarding(ctx context.Context) error {
	return s.onboardingSeeder.SeedAll(ctx)
}

// SeedOnboardingForTenant seeds onboarding data for specific tenant
func (s *MasterSeeder) SeedOnboardingForTenant(ctx context.Context, tenantID uint) error {
	s.logger.WithContext(ctx).Info("Seeding onboarding data for tenant", map[string]interface{}{
		"tenant_id": tenantID,
	})

	if err := s.onboardingSeeder.SeedDefaultJourneys(ctx, tenantID); err != nil {
		return fmt.Errorf("failed to seed default journeys: %w", err)
	}

	if err := s.onboardingSeeder.SeedTenantSetupJourney(ctx, tenantID); err != nil {
		return fmt.Errorf("failed to seed tenant setup journey: %w", err)
	}

	return nil
}