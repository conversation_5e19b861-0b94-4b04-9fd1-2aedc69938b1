-- Migration: 001_create_sessions_table
-- Description: Create sessions table for user session management (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS auth_sessions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Session Information
    token TEXT NOT NULL COMMENT 'Session token (can be long JWT)',
    
    -- Device Information
    device_name VARCHAR(100) COMMENT 'Device name (optional)',
    device_type ENUM('mobile', 'desktop', 'tablet', 'unknown') DEFAULT 'unknown' COMMENT 'Device type',
    os VARCHAR(50) COMMENT 'Operating system',
    browser VARCHAR(50) COMMENT 'Browser name',
    device_id VARCHAR(64) COMMENT 'Unique device identifier',
    
    -- Location Information
    ip_address VARCHAR(45) COMMENT 'IP address (IPv4/IPv6)',
    country VARCHAR(100) COMMENT 'Country name',
    city VARCHAR(100) COMMENT 'City name',
    
    -- Session State
    is_revoked BOOLEAN DEFAULT FALSE COMMENT 'Whether session is revoked',
    last_used_at TIMESTAMP NULL DEFAULT NULL COMMENT 'Last activity time',
    expires_at TIMESTAMP NOT NULL COMMENT 'Session expiration time',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    -- CONSTRAINT fk_auth_sessions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_sessions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes for Performance
    INDEX idx_auth_sessions_tenant_id (tenant_id),
    INDEX idx_auth_sessions_user_id (user_id),
    INDEX idx_auth_sessions_token (token(255)),
    INDEX idx_auth_sessions_device_id (device_id),
    INDEX idx_auth_sessions_expires_at (expires_at),
    INDEX idx_auth_sessions_is_revoked (is_revoked),
    INDEX idx_auth_sessions_last_used_at (last_used_at),
    
    -- Composite indexes for common queries
    INDEX idx_auth_sessions_user_active (user_id, is_revoked, expires_at),
    INDEX idx_auth_sessions_tenant_active (tenant_id, is_revoked, expires_at),
    INDEX idx_auth_sessions_cleanup (is_revoked, expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User sessions table for session management and device tracking';