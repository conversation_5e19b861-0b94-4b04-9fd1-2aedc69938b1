-- Migration: 201_create_users_table
-- Description: Create the global users table for multi-tenant user management (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS users (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Basic Information
    email VARCHAR(255) NOT NULL,
    username <PERSON><PERSON><PERSON><PERSON>(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    display_name <PERSON><PERSON><PERSON><PERSON>(255),
    
    -- Authentication
    password_hash VARCHAR(255) NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMP NULL DEFAULT NULL,
    
    -- Status and Configuration
    status VARCHAR(50) NOT NULL DEFAULT 'active' COMMENT 'Current status: active, suspended, inactive, pending_verification, deleted',
    
    -- Contact Information
    phone VARCHAR(50),
    phone_verified BOOLEAN DEFAULT FALSE,
    phone_verified_at TIMESTAMP NULL DEFAULT NULL,
    
    -- Profile Information
    avatar_url VARCHAR(500),
    timezone VARCHAR(100) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    
    -- Security
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255),
    recovery_codes JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of recovery codes',
    
    -- Activity Tracking
    last_login_at TIMESTAMP NULL DEFAULT NULL,
    last_login_ip VARCHAR(45),
    login_count INT UNSIGNED DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_users_status CHECK (status IN ('active', 'suspended', 'inactive', 'pending_verification', 'deleted')),
    CONSTRAINT chk_users_email_format CHECK (email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_users_username_format CHECK (username IS NULL OR username REGEXP '^[a-zA-Z0-9_-]{3,30}$'),
    CONSTRAINT chk_users_phone_format CHECK (phone IS NULL OR phone REGEXP '^\\+?[1-9]\\d{1,14}$'),
    
    -- Unique Constraints (email globally unique, username globally unique)
    UNIQUE KEY uk_users_email (email),
    UNIQUE KEY uk_users_username (username),
    
    -- Indexes
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_status (status),
    INDEX idx_users_created_at (created_at),
    INDEX idx_users_last_login (last_login_at),
    INDEX idx_users_email_verified (email_verified)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Global users table - users exist independently of tenants, relationships managed via tenant_memberships.';