-- Migration: 206_create_user_invitations_table
-- Description: Create the user invitations table for tenant invitation system (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS user_invitations (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NULL,
    
    -- Invitation Details
    token VARCHAR(255) NOT NULL COMMENT 'Unique invitation token',
    email VARCHAR(255) NOT NULL COMMENT 'Email address of the invitee',
    
    -- Invitation Configuration
    role_id INT UNSIGNED NULL COMMENT 'Role to assign when invitation is accepted',
    message TEXT NULL COMMENT 'Personal message from the inviter',
    
    -- Status and Tracking
    status VARCHAR(50) NOT NULL DEFAULT 'pending' COMMENT 'Invitation status: pending, accepted, rejected, expired, revoked',
    invited_by INT UNSIGNED NOT NULL COMMENT 'User ID who sent the invitation',
    invited_user_id INT UNSIGNED NULL COMMENT 'User ID if invitee already exists',
    
    -- Timestamps
    expires_at TIMESTAMP NOT NULL COMMENT 'When the invitation expires',
    accepted_at TIMESTAMP NULL DEFAULT NULL,
    rejected_at TIMESTAMP NULL DEFAULT NULL,
    revoked_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_user_invitations_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_invitations_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_invitations_invited_by FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_invitations_invited_user_id FOREIGN KEY (invited_user_id) REFERENCES users(id) ON DELETE SET NULL,
    -- CONSTRAINT fk_user_invitations_role_id FOREIGN KEY (role_id) REFERENCES rbac_roles(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_user_invitations_status CHECK (status IN ('pending', 'accepted', 'rejected', 'expired', 'revoked')),
    CONSTRAINT chk_user_invitations_email_format CHECK (email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_user_invitations_expires_at CHECK (expires_at > created_at),
    
    -- Unique Constraints
    UNIQUE KEY uk_user_invitations_token (token),
    UNIQUE KEY uk_user_invitations_tenant_email_pending (tenant_id, email, status),
    
    -- Indexes
    INDEX idx_user_invitations_tenant_id (tenant_id),
    INDEX idx_user_invitations_email (email),
    INDEX idx_user_invitations_status (status),
    INDEX idx_user_invitations_invited_by (invited_by),
    INDEX idx_user_invitations_invited_user_id (invited_user_id),
    INDEX idx_user_invitations_expires_at (expires_at),
    INDEX idx_user_invitations_created_at (created_at),
    INDEX idx_user_invitations_website_id (website_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User invitations table for tenant invitation system with role assignments.';