-- Migration: 205_create_tenant_memberships_table
-- Description: Create the tenant memberships table for many-to-many user-tenant relationships (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS tenant_memberships (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Membership Details
    status VARCHAR(50) NOT NULL DEFAULT 'active' COMMENT 'Membership status: active, inactive, suspended, pending, deleted',
    is_primary BOOLEAN DEFAULT FALSE COMMENT 'Marks the user primary/default tenant',
    local_username VARCHAR(255) COMMENT 'Username within this tenant (can be different from global username)',
    display_name VARCHAR(255) COMMENT 'Display name within this tenant',
    
    -- Invitation Details
    invited_by INT UNSIGNED NULL COMMENT 'User ID who invited this member',
    invitation_token VARCHAR(255) NULL COMMENT 'Token used for invitation',
    invitation_accepted_at TIMESTAMP NULL DEFAULT NULL,
    
    -- Activity Tracking
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP NULL DEFAULT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_tenant_memberships_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_tenant_memberships_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_tenant_memberships_invited_by FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_tenant_memberships_status CHECK (status IN ('active', 'inactive', 'suspended', 'pending', 'deleted')),
    CONSTRAINT chk_tenant_memberships_local_username CHECK (local_username IS NULL OR local_username REGEXP '^[a-zA-Z0-9_-]{3,30}$'),
    
    -- Unique Constraints
    UNIQUE KEY uk_tenant_memberships_user_tenant (user_id, tenant_id),
    UNIQUE KEY uk_tenant_memberships_tenant_local_username (tenant_id, local_username),
    
    -- Indexes
    INDEX idx_tenant_memberships_user_id (user_id),
    INDEX idx_tenant_memberships_tenant_id (tenant_id),
    INDEX idx_tenant_memberships_status (status),
    INDEX idx_tenant_memberships_is_primary (is_primary),
    INDEX idx_tenant_memberships_user_primary (user_id, is_primary),
    INDEX idx_tenant_memberships_invited_by (invited_by),
    INDEX idx_tenant_memberships_invitation_token (invitation_token),
    INDEX idx_tenant_memberships_joined_at (joined_at),
    INDEX idx_tenant_memberships_last_activity (last_activity_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Many-to-many relationship table for user-tenant memberships with local customization.';