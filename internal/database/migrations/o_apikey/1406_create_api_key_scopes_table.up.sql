-- Create API Key Scopes table
CREATE TABLE IF NOT EXISTS api_key_scopes (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    api_key_id INT UNSIGNED NOT NULL,
    scope VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    actions JSON NOT NULL,
    conditions JSON DEFAULT (JSON_OBJECT()),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    expires_at DATETIME NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_api_key_scopes_tenant_id (tenant_id),
    INDEX idx_api_key_scopes_website_id (website_id),
    INDEX idx_api_key_scopes_api_key_id (api_key_id),
    INDEX idx_api_key_scopes_scope (scope),
    INDEX idx_api_key_scopes_resource (resource),
    INDEX idx_api_key_scopes_is_active (is_active),
    INDEX idx_api_key_scopes_expires_at (expires_at),
    INDEX idx_api_key_scopes_created_at (created_at),
    INDEX idx_api_key_scopes_updated_at (updated_at),
    
    -- Composite indexes for common queries
    INDEX idx_api_key_scopes_key_scope (api_key_id, scope),
    INDEX idx_api_key_scopes_key_resource (api_key_id, resource),
    INDEX idx_api_key_scopes_active_scope (is_active, scope),
    INDEX idx_api_key_scopes_tenant_resource (tenant_id, resource),
    
    -- Foreign key constraints
    CONSTRAINT fk_api_key_scopes_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_scopes_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_scopes_api_key_id FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    
    -- Unique constraints
    UNIQUE KEY uk_api_key_scopes_key_scope_resource (api_key_id, scope, resource),
    
    -- Check constraints
    CHECK (LENGTH(scope) > 0),
    CHECK (LENGTH(resource) > 0),
    CHECK (JSON_VALID(actions)),
    CHECK (JSON_VALID(conditions)),
    CHECK (JSON_LENGTH(actions) > 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;