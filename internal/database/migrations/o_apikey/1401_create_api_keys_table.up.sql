-- Create API Keys table
CREATE TABLE IF NOT EXISTS api_keys (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    key_hash VARCHAR(64) NOT NULL UNIQUE,
    key_prefix VARCHAR(12) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive', 'expired', 'revoked', 'deleted') NOT NULL DEFAULT 'active',
    permissions JSON DEFAULT (JSON_OBJECT()),
    scopes J<PERSON><PERSON> DEFAULT (JSON_ARRAY()),
    ip_whitelist J<PERSON><PERSON> DEFAULT (JSON_ARRAY()),
    rate_limit INT UNSIGNED NOT NULL DEFAULT 1000,
    rate_window INT UNSIGNED NOT NULL DEFAULT 3600,
    expires_at DATETIME NULL,
    last_used_at DATETIME NULL,
    created_by INT <PERSON>SIGNED NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_api_keys_tenant_id (tenant_id),
    INDEX idx_api_keys_website_id (website_id),
    INDEX idx_api_keys_key_prefix (key_prefix),
    INDEX idx_api_keys_status (status),
    INDEX idx_api_keys_expires_at (expires_at),
    INDEX idx_api_keys_last_used_at (last_used_at),
    INDEX idx_api_keys_created_by (created_by),
    INDEX idx_api_keys_created_at (created_at),
    
    -- Foreign key constraints
    CONSTRAINT fk_api_keys_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_keys_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_keys_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Unique constraints
    UNIQUE KEY uk_api_keys_tenant_website_name (tenant_id, website_id, name),
    
    -- Check constraints
    CHECK (rate_limit > 0),
    CHECK (rate_window > 0),
    CHECK (LENGTH(key_hash) = 64),
    CHECK (LENGTH(key_prefix) BETWEEN 8 AND 12),
    CHECK (LENGTH(name) > 0),
    CHECK (JSON_VALID(permissions)),
    CHECK (JSON_VALID(scopes)),
    CHECK (JSON_VALID(ip_whitelist))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;