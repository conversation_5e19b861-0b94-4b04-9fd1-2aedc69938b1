-- Create API Key Webhooks table
CREATE TABLE IF NOT EXISTS api_key_webhooks (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    api_key_id INT UNSIGNED NOT NULL,
    webhook_url VARCHAR(500) NOT NULL,
    webhook_secret VARCHAR(100),
    events JSON NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_triggered_at DATETIME NULL,
    success_count INT UNSIGNED DEFAULT 0,
    failure_count INT UNSIGNED DEFAULT 0,
    last_success_at DATETIME NULL,
    last_failure_at DATETIME NULL,
    last_failure_reason TEXT,
    retry_count INT UNSIGNED DEFAULT 0,
    max_retries INT UNSIGNED DEFAULT 3,
    timeout_seconds INT UNSIGNED DEFAULT 30,
    metadata JSON DEFAULT (JSO<PERSON>_OBJECT()),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_api_key_webhooks_tenant_id (tenant_id),
    INDEX idx_api_key_webhooks_website_id (website_id),
    INDEX idx_api_key_webhooks_api_key_id (api_key_id),
    INDEX idx_api_key_webhooks_is_active (is_active),
    INDEX idx_api_key_webhooks_last_triggered_at (last_triggered_at),
    INDEX idx_api_key_webhooks_success_count (success_count),
    INDEX idx_api_key_webhooks_failure_count (failure_count),
    INDEX idx_api_key_webhooks_last_success_at (last_success_at),
    INDEX idx_api_key_webhooks_last_failure_at (last_failure_at),
    INDEX idx_api_key_webhooks_created_at (created_at),
    INDEX idx_api_key_webhooks_updated_at (updated_at),
    
    -- Composite indexes for common queries
    INDEX idx_api_key_webhooks_key_active (api_key_id, is_active),
    INDEX idx_api_key_webhooks_tenant_active (tenant_id, is_active),
    INDEX idx_api_key_webhooks_website_active (website_id, is_active),
    
    -- Foreign key constraints
    CONSTRAINT fk_api_key_webhooks_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_webhooks_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_webhooks_api_key_id FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    
    -- Check constraints
    CHECK (LENGTH(webhook_url) > 0),
    CHECK (webhook_url LIKE 'http%'),
    CHECK (JSON_VALID(events)),
    CHECK (JSON_LENGTH(events) > 0),
    CHECK (success_count >= 0),
    CHECK (failure_count >= 0),
    CHECK (retry_count >= 0),
    CHECK (max_retries >= 0),
    CHECK (timeout_seconds > 0 AND timeout_seconds <= 300),
    CHECK (JSON_VALID(metadata))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;