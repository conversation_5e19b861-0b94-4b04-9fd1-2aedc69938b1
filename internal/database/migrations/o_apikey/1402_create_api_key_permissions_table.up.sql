-- Create API Key Permissions table
CREATE TABLE IF NOT EXISTS api_key_permissions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    api_key_id INT UNSIGNED NOT NULL,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    conditions JSON DEFAULT (JSON_OBJECT()),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_api_key_permissions_tenant_id (tenant_id),
    INDEX idx_api_key_permissions_api_key_id (api_key_id),
    INDEX idx_api_key_permissions_resource (resource),
    INDEX idx_api_key_permissions_action (action),
    INDEX idx_api_key_permissions_resource_action (resource, action),
    INDEX idx_api_key_permissions_created_at (created_at),
    
    -- Foreign key constraints
    CONSTRAINT fk_api_key_permissions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_permissions_api_key_id FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    
    -- Unique constraints
    UNIQUE KEY uk_api_key_permissions_key_resource_action (api_key_id, resource, action),
    
    -- Check constraints
    CHECK (LENGTH(resource) > 0),
    CHECK (LENGTH(action) > 0),
    CHECK (JSON_VALID(conditions))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;