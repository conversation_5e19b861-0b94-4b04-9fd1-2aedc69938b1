-- Create API Key Analytics table
CREATE TABLE IF NOT EXISTS api_key_analytics (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    api_key_id INT UNSIGNED NOT NULL,
    date DATE NOT NULL,
    hour TINYINT UNSIGNED NOT NULL,
    total_requests INT UNSIGNED NOT NULL DEFAULT 0,
    successful_requests INT UNSIGNED NOT NULL DEFAULT 0,
    failed_requests INT UNSIGNED NOT NULL DEFAULT 0,
    rate_limited_requests INT UNSIGNED NOT NULL DEFAULT 0,
    average_response_time_ms INT UNSIGNED NOT NULL DEFAULT 0,
    total_response_time_ms BIGINT UNSIGNED NOT NULL DEFAULT 0,
    min_response_time_ms INT UNSIGNED NOT NULL DEFAULT 0,
    max_response_time_ms INT UNSIGNED NOT NULL DEFAULT 0,
    total_request_size_bytes BIGINT UNSIGNED NOT NULL DEFAULT 0,
    total_response_size_bytes BIGINT UNSIGNED NOT NULL DEFAULT 0,
    unique_ip_count INT UNSIGNED NOT NULL DEFAULT 0,
    unique_endpoints_count INT UNSIGNED NOT NULL DEFAULT 0,
    get_requests INT UNSIGNED NOT NULL DEFAULT 0,
    post_requests INT UNSIGNED NOT NULL DEFAULT 0,
    put_requests INT UNSIGNED NOT NULL DEFAULT 0,
    delete_requests INT UNSIGNED NOT NULL DEFAULT 0,
    other_requests INT UNSIGNED NOT NULL DEFAULT 0,
    status_2xx INT UNSIGNED NOT NULL DEFAULT 0,
    status_3xx INT UNSIGNED NOT NULL DEFAULT 0,
    status_4xx INT UNSIGNED NOT NULL DEFAULT 0,
    status_5xx INT UNSIGNED NOT NULL DEFAULT 0,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_api_key_analytics_tenant_id (tenant_id),
    INDEX idx_api_key_analytics_website_id (website_id),
    INDEX idx_api_key_analytics_api_key_id (api_key_id),
    INDEX idx_api_key_analytics_date (date),
    INDEX idx_api_key_analytics_hour (hour),
    INDEX idx_api_key_analytics_created_at (created_at),
    INDEX idx_api_key_analytics_updated_at (updated_at),
    
    -- Composite indexes for common queries
    INDEX idx_api_key_analytics_key_date (api_key_id, date),
    INDEX idx_api_key_analytics_key_date_hour (api_key_id, date, hour),
    INDEX idx_api_key_analytics_tenant_date (tenant_id, date),
    INDEX idx_api_key_analytics_website_date (website_id, date),
    INDEX idx_api_key_analytics_date_hour (date, hour),
    
    -- Foreign key constraints (commented out due to partitioning incompatibility)
    -- CONSTRAINT fk_api_key_analytics_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_api_key_analytics_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_api_key_analytics_api_key_id FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    
    -- Primary key
    PRIMARY KEY (id),
    
    -- Unique constraints
    UNIQUE KEY uk_api_key_analytics_key_date_hour (api_key_id, date, hour),
    
    -- Check constraints
    CHECK (hour >= 0 AND hour <= 23),
    CHECK (total_requests >= 0),
    CHECK (successful_requests >= 0),
    CHECK (failed_requests >= 0),
    CHECK (rate_limited_requests >= 0),
    CHECK (successful_requests + failed_requests + rate_limited_requests <= total_requests),
    CHECK (average_response_time_ms >= 0),
    CHECK (total_response_time_ms >= 0),
    CHECK (min_response_time_ms >= 0),
    CHECK (max_response_time_ms >= 0),
    CHECK (total_request_size_bytes >= 0),
    CHECK (total_response_size_bytes >= 0),
    CHECK (unique_ip_count >= 0),
    CHECK (unique_endpoints_count >= 0),
    CHECK (get_requests >= 0),
    CHECK (post_requests >= 0),
    CHECK (put_requests >= 0),
    CHECK (delete_requests >= 0),
    CHECK (other_requests >= 0),
    CHECK (status_2xx >= 0),
    CHECK (status_3xx >= 0),
    CHECK (status_4xx >= 0),
    CHECK (status_5xx >= 0),
    CHECK (JSON_VALID(metadata))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;