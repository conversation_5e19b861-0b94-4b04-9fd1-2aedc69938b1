-- Create API Key Rate Limits table
CREATE TABLE IF NOT EXISTS api_key_rate_limits (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    api_key_id INT UNSIGNED NOT NULL,
    limit_type ENUM('requests_per_minute', 'requests_per_hour', 'requests_per_day', 'concurrent_requests', 'bandwidth_per_hour') NOT NULL,
    limit_value INT UNSIGNED NOT NULL,
    current_usage INT UNSIGNED NOT NULL DEFAULT 0,
    window_start DATETIME NOT NULL,
    window_end DATETIME NOT NULL,
    reset_at DATETIME NOT NULL,
    burst_allowance INT UNSIGNED DEFAULT 0,
    burst_used INT UNSIGNED DEFAULT 0,
    exceeded_count INT UNSIGNED DEFAULT 0,
    last_exceeded_at DATETIME NULL,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_api_key_rate_limits_tenant_id (tenant_id),
    INDEX idx_api_key_rate_limits_website_id (website_id),
    INDEX idx_api_key_rate_limits_api_key_id (api_key_id),
    INDEX idx_api_key_rate_limits_limit_type (limit_type),
    INDEX idx_api_key_rate_limits_window_start (window_start),
    INDEX idx_api_key_rate_limits_window_end (window_end),
    INDEX idx_api_key_rate_limits_reset_at (reset_at),
    INDEX idx_api_key_rate_limits_created_at (created_at),
    INDEX idx_api_key_rate_limits_updated_at (updated_at),
    
    -- Composite indexes for common queries
    INDEX idx_api_key_rate_limits_key_type (api_key_id, limit_type),
    INDEX idx_api_key_rate_limits_key_reset (api_key_id, reset_at),
    INDEX idx_api_key_rate_limits_active_window (window_start, window_end),
    
    -- Foreign key constraints
    CONSTRAINT fk_api_key_rate_limits_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_rate_limits_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_rate_limits_api_key_id FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    
    -- Unique constraints
    UNIQUE KEY uk_api_key_rate_limits_key_type_window (api_key_id, limit_type, window_start),
    
    -- Check constraints
    CHECK (limit_value > 0),
    CHECK (current_usage >= 0),
    CHECK (current_usage <= limit_value + burst_allowance),
    CHECK (burst_allowance >= 0),
    CHECK (burst_used >= 0),
    CHECK (burst_used <= burst_allowance),
    CHECK (exceeded_count >= 0),
    CHECK (window_start < window_end),
    CHECK (reset_at >= window_start),
    CHECK (JSON_VALID(metadata))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;