-- Create blog_post_schedules table
CREATE TABLE IF NOT EXISTS blog_post_schedules (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    schedule_type ENUM('one-time', 'daily', 'weekly', 'monthly', 'custom') NOT NULL DEFAULT 'one-time',
    scheduled_at TIMESTAMP NOT NULL,
    timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
    recurring_config JSON DEFAULT (JSON_OBJECT()),
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'deleted') NOT NULL DEFAULT 'pending',
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_blog_post_schedules_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_schedules_post FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    INDEX idx_blog_post_schedules_tenant_id (tenant_id),
    INDEX idx_blog_post_schedules_tenant_post (tenant_id, post_id),
    INDEX idx_blog_post_schedules_tenant_status (tenant_id, status),
    INDEX idx_blog_post_schedules_tenant_scheduled (tenant_id, scheduled_at),
    INDEX idx_blog_post_schedules_post (post_id),
    INDEX idx_blog_post_schedules_scheduled_at (scheduled_at),
    INDEX idx_blog_post_schedules_status (status),
    INDEX idx_blog_post_schedules_type (schedule_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;