CREATE TABLE IF NOT EXISTS trello_boards (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    workspace_id INT UNSIGNED NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    background_type ENUM('color', 'image', 'gradient') NOT NULL DEFAULT 'color',
    background_value VARCHAR(255) NOT NULL DEFAULT '#0079bf',
    visibility ENUM('private', 'workspace', 'public') NOT NULL DEFAULT 'private',
    is_closed BOOLEAN NOT NULL DEFAULT FALSE,
    is_starred BOOLEAN NOT NULL DEFAULT FALSE,
    preferences JSON DEFAULT (JSON_OBJECT()),
    labels_normalized JSON DEFAULT (JSON_ARRAY()),
    created_by INT UNSIGNED NOT NULL,
    status ENUM('active', 'inactive', 'archived', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_boards_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_boards_workspace_id FOREIGN KEY (workspace_id) REFERENCES trello_workspaces(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_boards_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_boards_workspace_name (workspace_id, name),
    
    -- Indexes
    INDEX idx_trello_boards_tenant_id (tenant_id),
    INDEX idx_trello_boards_workspace_id (workspace_id),
    INDEX idx_trello_boards_tenant_workspace (tenant_id, workspace_id),
    INDEX idx_trello_boards_tenant_status (tenant_id, status),
    INDEX idx_trello_boards_created_by (created_by),
    INDEX idx_trello_boards_visibility (visibility),
    INDEX idx_trello_boards_is_starred (is_starred),
    INDEX idx_trello_boards_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;