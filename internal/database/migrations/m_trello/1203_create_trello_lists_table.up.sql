CREATE TABLE IF NOT EXISTS trello_lists (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    board_id INT UNSIGNED NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    position DECIMAL(10,5) NOT NULL,
    is_closed BOOLEAN NOT NULL DEFAULT FALSE,
    is_subscribed BOOLEAN NOT NULL DEFAULT FALSE,
    settings JSON DEFAULT (JSON_OBJECT()),
    status ENUM('active', 'inactive', 'archived', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_lists_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_lists_board_id FOREIGN KEY (board_id) REFERENCES trello_boards(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_lists_board_position (board_id, position),
    
    -- Indexes
    INDEX idx_trello_lists_tenant_id (tenant_id),
    INDEX idx_trello_lists_board_id (board_id),
    INDEX idx_trello_lists_tenant_board (tenant_id, board_id),
    INDEX idx_trello_lists_tenant_status (tenant_id, status),
    INDEX idx_trello_lists_position (position),
    INDEX idx_trello_lists_is_closed (is_closed),
    INDEX idx_trello_lists_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;