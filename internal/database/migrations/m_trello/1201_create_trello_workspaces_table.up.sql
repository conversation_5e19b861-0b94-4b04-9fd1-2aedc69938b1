CREATE TABLE IF NOT EXISTS trello_workspaces (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    visibility ENUM('private', 'workspace', 'public') NOT NULL DEFAULT 'private',
    logo_url VARCHAR(512),
    website_url VARCHAR(512),
    settings JSON DEFAULT (JSON_OBJECT()),
    created_by INT UNSIGNED NOT NULL,
    status ENUM('active', 'inactive', 'archived', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_workspaces_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_workspaces_created_by <PERSON>OREIG<PERSON> KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_workspaces_tenant_name (tenant_id, name),
    
    -- Indexes
    INDEX idx_trello_workspaces_tenant_id (tenant_id),
    INDEX idx_trello_workspaces_tenant_status (tenant_id, status),
    INDEX idx_trello_workspaces_created_by (created_by),
    INDEX idx_trello_workspaces_visibility (visibility)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;