CREATE TABLE IF NOT EXISTS trello_cards (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    board_id INT UNSIGNED NOT NULL,
    list_id INT UNSIGNED NOT NULL,
    title VARCHAR(512) NOT NULL,
    description TEXT,
    position DECIMAL(10,5) NOT NULL,
    due_date DATETIME NULL,
    start_date DATETIME NULL,
    due_complete BOOLEAN NOT NULL DEFAULT FALSE,
    is_closed BOOLEAN NOT NULL DEFAULT FALSE,
    is_subscribed BOOLEAN NOT NULL DEFAULT FALSE,
    cover_type ENUM('none', 'color', 'image') NOT NULL DEFAULT 'none',
    cover_value VARCHAR(255) NULL,
    cover_color VARCHAR(7) NULL,
    cover_size ENUM('normal', 'full') NOT NULL DEFAULT 'normal',
    badges JSON DEFAULT (JSON_OBJECT()),
    custom_fields JSON DEFAULT (JSON_OBJECT()),
    created_by INT UNSIGNED NOT NULL,
    status ENUM('active', 'inactive', 'archived', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_cards_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_cards_board_id FOREIGN KEY (board_id) REFERENCES trello_boards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_cards_list_id FOREIGN KEY (list_id) REFERENCES trello_lists(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_cards_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_cards_list_position (list_id, position),
    
    -- Indexes
    INDEX idx_trello_cards_tenant_id (tenant_id),
    INDEX idx_trello_cards_board_id (board_id),
    INDEX idx_trello_cards_list_id (list_id),
    INDEX idx_trello_cards_tenant_board (tenant_id, board_id),
    INDEX idx_trello_cards_tenant_list (tenant_id, list_id),
    INDEX idx_trello_cards_tenant_status (tenant_id, status),
    INDEX idx_trello_cards_created_by (created_by),
    INDEX idx_trello_cards_position (position),
    INDEX idx_trello_cards_due_date (due_date),
    INDEX idx_trello_cards_due_complete (due_complete),
    INDEX idx_trello_cards_is_closed (is_closed),
    INDEX idx_trello_cards_updated_at (updated_at),
    
    -- Full-text search index
    FULLTEXT INDEX idx_trello_cards_search (title, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;