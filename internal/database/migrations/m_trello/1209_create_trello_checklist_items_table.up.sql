CREATE TABLE IF NOT EXISTS trello_checklist_items (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    checklist_id INT UNSIGNED NOT NULL,
    card_id INT UNSIGNED NOT NULL,
    name VA<PERSON>HA<PERSON>(512) NOT NULL,
    position DECIMAL(10,5) NOT NULL,
    is_checked BOOLEAN NOT NULL DEFAULT FALSE,
    due_date DATETIME NULL,
    assigned_user_id INT UNSIGNED NULL,
    created_by INT UNSIGNED NOT NULL,
    checked_by INT UNSIGNED NULL,
    checked_at TIMESTAMP NULL,
    status ENUM('active', 'inactive', 'archived', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_checklist_items_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_checklist_items_checklist_id FOREIGN KEY (checklist_id) REFERENCES trello_checklists(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_checklist_items_card_id FOREIGN KEY (card_id) REFERENCES trello_cards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_checklist_items_assigned_user_id FOREIGN KEY (assigned_user_id) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_trello_checklist_items_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_checklist_items_checked_by FOREIGN KEY (checked_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_checklist_items_checklist_position (checklist_id, position),
    
    -- Indexes
    INDEX idx_trello_checklist_items_tenant_id (tenant_id),
    INDEX idx_trello_checklist_items_checklist_id (checklist_id),
    INDEX idx_trello_checklist_items_card_id (card_id),
    INDEX idx_trello_checklist_items_tenant_checklist (tenant_id, checklist_id),
    INDEX idx_trello_checklist_items_tenant_card (tenant_id, card_id),
    INDEX idx_trello_checklist_items_tenant_status (tenant_id, status),
    INDEX idx_trello_checklist_items_assigned_user_id (assigned_user_id),
    INDEX idx_trello_checklist_items_created_by (created_by),
    INDEX idx_trello_checklist_items_checked_by (checked_by),
    INDEX idx_trello_checklist_items_position (position),
    INDEX idx_trello_checklist_items_is_checked (is_checked),
    INDEX idx_trello_checklist_items_due_date (due_date),
    INDEX idx_trello_checklist_items_checked_at (checked_at),
    INDEX idx_trello_checklist_items_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;