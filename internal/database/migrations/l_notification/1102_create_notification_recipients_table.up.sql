CREATE TABLE IF NOT EXISTS notification_recipients (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    notification_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NULL,
    recipient_type ENUM('user', 'email', 'phone', 'device') NOT NULL DEFAULT 'user',
    recipient_address VARCHAR(255) NOT NULL,
    device_token VARCHAR(512) NULL,
    status ENUM('pending', 'sent', 'delivered', 'read', 'failed', 'bounced', 'blocked') NOT NULL DEFAULT 'pending',
    delivered_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    failed_at TIMESTAMP NULL,
    bounced_at TIMESTAMP NULL,
    error_message TEXT NULL,
    delivery_info JSON DEFAULT (JSON_OBJECT()),
    engagement_data JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_notification_recipients_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_notification_recipients_notification_id FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    CONSTRAINT fk_notification_recipients_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_notification_recipients_tenant_notification_address (tenant_id, notification_id, recipient_address),
    
    -- Indexes
    INDEX idx_notification_recipients_tenant_id (tenant_id),
    INDEX idx_notification_recipients_tenant_notification (tenant_id, notification_id),
    INDEX idx_notification_recipients_tenant_user (tenant_id, user_id),
    INDEX idx_notification_recipients_tenant_status (tenant_id, status),
    INDEX idx_notification_recipients_notification_id (notification_id),
    INDEX idx_notification_recipients_user_id (user_id),
    INDEX idx_notification_recipients_status (status),
    INDEX idx_notification_recipients_recipient_address (recipient_address),
    INDEX idx_notification_recipients_delivered_at (delivered_at),
    INDEX idx_notification_recipients_read_at (read_at),
    
    -- JSON indexes for engagement tracking
    INDEX idx_notification_recipients_engagement_opens ((CAST(JSON_EXTRACT(engagement_data, '$.opens') AS UNSIGNED))),
    INDEX idx_notification_recipients_engagement_clicks ((CAST(JSON_EXTRACT(engagement_data, '$.clicks') AS UNSIGNED)))
);