CREATE TABLE IF NOT EXISTS notification_logs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    notification_id INT UNSIGNED NOT NULL,
    recipient_id INT UNSIGNED NULL,
    event_type ENUM('created', 'queued', 'sent', 'delivered', 'opened', 'clicked', 'failed', 'bounced', 'complaint', 'unsubscribed') NOT NULL,
    event_data JSON DEFAULT (JSON_OBJECT()),
    user_agent TEXT NULL,
    ip_address VARCHAR(45) NULL,
    tracking_id VARCHAR(255) NULL,
    error_code VARCHAR(50) NULL,
    error_message TEXT NULL,
    external_id VARCHAR(255) NULL,
    provider_response JSON DEFAULT (JSON_OBJECT()),
    occurred_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_notification_logs_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_notification_logs_notification_id FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    CONSTRAINT fk_notification_logs_recipient_id FOREIGN KEY (recipient_id) REFERENCES notification_recipients(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_notification_logs_tenant_id (tenant_id),
    INDEX idx_notification_logs_tenant_notification (tenant_id, notification_id),
    INDEX idx_notification_logs_tenant_recipient (tenant_id, recipient_id),
    INDEX idx_notification_logs_tenant_event (tenant_id, event_type),
    INDEX idx_notification_logs_notification_id (notification_id),
    INDEX idx_notification_logs_recipient_id (recipient_id),
    INDEX idx_notification_logs_event_type (event_type),
    INDEX idx_notification_logs_tracking_id (tracking_id),
    INDEX idx_notification_logs_external_id (external_id),
    INDEX idx_notification_logs_occurred_at (occurred_at),
    INDEX idx_notification_logs_created_at (created_at),
    INDEX idx_notification_logs_ip_address (ip_address),
    
    -- JSON indexes for analytics
    INDEX idx_notification_logs_event_data_source ((CAST(JSON_EXTRACT(event_data, '$.source') AS CHAR(50)))),
    INDEX idx_notification_logs_event_data_campaign ((CAST(JSON_EXTRACT(event_data, '$.campaign_id') AS UNSIGNED)))
);