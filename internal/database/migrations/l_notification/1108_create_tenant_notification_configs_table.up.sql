CREATE TABLE IF NOT EXISTS tenant_notification_configs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    channel ENUM('email', 'socket', 'push', 'sms') NOT NULL,
    provider VARCHAR(100) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT false,
    is_primary BOOLEAN NOT NULL DEFAULT false,
    configuration JSON NOT NULL DEFAULT (JSON_OBJECT()),
    credentials JSON NOT NULL DEFAULT (JSON_OBJECT()),
    settings JSON DEFAULT (JSON_OBJECT()),
    rate_limits JSON DEFAULT (JSON_OBJECT()),
    last_tested_at TIMESTAMP NULL,
    test_status ENUM('pending', 'passed', 'failed') NULL,
    test_error TEXT NULL,
    created_by INT UNSIGNED NULL,
    updated_by INT UNSIGNED NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_tenant_notification_configs_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_tenant_notification_configs_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_tenant_notification_configs_updated_by FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Unique Constraints
    UNIQUE KEY uk_tenant_notification_configs_tenant_channel_provider (tenant_id, channel, provider),
    
    
    -- Indexes
    INDEX idx_tenant_notification_configs_tenant_id (tenant_id),
    INDEX idx_tenant_notification_configs_tenant_channel (tenant_id, channel),
    INDEX idx_tenant_notification_configs_tenant_active (tenant_id, is_active),
    INDEX idx_tenant_notification_configs_tenant_primary (tenant_id, is_primary),
    INDEX idx_tenant_notification_configs_channel (channel),
    INDEX idx_tenant_notification_configs_provider (provider),
    INDEX idx_tenant_notification_configs_is_active (is_active),
    INDEX idx_tenant_notification_configs_is_primary (is_primary),
    INDEX idx_tenant_notification_configs_test_status (test_status),
    INDEX idx_tenant_notification_configs_created_by (created_by),
    INDEX idx_tenant_notification_configs_last_tested_at (last_tested_at),
    
    -- JSON indexes for configuration queries
    INDEX idx_tenant_notification_configs_config_smtp_host ((CAST(JSON_EXTRACT(configuration, '$.smtp_host') AS CHAR(255)))),
    INDEX idx_tenant_notification_configs_settings_priority ((CAST(JSON_EXTRACT(settings, '$.priority') AS CHAR(20))))
);