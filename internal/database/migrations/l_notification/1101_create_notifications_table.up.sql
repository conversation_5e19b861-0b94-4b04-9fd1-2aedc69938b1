CREATE TABLE IF NOT EXISTS notifications (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    type VARCHAR(50) NOT NULL,
    channel ENUM('email', 'socket', 'push', 'sms') NOT NULL,
    priority ENUM('low', 'normal', 'high', 'urgent') NOT NULL DEFAULT 'normal',
    subject VARCHAR(255) NOT NULL,
    template_id INT UNSIGNED NULL,
    template_data JSON DEFAULT (JSON_OBJECT()),
    status ENUM('pending', 'queued', 'sent', 'delivered', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    failed_at TIMESTAMP NULL,
    error_message TEXT NULL,
    retry_count INT UNSIGNED NOT NULL DEFAULT 0,
    max_retries INT UNSIGNED NOT NULL DEFAULT 3,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_notifications_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_notifications_tenant_id (tenant_id),
    INDEX idx_notifications_tenant_status (tenant_id, status),
    INDEX idx_notifications_tenant_type (tenant_id, type),
    INDEX idx_notifications_tenant_channel (tenant_id, channel),
    INDEX idx_notifications_tenant_priority (tenant_id, priority),
    INDEX idx_notifications_status (status),
    INDEX idx_notifications_scheduled_at (scheduled_at),
    INDEX idx_notifications_sent_at (sent_at),
    INDEX idx_notifications_created_at (created_at),
    
    -- JSON indexes for metadata queries
    INDEX idx_notifications_metadata_type ((CAST(JSON_EXTRACT(metadata, '$.notification_type') AS CHAR(50)))),
    INDEX idx_notifications_template_data_user ((CAST(JSON_EXTRACT(template_data, '$.user_id') AS UNSIGNED)))
);