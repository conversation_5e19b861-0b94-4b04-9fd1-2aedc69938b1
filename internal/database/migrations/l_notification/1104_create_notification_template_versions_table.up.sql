CREATE TABLE IF NOT EXISTS notification_template_versions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    template_id INT UNSIGNED NOT NULL,
    version_number INT UNSIGNED NOT NULL DEFAULT 1,
    language VARCHAR(10) NOT NULL DEFAULT 'en',
    subject VARCHAR(255) NOT NULL,
    body_html LONGTEXT NOT NULL,
    body_text LONGTEXT NULL,
    variables JSON DEFAULT (JSON_ARRAY()),
    is_active BOOLEAN NOT NULL DEFAULT false,
    is_approved BOOLEAN NOT NULL DEFAULT false,
    approved_by INT UNSIGNED NULL,
    approved_at TIMESTAMP NULL,
    created_by INT UNSIGNED NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_notification_template_versions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_notification_template_versions_template_id FOREIGN KEY (template_id) REFERENCES notification_templates(id) ON DELETE CASCADE,
    CONSTRAINT fk_notification_template_versions_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_notification_template_versions_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Unique Constraints
    UNIQUE KEY uk_notification_template_versions_tenant_template_version_lang (tenant_id, template_id, version_number, language),
    
    -- Indexes
    INDEX idx_notification_template_versions_tenant_id (tenant_id),
    INDEX idx_notification_template_versions_tenant_template (tenant_id, template_id),
    INDEX idx_notification_template_versions_tenant_active (tenant_id, is_active),
    INDEX idx_notification_template_versions_template_id (template_id),
    INDEX idx_notification_template_versions_version_number (version_number),
    INDEX idx_notification_template_versions_language (language),
    INDEX idx_notification_template_versions_is_active (is_active),
    INDEX idx_notification_template_versions_is_approved (is_approved),
    INDEX idx_notification_template_versions_approved_by (approved_by),
    INDEX idx_notification_template_versions_created_by (created_by),
    INDEX idx_notification_template_versions_created_at (created_at)
);