# Notification Module Migrations

This directory contains database migrations for the notification module, providing comprehensive multi-channel notification infrastructure with tenant isolation.

## Migration Overview

The notification module uses migrations numbered **1101-1109** in the range assigned to the `l_notification` module.

### Migration Files

| Migration | Description | Tables Created |
|-----------|-------------|----------------|
| **1101** | Core notifications table | `notifications` |
| **1102** | Notification recipients | `notification_recipients` |
| **1103** | Notification templates | `notification_templates` |
| **1104** | Template versions | `notification_template_versions` |
| **1105** | Template version FK | Foreign key constraint |
| **1106** | User preferences | `notification_preferences` |
| **1107** | Event logs | `notification_logs` |
| **1108** | Tenant configurations | `tenant_notification_configs` |
| **1109** | Template FK | Foreign key constraint |

## Database Schema Features

### Core Features
- **Multi-tenant Isolation**: All tables include `tenant_id` with proper foreign key constraints
- **Multi-channel Support**: Email, Socket.IO, Push notifications, SMS
- **Template System**: Versioned templates with multi-language support
- **Delivery Tracking**: Complete audit trail from creation to delivery
- **User Preferences**: Granular notification preferences per user/channel/type

### Performance Optimizations
- **Composite Indexes**: Tenant-scoped queries optimized with `(tenant_id, field)` indexes
- **JSON Indexes**: Metadata and configuration queries optimized
- **Status Tracking**: Efficient status-based queries for delivery monitoring

### Security & Compliance
- **Cascade Deletes**: Proper cleanup when tenants/users are removed
- **Credential Protection**: Encrypted storage for channel credentials
- **Audit Logging**: Complete event tracking for compliance

## Table Relationships

```mermaid
erDiagram
    notifications {
        int id PK
        int tenant_id FK
        int template_id FK
        enum status
        enum channel
        enum priority
    }
    
    notification_recipients {
        int id PK
        int tenant_id FK
        int notification_id FK
        int user_id FK
        enum status
    }
    
    notification_templates {
        int id PK
        int tenant_id FK
        int active_version_id FK
        enum type
        enum channel
    }
    
    notification_template_versions {
        int id PK
        int tenant_id FK
        int template_id FK
        string language
        text body_html
    }
    
    notification_preferences {
        int id PK
        int tenant_id FK
        int user_id FK
        enum channel
        enum frequency
    }
    
    notification_logs {
        int id PK
        int tenant_id FK
        int notification_id FK
        int recipient_id FK
        enum event_type
    }
    
    tenant_notification_configs {
        int id PK
        int tenant_id FK
        enum channel
        string provider
        json configuration
    }
    
    notifications ||--o{ notification_recipients : "has many"
    notifications }o--|| notification_templates : "uses"
    notification_templates ||--o{ notification_template_versions : "has versions"
    notification_templates }o--|| notification_template_versions : "active version"
    notification_recipients ||--o{ notification_logs : "generates logs"
    notifications ||--o{ notification_logs : "tracks events"
```

## Usage Examples

### Running Migrations

```bash
# Apply all notification migrations
make migrate-up MODULE=l_notification

# Check migration status
make migrate-status MODULE=l_notification

# Rollback last migration
make migrate-down MODULE=l_notification
```

### Channel Types Supported
- **Email**: SMTP, SendGrid, Amazon SES
- **Socket**: Real-time WebSocket notifications
- **Push**: FCM (Android), APNS (iOS)
- **SMS**: Twilio, AWS SNS

### Notification Types
- **Transactional**: User actions, system events
- **Marketing**: Newsletters, promotions
- **System**: Maintenance, alerts
- **Custom**: Tenant-specific notifications

## Multi-Tenant Considerations

### Data Isolation
- All queries automatically scoped by `tenant_id`
- Complete data separation between tenants
- Tenant deletion cascades to remove all notification data

### Performance
- Composite indexes ensure efficient tenant-scoped queries
- JSON indexes for metadata and configuration queries
- Status-based queries optimized for delivery monitoring

### Security
- Tenant credentials isolated and encrypted
- User preferences respect tenant boundaries
- Audit logs maintain tenant context

## Testing

The migrations have been tested with:
- ✅ Fresh database creation
- ✅ Up migrations (1101-1109)
- ✅ Down migrations (rollback functionality)
- ✅ Foreign key constraints
- ✅ Multi-tenant data isolation
- ✅ Index performance optimization

## Notes

- **Migration 1105** adds circular foreign key between templates and template_versions
- **Migration 1109** establishes notification-template relationship
- **JSON fields** use proper MySQL 8 defaults: `JSON DEFAULT (JSON_OBJECT())`
- **Status-based soft deletes** used instead of `deleted_at` timestamps
- **Tenant isolation** enforced at database level with foreign key constraints