-- Migration: 1301_create_settings_table
-- Description: Create settings table for multi-level configuration management (MySQL 8)
-- Author: System
-- Date: 2025-01-18

CREATE TABLE IF NOT EXISTS settings (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Scoping fields (hierarchical resolution)
    scope_type ENUM('system', 'tenant', 'module', 'user', 'feature') NOT NULL COMMENT 'Setting scope level',
    scope_id INT UNSIGNED NULL COMMENT 'ID of the scoped entity (null for system level)',
    
    -- Setting identification
    module_name VARCHAR(100) NULL COMMENT 'Module name (e.g., auth, blog, rbac)',
    group_name VARCHAR(100) NOT NULL COMMENT 'Setting group for organization (e.g., general, email, security)',
    setting_key VARCHAR(255) NOT NULL COMMENT 'Unique setting key within the group',
    setting_value JSON NOT NULL COMMENT 'Setting value stored as JSON for flexibility',
    
    -- Metadata
    display_name VARCHAR(255) NOT NULL COMMENT 'Human-readable setting name',
    description TEXT NULL COMMENT 'Setting description for UI and documentation',
    data_type VARCHAR(50) NOT NULL DEFAULT 'string' COMMENT 'Expected data type',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT 'Whether the value should be encrypted at rest',
    is_public BOOLEAN DEFAULT FALSE COMMENT 'Whether the setting can be exposed to public APIs',
    is_required BOOLEAN DEFAULT FALSE COMMENT 'Whether the setting is required',
    
    -- Validation and defaults
    validation_rules JSON NULL COMMENT 'JSON schema or validation rules',
    default_value JSON NULL COMMENT 'Default value for the setting',
    allowed_values JSON NULL COMMENT 'Array of allowed values for enum-type settings',
    
    -- UI metadata
    ui_component VARCHAR(50) DEFAULT 'input' COMMENT 'UI component type (input, select, textarea, etc.)',
    ui_order INT UNSIGNED DEFAULT 0 COMMENT 'Display order in UI',
    ui_group VARCHAR(100) NULL COMMENT 'UI grouping for better organization',
    ui_attributes JSON NULL COMMENT 'Additional UI attributes (placeholder, help text, etc.)',
    
    -- Inheritance and overrides
    parent_setting_id INT UNSIGNED NULL COMMENT 'Parent setting for inheritance',
    is_override BOOLEAN DEFAULT FALSE COMMENT 'Whether this setting overrides a parent setting',
    
    -- Timestamps and audit
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED NULL COMMENT 'User ID who created the setting',
    updated_by INT UNSIGNED NULL COMMENT 'User ID who last updated the setting',
    
    -- Constraints
    CONSTRAINT fk_settings_parent FOREIGN KEY (parent_setting_id) REFERENCES settings(id) ON DELETE SET NULL,
    CONSTRAINT uk_settings_scope_key UNIQUE KEY (scope_type, scope_id, module_name, group_name, setting_key),
    CONSTRAINT chk_settings_data_type CHECK (data_type IN ('string', 'number', 'boolean', 'json', 'array', 'enum')),
    CONSTRAINT chk_settings_ui_component CHECK (ui_component IN ('input', 'textarea', 'select', 'checkbox', 'radio', 'toggle', 'color', 'date', 'time', 'datetime', 'file', 'image', 'editor')),
    CONSTRAINT chk_settings_scope_id CHECK (
        (scope_type = 'system' AND scope_id IS NULL) OR
        (scope_type != 'system' AND scope_id IS NOT NULL)
    ),
    
    -- Indexes
    INDEX idx_settings_scope (scope_type, scope_id),
    INDEX idx_settings_module_group (module_name, group_name),
    INDEX idx_settings_key (setting_key),
    INDEX idx_settings_public (is_public),
    INDEX idx_settings_ui_order (ui_order),
    INDEX idx_settings_updated_at (updated_at),
    INDEX idx_settings_parent_id (parent_setting_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Multi-level settings management with hierarchical resolution';