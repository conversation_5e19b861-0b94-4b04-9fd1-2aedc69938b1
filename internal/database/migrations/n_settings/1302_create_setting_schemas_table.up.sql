-- Migration: 1302_create_setting_schemas_table
-- Description: Create setting_schemas table for validation rules and UI metadata (MySQL 8)
-- Author: System
-- Date: 2025-01-18

CREATE TABLE IF NOT EXISTS setting_schemas (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Schema identification
    schema_name VARCHAR(100) NOT NULL COMMENT 'Unique schema name',
    version VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT 'Schema version',
    
    -- Scope and context
    module_name VARCHAR(100) NULL COMMENT 'Module this schema belongs to',
    group_name VARCHAR(100) NOT NULL COMMENT 'Setting group this schema applies to',
    setting_key VARCHAR(255) NOT NULL COMMENT 'Setting key this schema validates',
    
    -- Schema definition
    json_schema JSON NOT NULL COMMENT 'JSON Schema for validation',
    default_value JSON NULL COMMENT 'Default value for this setting',
    
    -- Validation rules
    validation_rules JSON NULL COMMENT 'Custom validation rules beyond JSON schema',
    allowed_values JSON NULL COMMENT 'Array of allowed values for enum-type settings',
    min_value JSON NULL COMMENT 'Minimum value for numeric settings',
    max_value JSON NULL COMMENT 'Maximum value for numeric settings',
    pattern VARCHAR(500) NULL COMMENT 'Regex pattern for string validation',
    
    -- UI metadata
    ui_config JSON NOT NULL COMMENT 'UI configuration for this setting',
    ui_component VARCHAR(50) DEFAULT 'input' COMMENT 'UI component type',
    ui_label VARCHAR(255) NOT NULL COMMENT 'Display label for UI',
    ui_description TEXT NULL COMMENT 'Help text for UI',
    ui_placeholder VARCHAR(255) NULL COMMENT 'Placeholder text for input fields',
    ui_order INT UNSIGNED DEFAULT 0 COMMENT 'Display order in UI',
    ui_group VARCHAR(100) NULL COMMENT 'UI grouping for better organization',
    ui_attributes JSON NULL COMMENT 'Additional UI attributes',
    
    -- Permissions and visibility
    is_public BOOLEAN DEFAULT FALSE COMMENT 'Whether this setting can be exposed publicly',
    is_required BOOLEAN DEFAULT FALSE COMMENT 'Whether this setting is required',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT 'Whether values should be encrypted',
    is_system BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a system-level schema',
    
    -- Conditional logic
    depends_on JSON NULL COMMENT 'Dependencies on other settings',
    conditional_rules JSON NULL COMMENT 'Conditional visibility/validation rules',
    
    -- Documentation
    documentation TEXT NULL COMMENT 'Detailed documentation for this setting',
    examples JSON NULL COMMENT 'Example values for this setting',
    tags JSON NULL COMMENT 'Tags for categorization and search',
    
    -- Status and lifecycle
    status ENUM('active', 'deprecated', 'draft', 'deleted') DEFAULT 'active' COMMENT 'Schema status',
    deprecation_message TEXT NULL COMMENT 'Message for deprecated schemas',
    replacement_schema_id INT UNSIGNED NULL COMMENT 'ID of replacement schema',
    
    -- Timestamps and audit
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED NULL COMMENT 'User ID who created the schema',
    updated_by INT UNSIGNED NULL COMMENT 'User ID who last updated the schema',
    
    -- Constraints
    CONSTRAINT fk_setting_schemas_replacement FOREIGN KEY (replacement_schema_id) REFERENCES setting_schemas(id) ON DELETE SET NULL,
    CONSTRAINT uk_setting_schemas_name_version UNIQUE KEY (schema_name, version),
    CONSTRAINT uk_setting_schemas_module_group_key UNIQUE KEY (module_name, group_name, setting_key),
    CONSTRAINT chk_setting_schemas_ui_component CHECK (ui_component IN ('input', 'textarea', 'select', 'checkbox', 'radio', 'toggle', 'color', 'date', 'time', 'datetime', 'file', 'image', 'editor', 'json', 'code')),
    CONSTRAINT chk_setting_schemas_status CHECK (status IN ('active', 'deprecated', 'draft', 'deleted')),
    
    -- Indexes
    INDEX idx_setting_schemas_name (schema_name),
    INDEX idx_setting_schemas_module_group (module_name, group_name),
    INDEX idx_setting_schemas_key (setting_key),
    INDEX idx_setting_schemas_status (status),
    INDEX idx_setting_schemas_system (is_system),
    INDEX idx_setting_schemas_public (is_public),
    INDEX idx_setting_schemas_ui_order (ui_order),
    INDEX idx_setting_schemas_updated_at (updated_at),
    -- INDEX idx_setting_schemas_tags (tags), -- JSON columns need generated columns for indexing
    INDEX idx_setting_schemas_replacement_id (replacement_schema_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Schema definitions for settings validation and UI metadata';