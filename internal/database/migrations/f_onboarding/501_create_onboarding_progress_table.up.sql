-- Migration: 501_create_onboarding_progress_table
-- Description: Create the onboarding_progress table for tracking user onboarding progress (MySQL 8)
-- Author: System
-- Date: 2025-07-22

CREATE TABLE IF NOT EXISTS onboarding_progress (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- User Reference
    user_id INT UNSIGNED NOT NULL,
    
    -- Progress Information
    status VARCHAR(50) NOT NULL DEFAULT 'pending' COMMENT 'Current onboarding status: pending, processing, completed',
    step VARCHAR(50) NOT NULL DEFAULT 'create_tenant' COMMENT 'Current onboarding step: create_tenant, create_website, completed',
    
    -- Progress Metadata
    started_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When the user started the onboarding process',
    completed_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When the user completed the onboarding process',
    
    -- Additional Context
    metadata JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON storage for additional onboarding metadata and context',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_onboarding_progress_status CHECK (status IN ('pending', 'processing', 'completed')),
    CONSTRAINT chk_onboarding_progress_step CHECK (step IN ('create_tenant', 'create_website', 'completed')),
    
    -- Foreign Keys
    CONSTRAINT fk_onboarding_progress_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints (one onboarding progress per user)
    UNIQUE KEY uk_onboarding_progress_user_id (user_id),
    
    -- Indexes
    INDEX idx_onboarding_progress_user_id (user_id),
    INDEX idx_onboarding_progress_status (status),
    INDEX idx_onboarding_progress_step (step),
    INDEX idx_onboarding_progress_created_at (created_at),
    INDEX idx_onboarding_progress_completed_at (completed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Onboarding progress tracking table - tracks user progress through the onboarding flow.';
