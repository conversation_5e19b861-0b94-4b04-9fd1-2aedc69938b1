-- Migration: 021_create_rbac_role_permissions_table
-- Description: Create the RBAC role permissions mapping table (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS rbac_role_permissions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    role_id INT UNSIGNED NOT NULL,
    permission_id INT UNSIGNED NOT NULL,
    
    -- Permission Grant Information
    granted_by INT UNSIGNED COMMENT 'User ID who granted this permission',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Permission Context and Scope
    context_type VARCHAR(50) COMMENT 'Context type for scoped permissions (website, tenant, global)',
    context_id INT UNSIGNED COMMENT 'Context ID for scoped permissions',
    
    -- Permission Constraints
    conditions JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object defining specific conditions for this permission grant',
    limitations JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object defining limitations for this permission grant',
    
    -- Permission Metadata
    is_inherited BOOLEAN DEFAULT FALSE COMMENT 'Whether this permission is inherited from parent role',
    is_temporary BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a temporary permission grant',
    
    -- Temporal Constraints
    valid_from TIMESTAMP NULL COMMENT 'When this permission grant becomes valid',
    valid_until TIMESTAMP NULL COMMENT 'When this permission grant expires',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Permission grant status: active, inactive, revoked',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP NULL COMMENT 'When this permission was revoked',
    revoked_by INT UNSIGNED COMMENT 'User ID who revoked this permission',
    
    -- Foreign Keys
    CONSTRAINT fk_rbac_role_permissions_role_id FOREIGN KEY (role_id) REFERENCES rbac_roles(id) ON DELETE CASCADE,
    CONSTRAINT fk_rbac_role_permissions_permission_id FOREIGN KEY (permission_id) REFERENCES rbac_permissions(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_rbac_role_permissions_granted_by FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    -- CONSTRAINT fk_rbac_role_permissions_revoked_by FOREIGN KEY (revoked_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_rbac_role_permissions_status CHECK (status IN ('active', 'inactive', 'revoked')),
    CONSTRAINT chk_rbac_role_permissions_context_type CHECK (context_type IS NULL OR context_type IN ('website', 'tenant', 'global')),
    CONSTRAINT chk_rbac_role_permissions_valid_dates CHECK (valid_from IS NULL OR valid_until IS NULL OR valid_from <= valid_until),
    
    -- Unique Constraints (prevent duplicate role-permission mappings)
    UNIQUE KEY uk_rbac_role_permissions_role_permission_context (role_id, permission_id, context_type, context_id),
    
    -- Indexes
    INDEX idx_rbac_role_permissions_role_id (role_id, status),
    INDEX idx_rbac_role_permissions_permission_id (permission_id, status),
    INDEX idx_rbac_role_permissions_context (context_type, context_id, status),
    INDEX idx_rbac_role_permissions_granted_by (granted_by, granted_at),
    INDEX idx_rbac_role_permissions_inherited (is_inherited),
    INDEX idx_rbac_role_permissions_temporary (is_temporary, valid_until),
    INDEX idx_rbac_role_permissions_valid_from (valid_from),
    INDEX idx_rbac_role_permissions_valid_until (valid_until),
    INDEX idx_rbac_role_permissions_revoked (revoked_at, revoked_by),
    INDEX idx_rbac_role_permissions_created_at (created_at),
    
    -- Composite indexes for permission checking
    INDEX idx_rbac_role_permissions_active_grants (role_id, permission_id, status, valid_from, valid_until),
    INDEX idx_rbac_role_permissions_context_check (context_type, context_id, role_id, permission_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RBAC role-permission mapping table for associating roles with permissions.';