-- Migration: 022_create_rbac_user_roles_table
-- Description: Create the RBAC user roles table with website-level assignments (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS rbac_user_roles (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    role_id INT UNSIGNED NOT NULL,
    
    -- Assignment Information
    assigned_by INT UNSIGNED COMMENT 'User ID who assigned this role',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Role Context and Scope
    context_type VARCHAR(50) DEFAULT 'tenant' COMMENT 'Context type: tenant, website, global',
    context_id INT UNSIGNED COMMENT 'Context ID for scoped roles (website_id, tenant_id, etc.)',
    
    -- Role Assignment Constraints
    conditions JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object defining specific conditions for this role assignment',
    limitations JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object defining limitations for this role assignment',
    
    -- Assignment Metadata
    is_primary BOOLEAN DEFAULT FALSE COMMENT 'Whether this is the primary role for the user in this context',
    is_inherited BOOLEAN DEFAULT FALSE COMMENT 'Whether this role is inherited from parent context',
    is_temporary BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a temporary role assignment',
    
    -- Temporal Constraints
    valid_from TIMESTAMP NULL COMMENT 'When this role assignment becomes valid',
    valid_until TIMESTAMP NULL COMMENT 'When this role assignment expires',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Role assignment status: active, inactive, suspended, revoked',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP NULL COMMENT 'When this role was revoked',
    revoked_by INT UNSIGNED COMMENT 'User ID who revoked this role',
    
    -- Foreign Keys
    -- CONSTRAINT fk_rbac_user_roles_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_rbac_user_roles_role_id FOREIGN KEY (role_id) REFERENCES rbac_roles(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_rbac_user_roles_assigned_by FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    -- CONSTRAINT fk_rbac_user_roles_revoked_by FOREIGN KEY (revoked_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_rbac_user_roles_status CHECK (status IN ('active', 'inactive', 'suspended', 'revoked')),
    CONSTRAINT chk_rbac_user_roles_context_type CHECK (context_type IN ('tenant', 'website', 'global')),
    CONSTRAINT chk_rbac_user_roles_valid_dates CHECK (valid_from IS NULL OR valid_until IS NULL OR valid_from <= valid_until),
    
    -- Unique Constraints (prevent duplicate user-role assignments in same context)
    UNIQUE KEY uk_rbac_user_roles_user_role_context (user_id, role_id, context_type, context_id),
    
    -- Indexes
    INDEX idx_rbac_user_roles_user_id (user_id, status),
    INDEX idx_rbac_user_roles_role_id (role_id, status),
    INDEX idx_rbac_user_roles_context (context_type, context_id, status),
    INDEX idx_rbac_user_roles_assigned_by (assigned_by, assigned_at),
    INDEX idx_rbac_user_roles_primary (is_primary, context_type, context_id),
    INDEX idx_rbac_user_roles_inherited (is_inherited),
    INDEX idx_rbac_user_roles_temporary (is_temporary, valid_until),
    INDEX idx_rbac_user_roles_valid_from (valid_from),
    INDEX idx_rbac_user_roles_valid_until (valid_until),
    INDEX idx_rbac_user_roles_revoked (revoked_at, revoked_by),
    INDEX idx_rbac_user_roles_created_at (created_at),
    
    -- Composite indexes for role checking
    INDEX idx_rbac_user_roles_active_assignments (user_id, role_id, status, valid_from, valid_until),
    INDEX idx_rbac_user_roles_context_check (context_type, context_id, user_id, role_id, status),
    INDEX idx_rbac_user_roles_user_context (user_id, context_type, context_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RBAC user-role assignments table with website-level scoping and temporal constraints.';