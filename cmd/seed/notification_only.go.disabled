package main

import (
	"log"

	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/database"
	"github.com/tranthanhloi/wn-api-v3/internal/database/seeders/notification"
)

func main() {
	log.Println("🌱 Starting notification template seeding...")

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Initialize database
	db, err := database.NewDatabase(cfg.Database)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Create notification template seeder
	seeder := notification.NewNotificationTemplateSeeder(db)

	// Run seeder
	if err := seeder.SeedAll(); err != nil {
		log.Fatal("Failed to seed notification templates:", err)
	}

	log.Println("✅ Notification template seeding completed successfully!")
}