package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/database"
	authServices "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	authRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	userRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	notificationRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	websiteServices "github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	websiteRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

func main() {
	fmt.Println("🧪 Testing Email Verification Template Rendering...")

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Initialize database
	db, err := database.NewDatabase(cfg.Database)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Initialize logger
	logger := utils.NewLogger(cfg.Log.Level)

	// Initialize repositories
	userRepo := userRepositories.NewUserRepository(db, logger)
	tokenRepo := authRepositories.NewEmailVerificationTokenRepository(db, logger)
	notificationRepo := notificationRepositories.NewNotificationRepository(db, logger)
	templateRepo := notificationRepositories.NewTemplateRepository(db, logger)
	websiteRepo := websiteRepositories.NewWebsiteRepository(db, logger)

	// Initialize services
	notificationService := notificationServices.NewNotificationService(notificationRepo, templateRepo, logger)
	websiteService := websiteServices.NewWebsiteService(websiteRepo, logger)
	// Create placeholder services (not all are used in test)
	var tenantService interface{} = nil
	var membershipRepo interface{} = nil
	
	emailVerificationService := authServices.NewEmailVerificationService(
		tokenRepo,
		userRepo,
		notificationService,
		templateRepo,
		websiteService,
		tenantService,
		membershipRepo,
		&cfg.Auth,
		logger,
	)

	// Create test user
	testUser := &userModels.User{
		Email:     "<EMAIL>",
		FirstName: strPtr("John"),
		LastName:  strPtr("Doe"),
		Status:    userModels.UserStatusPendingVerification,
		Timezone:  "UTC",
		Language:  "en",
	}

	// Set password hash (dummy)
	testUser.PasswordHash = "dummy_hash"

	// Create user in database
	if err := userRepo.Create(context.Background(), testUser); err != nil {
		log.Fatal("Failed to create test user:", err)
	}

	fmt.Printf("✅ Created test user: %s (ID: %d)\n", testUser.Email, testUser.ID)
	fmt.Printf("   Display Name: %s\n", testUser.GetDisplayName())

	// Create verification token request
	req := &authModels.CreateEmailVerificationTokenRequest{
		UserID:    testUser.ID,
		Email:     testUser.Email,
		UserAgent: "Test Client",
		IPAddress: "127.0.0.1",
	}

	// Create verification token and send email
	token, err := emailVerificationService.CreateVerificationToken(context.Background(), 1, req)
	if err != nil {
		log.Fatal("Failed to create verification token:", err)
	}

	fmt.Printf("✅ Created verification token: %s\n", token.Token[:20]+"...")
	fmt.Printf("   Expires at: %s\n", token.ExpiresAt.Format(time.RFC3339))

	// Check if template data is correctly populated
	fmt.Printf("\n🔍 Template data should contain:\n")
	fmt.Printf("   - user.name: %s\n", testUser.GetDisplayName())
	fmt.Printf("   - user.email: %s\n", testUser.Email)
	fmt.Printf("   - verification_url: <generated_url>\n")
	fmt.Printf("   - expiry_time: <calculated_time>\n")
	fmt.Printf("   - brand.name: BlogAPI\n")
	fmt.Printf("   - current_year: %d\n", time.Now().Year())

	// Clean up - delete test user
	if err := userRepo.Delete(context.Background(), testUser.ID); err != nil {
		log.Printf("⚠️  Failed to delete test user: %v", err)
	} else {
		fmt.Printf("🧹 Cleaned up test user\n")
	}

	fmt.Println("\n🎉 Email verification template test completed!")
}

func strPtr(s string) *string {
	return &s
}