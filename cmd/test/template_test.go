package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/database"
	authServices "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	authRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	userRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	notificationRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	websiteServices "github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	websiteRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

func main() {
	fmt.Println("🧪 Testing Email Verification Template with {{user.name}} placeholder...")

	// Set log level to debug
	os.Setenv("LOG_LEVEL", "debug")

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Initialize database
	db, err := database.NewDatabase(cfg.Database)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Initialize logger
	logger := utils.NewLogger(cfg.Log.Level)

	// Initialize repositories
	userRepo := userRepositories.NewUserRepository(db, logger)
	tokenRepo := authRepositories.NewEmailVerificationTokenRepository(db, logger)
	notificationRepo := notificationRepositories.NewNotificationRepository(db, logger)
	templateRepo := notificationRepositories.NewTemplateRepository(db, logger)
	websiteRepo := websiteRepositories.NewWebsiteRepository(db, logger)

	// Initialize services
	notificationService := notificationServices.NewNotificationService(notificationRepo, templateRepo, logger)
	websiteService := websiteServices.NewWebsiteService(websiteRepo, logger)
	// Create placeholder services (not all are used in test)
	var tenantService interface{} = nil
	var membershipRepo interface{} = nil
	
	emailVerificationService := authServices.NewEmailVerificationService(
		tokenRepo,
		userRepo,
		notificationService,
		templateRepo,
		websiteService,
		tenantService,
		membershipRepo,
		&cfg.Auth,
		logger,
	)

	// Get test user
	user, err := userRepo.GetByEmail(context.Background(), "<EMAIL>")
	if err != nil {
		log.Fatal("Failed to get test user:", err)
	}

	fmt.Printf("✅ Found test user: %s (ID: %d)\n", user.Email, user.ID)
	fmt.Printf("   Display Name: %s\n", user.GetDisplayName())
	fmt.Printf("   First Name: %s\n", *user.FirstName)
	fmt.Printf("   Last Name: %s\n", *user.LastName)

	// Create verification token request
	req := &authModels.CreateEmailVerificationTokenRequest{
		UserID:    user.ID,
		Email:     user.Email,
		UserAgent: "Test Client",
		IPAddress: "127.0.0.1",
	}

	// Create verification token and send email
	token, err := emailVerificationService.CreateVerificationToken(context.Background(), 1, req)
	if err != nil {
		log.Fatal("Failed to create verification token:", err)
	}

	fmt.Printf("✅ Created verification token: %s\n", token.Token[:20]+"...")
	fmt.Printf("   Expires at: %s\n", token.ExpiresAt.Format(time.RFC3339))

	// Check template data in database
	fmt.Printf("\n🔍 Checking template data in database...\n")
	fmt.Printf("   - Template should use {{user.name}} placeholder\n")
	fmt.Printf("   - Service provides user.name: %s\n", user.GetDisplayName())
	fmt.Printf("   - Template ID: 3 (email_verification)\n")

	fmt.Println("\n🎉 Email verification template test completed!")
	fmt.Println("📧 Check your email notification logs to see if {{user.name}} is properly replaced!")
}