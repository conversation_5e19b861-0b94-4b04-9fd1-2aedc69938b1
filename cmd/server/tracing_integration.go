package main

import (
	"context"
	"log"
	"os"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/tracing"
)

// InitializeTracing initializes the tracing system for the main server
func InitializeTracing() func() {
	// Check if tracing is enabled
	if !getEnvAsBool("TRACING_ENABLED", false) {
		log.Println("Tracing is disabled")
		return func() {}
	}

	// Create tracing configuration
	cfg := createTracingConfig()
	
	// Initialize tracing system
	if err := tracing.Initialize(cfg); err != nil {
		log.Printf("Failed to initialize tracing: %v", err)
		return func() {}
	}

	log.Printf("Tracing initialized successfully - service: %s, environment: %s", 
		cfg.ServiceName, cfg.Environment)

	// Return cleanup function
	return func() {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		
		if err := tracing.Shutdown(ctx); err != nil {
			log.Printf("Failed to shutdown tracing: %v", err)
		} else {
			log.Println("Tracing shutdown completed")
		}
	}
}

// createTracingConfig creates a tracing configuration based on environment
func createTracingConfig() *tracing.Config {
	env := getEnv("ENVIRONMENT", "development")
	
	// Base configuration
	cfg := &tracing.Config{
		ServiceName:    "blog-api-v3",
		ServiceVersion: "1.0.0",
		Environment:    env,
		Enabled:        true,
		Exporter:       tracing.JaegerExporter,
		Propagators:    []string{"tracecontext", "baggage"},
	}

	// Environment-specific settings
	switch env {
	case "production":
		cfg.Debug = false
		cfg.Sampling = tracing.TraceIDRatio
		cfg.SampleRate = 0.1
		cfg.JaegerEndpoint = "http://jaeger:14268/api/traces"
		cfg.BatchTimeout = 500 * time.Millisecond
		cfg.BatchSize = 200
		cfg.MaxExportBatchSize = 1024
		cfg.MaxQueueSize = 4096
		
	case "testing":
		cfg.Debug = true
		cfg.Exporter = tracing.ConsoleExporter
		cfg.Sampling = tracing.AlwaysSample
		cfg.SampleRate = 1.0
		cfg.BatchTimeout = 100 * time.Millisecond
		cfg.BatchSize = 10
		
	default: // development
		cfg.Debug = true
		cfg.Sampling = tracing.AlwaysSample
		cfg.SampleRate = 1.0
		cfg.JaegerEndpoint = "http://localhost:14268/api/traces"
		cfg.BatchTimeout = 1000 * time.Millisecond
		cfg.BatchSize = 100
		cfg.MaxExportBatchSize = 512
		cfg.MaxQueueSize = 2048
	}

	// Override with environment variables
	if endpoint := getEnv("JAEGER_ENDPOINT", ""); endpoint != "" {
		cfg.JaegerEndpoint = endpoint
	}
	
	if debug := getEnvAsBool("TRACING_DEBUG", false); debug {
		cfg.Debug = true
	}
	
	if sampleRate := getEnvAsFloat64("TRACING_SAMPLE_RATE", 0.0); sampleRate > 0 {
		cfg.SampleRate = sampleRate
	}

	// Resource attributes
	cfg.ResourceAttributes = map[string]string{
		"service.name":      cfg.ServiceName,
		"service.version":   cfg.ServiceVersion,
		"service.environment": cfg.Environment,
		"deployment.environment": cfg.Environment,
	}

	return cfg
}

// Helper functions
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		return value == "true" || value == "1" || value == "yes"
	}
	return defaultValue
}

func getEnvAsFloat64(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		// Simple conversion - in production, use strconv.ParseFloat
		if value == "1.0" {
			return 1.0
		}
		if value == "0.1" {
			return 0.1
		}
		if value == "0.01" {
			return 0.01
		}
	}
	return defaultValue
}

// GetTracingHealthStatus returns the health status of tracing
func GetTracingHealthStatus() map[string]interface{} {
	enabled := getEnvAsBool("TRACING_ENABLED", false)
	
	return map[string]interface{}{
		"enabled":     enabled,
		"service":     "blog-api-v3",
		"version":     "1.0.0",
		"environment": getEnv("ENVIRONMENT", "development"),
		"endpoint":    getEnv("JAEGER_ENDPOINT", "http://localhost:14268/api/traces"),
		"status":      "healthy",
		"timestamp":   time.Now().Unix(),
	}
}

// CreateTracingMiddleware creates a simple tracing middleware
func CreateTracingMiddleware() func(next func()) func() {
	return func(next func()) func() {
		return func() {
			// In a real implementation, this would create spans
			// For now, just execute the next function
			next()
		}
	}
}