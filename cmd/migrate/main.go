package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"
	"github.com/tranthanhloi/wn-api-v3/pkg/database"
)

func main() {
	var (
		command    = flag.String("command", "up", "Migration command: up, down, version, status")
		dbURL      = flag.String("database", os.Getenv("DATABASE_URL"), "Database connection URL")
		migrations = flag.String("path", "", "Path to migrations directory")
		module     = flag.String("module", "", "Module to migrate (e.g., tenant, website, user)")
		envFile    = flag.String("env", ".env", "Path to .env file")
	)
	flag.Parse()

	// Load .env file if it exists
	if _, err := os.Stat(*envFile); err == nil {
		if err := godotenv.Load(*envFile); err != nil {
			log.Printf("Warning: Failed to load .env file: %v", err)
		} else {
			log.Printf("Loaded environment variables from: %s", *envFile)
		}
	}

	if *dbURL == "" {
		// Try to build from environment variables
		host := os.Getenv("DB_HOST")
		port := os.Getenv("DB_PORT")
		user := os.Getenv("DB_USER")
		pass := os.Getenv("DB_PASSWORD")
		name := os.Getenv("DB_NAME")
		charset := os.Getenv("DB_CHARSET")

		if host != "" && user != "" && name != "" {
			if port == "" {
				port = "3306"
			}
			if charset == "" {
				charset = "utf8mb4"
			}
			*dbURL = fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s", user, pass, host, port, name, charset)
		} else {
			log.Fatal("Database connection information is required. Set DATABASE_URL or DB_* environment variables")
		}
	}

	// Determine migrations path
	if *migrations == "" {
		// Default to internal/database/migrations
		wd, err := os.Getwd()
		if err != nil {
			log.Fatalf("Failed to get working directory: %v", err)
		}
		*migrations = filepath.Join(wd, "internal", "database", "migrations")

		// If a specific module is requested, append module directory
		if *module != "" {
			*migrations = filepath.Join(*migrations, *module)
		}
	}

	// Verify migrations directory exists
	if _, err := os.Stat(*migrations); os.IsNotExist(err) {
		log.Fatalf("Migrations directory does not exist: %s", *migrations)
	}

	log.Printf("Using migrations from: %s", *migrations)

	// Connect to database
	db, err := sql.Open("mysql", *dbURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	// Create custom migration manager
	manager, err := database.NewCustomMigrationManager(db, *migrations)
	if err != nil {
		log.Fatalf("Failed to create migration manager: %v", err)
	}
	defer manager.Close()

	// Execute command
	switch *command {
	case "up":
		log.Println("Running migrations...")
		if err := manager.Up(); err != nil {
			log.Fatalf("Migration up failed: %v", err)
		}
		log.Println("✅ Migrations completed successfully")

	case "down":
		log.Println("Rolling back last migration...")
		if err := manager.Down(); err != nil {
			log.Fatalf("Migration down failed: %v", err)
		}
		log.Println("✅ Rolled back last migration")

	case "version":
		status, err := manager.GetMigrationStatus()
		if err != nil {
			log.Fatalf("Failed to get status: %v", err)
		}
		fmt.Println("Migration Status:")
		fmt.Printf("  Applied Migrations: %v\n", status["applied_migrations"])
		fmt.Printf("  Pending Migrations: %v\n", status["pending_migrations"])
		if lastApplied, ok := status["last_applied"]; ok {
			fmt.Printf("  Last Applied: %v\n", lastApplied)
		}

	case "force":
		log.Println("Force command not supported in custom migration manager")
		log.Println("Use 'down' command to rollback specific migrations")

	case "status":
		status, err := manager.GetMigrationStatus()
		if err != nil {
			log.Fatalf("Failed to get status: %v", err)
		}
		fmt.Println("Migration Status:")
		fmt.Printf("  Total Migrations: %v\n", status["total_migrations"])
		fmt.Printf("  Applied Count: %v\n", status["applied_count"])
		fmt.Printf("  Pending Count: %v\n", status["pending_count"])
		fmt.Printf("  Applied Migrations: %v\n", status["applied_migrations"])
		fmt.Printf("  Pending Migrations: %v\n", status["pending_migrations"])
		if lastApplied, ok := status["last_applied"]; ok {
			fmt.Printf("  Last Applied: %v\n", lastApplied)
			if appliedAt, ok := status["last_applied_at"]; ok {
				fmt.Printf("  Last Applied At: %v\n", appliedAt)
			}
		}

	default:
		log.Fatalf("Unknown command: %s", *command)
	}
}
