# Makefile Commands Reference

## Quick Reference Table

| Command | Description | Example |
|---------|-------------|---------|
| `make help` | Hiển thị tất cả commands | `make help` |
| `make run` | Chạy server | `make run` |
| `make dev` | Chạy với hot reload | `make dev` |
| `make build` | Build binary | `make build` |
| `make test` | Chạy unit tests | `make test` |
| `make migrate-up` | Chạy migrations | `make migrate-up` |
| `make seed-run` | Chạy seeders | `make seed-run` |

## Detailed Command Documentation

### 🚀 Development Commands

#### make run
```bash
make run
```
**Mô tả**: Chạy API server trong development mode
- Load `.env` file tự động
- Chạy trên port mặc định (8080 hoặc từ env)
- Enable debug logging

#### make dev
```bash
make dev
```
**<PERSON><PERSON> tả**: Chạy server với hot reload sử dụng Air
- Tự động restart khi code thay đổi
- Watch files: `*.go`, `*.env`, `*.yaml`
- Tự động cài Air nếu chưa có

#### make build
```bash
make build
```
**Mô tả**: Build production binary
- Output: `bin/server`
- Optimized build với flags production
- Strip debug symbols

---

### 🧪 Testing Commands

#### make test
```bash
make test
# Hoặc test specific package
make test PKG=./internal/modules/auth/...
```
**Mô tả**: Chạy unit tests
- Enable race detector
- Show coverage summary
- Fail fast on first error

#### make test-coverage
```bash
make test-coverage
```
**Mô tả**: Generate test coverage report
- Output HTML report: `coverage.html`
- Show coverage percentage
- Highlight uncovered code

#### make test-e2e
```bash
make test-e2e
```
**Mô tả**: Chạy end-to-end tests
- Requires running server
- Tests full API flows
- Uses test database

#### make test-e2e-auth
```bash
make test-e2e-auth
```
**Mô tả**: E2E tests cho auth module
- Test login/register flows
- Test JWT validation
- Test permissions

#### make test-all
```bash
make test-all
```
**Mô tả**: Chạy tất cả tests
- Unit tests + E2E tests
- Full coverage report
- CI/CD ready

---

### 🔍 Code Quality Commands

#### make lint
```bash
make lint
```
**Mô tả**: Kiểm tra code với golangci-lint
- Auto-install golangci-lint
- Check code style
- Find potential bugs
- Security scanning

#### make fmt
```bash
make fmt
```
**Mô tả**: Format code theo Go standards
- Run `go fmt`
- Update all `.go` files
- Ensure consistent style

#### make vet
```bash
make vet
```
**Mô tả**: Analyze code với go vet
- Find suspicious constructs
- Check for common mistakes
- Type checking

#### make check
```bash
make check
```
**Mô tả**: Run all quality checks
- Format → Vet → Lint → Test
- Pre-commit hook friendly
- CI/CD integration

---

### 🗄️ Database Migration Commands

#### make migrate-up
```bash
# Chạy tất cả migrations
make migrate-up

# Chạy cho module specific
make migrate-up MODULE=a_tenant
make migrate-up MODULE=d_auth
make migrate-up MODULE=g_blog
```
**Mô tả**: Apply pending migrations
- Module options: `a_tenant`, `b_website`, `c_user`, `d_auth`, `e_rbac`, `f_onboarding`, `g_blog`
- Auto-load `.env`
- Show applied migrations

#### make migrate-down
```bash
# Rollback last migration
make migrate-down

# Rollback module specific
make migrate-down MODULE=d_auth
```
**Mô tả**: Rollback migrations
- Rollback 1 migration at a time
- Confirm before destructive action
- Module-specific rollback

#### make migrate-status
```bash
# Check all migrations
make migrate-status

# Check module status
make migrate-status MODULE=g_blog
```
**Mô tả**: View migration status
- Show applied/pending migrations
- Check for conflicts
- Module filtering

#### make migrate-version
```bash
make migrate-version
```
**Mô tả**: Show current migration version
- Display latest applied migration
- Check schema version
- Useful for debugging

#### make migrate-create
```bash
make migrate-create
```
**Mô tả**: Create new migration (interactive)
- Prompts for module selection
- Prompts for migration name
- Creates .up.sql and .down.sql files
- Auto-generates proper numbering

---

### 🌱 Database Seeding Commands

#### make seed-run
```bash
# Run all seeders
make seed-run

# Run specific seeders
make seed-run SEEDERS=tenant
make seed-run SEEDERS=user,rbac
make seed-run SEEDERS=blog,media
```
**Mô tả**: Execute database seeders
- Available seeders: `tenant`, `user`, `rbac`, `blog`, `media`, `notification`
- Creates test data
- Idempotent execution

#### make seed-rollback
```bash
# Rollback all seeders
make seed-rollback

# Rollback specific
make seed-rollback SEEDERS=blog
```
**Mô tả**: Remove seeded data
- Clean test data
- Preserve user data
- Selective rollback

#### make seed-list
```bash
make seed-list
```
**Mô tả**: List available seeders
- Show all seeder modules
- Display descriptions
- Check dependencies

---

### 🧪 API Testing Commands

#### make bruno
```bash
make bruno

# Run specific collection
make bruno COLLECTION=Auth
make bruno COLLECTION=User
```
**Mô tả**: Run Bruno API tests
- Requires Bruno CLI
- Uses local environment
- Tests API endpoints

---

### 🐳 Docker Commands

#### make docker-build
```bash
make docker-build

# With custom tag
make docker-build TAG=v1.0.0
```
**Mô tả**: Build Docker image
- Multi-stage build
- Optimized size
- Production ready

#### make docker-run
```bash
make docker-run

# With custom port
make docker-run PORT=9000
```
**Mô tả**: Run Docker container
- Mount volumes
- Environment variables
- Port mapping

---

### 🛠️ Utility Commands

#### make clean
```bash
make clean
```
**Mô tả**: Clean build artifacts
- Remove `bin/` directory
- Clean test cache
- Remove coverage files
- Clean temporary files

#### make deps
```bash
make deps
```
**Mô tả**: Download dependencies
- Run `go mod download`
- Verify checksums
- Update go.sum
- Cache dependencies

#### make reset-db
```bash
make reset-db
```
**Mô tả**: Reset database (DANGER!)
- Drop all tables
- Re-run migrations
- Clear all data
- Requires confirmation

---

## Environment Variables

### Required Variables
```bash
# Database
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=blog_api_v3

# Or use DATABASE_URL
DATABASE_URL=mysql://user:pass@localhost:3306/blog_api_v3
```

### Optional Variables
```bash
# Server
PORT=8080
ENV=development

# Features
ENABLE_SWAGGER=true
ENABLE_METRICS=true

# Security
JWT_SECRET=your-secret-key
BCRYPT_COST=10
```

---

## Command Combinations

### Initial Setup
```bash
# 1. Install dependencies
make deps

# 2. Run migrations
make migrate-up

# 3. Seed test data
make seed-run

# 4. Start development
make dev
```

### Before Commit
```bash
# Run all checks
make check

# Or individually
make fmt
make lint
make test
```

### Deploy Preparation
```bash
# 1. Run all tests
make test-all

# 2. Build binary
make build

# 3. Build Docker image
make docker-build

# 4. Check migrations
make migrate-status
```

### Database Reset (Development)
```bash
# Complete reset
make reset-db
make seed-run

# Or selective
make migrate-down MODULE=g_blog
make migrate-up MODULE=g_blog
```

---

## Module-Specific Commands

### Tenant Module
```bash
make migrate-up MODULE=a_tenant
make migrate-status MODULE=a_tenant
make seed-run SEEDERS=tenant
```

### Auth Module
```bash
make migrate-up MODULE=d_auth
make test PKG=./internal/modules/auth/...
make test-e2e-auth
```

### Blog Module
```bash
make migrate-up MODULE=g_blog
make seed-run SEEDERS=blog
make bruno COLLECTION=Blog
```

---

## Troubleshooting

### Command Not Working

1. **Check Makefile exists**
   ```bash
   ls -la Makefile
   ```

2. **Check make version**
   ```bash
   make --version
   ```

3. **Use verbose mode**
   ```bash
   make migrate-up VERBOSE=true
   ```

### Database Connection Issues

1. **Check .env file**
   ```bash
   cat .env | grep DB_
   ```

2. **Test connection**
   ```bash
   make migrate-status
   ```

3. **Check MySQL running**
   ```bash
   docker ps | grep mysql
   # or
   ps aux | grep mysql
   ```

### Permission Errors

1. **Check file permissions**
   ```bash
   chmod +x scripts/*.sh
   ```

2. **Check database user**
   ```sql
   -- In MySQL
   SHOW GRANTS FOR 'user'@'localhost';
   ```

### Module Not Found

1. **List available modules**
   ```bash
   ls -la internal/database/migrations/
   ```

2. **Check module naming**
   - Use: `a_tenant`, `b_website`, `c_user`, etc.
   - Not: `tenant`, `website`, `user`

---

## Tips & Best Practices

### 1. Use Aliases
```bash
# Add to ~/.bashrc or ~/.zshrc
alias mr="make run"
alias md="make dev"
alias mt="make test"
alias mc="make check"
```

### 2. Combine Commands
```bash
# Format and test
make fmt && make test

# Migrate and seed
make migrate-up && make seed-run
```

### 3. Use Watch Mode
```bash
# Watch tests
watch -n 2 make test

# Watch migration status
watch -n 5 make migrate-status
```

### 4. Create Custom Commands
```makefile
# Add to Makefile
.PHONY: refresh
refresh:
	@make migrate-down
	@make migrate-up
	@make seed-run
	@echo "Database refreshed!"
```

### 5. Debug Commands
```bash
# See actual commands
make -n migrate-up

# Debug variables
make print-DB_NAME
```