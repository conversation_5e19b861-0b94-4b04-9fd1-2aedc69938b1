# CLI Tools Architecture

## Overview

Blog API v3 CLI tools đượ<PERSON> thiết kế theo kiến trúc modular, scalable và maintainable. Tài liệu này mô tả architecture, design patterns, và best practices cho CLI tools.

## Architecture Principles

### 1. Separation of Concerns
- **Command Logic**: Business logic riêng biệt với CLI interface
- **Configuration**: Centralized config management
- **Database**: Reuse existing database layer
- **Logging**: Unified logging across all tools

### 2. Modularity
- **Pluggable Commands**: Easy to add new commands
- **Shared Libraries**: Common functionality reused
- **Independent Tools**: Each tool can run standalone
- **Module Isolation**: CLI tools per module/domain

### 3. Consistency
- **Flag Naming**: Consistent across all tools
- **Output Format**: Standardized output patterns
- **Error Handling**: Uniform error responses
- **Help Text**: Standard help format

## System Communication Flow

### 1. Overall CLI System Architecture

```mermaid
graph TB
    subgraph "User Interface"
        User[User]
        Make[Makefile]
        Shell[Shell/Terminal]
    end
    
    subgraph "CLI Tools Layer"
        MigrateCLI[migrate CLI]
        SeedCLI[seed CLI]
        UserCLI[user CLI]
        ServerCLI[server CLI]
        CustomCLI[custom CLI]
    end
    
    subgraph "Shared CLI Framework"
        CLIApp[CLI App Framework]
        BaseCmd[Base Command]
        Config[Config Manager]
        Output[Output Formatter]
        Errors[Error Handler]
    end
    
    subgraph "Business Logic Layer"
        MigrateService[Migration Service]
        SeedService[Seeder Service]
        UserService[User Service]
        AuthService[Auth Service]
        TenantService[Tenant Service]
    end
    
    subgraph "Data Layer"
        DB[(Database)]
        Redis[(Redis Cache)]
        Files[File System]
    end
    
    subgraph "External Dependencies"
        Env[.env Config]
        Logs[Log Files]
    end
    
    %% User interactions
    User --> Make
    User --> Shell
    Make --> MigrateCLI
    Make --> SeedCLI
    Make --> UserCLI
    Shell --> MigrateCLI
    Shell --> SeedCLI
    Shell --> UserCLI
    
    %% CLI Tools use framework
    MigrateCLI --> CLIApp
    SeedCLI --> CLIApp
    UserCLI --> CLIApp
    ServerCLI --> CLIApp
    CustomCLI --> CLIApp
    
    %% Framework components
    CLIApp --> BaseCmd
    CLIApp --> Config
    CLIApp --> Output
    CLIApp --> Errors
    
    %% Business logic connections
    MigrateCLI --> MigrateService
    SeedCLI --> SeedService
    UserCLI --> UserService
    UserCLI --> AuthService
    UserCLI --> TenantService
    
    %% Data access
    MigrateService --> DB
    SeedService --> DB
    UserService --> DB
    AuthService --> DB
    TenantService --> DB
    UserService --> Redis
    
    %% External dependencies
    Config --> Env
    Errors --> Logs
    Output --> Logs
    MigrateService --> Files
    SeedService --> Files
```

### 2. CLI Tool Lifecycle Flow

```mermaid
sequenceDiagram
    participant User
    participant Make as Makefile
    participant CLI as CLI Tool
    participant Framework as CLI Framework
    participant Service as Business Service
    participant DB as Database
    
    User->>Make: make migrate-up
    Make->>CLI: ./bin/migrate -command up
    CLI->>Framework: Initialize App
    Framework->>Framework: Load Config (.env)
    Framework->>Framework: Parse Commands & Flags
    CLI->>Service: Execute Migration Service
    Service->>DB: Connect to Database
    Service->>DB: Execute SQL Migrations
    DB-->>Service: Migration Results
    Service-->>CLI: Success/Error Response
    CLI->>Framework: Format Output
    Framework->>Framework: Handle Errors (if any)
    Framework-->>User: Display Results
```

### 3. Module Communication Pattern

```mermaid
graph LR
    subgraph "CLI Tool Process"
        CLI[CLI Entry Point]
        Parser[Argument Parser]
        Command[Command Handler]
        Output[Output Formatter]
    end
    
    subgraph "Service Layer"
        Service[Module Service]
        Repo[Repository]
        Validator[Validator]
    end
    
    subgraph "Shared Components"
        Config[Config Manager]
        Logger[Logger]
        Cache[Cache Manager]
        DB[Database Pool]
    end
    
    CLI --> Parser
    Parser --> Command
    Command --> Service
    Service --> Repo
    Service --> Validator
    Command --> Output
    
    %% Shared components usage
    CLI --> Config
    Command --> Logger
    Service --> Logger
    Service --> Cache
    Repo --> DB
    
    %% Configuration flow
    Config -.-> Service
    Config -.-> Repo
    Config -.-> Logger
```

### 4. Multi-Tool Coordination

```mermaid
graph TB
    subgraph "Development Workflow"
        Dev[Developer]
        Dev --> Reset[make reset-db]
        Reset --> Migrate[make migrate-up]
        Migrate --> Seed[make seed-run]
        Seed --> Test[make test]
        Test --> Run[make dev]
    end
    
    subgraph "CLI Tools Coordination"
        ResetTool[Reset CLI]
        MigrateTool[Migrate CLI]
        SeedTool[Seed CLI]
        TestTool[Test Runner]
        ServerTool[Dev Server]
    end
    
    subgraph "Shared State"
        DBState[(Database State)]
        ConfigState[Config State]
        LogState[Log State]
    end
    
    ResetTool --> DBState
    MigrateTool --> DBState
    SeedTool --> DBState
    
    ResetTool --> ConfigState
    MigrateTool --> ConfigState
    SeedTool --> ConfigState
    TestTool --> ConfigState
    ServerTool --> ConfigState
    
    MigrateTool --> LogState
    SeedTool --> LogState
    TestTool --> LogState
    ServerTool --> LogState
```

### 5. CLI vs Server Architecture Separation

```mermaid
graph TB
    subgraph "CLI Environment"
        CLIUser[CLI User]
        CLITools[CLI Tools]
        CLIConfig[CLI Config]
        CLIOutput[CLI Output]
    end
    
    subgraph "Server Environment"
        APIUser[API Users]
        HTTPServer[HTTP Server]
        APIConfig[API Config]
        APIResponse[API Response]
    end
    
    subgraph "Shared Core"
        Services[Business Services]
        Models[Data Models]
        Repository[Repository Layer]
        Database[(Database)]
        Cache[(Cache)]
    end
    
    %% CLI path
    CLIUser --> CLITools
    CLITools --> CLIConfig
    CLITools --> Services
    Services --> Repository
    Repository --> Database
    Repository --> Cache
    CLITools --> CLIOutput
    
    %% Server path
    APIUser --> HTTPServer
    HTTPServer --> APIConfig
    HTTPServer --> Services
    APIResponse --> APIUser
    HTTPServer --> APIResponse
    
    %% Shared components
    Services --> Models
    Repository --> Models
    
    style CLIEnvironment fill:#e1f5fe
    style ServerEnvironment fill:#f3e5f5
    style SharedCore fill:#e8f5e8
```

## Core Components

### 1. Command Interface

```go
// internal/cli/interfaces.go
package cli

import "context"

// Command represents a CLI command
type Command interface {
    // Name returns the command name
    Name() string
    
    // Description returns brief description
    Description() string
    
    // Usage returns usage string
    Usage() string
    
    // Execute runs the command
    Execute(ctx context.Context, args []string) error
}

// CommandGroup groups related commands
type CommandGroup interface {
    // GroupName returns group name
    GroupName() string
    
    // Commands returns list of commands in group
    Commands() []Command
}
```

### 2. Base Command Structure

```go
// internal/cli/base.go
package cli

import (
    "context"
    "flag"
    "fmt"
    "os"
)

// BaseCommand provides common functionality
type BaseCommand struct {
    name        string
    description string
    usage       string
    flagSet     *flag.FlagSet
    
    // Common flags
    verbose *bool
    dryRun  *bool
    help    *bool
}

func NewBaseCommand(name, description, usage string) *BaseCommand {
    cmd := &BaseCommand{
        name:        name,
        description: description,
        usage:       usage,
        flagSet:     flag.NewFlagSet(name, flag.ExitOnError),
    }
    
    // Setup common flags
    cmd.verbose = cmd.flagSet.Bool("v", false, "Verbose output")
    cmd.dryRun = cmd.flagSet.Bool("dry-run", false, "Simulate execution")
    cmd.help = cmd.flagSet.Bool("h", false, "Show help")
    
    return cmd
}

func (c *BaseCommand) Name() string        { return c.name }
func (c *BaseCommand) Description() string { return c.description }
func (c *BaseCommand) Usage() string       { return c.usage }

func (c *BaseCommand) ParseArgs(args []string) error {
    return c.flagSet.Parse(args)
}

func (c *BaseCommand) IsVerbose() bool { return *c.verbose }
func (c *BaseCommand) IsDryRun() bool  { return *c.dryRun }
func (c *BaseCommand) ShowHelp() bool  { return *c.help }
```

### 3. CLI Application Framework

```go
// internal/cli/app.go
package cli

import (
    "context"
    "fmt"
    "os"
)

// App represents a CLI application
type App struct {
    name        string
    version     string
    description string
    commands    map[string]Command
    groups      map[string]CommandGroup
}

func NewApp(name, version, description string) *App {
    return &App{
        name:        name,
        version:     version,
        description: description,
        commands:    make(map[string]Command),
        groups:      make(map[string]CommandGroup),
    }
}

func (a *App) RegisterCommand(cmd Command) {
    a.commands[cmd.Name()] = cmd
}

func (a *App) RegisterGroup(group CommandGroup) {
    a.groups[group.GroupName()] = group
    for _, cmd := range group.Commands() {
        a.RegisterCommand(cmd)
    }
}

func (a *App) Run(ctx context.Context, args []string) error {
    if len(args) < 1 {
        a.printUsage()
        return nil
    }
    
    cmdName := args[0]
    cmd, exists := a.commands[cmdName]
    if !exists {
        return fmt.Errorf("unknown command: %s", cmdName)
    }
    
    return cmd.Execute(ctx, args[1:])
}

func (a *App) printUsage() {
    fmt.Printf("%s v%s - %s\n\n", a.name, a.version, a.description)
    fmt.Printf("Usage: %s <command> [flags]\n\n", a.name)
    
    // Print groups
    for groupName, group := range a.groups {
        fmt.Printf("%s Commands:\n", groupName)
        for _, cmd := range group.Commands() {
            fmt.Printf("  %-12s %s\n", cmd.Name(), cmd.Description())
        }
        fmt.Println()
    }
    
    fmt.Printf("Use '%s <command> -h' for more information about a command.\n", a.name)
}
```

## Tool-Specific Architecture

### 1. Migration Tool Detailed Flow

```mermaid
graph TB
    subgraph "Migration CLI Tool"
        MigrateMain[cmd/migrate/main.go]
        MigrateApp[Migration App]
        MigrateCommands[Migration Commands]
    end
    
    subgraph "Migration Commands"
        UpCmd[Up Command]
        DownCmd[Down Command]
        StatusCmd[Status Command]
        CreateCmd[Create Command]
        VersionCmd[Version Command]
    end
    
    subgraph "Migration Service Layer"
        Migrator[Migration Service]
        FileScanner[File Scanner]
        SQLExecutor[SQL Executor]
        StateTracker[State Tracker]
    end
    
    subgraph "Module System"
        ModuleA[a_tenant/]
        ModuleB[b_website/]
        ModuleC[c_user/]
        ModuleD[d_auth/]
        ModuleE[e_rbac/]
    end
    
    subgraph "Database Layer"
        MigrationTable[migration_history]
        MainDB[(Main Database)]
        TransactionMgr[Transaction Manager]
    end
    
    %% Main flow
    MigrateMain --> MigrateApp
    MigrateApp --> MigrateCommands
    MigrateCommands --> UpCmd
    MigrateCommands --> DownCmd
    MigrateCommands --> StatusCmd
    
    %% Service connections
    UpCmd --> Migrator
    DownCmd --> Migrator
    StatusCmd --> Migrator
    CreateCmd --> FileScanner
    
    %% Migration service operations
    Migrator --> FileScanner
    Migrator --> SQLExecutor
    Migrator --> StateTracker
    
    %% Module scanning
    FileScanner --> ModuleA
    FileScanner --> ModuleB
    FileScanner --> ModuleC
    FileScanner --> ModuleD
    FileScanner --> ModuleE
    
    %% Database operations
    SQLExecutor --> TransactionMgr
    StateTracker --> MigrationTable
    TransactionMgr --> MainDB
```

### 2. User Management CLI Tool Flow

```mermaid
graph TB
    subgraph "User CLI Tool"
        UserMain[cmd/user/main.go]
        UserApp[User Management App]
        UserCommands[User Commands]
    end
    
    subgraph "User Commands"
        CreateCmd[Create User]
        ListCmd[List Users]
        UpdateCmd[Update User]
        DeleteCmd[Delete User]
        InfoCmd[User Info]
        PermCmd[Permissions]
    end
    
    subgraph "Multi-Module Integration"
        UserSvc[User Service]
        AuthSvc[Auth Service]
        TenantSvc[Tenant Service]
        RBACsvc[RBAC Service]
    end
    
    subgraph "Data Operations"
        UserRepo[User Repository]
        AuthRepo[Auth Repository]
        TenantRepo[Tenant Repository]
        RBACRepo[RBAC Repository]
    end
    
    subgraph "Database Tables"
        UsersTable[users]
        TenantsTable[tenants]
        MembershipTable[tenant_memberships]
        RolesTable[rbac_user_roles]
        TokensTable[auth_tokens]
    end
    
    %% Main flow
    UserMain --> UserApp
    UserApp --> UserCommands
    UserCommands --> CreateCmd
    UserCommands --> ListCmd
    UserCommands --> UpdateCmd
    
    %% Service integration
    CreateCmd --> UserSvc
    CreateCmd --> TenantSvc
    CreateCmd --> RBACsvc
    UpdateCmd --> UserSvc
    UpdateCmd --> AuthSvc
    PermCmd --> RBACsvc
    
    %% Repository layer
    UserSvc --> UserRepo
    AuthSvc --> AuthRepo
    TenantSvc --> TenantRepo
    RBACsvc --> RBACRepo
    
    %% Database access
    UserRepo --> UsersTable
    TenantRepo --> TenantsTable
    TenantRepo --> MembershipTable
    RBACRepo --> RolesTable
    AuthRepo --> TokensTable
```

### 3. Seeder Tool Architecture Flow

```mermaid
sequenceDiagram
    participant CLI as Seed CLI
    participant Manager as Seeder Manager
    participant Registry as Seeder Registry
    participant TenantSeeder as Tenant Seeder
    participant UserSeeder as User Seeder
    participant RBACSeeder as RBAC Seeder
    participant DB as Database
    
    CLI->>Manager: seed run
    Manager->>Registry: Get dependency order
    Registry-->>Manager: [tenant, user, rbac]
    
    Manager->>TenantSeeder: Execute()
    TenantSeeder->>DB: Insert tenants
    DB-->>TenantSeeder: Success
    TenantSeeder-->>Manager: Completed
    
    Manager->>UserSeeder: Execute()
    UserSeeder->>DB: Insert users
    UserSeeder->>DB: Insert user_profiles
    DB-->>UserSeeder: Success
    UserSeeder-->>Manager: Completed
    
    Manager->>RBACSeeder: Execute()
    RBACSeeder->>DB: Insert roles
    RBACSeeder->>DB: Insert user_roles
    DB-->>RBACSeeder: Success
    RBACSeeder-->>Manager: Completed
    
    Manager-->>CLI: All seeders completed
```

### 4. CLI Tools vs Module Services Interaction

```mermaid
graph LR
    subgraph "CLI Layer"
        MigrateCLI[Migrate CLI]
        SeedCLI[Seed CLI]
        UserCLI[User CLI]
        TenantCLI[Tenant CLI]
    end
    
    subgraph "CLI Framework"
        CLIFramework[Shared CLI Framework]
        Config[Configuration]
        Output[Output Handler]
        Validation[Input Validation]
    end
    
    subgraph "Business Services"
        MigrationSvc[Migration Service]
        SeederSvc[Seeder Service]
        UserSvc[User Service]
        AuthSvc[Auth Service]
        TenantSvc[Tenant Service]
        RBACsvc[RBAC Service]
    end
    
    subgraph "Data Layer"
        UserRepo[User Repository]
        AuthRepo[Auth Repository]
        TenantRepo[Tenant Repository]
        Database[(Database)]
    end
    
    %% CLI to Framework
    MigrateCLI --> CLIFramework
    SeedCLI --> CLIFramework
    UserCLI --> CLIFramework
    TenantCLI --> CLIFramework
    
    %% Framework components
    CLIFramework --> Config
    CLIFramework --> Output
    CLIFramework --> Validation
    
    %% CLI to Services (bypassing HTTP layer)
    MigrateCLI -.-> MigrationSvc
    SeedCLI -.-> SeederSvc
    UserCLI -.-> UserSvc
    UserCLI -.-> AuthSvc
    UserCLI -.-> TenantSvc
    TenantCLI -.-> TenantSvc
    TenantCLI -.-> RBACsvc
    
    %% Services to Repositories
    UserSvc --> UserRepo
    AuthSvc --> AuthRepo
    TenantSvc --> TenantRepo
    
    %% Repositories to Database
    UserRepo --> Database
    AuthRepo --> Database
    TenantRepo --> Database
    MigrationSvc --> Database
    SeederSvc --> Database
    
    style CLILayer fill:#e3f2fd
    style BusinessServices fill:#f3e5f5
    style DataLayer fill:#e8f5e8
```

### 5. Multi-Tenant CLI Operations Flow

```mermaid
graph TB
    subgraph "CLI Input"
        UserInput[User Command]
        TenantFlag[--tenant-id flag]
        GlobalFlag[--global flag]
    end
    
    subgraph "Context Resolution"
        ContextResolver[Context Resolver]
        TenantValidator[Tenant Validator]
        PermissionChecker[Permission Checker]
    end
    
    subgraph "Operation Routing"
        TenantScoped[Tenant-Scoped Operation]
        GlobalScoped[Global Operation]
        MultiTenant[Multi-Tenant Operation]
    end
    
    subgraph "Service Execution"
        TenantService[Tenant Service]
        UserService[User Service]
        AuthService[Auth Service]
    end
    
    subgraph "Database Access"
        TenantQuery[Tenant-Scoped Query]
        GlobalQuery[Global Query]
        Database[(Database)]
    end
    
    %% Input processing
    UserInput --> ContextResolver
    TenantFlag --> ContextResolver
    GlobalFlag --> ContextResolver
    
    %% Context validation
    ContextResolver --> TenantValidator
    ContextResolver --> PermissionChecker
    
    %% Operation routing
    TenantValidator --> TenantScoped
    PermissionChecker --> GlobalScoped
    ContextResolver --> MultiTenant
    
    %% Service selection
    TenantScoped --> TenantService
    TenantScoped --> UserService
    GlobalScoped --> AuthService
    MultiTenant --> TenantService
    
    %% Database execution
    TenantService --> TenantQuery
    UserService --> TenantQuery
    AuthService --> GlobalQuery
    TenantQuery --> Database
    GlobalQuery --> Database
    
    %% Examples
    TenantScoped -.-> |"user create --tenant-id 1"| UserService
    GlobalScoped -.-> |"migrate up"| AuthService
    MultiTenant -.-> |"tenant list"| TenantService
```

### 1. Migration Tool Architecture

```go
// cmd/migrate/main.go
package main

import (
    "context"
    "log"
    "os"
    
    "github.com/tranthanhloi/wn-api-v3/internal/cli"
    "github.com/tranthanhloi/wn-api-v3/internal/cli/migrate"
)

func main() {
    // Create app
    app := cli.NewApp("migrate", "1.0.0", "Database migration tool")
    
    // Register migration commands
    migrationGroup := migrate.NewMigrationGroup()
    app.RegisterGroup(migrationGroup)
    
    // Run app
    ctx := context.Background()
    if err := app.Run(ctx, os.Args[1:]); err != nil {
        log.Fatal(err)
    }
}
```

```go
// internal/cli/migrate/group.go
package migrate

import (
    "github.com/tranthanhloi/wn-api-v3/internal/cli"
    "github.com/tranthanhloi/wn-api-v3/internal/database"
)

type MigrationGroup struct {
    migrator *database.Migrator
}

func NewMigrationGroup() *MigrationGroup {
    return &MigrationGroup{
        migrator: database.NewMigrator(),
    }
}

func (g *MigrationGroup) GroupName() string {
    return "Migration"
}

func (g *MigrationGroup) Commands() []cli.Command {
    return []cli.Command{
        NewUpCommand(g.migrator),
        NewDownCommand(g.migrator),
        NewStatusCommand(g.migrator),
        NewVersionCommand(g.migrator),
        NewCreateCommand(g.migrator),
    }
}
```

### 2. Seeder Tool Architecture

```go
// internal/cli/seed/group.go
package seed

import (
    "github.com/tranthanhloi/wn-api-v3/internal/cli"
    "github.com/tranthanhloi/wn-api-v3/internal/database/seeders"
)

type SeederGroup struct {
    manager *seeders.Manager
}

func NewSeederGroup() *SeederGroup {
    return &SeederGroup{
        manager: seeders.NewManager(),
    }
}

func (g *SeederGroup) GroupName() string {
    return "Seeder"
}

func (g *SeederGroup) Commands() []cli.Command {
    return []cli.Command{
        NewRunCommand(g.manager),
        NewRollbackCommand(g.manager),
        NewListCommand(g.manager),
        NewStatusCommand(g.manager),
    }
}
```

### 3. User Management Tool Architecture (Proposed)

```go
// internal/cli/user/group.go
package user

import (
    "github.com/tranthanhloi/wn-api-v3/internal/cli"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
)

type UserGroup struct {
    userService *services.UserService
    authService *services.AuthService
}

func NewUserGroup() *UserGroup {
    return &UserGroup{
        userService: services.NewUserService(),
        authService: services.NewAuthService(),
    }
}

func (g *UserGroup) GroupName() string {
    return "User Management"
}

func (g *UserGroup) Commands() []cli.Command {
    return []cli.Command{
        NewCreateCommand(g.userService),
        NewListCommand(g.userService),
        NewUpdateCommand(g.userService),
        NewDeleteCommand(g.userService),
        NewInfoCommand(g.userService),
        NewPermissionsCommand(g.authService),
    }
}
```

## Configuration Management

### 1. Environment Configuration

```go
// internal/cli/config/config.go
package config

import (
    "fmt"
    "os"
    
    "github.com/joho/godotenv"
)

type CLIConfig struct {
    Database DatabaseConfig
    Logger   LoggerConfig
    CLI      CLISettings
}

type DatabaseConfig struct {
    Host     string
    Port     string
    User     string
    Password string
    Name     string
    DSN      string
}

type LoggerConfig struct {
    Level  string
    Format string
    Output string
}

type CLISettings struct {
    DefaultTimeout int
    ColorOutput    bool
    ProgressBar    bool
}

func LoadConfig() (*CLIConfig, error) {
    // Load .env file
    if err := godotenv.Load(); err != nil {
        // .env is optional
    }
    
    config := &CLIConfig{
        Database: DatabaseConfig{
            Host:     getEnv("DB_HOST", "localhost"),
            Port:     getEnv("DB_PORT", "3306"),
            User:     getEnv("DB_USER", "root"),
            Password: getEnv("DB_PASSWORD", ""),
            Name:     getEnv("DB_NAME", "blog_api_v3"),
            DSN:      os.Getenv("DATABASE_URL"),
        },
        Logger: LoggerConfig{
            Level:  getEnv("LOG_LEVEL", "info"),
            Format: getEnv("LOG_FORMAT", "text"),
            Output: getEnv("LOG_OUTPUT", "stdout"),
        },
        CLI: CLISettings{
            DefaultTimeout: getEnvInt("CLI_TIMEOUT", 300),
            ColorOutput:    getEnvBool("CLI_COLOR", true),
            ProgressBar:    getEnvBool("CLI_PROGRESS", true),
        },
    }
    
    // Build DSN if not provided
    if config.Database.DSN == "" {
        config.Database.DSN = fmt.Sprintf(
            "%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=true",
            config.Database.User,
            config.Database.Password,
            config.Database.Host,
            config.Database.Port,
            config.Database.Name,
        )
    }
    
    return config, nil
}

func getEnv(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}
```

### 2. Command-Specific Configuration

```go
// internal/cli/config/command.go
package config

// CommandConfig holds command-specific configuration
type CommandConfig struct {
    // Migration settings
    Migration MigrationConfig
    
    // Seeder settings
    Seeder SeederConfig
    
    // User management settings
    User UserConfig
}

type MigrationConfig struct {
    MigrationsPath string
    BatchSize      int
    Timeout        int
}

type SeederConfig struct {
    SeedersPath string
    BatchSize   int
    Concurrent  bool
}

type UserConfig struct {
    DefaultRole       string
    RequireVerification bool
    PasswordMinLength int
}
```

## Output and Logging

### 1. Structured Output

```go
// internal/cli/output/output.go
package output

import (
    "encoding/json"
    "fmt"
    "io"
    "os"
    "text/tabwriter"
)

type OutputFormat string

const (
    FormatText OutputFormat = "text"
    FormatJSON OutputFormat = "json"
    FormatYAML OutputFormat = "yaml"
)

type Printer struct {
    writer io.Writer
    format OutputFormat
    color  bool
}

func NewPrinter(format OutputFormat, color bool) *Printer {
    return &Printer{
        writer: os.Stdout,
        format: format,
        color:  color,
    }
}

func (p *Printer) PrintSuccess(message string) {
    if p.color {
        fmt.Fprintf(p.writer, "\033[32m✓\033[0m %s\n", message)
    } else {
        fmt.Fprintf(p.writer, "✓ %s\n", message)
    }
}

func (p *Printer) PrintError(err error) {
    if p.color {
        fmt.Fprintf(p.writer, "\033[31m✗\033[0m %s\n", err.Error())
    } else {
        fmt.Fprintf(p.writer, "✗ %s\n", err.Error())
    }
}

func (p *Printer) PrintTable(headers []string, rows [][]string) {
    w := tabwriter.NewWriter(p.writer, 0, 0, 2, ' ', 0)
    
    // Print headers
    for i, header := range headers {
        if i > 0 {
            fmt.Fprint(w, "\t")
        }
        fmt.Fprint(w, header)
    }
    fmt.Fprintln(w)
    
    // Print rows
    for _, row := range rows {
        for i, cell := range row {
            if i > 0 {
                fmt.Fprint(w, "\t")
            }
            fmt.Fprint(w, cell)
        }
        fmt.Fprintln(w)
    }
    
    w.Flush()
}

func (p *Printer) PrintJSON(data interface{}) error {
    encoder := json.NewEncoder(p.writer)
    encoder.SetIndent("", "  ")
    return encoder.Encode(data)
}
```

### 2. Progress Indication

```go
// internal/cli/progress/progress.go
package progress

import (
    "fmt"
    "io"
    "strings"
    "time"
)

type ProgressBar struct {
    writer io.Writer
    total  int
    width  int
    done   int
}

func NewProgressBar(writer io.Writer, total, width int) *ProgressBar {
    return &ProgressBar{
        writer: writer,
        total:  total,
        width:  width,
    }
}

func (p *ProgressBar) Update(done int) {
    percent := float64(done) / float64(p.total)
    filled := int(percent * float64(p.width))
    
    bar := fmt.Sprintf("[%s%s] %d/%d (%.1f%%)",
        strings.Repeat("=", filled),
        strings.Repeat(" ", p.width-filled),
        done,
        p.total,
        percent*100,
    )
    
    fmt.Fprintf(p.writer, "\r%s", bar)
    
    if done >= p.total {
        fmt.Fprintln(p.writer)
    }
}

type Spinner struct {
    writer io.Writer
    chars  []string
    index  int
    active bool
}

func NewSpinner(writer io.Writer) *Spinner {
    return &Spinner{
        writer: writer,
        chars:  []string{"|", "/", "-", "\\"},
    }
}

func (s *Spinner) Start() {
    s.active = true
    go func() {
        for s.active {
            fmt.Fprintf(s.writer, "\r%s", s.chars[s.index])
            s.index = (s.index + 1) % len(s.chars)
            time.Sleep(100 * time.Millisecond)
        }
    }()
}

func (s *Spinner) Stop() {
    s.active = false
    fmt.Fprintf(s.writer, "\r")
}
```

## Error Handling

### 1. Error Types

```go
// internal/cli/errors/errors.go
package errors

import "fmt"

// CLIError represents a CLI-specific error
type CLIError struct {
    Code    int
    Message string
    Cause   error
}

func (e *CLIError) Error() string {
    if e.Cause != nil {
        return fmt.Sprintf("%s: %v", e.Message, e.Cause)
    }
    return e.Message
}

// Common error codes
const (
    ExitSuccess = 0
    ExitError   = 1
    ExitUsage   = 2
    ExitConfig  = 3
    ExitDB      = 4
)

// Error constructors
func NewConfigError(message string, cause error) *CLIError {
    return &CLIError{Code: ExitConfig, Message: message, Cause: cause}
}

func NewDatabaseError(message string, cause error) *CLIError {
    return &CLIError{Code: ExitDB, Message: message, Cause: cause}
}

func NewUsageError(message string) *CLIError {
    return &CLIError{Code: ExitUsage, Message: message}
}
```

### 2. Error Handler

```go
// internal/cli/errors/handler.go
package errors

import (
    "fmt"
    "os"
)

type ErrorHandler struct {
    verbose bool
}

func NewErrorHandler(verbose bool) *ErrorHandler {
    return &ErrorHandler{verbose: verbose}
}

func (h *ErrorHandler) Handle(err error) {
    if err == nil {
        return
    }
    
    if cliErr, ok := err.(*CLIError); ok {
        fmt.Fprintf(os.Stderr, "Error: %s\n", cliErr.Message)
        
        if h.verbose && cliErr.Cause != nil {
            fmt.Fprintf(os.Stderr, "Caused by: %v\n", cliErr.Cause)
        }
        
        os.Exit(cliErr.Code)
    } else {
        fmt.Fprintf(os.Stderr, "Error: %v\n", err)
        os.Exit(ExitError)
    }
}
```

## Testing CLI Tools

### 1. Command Testing Framework

```go
// internal/cli/testing/testing.go
package testing

import (
    "bytes"
    "context"
    "testing"
    
    "github.com/tranthanhloi/wn-api-v3/internal/cli"
)

// CommandTester helps test CLI commands
type CommandTester struct {
    cmd    cli.Command
    stdout *bytes.Buffer
    stderr *bytes.Buffer
}

func NewCommandTester(cmd cli.Command) *CommandTester {
    return &CommandTester{
        cmd:    cmd,
        stdout: &bytes.Buffer{},
        stderr: &bytes.Buffer{},
    }
}

func (ct *CommandTester) Execute(args []string) error {
    ctx := context.Background()
    return ct.cmd.Execute(ctx, args)
}

func (ct *CommandTester) GetOutput() string {
    return ct.stdout.String()
}

func (ct *CommandTester) GetError() string {
    return ct.stderr.String()
}

// Test helper functions
func AssertSuccess(t *testing.T, err error) {
    t.Helper()
    if err != nil {
        t.Fatalf("Expected success but got error: %v", err)
    }
}

func AssertError(t *testing.T, err error, expectedMessage string) {
    t.Helper()
    if err == nil {
        t.Fatal("Expected error but got success")
    }
    if err.Error() != expectedMessage {
        t.Fatalf("Expected error message '%s' but got '%s'", expectedMessage, err.Error())
    }
}

func AssertContains(t *testing.T, output, expected string) {
    t.Helper()
    if !bytes.Contains([]byte(output), []byte(expected)) {
        t.Fatalf("Output does not contain expected text.\nExpected: %s\nActual: %s", expected, output)
    }
}
```

### 2. Integration Testing

```go
// cmd/migrate/main_test.go
package main

import (
    "context"
    "os"
    "testing"
    
    "github.com/tranthanhloi/wn-api-v3/internal/cli"
    "github.com/tranthanhloi/wn-api-v3/internal/cli/migrate"
    cliTesting "github.com/tranthanhloi/wn-api-v3/internal/cli/testing"
)

func TestMigrateCommand(t *testing.T) {
    // Setup test database
    setupTestDB(t)
    defer cleanupTestDB(t)
    
    // Create migration group
    group := migrate.NewMigrationGroup()
    
    // Test up command
    upCmd := findCommand(group.Commands(), "up")
    tester := cliTesting.NewCommandTester(upCmd)
    
    err := tester.Execute([]string{})
    cliTesting.AssertSuccess(t, err)
    cliTesting.AssertContains(t, tester.GetOutput(), "Migration completed")
}

func setupTestDB(t *testing.T) {
    // Setup test database
    os.Setenv("DB_NAME", "blog_api_v3_test")
}

func cleanupTestDB(t *testing.T) {
    // Cleanup test database
    os.Unsetenv("DB_NAME")
}

func findCommand(commands []cli.Command, name string) cli.Command {
    for _, cmd := range commands {
        if cmd.Name() == name {
            return cmd
        }
    }
    return nil
}
```

## Performance Optimization

### 1. Connection Pooling

```go
// internal/cli/database/pool.go
package database

import (
    "context"
    "database/sql"
    "time"
)

type Pool struct {
    db *sql.DB
}

func NewPool(dsn string) (*Pool, error) {
    db, err := sql.Open("mysql", dsn)
    if err != nil {
        return nil, err
    }
    
    // Configure connection pool
    db.SetMaxOpenConns(10)
    db.SetMaxIdleConns(5)
    db.SetConnMaxLifetime(time.Hour)
    
    // Verify connection
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    if err := db.PingContext(ctx); err != nil {
        db.Close()
        return nil, err
    }
    
    return &Pool{db: db}, nil
}

func (p *Pool) Close() error {
    return p.db.Close()
}

func (p *Pool) DB() *sql.DB {
    return p.db
}
```

### 2. Batch Operations

```go
// internal/cli/batch/batch.go
package batch

import (
    "context"
    "sync"
)

type BatchProcessor struct {
    batchSize int
    workers   int
}

func NewBatchProcessor(batchSize, workers int) *BatchProcessor {
    return &BatchProcessor{
        batchSize: batchSize,
        workers:   workers,
    }
}

func (bp *BatchProcessor) Process(ctx context.Context, items []interface{}, processor func([]interface{}) error) error {
    batches := bp.createBatches(items)
    
    // Create worker pool
    jobs := make(chan []interface{}, len(batches))
    errors := make(chan error, len(batches))
    
    var wg sync.WaitGroup
    
    // Start workers
    for i := 0; i < bp.workers; i++ {
        wg.Add(1)
        go bp.worker(ctx, &wg, jobs, errors, processor)
    }
    
    // Send jobs
    for _, batch := range batches {
        jobs <- batch
    }
    close(jobs)
    
    // Wait for completion
    go func() {
        wg.Wait()
        close(errors)
    }()
    
    // Collect errors
    for err := range errors {
        if err != nil {
            return err
        }
    }
    
    return nil
}

func (bp *BatchProcessor) createBatches(items []interface{}) [][]interface{} {
    var batches [][]interface{}
    
    for i := 0; i < len(items); i += bp.batchSize {
        end := i + bp.batchSize
        if end > len(items) {
            end = len(items)
        }
        batches = append(batches, items[i:end])
    }
    
    return batches
}

func (bp *BatchProcessor) worker(ctx context.Context, wg *sync.WaitGroup, jobs <-chan []interface{}, errors chan<- error, processor func([]interface{}) error) {
    defer wg.Done()
    
    for batch := range jobs {
        select {
        case <-ctx.Done():
            errors <- ctx.Err()
            return
        default:
            errors <- processor(batch)
        }
    }
}
```

## Future Enhancements

### 1. Plugin System

```go
// internal/cli/plugins/plugin.go
package plugins

type Plugin interface {
    Name() string
    Version() string
    Commands() []cli.Command
    Initialize() error
    Cleanup() error
}

type PluginManager struct {
    plugins map[string]Plugin
}

func (pm *PluginManager) LoadPlugin(plugin Plugin) error {
    if err := plugin.Initialize(); err != nil {
        return err
    }
    
    pm.plugins[plugin.Name()] = plugin
    return nil
}
```

### 2. Configuration Management

```go
// internal/cli/config/manager.go
package config

type ConfigManager struct {
    profiles map[string]*CLIConfig
    active   string
}

func (cm *ConfigManager) SetProfile(name string, config *CLIConfig) {
    cm.profiles[name] = config
}

func (cm *ConfigManager) UseProfile(name string) error {
    if _, exists := cm.profiles[name]; !exists {
        return fmt.Errorf("profile not found: %s", name)
    }
    cm.active = name
    return nil
}
```

### 3. Interactive Mode

```go
// internal/cli/interactive/interactive.go
package interactive

import (
    "bufio"
    "context"
    "fmt"
    "os"
    "strings"
)

type InteractiveSession struct {
    app    *cli.App
    reader *bufio.Reader
    active bool
}

func NewInteractiveSession(app *cli.App) *InteractiveSession {
    return &InteractiveSession{
        app:    app,
        reader: bufio.NewReader(os.Stdin),
    }
}

func (is *InteractiveSession) Start(ctx context.Context) error {
    is.active = true
    
    fmt.Println("Interactive mode started. Type 'help' for commands or 'exit' to quit.")
    
    for is.active {
        fmt.Print("> ")
        line, err := is.reader.ReadString('\n')
        if err != nil {
            return err
        }
        
        line = strings.TrimSpace(line)
        if line == "" {
            continue
        }
        
        if line == "exit" {
            is.active = false
            break
        }
        
        args := strings.Fields(line)
        if err := is.app.Run(ctx, args); err != nil {
            fmt.Printf("Error: %v\n", err)
        }
    }
    
    return nil
}
```