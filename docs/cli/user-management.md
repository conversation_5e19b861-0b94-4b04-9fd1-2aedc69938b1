# CLI User Management Documentation

## Overview

Hiện tại, project chưa có CLI commands riêng cho việc quản lý users. Tài liệu này đề xuất thiết kế và implementation cho user management CLI.

## Current State

### Existing CLI Tools
1. **migrate** - Database migration management
2. **seed** - Database seeding
3. **reset-db** - Reset database
4. **server** - Run API server

### User Creation Methods (Current)
1. **API Endpoint**: `POST /auth/register` - Public registration
2. **Database Seeder**: Creates test users during seeding
3. **Direct Database**: Manual SQL insert (not recommended)

## Proposed CLI User Management Design

### Command Structure

```bash
# Main command
go run ./cmd/user [command] [flags]

# Or after building
./bin/user [command] [flags]
```

### Proposed Commands

#### 1. Create User
```bash
# Create a new user
./bin/user create \
  --email <EMAIL> \
  --username johndo<PERSON> \
  --password secretpass \
  --role admin \
  --tenant-id 1

# Create user interactively
./bin/user create --interactive
```

#### 2. List Users
```bash
# List all users
./bin/user list

# List users with filters
./bin/user list --tenant-id 1 --status active

# List with pagination
./bin/user list --page 1 --limit 20
```

#### 3. Update User
```bash
# Update user status
./bin/<NAME_EMAIL> --status active

# Update user role
./bin/<NAME_EMAIL> --role editor

# Reset password
./bin/<NAME_EMAIL> --reset-password
```

#### 4. Delete User
```bash
# Soft delete user
./bin/<NAME_EMAIL>

# Force delete (permanent)
./bin/<NAME_EMAIL> --force
```

#### 5. User Info
```bash
# Get user details
./bin/<NAME_EMAIL>

# Get user permissions
./bin/<NAME_EMAIL>
```

### Implementation Structure

```
cmd/user/
├── main.go
├── commands/
│   ├── create.go
│   ├── list.go
│   ├── update.go
│   ├── delete.go
│   └── info.go
├── flags/
│   └── flags.go
└── utils/
    ├── validator.go
    └── formatter.go
```

## Makefile Integration

Add these commands to Makefile:

```makefile
# User Management Commands
.PHONY: user-create user-list user-update user-delete user-info

user-create:
	@go run ./cmd/user create $(ARGS)

user-list:
	@go run ./cmd/user list $(ARGS)

user-update:
	@go run ./cmd/user update $(ARGS)

user-delete:
	@go run ./cmd/user delete $(ARGS)

user-info:
	@go run ./cmd/user info $(ARGS)

# Shortcut for creating admin user
admin-create:
	@go run ./cmd/user create \
		--email <EMAIL> \
		--username admin \
		--role super_admin \
		--interactive
```

## Usage Examples

### 1. Creating First Admin User

```bash
# After fresh installation
make migrate-up
make admin-create

# Enter password when prompted
# > Enter password: ********
# > Confirm password: ********
# ✓ User created successfully
# ✓ Email: <EMAIL>
# ✓ Role: super_admin
```

### 2. Creating Tenant Admin

```bash
make user-create ARGS="--email <EMAIL> --username tenant_admin --role admin --tenant-id 1"
```

### 3. Batch User Creation

```bash
# Create from CSV file
./bin/user import --file users.csv --tenant-id 1

# CSV format:
# email,username,role,full_name
# <EMAIL>,user1,editor,User One
# <EMAIL>,user2,viewer,User Two
```

## Security Considerations

### 1. Password Handling
- Never show passwords in plain text
- Use secure password input (hidden characters)
- Enforce password policies
- Generate secure random passwords if not provided

### 2. Permission Requirements
- CLI commands should require system-level access
- Use environment variables for authentication
- Log all CLI user operations

### 3. Multi-Tenant Considerations
- Always specify tenant context when creating users
- Validate tenant exists before user creation
- Handle cross-tenant user scenarios properly

## Environment Variables

```bash
# CLI Authentication
CLI_AUTH_TOKEN=system_token_here

# Default Values
DEFAULT_USER_ROLE=viewer
DEFAULT_USER_STATUS=active
DEFAULT_TENANT_ID=1

# Security
REQUIRE_STRONG_PASSWORD=true
MIN_PASSWORD_LENGTH=8
```

## Alternative: Using Existing Seeder

### Current Workaround

While dedicated CLI is not implemented, you can use the seeder:

1. **Modify seeder data**:
```go
// internal/database/seeders/user/user_seeder.go
// Add your admin user to the users array
```

2. **Run specific seeder**:
```bash
make seed-run SEEDERS=user
```

### Quick Admin User SQL

For immediate needs, use direct SQL:

```sql
-- Create admin user (password: 'admin123')
INSERT INTO users (email, username, password_hash, status, email_verified, created_at, updated_at)
VALUES (
    '<EMAIL>',
    'admin',
    '$2a$10$YourBcryptHashHere', -- Generate using bcrypt
    'active',
    true,
    NOW(),
    NOW()
);

-- Add to tenant
INSERT INTO tenant_memberships (user_id, tenant_id, role, status, is_primary)
VALUES (
    LAST_INSERT_ID(),
    1, -- Default tenant
    'owner',
    'active',
    true
);

-- Assign admin role
INSERT INTO rbac_user_roles (tenant_id, user_id, role_id)
VALUES (
    1,
    LAST_INSERT_ID(),
    1 -- Assuming role_id 1 is admin
);
```

## Implementation Priority

### Phase 1: Basic Commands
- [ ] Create user command
- [ ] List users command
- [ ] Update password command

### Phase 2: Advanced Features
- [ ] Role management
- [ ] Bulk operations
- [ ] Import/Export

### Phase 3: Integration
- [ ] Makefile shortcuts
- [ ] Docker integration
- [ ] CI/CD scripts

## Best Practices

### 1. Command Design
- Use consistent flag names
- Provide both short and long flags
- Support JSON output for automation
- Include dry-run mode

### 2. Error Handling
- Clear error messages
- Proper exit codes
- Validation before execution
- Rollback on failures

### 3. Logging
- Log all operations
- Include timestamp and operator
- Support different log levels
- Integrate with system logs

## Testing CLI Commands

### Manual Testing
```bash
# Test user creation
./bin/user create --email <EMAIL> --dry-run

# Test with invalid data
./bin/user create --email invalid-email

# Test permission errors
CLI_AUTH_TOKEN=invalid ./bin/user list
```

### Automated Testing
```go
// cmd/user/commands/create_test.go
func TestCreateCommand(t *testing.T) {
    // Test command execution
    // Verify database changes
    // Check output format
}
```

## Migration Path

### From API to CLI
1. Start using CLI for admin operations
2. Gradually phase out direct database access
3. Integrate with deployment scripts
4. Document standard procedures

### Integration with Existing Tools
- Use same database connection as migrate/seed
- Share configuration with server
- Consistent logging format
- Compatible with Docker setup