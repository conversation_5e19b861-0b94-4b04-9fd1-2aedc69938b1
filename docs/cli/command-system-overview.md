# Command System Overview

## <PERSON><PERSON>ới thiệu

Blog API v3 sử dụng hệ thống command-line interface (CLI) mạnh mẽ để quản lý và vận hành ứng dụng. Hệ thống được thiết kế theo kiến trúc modular, dễ mở rộng và bảo trì.

## Kiến trúc Command System

### 1. <PERSON><PERSON><PERSON> trúc thư mục

```
blog-api-v3/
├── Makefile                 # Command orchestration
├── cmd/                     # CLI applications
│   ├── server/             # Main API server
│   │   └── main.go
│   ├── migrate/            # Database migration tool
│   │   └── main.go
│   ├── seed/               # Database seeding tool
│   │   └── main.go
│   └── user/               # User management (proposed)
│       └── main.go
├── scripts/                # Helper scripts
│   ├── setup.sh
│   ├── test.sh
│   └── deploy.sh
└── bin/                    # Compiled binaries
    ├── server
    ├── migrate
    └── seed
```

### 2. Command Categories

#### Development Commands
- Khởi chạy server development
- Hot reload với Air
- Build application
- Code formatting và linting

#### Database Commands
- Migration management
- Data seeding
- Database reset
- Schema versioning

#### Testing Commands
- Unit tests
- Integration tests
- E2E tests
- Coverage reports

#### Deployment Commands
- Docker build
- Environment setup
- Health checks

## Command Interface Design

### 1. Makefile as Primary Interface

```makefile
# Pattern chung
.PHONY: command-name
command-name:
    @echo "Running command..."
    @go run ./cmd/tool/main.go $(ARGS)
```

### 2. Go CLI Tool Pattern

```go
// Standard structure cho CLI tools
package main

import (
    "flag"
    "fmt"
    "os"
)

func main() {
    // Parse command
    command := flag.String("command", "", "Command to execute")
    // Parse flags
    flag.Parse()
    
    // Execute command
    switch *command {
    case "up":
        runUp()
    case "down":
        runDown()
    default:
        printUsage()
    }
}
```

### 3. Environment Configuration

```bash
# .env file integration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=blog_api_v3

# Command sẽ tự động load .env
make migrate-up
```

## Command Implementation Guidelines

### 1. Command Structure

```go
// internal/cli/command.go
type Command interface {
    Name() string
    Description() string
    Execute(args []string) error
}

type BaseCommand struct {
    name        string
    description string
    flags       *flag.FlagSet
}
```

### 2. Flag Handling

```go
// Consistent flag patterns
var (
    verbose = flag.Bool("v", false, "Verbose output")
    dryRun  = flag.Bool("dry-run", false, "Simulate execution")
    force   = flag.Bool("f", false, "Force execution")
)
```

### 3. Error Handling

```go
// Standard error handling
if err != nil {
    fmt.Fprintf(os.Stderr, "Error: %v\n", err)
    os.Exit(1)
}
```

## Existing CLI Tools

### 1. Migration Tool (`cmd/migrate`)

**Purpose**: Quản lý database schema migrations

**Commands**:
- `up` - Chạy pending migrations
- `down` - Rollback migrations
- `status` - Xem migration status
- `version` - Xem current version

**Flags**:
- `-module` - Target specific module
- `-steps` - Number of migrations to run
- `-verbose` - Detailed output

### 2. Seeder Tool (`cmd/seed`)

**Purpose**: Populate database với test data

**Commands**:
- `run` - Chạy seeders
- `rollback` - Rollback seeded data
- `list` - List available seeders

**Flags**:
- `-seeders` - Specific seeders to run
- `-verbose` - Detailed output

### 3. Server Tool (`cmd/server`)

**Purpose**: Main API server

**Flags**:
- `-port` - Server port (default: 8080)
- `-env` - Environment (dev/prod)
- `-config` - Config file path

## Creating New Commands

### 1. Command Template

```go
// cmd/newcommand/main.go
package main

import (
    "flag"
    "fmt"
    "log"
    "os"
    
    "github.com/tranthanhloi/wn-api-v3/internal/config"
    "github.com/tranthanhloi/wn-api-v3/internal/database"
)

func main() {
    // Define commands
    if len(os.Args) < 2 {
        printUsage()
        os.Exit(1)
    }
    
    command := os.Args[1]
    
    // Setup flags for each command
    switch command {
    case "create":
        createCmd := flag.NewFlagSet("create", flag.ExitOnError)
        // Add flags
        createCmd.Parse(os.Args[2:])
        runCreate(createCmd)
    default:
        printUsage()
        os.Exit(1)
    }
}
```

### 2. Makefile Integration

```makefile
# Add to Makefile
.PHONY: newcommand
newcommand:
    @go run ./cmd/newcommand/main.go $(filter-out $@,$(MAKECMDGOALS))

# Allow arguments
%:
    @:
```

### 3. Testing Commands

```go
// cmd/newcommand/main_test.go
func TestCommand(t *testing.T) {
    // Test command execution
    cmd := exec.Command("go", "run", "main.go", "create")
    output, err := cmd.CombinedOutput()
    
    assert.NoError(t, err)
    assert.Contains(t, string(output), "Success")
}
```

## Command Best Practices

### 1. User Experience

- **Clear help text**: Mỗi command phải có description rõ ràng
- **Consistent flags**: Sử dụng flag names thống nhất
- **Progress feedback**: Hiển thị progress cho long-running tasks
- **Colored output**: Sử dụng colors cho success/error messages

### 2. Error Handling

- **Descriptive errors**: Cung cấp context và solutions
- **Exit codes**: Sử dụng proper exit codes (0=success, 1=error)
- **Validation**: Validate inputs trước khi execute
- **Rollback**: Cung cấp rollback cho destructive operations

### 3. Security

- **Input sanitization**: Clean user inputs
- **Permission checks**: Verify user permissions
- **Audit logging**: Log command executions
- **Secrets handling**: Never log sensitive data

### 4. Performance

- **Lazy loading**: Load resources khi cần
- **Concurrent execution**: Sử dụng goroutines cho parallel tasks
- **Progress indication**: Show progress cho long operations
- **Resource cleanup**: Đảm bảo cleanup resources

## Command Documentation Standards

### 1. In-Code Documentation

```go
// Command represents a CLI command
// Usage: command [flags] [arguments]
// Example: migrate up --module user
type Command struct {
    // Command name as used in CLI
    Name string
    
    // Brief description for help text
    Description string
    
    // Detailed usage instructions
    Usage string
    
    // Available flags
    Flags []Flag
}
```

### 2. Help Text Format

```
Usage: tool command [flags] [arguments]

Commands:
  create    Create a new resource
  list      List existing resources
  update    Update a resource
  delete    Delete a resource

Flags:
  -v, --verbose     Enable verbose output
  -f, --force       Force operation without confirmation
  --dry-run         Simulate execution without changes

Examples:
  tool create --name "example"
  tool list --filter "active"
  tool delete --id 123 --force
```

### 3. Man Page Style Docs

```markdown
# COMMAND(1) User Manual

## NAME
command - brief description

## SYNOPSIS
command [OPTIONS] ARGUMENTS

## DESCRIPTION
Detailed description of what the command does

## OPTIONS
-v, --verbose
    Enable verbose output
    
-f, --force
    Force operation

## EXAMPLES
Basic usage:
    command argument

With options:
    command -v --force argument

## SEE ALSO
related-command(1), other-command(1)
```

## Integration with CI/CD

### 1. GitHub Actions

```yaml
# .github/workflows/commands.yml
name: CLI Commands Test

on: [push, pull_request]

jobs:
  test-commands:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Test migrations
        run: make migrate-status
        
      - name: Test build
        run: make build
        
      - name: Test commands
        run: |
          ./bin/migrate -command version
          ./bin/seed -command list
```

### 2. Docker Integration

```dockerfile
# Dockerfile for CLI tools
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY . .

# Build all CLI tools
RUN go build -o bin/server ./cmd/server
RUN go build -o bin/migrate ./cmd/migrate
RUN go build -o bin/seed ./cmd/seed

# Runtime image
FROM alpine:latest
COPY --from=builder /app/bin/* /usr/local/bin/
```

### 3. Deployment Scripts

```bash
#!/bin/bash
# scripts/deploy.sh

# Run migrations
echo "Running migrations..."
./bin/migrate -command up

# Run seeders for staging
if [ "$ENV" = "staging" ]; then
    echo "Seeding test data..."
    ./bin/seed -command run -seeders test
fi

# Start server
echo "Starting server..."
./bin/server -env $ENV
```

## Troubleshooting Commands

### Common Issues

1. **Command not found**
   ```bash
   # Solution: Build binaries first
   make build
   ```

2. **Database connection errors**
   ```bash
   # Check .env file
   cat .env
   # Test connection
   make migrate-status
   ```

3. **Permission denied**
   ```bash
   # Make scripts executable
   chmod +x scripts/*.sh
   ```

4. **Flag parsing errors**
   ```bash
   # Use -- to separate flags
   make command -- --flag value
   ```

## Future Command Enhancements

### 1. Planned Commands

- **User Management CLI**: Create, update, delete users
- **Tenant CLI**: Manage tenants and settings
- **Cache CLI**: Clear and manage caches
- **Queue CLI**: Monitor and manage job queues
- **Backup CLI**: Database backup and restore

### 2. Command Framework

```go
// Proposed unified command framework
type CommandRegistry struct {
    commands map[string]Command
}

func (r *CommandRegistry) Register(cmd Command) {
    r.commands[cmd.Name()] = cmd
}

func (r *CommandRegistry) Execute(name string, args []string) error {
    cmd, exists := r.commands[name]
    if !exists {
        return fmt.Errorf("command not found: %s", name)
    }
    return cmd.Execute(args)
}
```

### 3. Interactive Mode

```go
// Future: Interactive CLI
func runInteractive() {
    reader := bufio.NewReader(os.Stdin)
    for {
        fmt.Print("> ")
        input, _ := reader.ReadString('\n')
        
        args := strings.Fields(input)
        if len(args) == 0 {
            continue
        }
        
        executeCommand(args[0], args[1:])
    }
}
```