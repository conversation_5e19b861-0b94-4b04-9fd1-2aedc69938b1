# Admin Quick Start Guide - Creating Your First Admin User

## Current Situation

The project doesn't have a dedicated CLI tool for user management yet. Here are the current methods to create an admin user for login.

## Method 1: Using API Registration + Database Update (Recommended)

### Step 1: Register a User via API

```bash
# Register a new user
curl -X POST http://localhost:9000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "admin",
    "password": "YourSecurePassword123!",
    "full_name": "System Administrator"
  }'
```

### Step 2: Update User Role in Database

```sql
-- Connect to MySQL
mysql -u root -p blog_api_v3

-- Find the user ID
SELECT id, email, username FROM users WHERE email = '<EMAIL>';

-- Assuming user_id is 1 and tenant_id is 1
-- Add user to tenant with owner role
INSERT INTO tenant_memberships (user_id, tenant_id, role, status, is_primary, joined_at)
VALUES (1, 1, 'owner', 'active', true, NOW());

-- Assign admin role (assuming role_id 1 is admin)
INSERT INTO rbac_user_roles (tenant_id, user_id, role_id, assigned_at, assigned_by)
VALUES (1, 1, 1, NOW(), 1);
```

### Step 3: Login

```bash
# Login with the created admin user
curl -X POST http://localhost:9000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "YourSecurePassword123!"
  }'
```

## Method 2: Direct Database Insert

### Generate Password Hash

First, generate a bcrypt hash for your password:

```go
// Use this Go code to generate password hash
package main

import (
    "fmt"
    "golang.org/x/crypto/bcrypt"
)

func main() {
    password := "YourSecurePassword123!"
    hash, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    fmt.Println(string(hash))
}
```

Or use an online bcrypt generator (not recommended for production).

### Insert Admin User

```sql
-- Insert user directly
INSERT INTO users (
    email, 
    username, 
    password_hash, 
    status, 
    email_verified,
    email_verified_at,
    created_at, 
    updated_at
) VALUES (
    '<EMAIL>',
    'admin',
    '$2a$10$YourGeneratedHashHere', -- Replace with actual hash
    'active',
    true,
    NOW(),
    NOW(),
    NOW()
);

-- Get the inserted user ID
SET @user_id = LAST_INSERT_ID();

-- Add to default tenant as owner
INSERT INTO tenant_memberships (
    user_id, 
    tenant_id, 
    role, 
    status, 
    is_primary, 
    joined_at
) VALUES (
    @user_id,
    1, -- Default tenant
    'owner',
    'active',
    true,
    NOW()
);

-- Assign admin role
INSERT INTO rbac_user_roles (
    tenant_id,
    user_id, 
    role_id,
    assigned_at,
    assigned_by
) VALUES (
    1,
    @user_id,
    1, -- Assuming role_id 1 is admin
    NOW(),
    @user_id
);
```

## Method 3: Using Seeder (Development Only)

### Step 1: Modify User Seeder

Edit `internal/database/seeders/user/user_seeder.go`:

```go
var users = []userData{
    {
        Email:          "<EMAIL>",
        Username:       "admin",
        Password:       "admin123", // Will be hashed
        EmailVerified:  true,
        Status:         "active",
    },
    // ... other test users
}
```

### Step 2: Run Seeder

```bash
# Run all seeders
make seed-run

# Or run only user seeder
make seed-run SEEDERS=user
```

## Method 4: Quick SQL Script

Create a file `scripts/create-admin.sql`:

```sql
-- Create Admin User Script
-- Password: Admin123!

-- Create user
INSERT INTO users (
    email, 
    username, 
    password_hash, 
    status, 
    email_verified,
    email_verified_at,
    created_at, 
    updated_at
) VALUES (
    '<EMAIL>',
    'admin',
    '$2a$10$Jvhm7bPsGQzLZ9p8kJpqNuK5mJ5KfYx4O5zU1DNYoJ3kRzQ3qVFDm', -- Password: Admin123!
    'active',
    true,
    NOW(),
    NOW(),
    NOW()
);

SET @user_id = LAST_INSERT_ID();

-- Add to tenant
INSERT INTO tenant_memberships (
    user_id, 
    tenant_id, 
    role, 
    status, 
    is_primary, 
    joined_at
) VALUES (
    @user_id,
    1,
    'owner',
    'active',
    true,
    NOW()
);

-- Assign admin role
INSERT INTO rbac_user_roles (
    tenant_id,
    user_id, 
    role_id,
    assigned_at,
    assigned_by
) VALUES (
    1,
    @user_id,
    1,
    NOW(),
    @user_id
);

-- Verify
SELECT u.id, u.email, u.username, tm.role as tenant_role, r.name as rbac_role
FROM users u
JOIN tenant_memberships tm ON u.id = tm.user_id
LEFT JOIN rbac_user_roles ur ON u.id = ur.user_id
LEFT JOIN rbac_roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';
```

Run the script:

```bash
mysql -u root -p blog_api_v3 < scripts/create-admin.sql
```

## Verification

### Check User Exists

```sql
-- Check user and roles
SELECT 
    u.id,
    u.email,
    u.username,
    u.status,
    tm.tenant_id,
    tm.role as membership_role,
    r.name as rbac_role
FROM users u
LEFT JOIN tenant_memberships tm ON u.id = tm.user_id
LEFT JOIN rbac_user_roles ur ON u.id = ur.user_id AND ur.tenant_id = tm.tenant_id
LEFT JOIN rbac_roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';
```

### Test Login

```bash
# Test login via API
curl -X POST http://localhost:9000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "YourPassword"
  }'

# Should return access token and user info
```

## Common Issues

### 1. Email Verification Required

If email verification is enabled:

```sql
-- Force verify email
UPDATE users 
SET email_verified = true, 
    email_verified_at = NOW() 
WHERE email = '<EMAIL>';
```

### 2. No Tenant Assignment

```sql
-- Check tenant membership
SELECT * FROM tenant_memberships WHERE user_id = YOUR_USER_ID;

-- If missing, add it
INSERT INTO tenant_memberships (user_id, tenant_id, role, status, is_primary, joined_at)
VALUES (YOUR_USER_ID, 1, 'owner', 'active', true, NOW());
```

### 3. No RBAC Role

```sql
-- Check RBAC roles
SELECT * FROM rbac_roles;

-- Check user roles
SELECT * FROM rbac_user_roles WHERE user_id = YOUR_USER_ID;

-- Assign admin role
INSERT INTO rbac_user_roles (tenant_id, user_id, role_id, assigned_at, assigned_by)
VALUES (1, YOUR_USER_ID, 1, NOW(), YOUR_USER_ID);
```

## Best Practices

1. **Use Strong Passwords**: Always use secure passwords for admin accounts
2. **Change Default Passwords**: If using seeders, change passwords immediately
3. **Enable 2FA**: Enable two-factor authentication for admin accounts
4. **Audit Trail**: Keep track of who creates admin accounts
5. **Limit Admin Users**: Only create admin accounts when necessary

## Future CLI Tool

A dedicated CLI tool is planned for user management:

```bash
# Future commands (not yet implemented)
./bin/user create --email <EMAIL> --role admin
./bin/user list --role admin
./bin/<NAME_EMAIL> --password
./bin/<NAME_EMAIL>
```

See [User Management CLI Documentation](./user-management.md) for the proposed design.