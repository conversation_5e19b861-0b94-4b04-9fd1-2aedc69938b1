# Best Practices Guide

## Overview

This guide provides comprehensive best practices for implementing and maintaining distributed tracing in the WN API v3 project.

## Span Design Principles

### 1. Span Naming Conventions

Use consistent, meaningful span names that follow semantic conventions:

```go
// ✅ Good: Descriptive and consistent
"http.request.GET /api/v1/tenants"
"db.query.SELECT tenants"
"service.tenant.create"
"external.email.send"

// ❌ Bad: Vague or inconsistent
"request"
"query"
"operation"
"call"
```

**Recommended Patterns:**
```go
// HTTP operations
"http.{method} {route}"

// Database operations
"db.{operation} {table}"

// Service operations
"service.{service_name}.{method}"

// External services
"external.{service_name}.{operation}"
```

### 2. Span Hierarchy

Design clear parent-child relationships:

```go
func ProcessTenantCreation(ctx context.Context, req *CreateTenantRequest) error {
    // Root span for the operation
    ctx, span := tracing.StartSpan(ctx, "tenant.create")
    defer span.End()
    
    // Child span for validation
    if err := s.validateTenant(ctx, req); err != nil {
        return err
    }
    
    // Child span for persistence
    if err := s.saveTenant(ctx, req); err != nil {
        return err
    }
    
    // Child span for notifications
    if err := s.sendNotifications(ctx, req); err != nil {
        return err
    }
    
    return nil
}

func (s *TenantService) validateTenant(ctx context.Context, req *CreateTenantRequest) error {
    ctx, span := tracing.StartSpan(ctx, "tenant.validate")
    defer span.End()
    
    // Validation logic
    return nil
}
```

### 3. Attribute Guidelines

Use semantic attributes effectively:

```go
// ✅ Good: Semantic and structured attributes
span.SetAttributes(
    attribute.String("tenant.id", tenantID),
    attribute.String("tenant.name", tenantName),
    attribute.String("tenant.plan", planType),
    attribute.Int("tenant.user_count", userCount),
    attribute.Bool("tenant.is_trial", isTrial),
)

// ❌ Bad: Unstructured or non-semantic attributes
span.SetAttributes(
    attribute.String("id", tenantID),
    attribute.String("data", jsonData),
    attribute.String("info", "some info"),
)
```

**Attribute Categories:**
- **Identity**: `tenant.id`, `user.id`, `session.id`
- **Classification**: `tenant.plan`, `user.role`, `request.type`
- **Metrics**: `response.size`, `duration.ms`, `retry.count`
- **Status**: `operation.status`, `validation.passed`, `auth.verified`

## Error Handling

### 1. Error Recording

Record errors with context and structured information:

```go
func (s *TenantService) CreateTenant(ctx context.Context, req *CreateTenantRequest) (*models.Tenant, error) {
    ctx, span := tracing.StartSpan(ctx, "tenant.create")
    defer span.End()
    
    // Validate input
    if err := s.validator.Validate(req); err != nil {
        // Record validation error
        span.RecordError(err)
        span.SetStatus(codes.Error, "validation failed")
        span.SetAttributes(
            attribute.String("error.type", "validation"),
            attribute.String("error.field", getErrorField(err)),
            attribute.String("error.message", err.Error()),
        )
        return nil, err
    }
    
    // Create tenant
    tenant, err := s.repository.Create(ctx, req)
    if err != nil {
        // Record repository error
        span.RecordError(err)
        span.SetStatus(codes.Error, "database operation failed")
        span.SetAttributes(
            attribute.String("error.type", "database"),
            attribute.String("error.operation", "create"),
            attribute.String("error.table", "tenants"),
        )
        return nil, err
    }
    
    // Set success attributes
    span.SetAttributes(
        attribute.String("tenant.id", tenant.ID),
        attribute.String("operation.status", "success"),
    )
    
    return tenant, nil
}
```

### 2. Error Categories

Categorize errors for better analysis:

```go
// Error type constants
const (
    ErrorTypeValidation = "validation"
    ErrorTypeDatabase   = "database"
    ErrorTypeExternal   = "external"
    ErrorTypeAuth       = "authentication"
    ErrorTypeRateLimit  = "rate_limit"
    ErrorTypeTimeout    = "timeout"
    ErrorTypeBusiness   = "business_logic"
)

// Error recording helper
func RecordError(span trace.Span, err error, errorType string) {
    span.RecordError(err)
    span.SetStatus(codes.Error, err.Error())
    span.SetAttributes(
        attribute.String("error.type", errorType),
        attribute.String("error.message", err.Error()),
        attribute.Bool("error.retryable", isRetryable(err)),
    )
}

// Usage
if err := s.repository.Create(ctx, req); err != nil {
    RecordError(span, err, ErrorTypeDatabase)
    return nil, err
}
```

## Performance Optimization

### 1. Efficient Span Creation

Create spans only when necessary:

```go
// ✅ Good: Conditional span creation
func (s *Service) ProcessRequest(ctx context.Context, req *Request) error {
    // Only create span if tracing is enabled and request is traced
    if shouldTrace(ctx, req) {
        ctx, span := tracing.StartSpan(ctx, "service.process_request")
        defer span.End()
    }
    
    return s.doProcessing(ctx, req)
}

func shouldTrace(ctx context.Context, req *Request) bool {
    // Check if context has active span
    if !trace.SpanFromContext(ctx).IsRecording() {
        return false
    }
    
    // Skip tracing for health checks
    if req.IsHealthCheck() {
        return false
    }
    
    return true
}
```

### 2. Lazy Attribute Setting

Set attributes only when span is recording:

```go
func (s *Service) ProcessTenant(ctx context.Context, tenant *models.Tenant) error {
    ctx, span := tracing.StartSpan(ctx, "service.process_tenant")
    defer span.End()
    
    // Only set attributes if span is recording
    if span.IsRecording() {
        span.SetAttributes(
            attribute.String("tenant.id", tenant.ID),
            attribute.String("tenant.name", tenant.Name),
            attribute.String("tenant.plan", tenant.PlanID),
        )
    }
    
    return s.doProcessing(ctx, tenant)
}
```

### 3. Batch Operations

Handle batch operations efficiently:

```go
func (s *Service) ProcessBatch(ctx context.Context, items []*Item) error {
    ctx, span := tracing.StartSpan(ctx, "service.process_batch")
    defer span.End()
    
    span.SetAttributes(
        attribute.Int("batch.size", len(items)),
        attribute.String("batch.type", "tenant_creation"),
    )
    
    successCount := 0
    errorCount := 0
    
    for i, item := range items {
        // Create child span for each item
        itemCtx, itemSpan := tracing.StartSpan(ctx, "service.process_item")
        
        if err := s.processItem(itemCtx, item); err != nil {
            errorCount++
            itemSpan.RecordError(err)
            itemSpan.SetStatus(codes.Error, "item processing failed")
        } else {
            successCount++
        }
        
        itemSpan.SetAttributes(
            attribute.Int("batch.item_index", i),
            attribute.String("item.id", item.ID),
        )
        itemSpan.End()
    }
    
    // Set batch results
    span.SetAttributes(
        attribute.Int("batch.success_count", successCount),
        attribute.Int("batch.error_count", errorCount),
        attribute.Float64("batch.success_rate", float64(successCount)/float64(len(items))),
    )
    
    return nil
}
```

## Security Considerations

### 1. Sensitive Data Handling

Never include sensitive data in traces:

```go
// ✅ Good: Sanitized attributes
func (s *AuthService) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
    ctx, span := tracing.StartSpan(ctx, "auth.login")
    defer span.End()
    
    // Safe attributes only
    span.SetAttributes(
        attribute.String("auth.username", req.Username),
        attribute.String("auth.method", "password"),
        attribute.String("auth.client_ip", getClientIP(ctx)),
        // ❌ NEVER: attribute.String("auth.password", req.Password),
    )
    
    // ... authentication logic ...
}

// ❌ Bad: Exposing sensitive data
span.SetAttributes(
    attribute.String("user.password", password),
    attribute.String("user.ssn", ssn),
    attribute.String("payment.card_number", cardNumber),
)
```

### 2. Data Sanitization

Implement data sanitization for traces:

```go
// Sanitization helper
func SanitizeForTracing(data map[string]interface{}) map[string]interface{} {
    sanitized := make(map[string]interface{})
    
    sensitiveFields := map[string]bool{
        "password":     true,
        "token":        true,
        "secret":       true,
        "key":          true,
        "card_number":  true,
        "ssn":          true,
        "credit_card":  true,
    }
    
    for key, value := range data {
        if sensitiveFields[strings.ToLower(key)] {
            sanitized[key] = "[REDACTED]"
        } else {
            sanitized[key] = value
        }
    }
    
    return sanitized
}

// Usage
func (s *UserService) UpdateProfile(ctx context.Context, userID string, data map[string]interface{}) error {
    ctx, span := tracing.StartSpan(ctx, "user.update_profile")
    defer span.End()
    
    // Sanitize data before adding to span
    sanitized := SanitizeForTracing(data)
    span.SetAttributes(
        attribute.String("user.id", userID),
        attribute.String("update.fields", fmt.Sprintf("%v", sanitized)),
    )
    
    return s.repository.UpdateProfile(ctx, userID, data)
}
```

### 3. Access Control

Implement access control for trace data:

```go
// Trace access control
func (s *Service) ProcessAdminRequest(ctx context.Context, req *AdminRequest) error {
    ctx, span := tracing.StartSpan(ctx, "admin.process_request")
    defer span.End()
    
    // Add admin-specific attributes only if user has admin role
    if hasAdminRole(ctx) {
        span.SetAttributes(
            attribute.String("admin.operation", req.Operation),
            attribute.String("admin.target", req.Target),
            attribute.String("admin.requester", req.RequesterID),
        )
    } else {
        span.SetAttributes(
            attribute.String("operation.type", "admin"),
            attribute.Bool("operation.restricted", true),
        )
    }
    
    return s.executeAdminOperation(ctx, req)
}
```

## Multi-Tenant Considerations

### 1. Tenant Isolation

Ensure proper tenant isolation in traces:

```go
// Tenant context propagation
func WithTenantContext(ctx context.Context, tenantID string) context.Context {
    // Add tenant to context
    ctx = context.WithValue(ctx, "tenant_id", tenantID)
    
    // Add tenant to all spans in this context
    span := trace.SpanFromContext(ctx)
    if span.IsRecording() {
        span.SetAttributes(
            attribute.String("tenant.id", tenantID),
            attribute.String("tenant.isolation", "enabled"),
        )
    }
    
    return ctx
}

// Middleware for tenant isolation
func TenantTracingMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        tenantID := extractTenantID(c)
        if tenantID != "" {
            ctx := WithTenantContext(c.Request.Context(), tenantID)
            c.Request = c.Request.WithContext(ctx)
        }
        
        c.Next()
    }
}
```

### 2. Cross-Tenant Operations

Handle cross-tenant operations carefully:

```go
func (s *AdminService) CrossTenantOperation(ctx context.Context, sourceTenantID, targetTenantID string) error {
    ctx, span := tracing.StartSpan(ctx, "admin.cross_tenant_operation")
    defer span.End()
    
    // Mark as cross-tenant operation
    span.SetAttributes(
        attribute.String("operation.type", "cross_tenant"),
        attribute.String("source.tenant_id", sourceTenantID),
        attribute.String("target.tenant_id", targetTenantID),
        attribute.Bool("operation.requires_audit", true),
    )
    
    // Process each tenant separately
    if err := s.processSourceTenant(ctx, sourceTenantID); err != nil {
        return err
    }
    
    if err := s.processTargetTenant(ctx, targetTenantID); err != nil {
        return err
    }
    
    return nil
}
```

## Testing Strategies

### 1. Unit Testing with Traces

Test tracing in unit tests:

```go
func TestTenantService_CreateTenant(t *testing.T) {
    // Setup test tracer
    recorder := tracetest.NewSpanRecorder()
    provider := trace.NewTracerProvider(trace.WithSpanProcessor(recorder))
    
    ctx := context.Background()
    tracer := provider.Tracer("test")
    
    // Create test span
    ctx, span := tracer.Start(ctx, "test.create_tenant")
    defer span.End()
    
    // Test service method
    service := NewTenantService(mockRepo)
    tenant, err := service.CreateTenant(ctx, &CreateTenantRequest{
        Name: "Test Tenant",
    })
    
    assert.NoError(t, err)
    assert.NotNil(t, tenant)
    
    // Verify spans were created
    spans := recorder.Ended()
    assert.Len(t, spans, 2) // Parent + child spans
    
    // Verify span attributes
    childSpan := spans[1]
    assert.Equal(t, "tenant.create", childSpan.Name())
    
    attributes := childSpan.Attributes()
    assert.Equal(t, "Test Tenant", getAttributeValue(attributes, "tenant.name"))
}
```

### 2. Integration Testing

Test end-to-end tracing:

```go
func TestE2E_TenantCreationTrace(t *testing.T) {
    // Setup test environment
    testServer := setupTestServer()
    defer testServer.Close()
    
    // Create HTTP client with tracing
    client := &http.Client{
        Transport: otelhttp.NewTransport(http.DefaultTransport),
    }
    
    // Create trace context
    ctx, span := tracer.Start(context.Background(), "test.e2e_tenant_creation")
    defer span.End()
    
    // Make HTTP request
    req, _ := http.NewRequestWithContext(ctx, "POST", testServer.URL+"/api/v1/tenants", bytes.NewBuffer(requestBody))
    resp, err := client.Do(req)
    
    assert.NoError(t, err)
    assert.Equal(t, 201, resp.StatusCode)
    
    // Verify trace was created
    traceID := span.SpanContext().TraceID()
    assert.NotEmpty(t, traceID)
    
    // Wait for trace to be exported
    time.Sleep(2 * time.Second)
    
    // Verify trace in backend
    trace, err := jaegerClient.GetTrace(traceID.String())
    assert.NoError(t, err)
    assert.NotNil(t, trace)
}
```

## Monitoring and Alerting

### 1. Key Metrics to Monitor

Track essential tracing metrics:

```go
// Tracing metrics
type TracingMetrics struct {
    // Span metrics
    SpansCreated    prometheus.Counter
    SpansExported   prometheus.Counter
    SpansDropped    prometheus.Counter
    
    // Export metrics
    ExportDuration  prometheus.Histogram
    ExportErrors    prometheus.Counter
    ExportBatchSize prometheus.Histogram
    
    // Performance metrics
    MemoryUsage     prometheus.Gauge
    CPUOverhead     prometheus.Gauge
    NetworkBandwidth prometheus.Gauge
}

func NewTracingMetrics() *TracingMetrics {
    return &TracingMetrics{
        SpansCreated: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "tracing_spans_created_total",
            Help: "Total number of spans created",
        }),
        SpansExported: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "tracing_spans_exported_total",
            Help: "Total number of spans exported",
        }),
        ExportDuration: prometheus.NewHistogram(prometheus.HistogramOpts{
            Name: "tracing_export_duration_seconds",
            Help: "Time spent exporting spans",
            Buckets: prometheus.DefBuckets,
        }),
    }
}
```

### 2. Health Checks

Implement comprehensive health checks:

```go
func TracingHealthCheck() gin.HandlerFunc {
    return func(c *gin.Context) {
        ctx := c.Request.Context()
        
        health := gin.H{
            "status": "healthy",
            "checks": make(map[string]interface{}),
        }
        
        // Test span creation
        ctx, span := tracer.Start(ctx, "health.span_creation")
        span.End()
        health["checks"]["span_creation"] = "ok"
        
        // Test context propagation
        childCtx, childSpan := tracer.Start(ctx, "health.context_propagation")
        childSpan.End()
        
        if childSpan.SpanContext().TraceID() == span.SpanContext().TraceID() {
            health["checks"]["context_propagation"] = "ok"
        } else {
            health["checks"]["context_propagation"] = "failed"
            health["status"] = "unhealthy"
        }
        
        // Test export
        if err := testExport(ctx); err != nil {
            health["checks"]["export"] = "failed: " + err.Error()
            health["status"] = "unhealthy"
        } else {
            health["checks"]["export"] = "ok"
        }
        
        // Add metrics
        metrics := GetTracingMetrics()
        health["metrics"] = metrics
        
        if health["status"] == "healthy" {
            c.JSON(200, health)
        } else {
            c.JSON(500, health)
        }
    }
}
```

## Documentation Standards

### 1. Trace Documentation

Document your tracing implementation:

```go
// TenantService provides tenant management operations with distributed tracing.
//
// Tracing Details:
// - Operation spans: tenant.create, tenant.update, tenant.delete
// - Attributes: tenant.id, tenant.name, tenant.plan, tenant.status
// - Error conditions: validation errors, database errors, external service errors
//
// Example trace structure:
//   http.request.POST /api/v1/tenants
//   ├── tenant.create
//   │   ├── tenant.validate
//   │   ├── db.insert tenants
//   │   └── external.notification.send
//   └── http.response.201
type TenantService struct {
    repo         TenantRepository
    validator    TenantValidator
    notification NotificationService
}
```

### 2. Span Documentation

Document span semantics:

```go
// CreateTenant creates a new tenant with full distributed tracing support.
//
// Span: tenant.create
// Attributes:
//   - tenant.name: The tenant name
//   - tenant.domain: The tenant domain
//   - tenant.plan: The subscription plan
//   - tenant.trial: Whether this is a trial tenant
//
// Child Spans:
//   - tenant.validate: Input validation
//   - db.insert tenants: Database insertion
//   - external.notification.send: Notification sending
//
// Error Conditions:
//   - ValidationError: Invalid input data
//   - DatabaseError: Database operation failed
//   - NotificationError: Notification failed (non-blocking)
func (s *TenantService) CreateTenant(ctx context.Context, req *CreateTenantRequest) (*models.Tenant, error) {
    ctx, span := tracing.StartSpan(ctx, "tenant.create")
    defer span.End()
    
    // Implementation...
}
```

## Migration and Rollback

### 1. Gradual Rollout

Implement tracing gradually:

```go
// Feature flag for tracing
type TracingFlags struct {
    HTTPTracing      bool
    DatabaseTracing  bool
    ExternalTracing  bool
    ServiceTracing   bool
    SamplingRate     float64
}

func GetTracingFlags() *TracingFlags {
    return &TracingFlags{
        HTTPTracing:     getBoolEnv("TRACING_HTTP_ENABLED", false),
        DatabaseTracing: getBoolEnv("TRACING_DATABASE_ENABLED", false),
        ExternalTracing: getBoolEnv("TRACING_EXTERNAL_ENABLED", false),
        ServiceTracing:  getBoolEnv("TRACING_SERVICE_ENABLED", false),
        SamplingRate:    getFloatEnv("TRACING_SAMPLING_RATE", 0.1),
    }
}

// Conditional tracing middleware
func ConditionalTracingMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        flags := GetTracingFlags()
        
        if flags.HTTPTracing {
            // Apply tracing middleware
            otelgin.Middleware("wn-api-v3")(c)
        }
        
        c.Next()
    }
}
```

### 2. Safe Rollback

Implement safe rollback mechanisms:

```go
// Rollback mechanism
func RollbackTracing() {
    log.Println("Rolling back tracing configuration...")
    
    // Disable new span creation
    otel.SetTracerProvider(trace.NewNoopTracerProvider())
    
    // Flush existing spans
    if tp, ok := otel.GetTracerProvider().(*trace.TracerProvider); ok {
        ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
        defer cancel()
        
        if err := tp.ForceFlush(ctx); err != nil {
            log.Printf("Error flushing spans during rollback: %v", err)
        }
    }
    
    log.Println("Tracing rollback completed")
}
```

## Next Steps

1. Implement these practices incrementally
2. Monitor tracing performance and adjust as needed
3. Train team members on tracing best practices
4. Regular review and update of tracing configuration
5. Set up monitoring and alerting for tracing health

## References

- [OpenTelemetry Semantic Conventions](https://opentelemetry.io/docs/reference/specification/semantic_conventions/)
- [Distributed Tracing Best Practices](https://opentelemetry.io/docs/concepts/observability-primer/)
- [Jaeger Best Practices](https://www.jaegertracing.io/docs/1.35/best-practices/)