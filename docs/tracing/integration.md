# Tracing Integration Guide

## Overview

This guide provides detailed instructions for integrating distributed tracing across all modules in the WN API v3 project.

## HTTP Request Tracing

### Gin Middleware Integration

Create `pkg/tracing/middleware.go`:

```go
package tracing

import (
    "fmt"
    "strconv"
    "time"

    "github.com/gin-gonic/gin"
    "go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/codes"
    "go.opentelemetry.io/otel/trace"
)

// GinMiddleware returns a Gin middleware that traces HTTP requests
func GinMiddleware() gin.HandlerFunc {
    return otelgin.Middleware("wn-api-v3", 
        otelgin.WithFilter(func(req *gin.Request) bool {
            // Skip tracing for health check and metrics endpoints
            return req.URL.Path != "/health" && req.URL.Path != "/metrics"
        }),
    )
}

// CustomSpanMiddleware adds custom attributes to spans
func CustomSpanMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        span := trace.SpanFromContext(c.Request.Context())
        
        // Add custom attributes
        span.SetAttributes(
            attribute.String("http.user_agent", c.Request.UserAgent()),
            attribute.String("http.remote_addr", c.ClientIP()),
            attribute.String("tenant.id", getTenantID(c)),
            attribute.String("user.id", getUserID(c)),
        )
        
        // Set operation name
        span.SetName(fmt.Sprintf("%s %s", c.Request.Method, c.FullPath()))
        
        c.Next()
        
        // Add response attributes
        span.SetAttributes(
            attribute.Int("http.status_code", c.Writer.Status()),
            attribute.Int("http.response_size", c.Writer.Size()),
        )
        
        // Set span status based on HTTP status
        if c.Writer.Status() >= 400 {
            span.SetStatus(codes.Error, fmt.Sprintf("HTTP %d", c.Writer.Status()))
        }
    }
}

// getTenantID extracts tenant ID from context
func getTenantID(c *gin.Context) string {
    if tenantID, exists := c.Get("tenant_id"); exists {
        return fmt.Sprintf("%v", tenantID)
    }
    return ""
}

// getUserID extracts user ID from context
func getUserID(c *gin.Context) string {
    if userID, exists := c.Get("user_id"); exists {
        return fmt.Sprintf("%v", userID)
    }
    return ""
}
```

### Route Registration

Update your route registration:

```go
// In your main.go or route setup
func setupRoutes(router *gin.Engine) {
    // Apply tracing middleware
    router.Use(tracing.GinMiddleware())
    router.Use(tracing.CustomSpanMiddleware())
    
    // Your existing routes...
    api := router.Group("/api/v1")
    {
        // Module routes
        tenant.RegisterRoutes(api)
        auth.RegisterRoutes(api)
        user.RegisterRoutes(api)
        website.RegisterRoutes(api)
    }
}
```

## Database Tracing

### GORM Integration

Create `pkg/tracing/database.go`:

```go
package tracing

import (
    "context"
    "fmt"
    "time"

    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/codes"
    "go.opentelemetry.io/otel/trace"
    "gorm.io/gorm"
)

// DatabaseTracer provides database operation tracing
type DatabaseTracer struct {
    tracer trace.Tracer
}

// NewDatabaseTracer creates a new database tracer
func NewDatabaseTracer() *DatabaseTracer {
    return &DatabaseTracer{
        tracer: otel.Tracer("database"),
    }
}

// TraceQuery traces database queries
func (t *DatabaseTracer) TraceQuery(ctx context.Context, query string, args []interface{}) (context.Context, trace.Span) {
    ctx, span := t.tracer.Start(ctx, "db.query")
    
    span.SetAttributes(
        attribute.String("db.system", "mysql"),
        attribute.String("db.statement", query),
        attribute.Int("db.args_count", len(args)),
    )
    
    return ctx, span
}

// TraceTransaction traces database transactions
func (t *DatabaseTracer) TraceTransaction(ctx context.Context, operation string) (context.Context, trace.Span) {
    ctx, span := t.tracer.Start(ctx, fmt.Sprintf("db.transaction.%s", operation))
    
    span.SetAttributes(
        attribute.String("db.system", "mysql"),
        attribute.String("db.operation", operation),
    )
    
    return ctx, span
}

// GORM Plugin for automatic tracing
type GormTracer struct {
    tracer trace.Tracer
}

func NewGormTracer() *GormTracer {
    return &GormTracer{
        tracer: otel.Tracer("gorm"),
    }
}

func (gt *GormTracer) Name() string {
    return "gorm:tracing"
}

func (gt *GormTracer) Initialize(db *gorm.DB) error {
    // Before query
    db.Callback().Query().Before("gorm:query").Register("tracing:before_query", gt.beforeQuery)
    db.Callback().Create().Before("gorm:create").Register("tracing:before_create", gt.beforeCreate)
    db.Callback().Update().Before("gorm:update").Register("tracing:before_update", gt.beforeUpdate)
    db.Callback().Delete().Before("gorm:delete").Register("tracing:before_delete", gt.beforeDelete)
    
    // After query
    db.Callback().Query().After("gorm:query").Register("tracing:after_query", gt.afterQuery)
    db.Callback().Create().After("gorm:create").Register("tracing:after_create", gt.afterQuery)
    db.Callback().Update().After("gorm:update").Register("tracing:after_update", gt.afterQuery)
    db.Callback().Delete().After("gorm:delete").Register("tracing:after_delete", gt.afterQuery)
    
    return nil
}

func (gt *GormTracer) beforeQuery(db *gorm.DB) {
    ctx, span := gt.tracer.Start(db.Statement.Context, "gorm.query")
    
    span.SetAttributes(
        attribute.String("db.system", "mysql"),
        attribute.String("db.operation", "SELECT"),
        attribute.String("db.table", db.Statement.Table),
    )
    
    db.Statement.Context = ctx
    db.Set("tracing:span", span)
    db.Set("tracing:start_time", time.Now())
}

func (gt *GormTracer) beforeCreate(db *gorm.DB) {
    ctx, span := gt.tracer.Start(db.Statement.Context, "gorm.create")
    
    span.SetAttributes(
        attribute.String("db.system", "mysql"),
        attribute.String("db.operation", "INSERT"),
        attribute.String("db.table", db.Statement.Table),
    )
    
    db.Statement.Context = ctx
    db.Set("tracing:span", span)
    db.Set("tracing:start_time", time.Now())
}

func (gt *GormTracer) beforeUpdate(db *gorm.DB) {
    ctx, span := gt.tracer.Start(db.Statement.Context, "gorm.update")
    
    span.SetAttributes(
        attribute.String("db.system", "mysql"),
        attribute.String("db.operation", "UPDATE"),
        attribute.String("db.table", db.Statement.Table),
    )
    
    db.Statement.Context = ctx
    db.Set("tracing:span", span)
    db.Set("tracing:start_time", time.Now())
}

func (gt *GormTracer) beforeDelete(db *gorm.DB) {
    ctx, span := gt.tracer.Start(db.Statement.Context, "gorm.delete")
    
    span.SetAttributes(
        attribute.String("db.system", "mysql"),
        attribute.String("db.operation", "DELETE"),
        attribute.String("db.table", db.Statement.Table),
    )
    
    db.Statement.Context = ctx
    db.Set("tracing:span", span)
    db.Set("tracing:start_time", time.Now())
}

func (gt *GormTracer) afterQuery(db *gorm.DB) {
    span, exists := db.Get("tracing:span")
    if !exists {
        return
    }
    
    traceSpan := span.(trace.Span)
    defer traceSpan.End()
    
    // Calculate duration
    if startTime, exists := db.Get("tracing:start_time"); exists {
        duration := time.Since(startTime.(time.Time))
        traceSpan.SetAttributes(attribute.Int64("db.duration_ms", duration.Milliseconds()))
    }
    
    // Add result attributes
    traceSpan.SetAttributes(
        attribute.Int64("db.rows_affected", db.RowsAffected),
        attribute.String("db.sql", db.Dialector.Explain(db.Statement.SQL.String(), db.Statement.Vars...)),
    )
    
    // Handle errors
    if db.Error != nil {
        traceSpan.SetStatus(codes.Error, db.Error.Error())
        traceSpan.SetAttributes(attribute.String("db.error", db.Error.Error()))
    }
}
```

### Database Configuration

Update your database initialization:

```go
// In pkg/database/mysql.go
func NewMySQLConnection(config *Config) (*gorm.DB, error) {
    dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
        config.Username, config.Password, config.Host, config.Port, config.Database)
    
    db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    })
    if err != nil {
        return nil, err
    }
    
    // Add tracing plugin
    if err := db.Use(tracing.NewGormTracer()); err != nil {
        return nil, fmt.Errorf("failed to add tracing plugin: %w", err)
    }
    
    return db, nil
}
```

## Service Layer Tracing

### Service Integration

Create tracing utilities in `pkg/tracing/span.go`:

```go
package tracing

import (
    "context"
    "fmt"

    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/codes"
    "go.opentelemetry.io/otel/trace"
)

// StartSpan starts a new span with the given name
func StartSpan(ctx context.Context, name string, attributes ...attribute.KeyValue) (context.Context, trace.Span) {
    tracer := otel.Tracer("service")
    ctx, span := tracer.Start(ctx, name)
    
    if len(attributes) > 0 {
        span.SetAttributes(attributes...)
    }
    
    return ctx, span
}

// RecordError records an error on the span
func RecordError(span trace.Span, err error, description string) {
    span.RecordError(err)
    span.SetStatus(codes.Error, description)
    span.SetAttributes(attribute.String("error.message", err.Error()))
}

// AddAttributes adds multiple attributes to a span
func AddAttributes(span trace.Span, attributes ...attribute.KeyValue) {
    span.SetAttributes(attributes...)
}

// TraceServiceMethod is a helper to trace service methods
func TraceServiceMethod(ctx context.Context, serviceName, methodName string, attributes ...attribute.KeyValue) (context.Context, trace.Span) {
    spanName := fmt.Sprintf("%s.%s", serviceName, methodName)
    ctx, span := StartSpan(ctx, spanName, attributes...)
    
    span.SetAttributes(
        attribute.String("service.name", serviceName),
        attribute.String("service.method", methodName),
    )
    
    return ctx, span
}
```

### Example Service Implementation

Update your service implementations:

```go
// In internal/modules/tenant/services/tenant_service.go
package services

import (
    "context"
    "fmt"

    "go.opentelemetry.io/otel/attribute"
    "github.com/tranthanhloi/wn-api-v3/pkg/tracing"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

type TenantService struct {
    repo TenantRepository
}

func (s *TenantService) CreateTenant(ctx context.Context, req *CreateTenantRequest) (*models.Tenant, error) {
    // Start tracing
    ctx, span := tracing.TraceServiceMethod(ctx, "TenantService", "CreateTenant",
        attribute.String("tenant.name", req.Name),
        attribute.String("tenant.domain", req.Domain),
    )
    defer span.End()
    
    // Validate request
    if err := s.validateCreateRequest(req); err != nil {
        tracing.RecordError(span, err, "validation failed")
        return nil, err
    }
    
    // Create tenant
    tenant, err := s.repo.Create(ctx, req)
    if err != nil {
        tracing.RecordError(span, err, "failed to create tenant")
        return nil, err
    }
    
    // Add success attributes
    span.SetAttributes(
        attribute.String("tenant.id", tenant.ID),
        attribute.String("tenant.status", string(tenant.Status)),
    )
    
    return tenant, nil
}

func (s *TenantService) GetTenant(ctx context.Context, id string) (*models.Tenant, error) {
    ctx, span := tracing.TraceServiceMethod(ctx, "TenantService", "GetTenant",
        attribute.String("tenant.id", id),
    )
    defer span.End()
    
    tenant, err := s.repo.GetByID(ctx, id)
    if err != nil {
        tracing.RecordError(span, err, "failed to get tenant")
        return nil, err
    }
    
    span.SetAttributes(
        attribute.String("tenant.name", tenant.Name),
        attribute.String("tenant.domain", tenant.Domain),
        attribute.String("tenant.status", string(tenant.Status)),
    )
    
    return tenant, nil
}
```

## External Service Tracing

### HTTP Client Tracing

Create `pkg/tracing/external.go`:

```go
package tracing

import (
    "context"
    "fmt"
    "net/http"
    "time"

    "go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/codes"
    "go.opentelemetry.io/otel/trace"
)

// HTTPClientTracer provides HTTP client tracing
type HTTPClientTracer struct {
    client *http.Client
    tracer trace.Tracer
}

// NewHTTPClientTracer creates a new HTTP client with tracing
func NewHTTPClientTracer(timeout time.Duration) *HTTPClientTracer {
    client := &http.Client{
        Timeout: timeout,
        Transport: otelhttp.NewTransport(http.DefaultTransport),
    }
    
    return &HTTPClientTracer{
        client: client,
        tracer: otel.Tracer("http-client"),
    }
}

// Do performs an HTTP request with tracing
func (t *HTTPClientTracer) Do(ctx context.Context, req *http.Request, service string) (*http.Response, error) {
    ctx, span := t.tracer.Start(ctx, fmt.Sprintf("http.client.%s", service))
    defer span.End()
    
    span.SetAttributes(
        attribute.String("http.method", req.Method),
        attribute.String("http.url", req.URL.String()),
        attribute.String("http.scheme", req.URL.Scheme),
        attribute.String("http.host", req.URL.Host),
        attribute.String("external.service", service),
    )
    
    req = req.WithContext(ctx)
    
    resp, err := t.client.Do(req)
    if err != nil {
        span.RecordError(err)
        span.SetStatus(codes.Error, "HTTP request failed")
        return nil, err
    }
    
    span.SetAttributes(
        attribute.Int("http.status_code", resp.StatusCode),
        attribute.String("http.status_text", resp.Status),
    )
    
    if resp.StatusCode >= 400 {
        span.SetStatus(codes.Error, fmt.Sprintf("HTTP %d", resp.StatusCode))
    }
    
    return resp, nil
}
```

### Redis Tracing

```go
// In pkg/cache/redis.go
package cache

import (
    "context"
    "time"

    "github.com/redis/go-redis/v9"
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/codes"
    "go.opentelemetry.io/otel/trace"
)

type RedisTracer struct {
    client *redis.Client
    tracer trace.Tracer
}

func NewRedisTracer(client *redis.Client) *RedisTracer {
    return &RedisTracer{
        client: client,
        tracer: otel.Tracer("redis"),
    }
}

func (r *RedisTracer) Get(ctx context.Context, key string) (string, error) {
    ctx, span := r.tracer.Start(ctx, "redis.get")
    defer span.End()
    
    span.SetAttributes(
        attribute.String("redis.command", "GET"),
        attribute.String("redis.key", key),
    )
    
    result := r.client.Get(ctx, key)
    
    if result.Err() != nil {
        if result.Err() == redis.Nil {
            span.SetAttributes(attribute.Bool("redis.cache_miss", true))
        } else {
            span.RecordError(result.Err())
            span.SetStatus(codes.Error, "Redis GET failed")
        }
        return "", result.Err()
    }
    
    span.SetAttributes(attribute.Bool("redis.cache_hit", true))
    return result.Val(), nil
}

func (r *RedisTracer) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
    ctx, span := r.tracer.Start(ctx, "redis.set")
    defer span.End()
    
    span.SetAttributes(
        attribute.String("redis.command", "SET"),
        attribute.String("redis.key", key),
        attribute.Int64("redis.ttl_seconds", int64(expiration.Seconds())),
    )
    
    result := r.client.Set(ctx, key, value, expiration)
    
    if result.Err() != nil {
        span.RecordError(result.Err())
        span.SetStatus(codes.Error, "Redis SET failed")
        return result.Err()
    }
    
    return nil
}
```

## Module-Specific Integration

### Auth Module

```go
// In internal/modules/auth/services/auth_service.go
func (s *AuthService) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
    ctx, span := tracing.TraceServiceMethod(ctx, "AuthService", "Login",
        attribute.String("auth.username", req.Username),
        attribute.String("auth.method", "password"),
    )
    defer span.End()
    
    // Validate credentials
    user, err := s.validateCredentials(ctx, req)
    if err != nil {
        tracing.RecordError(span, err, "authentication failed")
        return nil, err
    }
    
    // Generate tokens
    tokens, err := s.generateTokens(ctx, user)
    if err != nil {
        tracing.RecordError(span, err, "token generation failed")
        return nil, err
    }
    
    span.SetAttributes(
        attribute.String("user.id", user.ID),
        attribute.String("auth.status", "success"),
    )
    
    return &LoginResponse{
        User:         user,
        AccessToken:  tokens.AccessToken,
        RefreshToken: tokens.RefreshToken,
    }, nil
}
```

### User Module

```go
// In internal/modules/user/services/user_service.go
func (s *UserService) UpdateProfile(ctx context.Context, userID string, req *UpdateProfileRequest) (*models.UserProfile, error) {
    ctx, span := tracing.TraceServiceMethod(ctx, "UserService", "UpdateProfile",
        attribute.String("user.id", userID),
    )
    defer span.End()
    
    // Get existing profile
    profile, err := s.repo.GetProfileByUserID(ctx, userID)
    if err != nil {
        tracing.RecordError(span, err, "failed to get user profile")
        return nil, err
    }
    
    // Update profile
    updatedProfile, err := s.repo.UpdateProfile(ctx, profile.ID, req)
    if err != nil {
        tracing.RecordError(span, err, "failed to update user profile")
        return nil, err
    }
    
    span.SetAttributes(
        attribute.String("profile.id", updatedProfile.ID),
        attribute.Bool("profile.updated", true),
    )
    
    return updatedProfile, nil
}
```

## Context Propagation

### Tenant Context

```go
// In pkg/context/tenant.go
package context

import (
    "context"
    
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/trace"
)

type TenantContext struct {
    TenantID string
    Domain   string
    PlanID   string
}

func WithTenant(ctx context.Context, tenant *TenantContext) context.Context {
    // Add to trace
    span := trace.SpanFromContext(ctx)
    if span.IsRecording() {
        span.SetAttributes(
            attribute.String("tenant.id", tenant.TenantID),
            attribute.String("tenant.domain", tenant.Domain),
            attribute.String("tenant.plan", tenant.PlanID),
        )
    }
    
    return context.WithValue(ctx, "tenant", tenant)
}

func GetTenant(ctx context.Context) (*TenantContext, bool) {
    tenant, ok := ctx.Value("tenant").(*TenantContext)
    return tenant, ok
}
```

## Next Steps

1. Review [Performance Tuning](./performance.md) for optimization strategies
2. Check [Best Practices](./best-practices.md) for recommended patterns
3. See [Troubleshooting](./troubleshooting.md) for common issues

## References

- [OpenTelemetry Go SDK](https://github.com/open-telemetry/opentelemetry-go)
- [GORM OpenTelemetry Plugin](https://github.com/go-gorm/opentelemetry)
- [Gin OpenTelemetry Middleware](https://github.com/open-telemetry/opentelemetry-go-contrib/tree/main/instrumentation/github.com/gin-gonic/gin/otelgin)