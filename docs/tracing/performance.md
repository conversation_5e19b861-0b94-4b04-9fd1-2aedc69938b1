# Performance Tuning Guide

## Overview

This guide provides comprehensive strategies for optimizing distributed tracing performance in the WN API v3 project while maintaining observability.

## Sampling Strategies

### 1. Probabilistic Sampling

Reduce trace volume by sampling a percentage of requests:

```go
// pkg/tracing/sampling.go
package tracing

import (
    "context"
    "math/rand"
    "time"
    
    "go.opentelemetry.io/otel/sdk/trace"
    "go.opentelemetry.io/otel/trace"
)

// ProbabilisticSampler implements probabilistic sampling
type ProbabilisticSampler struct {
    probability float64
}

func NewProbabilisticSampler(probability float64) *ProbabilisticSampler {
    return &ProbabilisticSampler{
        probability: probability,
    }
}

func (s *ProbabilisticSampler) ShouldSample(ctx context.Context, traceID trace.TraceID, name string, spanKind trace.SpanKind, attributes []attribute.KeyValue, links []trace.Link) trace.SamplingResult {
    if rand.Float64() < s.probability {
        return trace.SamplingResult{
            Decision: trace.RecordAndSample,
        }
    }
    return trace.SamplingResult{
        Decision: trace.Drop,
    }
}

func (s *ProbabilisticSampler) Description() string {
    return fmt.Sprintf("ProbabilisticSampler{probability=%f}", s.probability)
}
```

### 2. Adaptive Sampling

Implement adaptive sampling based on request characteristics:

```go
// AdaptiveSampler adjusts sampling based on request type
type AdaptiveSampler struct {
    defaultRate  float64
    errorRate    float64
    slowRate     float64
    adminRate    float64
    healthRate   float64
}

func NewAdaptiveSampler() *AdaptiveSampler {
    return &AdaptiveSampler{
        defaultRate: 0.1,   // 10% for normal requests
        errorRate:   1.0,   // 100% for errors
        slowRate:    0.5,   // 50% for slow requests
        adminRate:   0.8,   // 80% for admin operations
        healthRate:  0.01,  // 1% for health checks
    }
}

func (s *AdaptiveSampler) ShouldSample(ctx context.Context, traceID trace.TraceID, name string, spanKind trace.SpanKind, attributes []attribute.KeyValue, links []trace.Link) trace.SamplingResult {
    var rate float64
    
    // Determine sampling rate based on span characteristics
    for _, attr := range attributes {
        switch attr.Key {
        case "http.route":
            if strings.Contains(attr.Value.AsString(), "/health") {
                rate = s.healthRate
            } else if strings.Contains(attr.Value.AsString(), "/admin") {
                rate = s.adminRate
            }
        case "http.status_code":
            if attr.Value.AsInt64() >= 400 {
                rate = s.errorRate
            }
        case "http.request_duration":
            if attr.Value.AsInt64() > 1000 { // > 1s
                rate = s.slowRate
            }
        }
    }
    
    if rate == 0 {
        rate = s.defaultRate
    }
    
    if rand.Float64() < rate {
        return trace.SamplingResult{
            Decision: trace.RecordAndSample,
        }
    }
    
    return trace.SamplingResult{
        Decision: trace.Drop,
    }
}
```

### 3. Environment-Specific Sampling

Configure different sampling rates per environment:

```go
// Environment-specific sampling configuration
func GetSamplingConfig(env string) trace.Sampler {
    switch env {
    case "development":
        return trace.AlwaysSample()
    case "staging":
        return trace.TraceIDRatioBased(0.1) // 10%
    case "production":
        return NewAdaptiveSampler()
    default:
        return trace.NeverSample()
    }
}
```

## Span Optimization

### 1. Span Batching

Configure span batching for better performance:

```go
// pkg/tracing/tracer.go
func Initialize(config *Config) func() {
    // ... existing code ...
    
    // Configure batching
    batchOptions := []trace.BatchSpanProcessorOption{
        trace.WithMaxExportBatchSize(512),        // Batch size
        trace.WithBatchTimeout(5 * time.Second),  // Batch timeout
        trace.WithMaxExportBatchSize(2048),       // Max batch size
        trace.WithExportTimeout(30 * time.Second), // Export timeout
    }
    
    // Create batch processor
    bsp := trace.NewBatchSpanProcessor(exp, batchOptions...)
    
    // Create trace provider
    tp := trace.NewTracerProvider(
        trace.WithSpanProcessor(bsp),
        trace.WithResource(res),
        trace.WithSampler(GetSamplingConfig(config.Environment)),
    )
    
    // ... rest of initialization ...
}
```

### 2. Span Attributes Optimization

Limit span attributes to reduce overhead:

```go
// pkg/tracing/span.go
const (
    MaxAttributeValueLength = 1024
    MaxAttributeCount      = 50
)

// OptimizedSpan wraps trace.Span with optimization
type OptimizedSpan struct {
    trace.Span
    attributeCount int
}

func NewOptimizedSpan(span trace.Span) *OptimizedSpan {
    return &OptimizedSpan{
        Span:           span,
        attributeCount: 0,
    }
}

func (s *OptimizedSpan) SetAttributes(attributes ...attribute.KeyValue) {
    if s.attributeCount >= MaxAttributeCount {
        return // Skip if too many attributes
    }
    
    optimizedAttrs := make([]attribute.KeyValue, 0, len(attributes))
    for _, attr := range attributes {
        if s.attributeCount >= MaxAttributeCount {
            break
        }
        
        // Truncate long values
        if attr.Value.Type() == attribute.STRING {
            value := attr.Value.AsString()
            if len(value) > MaxAttributeValueLength {
                value = value[:MaxAttributeValueLength] + "..."
            }
            attr = attribute.String(string(attr.Key), value)
        }
        
        optimizedAttrs = append(optimizedAttrs, attr)
        s.attributeCount++
    }
    
    s.Span.SetAttributes(optimizedAttrs...)
}
```

### 3. Conditional Tracing

Skip tracing for certain operations:

```go
// pkg/tracing/conditional.go
type ConditionalTracer struct {
    tracer trace.Tracer
    config *Config
}

func NewConditionalTracer(tracer trace.Tracer, config *Config) *ConditionalTracer {
    return &ConditionalTracer{
        tracer: tracer,
        config: config,
    }
}

func (t *ConditionalTracer) StartSpan(ctx context.Context, name string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
    // Skip tracing for certain operations
    if t.shouldSkipTracing(name) {
        return ctx, trace.SpanFromContext(ctx)
    }
    
    return t.tracer.Start(ctx, name, opts...)
}

func (t *ConditionalTracer) shouldSkipTracing(operationName string) bool {
    skipPatterns := []string{
        "health_check",
        "metrics_collection",
        "heartbeat",
        "ping",
    }
    
    for _, pattern := range skipPatterns {
        if strings.Contains(operationName, pattern) {
            return true
        }
    }
    
    return false
}
```

## Memory Optimization

### 1. Resource Limits

Configure resource limits for the tracer:

```go
// pkg/tracing/limits.go
type ResourceLimits struct {
    MaxSpansPerTrace       int
    MaxAttributesPerSpan   int
    MaxEventsPerSpan       int
    MaxLinksPerSpan        int
    MaxAttributeValueLength int
}

func DefaultResourceLimits() *ResourceLimits {
    return &ResourceLimits{
        MaxSpansPerTrace:       1000,
        MaxAttributesPerSpan:   50,
        MaxEventsPerSpan:       10,
        MaxLinksPerSpan:        10,
        MaxAttributeValueLength: 1024,
    }
}

// LimitedSpanProcessor wraps span processor with limits
type LimitedSpanProcessor struct {
    processor trace.SpanProcessor
    limits    *ResourceLimits
}

func NewLimitedSpanProcessor(processor trace.SpanProcessor, limits *ResourceLimits) *LimitedSpanProcessor {
    return &LimitedSpanProcessor{
        processor: processor,
        limits:    limits,
    }
}

func (p *LimitedSpanProcessor) OnStart(parent context.Context, s trace.ReadWriteSpan) {
    // Apply limits to span
    if span, ok := s.(*span); ok {
        span.SetLimits(p.limits)
    }
    
    p.processor.OnStart(parent, s)
}

func (p *LimitedSpanProcessor) OnEnd(s trace.ReadOnlySpan) {
    p.processor.OnEnd(s)
}

func (p *LimitedSpanProcessor) Shutdown(ctx context.Context) error {
    return p.processor.Shutdown(ctx)
}

func (p *LimitedSpanProcessor) ForceFlush(ctx context.Context) error {
    return p.processor.ForceFlush(ctx)
}
```

### 2. Memory Pool

Use memory pools for span objects:

```go
// pkg/tracing/pool.go
import (
    "sync"
    "go.opentelemetry.io/otel/attribute"
)

var (
    attributePool = sync.Pool{
        New: func() interface{} {
            return make([]attribute.KeyValue, 0, 10)
        },
    }
)

func GetAttributeSlice() []attribute.KeyValue {
    attrs := attributePool.Get().([]attribute.KeyValue)
    return attrs[:0] // Reset length but keep capacity
}

func PutAttributeSlice(attrs []attribute.KeyValue) {
    if cap(attrs) > 100 { // Don't pool very large slices
        return
    }
    attributePool.Put(attrs)
}

// Usage example
func TraceWithPool(ctx context.Context, name string) (context.Context, trace.Span) {
    attrs := GetAttributeSlice()
    defer PutAttributeSlice(attrs)
    
    // Use attrs for span attributes
    attrs = append(attrs, attribute.String("operation", name))
    
    return tracer.Start(ctx, name, trace.WithAttributes(attrs...))
}
```

## Network Optimization

### 1. Compression

Enable compression for span export:

```go
// pkg/tracing/compression.go
import (
    "go.opentelemetry.io/otel/exporters/otlp/otlptrace"
    "go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
)

func NewOptimizedExporter(endpoint string) (trace.SpanExporter, error) {
    options := []otlptracehttp.Option{
        otlptracehttp.WithEndpoint(endpoint),
        otlptracehttp.WithCompression(otlptracehttp.GzipCompression),
        otlptracehttp.WithTimeout(30 * time.Second),
        otlptracehttp.WithRetry(otlptracehttp.RetryConfig{
            Enabled:         true,
            InitialInterval: 5 * time.Second,
            MaxInterval:     30 * time.Second,
            MaxElapsedTime:  5 * time.Minute,
        }),
    }
    
    return otlptracehttp.New(context.Background(), options...)
}
```

### 2. Connection Pooling

Configure HTTP connection pooling:

```go
// pkg/tracing/client.go
import (
    "net/http"
    "time"
)

func NewOptimizedHTTPClient() *http.Client {
    transport := &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
        DisableKeepAlives:   false,
        DisableCompression:  false,
    }
    
    return &http.Client{
        Transport: transport,
        Timeout:   30 * time.Second,
    }
}
```

## Database Optimization

### 1. Connection Pool Tracing

Optimize database connection pool tracing:

```go
// pkg/tracing/database.go
type OptimizedDBTracer struct {
    tracer        trace.Tracer
    skipQueries   map[string]bool
    slowThreshold time.Duration
}

func NewOptimizedDBTracer() *OptimizedDBTracer {
    return &OptimizedDBTracer{
        tracer: otel.Tracer("database"),
        skipQueries: map[string]bool{
            "SELECT 1":           true,
            "SELECT @@version":   true,
            "SHOW TABLES":        true,
            "SHOW VARIABLES":     true,
        },
        slowThreshold: 100 * time.Millisecond,
    }
}

func (t *OptimizedDBTracer) TraceQuery(ctx context.Context, query string, duration time.Duration) (context.Context, trace.Span) {
    // Skip tracing for health check queries
    if t.skipQueries[query] {
        return ctx, trace.SpanFromContext(ctx)
    }
    
    // Only trace slow queries in production
    if os.Getenv("APP_ENV") == "production" && duration < t.slowThreshold {
        return ctx, trace.SpanFromContext(ctx)
    }
    
    ctx, span := t.tracer.Start(ctx, "db.query")
    
    // Add minimal attributes for fast queries
    if duration < t.slowThreshold {
        span.SetAttributes(
            attribute.String("db.statement", truncateQuery(query, 100)),
            attribute.Int64("db.duration_ms", duration.Milliseconds()),
        )
    } else {
        // Add full attributes for slow queries
        span.SetAttributes(
            attribute.String("db.statement", query),
            attribute.Int64("db.duration_ms", duration.Milliseconds()),
            attribute.Bool("db.slow_query", true),
        )
    }
    
    return ctx, span
}

func truncateQuery(query string, maxLength int) string {
    if len(query) <= maxLength {
        return query
    }
    return query[:maxLength] + "..."
}
```

## Configuration Optimization

### 1. Production Configuration

Optimized configuration for production:

```go
// pkg/tracing/config.go
type PerformanceConfig struct {
    // Sampling
    SamplingRate        float64
    AdaptiveSampling    bool
    
    // Batching
    BatchSize          int
    BatchTimeout       time.Duration
    ExportTimeout      time.Duration
    
    // Limits
    MaxSpansPerTrace   int
    MaxAttributesPerSpan int
    MaxAttributeValueLength int
    
    // Network
    CompressionEnabled bool
    MaxRetries        int
    RetryInterval     time.Duration
    
    // Memory
    MemoryLimitMB     int
    GCInterval        time.Duration
}

func ProductionConfig() *PerformanceConfig {
    return &PerformanceConfig{
        SamplingRate:        0.01, // 1% sampling
        AdaptiveSampling:    true,
        
        BatchSize:          1000,
        BatchTimeout:       5 * time.Second,
        ExportTimeout:      30 * time.Second,
        
        MaxSpansPerTrace:   500,
        MaxAttributesPerSpan: 20,
        MaxAttributeValueLength: 512,
        
        CompressionEnabled: true,
        MaxRetries:        3,
        RetryInterval:     5 * time.Second,
        
        MemoryLimitMB:     256,
        GCInterval:        1 * time.Minute,
    }
}

func DevelopmentConfig() *PerformanceConfig {
    return &PerformanceConfig{
        SamplingRate:        1.0, // 100% sampling
        AdaptiveSampling:    false,
        
        BatchSize:          100,
        BatchTimeout:       1 * time.Second,
        ExportTimeout:      10 * time.Second,
        
        MaxSpansPerTrace:   1000,
        MaxAttributesPerSpan: 50,
        MaxAttributeValueLength: 1024,
        
        CompressionEnabled: false,
        MaxRetries:        1,
        RetryInterval:     1 * time.Second,
        
        MemoryLimitMB:     128,
        GCInterval:        30 * time.Second,
    }
}
```

### 2. Runtime Configuration

Allow runtime configuration changes:

```go
// pkg/tracing/runtime.go
type RuntimeConfig struct {
    mu           sync.RWMutex
    samplingRate float64
    enabled      bool
}

func NewRuntimeConfig() *RuntimeConfig {
    return &RuntimeConfig{
        samplingRate: 0.1,
        enabled:      true,
    }
}

func (r *RuntimeConfig) SetSamplingRate(rate float64) {
    r.mu.Lock()
    defer r.mu.Unlock()
    r.samplingRate = rate
}

func (r *RuntimeConfig) GetSamplingRate() float64 {
    r.mu.RLock()
    defer r.mu.RUnlock()
    return r.samplingRate
}

func (r *RuntimeConfig) Enable() {
    r.mu.Lock()
    defer r.mu.Unlock()
    r.enabled = true
}

func (r *RuntimeConfig) Disable() {
    r.mu.Lock()
    defer r.mu.Unlock()
    r.enabled = false
}

func (r *RuntimeConfig) IsEnabled() bool {
    r.mu.RLock()
    defer r.mu.RUnlock()
    return r.enabled
}
```

## Monitoring and Metrics

### 1. Tracing Metrics

Monitor tracing performance:

```go
// pkg/tracing/metrics.go
import (
    "time"
    "sync/atomic"
)

type TracingMetrics struct {
    spansCreated    int64
    spansExported   int64
    exportFailures int64
    exportDuration int64
    memoryUsage    int64
}

var metrics = &TracingMetrics{}

func RecordSpanCreated() {
    atomic.AddInt64(&metrics.spansCreated, 1)
}

func RecordSpanExported() {
    atomic.AddInt64(&metrics.spansExported, 1)
}

func RecordExportFailure() {
    atomic.AddInt64(&metrics.exportFailures, 1)
}

func RecordExportDuration(duration time.Duration) {
    atomic.StoreInt64(&metrics.exportDuration, duration.Milliseconds())
}

func GetMetrics() *TracingMetrics {
    return &TracingMetrics{
        spansCreated:    atomic.LoadInt64(&metrics.spansCreated),
        spansExported:   atomic.LoadInt64(&metrics.spansExported),
        exportFailures: atomic.LoadInt64(&metrics.exportFailures),
        exportDuration: atomic.LoadInt64(&metrics.exportDuration),
        memoryUsage:    atomic.LoadInt64(&metrics.memoryUsage),
    }
}
```

### 2. Health Checks

Add tracing health checks:

```go
// pkg/tracing/health.go
func HealthCheck() map[string]interface{} {
    metrics := GetMetrics()
    
    health := map[string]interface{}{
        "tracing_enabled": true,
        "spans_created":   metrics.spansCreated,
        "spans_exported":  metrics.spansExported,
        "export_failures": metrics.exportFailures,
        "export_duration_ms": metrics.exportDuration,
        "memory_usage_mb": metrics.memoryUsage / 1024 / 1024,
    }
    
    // Calculate health score
    if metrics.spansCreated > 0 {
        exportRate := float64(metrics.spansExported) / float64(metrics.spansCreated)
        failureRate := float64(metrics.exportFailures) / float64(metrics.spansCreated)
        
        health["export_rate"] = exportRate
        health["failure_rate"] = failureRate
        health["healthy"] = exportRate > 0.9 && failureRate < 0.1
    }
    
    return health
}
```

## Best Practices

### 1. Span Lifecycle Management

- Start spans as late as possible
- End spans as early as possible
- Use defer for span cleanup
- Avoid long-lived spans

### 2. Attribute Guidelines

- Use semantic conventions
- Limit attribute cardinality
- Avoid PII in attributes
- Use structured attribute keys

### 3. Error Handling

- Always handle export errors
- Implement graceful degradation
- Log tracing failures separately
- Don't fail requests due to tracing

### 4. Resource Management

- Monitor memory usage
- Implement proper shutdown
- Use connection pooling
- Configure appropriate timeouts

## Performance Monitoring

### 1. Key Metrics

Monitor these metrics:

- Span creation rate
- Span export rate
- Export failure rate
- Memory usage
- Network bandwidth
- CPU overhead

### 2. Alerting

Set up alerts for:

- High export failure rate (>5%)
- High memory usage (>80%)
- Slow export times (>30s)
- Span backlog buildup

### 3. Optimization Triggers

Optimize when:

- Memory usage exceeds limits
- Export failures increase
- Application latency increases
- Network bandwidth is saturated

## Next Steps

1. Review [Best Practices](./best-practices.md) for recommended patterns
2. Check [Troubleshooting](./troubleshooting.md) for common issues
3. Monitor performance metrics in production
4. Regularly review and adjust sampling rates

## References

- [OpenTelemetry Performance Guidelines](https://opentelemetry.io/docs/reference/specification/performance/)
- [Jaeger Performance Tuning](https://www.jaegertracing.io/docs/1.35/performance-tuning/)
- [Go Memory Management](https://golang.org/doc/gc-guide)