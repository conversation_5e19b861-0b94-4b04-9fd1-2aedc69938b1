definitions:
  dto.APIKeyResponse:
    properties:
      created_at:
        type: string
      description:
        example: API key for production environment
        type: string
      expires_at:
        description: 'KEEP_OMITEMPTY: Optional expiration'
        type: string
      id:
        example: 1
        type: integer
      key:
        description: 'KEEP_OMITEMPTY: Sensitive data, only shown on creation'
        example: ak_1234567890abcdef
        type: string
      key_prefix:
        example: ak_1234
        type: string
      last_used_at:
        description: 'KEEP_OMITEMPTY: Optional usage timestamp'
        type: string
      name:
        example: Production API Key
        type: string
      rate_limit:
        example: 1000
        type: integer
      rate_window:
        example: 3600
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/models.APIKeyStatus'
        example: active
      tenant_id:
        example: 1
        type: integer
      updated_at:
        type: string
      website_id:
        example: 1
        type: integer
    type: object
  dto.BlogCategoryResponse:
    properties:
      created_at:
        type: string
      description:
        description: 'KEEP_OMITEMPTY: Optional content'
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      meta_keywords:
        description: 'KEEP_OMITEMPTY: Optional SEO field'
        type: string
      meta_title:
        description: 'KEEP_OMITEMPTY: Optional SEO field'
        type: string
      name:
        type: string
      parent_id:
        description: 'KEEP_OMITEMPTY: Optional hierarchy relationship'
        type: integer
      post_count:
        type: integer
      slug:
        type: string
      sort_order:
        type: integer
      tenant_id:
        type: integer
      updated_at:
        type: string
      website_id:
        type: integer
    type: object
  dto.BlogPostCreateRequest:
    properties:
      allow_comments:
        example: true
        type: boolean
      author_id:
        example: 1
        minimum: 1
        type: integer
      category_id:
        example: 1
        minimum: 1
        type: integer
      content:
        example: This is the comprehensive content of my first blog post...
        type: string
      excerpt:
        example: A brief summary of my first blog post
        maxLength: 1000
        type: string
      featured_image:
        example: https://example.com/images/featured-image.jpg
        type: string
      is_featured:
        example: false
        type: boolean
      password:
        example: ""
        maxLength: 255
        type: string
      scheduled_at:
        example: "2025-01-20T15:00:00Z"
        type: string
      slug:
        example: my-first-blog-post
        maxLength: 255
        minLength: 1
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.BlogPostStatus'
        enum:
        - draft
        - review
        - published
        - scheduled
        - archived
        - rejected
        example: draft
      tag_ids:
        items:
          type: integer
        type: array
      tenant_id:
        example: 1
        minimum: 1
        type: integer
      title:
        example: My First Blog Post
        maxLength: 255
        minLength: 1
        type: string
      type:
        allOf:
        - $ref: '#/definitions/models.BlogPostType'
        enum:
        - post
        - page
        - announcement
        example: post
      website_id:
        example: 1
        minimum: 1
        type: integer
    required:
    - author_id
    - content
    - slug
    - tenant_id
    - title
    - website_id
    type: object
  dto.BlogPostListResponse:
    properties:
      pagination:
        $ref: '#/definitions/pagination.CursorResponse'
      posts:
        items:
          $ref: '#/definitions/dto.BlogPostResponse'
        type: array
    type: object
  dto.BlogPostResponse:
    properties:
      allow_comments:
        type: boolean
      author_id:
        type: integer
      category:
        allOf:
        - $ref: '#/definitions/dto.BlogCategoryResponse'
        description: 'KEEP_OMITEMPTY: Optional nested object'
      category_id:
        description: 'KEEP_OMITEMPTY: Optional relationship'
        type: integer
      content:
        type: string
      created_at:
        type: string
      excerpt:
        description: 'KEEP_OMITEMPTY: Optional content summary'
        type: string
      featured_image:
        description: 'KEEP_OMITEMPTY: Optional media'
        type: string
      id:
        type: integer
      is_featured:
        type: boolean
      published_at:
        description: 'KEEP_OMITEMPTY: Optional publish timestamp'
        type: string
      scheduled_at:
        description: 'KEEP_OMITEMPTY: Optional schedule timestamp'
        type: string
      slug:
        type: string
      status:
        $ref: '#/definitions/models.BlogPostStatus'
      tags:
        description: 'KEEP_OMITEMPTY: Optional nested array'
        items:
          $ref: '#/definitions/dto.BlogTagResponse'
        type: array
      tenant_id:
        type: integer
      title:
        type: string
      type:
        $ref: '#/definitions/models.BlogPostType'
      updated_at:
        type: string
      view_count:
        type: integer
      website_id:
        type: integer
    type: object
  dto.BlogPostUpdateRequest:
    properties:
      allow_comments:
        example: false
        type: boolean
      category_id:
        example: 2
        minimum: 1
        type: integer
      content:
        example: Updated content of the blog post...
        type: string
      excerpt:
        example: Updated excerpt
        maxLength: 1000
        type: string
      featured_image:
        example: https://example.com/images/updated.jpg
        type: string
      is_featured:
        example: true
        type: boolean
      password:
        example: ""
        maxLength: 255
        type: string
      scheduled_at:
        example: "2025-01-22T15:00:00Z"
        type: string
      slug:
        example: updated-blog-post
        maxLength: 255
        minLength: 1
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.BlogPostStatus'
        enum:
        - draft
        - review
        - published
        - scheduled
        - archived
        - rejected
        example: published
      tag_ids:
        items:
          type: integer
        type: array
      title:
        example: Updated Blog Post Title
        maxLength: 255
        minLength: 1
        type: string
      type:
        allOf:
        - $ref: '#/definitions/models.BlogPostType'
        enum:
        - post
        - page
        - announcement
        example: post
    type: object
  dto.BlogTagResponse:
    properties:
      color:
        description: 'KEEP_OMITEMPTY: Optional display customization'
        type: string
      created_at:
        type: string
      description:
        description: 'KEEP_OMITEMPTY: Optional content'
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      name:
        type: string
      post_count:
        type: integer
      slug:
        type: string
      tenant_id:
        type: integer
      updated_at:
        type: string
      website_id:
        type: integer
    type: object
  dto.CompleteTwoFactorLoginRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      two_factor_code:
        example: "123456"
        type: string
    required:
    - email
    - two_factor_code
    type: object
  dto.CompleteTwoFactorLoginResponse:
    properties:
      status:
        type: string
    type: object
  dto.CreateAPIKeyRequest:
    properties:
      description:
        example: API key for production environment
        maxLength: 1000
        type: string
      expires_in:
        example: 7776000
        minimum: 1
        type: integer
      ip_whitelist:
        example:
        - ***********
        - ***********
        items:
          type: string
        type: array
      name:
        example: Production API Key
        maxLength: 100
        minLength: 1
        type: string
      permissions:
        additionalProperties: true
        type: object
      rate_limit:
        example: 1000
        maximum: 100000
        minimum: 1
        type: integer
      rate_window:
        example: 3600
        maximum: 86400
        minimum: 1
        type: integer
      scopes:
        example:
        - read
        - write
        items:
          type: string
        type: array
    required:
    - name
    type: object
  dto.DisableTwoFactorRequest:
    properties:
      code:
        example: "123456"
        type: string
    required:
    - code
    type: object
  dto.DisableTwoFactorResponse:
    properties:
      status:
        type: string
      verified:
        type: boolean
    type: object
  dto.EnableTwoFactorResponse:
    properties:
      backup_codes:
        items:
          type: string
        type: array
      qr_code:
        type: string
      secret:
        type: string
      status:
        type: string
    type: object
  dto.ForgotPasswordRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  dto.ForgotPasswordResponse:
    properties:
      status:
        type: string
    type: object
  dto.GetActiveSessionsResponse:
    properties:
      sessions:
        items:
          $ref: '#/definitions/dto.SessionResponse'
        type: array
    type: object
  dto.GetProfileResponse:
    properties:
      email:
        type: string
      user_id:
        type: integer
    type: object
  dto.LoginRequest:
    properties:
      device_name:
        example: iPhone 15 Pro
        type: string
      email:
        example: <EMAIL>
        type: string
      password:
        example: SecurePass123!
        type: string
      two_factor_code:
        example: ""
        type: string
    required:
    - email
    - password
    type: object
  dto.LoginResponse:
    properties:
      access_token:
        type: string
      expires_in:
        type: integer
      refresh_token:
        type: string
      requires_email_verification:
        type: boolean
      requires_onboarding:
        type: boolean
      requires_two_factor:
        type: boolean
      session_id:
        type: integer
      token_type:
        type: string
    type: object
  dto.LogoutAllDevicesResponse:
    properties:
      sessions_terminated:
        type: integer
    type: object
  dto.NotificationCreateRequest:
    type: object
  dto.NotificationListResponse:
    properties:
      notifications:
        items:
          $ref: '#/definitions/dto.NotificationResponse'
        type: array
      pagination:
        $ref: '#/definitions/pagination.CursorResponse'
    type: object
  dto.NotificationRecipientResponse:
    properties:
      bounced_at:
        description: 'KEEP_OMITEMPTY: Optional bounce timestamp'
        type: string
      created_at:
        type: string
      delivered_at:
        description: 'KEEP_OMITEMPTY: Optional delivery timestamp'
        type: string
      delivery_info:
        description: 'KEEP_OMITEMPTY: Optional delivery metadata'
        items:
          type: integer
        type: array
      device_token:
        description: 'KEEP_OMITEMPTY: Optional device token'
        type: string
      engagement_data:
        description: 'KEEP_OMITEMPTY: Optional engagement data'
        items:
          type: integer
        type: array
      error_message:
        description: 'KEEP_OMITEMPTY: Optional error details'
        type: string
      failed_at:
        description: 'KEEP_OMITEMPTY: Optional failure timestamp'
        type: string
      id:
        example: 1
        type: integer
      notification_id:
        example: 1
        type: integer
      read_at:
        description: 'KEEP_OMITEMPTY: Optional engagement timestamp'
        type: string
      recipient_address:
        example: <EMAIL>
        type: string
      recipient_type:
        allOf:
        - $ref: '#/definitions/models.RecipientType'
        example: email
      status:
        allOf:
        - $ref: '#/definitions/models.RecipientStatus'
        example: delivered
      tenant_id:
        example: 1
        type: integer
      updated_at:
        type: string
      user_id:
        description: 'KEEP_OMITEMPTY: Optional user reference'
        example: 123
        type: integer
    type: object
  dto.NotificationResponse:
    properties:
      channel:
        allOf:
        - $ref: '#/definitions/models.NotificationChannel'
        example: email
      created_at:
        type: string
      delivered_at:
        description: 'KEEP_OMITEMPTY: Optional delivery timestamp'
        type: string
      error_message:
        description: 'KEEP_OMITEMPTY: Optional error details'
        type: string
      failed_at:
        description: 'KEEP_OMITEMPTY: Optional failure timestamp'
        type: string
      id:
        example: 1
        type: integer
      max_retries:
        example: 3
        type: integer
      metadata:
        description: 'KEEP_OMITEMPTY: Optional metadata'
        items:
          type: integer
        type: array
      priority:
        allOf:
        - $ref: '#/definitions/models.NotificationPriority'
        example: normal
      recipients:
        description: 'KEEP_OMITEMPTY: Optional nested array'
        items:
          $ref: '#/definitions/dto.NotificationRecipientResponse'
        type: array
      retry_count:
        example: 0
        type: integer
      scheduled_at:
        description: 'KEEP_OMITEMPTY: Optional scheduling'
        type: string
      sent_at:
        description: 'KEEP_OMITEMPTY: Optional delivery timestamp'
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.NotificationStatus'
        example: sent
      subject:
        example: Welcome to our platform!
        type: string
      template:
        allOf:
        - $ref: '#/definitions/dto.NotificationTemplateResponse'
        description: Optional relationships
      template_data:
        description: 'KEEP_OMITEMPTY: Optional data payload'
        items:
          type: integer
        type: array
      template_id:
        description: 'KEEP_OMITEMPTY: Optional template reference'
        example: 1
        type: integer
      tenant_id:
        example: 1
        type: integer
      type:
        example: welcome_email
        type: string
      updated_at:
        type: string
    type: object
  dto.NotificationTemplateCreateRequest:
    properties:
      channel:
        allOf:
        - $ref: '#/definitions/models.NotificationChannel'
        enum:
        - email
        - socket
        - push
        - sms
        example: email
      code:
        example: welcome_email_v1
        maxLength: 100
        type: string
      description:
        example: Welcome email sent to new users upon registration
        type: string
      is_active:
        example: false
        type: boolean
      name:
        example: Welcome Email Template
        maxLength: 255
        type: string
      type:
        allOf:
        - $ref: '#/definitions/models.TemplateType'
        enum:
        - transactional
        - marketing
        - system
        - custom
        example: transactional
      variables:
        example:
        - name
        - company
        - activation_link
        items:
          type: string
        type: array
    required:
    - channel
    - code
    - name
    - type
    type: object
  dto.NotificationTemplateResponse:
    properties:
      active_version:
        $ref: '#/definitions/dto.NotificationTemplateVersionResponse'
      active_version_id:
        example: 5
        type: integer
      channel:
        allOf:
        - $ref: '#/definitions/models.NotificationChannel'
        example: email
      code:
        example: welcome_email_v1
        type: string
      created_at:
        type: string
      created_by:
        example: 123
        type: integer
      description:
        type: string
      id:
        example: 1
        type: integer
      is_active:
        example: true
        type: boolean
      name:
        example: Welcome Email Template
        type: string
      tenant_id:
        example: 1
        type: integer
      type:
        allOf:
        - $ref: '#/definitions/models.TemplateType'
        example: transactional
      updated_at:
        type: string
      updated_by:
        example: 124
        type: integer
      variables:
        items:
          type: integer
        type: array
      version_count:
        example: 3
        type: integer
      versions:
        description: Optional relationships
        items:
          $ref: '#/definitions/dto.NotificationTemplateVersionResponse'
        type: array
    type: object
  dto.NotificationTemplateVersionResponse:
    properties:
      approved_at:
        type: string
      approved_by:
        example: 125
        type: integer
      body_html:
        example: <h1>Welcome {{name}}!</h1>
        type: string
      body_text:
        type: string
      created_at:
        type: string
      created_by:
        example: 123
        type: integer
      id:
        example: 1
        type: integer
      is_active:
        example: true
        type: boolean
      is_approved:
        example: true
        type: boolean
      language:
        example: en
        type: string
      subject:
        example: Welcome to {{company}}!
        type: string
      template_id:
        example: 1
        type: integer
      tenant_id:
        example: 1
        type: integer
      updated_at:
        type: string
      variables:
        items:
          type: integer
        type: array
      version_number:
        example: 2
        type: integer
    type: object
  dto.NotificationTrackClickRequest:
    type: object
  dto.RefreshTokenRequest:
    properties:
      refresh_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    required:
    - refresh_token
    type: object
  dto.RefreshTokenResponse:
    properties:
      access_token:
        type: string
      expires_in:
        type: integer
      refresh_token:
        type: string
      token_type:
        type: string
    type: object
  dto.RegisterRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        maxLength: 100
        type: string
      invitation_token:
        example: ""
        type: string
      last_name:
        example: Doe
        maxLength: 100
        type: string
      password:
        example: SecurePass123!
        minLength: 8
        type: string
      username:
        example: johndoe
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - password
    type: object
  dto.RegisterResponse:
    properties:
      email_verification_sent:
        type: boolean
      message:
        type: string
      requires_email_verification:
        type: boolean
    type: object
  dto.ResendVerificationEmailRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  dto.ResendVerificationEmailResponse:
    properties:
      email:
        type: string
      max_resends:
        type: integer
      next_resend_at:
        type: string
      resend_count:
        type: integer
      status:
        type: string
    type: object
  dto.ResetPasswordRequest:
    properties:
      confirm_password:
        example: NewSecurePass123!
        minLength: 8
        type: string
      new_password:
        example: NewSecurePass123!
        minLength: 8
        type: string
      token:
        example: abc123def456
        type: string
    required:
    - confirm_password
    - new_password
    - token
    type: object
  dto.ResetPasswordResponse:
    properties:
      status:
        type: string
    type: object
  dto.RevokeSessionResponse:
    properties:
      revoked:
        type: boolean
      revoked_count:
        type: integer
    type: object
  dto.SEORedirectListResponse:
    properties:
      page:
        example: 1
        type: integer
      page_size:
        example: 20
        type: integer
      pagination:
        $ref: '#/definitions/pagination.CursorResponse'
      redirects:
        items:
          $ref: '#/definitions/dto.SEORedirectResponse'
        type: array
      total:
        description: Legacy fields for backward compatibility
        example: 50
        type: integer
      total_pages:
        example: 3
        type: integer
    type: object
  dto.SEORedirectResponse:
    properties:
      active_from:
        description: Scheduling
        type: string
      active_until:
        type: string
      conditions:
        additionalProperties: true
        description: Conditional Redirects
        type: object
      created_at:
        type: string
      created_by:
        example: 123
        type: integer
      destination_path:
        example: /new-page
        type: string
      destination_url:
        example: https://example.com/new-page
        type: string
      hit_count:
        description: Tracking and Analytics
        example: 150
        type: integer
      id:
        example: 1
        type: integer
      last_hit_at:
        type: string
      last_hit_ip:
        example: *************
        type: string
      last_hit_referrer:
        example: https://google.com
        type: string
      last_hit_user_agent:
        example: Mozilla/5.0...
        type: string
      notes:
        example: Redirecting old product page
        type: string
      query_string_handling:
        allOf:
        - $ref: '#/definitions/models.QueryStringHandling'
        example: preserve
      redirect_match:
        allOf:
        - $ref: '#/definitions/models.RedirectMatch'
        example: exact
      redirect_type:
        allOf:
        - $ref: '#/definitions/models.RedirectType'
        description: Redirect Configuration
        example: "301"
      regex_pattern:
        example: ^/old-(.*)$
        type: string
      replacement_pattern:
        example: /new-$1
        type: string
      seo_reason:
        description: SEO Information
        example: page_moved
        type: string
      source_path:
        example: /old-page
        type: string
      source_url:
        description: Redirect Information
        example: /old-page
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.RedirectStatus'
        description: Status and Timestamps
        example: active
      tenant_id:
        example: 1
        type: integer
      updated_at:
        type: string
      updated_by:
        example: 124
        type: integer
      website_id:
        example: 1
        type: integer
    type: object
  dto.SessionResponse:
    properties:
      device_name:
        type: string
      device_type:
        type: string
      expires_at:
        type: string
      id:
        type: integer
      ip_address:
        type: string
      last_used_at:
        type: string
    type: object
  dto.SwitchTenantRequest:
    properties:
      tenant_id:
        example: 1
        type: integer
    required:
    - tenant_id
    type: object
  dto.SwitchTenantResponse:
    properties:
      access_token:
        type: string
      expires_at:
        type: integer
      refresh_token:
        type: string
    type: object
  dto.TenantAssignPlanRequest:
    properties:
      plan_id:
        example: 2
        type: integer
    required:
    - plan_id
    type: object
  dto.TenantCreateRequest:
    properties:
      company_address:
        example: 123 Business St, City, State
        maxLength: 500
        type: string
      company_name:
        example: My Company Inc.
        maxLength: 200
        type: string
      company_tax_id:
        example: TAX123456
        maxLength: 50
        type: string
      contact_email:
        example: <EMAIL>
        type: string
      contact_phone:
        example: "+1234567890"
        type: string
      domain:
        example: mycompany
        maxLength: 100
        minLength: 3
        type: string
      name:
        example: My Company
        maxLength: 100
        minLength: 3
        type: string
      plan_id:
        example: 1
        type: integer
    required:
    - contact_email
    - domain
    - name
    - plan_id
    type: object
  dto.TenantPlanCreateRequest:
    properties:
      billing_period:
        enum:
        - monthly
        - yearly
        - lifetime
        example: monthly
        type: string
      custom_settings:
        additionalProperties: true
        type: object
      description:
        example: Perfect for growing businesses
        maxLength: 500
        type: string
      display_order:
        example: 1
        type: integer
      features:
        example:
        - feature1
        - feature2
        - feature3
        items:
          type: string
        type: array
      is_featured:
        example: false
        type: boolean
      is_visible:
        example: true
        type: boolean
      max_api_calls:
        example: 10000
        minimum: 0
        type: integer
      max_projects:
        example: 5
        minimum: 1
        type: integer
      max_storage:
        example: ***********
        minimum: 0
        type: integer
      max_users:
        example: 10
        minimum: 1
        type: integer
      name:
        example: Professional Plan
        maxLength: 100
        minLength: 3
        type: string
      price:
        example: 29.99
        minimum: 0
        type: number
      slug:
        example: pro
        maxLength: 50
        minLength: 3
        type: string
      trial_days:
        example: 14
        maximum: 365
        minimum: 0
        type: integer
    required:
    - billing_period
    - max_api_calls
    - max_projects
    - max_storage
    - max_users
    - name
    - price
    - slug
    type: object
  dto.TenantPlanResponse:
    properties:
      created_at:
        type: string
      description:
        example: Perfect for growing businesses
        type: string
      display_order:
        example: 1
        type: integer
      features:
        example:
        - feature1
        - feature2
        - feature3
        items:
          type: string
        type: array
      id:
        example: 1
        type: integer
      is_featured:
        example: false
        type: boolean
      is_visible:
        example: true
        type: boolean
      max_api_calls_per_day:
        example: 10000
        type: integer
      max_projects:
        example: 5
        type: integer
      max_storage:
        example: ***********
        type: integer
      max_users:
        example: 10
        type: integer
      monthly_price:
        example: 29.99
        type: number
      name:
        example: Professional Plan
        type: string
      slug:
        example: pro
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.PlanStatus'
        example: active
      tenant_count:
        example: 25
        type: integer
      trial_days:
        example: 14
        type: integer
      updated_at:
        type: string
      yearly_price:
        example: 299.99
        type: number
    type: object
  dto.TenantPlanUpdateRequest:
    properties:
      custom_settings:
        additionalProperties: true
        type: object
      description:
        example: Updated description
        maxLength: 500
        type: string
      display_order:
        example: 0
        type: integer
      features:
        example:
        - feature1
        - feature2
        - feature4
        items:
          type: string
        type: array
      is_featured:
        example: true
        type: boolean
      is_visible:
        example: true
        type: boolean
      max_api_calls_per_day:
        example: 20000
        minimum: 0
        type: integer
      max_projects:
        example: 10
        minimum: 1
        type: integer
      max_storage:
        example: 21474836480
        minimum: 0
        type: integer
      max_users:
        example: 20
        minimum: 1
        type: integer
      monthly_price:
        example: 24.99
        minimum: 0
        type: number
      name:
        example: Updated Professional Plan
        maxLength: 100
        minLength: 3
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.PlanStatus'
        enum:
        - active
        - inactive
        - deprecated
        example: active
      trial_days:
        example: 30
        maximum: 365
        minimum: 0
        type: integer
      yearly_price:
        example: 249.99
        minimum: 0
        type: number
    type: object
  dto.TenantResponse:
    properties:
      company_address:
        description: 'KEEP_OMITEMPTY: Optional company data'
        example: 123 Business St
        type: string
      company_name:
        description: 'KEEP_OMITEMPTY: Optional company data'
        example: My Company Inc.
        type: string
      company_tax_id:
        description: 'KEEP_OMITEMPTY: Optional company data'
        example: TAX123456
        type: string
      contact_email:
        example: <EMAIL>
        type: string
      contact_phone:
        description: 'KEEP_OMITEMPTY: Optional contact'
        example: "+1234567890"
        type: string
      created_at:
        type: string
      custom_css:
        description: 'KEEP_OMITEMPTY: Optional branding'
        type: string
      domain:
        example: mycompany
        type: string
      favicon_url:
        description: 'KEEP_OMITEMPTY: Optional branding'
        example: https://example.com/favicon.ico
        type: string
      id:
        example: 1
        type: integer
      logo_url:
        description: 'KEEP_OMITEMPTY: Optional branding'
        example: https://example.com/logo.png
        type: string
      name:
        example: My Company
        type: string
      plan_id:
        example: 1
        type: integer
      primary_color:
        description: 'KEEP_OMITEMPTY: Optional branding'
        example: '#3B82F6'
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.TenantStatus'
        example: active
      subscription_ends:
        description: 'KEEP_OMITEMPTY: Nullable subscription date'
        type: string
      trial_ends:
        description: 'KEEP_OMITEMPTY: Nullable trial date'
        type: string
      updated_at:
        type: string
    type: object
  dto.TenantUpdateRequest:
    properties:
      company_address:
        example: 456 New Address St
        maxLength: 500
        type: string
      company_name:
        example: Updated Company Inc.
        maxLength: 200
        type: string
      company_tax_id:
        example: NEWTAX789
        maxLength: 50
        type: string
      contact_email:
        example: <EMAIL>
        type: string
      contact_phone:
        example: "+9876543210"
        type: string
      custom_css:
        example: 'body { font-family: Arial; }'
        maxLength: 10000
        type: string
      favicon_url:
        example: https://example.com/favicon.ico
        type: string
      logo_url:
        example: https://example.com/logo.png
        type: string
      name:
        example: Updated Company
        maxLength: 100
        minLength: 3
        type: string
      primary_color:
        example: '#3B82F6'
        type: string
    type: object
  dto.TenantUpdateStatusRequest:
    properties:
      status:
        enum:
        - active
        - suspended
        - inactive
        - trial
        - deleted
        example: active
        type: string
    required:
    - status
    type: object
  dto.TokenRefreshRequest:
    properties:
      refresh_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    required:
    - refresh_token
    type: object
  dto.TokenRefreshResponse:
    properties:
      access_token:
        type: string
      expires_at:
        type: integer
      refresh_token:
        type: string
    type: object
  dto.UserCreateRequest:
    properties:
      avatar_url:
        example: https://example.com/avatar.jpg
        type: string
      display_name:
        example: John Doe
        maxLength: 100
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        maxLength: 50
        minLength: 1
        type: string
      language:
        example: en
        type: string
      last_name:
        example: Doe
        maxLength: 50
        minLength: 1
        type: string
      password:
        example: SecurePass123!
        minLength: 8
        type: string
      phone:
        example: "+1234567890"
        type: string
      role:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.UserRole'
        enum:
        - user
        - admin
        - moderator
        example: user
      send_welcome_email:
        example: true
        type: boolean
      status:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.UserStatus'
        enum:
        - active
        - inactive
        - suspended
        - pending_verification
        example: active
      tenant_id:
        example: 1
        type: integer
      timezone:
        example: America/New_York
        type: string
      username:
        example: john_doe
        maxLength: 30
        minLength: 3
        type: string
    required:
    - email
    - first_name
    - last_name
    - password
    - role
    - tenant_id
    - username
    type: object
  dto.UserResponse:
    properties:
      avatar_url:
        description: 'KEEP_OMITEMPTY: Optional personal data'
        example: https://example.com/avatar.jpg
        type: string
      created_at:
        type: string
      display_name:
        example: John Doe
        type: string
      email:
        example: <EMAIL>
        type: string
      email_verified:
        example: true
        type: boolean
      email_verified_at:
        description: 'KEEP_OMITEMPTY: Nullable timestamp'
        type: string
      first_name:
        example: John
        type: string
      id:
        example: 1
        type: integer
      language:
        example: en
        type: string
      last_login_at:
        description: 'KEEP_OMITEMPTY: Nullable timestamp'
        type: string
      last_name:
        example: Doe
        type: string
      login_count:
        example: 42
        type: integer
      phone:
        description: 'KEEP_OMITEMPTY: Optional personal data'
        example: "+1234567890"
        type: string
      phone_verified:
        example: false
        type: boolean
      phone_verified_at:
        description: 'KEEP_OMITEMPTY: Nullable timestamp'
        type: string
      role:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.UserRole'
        example: user
      status:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.UserStatus'
        example: active
      tenant_id:
        example: 1
        type: integer
      timezone:
        example: America/New_York
        type: string
      two_factor_enabled:
        example: false
        type: boolean
      updated_at:
        type: string
      username:
        example: john_doe
        type: string
    type: object
  dto.UserUpdateRequest:
    properties:
      avatar_url:
        example: https://example.com/new-avatar.jpg
        type: string
      display_name:
        example: Jane Smith
        maxLength: 100
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: Jane
        maxLength: 50
        minLength: 1
        type: string
      language:
        example: fr
        type: string
      last_name:
        example: Smith
        maxLength: 50
        minLength: 1
        type: string
      phone:
        example: "+9876543210"
        type: string
      role:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.UserRole'
        enum:
        - user
        - admin
        - moderator
        example: admin
      status:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.UserStatus'
        enum:
        - active
        - inactive
        - suspended
        - pending_verification
        - deleted
        example: inactive
      timezone:
        example: Europe/London
        type: string
      username:
        example: updated_username
        maxLength: 30
        minLength: 3
        type: string
    type: object
  dto.VerifyEmailRequest:
    properties:
      token:
        example: abc123def456
        type: string
    required:
    - token
    type: object
  dto.VerifyEmailResponse:
    properties:
      email:
        type: string
      email_verified:
        type: boolean
      status:
        type: string
      user_id:
        type: integer
    type: object
  dto.VerifyTwoFactorRequest:
    properties:
      code:
        example: "123456"
        type: string
    required:
    - code
    type: object
  dto.VerifyTwoFactorResponse:
    properties:
      backup_codes:
        items:
          type: string
        type: array
      recovery_used:
        type: boolean
      status:
        type: string
      verified:
        type: boolean
    type: object
  dto.WebsiteCreateRequest:
    properties:
      active_theme:
        example: default
        maxLength: 100
        type: string
      custom_css:
        example: 'body { color: #333; }'
        type: string
      custom_js:
        example: console.log('Hello world');
        type: string
      description:
        example: A description of my website
        type: string
      domain:
        example: example.com
        type: string
      favicon:
        example: https://example.com/favicon.ico
        type: string
      language:
        example: en
        maxLength: 10
        minLength: 2
        type: string
      name:
        example: My Website
        maxLength: 255
        minLength: 1
        type: string
      site_logo:
        example: https://example.com/logo.png
        type: string
      social_media:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_website_models.JSONMap'
      subdomain:
        example: my-site
        maxLength: 100
        minLength: 2
        type: string
      timezone:
        example: America/New_York
        type: string
    required:
    - name
    type: object
  dto.WebsiteListResponse:
    properties:
      pagination:
        $ref: '#/definitions/pagination.CursorResponse'
      websites:
        items:
          $ref: '#/definitions/dto.WebsiteResponse'
        type: array
    type: object
  dto.WebsiteResponse:
    properties:
      active_theme:
        description: 'KEEP_OMITEMPTY: Optional theme'
        example: default
        type: string
      created_at:
        type: string
      custom_css:
        description: 'KEEP_OMITEMPTY: Optional customization'
        type: string
      custom_js:
        description: 'KEEP_OMITEMPTY: Optional customization'
        type: string
      description:
        description: 'KEEP_OMITEMPTY: Optional content'
        example: A description of my website
        type: string
      domain:
        description: 'KEEP_OMITEMPTY: Optional custom domain'
        example: example.com
        type: string
      facebook_pixel_id:
        description: 'KEEP_OMITEMPTY: Optional analytics'
        example: "123456789"
        type: string
      favicon:
        description: 'KEEP_OMITEMPTY: Optional branding'
        example: https://example.com/favicon.ico
        type: string
      google_analytics_id:
        description: 'KEEP_OMITEMPTY: Optional analytics'
        example: GA-123456-7
        type: string
      google_tag_manager_id:
        description: 'KEEP_OMITEMPTY: Optional analytics'
        example: GTM-ABC123
        type: string
      id:
        example: 1
        type: integer
      language:
        example: en
        type: string
      name:
        example: My Website
        type: string
      site_logo:
        description: 'KEEP_OMITEMPTY: Optional branding'
        example: https://example.com/logo.png
        type: string
      social_media:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_website_models.JSONMap'
        description: 'KEEP_OMITEMPTY: Optional social links'
      status:
        allOf:
        - $ref: '#/definitions/models.WebsiteStatus'
        example: active
      subdomain:
        description: 'KEEP_OMITEMPTY: Optional subdomain'
        example: my-site
        type: string
      tenant_id:
        example: 1
        type: integer
      timezone:
        example: America/New_York
        type: string
      updated_at:
        type: string
    type: object
  gin.H:
    additionalProperties: {}
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_auth_dto.VerificationStatusResponse:
    properties:
      can_resend:
        type: boolean
      email:
        type: string
      expires_at:
        type: string
      has_token:
        type: boolean
      is_expired:
        type: boolean
      is_verified:
        type: boolean
      max_resends:
        type: integer
      resend_count:
        type: integer
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.Tenant:
    properties:
      id:
        type: integer
      name:
        type: string
      slug:
        type: string
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.User:
    properties:
      email:
        type: string
      id:
        type: integer
      name:
        type: string
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.Website:
    properties:
      domain:
        type: string
      id:
        type: integer
      name:
        type: string
      tenant_id:
        type: integer
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_onboarding_models.AnalyticsResponse:
    properties:
      context_id:
        type: integer
      context_type:
        type: string
      created_at:
        type: string
      device_type:
        type: string
      dimensions:
        additionalProperties: true
        type: object
      duration:
        type: integer
      event_data:
        additionalProperties: true
        type: object
      event_timestamp:
        type: string
      event_type:
        type: string
      id:
        type: integer
      ip_address:
        type: string
      metric_type:
        type: string
      metric_value:
        additionalProperties: true
        type: object
      platform:
        type: string
      referrer_url:
        type: string
      session_id:
        type: string
      tenant_id:
        type: integer
      user_agent:
        type: string
      user_id:
        type: integer
      value:
        type: number
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_seo_models.ValidationError:
    properties:
      code:
        type: string
      context:
        type: string
      field:
        type: string
      message:
        type: string
      severity:
        type: string
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_seo_models.ValidationWarning:
    properties:
      code:
        type: string
      context:
        type: string
      field:
        type: string
      message:
        type: string
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.DataType:
    enum:
    - string
    - number
    - boolean
    - json
    - array
    type: string
    x-enum-varnames:
    - DataTypeString
    - DataTypeNumber
    - DataTypeBoolean
    - DataTypeJSON
    - DataTypeArray
  github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.SettingCategory:
    properties:
      description:
        type: string
      display_name:
        type: string
      icon:
        type: string
      name:
        type: string
      order:
        type: integer
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.SettingCreateRequest:
    properties:
      category:
        maxLength: 100
        type: string
      data_type:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.DataType'
        enum:
        - string
        - number
        - boolean
        - json
        - array
      default_value: {}
      description:
        maxLength: 1000
        type: string
      is_encrypted:
        type: boolean
      is_public:
        type: boolean
      is_read_only:
        type: boolean
      is_required:
        type: boolean
      key:
        maxLength: 255
        type: string
      options:
        items:
          type: integer
        type: array
      scope_id:
        type: integer
      scope_type:
        allOf:
        - $ref: '#/definitions/models.ScopeType'
        enum:
        - global
        - tenant
        - website
        - user
      validation_rules:
        items:
          type: integer
        type: array
      value: {}
    required:
    - category
    - data_type
    - key
    - scope_type
    - value
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.SettingUpdateRequest:
    properties:
      default_value: {}
      description:
        maxLength: 1000
        type: string
      is_public:
        type: boolean
      is_read_only:
        type: boolean
      is_required:
        type: boolean
      options:
        items:
          type: integer
        type: array
      validation_rules:
        items:
          type: integer
        type: array
      value: {}
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.TenantMembership:
    properties:
      created_at:
        description: Timestamps
        type: string
      display_name:
        type: string
      id:
        type: integer
      invitation_accepted_at:
        type: string
      invited_by:
        description: Invitation Details
        type: integer
      invited_by_user:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.User'
      is_primary:
        type: boolean
      joined_at:
        description: Activity Tracking
        type: string
      last_activity_at:
        type: string
      local_username:
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.TenantMembershipStatus'
        description: Membership Details
      tenant_id:
        type: integer
      updated_at:
        type: string
      user:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.User'
        description: Relationships
      user_id:
        type: integer
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.User:
    properties:
      avatar_url:
        description: Profile Information
        type: string
      created_at:
        description: Timestamps
        type: string
      display_name:
        maxLength: 255
        minLength: 1
        type: string
      email:
        description: Basic Information
        type: string
      email_verified:
        type: boolean
      email_verified_at:
        type: string
      first_name:
        maxLength: 255
        minLength: 1
        type: string
      id:
        type: integer
      language:
        maxLength: 10
        minLength: 2
        type: string
      last_login_at:
        description: Activity Tracking
        type: string
      last_login_ip:
        type: string
      last_name:
        maxLength: 255
        minLength: 1
        type: string
      login_count:
        type: integer
      phone:
        description: Contact Information
        type: string
      phone_verified:
        type: boolean
      phone_verified_at:
        type: string
      preferences:
        $ref: '#/definitions/models.UserPreferences'
      profile:
        allOf:
        - $ref: '#/definitions/models.UserProfile'
        description: Relationships
      social_links:
        items:
          $ref: '#/definitions/models.UserSocialLink'
        type: array
      status:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.UserStatus'
        description: Status and Configuration
        enum:
        - active
        - suspended
        - inactive
        - pending_verification
        - deleted
      tenant_memberships:
        items:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.TenantMembership'
        type: array
      timezone:
        type: string
      two_factor_enabled:
        description: Security
        type: boolean
      updated_at:
        type: string
      username:
        maxLength: 30
        minLength: 3
        type: string
    required:
    - email
    - language
    - status
    - timezone
    type: object
  github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.UserRole:
    enum:
    - admin
    - user
    - guest
    type: string
    x-enum-varnames:
    - UserRoleAdmin
    - UserRoleUser
    - UserRoleGuest
  github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.UserStatus:
    enum:
    - active
    - suspended
    - inactive
    - pending_verification
    - deleted
    type: string
    x-enum-varnames:
    - UserStatusActive
    - UserStatusSuspended
    - UserStatusInactive
    - UserStatusPendingVerification
    - UserStatusDeleted
  github_com_tranthanhloi_wn-api-v3_internal_modules_website_models.JSONMap:
    additionalProperties: true
    type: object
  handlers.TestEmailRequest:
    properties:
      content:
        type: string
      subject:
        type: string
      to:
        type: string
    required:
    - content
    - subject
    - to
    type: object
  handlers.TestEmailResponse:
    properties:
      data: {}
    type: object
  models.APIKeyStatus:
    enum:
    - active
    - inactive
    - expired
    - revoked
    - deleted
    type: string
    x-enum-varnames:
    - APIKeyStatusActive
    - APIKeyStatusInactive
    - APIKeyStatusExpired
    - APIKeyStatusRevoked
    - APIKeyStatusDeleted
  models.AccessPermission:
    properties:
      permission:
        description: read, write, delete
        type: string
      user_id:
        type: integer
    type: object
  models.AnalyticsAggregation:
    properties:
      average_duration:
        type: number
      average_value:
        type: number
      context_id:
        type: integer
      context_type:
        type: string
      count:
        type: integer
      end_date:
        type: string
      event_type:
        type: string
      start_date:
        type: string
      total_duration:
        type: integer
      total_value:
        type: number
      unique_users:
        type: integer
    type: object
  models.AnalyticsRequest:
    properties:
      context_id:
        type: integer
      context_type:
        enum:
        - journey
        - step
        - template
        - user
        type: string
      device_type:
        maxLength: 50
        type: string
      dimensions:
        additionalProperties: true
        type: object
      duration:
        type: integer
      event_data:
        additionalProperties: true
        type: object
      event_type:
        maxLength: 50
        type: string
      ip_address:
        maxLength: 45
        type: string
      metric_type:
        maxLength: 50
        type: string
      metric_value:
        additionalProperties: true
        type: object
      platform:
        maxLength: 50
        type: string
      referrer_url:
        maxLength: 500
        type: string
      session_id:
        maxLength: 100
        type: string
      user_agent:
        maxLength: 500
        type: string
      user_id:
        type: integer
      value:
        type: number
    required:
    - context_id
    - context_type
    - event_type
    type: object
  models.BlogPostStatus:
    enum:
    - draft
    - review
    - published
    - scheduled
    - archived
    - rejected
    - deleted
    type: string
    x-enum-varnames:
    - BlogPostStatusDraft
    - BlogPostStatusReview
    - BlogPostStatusPublished
    - BlogPostStatusScheduled
    - BlogPostStatusArchived
    - BlogPostStatusRejected
    - BlogPostStatusDeleted
  models.BlogPostType:
    enum:
    - post
    - page
    - announcement
    type: string
    x-enum-varnames:
    - BlogPostTypePost
    - BlogPostTypePage
    - BlogPostTypeAnnouncement
  models.CreateMediaFolderRequest:
    properties:
      color:
        type: string
      description:
        type: string
      icon:
        type: string
      name:
        maxLength: 255
        minLength: 1
        type: string
      parent_id:
        type: integer
      settings:
        additionalProperties: true
        type: object
      tenant_id:
        type: integer
      type:
        $ref: '#/definitions/models.FolderType'
      visibility:
        $ref: '#/definitions/models.FolderVisibility'
      website_id:
        type: integer
    required:
    - name
    - tenant_id
    - website_id
    type: object
  models.CreateSEOMetaRequest:
    properties:
      additional_meta:
        description: Additional Meta Tags
        items:
          $ref: '#/definitions/models.MetaTag'
        type: array
      canonical_url:
        type: string
      change_frequency:
        enum:
        - always
        - hourly
        - daily
        - weekly
        - monthly
        - yearly
        - never
        type: string
      focus_keyword:
        description: SEO Settings
        maxLength: 255
        type: string
      is_indexed:
        description: Status and Indexing
        type: boolean
      is_sitemap_included:
        type: boolean
      meta_description:
        maxLength: 500
        type: string
      meta_keywords:
        type: string
      meta_robots:
        maxLength: 100
        type: string
      meta_title:
        description: SEO Meta Tags
        maxLength: 255
        type: string
      og_description:
        maxLength: 500
        type: string
      og_image:
        type: string
      og_locale:
        maxLength: 10
        type: string
      og_site_name:
        maxLength: 255
        type: string
      og_title:
        description: Open Graph Meta Tags
        maxLength: 255
        type: string
      og_type:
        maxLength: 50
        type: string
      og_url:
        type: string
      page_id:
        type: integer
      page_path:
        type: string
      page_type:
        enum:
        - page
        - post
        - category
        - tag
        - product
        - custom
        type: string
      page_url:
        type: string
      priority:
        maximum: 1
        minimum: 0
        type: number
      schema_data:
        $ref: '#/definitions/models.SchemaData'
      schema_type:
        description: Schema.org Structured Data
        maxLength: 100
        type: string
      twitter_card:
        description: Twitter Card Meta Tags
        maxLength: 50
        type: string
      twitter_creator:
        maxLength: 255
        type: string
      twitter_description:
        maxLength: 500
        type: string
      twitter_image:
        type: string
      twitter_site:
        maxLength: 255
        type: string
      twitter_title:
        maxLength: 255
        type: string
      website_id:
        type: integer
    required:
    - page_path
    - page_type
    - page_url
    - website_id
    type: object
  models.CreateTemplateVersionRequest:
    properties:
      body_html:
        type: string
      body_text:
        type: string
      language:
        maxLength: 10
        type: string
      subject:
        maxLength: 255
        type: string
      variables:
        items:
          type: string
        type: array
    required:
    - body_html
    - language
    - subject
    type: object
  models.FileStatus:
    enum:
    - uploading
    - processing
    - ready
    - error
    - deleted
    type: string
    x-enum-varnames:
    - FileStatusUploading
    - FileStatusProcessing
    - FileStatusReady
    - FileStatusError
    - FileStatusDeleted
  models.FileType:
    enum:
    - image
    - video
    - audio
    - document
    - archive
    - other
    type: string
    x-enum-varnames:
    - FileTypeImage
    - FileTypeVideo
    - FileTypeAudio
    - FileTypeDocument
    - FileTypeArchive
    - FileTypeOther
  models.FileVisibility:
    enum:
    - public
    - private
    - shared
    type: string
    x-enum-varnames:
    - FileVisibilityPublic
    - FileVisibilityPrivate
    - FileVisibilityShared
  models.FolderStatus:
    enum:
    - active
    - archived
    - deleted
    type: string
    x-enum-varnames:
    - FolderStatusActive
    - FolderStatusArchived
    - FolderStatusDeleted
  models.FolderType:
    enum:
    - user
    - system
    - public
    - private
    - shared
    type: string
    x-enum-varnames:
    - FolderTypeUser
    - FolderTypeSystem
    - FolderTypePublic
    - FolderTypePrivate
    - FolderTypeShared
  models.FolderVisibility:
    enum:
    - public
    - private
    - shared
    type: string
    x-enum-varnames:
    - FolderVisibilityPublic
    - FolderVisibilityPrivate
    - FolderVisibilityShared
  models.Gender:
    enum:
    - male
    - female
    - other
    - prefer_not_to_say
    type: string
    x-enum-varnames:
    - GenderMale
    - GenderFemale
    - GenderOther
    - GenderPreferNotToSay
  models.JourneyAnalytics:
    properties:
      abandon_rate:
        type: number
      average_duration:
        type: integer
      completion_rate:
        type: number
      journey_id:
        type: integer
      journey_name:
        type: string
      median_duration:
        type: integer
      step_analytics:
        items:
          $ref: '#/definitions/models.StepAnalytics'
        type: array
      total_abandons:
        type: integer
      total_completions:
        type: integer
      total_starts:
        type: integer
    type: object
  models.JourneyRequest:
    properties:
      branching_logic:
        additionalProperties: true
        type: object
      completion_criteria:
        additionalProperties: true
        type: object
      context_id:
        type: integer
      context_type:
        enum:
        - tenant
        - website
        - user
        - global
        type: string
      description:
        maxLength: 5000
        type: string
      display_name:
        maxLength: 255
        minLength: 1
        type: string
      estimated_duration:
        type: integer
      is_active:
        type: boolean
      is_required:
        type: boolean
      is_skippable:
        type: boolean
      journey_type:
        enum:
        - user
        - admin
        - setup
        - feature
        - integration
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        maxLength: 255
        minLength: 1
        type: string
      slug:
        maxLength: 255
        minLength: 1
        type: string
      start_condition:
        additionalProperties: true
        type: object
      status:
        enum:
        - active
        - inactive
        - draft
        - archived
        type: string
      tags:
        items:
          type: string
        type: array
      timeout_duration:
        type: integer
      trigger_type:
        enum:
        - manual
        - automatic
        - conditional
        - scheduled
        type: string
      version:
        maxLength: 20
        type: string
    required:
    - display_name
    - journey_type
    - name
    - slug
    - trigger_type
    type: object
  models.JourneyResponse:
    properties:
      completed_users:
        type: integer
      completion_rate:
        type: number
      context_id:
        type: integer
      context_type:
        type: string
      created_at:
        type: string
      description:
        type: string
      display_name:
        type: string
      estimated_duration:
        type: integer
      id:
        type: integer
      is_active:
        type: boolean
      is_required:
        type: boolean
      is_skippable:
        type: boolean
      journey_type:
        type: string
      name:
        type: string
      published_at:
        type: string
      slug:
        type: string
      status:
        type: string
      steps:
        items:
          $ref: '#/definitions/models.StepResponse'
        type: array
      tenant_id:
        type: integer
      total_users:
        type: integer
      trigger_type:
        type: string
      updated_at:
        type: string
      version:
        type: string
    type: object
  models.JourneyUpdateRequest:
    properties:
      branching_logic:
        additionalProperties: true
        type: object
      completion_criteria:
        additionalProperties: true
        type: object
      context_id:
        type: integer
      context_type:
        enum:
        - tenant
        - website
        - user
        - global
        type: string
      description:
        maxLength: 5000
        type: string
      display_name:
        maxLength: 255
        minLength: 1
        type: string
      estimated_duration:
        type: integer
      is_active:
        type: boolean
      is_required:
        type: boolean
      is_skippable:
        type: boolean
      journey_type:
        enum:
        - user
        - admin
        - setup
        - feature
        - integration
        type: string
      metadata:
        additionalProperties: true
        type: object
      name:
        maxLength: 255
        minLength: 1
        type: string
      start_condition:
        additionalProperties: true
        type: object
      status:
        enum:
        - active
        - inactive
        - draft
        - archived
        type: string
      tags:
        items:
          type: string
        type: array
      timeout_duration:
        type: integer
      trigger_type:
        enum:
        - manual
        - automatic
        - conditional
        - scheduled
        type: string
      version:
        maxLength: 20
        type: string
    type: object
  models.KeywordAnalysis:
    properties:
      count:
        type: integer
      density:
        type: number
      suggestions:
        items:
          type: string
        type: array
      variations:
        items:
          type: string
        type: array
    type: object
  models.MediaFile:
    properties:
      access_permissions:
        items:
          $ref: '#/definitions/models.AccessPermission'
        type: array
      alt_text:
        description: SEO and accessibility
        type: string
      category:
        type: string
      cdn_provider:
        type: string
      cdn_url:
        description: CDN information
        type: string
      created_at:
        description: Timestamps
        type: string
      description:
        type: string
      download_count:
        type: integer
      duration:
        description: in seconds for video/audio
        type: integer
      error_message:
        type: string
      file_hash:
        type: string
      file_size:
        type: integer
      file_type:
        allOf:
        - $ref: '#/definitions/models.FileType'
        description: File categorization
      filename:
        description: File information
        type: string
      folder:
        allOf:
        - $ref: '#/definitions/models.MediaFolder'
        description: Relationships
      folder_id:
        type: integer
      height:
        type: integer
      id:
        type: integer
      last_accessed_at:
        type: string
      metadata:
        additionalProperties: true
        type: object
      mime_type:
        type: string
      optimized_at:
        type: string
      optimized_path:
        description: Optimization information
        type: string
      optimized_size:
        type: integer
      optimized_url:
        type: string
      original_filename:
        type: string
      processing_status:
        additionalProperties: true
        type: object
      public_url:
        type: string
      slug:
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.FileStatus'
        description: Processing status
      storage_path:
        type: string
      storage_provider:
        type: string
      storage_type:
        allOf:
        - $ref: '#/definitions/models.StorageType'
        description: Storage information
      tags:
        items:
          $ref: '#/definitions/models.MediaTag'
        type: array
      tenant:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.Tenant'
      tenant_id:
        type: integer
      thumbnails:
        items:
          $ref: '#/definitions/models.MediaThumbnail'
        type: array
      title:
        type: string
      updated_at:
        type: string
      uploader:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.User'
      user_id:
        type: integer
      view_count:
        description: Usage tracking
        type: integer
      visibility:
        allOf:
        - $ref: '#/definitions/models.FileVisibility'
        description: Access control
      website:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.Website'
      website_id:
        type: integer
      width:
        description: Media metadata
        type: integer
    type: object
  models.MediaFileResponse:
    properties:
      alt_text:
        type: string
      category:
        type: string
      cdn_provider:
        type: string
      cdn_url:
        type: string
      created_at:
        type: string
      description:
        type: string
      download_count:
        type: integer
      duration:
        type: integer
      file_hash:
        type: string
      file_size:
        type: integer
      file_type:
        $ref: '#/definitions/models.FileType'
      filename:
        type: string
      folder:
        $ref: '#/definitions/models.MediaFolderResponse'
      folder_id:
        type: integer
      height:
        type: integer
      id:
        type: integer
      last_accessed_at:
        type: string
      mime_type:
        type: string
      optimized_at:
        type: string
      optimized_path:
        type: string
      optimized_size:
        type: integer
      optimized_url:
        type: string
      original_filename:
        type: string
      public_url:
        type: string
      slug:
        type: string
      status:
        $ref: '#/definitions/models.FileStatus'
      storage_path:
        type: string
      storage_provider:
        type: string
      storage_type:
        $ref: '#/definitions/models.StorageType'
      tags:
        items:
          $ref: '#/definitions/models.MediaTagResponse'
        type: array
      tenant_id:
        type: integer
      thumbnails:
        items:
          $ref: '#/definitions/models.MediaThumbnailResponse'
        type: array
      title:
        type: string
      updated_at:
        type: string
      user_id:
        type: integer
      view_count:
        type: integer
      visibility:
        $ref: '#/definitions/models.FileVisibility'
      website_id:
        type: integer
      width:
        type: integer
    type: object
  models.MediaFileStats:
    properties:
      archive_files:
        type: integer
      audio_files:
        type: integer
      document_files:
        type: integer
      image_files:
        type: integer
      other_files:
        type: integer
      storage_limit:
        type: integer
      storage_percent:
        type: number
      storage_used:
        type: integer
      total_files:
        type: integer
      total_folders:
        type: integer
      total_size:
        type: integer
      video_files:
        type: integer
    type: object
  models.MediaFileTag:
    properties:
      confidence_score:
        description: For AI-generated tags (0.0 to 1.0)
        type: number
      file:
        allOf:
        - $ref: '#/definitions/models.MediaFile'
        description: Relationships
      file_id:
        type: integer
      id:
        type: integer
      tag:
        $ref: '#/definitions/models.MediaTag'
      tag_context:
        additionalProperties: true
        description: Tag metadata
        type: object
      tag_id:
        type: integer
      tag_source:
        $ref: '#/definitions/models.TagSource'
      tagged_at:
        description: Tagging information
        type: string
      tagger:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.User'
      user_id:
        type: integer
    type: object
  models.MediaFolder:
    properties:
      access_permissions:
        items:
          $ref: '#/definitions/models.AccessPermission'
        type: array
      children:
        items:
          $ref: '#/definitions/models.MediaFolder'
        type: array
      color:
        type: string
      created_at:
        type: string
      creator:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.User'
      description:
        type: string
      file_count:
        description: Usage statistics
        type: integer
      files:
        items:
          $ref: '#/definitions/models.MediaFile'
        type: array
      icon:
        type: string
      id:
        type: integer
      level:
        type: integer
      lft:
        description: Nested set model for hierarchy
        type: integer
      name:
        description: Folder information
        type: string
      parent:
        allOf:
        - $ref: '#/definitions/models.MediaFolder'
        description: Relationships
      parent_id:
        type: integer
      path:
        type: string
      rgt:
        type: integer
      settings:
        additionalProperties: true
        type: object
      slug:
        type: string
      sort_order:
        description: Folder settings
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/models.FolderStatus'
        description: Status and timestamps
      tenant:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.Tenant'
      tenant_id:
        type: integer
      total_size:
        type: integer
      type:
        allOf:
        - $ref: '#/definitions/models.FolderType'
        description: Folder type and settings
      updated_at:
        type: string
      user_id:
        type: integer
      visibility:
        allOf:
        - $ref: '#/definitions/models.FolderVisibility'
        description: Access control
      website:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.Website'
      website_id:
        type: integer
    type: object
  models.MediaFolderResponse:
    properties:
      children:
        items:
          $ref: '#/definitions/models.MediaFolderResponse'
        type: array
      color:
        type: string
      created_at:
        type: string
      description:
        type: string
      file_count:
        type: integer
      files:
        items:
          $ref: '#/definitions/models.MediaFileResponse'
        type: array
      icon:
        type: string
      id:
        type: integer
      level:
        type: integer
      name:
        type: string
      parent_id:
        type: integer
      path:
        type: string
      slug:
        type: string
      sort_order:
        type: integer
      status:
        $ref: '#/definitions/models.FolderStatus'
      tenant_id:
        type: integer
      total_size:
        type: integer
      type:
        $ref: '#/definitions/models.FolderType'
      updated_at:
        type: string
      user_id:
        type: integer
      visibility:
        $ref: '#/definitions/models.FolderVisibility'
      website_id:
        type: integer
    type: object
  models.MediaTag:
    properties:
      category:
        description: Tag categorization
        type: string
      color:
        description: Tag appearance
        type: string
      created_at:
        type: string
      creator:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.User'
        description: Relationships
      description:
        type: string
      file_tags:
        items:
          $ref: '#/definitions/models.MediaFileTag'
        type: array
      files:
        items:
          $ref: '#/definitions/models.MediaFile'
        type: array
      icon:
        type: string
      id:
        type: integer
      is_featured:
        description: Tag settings
        type: boolean
      is_private:
        type: boolean
      name:
        description: Tag information
        type: string
      popularity_score:
        type: number
      settings:
        additionalProperties: true
        type: object
      slug:
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.TagStatus'
        description: Status and timestamps
      tenant:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.Tenant'
      tenant_id:
        type: integer
      type:
        $ref: '#/definitions/models.TagType'
      updated_at:
        type: string
      usage_count:
        description: Usage statistics
        type: integer
      user_id:
        type: integer
      website:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_media_models.Website'
      website_id:
        type: integer
    type: object
  models.MediaTagResponse:
    properties:
      category:
        type: string
      color:
        type: string
      created_at:
        type: string
      description:
        type: string
      icon:
        type: string
      id:
        type: integer
      is_featured:
        type: boolean
      is_private:
        type: boolean
      name:
        type: string
      popularity_score:
        type: number
      slug:
        type: string
      status:
        $ref: '#/definitions/models.TagStatus'
      tenant_id:
        type: integer
      type:
        $ref: '#/definitions/models.TagType'
      updated_at:
        type: string
      usage_count:
        type: integer
      user_id:
        type: integer
      website_id:
        type: integer
    type: object
  models.MediaThumbnail:
    properties:
      created_at:
        type: string
      file:
        allOf:
        - $ref: '#/definitions/models.MediaFile'
        description: Relationships
      file_hash:
        type: string
      file_id:
        type: integer
      file_size:
        description: File details
        type: integer
      format:
        description: Optimization settings
        type: string
      height:
        type: integer
      id:
        type: integer
      is_optimized:
        type: boolean
      mime_type:
        type: string
      optimization_savings:
        description: percentage saved
        type: number
      processing_method:
        allOf:
        - $ref: '#/definitions/models.ProcessingMethod'
        description: Processing information
      processing_options:
        additionalProperties: true
        type: object
      public_url:
        type: string
      quality:
        description: JPEG quality 1-100
        type: integer
      size_name:
        description: Thumbnail information
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.ThumbnailStatus'
        description: Status and timestamps
      storage_path:
        type: string
      storage_type:
        allOf:
        - $ref: '#/definitions/models.StorageType'
        description: Storage information
      updated_at:
        type: string
      width:
        type: integer
    type: object
  models.MediaThumbnailResponse:
    properties:
      created_at:
        type: string
      file_hash:
        type: string
      file_id:
        type: integer
      file_size:
        type: integer
      format:
        type: string
      height:
        type: integer
      id:
        type: integer
      is_optimized:
        type: boolean
      mime_type:
        type: string
      optimization_savings:
        type: number
      processing_method:
        $ref: '#/definitions/models.ProcessingMethod'
      public_url:
        type: string
      quality:
        type: integer
      size_name:
        type: string
      status:
        $ref: '#/definitions/models.ThumbnailStatus'
      storage_path:
        type: string
      storage_type:
        $ref: '#/definitions/models.StorageType'
      updated_at:
        type: string
      width:
        type: integer
    type: object
  models.MetaTag:
    properties:
      content:
        type: string
      name:
        type: string
      property:
        type: string
    type: object
  models.NotificationChannel:
    enum:
    - email
    - socket
    - push
    - sms
    type: string
    x-enum-varnames:
    - ChannelEmail
    - ChannelSocket
    - ChannelPush
    - ChannelSMS
  models.NotificationPriority:
    enum:
    - low
    - normal
    - high
    - urgent
    type: string
    x-enum-varnames:
    - PriorityLow
    - PriorityNormal
    - PriorityHigh
    - PriorityUrgent
  models.NotificationStatus:
    enum:
    - pending
    - queued
    - sent
    - delivered
    - failed
    - cancelled
    type: string
    x-enum-varnames:
    - StatusPending
    - StatusQueued
    - StatusSent
    - StatusDelivered
    - StatusFailed
    - StatusCancelled
  models.PlanStatus:
    enum:
    - active
    - inactive
    - deprecated
    type: string
    x-enum-varnames:
    - PlanStatusActive
    - PlanStatusInactive
    - PlanStatusDeprecated
  models.ProcessingJob:
    properties:
      file_id:
        type: integer
      id:
        type: string
      status:
        description: queued, processing, completed, failed
        type: string
      type:
        description: thumbnail, optimization, analysis
        type: string
    type: object
  models.ProcessingMethod:
    enum:
    - crop
    - resize
    - fit
    - fill
    type: string
    x-enum-varnames:
    - ProcessingMethodCrop
    - ProcessingMethodResize
    - ProcessingMethodFit
    - ProcessingMethodFill
  models.ProfileVisibility:
    enum:
    - public
    - private
    - friends_only
    type: string
    x-enum-varnames:
    - ProfileVisibilityPublic
    - ProfileVisibilityPrivate
    - ProfileVisibilityFriendsOnly
  models.ProgressResponse:
    properties:
      attempt_number:
        type: integer
      completed_at:
        type: string
      created_at:
        type: string
      duration:
        type: integer
      error_messages:
        additionalProperties: true
        type: object
      id:
        type: integer
      journey:
        $ref: '#/definitions/models.JourneyResponse'
      journey_id:
        type: integer
      metadata:
        additionalProperties: true
        type: object
      notes:
        type: string
      skipped_at:
        type: string
      started_at:
        type: string
      status:
        type: string
      step:
        $ref: '#/definitions/models.StepResponse'
      step_data:
        additionalProperties: true
        type: object
      step_id:
        type: integer
      tenant_id:
        type: integer
      updated_at:
        type: string
      user_data:
        additionalProperties: true
        type: object
      user_id:
        type: integer
      validation_results:
        additionalProperties: true
        type: object
    type: object
  models.ProgressSummary:
    properties:
      completed_at:
        type: string
      completed_steps:
        type: integer
      completion_rate:
        type: number
      current_step:
        $ref: '#/definitions/models.StepResponse'
      failed_steps:
        type: integer
      is_completed:
        type: boolean
      journey_id:
        type: integer
      journey_name:
        type: string
      pending_steps:
        type: integer
      skipped_steps:
        type: integer
      started_at:
        type: string
      tenant_id:
        type: integer
      total_duration:
        type: integer
      total_steps:
        type: integer
      user_id:
        type: integer
    type: object
  models.ProgressUpdateRequest:
    properties:
      error_messages:
        additionalProperties: true
        type: object
      metadata:
        additionalProperties: true
        type: object
      notes:
        maxLength: 5000
        type: string
      status:
        enum:
        - pending
        - in_progress
        - completed
        - skipped
        - failed
        type: string
      step_data:
        additionalProperties: true
        type: object
      user_data:
        additionalProperties: true
        type: object
      validation_results:
        additionalProperties: true
        type: object
    required:
    - status
    type: object
  models.QueryStringHandling:
    enum:
    - ignore
    - preserve
    - append
    type: string
    x-enum-varnames:
    - QueryStringIgnore
    - QueryStringPreserve
    - QueryStringAppend
  models.ReadabilityAnalysis:
    properties:
      grade:
        type: string
      issues:
        items:
          type: string
        type: array
      score:
        type: number
      suggestions:
        items:
          type: string
        type: array
    type: object
  models.RecipientStatus:
    enum:
    - pending
    - sent
    - delivered
    - read
    - failed
    - bounced
    - blocked
    type: string
    x-enum-varnames:
    - RecipientStatusPending
    - RecipientStatusSent
    - RecipientStatusDelivered
    - RecipientStatusRead
    - RecipientStatusFailed
    - RecipientStatusBounced
    - RecipientStatusBlocked
  models.RecipientType:
    enum:
    - user
    - email
    - phone
    - device
    type: string
    x-enum-varnames:
    - RecipientTypeUser
    - RecipientTypeEmail
    - RecipientTypePhone
    - RecipientTypeDevice
  models.RedirectMatch:
    enum:
    - exact
    - regex
    - wildcard
    type: string
    x-enum-varnames:
    - RedirectMatchExact
    - RedirectMatchRegex
    - RedirectMatchWildcard
  models.RedirectStatus:
    enum:
    - active
    - inactive
    - expired
    - deleted
    type: string
    x-enum-varnames:
    - RedirectStatusActive
    - RedirectStatusInactive
    - RedirectStatusExpired
    - RedirectStatusDeleted
  models.RedirectType:
    enum:
    - "301"
    - "302"
    - "303"
    - "307"
    - "308"
    type: string
    x-enum-varnames:
    - RedirectType301
    - RedirectType302
    - RedirectType303
    - RedirectType307
    - RedirectType308
  models.SEOAnalysis:
    properties:
      description_analysis:
        $ref: '#/definitions/models.TextAnalysis'
      keyword_analysis:
        $ref: '#/definitions/models.KeywordAnalysis'
      overall_score:
        type: number
      readability_analysis:
        $ref: '#/definitions/models.ReadabilityAnalysis'
      recommendations:
        items:
          type: string
        type: array
      technical_analysis:
        $ref: '#/definitions/models.TechnicalAnalysis'
      title_analysis:
        $ref: '#/definitions/models.TextAnalysis'
    type: object
  models.SEOMetaResponse:
    properties:
      additional_meta:
        description: Additional Meta Tags
        items:
          $ref: '#/definitions/models.MetaTag'
        type: array
      canonical_url:
        type: string
      change_frequency:
        type: string
      created_at:
        type: string
      focus_keyword:
        description: SEO Settings
        type: string
      generated_tags:
        items:
          type: string
        type: array
      id:
        type: integer
      is_indexed:
        description: Status and Indexing
        type: boolean
      is_sitemap_included:
        type: boolean
      meta_description:
        type: string
      meta_keywords:
        type: string
      meta_robots:
        type: string
      meta_title:
        description: SEO Meta Tags
        type: string
      og_description:
        type: string
      og_image:
        type: string
      og_locale:
        type: string
      og_site_name:
        type: string
      og_title:
        description: Open Graph Meta Tags
        type: string
      og_type:
        type: string
      og_url:
        type: string
      page_id:
        type: integer
      page_path:
        type: string
      page_type:
        description: Page Information
        type: string
      page_url:
        type: string
      priority:
        type: number
      readability_score:
        type: number
      schema_data:
        $ref: '#/definitions/models.SchemaData'
      schema_type:
        description: Schema.org Structured Data
        type: string
      seo_analysis:
        $ref: '#/definitions/models.SEOAnalysis'
      seo_score:
        type: number
      status:
        description: Status and Timestamps
        type: string
      tenant_id:
        type: integer
      twitter_card:
        description: Twitter Card Meta Tags
        type: string
      twitter_creator:
        type: string
      twitter_description:
        type: string
      twitter_image:
        type: string
      twitter_site:
        type: string
      twitter_title:
        type: string
      updated_at:
        type: string
      website_id:
        type: integer
    type: object
  models.SEOValidationResult:
    properties:
      errors:
        items:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_seo_models.ValidationError'
        type: array
      is_valid:
        type: boolean
      score:
        type: number
      suggestions:
        items:
          $ref: '#/definitions/models.ValidationSuggestion'
        type: array
      warnings:
        items:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_seo_models.ValidationWarning'
        type: array
    type: object
  models.SchemaData:
    additionalProperties: true
    type: object
  models.ScopeType:
    enum:
    - global
    - tenant
    - website
    - user
    type: string
    x-enum-varnames:
    - ScopeTypeGlobal
    - ScopeTypeTenant
    - ScopeTypeWebsite
    - ScopeTypeUser
  models.SetSettingValueRequest:
    properties:
      scope_id:
        type: integer
      scope_type:
        allOf:
        - $ref: '#/definitions/models.ScopeType'
        enum:
        - global
        - tenant
        - website
        - user
      value: {}
    required:
    - scope_type
    - value
    type: object
  models.SettingResponse:
    properties:
      category:
        type: string
      created_at:
        type: string
      data_type:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.DataType'
      default_value: {}
      description:
        type: string
      full_key:
        type: string
      id:
        type: integer
      is_encrypted:
        type: boolean
      is_public:
        type: boolean
      is_read_only:
        type: boolean
      is_required:
        type: boolean
      key:
        type: string
      options:
        items:
          type: integer
        type: array
      scope_id:
        type: integer
      scope_key:
        type: string
      scope_type:
        $ref: '#/definitions/models.ScopeType'
      updated_at:
        type: string
      validation_rules:
        description: Optional metadata (only included in detailed responses)
        items:
          type: integer
        type: array
      value: {}
    type: object
  models.SettingSchemaCreateRequest:
    properties:
      category:
        maxLength: 100
        type: string
      data_type:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.DataType'
        enum:
        - string
        - number
        - boolean
        - json
        - array
      default_value: {}
      description:
        maxLength: 1000
        type: string
      is_deletable:
        type: boolean
      is_encrypted:
        type: boolean
      is_public:
        type: boolean
      is_read_only:
        type: boolean
      is_required:
        type: boolean
      key:
        maxLength: 255
        type: string
      name:
        maxLength: 255
        type: string
      options:
        items:
          type: integer
        type: array
      ui_config:
        items:
          type: integer
        type: array
      validation_rules:
        items:
          type: integer
        type: array
    required:
    - category
    - data_type
    - key
    - name
    type: object
  models.SettingSchemaResponse:
    properties:
      category:
        type: string
      created_at:
        type: string
      data_type:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.DataType'
      default_value: {}
      description:
        type: string
      id:
        type: integer
      is_deletable:
        type: boolean
      is_encrypted:
        type: boolean
      is_public:
        type: boolean
      is_read_only:
        type: boolean
      is_required:
        type: boolean
      key:
        type: string
      name:
        type: string
      options:
        items:
          type: integer
        type: array
      ui_config:
        items:
          type: integer
        type: array
      updated_at:
        type: string
      validation_rules:
        description: Optional metadata
        items:
          type: integer
        type: array
      version:
        type: integer
    type: object
  models.SettingSchemaUpdateRequest:
    properties:
      data_type:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.DataType'
        enum:
        - string
        - number
        - boolean
        - json
        - array
      default_value: {}
      description:
        maxLength: 1000
        type: string
      is_deletable:
        type: boolean
      is_public:
        type: boolean
      is_read_only:
        type: boolean
      is_required:
        type: boolean
      name:
        maxLength: 255
        type: string
      options:
        items:
          type: integer
        type: array
      ui_config:
        items:
          type: integer
        type: array
      validation_rules:
        items:
          type: integer
        type: array
    type: object
  models.SocialPlatform:
    enum:
    - twitter
    - linkedin
    - github
    - facebook
    - instagram
    - youtube
    - tiktok
    - snapchat
    - discord
    - twitch
    - reddit
    - pinterest
    - medium
    - dev
    - stackoverflow
    - behance
    - dribbble
    - website
    - other
    type: string
    x-enum-varnames:
    - PlatformTwitter
    - PlatformLinkedIn
    - PlatformGitHub
    - PlatformFacebook
    - PlatformInstagram
    - PlatformYouTube
    - PlatformTikTok
    - PlatformSnapchat
    - PlatformDiscord
    - PlatformTwitch
    - PlatformReddit
    - PlatformPinterest
    - PlatformMedium
    - PlatformDev
    - PlatformStackOverflow
    - PlatformBehance
    - PlatformDribbble
    - PlatformWebsite
    - PlatformOther
  models.StepAnalytics:
    properties:
      abandon_rate:
        type: number
      average_duration:
        type: integer
      completion_rate:
        type: number
      drop_off_rate:
        type: number
      skip_rate:
        type: number
      step_id:
        type: integer
      step_name:
        type: string
      step_order:
        type: integer
      total_abandons:
        type: integer
      total_completions:
        type: integer
      total_entries:
        type: integer
      total_skips:
        type: integer
    type: object
  models.StepRequest:
    properties:
      auto_advance:
        type: boolean
      completion_conditions:
        additionalProperties: true
        type: object
      component_type:
        maxLength: 100
        type: string
      content:
        additionalProperties: true
        type: object
      description:
        maxLength: 5000
        type: string
      display_name:
        maxLength: 255
        minLength: 1
        type: string
      entry_conditions:
        additionalProperties: true
        type: object
      error_messages:
        additionalProperties: true
        type: object
      estimated_duration:
        type: integer
      help_text:
        maxLength: 5000
        type: string
      is_blocking:
        type: boolean
      is_required:
        type: boolean
      is_skippable:
        type: boolean
      journey_id:
        type: integer
      level:
        minimum: 0
        type: integer
      name:
        maxLength: 255
        minLength: 1
        type: string
      on_complete_actions:
        items:
          additionalProperties: true
          type: object
        type: array
      on_entry_actions:
        items:
          additionalProperties: true
          type: object
        type: array
      on_skip_actions:
        items:
          additionalProperties: true
          type: object
        type: array
      parent_step_id:
        type: integer
      slug:
        maxLength: 255
        minLength: 1
        type: string
      status:
        enum:
        - active
        - inactive
        - draft
        type: string
      step_order:
        minimum: 1
        type: integer
      step_type:
        enum:
        - action
        - form
        - tutorial
        - verification
        - conditional
        - decision
        type: string
      success_message:
        maxLength: 500
        type: string
      timeout_duration:
        type: integer
      title:
        maxLength: 255
        type: string
      ui_config:
        additionalProperties: true
        type: object
      validation_rules:
        additionalProperties: true
        type: object
    required:
    - display_name
    - journey_id
    - name
    - slug
    - step_order
    - step_type
    type: object
  models.StepResponse:
    properties:
      auto_advance:
        type: boolean
      average_duration:
        type: integer
      child_steps:
        items:
          $ref: '#/definitions/models.StepResponse'
        type: array
      completion_rate:
        type: number
      component_type:
        type: string
      content:
        additionalProperties: true
        type: object
      created_at:
        type: string
      description:
        type: string
      display_name:
        type: string
      estimated_duration:
        type: integer
      help_text:
        type: string
      id:
        type: integer
      is_blocking:
        type: boolean
      is_required:
        type: boolean
      is_skippable:
        type: boolean
      journey_id:
        type: integer
      level:
        type: integer
      name:
        type: string
      parent_step_id:
        type: integer
      slug:
        type: string
      status:
        type: string
      step_order:
        type: integer
      step_type:
        type: string
      success_message:
        type: string
      title:
        type: string
      total_completions:
        type: integer
      total_entries:
        type: integer
      total_skips:
        type: integer
      ui_config:
        additionalProperties: true
        type: object
      updated_at:
        type: string
    type: object
  models.StepUpdateRequest:
    properties:
      auto_advance:
        type: boolean
      completion_conditions:
        additionalProperties: true
        type: object
      component_type:
        maxLength: 100
        type: string
      content:
        additionalProperties: true
        type: object
      description:
        maxLength: 5000
        type: string
      display_name:
        maxLength: 255
        minLength: 1
        type: string
      entry_conditions:
        additionalProperties: true
        type: object
      error_messages:
        additionalProperties: true
        type: object
      estimated_duration:
        type: integer
      help_text:
        maxLength: 5000
        type: string
      is_blocking:
        type: boolean
      is_required:
        type: boolean
      is_skippable:
        type: boolean
      level:
        minimum: 0
        type: integer
      name:
        maxLength: 255
        minLength: 1
        type: string
      on_complete_actions:
        items:
          additionalProperties: true
          type: object
        type: array
      on_entry_actions:
        items:
          additionalProperties: true
          type: object
        type: array
      on_skip_actions:
        items:
          additionalProperties: true
          type: object
        type: array
      parent_step_id:
        type: integer
      status:
        enum:
        - active
        - inactive
        - draft
        type: string
      step_order:
        minimum: 1
        type: integer
      step_type:
        enum:
        - action
        - form
        - tutorial
        - verification
        - conditional
        - decision
        type: string
      success_message:
        maxLength: 500
        type: string
      timeout_duration:
        type: integer
      title:
        maxLength: 255
        type: string
      ui_config:
        additionalProperties: true
        type: object
      validation_rules:
        additionalProperties: true
        type: object
    type: object
  models.StorageResult:
    properties:
      cdn_url:
        type: string
      etag:
        type: string
      hash:
        type: string
      metadata:
        additionalProperties: true
        type: object
      path:
        type: string
      size:
        type: integer
      storage_provider:
        type: string
      uploaded_at:
        type: string
      url:
        type: string
    type: object
  models.StorageType:
    enum:
    - local
    - minio
    - s3
    - gcs
    type: string
    x-enum-varnames:
    - StorageTypeLocal
    - StorageTypeMinio
    - StorageTypeS3
    - StorageTypeGCS
  models.TagSource:
    enum:
    - manual
    - auto
    - ai
    - import
    type: string
    x-enum-varnames:
    - TagSourceManual
    - TagSourceAuto
    - TagSourceAI
    - TagSourceImport
  models.TagStatus:
    enum:
    - active
    - archived
    - deleted
    type: string
    x-enum-varnames:
    - TagStatusActive
    - TagStatusArchived
    - TagStatusDeleted
  models.TagType:
    enum:
    - user
    - system
    - auto
    type: string
    x-enum-varnames:
    - TagTypeUser
    - TagTypeSystem
    - TagTypeAuto
  models.TechnicalAnalysis:
    properties:
      has_canonical:
        type: boolean
      has_description:
        type: boolean
      has_open_graph:
        type: boolean
      has_structured_data:
        type: boolean
      has_title:
        type: boolean
      has_twitter_card:
        type: boolean
      issues:
        items:
          type: string
        type: array
      suggestions:
        items:
          type: string
        type: array
    type: object
  models.TemplateRequest:
    properties:
      author:
        maxLength: 255
        type: string
      category:
        enum:
        - general
        - ecommerce
        - saas
        - educational
        - marketing
        - support
        type: string
      compatibility_version:
        maxLength: 20
        type: string
      customization_options:
        additionalProperties: true
        type: object
      default_config:
        additionalProperties: true
        type: object
      demo_url:
        maxLength: 500
        type: string
      description:
        maxLength: 5000
        type: string
      display_name:
        maxLength: 255
        minLength: 1
        type: string
      is_featured:
        type: boolean
      is_public:
        type: boolean
      is_system_template:
        type: boolean
      keywords:
        items:
          type: string
        type: array
      name:
        maxLength: 255
        minLength: 1
        type: string
      preview_data:
        additionalProperties: true
        type: object
      required_fields:
        items:
          type: string
        type: array
      screenshot_url:
        maxLength: 500
        type: string
      slug:
        maxLength: 255
        minLength: 1
        type: string
      status:
        enum:
        - active
        - inactive
        - deprecated
        - draft
        type: string
      style_options:
        additionalProperties: true
        type: object
      tags:
        items:
          type: string
        type: array
      template_data:
        additionalProperties: true
        type: object
      template_type:
        enum:
        - journey
        - step
        - component
        - widget
        - layout
        type: string
      use_case:
        maxLength: 100
        type: string
      validation_rules:
        additionalProperties: true
        type: object
      variable_definitions:
        additionalProperties: true
        type: object
      version:
        maxLength: 20
        type: string
    required:
    - category
    - display_name
    - name
    - slug
    - template_data
    - template_type
    type: object
  models.TemplateResponse:
    properties:
      author:
        type: string
      category:
        type: string
      compatibility_version:
        type: string
      created_at:
        type: string
      customization_options:
        additionalProperties: true
        type: object
      default_config:
        additionalProperties: true
        type: object
      demo_url:
        type: string
      deprecated_at:
        type: string
      description:
        type: string
      display_name:
        type: string
      feedback_count:
        type: integer
      id:
        type: integer
      is_featured:
        type: boolean
      is_public:
        type: boolean
      is_system_template:
        type: boolean
      keywords:
        items:
          type: string
        type: array
      last_used_at:
        type: string
      name:
        type: string
      preview_data:
        additionalProperties: true
        type: object
      published_at:
        type: string
      rating_average:
        type: number
      rating_count:
        type: integer
      required_fields:
        items:
          type: string
        type: array
      screenshot_url:
        type: string
      slug:
        type: string
      status:
        type: string
      style_options:
        additionalProperties: true
        type: object
      tags:
        items:
          type: string
        type: array
      template_data:
        additionalProperties: true
        type: object
      template_type:
        type: string
      tenant_id:
        type: integer
      updated_at:
        type: string
      usage_count:
        type: integer
      use_case:
        type: string
      validation_rules:
        additionalProperties: true
        type: object
      variable_definitions:
        additionalProperties: true
        type: object
      version:
        type: string
    type: object
  models.TemplateType:
    enum:
    - transactional
    - marketing
    - system
    - custom
    type: string
    x-enum-varnames:
    - TemplateTypeTransactional
    - TemplateTypeMarketing
    - TemplateTypeSystem
    - TemplateTypeCustom
  models.TemplateUpdateRequest:
    properties:
      author:
        maxLength: 255
        type: string
      category:
        enum:
        - general
        - ecommerce
        - saas
        - educational
        - marketing
        - support
        type: string
      compatibility_version:
        maxLength: 20
        type: string
      customization_options:
        additionalProperties: true
        type: object
      default_config:
        additionalProperties: true
        type: object
      demo_url:
        maxLength: 500
        type: string
      description:
        maxLength: 5000
        type: string
      display_name:
        maxLength: 255
        minLength: 1
        type: string
      is_featured:
        type: boolean
      is_public:
        type: boolean
      is_system_template:
        type: boolean
      keywords:
        items:
          type: string
        type: array
      name:
        maxLength: 255
        minLength: 1
        type: string
      preview_data:
        additionalProperties: true
        type: object
      required_fields:
        items:
          type: string
        type: array
      screenshot_url:
        maxLength: 500
        type: string
      status:
        enum:
        - active
        - inactive
        - deprecated
        - draft
        type: string
      style_options:
        additionalProperties: true
        type: object
      tags:
        items:
          type: string
        type: array
      template_data:
        additionalProperties: true
        type: object
      template_type:
        enum:
        - journey
        - step
        - component
        - widget
        - layout
        type: string
      use_case:
        maxLength: 100
        type: string
      validation_rules:
        additionalProperties: true
        type: object
      variable_definitions:
        additionalProperties: true
        type: object
      version:
        maxLength: 20
        type: string
    type: object
  models.TenantMembershipStatus:
    enum:
    - active
    - inactive
    - suspended
    - pending
    - deleted
    type: string
    x-enum-varnames:
    - TenantMembershipStatusActive
    - TenantMembershipStatusInactive
    - TenantMembershipStatusSuspended
    - TenantMembershipStatusPending
    - TenantMembershipStatusDeleted
  models.TenantStatus:
    enum:
    - active
    - suspended
    - inactive
    - trial
    - deleted
    type: string
    x-enum-varnames:
    - TenantStatusActive
    - TenantStatusSuspended
    - TenantStatusInactive
    - TenantStatusTrial
    - TenantStatusDeleted
  models.TextAnalysis:
    properties:
      issues:
        items:
          type: string
        type: array
      length:
        type: integer
      score:
        type: integer
      suggestions:
        items:
          type: string
        type: array
      word_count:
        type: integer
    type: object
  models.Theme:
    enum:
    - light
    - dark
    - system
    type: string
    x-enum-varnames:
    - ThemeLight
    - ThemeDark
    - ThemeSystem
  models.ThumbnailStatus:
    enum:
    - generating
    - ready
    - error
    - deleted
    type: string
    x-enum-varnames:
    - ThumbnailStatusGenerating
    - ThumbnailStatusReady
    - ThumbnailStatusError
    - ThumbnailStatusDeleted
  models.TokenStats:
    properties:
      total_active:
        type: integer
      total_created:
        type: integer
      total_expired:
        type: integer
      total_resends:
        type: integer
      total_used:
        type: integer
    type: object
  models.UpdateMediaFileRequest:
    properties:
      alt_text:
        type: string
      category:
        type: string
      description:
        type: string
      filename:
        maxLength: 255
        minLength: 1
        type: string
      folder_id:
        type: integer
      metadata:
        additionalProperties: true
        type: object
      title:
        type: string
      visibility:
        $ref: '#/definitions/models.FileVisibility'
    type: object
  models.UpdateMediaFolderRequest:
    properties:
      color:
        type: string
      description:
        type: string
      icon:
        type: string
      name:
        maxLength: 255
        minLength: 1
        type: string
      parent_id:
        type: integer
      settings:
        additionalProperties: true
        type: object
      visibility:
        $ref: '#/definitions/models.FolderVisibility'
    type: object
  models.UpdateNotificationRequest:
    properties:
      metadata:
        additionalProperties: true
        type: object
      scheduled_at:
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.NotificationStatus'
        enum:
        - pending
        - queued
        - sent
        - delivered
        - failed
        - cancelled
    type: object
  models.UpdateSEOMetaRequest:
    properties:
      additional_meta:
        description: Additional Meta Tags
        items:
          $ref: '#/definitions/models.MetaTag'
        type: array
      canonical_url:
        type: string
      change_frequency:
        enum:
        - always
        - hourly
        - daily
        - weekly
        - monthly
        - yearly
        - never
        type: string
      focus_keyword:
        description: SEO Settings
        maxLength: 255
        type: string
      is_indexed:
        description: Status and Indexing
        type: boolean
      is_sitemap_included:
        type: boolean
      meta_description:
        maxLength: 500
        type: string
      meta_keywords:
        type: string
      meta_robots:
        maxLength: 100
        type: string
      meta_title:
        maxLength: 255
        type: string
      og_description:
        maxLength: 500
        type: string
      og_image:
        type: string
      og_locale:
        maxLength: 10
        type: string
      og_site_name:
        maxLength: 255
        type: string
      og_title:
        description: Open Graph Meta Tags
        maxLength: 255
        type: string
      og_type:
        maxLength: 50
        type: string
      og_url:
        type: string
      priority:
        maximum: 1
        minimum: 0
        type: number
      schema_data:
        $ref: '#/definitions/models.SchemaData'
      schema_type:
        description: Schema.org Structured Data
        maxLength: 100
        type: string
      status:
        description: Status
        enum:
        - active
        - inactive
        - deleted
        type: string
      twitter_card:
        description: Twitter Card Meta Tags
        maxLength: 50
        type: string
      twitter_creator:
        maxLength: 255
        type: string
      twitter_description:
        maxLength: 500
        type: string
      twitter_image:
        type: string
      twitter_site:
        maxLength: 255
        type: string
      twitter_title:
        maxLength: 255
        type: string
    type: object
  models.UpdateTemplateRequest:
    properties:
      description:
        type: string
      is_active:
        type: boolean
      name:
        maxLength: 255
        type: string
      type:
        allOf:
        - $ref: '#/definitions/models.TemplateType'
        enum:
        - transactional
        - marketing
        - system
        - custom
      variables:
        items:
          type: string
        type: array
    type: object
  models.UpdateTemplateVersionRequest:
    properties:
      body_html:
        type: string
      body_text:
        type: string
      is_active:
        type: boolean
      subject:
        maxLength: 255
        type: string
      variables:
        items:
          type: string
        type: array
    type: object
  models.UploadResult:
    properties:
      file:
        $ref: '#/definitions/models.MediaFile'
      processing_jobs:
        items:
          $ref: '#/definitions/models.ProcessingJob'
        type: array
      storage:
        $ref: '#/definitions/models.StorageResult'
      thumbnails:
        items:
          $ref: '#/definitions/models.MediaThumbnail'
        type: array
    type: object
  models.UserAnalytics:
    properties:
      abandoned_journeys:
        type: integer
      average_completion:
        type: number
      average_step_time:
        type: integer
      completed_journeys:
        type: integer
      completed_steps:
        type: integer
      failed_steps:
        type: integer
      in_progress_journeys:
        type: integer
      last_activity:
        type: string
      skipped_steps:
        type: integer
      total_journeys:
        type: integer
      total_steps:
        type: integer
      total_time_spent:
        type: integer
      user_id:
        type: integer
    type: object
  models.UserPreferences:
    properties:
      allow_search:
        type: boolean
      auto_save:
        description: Application Settings
        type: boolean
      created_at:
        description: Timestamps
        type: string
      custom_preferences:
        description: Custom Preferences (JSON for tenant-specific settings)
        type: object
      dashboard_layout:
        type: string
      data_processing_consent:
        type: boolean
      email_notifications:
        description: Notification Preferences
        type: boolean
      feature_preferences:
        description: Feature Flags (JSON for tenant-specific features)
        type: object
      id:
        type: integer
      items_per_page:
        maximum: 100
        minimum: 10
        type: integer
      keyboard_shortcuts:
        type: boolean
      marketing_emails:
        description: Communication Preferences
        type: boolean
      newsletter_subscription:
        type: boolean
      notification_types:
        description: Notification Types (JSON for flexibility)
        type: object
      product_updates:
        type: boolean
      profile_visibility:
        allOf:
        - $ref: '#/definitions/models.ProfileVisibility'
        description: Privacy Settings
        enum:
        - public
        - private
        - friends_only
      push_notifications:
        type: boolean
      security_alerts:
        type: boolean
      show_online_status:
        type: boolean
      sms_notifications:
        type: boolean
      tenant_id:
        type: integer
      theme:
        allOf:
        - $ref: '#/definitions/models.Theme'
        description: UI/UX Preferences
        enum:
        - light
        - dark
        - system
      tooltips_enabled:
        type: boolean
      updated_at:
        type: string
      user:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.User'
        description: Relationships
      user_id:
        type: integer
    type: object
  models.UserProfile:
    properties:
      address_line1:
        description: Address Information
        maxLength: 255
        type: string
      address_line2:
        maxLength: 255
        type: string
      allow_contact:
        type: boolean
      bio:
        description: Personal Information
        maxLength: 1000
        type: string
      birth_date:
        description: Demographics
        type: string
      city:
        maxLength: 100
        type: string
      company:
        maxLength: 255
        type: string
      completion_percentage:
        maximum: 100
        minimum: 0
        type: integer
      country:
        maxLength: 100
        type: string
      created_at:
        description: Timestamps
        type: string
      custom_fields:
        description: Custom Fields (tenant-specific)
        type: object
      department:
        maxLength: 255
        type: string
      display_profile:
        description: Social Information
        type: boolean
      gender:
        allOf:
        - $ref: '#/definitions/models.Gender'
        enum:
        - male
        - female
        - other
        - prefer_not_to_say
      id:
        type: integer
      interests:
        description: JSON array of interests
        items:
          type: string
        type: array
      job_title:
        description: Professional Information
        maxLength: 255
        type: string
      location:
        maxLength: 255
        type: string
      postal_code:
        maxLength: 20
        type: string
      profile_completed:
        description: Profile Completion
        type: boolean
      show_email:
        type: boolean
      show_phone:
        type: boolean
      skills:
        description: JSON array of skills
        items:
          type: string
        type: array
      state:
        maxLength: 100
        type: string
      tenant_id:
        type: integer
      title:
        maxLength: 255
        type: string
      updated_at:
        type: string
      user:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.User'
        description: Relationships
      user_id:
        type: integer
      website:
        type: string
    type: object
  models.UserSocialLink:
    properties:
      created_at:
        description: Timestamps
        type: string
      display_order:
        description: Display Settings
        type: integer
      id:
        type: integer
      is_public:
        type: boolean
      is_verified:
        type: boolean
      platform:
        allOf:
        - $ref: '#/definitions/models.SocialPlatform'
        description: Social Platform Information
        enum:
        - twitter
        - linkedin
        - github
        - facebook
        - instagram
        - youtube
        - tiktok
        - snapchat
        - discord
        - twitch
        - reddit
        - pinterest
        - medium
        - dev
        - stackoverflow
        - behance
        - dribbble
        - website
        - other
      profile_data:
        description: Metadata
        type: object
      tenant_id:
        type: integer
      updated_at:
        type: string
      url:
        type: string
      user:
        allOf:
        - $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.User'
        description: Relationships
      user_id:
        type: integer
      username:
        maxLength: 255
        minLength: 1
        type: string
      verified_at:
        type: string
    required:
    - platform
    - url
    - username
    type: object
  models.UserSocialLinkOrder:
    properties:
      display_order:
        type: integer
      id:
        minimum: 1
        type: integer
    required:
    - id
    type: object
  models.ValidationSuggestion:
    properties:
      context:
        type: string
      description:
        type: string
      field:
        type: string
      impact:
        type: string
      type:
        type: string
    type: object
  models.WebsiteStatus:
    enum:
    - active
    - inactive
    - suspended
    type: string
    x-enum-varnames:
    - WebsiteStatusActive
    - WebsiteStatusInactive
    - WebsiteStatusSuspended
  models.WebsiteUpdateRequest:
    properties:
      active_theme:
        maxLength: 100
        type: string
      custom_css:
        type: string
      custom_js:
        type: string
      description:
        type: string
      domain:
        type: string
      facebook_pixel_id:
        maxLength: 50
        type: string
      favicon:
        type: string
      google_analytics_id:
        maxLength: 50
        type: string
      google_tag_manager_id:
        maxLength: 50
        type: string
      language:
        maxLength: 10
        minLength: 2
        type: string
      name:
        maxLength: 255
        minLength: 1
        type: string
      site_logo:
        type: string
      social_media:
        $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_website_models.JSONMap'
      status:
        allOf:
        - $ref: '#/definitions/models.WebsiteStatus'
        enum:
        - active
        - inactive
        - suspended
      subdomain:
        maxLength: 100
        minLength: 2
        type: string
      timezone:
        maxLength: 50
        minLength: 3
        type: string
    type: object
  pagination.CursorResponse:
    properties:
      count:
        type: integer
      has_more:
        description: Alias for HasNext
        type: boolean
      has_next:
        type: boolean
      has_previous:
        type: boolean
      limit:
        type: integer
      next_cursor:
        type: string
      previous_cursor:
        type: string
    type: object
  response.PaginatedResponse:
    properties:
      items: {}
      page:
        type: integer
      page_size:
        type: integer
      pages:
        type: integer
      total:
        type: integer
    type: object
  response.Response:
    properties:
      data: {}
      status:
        $ref: '#/definitions/response.Status'
    type: object
  response.Status:
    properties:
      code:
        type: integer
      details: {}
      error_code:
        type: string
      message:
        type: string
      path:
        type: string
      success:
        type: boolean
      timestamp:
        type: string
    type: object
  services.AcceptInvitationInput:
    properties:
      token:
        type: string
    required:
    - token
    type: object
  services.ActionActivity:
    properties:
      action:
        type: string
      date:
        type: string
      result:
        type: string
      target:
        type: string
    type: object
  services.ActivityPoint:
    properties:
      active:
        type: integer
      date:
        type: string
      logins:
        type: integer
    type: object
  services.AddTenantMemberInput:
    properties:
      display_name:
        maxLength: 255
        minLength: 1
        type: string
      invited_by:
        type: integer
      local_username:
        maxLength: 30
        minLength: 3
        type: string
      role:
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.TenantMembershipStatus'
        enum:
        - active
        - inactive
        - suspended
        - pending
      tenant_id:
        type: integer
      user_id:
        type: integer
    required:
    - role
    - tenant_id
    - user_id
    type: object
  services.Cohort:
    properties:
      date:
        type: string
      retention:
        items:
          type: number
        type: array
      size:
        type: integer
    type: object
  services.CreateInvitationInput:
    properties:
      email:
        type: string
      expires_in:
        description: Hours, max 1 year
        maximum: 8760
        minimum: 1
        type: integer
      message:
        maxLength: 1000
        type: string
      role_id:
        minimum: 1
        type: integer
      tenant_id:
        minimum: 1
        type: integer
      website_id:
        minimum: 1
        type: integer
    required:
    - email
    - tenant_id
    type: object
  services.CreateUserPreferencesInput:
    properties:
      allow_search:
        type: boolean
      auto_save:
        type: boolean
      custom_preferences:
        additionalProperties: true
        type: object
      dashboard_layout:
        type: string
      data_processing_consent:
        type: boolean
      email_notifications:
        type: boolean
      feature_preferences:
        additionalProperties: true
        type: object
      items_per_page:
        maximum: 100
        minimum: 10
        type: integer
      keyboard_shortcuts:
        type: boolean
      marketing_emails:
        type: boolean
      newsletter_subscription:
        type: boolean
      notification_types:
        additionalProperties:
          type: boolean
        type: object
      product_updates:
        type: boolean
      profile_visibility:
        allOf:
        - $ref: '#/definitions/models.ProfileVisibility'
        enum:
        - public
        - private
        - friends_only
      push_notifications:
        type: boolean
      security_alerts:
        type: boolean
      show_online_status:
        type: boolean
      sms_notifications:
        type: boolean
      theme:
        allOf:
        - $ref: '#/definitions/models.Theme'
        enum:
        - light
        - dark
        - system
      tooltips_enabled:
        type: boolean
      user_id:
        type: integer
    required:
    - user_id
    type: object
  services.CreateUserProfileInput:
    properties:
      address:
        maxLength: 200
        type: string
      allow_contact:
        type: boolean
      bio:
        maxLength: 500
        type: string
      birth_date:
        type: string
      city:
        maxLength: 100
        type: string
      company:
        maxLength: 100
        type: string
      country:
        maxLength: 100
        type: string
      custom_fields:
        additionalProperties: true
        type: object
      department:
        maxLength: 100
        type: string
      display_profile:
        type: boolean
      gender:
        enum:
        - male
        - female
        - other
        - prefer_not_to_say
        type: string
      interests:
        items:
          type: string
        type: array
      job_title:
        maxLength: 100
        type: string
      location:
        maxLength: 100
        type: string
      postal_code:
        maxLength: 20
        type: string
      show_email:
        type: boolean
      show_phone:
        type: boolean
      skills:
        items:
          type: string
        type: array
      state:
        maxLength: 100
        type: string
      title:
        maxLength: 100
        type: string
      user_id:
        type: integer
      website:
        type: string
    required:
    - user_id
    type: object
  services.CreateUserSocialLinkInput:
    properties:
      display_order:
        type: integer
      is_public:
        type: boolean
      platform:
        allOf:
        - $ref: '#/definitions/models.SocialPlatform'
        enum:
        - twitter
        - linkedin
        - github
        - facebook
        - instagram
        - youtube
        - tiktok
        - snapchat
        - discord
        - twitch
        - reddit
        - pinterest
        - medium
        - dev
        - stackoverflow
        - behance
        - dribbble
        - website
        - other
      profile_data:
        additionalProperties: true
        type: object
      url:
        type: string
      user_id:
        type: integer
      username:
        maxLength: 255
        minLength: 1
        type: string
    required:
    - platform
    - url
    - user_id
    - username
    type: object
  services.CreateWebsiteOnboardingRequest:
    properties:
      category:
        maxLength: 100
        type: string
      custom_data:
        additionalProperties: true
        type: object
      description:
        maxLength: 500
        type: string
      domain:
        type: string
      favicon:
        type: string
      language:
        maxLength: 10
        minLength: 2
        type: string
      name:
        maxLength: 255
        minLength: 1
        type: string
      site_logo:
        type: string
      social_media:
        additionalProperties: true
        type: object
      subdomain:
        maxLength: 100
        minLength: 2
        type: string
      tags:
        items:
          type: string
        type: array
      template_id:
        minimum: 1
        type: integer
      theme:
        maxLength: 100
        type: string
      timezone:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - name
    type: object
  services.DemographicStats:
    properties:
      age_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      device_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      gender_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      language_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      location_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      timezone_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
    type: object
  services.EngagementStats:
    properties:
      average_actions_per_user:
        type: number
      average_session_time:
        type: number
      bounce_rate:
        type: number
      daily_active_users:
        type: integer
      monthly_active_users:
        type: integer
      period:
        type: string
      return_rate:
        type: number
      total_active_users:
        type: integer
      weekly_active_users:
        type: integer
    type: object
  services.LoginActivity:
    properties:
      date:
        type: string
      ip_address:
        type: string
      success:
        type: boolean
      user_agent:
        type: string
    type: object
  services.NotificationPreferencesInput:
    properties:
      email_notifications:
        type: boolean
      marketing_emails:
        type: boolean
      newsletter_subscription:
        type: boolean
      notification_types:
        additionalProperties:
          type: boolean
        type: object
      product_updates:
        type: boolean
      push_notifications:
        type: boolean
      security_alerts:
        type: boolean
      sms_notifications:
        type: boolean
    type: object
  services.NotificationStats:
    properties:
      email_enabled:
        type: integer
      marketing_allow:
        type: integer
      newsletter_allow:
        type: integer
      push_enabled:
        type: integer
      security_allow:
        type: integer
      sms_enabled:
        type: integer
    type: object
  services.PreferencesStats:
    properties:
      language_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      notification_stats:
        $ref: '#/definitions/services.NotificationStats'
      privacy_stats:
        $ref: '#/definitions/services.PrivacyStats'
      theme_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      timezone_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      total_preferences:
        type: integer
    type: object
  services.PrivacyPreferencesInput:
    properties:
      allow_search:
        type: boolean
      data_processing_consent:
        type: boolean
      profile_visibility:
        allOf:
        - $ref: '#/definitions/models.ProfileVisibility'
        enum:
        - public
        - private
        - friends_only
      show_online_status:
        type: boolean
    type: object
  services.PrivacyStats:
    properties:
      friends_profiles:
        type: integer
      online_status_show:
        type: integer
      private_profiles:
        type: integer
      public_profiles:
        type: integer
      search_allow:
        type: integer
    type: object
  services.ProfileCompletionStats:
    properties:
      average_completion:
        type: number
      completed_profiles:
        type: integer
      completion_rate:
        type: number
      incomplete_profiles:
        type: integer
      profiles_with_avatar:
        type: integer
      profiles_with_bio:
        type: integer
      profiles_with_company:
        type: integer
      profiles_with_location:
        type: integer
      profiles_with_skills:
        type: integer
      total_profiles:
        type: integer
    type: object
  services.RegistrationPoint:
    properties:
      count:
        type: integer
      date:
        type: string
    type: object
  services.RegistrationStats:
    properties:
      devices:
        additionalProperties:
          format: int64
          type: integer
        type: object
      locations:
        additionalProperties:
          format: int64
          type: integer
        type: object
      period:
        type: string
      sources:
        additionalProperties:
          format: int64
          type: integer
        type: object
      total_registrations:
        type: integer
      trend:
        items:
          $ref: '#/definitions/services.RegistrationPoint'
        type: array
    type: object
  services.RejectInvitationInput:
    properties:
      token:
        type: string
    required:
    - token
    type: object
  services.RetentionStats:
    properties:
      churn_rate:
        type: number
      cohort_data:
        items:
          $ref: '#/definitions/services.Cohort'
        type: array
      day1_retention:
        type: number
      day7_retention:
        type: number
      day30_retention:
        type: number
      period:
        type: string
    type: object
  services.SessionActivity:
    properties:
      actions:
        type: integer
      date:
        type: string
      duration:
        description: in seconds
        type: integer
      ip_address:
        type: string
    type: object
  services.SocialPlatformStats:
    properties:
      average_links_per_user:
        type: number
      platform_counts:
        additionalProperties:
          format: int64
          type: integer
        type: object
      private_links:
        type: integer
      public_links:
        type: integer
      public_rates:
        additionalProperties:
          format: float64
          type: number
        type: object
      total_links:
        type: integer
      unverified_links:
        type: integer
      users_with_links:
        type: integer
      verification_rates:
        additionalProperties:
          format: float64
          type: number
        type: object
      verified_links:
        type: integer
    type: object
  services.TenantMembershipListResponse:
    properties:
      memberships:
        items:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.TenantMembership'
        type: array
      pagination:
        $ref: '#/definitions/pagination.CursorResponse'
      total:
        type: integer
    type: object
  services.TenantMembershipStats:
    properties:
      active_members:
        type: integer
      deleted_members:
        type: integer
      inactive_members:
        type: integer
      pending_members:
        type: integer
      recent_activity:
        items:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.TenantMembership'
        type: array
      recent_joins:
        items:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.TenantMembership'
        type: array
      status_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      suspended_members:
        type: integer
      total_members:
        type: integer
    type: object
  services.TwoFactorSetup:
    properties:
      backup_codes:
        items:
          type: string
        type: array
      qr_code_url:
        type: string
      secret:
        type: string
    type: object
  services.UIPreferencesInput:
    properties:
      auto_save:
        type: boolean
      dashboard_layout:
        type: string
      items_per_page:
        maximum: 100
        minimum: 10
        type: integer
      keyboard_shortcuts:
        type: boolean
      theme:
        allOf:
        - $ref: '#/definitions/models.Theme'
        enum:
        - light
        - dark
        - system
      tooltips_enabled:
        type: boolean
    type: object
  services.UpdateInvitationInput:
    properties:
      expires_at:
        type: string
      message:
        maxLength: 1000
        type: string
      role_id:
        minimum: 1
        type: integer
    type: object
  services.UpdateMemberRoleInput:
    properties:
      display_name:
        maxLength: 255
        minLength: 1
        type: string
      local_username:
        maxLength: 30
        minLength: 3
        type: string
      role:
        type: string
    required:
    - role
    type: object
  services.UpdateUserPreferencesInput:
    properties:
      allow_search:
        type: boolean
      auto_save:
        type: boolean
      custom_preferences:
        additionalProperties: true
        type: object
      dashboard_layout:
        type: string
      data_processing_consent:
        type: boolean
      email_notifications:
        type: boolean
      feature_preferences:
        additionalProperties: true
        type: object
      items_per_page:
        maximum: 100
        minimum: 10
        type: integer
      keyboard_shortcuts:
        type: boolean
      marketing_emails:
        type: boolean
      newsletter_subscription:
        type: boolean
      notification_types:
        additionalProperties:
          type: boolean
        type: object
      product_updates:
        type: boolean
      profile_visibility:
        allOf:
        - $ref: '#/definitions/models.ProfileVisibility'
        enum:
        - public
        - private
        - friends_only
      push_notifications:
        type: boolean
      security_alerts:
        type: boolean
      show_online_status:
        type: boolean
      sms_notifications:
        type: boolean
      theme:
        allOf:
        - $ref: '#/definitions/models.Theme'
        enum:
        - light
        - dark
        - system
      tooltips_enabled:
        type: boolean
    type: object
  services.UpdateUserProfileInput:
    properties:
      address:
        maxLength: 200
        type: string
      allow_contact:
        type: boolean
      bio:
        maxLength: 500
        type: string
      birth_date:
        type: string
      city:
        maxLength: 100
        type: string
      company:
        maxLength: 100
        type: string
      country:
        maxLength: 100
        type: string
      custom_fields:
        additionalProperties: true
        type: object
      department:
        maxLength: 100
        type: string
      display_profile:
        type: boolean
      gender:
        enum:
        - male
        - female
        - other
        - prefer_not_to_say
        type: string
      interests:
        items:
          type: string
        type: array
      job_title:
        maxLength: 100
        type: string
      location:
        maxLength: 100
        type: string
      postal_code:
        maxLength: 20
        type: string
      show_email:
        type: boolean
      show_phone:
        type: boolean
      skills:
        items:
          type: string
        type: array
      state:
        maxLength: 100
        type: string
      title:
        maxLength: 100
        type: string
      website:
        type: string
    type: object
  services.UpdateUserSocialLinkInput:
    properties:
      display_order:
        type: integer
      is_public:
        type: boolean
      profile_data:
        additionalProperties: true
        type: object
      url:
        type: string
      username:
        maxLength: 255
        minLength: 1
        type: string
    type: object
  services.UserActivity:
    properties:
      action_history:
        items:
          $ref: '#/definitions/services.ActionActivity'
        type: array
      average_session_duration:
        type: number
      last_activity:
        type: string
      login_history:
        items:
          $ref: '#/definitions/services.LoginActivity'
        type: array
      session_history:
        items:
          $ref: '#/definitions/services.SessionActivity'
        type: array
      total_actions:
        type: integer
      total_logins:
        type: integer
      total_sessions:
        type: integer
      user_id:
        type: integer
    type: object
  services.UserListResponse:
    properties:
      pagination:
        $ref: '#/definitions/pagination.CursorResponse'
      total:
        type: integer
      users:
        items:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.User'
        type: array
    type: object
  services.UserProfileListResponse:
    properties:
      pagination:
        $ref: '#/definitions/pagination.CursorResponse'
      profiles:
        items:
          $ref: '#/definitions/models.UserProfile'
        type: array
      total:
        type: integer
    type: object
  services.UserSearchResponse:
    properties:
      pagination:
        $ref: '#/definitions/pagination.CursorResponse'
      query:
        type: string
      total:
        type: integer
      users:
        items:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.User'
        type: array
    type: object
  services.UserStats:
    properties:
      active_users:
        type: integer
      activity_trend:
        items:
          $ref: '#/definitions/services.ActivityPoint'
        type: array
      average_login_count:
        type: number
      deleted_users:
        type: integer
      inactive_users:
        type: integer
      recently_active_count:
        type: integer
      registration_trend:
        items:
          $ref: '#/definitions/services.RegistrationPoint'
        type: array
      role_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      status_distribution:
        additionalProperties:
          format: int64
          type: integer
        type: object
      suspended_users:
        type: integer
      total_users:
        type: integer
      two_factor_users:
        type: integer
      unverified_users:
        type: integer
      verified_users:
        type: integer
    type: object
  services.VerificationStatus:
    properties:
      email_verified:
        type: boolean
      email_verified_at:
        type: string
      last_email_sent:
        type: string
      last_phone_sent:
        type: string
      phone_verified:
        type: boolean
      phone_verified_at:
        type: string
      user_id:
        type: integer
    type: object
info:
  contact: {}
paths:
  /2fa/complete-login:
    post:
      consumes:
      - application/json
      description: Complete login process when user has 2FA enabled
      parameters:
      - description: Complete login with 2FA code
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dto.CompleteTwoFactorLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Login completed successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.CompleteTwoFactorLoginResponse'
              type: object
        "400":
          description: Invalid request or 2FA code
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication failed
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Complete login with 2FA
      tags:
      - auth
  /2fa/disable:
    post:
      consumes:
      - application/json
      description: Disable 2FA for the authenticated user with verification code
      parameters:
      - description: Disable 2FA request with verification code
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dto.DisableTwoFactorRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Two-factor authentication disabled
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.DisableTwoFactorResponse'
              type: object
        "400":
          description: Invalid request or verification code
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: Disable two-factor authentication
      tags:
      - auth
  /2fa/enable:
    post:
      consumes:
      - application/json
      description: Enable 2FA for the authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: Two-factor authentication enabled
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.EnableTwoFactorResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: Enable two-factor authentication
      tags:
      - auth
  /2fa/verify:
    post:
      consumes:
      - application/json
      description: Verify 2FA code for the authenticated user
      parameters:
      - description: 2FA verification code
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dto.VerifyTwoFactorRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Two-factor authentication verified
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.VerifyTwoFactorResponse'
              type: object
        "400":
          description: Invalid verification code
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: Verify two-factor authentication code
      tags:
      - auth
  /api/cms/v1/apikeys:
    post:
      consumes:
      - application/json
      description: Creates a new API key with the provided configuration
      parameters:
      - description: API key creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.CreateAPIKeyRequest'
      produces:
      - application/json
      responses:
        "201":
          description: API key created successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.APIKeyResponse'
              type: object
        "400":
          description: Invalid request body or missing tenant/website ID
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to create API key
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Create a new API key
      tags:
      - API Keys
  /api/cms/v1/auth/forgot-password:
    post:
      consumes:
      - application/json
      description: Send password reset email to user
      parameters:
      - description: Forgot password request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dto.ForgotPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password reset email sent successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.ForgotPasswordResponse'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Send password reset email
      tags:
      - auth
  /api/cms/v1/auth/login:
    post:
      consumes:
      - application/json
      description: Authenticates a user and returns access and refresh tokens
      parameters:
      - description: Login credentials
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dto.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.LoginResponse'
              type: object
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Invalid email or password
          schema:
            $ref: '#/definitions/response.Response'
        "403":
          description: Email not verified or account inactive/suspended
          schema:
            $ref: '#/definitions/response.Response'
        "429":
          description: Account locked due to too many failed attempts
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      summary: User login
      tags:
      - auth
  /api/cms/v1/auth/logout:
    post:
      consumes:
      - application/json
      description: Logs out the current user and invalidates their session
      produces:
      - application/json
      responses:
        "200":
          description: Logout successful
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: object
              type: object
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: User logout
      tags:
      - auth
  /api/cms/v1/auth/logout-all:
    post:
      consumes:
      - application/json
      description: Logout user from all active sessions/devices
      produces:
      - application/json
      responses:
        "200":
          description: Logged out from all devices successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.LogoutAllDevicesResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: Logout from all devices
      tags:
      - auth
  /api/cms/v1/auth/refresh:
    post:
      consumes:
      - application/json
      description: Exchanges a refresh token for new access and refresh tokens
      parameters:
      - description: Refresh token request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dto.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Token refresh successful
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.RefreshTokenResponse'
              type: object
        "401":
          description: Invalid refresh token
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Refresh access token
      tags:
      - auth
  /api/cms/v1/auth/refresh-with-tenant:
    post:
      consumes:
      - application/json
      description: Refresh access token while maintaining current tenant context
      parameters:
      - description: Token refresh request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.TokenRefreshRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Token refreshed successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.TokenRefreshResponse'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Invalid refresh token
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: Refresh token with tenant context
      tags:
      - Authentication - Multi-Tenant
  /api/cms/v1/auth/register:
    post:
      consumes:
      - application/json
      description: Registers a new user and sends an email verification
      parameters:
      - description: Register request body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dto.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Registration successful
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.RegisterResponse'
              type: object
        "400":
          description: Invalid request format or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "409":
          description: Email already exists
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Register a new user
      tags:
      - auth
  /api/cms/v1/auth/resend-verification:
    post:
      consumes:
      - application/json
      description: Resend verification email to user (rate limited)
      parameters:
      - description: Resend verification request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dto.ResendVerificationEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Verification email sent successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.ResendVerificationEmailResponse'
              type: object
        "400":
          description: Invalid request or rate limited
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Resend email verification
      tags:
      - auth
  /api/cms/v1/auth/reset-password:
    post:
      consumes:
      - application/json
      description: Reset user password using reset token
      parameters:
      - description: Reset password request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dto.ResetPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password reset successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.ResetPasswordResponse'
              type: object
        "400":
          description: Invalid request or token
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Reset password with token
      tags:
      - auth
  /api/cms/v1/auth/verify-email:
    post:
      consumes:
      - application/json
      description: Verify user's email address using verification token
      parameters:
      - description: Verification request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.VerifyEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Email verified successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.VerifyEmailResponse'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Verify email address
      tags:
      - Authentication - Email
  /api/cms/v1/blog/posts:
    post:
      consumes:
      - application/json
      description: Creates a new blog post with the provided data
      parameters:
      - description: Blog post data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.BlogPostCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Blog post created successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.BlogPostResponse'
              type: object
        "400":
          description: Invalid request body
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to create post
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Create a new blog post
      tags:
      - Blog Posts
  /api/cms/v1/blog/posts/{id}:
    get:
      consumes:
      - application/json
      description: Retrieves a single blog post by its ID
      parameters:
      - description: Blog post ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Blog post retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.BlogPostResponse'
              type: object
        "400":
          description: Invalid post ID
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Post not found
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Get a blog post by ID
      tags:
      - Blog Posts
    put:
      consumes:
      - application/json
      description: Updates an existing blog post with the provided data
      parameters:
      - description: Blog post ID
        in: path
        name: id
        required: true
        type: integer
      - description: Blog post update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.BlogPostUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Blog post updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.BlogPostResponse'
              type: object
        "400":
          description: Invalid request body or post ID
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Post not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to update post
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Update a blog post
      tags:
      - Blog Posts
  /api/cms/v1/onboarding/admin/analytics/completion-rates:
    get:
      consumes:
      - application/json
      description: Get system-wide onboarding completion rates
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get onboarding completion rates (Admin)
      tags:
      - onboarding-admin
  /api/cms/v1/onboarding/admin/analytics/dropoff-points:
    get:
      consumes:
      - application/json
      description: Get system-wide onboarding dropoff analysis
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get onboarding dropoff points (Admin)
      tags:
      - onboarding-admin
  /api/cms/v1/onboarding/admin/analytics/user-segments:
    get:
      consumes:
      - application/json
      description: Get user segmentation based on onboarding behavior
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get user segments analysis (Admin)
      tags:
      - onboarding-admin
  /api/cms/v1/onboarding/admin/users/{user_id}/force-complete:
    post:
      consumes:
      - application/json
      description: Force complete specific user's onboarding
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Force complete user onboarding (Admin)
      tags:
      - onboarding-admin
  /api/cms/v1/onboarding/admin/users/{user_id}/reset:
    post:
      consumes:
      - application/json
      description: Reset specific user's onboarding progress
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Reset user onboarding (Admin)
      tags:
      - onboarding-admin
  /api/cms/v1/onboarding/admin/users/{user_id}/status:
    get:
      consumes:
      - application/json
      description: Get specific user's onboarding status
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get user onboarding status (Admin)
      tags:
      - onboarding-admin
  /api/cms/v1/onboarding/my/complete-step:
    post:
      consumes:
      - application/json
      description: Complete current user's current onboarding step
      parameters:
      - description: Step completion data
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Complete my current onboarding step
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/current-step:
    get:
      consumes:
      - application/json
      description: Get current user's current onboarding step
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get my current onboarding step
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/next-step:
    get:
      consumes:
      - application/json
      description: Get current user's next onboarding step
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get my next onboarding step
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/organization:
    post:
      consumes:
      - application/json
      description: Create organization for current user
      parameters:
      - description: Organization data
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create my organization
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/organization-status:
    get:
      consumes:
      - application/json
      description: Get current user's organization status from JWT context
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get my organization status
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/progress:
    get:
      consumes:
      - application/json
      description: Get current user's onboarding progress
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get my onboarding progress
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/skip-step:
    post:
      consumes:
      - application/json
      description: Skip current user's current onboarding step
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Skip my current onboarding step
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/start:
    post:
      consumes:
      - application/json
      description: Start onboarding for current user
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Start my onboarding journey
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/status:
    get:
      consumes:
      - application/json
      description: Get current user's onboarding status from JWT context
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get my onboarding status
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/templates:
    get:
      consumes:
      - application/json
      description: Get templates available for current user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get my available templates
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/website:
    get:
      consumes:
      - application/json
      description: Get detailed information about current user's website
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get my website details
      tags:
      - onboarding-my
    post:
      consumes:
      - application/json
      description: Create website for current user during onboarding
      parameters:
      - description: Website data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/services.CreateWebsiteOnboardingRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create my website
      tags:
      - onboarding-my
    put:
      consumes:
      - application/json
      description: Update current user's website information
      parameters:
      - description: Website update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.WebsiteUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update my website
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/website-status:
    get:
      consumes:
      - application/json
      description: Get website setup status for current user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get my website status
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/website-templates:
    get:
      consumes:
      - application/json
      description: Get available website templates for onboarding
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get website templates
      tags:
      - onboarding-my
  /api/cms/v1/onboarding/my/website-templates/{template_id}/preview:
    get:
      consumes:
      - application/json
      description: Preview a specific website template
      parameters:
      - description: Template ID
        in: path
        name: template_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Preview website template
      tags:
      - onboarding-my
  /api/cms/v1/setting-schemas:
    get:
      consumes:
      - application/json
      description: Get setting schemas with optional filters
      parameters:
      - description: Category filter
        in: query
        name: category
        type: string
      - description: Data type filter
        enum:
        - string
        - number
        - boolean
        - json
        - array
        in: query
        name: data_type
        type: string
      - description: Public schemas only
        in: query
        name: is_public
        type: boolean
      - description: Search in name and description
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SettingSchemaResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get setting schemas
      tags:
      - Setting Schemas
    post:
      consumes:
      - application/json
      description: Create a new setting schema
      parameters:
      - description: Schema create request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.SettingSchemaCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SettingSchemaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Create setting schema
      tags:
      - Setting Schemas
  /api/cms/v1/setting-schemas/{category}/{key}:
    get:
      consumes:
      - application/json
      description: Get a specific schema by category and key
      parameters:
      - description: Category name
        in: path
        name: category
        required: true
        type: string
      - description: Setting key
        in: path
        name: key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SettingSchemaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get schema by category and key
      tags:
      - Setting Schemas
  /api/cms/v1/setting-schemas/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a setting schema
      parameters:
      - description: Schema ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Delete setting schema
      tags:
      - Setting Schemas
    get:
      consumes:
      - application/json
      description: Get a specific setting schema by ID
      parameters:
      - description: Schema ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SettingSchemaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get setting schema by ID
      tags:
      - Setting Schemas
    put:
      consumes:
      - application/json
      description: Update an existing setting schema
      parameters:
      - description: Schema ID
        in: path
        name: id
        required: true
        type: integer
      - description: Schema update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.SettingSchemaUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SettingSchemaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Update setting schema
      tags:
      - Setting Schemas
  /api/cms/v1/setting-schemas/category/{category}:
    get:
      consumes:
      - application/json
      description: Get all schemas in a specific category
      parameters:
      - description: Category name
        in: path
        name: category
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SettingSchemaResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get schemas by category
      tags:
      - Setting Schemas
  /api/cms/v1/settings:
    get:
      consumes:
      - application/json
      description: Get settings by scope and filters
      parameters:
      - description: Tenant ID
        in: header
        name: X-Tenant-ID
        required: true
        type: string
      - description: Scope type
        enum:
        - global
        - tenant
        - website
        - user
        in: query
        name: scope_type
        required: true
        type: string
      - description: Scope ID
        in: query
        name: scope_id
        type: integer
      - description: Category filter
        in: query
        name: category
        type: string
      - description: Key filter
        in: query
        name: key
        type: string
      - description: Public settings only
        in: query
        name: is_public
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SettingResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get settings
      tags:
      - Settings
    post:
      consumes:
      - application/json
      description: Create a new setting
      parameters:
      - description: Tenant ID
        in: header
        name: X-Tenant-ID
        required: true
        type: string
      - description: Setting create request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.SettingCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SettingResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Create setting
      tags:
      - Settings
  /api/cms/v1/settings/{category}/{key}/value:
    get:
      consumes:
      - application/json
      description: Get the value of a specific setting by category and key
      parameters:
      - description: Tenant ID
        in: header
        name: X-Tenant-ID
        required: true
        type: string
      - description: Category name
        in: path
        name: category
        required: true
        type: string
      - description: Setting key
        in: path
        name: key
        required: true
        type: string
      - description: Scope type
        enum:
        - global
        - tenant
        - website
        - user
        in: query
        name: scope_type
        required: true
        type: string
      - description: Scope ID
        in: query
        name: scope_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data: {}
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get setting value
      tags:
      - Settings
    put:
      consumes:
      - application/json
      description: Set the value of a specific setting by category and key
      parameters:
      - description: Tenant ID
        in: header
        name: X-Tenant-ID
        required: true
        type: string
      - description: Category name
        in: path
        name: category
        required: true
        type: string
      - description: Setting key
        in: path
        name: key
        required: true
        type: string
      - description: Setting value request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.SetSettingValueRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data: {}
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Set setting value
      tags:
      - Settings
  /api/cms/v1/settings/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a setting
      parameters:
      - description: Tenant ID
        in: header
        name: X-Tenant-ID
        required: true
        type: string
      - description: Setting ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Delete setting
      tags:
      - Settings
    get:
      consumes:
      - application/json
      description: Get a specific setting by ID
      parameters:
      - description: Tenant ID
        in: header
        name: X-Tenant-ID
        required: true
        type: string
      - description: Setting ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SettingResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get setting by ID
      tags:
      - Settings
    put:
      consumes:
      - application/json
      description: Update an existing setting
      parameters:
      - description: Tenant ID
        in: header
        name: X-Tenant-ID
        required: true
        type: string
      - description: Setting ID
        in: path
        name: id
        required: true
        type: integer
      - description: Setting update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.SettingUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SettingResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Update setting
      tags:
      - Settings
  /api/cms/v1/settings/categories:
    get:
      consumes:
      - application/json
      description: Get all available setting categories
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_settings_models.SettingCategory'
                  type: array
              type: object
      summary: Get setting categories
      tags:
      - Settings
  /api/cms/v1/settings/category/{category}:
    get:
      consumes:
      - application/json
      description: Get all settings in a specific category
      parameters:
      - description: Tenant ID
        in: header
        name: X-Tenant-ID
        required: true
        type: string
      - description: Category name
        in: path
        name: category
        required: true
        type: string
      - description: Scope type
        enum:
        - global
        - tenant
        - website
        - user
        in: query
        name: scope_type
        required: true
        type: string
      - description: Scope ID
        in: query
        name: scope_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SettingResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get settings by category
      tags:
      - Settings
  /api/cms/v1/tenant-plans:
    post:
      consumes:
      - application/json
      description: Create a new tenant plan with the provided information
      parameters:
      - description: Plan creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.TenantPlanCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Plan created successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.TenantPlanResponse'
              type: object
        "400":
          description: Invalid request body or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to create plan
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Create a new tenant plan
      tags:
      - Tenant Plans
  /api/cms/v1/tenant-plans/{id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific tenant plan
      parameters:
      - description: Plan ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Plan retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.TenantPlanResponse'
              type: object
        "400":
          description: Invalid plan ID
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Plan not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to get plan
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Get tenant plan by ID
      tags:
      - Tenant Plans
    put:
      consumes:
      - application/json
      description: Update tenant plan information
      parameters:
      - description: Plan ID
        in: path
        name: id
        required: true
        type: integer
      - description: Plan update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.TenantPlanUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Plan updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.TenantPlanResponse'
              type: object
        "400":
          description: Invalid request body or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Plan not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to update plan
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Update a tenant plan
      tags:
      - Tenant Plans
  /api/cms/v1/tenants:
    post:
      consumes:
      - application/json
      description: Create a new tenant with the provided information
      parameters:
      - description: Tenant creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.TenantCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Tenant created successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.TenantResponse'
              type: object
        "400":
          description: Invalid request body or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to create tenant
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Create a new tenant
      tags:
      - Tenants
  /api/cms/v1/tenants/{id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific tenant
      parameters:
      - description: Tenant ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Tenant retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.TenantResponse'
              type: object
        "400":
          description: Invalid tenant ID
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Tenant not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to get tenant
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Get tenant by ID
      tags:
      - Tenants
    put:
      consumes:
      - application/json
      description: Update tenant information
      parameters:
      - description: Tenant ID
        in: path
        name: id
        required: true
        type: integer
      - description: Tenant update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.TenantUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Tenant updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.TenantResponse'
              type: object
        "400":
          description: Invalid request body or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Tenant not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to update tenant
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Update a tenant
      tags:
      - Tenants
  /api/cms/v1/tenants/{id}/assign-plan:
    post:
      consumes:
      - application/json
      description: Assign a specific plan to a tenant
      parameters:
      - description: Tenant ID
        in: path
        name: id
        required: true
        type: integer
      - description: Plan assignment data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.TenantAssignPlanRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Plan assigned successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
        "400":
          description: Invalid request body or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Tenant or plan not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to assign plan
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Assign plan to tenant
      tags:
      - Tenants
  /api/cms/v1/tenants/{id}/status:
    patch:
      consumes:
      - application/json
      description: Update the status of a tenant
      parameters:
      - description: Tenant ID
        in: path
        name: id
        required: true
        type: integer
      - description: Status update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.TenantUpdateStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Status updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
        "400":
          description: Invalid request body or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Tenant not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to update tenant status
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Update tenant status
      tags:
      - Tenants
  /api/cms/v1/tenants/domain/{domain}:
    get:
      consumes:
      - application/json
      description: Get tenant information by domain name
      parameters:
      - description: Domain name
        in: path
        name: domain
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Tenant retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.TenantResponse'
              type: object
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Tenant not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to get tenant
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Get tenant by domain
      tags:
      - Tenants
  /api/cms/v1/users:
    post:
      consumes:
      - application/json
      description: Create a new user with the provided information
      parameters:
      - description: User data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UserCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: User created successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.UserResponse'
              type: object
        "400":
          description: Invalid request body or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "409":
          description: Email or username already exists
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to create user
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Create a new user
      tags:
      - Users
  /api/cms/v1/users/{id}:
    put:
      consumes:
      - application/json
      description: Update a user's information with tenant authorization
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: User update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UserUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.UserResponse'
              type: object
        "400":
          description: Invalid request body or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/response.Response'
        "409":
          description: Email or username already exists
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to update user
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Update a user
      tags:
      - Users
  /api/cms/v1/websites:
    get:
      description: Get paginated list of websites with filters using cursor pagination
      parameters:
      - description: Website name filter
        in: query
        name: name
        type: string
      - description: Domain filter
        in: query
        name: domain
        type: string
      - description: Status filter
        enum:
        - active
        - inactive
        - suspended
        in: query
        name: status
        type: string
      - description: Language filter
        in: query
        name: language
        type: string
      - description: Pagination cursor
        in: query
        name: cursor
        type: string
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      - default: created_at
        description: Sort by field
        enum:
        - id
        - name
        - domain
        - created_at
        - updated_at
        in: query
        name: sort_by
        type: string
      - default: desc
        description: Sort order
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.WebsiteListResponse'
        "400":
          description: Invalid filter parameters
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to retrieve websites
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: List websites
      tags:
      - Websites
    post:
      consumes:
      - application/json
      description: Creates a new website with the provided configuration
      parameters:
      - description: Website creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.WebsiteCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Website created successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.WebsiteResponse'
              type: object
        "400":
          description: Invalid request body or validation error
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to create website
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Create a new website
      tags:
      - Websites
  /api/cms/v1/websites/{website_id}/blog/posts:
    get:
      description: Get paginated list of blog posts with filters using cursor pagination
      parameters:
      - description: Website ID
        in: path
        name: website_id
        required: true
        type: integer
      - description: Title filter
        in: query
        name: title
        type: string
      - description: Status filter
        enum:
        - draft
        - review
        - published
        - scheduled
        - archived
        - rejected
        in: query
        name: status
        type: string
      - description: Type filter
        enum:
        - post
        - page
        - announcement
        in: query
        name: type
        type: string
      - description: Category ID filter
        in: query
        name: category_id
        type: integer
      - description: Author ID filter
        in: query
        name: author_id
        type: integer
      - description: Featured filter
        in: query
        name: is_featured
        type: boolean
      - description: Tag ID filter
        in: query
        name: tag_id
        type: integer
      - description: Date from filter (RFC3339)
        in: query
        name: date_from
        type: string
      - description: Date to filter (RFC3339)
        in: query
        name: date_to
        type: string
      - description: Pagination cursor
        in: query
        name: cursor
        type: string
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      - default: created_at
        description: Sort by field
        enum:
        - id
        - title
        - created_at
        - updated_at
        - published_at
        - view_count
        in: query
        name: sort_by
        type: string
      - default: desc
        description: Sort order
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.BlogPostListResponse'
        "400":
          description: Invalid filter parameters
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Failed to retrieve blog posts
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: List blog posts with cursor pagination
      tags:
      - Blog Posts
  /api/cms/v1/websites/{website_id}/seo/redirects:
    get:
      description: Get paginated list of SEO redirects with filters using cursor pagination
      parameters:
      - description: Website ID
        in: path
        name: website_id
        required: true
        type: integer
      - description: Status filter
        enum:
        - active
        - inactive
        - expired
        - deleted
        in: query
        name: status
        type: string
      - description: Redirect type filter
        enum:
        - "301"
        - "302"
        - "303"
        - "307"
        - "308"
        in: query
        name: redirect_type
        type: string
      - description: Source URL filter
        in: query
        name: source_url
        type: string
      - description: Search in URLs and notes
        in: query
        name: search
        type: string
      - description: Created by user ID
        in: query
        name: created_by
        type: integer
      - description: Date from filter (RFC3339)
        in: query
        name: date_from
        type: string
      - description: Date to filter (RFC3339)
        in: query
        name: date_to
        type: string
      - description: Pagination cursor
        in: query
        name: cursor
        type: string
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      - default: created_at
        description: Sort by field
        enum:
        - id
        - created_at
        - updated_at
        - hit_count
        - last_hit_at
        in: query
        name: sort_by
        type: string
      - default: desc
        description: Sort order
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.SEORedirectListResponse'
        "400":
          description: Invalid filter parameters
          schema:
            $ref: '#/definitions/gin.H'
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/gin.H'
        "500":
          description: Failed to retrieve redirects
          schema:
            $ref: '#/definitions/gin.H'
      security:
      - Bearer: []
      summary: List redirects with cursor pagination
      tags:
      - SEO Redirects
  /api/v1/onboarding/analytics/active-sessions:
    get:
      consumes:
      - application/json
      description: Get list of active sessions since a specific time
      parameters:
      - default: 1 hour ago
        description: Since time (RFC3339 format)
        in: query
        name: since
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              items:
                type: string
              type: array
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get active sessions
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/active-users:
    get:
      consumes:
      - application/json
      description: Get list of active users since a specific time
      parameters:
      - default: 1 hour ago
        description: Since time (RFC3339 format)
        in: query
        name: since
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              items:
                type: integer
              type: array
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get active users
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/aggregation:
    get:
      consumes:
      - application/json
      description: Get aggregated analytics data for a specific context and event
        type
      parameters:
      - description: Context type
        in: query
        name: context_type
        required: true
        type: string
      - description: Context ID
        in: query
        name: context_id
        required: true
        type: integer
      - description: Event type
        in: query
        name: event_type
        required: true
        type: string
      - description: Start time (RFC3339 format)
        in: query
        name: start_time
        required: true
        type: string
      - description: End time (RFC3339 format)
        in: query
        name: end_time
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AnalyticsAggregation'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get analytics aggregation
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/bulk-events:
    post:
      consumes:
      - application/json
      description: Log multiple analytics events at once
      parameters:
      - description: Bulk events request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk log events
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/daily:
    get:
      consumes:
      - application/json
      description: Get aggregated analytics data for a specific day
      parameters:
      - description: Context type
        in: query
        name: context_type
        required: true
        type: string
      - description: Context ID
        in: query
        name: context_id
        required: true
        type: integer
      - description: Event type
        in: query
        name: event_type
        required: true
        type: string
      - description: Date (YYYY-MM-DD format)
        in: query
        name: date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AnalyticsAggregation'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get daily analytics aggregation
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/events:
    get:
      consumes:
      - application/json
      description: Get a list of analytics events with pagination
      parameters:
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      - description: Filter by context type
        in: query
        name: context_type
        type: string
      - description: Filter by context ID
        in: query
        name: context_id
        type: integer
      - description: Filter by event type
        in: query
        name: event_type
        type: string
      - description: Filter by user ID
        in: query
        name: user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_onboarding_models.AnalyticsResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: List analytics events
      tags:
      - onboarding-analytics
    post:
      consumes:
      - application/json
      description: Log an analytics event for tracking onboarding metrics
      parameters:
      - description: Analytics event request
        in: body
        name: event
        required: true
        schema:
          $ref: '#/definitions/models.AnalyticsRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Log an analytics event
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/events/time-range:
    get:
      consumes:
      - application/json
      description: Get analytics events within a specific time range
      parameters:
      - description: Start time (RFC3339 format)
        in: query
        name: start_time
        required: true
        type: string
      - description: End time (RFC3339 format)
        in: query
        name: end_time
        required: true
        type: string
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_onboarding_models.AnalyticsResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: List analytics events by time range
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/insights:
    get:
      consumes:
      - application/json
      description: Get analytics insights for a specific context
      parameters:
      - description: Context type
        in: query
        name: context_type
        required: true
        type: string
      - description: Context ID
        in: query
        name: context_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get analytics insights
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/journeys/{journey_id}:
    get:
      consumes:
      - application/json
      description: Get comprehensive analytics for a specific journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.JourneyAnalytics'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get journey analytics
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/journeys/{journey_id}/dropoff:
    get:
      consumes:
      - application/json
      description: Get dropoff analytics for all steps in a journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.StepAnalytics'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get step dropoff analytics
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/journeys/{journey_id}/funnel:
    get:
      consumes:
      - application/json
      description: Get funnel analytics for a specific journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.StepAnalytics'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get journey funnel analytics
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/monthly:
    get:
      consumes:
      - application/json
      description: Get aggregated analytics data for a specific month
      parameters:
      - description: Context type
        in: query
        name: context_type
        required: true
        type: string
      - description: Context ID
        in: query
        name: context_id
        required: true
        type: integer
      - description: Event type
        in: query
        name: event_type
        required: true
        type: string
      - description: Month (YYYY-MM format)
        in: query
        name: month
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AnalyticsAggregation'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get monthly analytics aggregation
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/realtime:
    get:
      consumes:
      - application/json
      description: Get recent analytics events for real-time monitoring
      parameters:
      - default: 50
        description: Number of events to return
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_onboarding_models.AnalyticsResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get real-time events
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/reports:
    get:
      consumes:
      - application/json
      description: Generate a comprehensive analytics report
      parameters:
      - description: Report type (journey_performance, user_engagement, completion_rates)
        in: query
        name: report_type
        required: true
        type: string
      - description: Start time (RFC3339 format)
        in: query
        name: start_time
        required: true
        type: string
      - description: End time (RFC3339 format)
        in: query
        name: end_time
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Generate analytics report
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/steps/{step_id}:
    get:
      consumes:
      - application/json
      description: Get analytics for a specific step
      parameters:
      - description: Step ID
        in: path
        name: step_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StepAnalytics'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get step analytics
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/tenant:
    get:
      consumes:
      - application/json
      description: Get analytics for the current tenant
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get tenant analytics
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/user-events:
    post:
      consumes:
      - application/json
      description: Log an analytics event for a specific user
      parameters:
      - description: Analytics event request
        in: body
        name: event
        required: true
        schema:
          $ref: '#/definitions/models.AnalyticsRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Log a user analytics event
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/users/{user_id}:
    get:
      consumes:
      - application/json
      description: Get analytics for a specific user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserAnalytics'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user analytics
      tags:
      - onboarding-analytics
  /api/v1/onboarding/analytics/weekly:
    get:
      consumes:
      - application/json
      description: Get aggregated analytics data for a specific week
      parameters:
      - description: Context type
        in: query
        name: context_type
        required: true
        type: string
      - description: Context ID
        in: query
        name: context_id
        required: true
        type: integer
      - description: Event type
        in: query
        name: event_type
        required: true
        type: string
      - description: Week start date (YYYY-MM-DD format)
        in: query
        name: week
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AnalyticsAggregation'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get weekly analytics aggregation
      tags:
      - onboarding-analytics
  /api/v1/onboarding/journeys:
    get:
      consumes:
      - application/json
      description: Get a list of onboarding journeys with pagination
      parameters:
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      - description: Filter by journey type
        in: query
        name: type
        type: string
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Search query
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.JourneyResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: List onboarding journeys
      tags:
      - onboarding-journeys
    post:
      consumes:
      - application/json
      description: Create a new onboarding journey with the provided details
      parameters:
      - description: Journey creation request
        in: body
        name: journey
        required: true
        schema:
          $ref: '#/definitions/models.JourneyRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.JourneyResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new onboarding journey
      tags:
      - onboarding-journeys
  /api/v1/onboarding/journeys/{id}:
    delete:
      consumes:
      - application/json
      description: Delete an onboarding journey
      parameters:
      - description: Journey ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Delete onboarding journey
      tags:
      - onboarding-journeys
    get:
      consumes:
      - application/json
      description: Get a specific onboarding journey by its ID
      parameters:
      - description: Journey ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.JourneyResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get onboarding journey by ID
      tags:
      - onboarding-journeys
    put:
      consumes:
      - application/json
      description: Update an existing onboarding journey
      parameters:
      - description: Journey ID
        in: path
        name: id
        required: true
        type: integer
      - description: Journey update request
        in: body
        name: journey
        required: true
        schema:
          $ref: '#/definitions/models.JourneyUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.JourneyResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Update onboarding journey
      tags:
      - onboarding-journeys
  /api/v1/onboarding/journeys/{id}/analytics:
    get:
      consumes:
      - application/json
      description: Get analytics data for a specific journey
      parameters:
      - description: Journey ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.JourneyAnalytics'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get journey analytics
      tags:
      - onboarding-journeys
  /api/v1/onboarding/journeys/{id}/trigger:
    post:
      consumes:
      - application/json
      description: Trigger a specific journey for a user
      parameters:
      - description: Journey ID
        in: path
        name: id
        required: true
        type: integer
      - description: Trigger request with user_id
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Trigger a journey for a user
      tags:
      - onboarding-journeys
  /api/v1/onboarding/journeys/{journey_id}/steps:
    get:
      consumes:
      - application/json
      description: Get all steps for a specific journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      - default: true
        description: Whether to return steps in order
        in: query
        name: ordered
        type: boolean
      - default: false
        description: Whether to return only active steps
        in: query
        name: active_only
        type: boolean
      - default: false
        description: Whether to return only required steps
        in: query
        name: required_only
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.StepResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get steps by journey ID
      tags:
      - onboarding-steps
    post:
      consumes:
      - application/json
      description: Create a new onboarding step for a journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      - description: Step creation request
        in: body
        name: step
        required: true
        schema:
          $ref: '#/definitions/models.StepRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.StepResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new onboarding step
      tags:
      - onboarding-steps
  /api/v1/onboarding/journeys/{journey_id}/steps/bulk:
    post:
      consumes:
      - application/json
      description: Create multiple steps for a journey at once
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      - description: Bulk create request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            items:
              $ref: '#/definitions/models.StepResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk create steps
      tags:
      - onboarding-steps
  /api/v1/onboarding/journeys/{journey_id}/steps/bulk-update-status:
    post:
      consumes:
      - application/json
      description: Update the status of multiple steps at once
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      - description: Bulk update request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk update step status
      tags:
      - onboarding-steps
  /api/v1/onboarding/journeys/{journey_id}/steps/hierarchy:
    get:
      consumes:
      - application/json
      description: Get the complete step hierarchy for a journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.StepResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get step hierarchy
      tags:
      - onboarding-steps
  /api/v1/onboarding/journeys/{journey_id}/steps/next:
    get:
      consumes:
      - application/json
      description: Get the next step in a journey based on current step order
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      - description: Current step order
        in: query
        name: current_order
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StepResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get next step
      tags:
      - onboarding-steps
  /api/v1/onboarding/journeys/{journey_id}/steps/previous:
    get:
      consumes:
      - application/json
      description: Get the previous step in a journey based on current step order
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      - description: Current step order
        in: query
        name: current_order
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StepResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get previous step
      tags:
      - onboarding-steps
  /api/v1/onboarding/journeys/{journey_id}/steps/reorder:
    post:
      consumes:
      - application/json
      description: Reorder steps in a journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      - description: Reorder request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Reorder steps
      tags:
      - onboarding-steps
  /api/v1/onboarding/journeys/{journey_id}/steps/slug/{slug}:
    get:
      consumes:
      - application/json
      description: Get a specific onboarding step by its slug within a journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      - description: Step slug
        in: path
        name: slug
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StepResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get onboarding step by slug
      tags:
      - onboarding-steps
  /api/v1/onboarding/journeys/bulk-update-status:
    post:
      consumes:
      - application/json
      description: Update the status of multiple journeys at once
      parameters:
      - description: Bulk update request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk update journey status
      tags:
      - onboarding-journeys
  /api/v1/onboarding/journeys/context:
    get:
      consumes:
      - application/json
      description: Get journeys filtered by context type and context ID
      parameters:
      - description: Context type
        in: query
        name: context_type
        required: true
        type: string
      - description: Context ID
        in: query
        name: context_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.JourneyResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get journeys by context
      tags:
      - onboarding-journeys
  /api/v1/onboarding/journeys/slug/{slug}:
    get:
      consumes:
      - application/json
      description: Get a specific onboarding journey by its slug
      parameters:
      - description: Journey slug
        in: path
        name: slug
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.JourneyResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get onboarding journey by slug
      tags:
      - onboarding-journeys
  /api/v1/onboarding/journeys/triggerable:
    get:
      consumes:
      - application/json
      description: Get journeys that can be triggered by a specific trigger type
      parameters:
      - description: Trigger type
        in: query
        name: trigger_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.JourneyResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get triggerable journeys
      tags:
      - onboarding-journeys
  /api/v1/onboarding/progress/{id}:
    get:
      consumes:
      - application/json
      description: Get a specific progress entry by its ID
      parameters:
      - description: Progress ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ProgressResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get progress by ID
      tags:
      - onboarding-progress
    put:
      consumes:
      - application/json
      description: Update a progress entry
      parameters:
      - description: Progress ID
        in: path
        name: id
        required: true
        type: integer
      - description: Progress update request
        in: body
        name: progress
        required: true
        schema:
          $ref: '#/definitions/models.ProgressUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ProgressResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Update progress
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/complete-step:
    post:
      consumes:
      - application/json
      description: Complete a step for a user with completion data
      parameters:
      - description: Complete step request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ProgressResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Complete a step for a user
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/fail-step:
    post:
      consumes:
      - application/json
      description: Mark a step as failed for a user with a reason
      parameters:
      - description: Fail step request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ProgressResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Mark a step as failed for a user
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/journey/{journey_id}/average-duration:
    get:
      consumes:
      - application/json
      description: Get average duration for a journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: integer
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get average duration
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/journey/{journey_id}/completion-rate:
    get:
      consumes:
      - application/json
      description: Get completion rate for a journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              format: float64
              type: number
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get completion rate
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/journey/{journey_id}/dropoff-points:
    get:
      consumes:
      - application/json
      description: Get dropoff points for a journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              items:
                type: integer
              type: array
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get dropoff points
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/journey/{journey_id}/stats:
    get:
      consumes:
      - application/json
      description: Get completion statistics for a journey
      parameters:
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: integer
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get journey completion stats
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/skip-step:
    post:
      consumes:
      - application/json
      description: Skip a step for a user with a reason
      parameters:
      - description: Skip step request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ProgressResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Skip a step for a user
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/start-journey:
    post:
      consumes:
      - application/json
      description: Start a new journey for a user
      parameters:
      - description: Start journey request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.ProgressResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Start a journey for a user
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/start-step:
    post:
      consumes:
      - application/json
      description: Start a new step for a user
      parameters:
      - description: Start step request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.ProgressResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Start a step for a user
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/user/{user_id}:
    get:
      consumes:
      - application/json
      description: Get progress entries for a specific user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.ProgressResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user progress
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/user/{user_id}/analytics:
    get:
      consumes:
      - application/json
      description: Get analytics data for a specific user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserAnalytics'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user analytics
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/user/{user_id}/journey/{journey_id}:
    get:
      consumes:
      - application/json
      description: Get progress entries for a specific user and journey
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.ProgressResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user journey progress
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/user/{user_id}/journey/{journey_id}/current:
    get:
      consumes:
      - application/json
      description: Get the current step for a user in a journey
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ProgressResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get user current step
      tags:
      - onboarding-progress
  /api/v1/onboarding/progress/user/{user_id}/journey/{journey_id}/summary:
    get:
      consumes:
      - application/json
      description: Get progress summary for a user in a journey
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Journey ID
        in: path
        name: journey_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ProgressSummary'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get progress summary
      tags:
      - onboarding-progress
  /api/v1/onboarding/steps/{id}:
    delete:
      consumes:
      - application/json
      description: Delete an onboarding step
      parameters:
      - description: Step ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Delete onboarding step
      tags:
      - onboarding-steps
    get:
      consumes:
      - application/json
      description: Get a specific onboarding step by its ID
      parameters:
      - description: Step ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StepResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get onboarding step by ID
      tags:
      - onboarding-steps
    put:
      consumes:
      - application/json
      description: Update an existing onboarding step
      parameters:
      - description: Step ID
        in: path
        name: id
        required: true
        type: integer
      - description: Step update request
        in: body
        name: step
        required: true
        schema:
          $ref: '#/definitions/models.StepUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StepResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Update onboarding step
      tags:
      - onboarding-steps
  /api/v1/onboarding/steps/{id}/analytics:
    get:
      consumes:
      - application/json
      description: Get analytics data for a specific step
      parameters:
      - description: Step ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StepAnalytics'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get step analytics
      tags:
      - onboarding-steps
  /api/v1/onboarding/steps/{id}/children:
    get:
      consumes:
      - application/json
      description: Get child steps for a specific step
      parameters:
      - description: Parent Step ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.StepResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get step children
      tags:
      - onboarding-steps
  /api/v1/onboarding/steps/{id}/parent:
    get:
      consumes:
      - application/json
      description: Get parent step for a specific step
      parameters:
      - description: Step ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StepResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get step parent
      tags:
      - onboarding-steps
  /api/v1/onboarding/templates:
    get:
      consumes:
      - application/json
      description: Get a list of onboarding templates with filters and pagination
      parameters:
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      - description: Filter by template type
        in: query
        name: type
        type: string
      - description: Filter by category
        in: query
        name: category
        type: string
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Filter by public templates
        in: query
        name: public
        type: boolean
      - description: Filter by featured templates
        in: query
        name: featured
        type: boolean
      - description: Search query
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.TemplateResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: List onboarding templates
      tags:
      - onboarding-templates
    post:
      consumes:
      - application/json
      description: Create a new onboarding template
      parameters:
      - description: Template creation request
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/models.TemplateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.TemplateResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new onboarding template
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/{id}:
    delete:
      consumes:
      - application/json
      description: Delete an onboarding template
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Delete onboarding template
      tags:
      - onboarding-templates
    get:
      consumes:
      - application/json
      description: Get a specific onboarding template by its ID
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TemplateResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get onboarding template by ID
      tags:
      - onboarding-templates
    put:
      consumes:
      - application/json
      description: Update an existing onboarding template
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      - description: Template update request
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/models.TemplateUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TemplateResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Update onboarding template
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/{id}/apply:
    post:
      consumes:
      - application/json
      description: Apply a template to create content based on the template
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      - description: Apply template request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Apply a template
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/{id}/clone:
    post:
      consumes:
      - application/json
      description: Create a copy of an existing template
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      - description: Clone template request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.TemplateResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Clone a template
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/{id}/rate:
    post:
      consumes:
      - application/json
      description: Rate a template (1-5 stars)
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      - description: Rating request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Rate a template
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/{id}/use:
    post:
      consumes:
      - application/json
      description: Mark a template as used and increment usage count
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Use a template
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/bulk-deprecate:
    post:
      consumes:
      - application/json
      description: Deprecate multiple templates at once
      parameters:
      - description: Bulk deprecate request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk deprecate templates
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/bulk-publish:
    post:
      consumes:
      - application/json
      description: Publish multiple templates at once
      parameters:
      - description: Bulk publish request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk publish templates
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/bulk-update-status:
    post:
      consumes:
      - application/json
      description: Update the status of multiple templates at once
      parameters:
      - description: Bulk update request
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk update template status
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/latest:
    get:
      consumes:
      - application/json
      description: Get the latest version of a template
      parameters:
      - description: Template name
        in: query
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TemplateResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get latest template version
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/most-used:
    get:
      consumes:
      - application/json
      description: Get the most used templates for a tenant
      parameters:
      - default: 10
        description: Number of items to return
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.TemplateResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get most used templates
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/recently-used:
    get:
      consumes:
      - application/json
      description: Get the recently used templates for a tenant
      parameters:
      - default: 10
        description: Number of items to return
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.TemplateResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get recently used templates
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/slug/{slug}:
    get:
      consumes:
      - application/json
      description: Get a specific onboarding template by its slug
      parameters:
      - description: Template slug
        in: path
        name: slug
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TemplateResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get onboarding template by slug
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/top-rated:
    get:
      consumes:
      - application/json
      description: Get the top rated templates for a tenant
      parameters:
      - default: 10
        description: Number of items to return
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.TemplateResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get top rated templates
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/version:
    get:
      consumes:
      - application/json
      description: Get a specific version of a template
      parameters:
      - description: Template name
        in: query
        name: name
        required: true
        type: string
      - description: Template version
        in: query
        name: version
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TemplateResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
      summary: Get template by version
      tags:
      - onboarding-templates
  /api/v1/onboarding/templates/versions:
    get:
      consumes:
      - application/json
      description: Get all versions of a template
      parameters:
      - description: Template name
        in: query
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.TemplateResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: List template versions
      tags:
      - onboarding-templates
  /auth/switch-tenant:
    post:
      consumes:
      - application/json
      description: Switch the active tenant context for multi-tenant user
      parameters:
      - description: Switch tenant request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.SwitchTenantRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Tenant switched successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.SwitchTenantResponse'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.Response'
        "403":
          description: Access denied to tenant
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: Switch tenant context
      tags:
      - Authentication - Multi-Tenant
  /auth/test-email:
    post:
      consumes:
      - application/json
      description: Send a test email to verify email configuration
      parameters:
      - description: Test email request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.TestEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Test email sent successfully
          schema:
            $ref: '#/definitions/handlers.TestEmailResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handlers.TestEmailResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handlers.TestEmailResponse'
      summary: Send test email
      tags:
      - Authentication - Email Test
  /auth/test-smtp:
    post:
      consumes:
      - application/json
      description: Test SMTP server connection and configuration
      produces:
      - application/json
      responses:
        "200":
          description: SMTP connection successful
          schema:
            $ref: '#/definitions/handlers.TestEmailResponse'
        "500":
          description: SMTP connection failed
          schema:
            $ref: '#/definitions/handlers.TestEmailResponse'
      summary: Test SMTP connection
      tags:
      - Authentication - Email Test
  /auth/verification-token-stats:
    get:
      consumes:
      - application/json
      description: Get email verification token statistics for the authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: Token statistics
          schema:
            $ref: '#/definitions/models.TokenStats'
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get verification token statistics
      tags:
      - Authentication - Email
  /blog/posts/{id}/seo:
    delete:
      consumes:
      - application/json
      description: Delete SEO metadata for a specific blog post
      parameters:
      - description: Post ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Delete SEO metadata for blog post
      tags:
      - Blog SEO
    get:
      consumes:
      - application/json
      description: Get SEO metadata for a specific blog post
      parameters:
      - description: Post ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOMetaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get SEO metadata for blog post
      tags:
      - Blog SEO
    post:
      consumes:
      - application/json
      description: Create SEO metadata for a specific blog post
      parameters:
      - description: Post ID
        in: path
        name: id
        required: true
        type: integer
      - description: Create SEO meta request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.CreateSEOMetaRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOMetaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "422":
          description: Unprocessable Entity
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Create SEO metadata for blog post
      tags:
      - Blog SEO
    put:
      consumes:
      - application/json
      description: Update SEO metadata for a specific blog post
      parameters:
      - description: Post ID
        in: path
        name: id
        required: true
        type: integer
      - description: Update SEO meta request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateSEOMetaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOMetaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "422":
          description: Unprocessable Entity
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Update SEO metadata for blog post
      tags:
      - Blog SEO
  /blog/posts/{id}/seo/analyze:
    post:
      consumes:
      - application/json
      description: Analyze SEO metadata and provide optimization recommendations for
        blog post
      parameters:
      - description: Post ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOAnalysis'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Analyze SEO metadata for blog post
      tags:
      - Blog SEO
  /blog/posts/{id}/seo/tags:
    get:
      consumes:
      - application/json
      description: Generate HTML meta tags for SEO metadata for blog post
      parameters:
      - description: Post ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Generate meta tags for blog post
      tags:
      - Blog SEO
  /blog/posts/{id}/seo/validate:
    post:
      consumes:
      - application/json
      description: Validate SEO metadata and check for issues for blog post
      parameters:
      - description: Post ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOValidationResult'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Validate SEO metadata for blog post
      tags:
      - Blog SEO
  /media/files:
    get:
      consumes:
      - application/json
      description: Get a list of media files with filtering and pagination
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Website ID
        in: query
        name: website_id
        required: true
        type: integer
      - description: Folder ID
        in: query
        name: folder_id
        type: integer
      - description: File type (image, video, audio, document, archive, other)
        in: query
        name: file_type
        type: string
      - description: File category
        in: query
        name: category
        type: string
      - description: MIME type
        in: query
        name: mime_type
        type: string
      - description: File visibility (public, private, shared)
        in: query
        name: visibility
        type: string
      - description: Search query
        in: query
        name: query
        type: string
      - description: Tags (comma-separated)
        in: query
        name: tags
        type: string
      - description: Minimum file size
        in: query
        name: min_size
        type: integer
      - description: Maximum file size
        in: query
        name: max_size
        type: integer
      - description: Sort by (name, size, created_at, updated_at)
        in: query
        name: sort_by
        type: string
      - description: Sort order (asc, desc)
        in: query
        name: sort_order
        type: string
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Page size
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.PaginatedResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: List media files
      tags:
      - Media
  /media/files/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a media file
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: File ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Delete media file
      tags:
      - Media
    get:
      consumes:
      - application/json
      description: Get a media file by its ID
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: File ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.MediaFileResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get media file by ID
      tags:
      - Media
    put:
      consumes:
      - application/json
      description: Update a media file's metadata
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: File ID
        in: path
        name: id
        required: true
        type: integer
      - description: Update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateMediaFileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.MediaFileResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Update media file
      tags:
      - Media
  /media/files/{id}/download:
    get:
      consumes:
      - application/json
      description: Download a media file
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: File ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/octet-stream
      responses:
        "200":
          description: OK
          schema:
            type: file
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Download media file
      tags:
      - Media
  /media/folders:
    get:
      consumes:
      - application/json
      description: Get a list of media folders
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Website ID
        in: query
        name: website_id
        required: true
        type: integer
      - description: Parent folder ID
        in: query
        name: parent_id
        type: integer
      - description: Folder type (user, system, public, private, shared)
        in: query
        name: type
        type: string
      - description: Folder visibility (public, private, shared)
        in: query
        name: visibility
        type: string
      - default: false
        description: Return as tree structure
        in: query
        name: tree
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.MediaFolderResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: List media folders
      tags:
      - Media Folders
    post:
      consumes:
      - application/json
      description: Create a new media folder
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Create folder request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.CreateMediaFolderRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.MediaFolderResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Create media folder
      tags:
      - Media Folders
  /media/folders/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a media folder
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Folder ID
        in: path
        name: id
        required: true
        type: integer
      - default: false
        description: Force delete (delete files too)
        in: query
        name: force
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Delete media folder
      tags:
      - Media Folders
    get:
      consumes:
      - application/json
      description: Get a media folder by its ID
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Folder ID
        in: path
        name: id
        required: true
        type: integer
      - default: false
        description: Include files in response
        in: query
        name: include_files
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.MediaFolderResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get media folder by ID
      tags:
      - Media Folders
    put:
      consumes:
      - application/json
      description: Update a media folder
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Folder ID
        in: path
        name: id
        required: true
        type: integer
      - description: Update folder request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateMediaFolderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.MediaFolderResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Update media folder
      tags:
      - Media Folders
  /media/folders/{id}/breadcrumb:
    get:
      consumes:
      - application/json
      description: Get folder breadcrumb path
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Folder ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.MediaFolderResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get folder breadcrumb
      tags:
      - Media Folders
  /media/folders/{id}/move:
    post:
      consumes:
      - application/json
      description: Move a media folder to a different parent
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Folder ID
        in: path
        name: id
        required: true
        type: integer
      - description: New parent folder ID (null for root)
        in: formData
        name: parent_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.MediaFolderResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Move media folder
      tags:
      - Media Folders
  /media/stats:
    get:
      consumes:
      - application/json
      description: Get media usage statistics
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Website ID
        in: query
        name: website_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.MediaFileStats'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get media statistics
      tags:
      - Media
  /media/upload:
    post:
      consumes:
      - multipart/form-data
      description: Upload a media file to storage
      parameters:
      - description: Tenant ID
        in: header
        name: tenant_id
        required: true
        type: integer
      - description: Website ID
        in: formData
        name: website_id
        required: true
        type: integer
      - description: Folder ID
        in: formData
        name: folder_id
        type: integer
      - description: Media file
        in: formData
        name: file
        required: true
        type: file
      - description: Alt text for accessibility
        in: formData
        name: alt_text
        type: string
      - description: File title
        in: formData
        name: title
        type: string
      - description: File description
        in: formData
        name: description
        type: string
      - description: File category
        in: formData
        name: category
        type: string
      - description: File visibility (public, private, shared)
        in: formData
        name: visibility
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UploadResult'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Upload media file
      tags:
      - Media
  /notifications:
    get:
      description: Get paginated list of notifications with filters using cursor pagination
      parameters:
      - description: Notification type
        in: query
        name: type
        type: string
      - description: Channel
        enum:
        - email
        - socket
        - push
        - sms
        in: query
        name: channel
        type: string
      - description: Status
        enum:
        - pending
        - queued
        - sent
        - delivered
        - failed
        - cancelled
        in: query
        name: status
        type: string
      - description: Priority
        enum:
        - low
        - normal
        - high
        - urgent
        in: query
        name: priority
        type: string
      - description: Date from (YYYY-MM-DD)
        in: query
        name: date_from
        type: string
      - description: Date to (YYYY-MM-DD)
        in: query
        name: date_to
        type: string
      - description: Pagination cursor
        in: query
        name: cursor
        type: string
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      - default: created_at
        description: Sort by field
        in: query
        name: sort_by
        type: string
      - default: desc
        description: Sort order
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dto.NotificationListResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: List notifications
      tags:
      - notifications
    post:
      consumes:
      - application/json
      description: Create a new notification with recipients
      parameters:
      - description: Notification data
        in: body
        name: notification
        required: true
        schema:
          $ref: '#/definitions/dto.NotificationCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create notification
      tags:
      - notifications
  /notifications/{id}:
    delete:
      description: Delete a notification by ID
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Delete notification
      tags:
      - notifications
    get:
      description: Get notification details by ID
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get notification
      tags:
      - notifications
    put:
      consumes:
      - application/json
      description: Update notification details
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: integer
      - description: Update data
        in: body
        name: notification
        required: true
        schema:
          $ref: '#/definitions/models.UpdateNotificationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update notification
      tags:
      - notifications
  /notifications/{id}/cancel:
    post:
      description: Cancel a pending or queued notification
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Cancel notification
      tags:
      - notifications
  /notifications/{id}/retry:
    post:
      description: Retry a failed notification
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Retry notification
      tags:
      - notifications
  /notifications/{id}/send:
    post:
      description: Manually trigger sending of a pending notification
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Send notification
      tags:
      - notifications
  /notifications/statistics:
    get:
      description: Get notification statistics for analytics
      parameters:
      - description: Date from (YYYY-MM-DD)
        in: query
        name: date_from
        type: string
      - description: Date to (YYYY-MM-DD)
        in: query
        name: date_to
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
      summary: Get notification statistics
      tags:
      - notifications
  /profile:
    get:
      consumes:
      - application/json
      description: Returns the current authenticated user's profile information
      produces:
      - application/json
      responses:
        "200":
          description: Profile retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.GetProfileResponse'
              type: object
        "401":
          description: Authentication required
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - Bearer: []
      summary: Get user profile
      tags:
      - auth
  /seo/meta:
    get:
      consumes:
      - application/json
      description: List SEO metadata with optional filtering
      parameters:
      - description: Website ID
        in: query
        name: website_id
        type: integer
      - description: Page type (page, post, category, tag, product, custom)
        in: query
        name: page_type
        type: string
      - description: Status (active, inactive, deleted)
        in: query
        name: status
        type: string
      - description: Is indexed
        in: query
        name: is_indexed
        type: boolean
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SEOMetaResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: List SEO metadata
      tags:
      - SEO Meta
    post:
      consumes:
      - application/json
      description: Create new SEO metadata for a page
      parameters:
      - description: Create SEO meta request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.CreateSEOMetaRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOMetaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "422":
          description: Unprocessable Entity
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Create SEO metadata
      tags:
      - SEO Meta
  /seo/meta/{id}:
    delete:
      consumes:
      - application/json
      description: Delete SEO metadata by ID
      parameters:
      - description: SEO Meta ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Delete SEO metadata
      tags:
      - SEO Meta
    get:
      consumes:
      - application/json
      description: Get SEO metadata by its ID
      parameters:
      - description: SEO Meta ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOMetaResponse'
              type: object
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get SEO metadata by ID
      tags:
      - SEO Meta
    put:
      consumes:
      - application/json
      description: Update existing SEO metadata
      parameters:
      - description: SEO Meta ID
        in: path
        name: id
        required: true
        type: integer
      - description: Update SEO meta request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateSEOMetaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOMetaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "422":
          description: Unprocessable Entity
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Update SEO metadata
      tags:
      - SEO Meta
  /seo/meta/{id}/analyze:
    post:
      consumes:
      - application/json
      description: Analyze SEO metadata and provide optimization recommendations
      parameters:
      - description: SEO Meta ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOAnalysis'
              type: object
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Analyze SEO metadata
      tags:
      - SEO Meta
  /seo/meta/{id}/tags:
    get:
      consumes:
      - application/json
      description: Generate HTML meta tags for SEO metadata
      parameters:
      - description: SEO Meta ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
              type: object
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Generate meta tags
      tags:
      - SEO Meta
  /seo/meta/{id}/validate:
    post:
      consumes:
      - application/json
      description: Validate SEO metadata and check for issues
      parameters:
      - description: SEO Meta ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOValidationResult'
              type: object
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Validate SEO metadata
      tags:
      - SEO Meta
  /seo/meta/bulk:
    post:
      consumes:
      - application/json
      description: Create multiple SEO metadata entries at once
      parameters:
      - description: Bulk create SEO meta requests
        in: body
        name: request
        required: true
        schema:
          items:
            $ref: '#/definitions/models.CreateSEOMetaRequest'
          type: array
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SEOMetaResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "422":
          description: Unprocessable Entity
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Bulk create SEO metadata
      tags:
      - SEO Meta
  /seo/meta/by-page:
    get:
      consumes:
      - application/json
      description: Get SEO metadata by page type and page ID
      parameters:
      - description: Page type (page, post, category, tag, product, custom)
        in: query
        name: page_type
        required: true
        type: string
      - description: Page ID
        in: query
        name: page_id
        type: integer
      - description: Page URL
        in: query
        name: page_url
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SEOMetaResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get SEO metadata by page
      tags:
      - SEO Meta
  /sessions:
    get:
      consumes:
      - application/json
      description: Get list of user's active login sessions
      produces:
      - application/json
      responses:
        "200":
          description: List of active sessions
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.GetActiveSessionsResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: Get active sessions
      tags:
      - auth
  /sessions/{sessionId}:
    delete:
      consumes:
      - application/json
      description: Revoke a specific login session by session ID
      parameters:
      - description: Session ID to revoke
        in: path
        name: sessionId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Session revoked successfully
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/dto.RevokeSessionResponse'
              type: object
        "400":
          description: Invalid session ID
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Session not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: Revoke session
      tags:
      - auth
  /templates:
    get:
      description: Get paginated list of templates with filters
      parameters:
      - description: Template type
        enum:
        - transactional
        - marketing
        - system
        - custom
        in: query
        name: type
        type: string
      - description: Channel
        enum:
        - email
        - socket
        - push
        - sms
        in: query
        name: channel
        type: string
      - description: Active status
        in: query
        name: is_active
        type: boolean
      - description: Search in name, code, description
        in: query
        name: search
        type: string
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      - default: created_at
        description: Sort by field
        in: query
        name: sort_by
        type: string
      - default: desc
        description: Sort order
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: List templates
      tags:
      - templates
    post:
      consumes:
      - application/json
      description: Create a new notification template
      parameters:
      - description: Template data
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/dto.NotificationTemplateCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create template
      tags:
      - templates
  /templates/{id}:
    delete:
      description: Delete a template and all its versions
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Delete template
      tags:
      - templates
    get:
      description: Get template details by ID with versions
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get template
      tags:
      - templates
    put:
      consumes:
      - application/json
      description: Update template details
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      - description: Update data
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/models.UpdateTemplateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update template
      tags:
      - templates
  /templates/{id}/activate:
    post:
      description: Activate a template for use
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Activate template
      tags:
      - templates
  /templates/{id}/deactivate:
    post:
      description: Deactivate a template
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Deactivate template
      tags:
      - templates
  /templates/{id}/versions:
    get:
      description: Get all versions of a template
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
      summary: List template versions
      tags:
      - templates
    post:
      consumes:
      - application/json
      description: Create a new version of a template
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      - description: Version data
        in: body
        name: version
        required: true
        schema:
          $ref: '#/definitions/models.CreateTemplateVersionRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create template version
      tags:
      - templates
  /templates/{id}/versions/{version_id}:
    put:
      consumes:
      - application/json
      description: Update template version content
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      - description: Version ID
        in: path
        name: version_id
        required: true
        type: integer
      - description: Update data
        in: body
        name: version
        required: true
        schema:
          $ref: '#/definitions/models.UpdateTemplateVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update template version
      tags:
      - templates
  /templates/{id}/versions/{version_id}/activate:
    post:
      description: Activate a specific version of a template
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      - description: Version ID
        in: path
        name: version_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Activate template version
      tags:
      - templates
  /templates/{id}/versions/{version_id}/preview:
    post:
      consumes:
      - application/json
      description: Preview template rendering with test data
      parameters:
      - description: Template ID
        in: path
        name: id
        required: true
        type: integer
      - description: Version ID
        in: path
        name: version_id
        required: true
        type: integer
      - description: Test data for template variables
        in: body
        name: data
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Preview template
      tags:
      - templates
  /tenants/{tenantId}/members:
    get:
      consumes:
      - application/json
      description: Retrieve all members for a specific tenant with filtering and pagination
      parameters:
      - description: Tenant ID
        in: path
        name: tenantId
        required: true
        type: integer
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Filter by role
        in: query
        name: role
        type: string
      - description: Search query
        in: query
        name: search
        type: string
      - description: Sort by field
        in: query
        name: sort_by
        type: string
      - description: Sort order (asc/desc)
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.TenantMembershipListResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get tenant members
      tags:
      - tenant-memberships
    post:
      consumes:
      - application/json
      description: Add a user as a member to a tenant
      parameters:
      - description: Tenant ID
        in: path
        name: tenantId
        required: true
        type: integer
      - description: Member data
        in: body
        name: member
        required: true
        schema:
          $ref: '#/definitions/services.AddTenantMemberInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.TenantMembership'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Add tenant member
      tags:
      - tenant-memberships
  /tenants/{tenantId}/members/{userId}:
    delete:
      consumes:
      - application/json
      description: Remove a user from a tenant (soft delete)
      parameters:
      - description: Tenant ID
        in: path
        name: tenantId
        required: true
        type: integer
      - description: User ID
        in: path
        name: userId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Remove tenant member
      tags:
      - tenant-memberships
    get:
      consumes:
      - application/json
      description: Retrieve a specific membership by tenant and user IDs
      parameters:
      - description: Tenant ID
        in: path
        name: tenantId
        required: true
        type: integer
      - description: User ID
        in: path
        name: userId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.TenantMembership'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get membership
      tags:
      - tenant-memberships
  /tenants/{tenantId}/members/{userId}/role:
    put:
      consumes:
      - application/json
      description: Update a member's role and details in a tenant
      parameters:
      - description: Tenant ID
        in: path
        name: tenantId
        required: true
        type: integer
      - description: User ID
        in: path
        name: userId
        required: true
        type: integer
      - description: Role update data
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/services.UpdateMemberRoleInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.TenantMembership'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update member role
      tags:
      - tenant-memberships
  /tenants/{tenantId}/members/{userId}/status:
    put:
      consumes:
      - application/json
      description: Update the status of a membership
      parameters:
      - description: Tenant ID
        in: path
        name: tenantId
        required: true
        type: integer
      - description: User ID
        in: path
        name: userId
        required: true
        type: integer
      - description: Status data
        in: body
        name: status
        required: true
        schema:
          additionalProperties:
            type: string
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update membership status
      tags:
      - tenant-memberships
  /tenants/{tenantId}/members/stats:
    get:
      consumes:
      - application/json
      description: Retrieve membership statistics for a tenant
      parameters:
      - description: Tenant ID
        in: path
        name: tenantId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.TenantMembershipStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get membership statistics
      tags:
      - tenant-memberships
  /tracking/{tenant_id}/recipients/{recipient_id}/click:
    post:
      consumes:
      - application/json
      description: Track when a link in notification is clicked
      parameters:
      - description: Tenant ID
        in: path
        name: tenant_id
        required: true
        type: integer
      - description: Recipient ID
        in: path
        name: recipient_id
        required: true
        type: integer
      - description: Click data
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/dto.NotificationTrackClickRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Track click
      tags:
      - tracking
  /tracking/{tenant_id}/recipients/{recipient_id}/open:
    post:
      description: Track when a notification is opened by recipient
      parameters:
      - description: Tenant ID
        in: path
        name: tenant_id
        required: true
        type: integer
      - description: Recipient ID
        in: path
        name: recipient_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Track open
      tags:
      - tracking
  /tracking/{tenant_id}/recipients/{recipient_id}/pixel.gif:
    get:
      description: Serve 1x1 pixel image for email open tracking
      parameters:
      - description: Tenant ID
        in: path
        name: tenant_id
        required: true
        type: integer
      - description: Recipient ID
        in: path
        name: recipient_id
        required: true
        type: integer
      produces:
      - image/gif
      responses:
        "200":
          description: OK
          schema:
            type: file
      summary: Get tracking pixel
      tags:
      - tracking
  /tracking/{tenant_id}/recipients/{recipient_id}/redirect:
    get:
      description: Redirect to target URL while tracking the click
      parameters:
      - description: Tenant ID
        in: path
        name: tenant_id
        required: true
        type: integer
      - description: Recipient ID
        in: path
        name: recipient_id
        required: true
        type: integer
      - description: Target URL to redirect to
        in: query
        name: url
        required: true
        type: string
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to target URL
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      summary: Redirect and track
      tags:
      - tracking
  /user-analytics/activity/{user_id}:
    get:
      consumes:
      - application/json
      description: Retrieve activity data for a specific user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - default: 30
        description: Number of days to look back
        in: query
        name: days
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserActivity'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user activity
      tags:
      - user-analytics
  /user-analytics/demographics:
    get:
      consumes:
      - application/json
      description: Retrieve demographic statistics for the current tenant
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.DemographicStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get demographic statistics
      tags:
      - user-analytics
  /user-analytics/engagement:
    get:
      consumes:
      - application/json
      description: Retrieve engagement statistics for the current tenant
      parameters:
      - default: month
        description: Time period (day, week, month, year)
        in: query
        name: period
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.EngagementStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get engagement statistics
      tags:
      - user-analytics
  /user-analytics/registrations:
    get:
      consumes:
      - application/json
      description: Retrieve registration statistics for the current tenant
      parameters:
      - default: month
        description: Time period (day, week, month, year)
        in: query
        name: period
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.RegistrationStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get registration statistics
      tags:
      - user-analytics
  /user-analytics/retention:
    get:
      consumes:
      - application/json
      description: Retrieve retention statistics for the current tenant
      parameters:
      - default: month
        description: Time period (day, week, month, year)
        in: query
        name: period
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.RetentionStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get retention statistics
      tags:
      - user-analytics
  /user-analytics/stats:
    get:
      consumes:
      - application/json
      description: Retrieve comprehensive user statistics for the current tenant
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user statistics
      tags:
      - user-analytics
  /user-invitations:
    get:
      consumes:
      - application/json
      description: Retrieve a list of invitations with filtering and pagination
      parameters:
      - description: Tenant ID
        in: query
        name: tenant_id
        required: true
        type: integer
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Filter by email
        in: query
        name: email
        type: string
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: List invitations
      tags:
      - user-invitations
    post:
      consumes:
      - application/json
      description: Create a new invitation for a user to join a tenant
      parameters:
      - description: Invitation data
        in: body
        name: invitation
        required: true
        schema:
          $ref: '#/definitions/services.CreateInvitationInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create user invitation
      tags:
      - user-invitations
  /user-invitations/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a user invitation permanently
      parameters:
      - description: Invitation ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Delete invitation
      tags:
      - user-invitations
    get:
      consumes:
      - application/json
      description: Retrieve a user invitation by its ID
      parameters:
      - description: Invitation ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get invitation by ID
      tags:
      - user-invitations
    put:
      consumes:
      - application/json
      description: Update an existing user invitation
      parameters:
      - description: Invitation ID
        in: path
        name: id
        required: true
        type: integer
      - description: Invitation update data
        in: body
        name: invitation
        required: true
        schema:
          $ref: '#/definitions/services.UpdateInvitationInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update invitation
      tags:
      - user-invitations
  /user-invitations/{id}/resend:
    post:
      consumes:
      - application/json
      description: Resend a user invitation with a new token and expiration
      parameters:
      - description: Invitation ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Resend invitation
      tags:
      - user-invitations
  /user-invitations/{id}/revoke:
    post:
      consumes:
      - application/json
      description: Revoke a user invitation (only pending invitations can be revoked)
      parameters:
      - description: Invitation ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Revoke invitation
      tags:
      - user-invitations
  /user-invitations/accept:
    post:
      consumes:
      - application/json
      description: Accept a user invitation using the invitation token
      parameters:
      - description: Invitation acceptance data
        in: body
        name: invitation
        required: true
        schema:
          $ref: '#/definitions/services.AcceptInvitationInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Accept invitation
      tags:
      - user-invitations
  /user-invitations/reject:
    post:
      consumes:
      - application/json
      description: Reject a user invitation using the invitation token
      parameters:
      - description: Invitation rejection data
        in: body
        name: invitation
        required: true
        schema:
          $ref: '#/definitions/services.RejectInvitationInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Reject invitation
      tags:
      - user-invitations
  /user-invitations/token/{token}:
    get:
      consumes:
      - application/json
      description: Retrieve a user invitation by its token
      parameters:
      - description: Invitation token
        in: path
        name: token
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get invitation by token
      tags:
      - user-invitations
  /user-preferences:
    post:
      consumes:
      - application/json
      description: Create new user preferences with default values
      parameters:
      - description: User preferences data
        in: body
        name: preferences
        required: true
        schema:
          $ref: '#/definitions/services.CreateUserPreferencesInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.UserPreferences'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create new user preferences
      tags:
      - user-preferences
  /user-preferences/defaults:
    get:
      consumes:
      - application/json
      description: Retrieve default preferences for the current tenant
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserPreferences'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get default preferences
      tags:
      - user-preferences
  /user-preferences/stats:
    get:
      consumes:
      - application/json
      description: Retrieve statistics about user preferences for the current tenant
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.PreferencesStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get preferences statistics
      tags:
      - user-preferences
  /user-preferences/user/{user_id}:
    get:
      consumes:
      - application/json
      description: Retrieve user preferences by user ID
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserPreferences'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user preferences by user ID
      tags:
      - user-preferences
    put:
      consumes:
      - application/json
      description: Update user preferences with new values
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: User preferences update data
        in: body
        name: preferences
        required: true
        schema:
          $ref: '#/definitions/services.UpdateUserPreferencesInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserPreferences'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update user preferences
      tags:
      - user-preferences
  /user-preferences/user/{user_id}/export:
    get:
      consumes:
      - application/json
      description: Export user preferences to a portable format
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Export user preferences
      tags:
      - user-preferences
  /user-preferences/user/{user_id}/import:
    post:
      consumes:
      - application/json
      description: Import user preferences from a portable format
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Preferences data to import
        in: body
        name: preferences
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Import user preferences
      tags:
      - user-preferences
  /user-preferences/user/{user_id}/notifications:
    put:
      consumes:
      - application/json
      description: Update notification preferences for a user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Notification preferences data
        in: body
        name: preferences
        required: true
        schema:
          $ref: '#/definitions/services.NotificationPreferencesInput'
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update notification preferences
      tags:
      - user-preferences
  /user-preferences/user/{user_id}/privacy:
    put:
      consumes:
      - application/json
      description: Update privacy preferences for a user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Privacy preferences data
        in: body
        name: preferences
        required: true
        schema:
          $ref: '#/definitions/services.PrivacyPreferencesInput'
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update privacy preferences
      tags:
      - user-preferences
  /user-preferences/user/{user_id}/ui:
    put:
      consumes:
      - application/json
      description: Update UI preferences for a user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: UI preferences data
        in: body
        name: preferences
        required: true
        schema:
          $ref: '#/definitions/services.UIPreferencesInput'
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update UI preferences
      tags:
      - user-preferences
  /user-preferences/validate:
    post:
      consumes:
      - application/json
      description: Validate user preferences data without saving
      parameters:
      - description: User preferences data to validate
        in: body
        name: preferences
        required: true
        schema:
          $ref: '#/definitions/services.UpdateUserPreferencesInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: boolean
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Validate preferences data
      tags:
      - user-preferences
  /user-profiles:
    post:
      consumes:
      - application/json
      description: Create a new user profile with the provided information
      parameters:
      - description: User profile data
        in: body
        name: profile
        required: true
        schema:
          $ref: '#/definitions/services.CreateUserProfileInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.UserProfile'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new user profile
      tags:
      - user-profiles
  /user-profiles/low-completion:
    get:
      consumes:
      - application/json
      description: Retrieve profiles with completion percentage below threshold
      parameters:
      - default: 50
        description: Completion threshold percentage
        in: query
        name: threshold
        type: integer
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserProfileListResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get profiles with low completion
      tags:
      - user-profiles
  /user-profiles/search/location:
    get:
      consumes:
      - application/json
      description: Search user profiles by location
      parameters:
      - description: Location to search for
        in: query
        name: location
        required: true
        type: string
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserProfileListResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Search profiles by location
      tags:
      - user-profiles
  /user-profiles/search/skills:
    get:
      consumes:
      - application/json
      description: Search user profiles by skills
      parameters:
      - collectionFormat: multi
        description: Skills to search for
        in: query
        items:
          type: string
        name: skills
        required: true
        type: array
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserProfileListResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Search profiles by skills
      tags:
      - user-profiles
  /user-profiles/stats/completion:
    get:
      consumes:
      - application/json
      description: Retrieve statistics about profile completion for the current tenant
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.ProfileCompletionStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get profile completion statistics
      tags:
      - user-profiles
  /user-profiles/user/{user_id}:
    get:
      consumes:
      - application/json
      description: Retrieve a user profile by user ID
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserProfile'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get a user profile by user ID
      tags:
      - user-profiles
    put:
      consumes:
      - application/json
      description: Update a user profile's information
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: User profile update data
        in: body
        name: profile
        required: true
        schema:
          $ref: '#/definitions/services.UpdateUserProfileInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserProfile'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update a user profile
      tags:
      - user-profiles
  /user-profiles/user/{user_id}/completion:
    put:
      consumes:
      - application/json
      description: Update the completion status of a user profile
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update profile completion status
      tags:
      - user-profiles
  /user-profiles/user/{user_id}/custom-fields:
    put:
      consumes:
      - application/json
      description: Update custom fields for a user profile
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Custom fields data
        in: body
        name: custom_fields
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update custom fields
      tags:
      - user-profiles
  /user-profiles/validate:
    post:
      consumes:
      - application/json
      description: Validate user profile data without saving
      parameters:
      - description: User profile data to validate
        in: body
        name: profile
        required: true
        schema:
          $ref: '#/definitions/services.UpdateUserProfileInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: boolean
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Validate profile data
      tags:
      - user-profiles
  /user-search:
    get:
      consumes:
      - application/json
      description: Search users by query with advanced filtering options
      parameters:
      - description: Search query
        in: query
        name: query
        required: true
        type: string
      - collectionFormat: multi
        description: Skills to search for
        in: query
        items:
          type: string
        name: skills
        type: array
      - description: Location to search for
        in: query
        name: location
        type: string
      - description: Company to search for
        in: query
        name: company
        type: string
      - description: Role to filter by
        in: query
        name: role
        type: string
      - description: Status to filter by
        in: query
        name: status
        type: string
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserSearchResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Search users with advanced filtering
      tags:
      - user-search
  /user-search/company:
    get:
      consumes:
      - application/json
      description: Search users by company
      parameters:
      - description: Company to search for
        in: query
        name: company
        required: true
        type: string
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserSearchResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Search users by company
      tags:
      - user-search
  /user-search/location:
    get:
      consumes:
      - application/json
      description: Search users by location
      parameters:
      - description: Location to search for
        in: query
        name: location
        required: true
        type: string
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserSearchResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Search users by location
      tags:
      - user-search
  /user-search/popular-locations:
    get:
      consumes:
      - application/json
      description: Get popular locations used by users
      parameters:
      - default: 20
        description: Limit for locations
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get popular locations
      tags:
      - user-search
  /user-search/popular-skills:
    get:
      consumes:
      - application/json
      description: Get popular skills used by users
      parameters:
      - default: 20
        description: Limit for skills
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get popular skills
      tags:
      - user-search
  /user-search/skills:
    get:
      consumes:
      - application/json
      description: Search users by specific skills
      parameters:
      - collectionFormat: multi
        description: Skills to search for
        in: query
        items:
          type: string
        name: skills
        required: true
        type: array
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserSearchResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Search users by skills
      tags:
      - user-search
  /user-search/suggestions:
    get:
      consumes:
      - application/json
      description: Get search suggestions based on partial query
      parameters:
      - description: Partial search query
        in: query
        name: query
        required: true
        type: string
      - default: 10
        description: Limit for suggestions
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get search suggestions
      tags:
      - user-search
  /user-social-links:
    post:
      consumes:
      - application/json
      description: Create a new social link for a user
      parameters:
      - description: Social link data
        in: body
        name: link
        required: true
        schema:
          $ref: '#/definitions/services.CreateUserSocialLinkInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.UserSocialLink'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new social link
      tags:
      - user-social-links
  /user-social-links/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a social link
      parameters:
      - description: Social Link ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Delete a social link
      tags:
      - user-social-links
    put:
      consumes:
      - application/json
      description: Update a social link's information
      parameters:
      - description: Social Link ID
        in: path
        name: id
        required: true
        type: integer
      - description: Social link update data
        in: body
        name: link
        required: true
        schema:
          $ref: '#/definitions/services.UpdateUserSocialLinkInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserSocialLink'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update a social link
      tags:
      - user-social-links
  /user-social-links/{id}/unverify:
    post:
      consumes:
      - application/json
      description: Unverify a social link
      parameters:
      - description: Social Link ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Unverify a social link
      tags:
      - user-social-links
  /user-social-links/{id}/verify:
    post:
      consumes:
      - application/json
      description: Verify a social link
      parameters:
      - description: Social Link ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Verify a social link
      tags:
      - user-social-links
  /user-social-links/bulk/verify:
    put:
      consumes:
      - application/json
      description: Update verification status for multiple social links at once
      parameters:
      - description: Bulk verification data
        in: body
        name: data
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk update verification status
      tags:
      - user-social-links
  /user-social-links/stats:
    get:
      consumes:
      - application/json
      description: Retrieve statistics about social platform usage for the current
        tenant
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.SocialPlatformStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get platform statistics
      tags:
      - user-social-links
  /user-social-links/user/{user_id}:
    get:
      consumes:
      - application/json
      description: Retrieve all social links for a user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.UserSocialLink'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user social links
      tags:
      - user-social-links
  /user-social-links/user/{user_id}/bulk:
    post:
      consumes:
      - application/json
      description: Create multiple social links for a user at once
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Social links data
        in: body
        name: links
        required: true
        schema:
          items:
            $ref: '#/definitions/services.CreateUserSocialLinkInput'
          type: array
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk create social links
      tags:
      - user-social-links
  /user-social-links/user/{user_id}/platform/{platform}:
    get:
      consumes:
      - application/json
      description: Retrieve a social link by user ID and platform
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Social platform
        in: path
        name: platform
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserSocialLink'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get social link by platform
      tags:
      - user-social-links
  /user-social-links/user/{user_id}/public:
    get:
      consumes:
      - application/json
      description: Retrieve public social links for a user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.UserSocialLink'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get public social links
      tags:
      - user-social-links
  /user-social-links/user/{user_id}/reorder:
    post:
      consumes:
      - application/json
      description: Reorder social links for a user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Link order data
        in: body
        name: orders
        required: true
        schema:
          items:
            $ref: '#/definitions/models.UserSocialLinkOrder'
          type: array
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Reorder social links
      tags:
      - user-social-links
  /user-social-links/validate:
    post:
      consumes:
      - application/json
      description: Validate social link data without saving
      parameters:
      - description: Social link data to validate
        in: body
        name: link
        required: true
        schema:
          $ref: '#/definitions/services.CreateUserSocialLinkInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: boolean
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Validate social link data
      tags:
      - user-social-links
  /user-verification/{user_id}/email/send:
    post:
      consumes:
      - application/json
      description: Send email verification to user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Send email verification
      tags:
      - user-verification
  /user-verification/{user_id}/email/verify:
    post:
      consumes:
      - application/json
      description: Verify user email with verification token
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Verification token
        in: body
        name: token
        required: true
        schema:
          additionalProperties:
            type: string
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Verify email with token
      tags:
      - user-verification
  /user-verification/{user_id}/phone/send:
    post:
      consumes:
      - application/json
      description: Send phone verification to user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Send phone verification
      tags:
      - user-verification
  /user-verification/{user_id}/phone/verify:
    post:
      consumes:
      - application/json
      description: Verify user phone with verification code
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Verification code
        in: body
        name: code
        required: true
        schema:
          additionalProperties:
            type: string
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Verify phone with code
      tags:
      - user-verification
  /user-verification/{user_id}/resend:
    post:
      consumes:
      - application/json
      description: Resend verification for email or phone
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      - description: Verification type (email or phone)
        in: body
        name: type
        required: true
        schema:
          additionalProperties:
            type: string
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Resend verification
      tags:
      - user-verification
  /user-verification/{user_id}/status:
    get:
      consumes:
      - application/json
      description: Check the verification status of a user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.VerificationStatus'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Check verification status
      tags:
      - user-verification
  /users:
    get:
      consumes:
      - application/json
      description: Retrieve a list of users with pagination and filtering options.
        Supports both tenant-specific and global listing.
      parameters:
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Filter by role
        in: query
        name: role
        type: string
      - description: Filter by email verification status
        in: query
        name: email_verified
        type: boolean
      - description: Filter by phone verification status
        in: query
        name: phone_verified
        type: boolean
      - description: Filter by two-factor authentication status
        in: query
        name: two_factor_enabled
        type: boolean
      - description: Search query
        in: query
        name: search
        type: string
      - description: Search across all tenants (requires admin privileges)
        in: query
        name: global
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserListResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: List users
      tags:
      - users
  /users/{id}:
    delete:
      consumes:
      - application/json
      description: Soft delete a user with tenant authorization
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Delete a user
      tags:
      - users
    get:
      consumes:
      - application/json
      description: Retrieve a user by their unique identifier with tenant membership
        information
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.User'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get a user by ID
      tags:
      - users
  /users/{id}/password:
    put:
      consumes:
      - application/json
      description: Update a user's password
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Password data
        in: body
        name: password
        required: true
        schema:
          additionalProperties:
            type: string
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update user password
      tags:
      - users
  /users/{id}/status:
    put:
      consumes:
      - application/json
      description: Update the status of a user with tenant authorization
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Status data
        in: body
        name: status
        required: true
        schema:
          additionalProperties:
            type: string
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update user status
      tags:
      - users
  /users/{id}/two-factor/disable:
    post:
      consumes:
      - application/json
      description: Disable two-factor authentication for a user
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Disable two-factor authentication
      tags:
      - users
  /users/{id}/two-factor/enable:
    post:
      consumes:
      - application/json
      description: Enable two-factor authentication for a user
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.TwoFactorSetup'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Enable two-factor authentication
      tags:
      - users
  /users/{id}/verify-email:
    post:
      consumes:
      - application/json
      description: Verify a user's email address
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Verify user email
      tags:
      - users
  /users/{id}/verify-phone:
    post:
      consumes:
      - application/json
      description: Verify a user's phone number
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Verify user phone
      tags:
      - users
  /users/{userId}/memberships:
    get:
      consumes:
      - application/json
      description: Retrieve all tenant memberships for a specific user
      parameters:
      - description: User ID
        in: path
        name: userId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_user_models.TenantMembership'
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user memberships
      tags:
      - tenant-memberships
  /users/bulk/status:
    put:
      consumes:
      - application/json
      description: Update the status of multiple users at once
      parameters:
      - description: Bulk update data
        in: body
        name: data
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Bulk update user status
      tags:
      - users
  /users/search:
    get:
      consumes:
      - application/json
      description: Search users by query with pagination. Supports both tenant-specific
        and global search.
      parameters:
      - description: Search query
        in: query
        name: query
        required: true
        type: string
      - description: Cursor for pagination
        in: query
        name: cursor
        type: string
      - default: 20
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      - description: Search across all tenants (requires admin privileges)
        in: query
        name: global
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserSearchResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Search users
      tags:
      - users
  /users/stats:
    get:
      consumes:
      - application/json
      description: Retrieve statistics for users in the current tenant
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/services.UserStats'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get user statistics
      tags:
      - users
  /verification-status:
    get:
      consumes:
      - application/json
      description: Get the verification status for a user's email
      parameters:
      - description: User email address
        in: query
        name: email
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Email verification status
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/github_com_tranthanhloi_wn-api-v3_internal_modules_auth_dto.VerificationStatusResponse'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/response.Response'
      summary: Get email verification status
      tags:
      - auth
swagger: "2.0"
