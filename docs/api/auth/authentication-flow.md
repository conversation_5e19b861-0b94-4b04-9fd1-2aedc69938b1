# Authentication Flow Documentation

This document describes the new JWT authentication architecture and multi-tenant access patterns implemented in Blog API v3.

## Overview

The authentication system has been redesigned to support a clear separation between user identity and tenant context:

- **JWT Tokens**: Contain only user-level information (user_id, email, session_id)
- **Tenant Context**: Managed via `X-Tenant-ID` header and tenant membership validation
- **User Management**: Global users can belong to multiple tenants with different roles

## Authentication Flow

### 1. User Registration

```http
POST /api/cms/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "requires_email_verification": true,
    "email_verification_sent": true
  }
}
```

**Important**: Registration creates a global user account without tenant affiliation. Users must complete onboarding to join or create tenants.

### 2. User Login

```http
POST /api/cms/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "token_type": "Bearer",
    "session_id": 123,
    "requires_two_factor": false,
    "requires_email_verification": false,
    "requires_onboarding": false
  }
}
```

### 3. JWT Token Structure

The JWT token contains minimal user information:

```json
{
  "user_id": 123,
  "email": "<EMAIL>",
  "session_id": 789,
  "scopes": ["read", "write"],
  "iss": "blog-api-v3",
  "exp": **********,
  "iat": **********
}
```

**Key Changes:**
- ❌ No `current_tenant_id` field
- ❌ No `tenant_memberships` array
- ❌ No `role` information
- ✅ Only user-level identity and session data

## Multi-Tenant Access Patterns

### Method 1: X-Tenant-ID Header (Recommended)

For each API request requiring tenant context, include the tenant ID in headers:

```http
GET /api/cms/v1/blog/posts
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-Tenant-ID: 456
```

**Validation Flow:**
1. JWT middleware validates the token and extracts user_id
2. Tenant middleware reads X-Tenant-ID header
3. System validates: `UserBelongsToTenant(user_id, tenant_id)`
4. If valid, tenant_id is added to request context
5. All subsequent operations are scoped to that tenant

### Method 2: Tenant Switch API (Optional)

Alternative approach for clients that prefer token-based tenant context:

```http
POST /api/cms/v1/auth/switch-tenant
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "tenant_id": 456
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": **********
  }
}
```

**Note**: Even after switching tenant, the new token still contains only user information. The client should continue to send `X-Tenant-ID` header.

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/cms/v1/auth/register` | Register new user account |
| POST | `/api/cms/v1/auth/login` | Authenticate user and get tokens |
| POST | `/api/cms/v1/auth/logout` | Logout current session |
| POST | `/api/cms/v1/auth/logout-all` | Logout all user sessions |
| POST | `/api/cms/v1/auth/refresh` | Refresh access token |
| GET | `/api/cms/v1/auth/profile` | Get current user profile |

### Session Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/cms/v1/auth/sessions` | List active sessions |
| DELETE | `/api/cms/v1/auth/sessions/{id}` | Revoke specific session |

### Multi-Tenant Authentication

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/cms/v1/auth/switch-tenant` | Switch active tenant context |
| POST | `/api/cms/v1/auth/refresh-with-tenant` | Refresh token maintaining tenant context |

### Email Verification

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/cms/v1/auth/verify-email` | Verify email with token |
| POST | `/api/cms/v1/auth/resend-verification` | Resend verification email |
| GET | `/api/cms/v1/auth/verification-status` | Check verification status |

## Error Handling

### Authentication Errors

| Status Code | Error | Description |
|-------------|-------|-------------|
| 400 | `invalid_request` | Malformed request or validation errors |
| 401 | `invalid_credentials` | Wrong email or password |
| 401 | `invalid_token` | JWT token is invalid or expired |
| 403 | `email_not_verified` | User must verify email before login |
| 403 | `account_inactive` | User account is deactivated |
| 403 | `account_suspended` | User account is suspended |
| 429 | `account_locked` | Too many failed login attempts |

### Tenant Access Errors

| Status Code | Error | Description |
|-------------|-------|-------------|
| 400 | `missing_tenant_header` | X-Tenant-ID header is required |
| 403 | `tenant_access_denied` | User is not a member of the tenant |
| 403 | `tenant_membership_inactive` | User's tenant membership is inactive |
| 404 | `tenant_not_found` | Specified tenant does not exist |

## Security Considerations

### Token Security
- JWT tokens are stateless and contain no sensitive tenant information
- Session validation occurs through database lookup
- Token expiration is enforced (default: 1 hour for access, 7 days for refresh)

### Tenant Isolation
- All tenant membership checks are performed in real-time
- No tenant information is cached in JWT tokens
- Each request validates user's current access to the requested tenant

### Best Practices

1. **Always include X-Tenant-ID header** for tenant-scoped operations
2. **Handle token refresh gracefully** when tokens expire
3. **Validate tenant access on frontend** before making API calls
4. **Use HTTPS only** for all authentication endpoints
5. **Implement proper logout** to invalidate sessions

## Implementation Examples

### Frontend Authentication State

```javascript
class AuthService {
  constructor() {
    this.token = localStorage.getItem('access_token');
    this.refreshToken = localStorage.getItem('refresh_token');
    this.currentTenantId = localStorage.getItem('current_tenant_id');
  }

  async makeRequest(url, options = {}) {
    const headers = {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add tenant context for tenant-scoped endpoints
    if (this.currentTenantId && this.isTenantScopedEndpoint(url)) {
      headers['X-Tenant-ID'] = this.currentTenantId;
    }

    return fetch(url, { ...options, headers });
  }

  isTenantScopedEndpoint(url) {
    const tenantScopedPaths = ['/blog/', '/media/', '/users/', '/settings/'];
    return tenantScopedPaths.some(path => url.includes(path));
  }
}
```

### Backend Middleware Usage

```go
// JWT Authentication - validates token and extracts user info
router.Use(middleware.JWTAuth())

// Tenant Context - validates tenant access and sets context
tenantRoutes := router.Group("/api/cms/v1")
tenantRoutes.Use(middleware.TenantContext())

// All routes under tenantRoutes automatically have tenant_id in context
tenantRoutes.GET("/blog/posts", blogHandler.GetPosts)
tenantRoutes.POST("/blog/posts", blogHandler.CreatePost)
```

## Migration Notes

### Breaking Changes from Previous Architecture

1. **JWT Payload**: Removed `current_tenant_id` and `tenant_memberships` fields
2. **API Headers**: `X-Tenant-ID` header now required for tenant-scoped endpoints
3. **Error Responses**: New error codes for tenant access validation
4. **Authentication Flow**: Login no longer automatically sets tenant context

### Migration Checklist

- [ ] Update frontend to include `X-Tenant-ID` header
- [ ] Remove dependencies on `current_tenant_id` from JWT payload
- [ ] Update error handling for new tenant access errors
- [ ] Test multi-tenant user scenarios
- [ ] Update API documentation and examples

---

**Last Updated**: 2025-01-22  
**Version**: 2.0.0  
**Architecture**: User-only JWT + Header-based Tenant Context