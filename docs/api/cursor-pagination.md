# Phân trang bằng Cursor (Cursor Pagination)

API sử dụng phương pháp phân trang bằng cursor cho tất cả các endpoint trả về danh sách. Phương pháp này hiệu quả và đảm bảo thứ tự ổn định, đặc biệt với các tập dữ liệu lớn.

## Quy tắc hoạt động

Thay vì dùng số trang, phương pháp này sử dụng một chuỗi `cursor` để trỏ đến một vị trí cụ thể trong danh sách. Client sử dụng `cursor` từ lần phản hồi trước để yêu cầu trang dữ liệu tiếp theo.

---

## Quy tắc cho Yêu cầu (Request)

### Tham số Query

| Tham số | Bắt buộc | Kiểu dữ liệu | <PERSON><PERSON> tả |
|-----------|----------|-------------|---------|
| `cursor` | Không | string | Chuỗi cursor lấy từ phản hồi trước đó. Để trống nếu muốn lấy trang đầu tiên. |
| `limit` | Không | integer | Số lượng mục trên mỗi trang. Mặc định là `20`, tối đa là `100`. |

### Yêu cầu mẫu

**Lấy trang đầu tiên:**
```http
GET /api/v1/resource?limit=20
```

**Lấy trang tiếp theo:**
```http
GET /api/v1/resource?cursor=eyJpZCI6MjB9&limit=20
```

---

## Quy tắc cho Phản hồi (Response)

Tất cả các phản hồi phân trang sẽ có cấu trúc JSON như sau:

```json
{
  "success": true,
  "data": [
    // Mảng chứa các mục dữ liệu của trang hiện tại
  ],
  "meta": {
    "next_cursor": "eyJpZCI6NDB9",
    "has_more": true
  }
}
```

### Các trường trong `meta`

| Trường | Kiểu dữ liệu | Mô tả |
|--------------|-------------|-------------|
| `next_cursor`| string \| null | Chuỗi cursor để lấy trang tiếp theo. Nếu giá trị là `null` hoặc chuỗi rỗng, có nghĩa là đã hết dữ liệu. |
| `has_more` | boolean | Cờ `true`/`false` cho biết có trang tiếp theo hay không. |

---

## Quy tắc phía Client

1.  **Không tự tạo hoặc chỉnh sửa `cursor`**: Luôn sử dụng chuỗi `cursor` được cung cấp nguyên vẹn từ `meta.next_cursor` của phản hồi trước đó.
2.  **Kiểm tra `next_cursor` để dừng lại**: Khi `meta.next_cursor` là `null` hoặc chuỗi rỗng (hoặc `meta.has_more` là `false`), client phải hiểu rằng không còn trang nào để tải.
3.  **Bắt đầu lại từ đầu nếu `cursor` không hợp lệ**: Nếu API trả về lỗi `INVALID_CURSOR` hoặc `CURSOR_EXPIRED`, client nên xóa `cursor` hiện tại và thực hiện lại yêu cầu để lấy trang đầu tiên.
