# Hướng dẫn Multi-Tenant

Tài liệu này cung cấp hướng dẫn chi tiết cho các nhà phát triển làm việc với hệ thống API multi-tenant.

## 1. Tổng quan về Kiến trúc

Hệ thống được xây dựng theo mô hình multi-tenant, trong đó:

-   **Người dùng là Global**: Một tài khoản người dùng (dựa trên email) là duy nhất trên toàn hệ thống và không bị giới hạn trong một tenant nào.
-   **Thành viên Tenant (Tenant Membership)**: Người dùng có thể là thành viên của nhiều tenant khác nhau, với các vai trò và quyền hạn riêng biệt trong từng tenant.
-   **<PERSON><PERSON> cảnh (Context) trong JWT**: <PERSON><PERSON> <PERSON>hi đăng nhập, JWT token của người dùng sẽ chứa `CurrentTenantID`, xác định tenant mà người dùng đang làm việc.
-   **Chuyển đổi Ngữ cảnh**: Người dùng có thể dễ dàng chuyển đổi giữa các tenant mà họ là thành viên thông qua một API endpoint chuyên dụng.

### Sơ đồ Quan hệ Dữ liệu

```mermaid
erDiagram
    users ||--o{ tenant_memberships : "có"
    tenants ||--o{ tenant_memberships : "có"
    tenants ||--o{ websites : "sở hữu"

    users {
        int id PK
        string email UK
        string status
    }

    tenants {
        int id PK
        string name
        string slug UK
    }

    tenant_memberships {
        int id PK
        int user_id FK
        int tenant_id FK
        string status
    }

    websites {
        int id PK
        int tenant_id FK
        string name
    }
```

---

## 2. Luồng Xác thực và Quản lý Ngữ cảnh

### 2.1. Đăng ký và Onboarding

1.  **Đăng ký (`POST /auth/register`)**: Người dùng đăng ký một tài khoản global. Ở bước này, tài khoản chưa thuộc về bất kỳ tenant nào.
2.  **Onboarding**: Sau khi đăng ký, người dùng sẽ được hướng dẫn qua quy trình onboarding để:
    *   Tạo một tenant mới (nếu là người dùng mới).
    *   Hoặc tham gia vào một tenant đã có thông qua lời mời.
3.  **Đăng nhập (`POST /auth/login`)**: Khi đăng nhập, hệ thống tạo JWT token chỉ chứa thông tin người dùng. Client sau đó cần gửi `X-Tenant-ID` header trong các request tiếp theo để xác định tenant context.

### 2.2. Cấu trúc JWT Token

Token chỉ chứa thông tin người dùng toàn cục, không bao gồm thông tin tenant cụ thể.

```json
{
  "user_id": 123,
  "email": "<EMAIL>",
  "session_id": 789,
  "scopes": ["read", "write"],
  "exp": 1640995200
}
```

**Quan trọng**: Token không còn chứa `current_tenant_id` hay `tenant_memberships`. Thông tin này phải được gửi qua header `X-Tenant-ID` và được xác thực thông qua tenant membership repository.

### 2.3. Chuyển đổi Ngữ cảnh Tenant

Có hai cách để làm việc với tenant context:

#### Phương pháp 1: Sử dụng X-Tenant-ID Header (Khuyến nghị)
Client gửi tenant ID trong header cho mỗi request:

```http
GET /api/cms/v1/blog/posts
Authorization: Bearer <jwt_token>
X-Tenant-ID: 500
```

#### Phương pháp 2: Switch Tenant API (Tùy chọn)
**Endpoint**: `POST /auth/switch-tenant`

**Request Body**:
```json
{
  "tenant_id": 500
}
```

**Luồng hoạt động**:
1.  Client gửi yêu cầu chuyển đến `tenant_id` mới.
2.  Server xác thực rằng người dùng là thành viên hợp lệ của tenant được yêu cầu.
3.  Server tạo ra một cặp token (access và refresh) mới với thông tin user không đổi.
4.  Client có thể sử dụng token mới và gửi `X-Tenant-ID` header trong các request tiếp theo.

```mermaid
sequenceDiagram
    Client->>+API: POST /auth/switch-tenant (body: {tenant_id: 500})
    API->>+AuthHandler: SwitchTenant(c)
    AuthHandler->>+TenantMembershipRepo: UserBelongsToTenant(userID, 500)?
    TenantMembershipRepo-->>-AuthHandler: (true)
    AuthHandler->>+JWTService: GenerateTokenPair(userClaims)
    Note right of AuthHandler: userClaims chỉ chứa user_id, email, session_id
    JWTService-->>-AuthHandler: {new_access_token, new_refresh_token}
    AuthHandler-->>-API: 200 OK
    API-->>-Client: {access_token, refresh_token, expires_at}
```

#### Header-based Tenant Context Flow
```mermaid
sequenceDiagram
    Client->>+API: GET /api/cms/v1/blog/posts<br/>Authorization: Bearer token<br/>X-Tenant-ID: 500
    API->>+TenantMiddleware: ValidateTenantAccess(userID, 500)
    TenantMiddleware->>+TenantMembershipRepo: UserBelongsToTenant(userID, 500)?
    TenantMembershipRepo-->>-TenantMiddleware: (true)
    TenantMiddleware->>+API: Context with tenant_id: 500
    API->>+BlogHandler: GetPosts(c)
    BlogHandler->>+BlogService: GetPosts(tenantID=500, filters)
    BlogService-->>-BlogHandler: posts[]
    BlogHandler-->>-API: 200 OK
    API-->>-Client: {posts: [...]}
```

---

## 3. Hướng dẫn cho Lập trình viên Backend

### Middleware xử lý Ngữ cảnh

Hệ thống sử dụng hai middleware chính:

#### 1. JWT Authentication Middleware
- Xác thực JWT token từ header `Authorization`
- Trích xuất `user_id`, `email`, `session_id` từ token
- Đặt thông tin user vào Gin context

#### 2. Tenant Context Middleware
`TenantContextMiddleware` thực hiện các công việc sau:

1.  Đọc `X-Tenant-ID` từ request header
2.  Lấy `user_id` từ JWT claims đã được xác thực
3.  Xác thực quyền truy cập: `UserBelongsToTenant(user_id, tenant_id)`
4.  Đặt `tenant_id` vào context của Gin (`c.Set("tenant_id", tenant_id)`)

### Truy vấn Dữ liệu theo Tenant

Trong các repository, tất cả các truy vấn phải được giới hạn trong phạm vi của tenant hiện tại bằng cách sử dụng `tenant_id` từ context.

**Ví dụ trong GORM:**

```go
func (r *postRepository) FindByTenant(ctx context.Context, tenantID uint, postID uint) (*Post, error) {
    var post Post
    // Luôn thêm điều kiện Where("tenant_id = ?", tenantID)
    if err := r.db.WithContext(ctx).Where("tenant_id = ? AND id = ?", tenantID, postID).First(&post).Error; err != nil {
        return nil, err
    }
    return &post, nil
}
```

Việc này đảm bảo rằng dữ liệu của các tenant được cách ly hoàn toàn và một người dùng từ tenant A không thể vô tình hay cố ý truy cập dữ liệu của tenant B.
