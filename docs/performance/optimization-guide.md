# Performance Optimization Guide

## 1. Database Performance Optimization

### 1.1 Query Optimization
**Current Issues & Solutions**:

```go
// Before: N+1 Query Problem
func GetUsersWithPosts(db *gorm.DB) ([]User, error) {
    var users []User
    db.Find(&users)
    
    for i := range users {
        db.Model(&users[i]).Association("Posts").Find(&users[i].Posts)
    }
    return users, nil
}

// After: Eager Loading
func GetUsersWithPosts(db *gorm.DB) ([]User, error) {
    var users []User
    err := db.Preload("Posts").Find(&users).Error
    return users, err
}

// Advanced: Selective Preloading
func GetUsersWithRecentPosts(db *gorm.DB, limit int) ([]User, error) {
    var users []User
    err := db.Preload("Posts", func(db *gorm.DB) *gorm.DB {
        return db.Order("created_at DESC").Limit(limit)
    }).Find(&users).Error
    return users, err
}
```

### 1.2 Database Indexing Strategy
```sql
-- Essential indexes for performance
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_users_username ON users(username);
CREATE INDEX CONCURRENTLY idx_users_tenant_id ON users(tenant_id);
CREATE INDEX CONCURRENTLY idx_users_created_at ON users(created_at);
CREATE INDEX CONCURRENTLY idx_users_status ON users(status);

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY idx_users_tenant_status ON users(tenant_id, status);
CREATE INDEX CONCURRENTLY idx_posts_user_published ON posts(user_id, published_at) WHERE published_at IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_sessions_user_active ON sessions(user_id, is_active) WHERE is_active = true;

-- Partial indexes for better performance
CREATE INDEX CONCURRENTLY idx_users_active ON users(id) WHERE status = 'active';
CREATE INDEX CONCURRENTLY idx_posts_published ON posts(id, created_at) WHERE published_at IS NOT NULL;
```

### 1.3 Connection Pooling Optimization
```go
type DatabaseConfig struct {
    MaxOpenConns    int           `json:"max_open_conns" default:"25"`
    MaxIdleConns    int           `json:"max_idle_conns" default:"5"`
    ConnMaxLifetime time.Duration `json:"conn_max_lifetime" default:"5m"`
    ConnMaxIdleTime time.Duration `json:"conn_max_idle_time" default:"1m"`
}

func ConfigureDatabase(db *sql.DB, config DatabaseConfig) {
    db.SetMaxOpenConns(config.MaxOpenConns)
    db.SetMaxIdleConns(config.MaxIdleConns)
    db.SetConnMaxLifetime(config.ConnMaxLifetime)
    db.SetConnMaxIdleTime(config.ConnMaxIdleTime)
}
```

### 1.4 Read Replicas Implementation
```go
type DatabaseCluster struct {
    Master   *gorm.DB
    Replicas []*gorm.DB
    current  int
    mu       sync.RWMutex
}

func (dc *DatabaseCluster) GetReadDB() *gorm.DB {
    if len(dc.Replicas) == 0 {
        return dc.Master
    }
    
    dc.mu.Lock()
    defer dc.mu.Unlock()
    
    db := dc.Replicas[dc.current]
    dc.current = (dc.current + 1) % len(dc.Replicas)
    return db
}

func (dc *DatabaseCluster) GetWriteDB() *gorm.DB {
    return dc.Master
}

// Usage in repository
func (r *UserRepository) GetUser(id uint) (*User, error) {
    var user User
    err := r.cluster.GetReadDB().First(&user, id).Error
    return &user, err
}

func (r *UserRepository) CreateUser(user *User) error {
    return r.cluster.GetWriteDB().Create(user).Error
}
```

## 2. Caching Strategy

### 2.1 Multi-Level Caching
```go
type CacheManager struct {
    L1 *sync.Map          // In-memory cache
    L2 *redis.Client      // Redis cache
    L3 *memcached.Client  // Memcached for large objects
}

type CacheConfig struct {
    L1TTL time.Duration `json:"l1_ttl" default:"5m"`
    L2TTL time.Duration `json:"l2_ttl" default:"1h"`
    L3TTL time.Duration `json:"l3_ttl" default:"24h"`
}

func (cm *CacheManager) Get(ctx context.Context, key string) (interface{}, error) {
    // Try L1 cache first
    if value, ok := cm.L1.Load(key); ok {
        return value, nil
    }
    
    // Try L2 cache (Redis)
    if value, err := cm.L2.Get(ctx, key).Result(); err == nil {
        var data interface{}
        if err := json.Unmarshal([]byte(value), &data); err == nil {
            cm.L1.Store(key, data)
            return data, nil
        }
    }
    
    // Try L3 cache (Memcached)
    if item, err := cm.L3.Get(key); err == nil {
        var data interface{}
        if err := json.Unmarshal(item.Value, &data); err == nil {
            cm.setL2(ctx, key, data)
            cm.L1.Store(key, data)
            return data, nil
        }
    }
    
    return nil, errors.New("cache miss")
}
```

### 2.2 Smart Cache Invalidation
```go
type CacheInvalidator struct {
    cache   CacheManager
    patterns map[string][]string // entity -> cache key patterns
}

func (ci *CacheInvalidator) InvalidateUser(userID uint) {
    patterns := []string{
        fmt.Sprintf("user:%d", userID),
        fmt.Sprintf("user:%d:*", userID),
        "users:list:*",
        fmt.Sprintf("tenant:%d:users:*", getTenantID(userID)),
    }
    
    for _, pattern := range patterns {
        ci.invalidatePattern(pattern)
    }
}

// Cache-aside pattern with automatic invalidation
func (s *UserService) GetUser(ctx context.Context, id uint) (*User, error) {
    cacheKey := fmt.Sprintf("user:%d", id)
    
    // Try cache first
    if cached, err := s.cache.Get(ctx, cacheKey); err == nil {
        return cached.(*User), nil
    }
    
    // Cache miss - get from database
    user, err := s.repo.GetUser(id)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    s.cache.Set(ctx, cacheKey, user, time.Hour)
    return user, nil
}
```

### 2.3 Response Caching Middleware
```go
type ResponseCacheConfig struct {
    TTL           time.Duration
    VaryBy        []string // Headers to vary cache by
    SkipAuth      bool     // Skip caching for authenticated requests
    CacheControl  string   // Cache-Control header value
}

func ResponseCacheMiddleware(config ResponseCacheConfig) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Skip caching for non-GET requests
        if c.Request.Method != "GET" {
            c.Next()
            return
        }
        
        // Skip caching for authenticated requests if configured
        if config.SkipAuth && c.GetHeader("Authorization") != "" {
            c.Next()
            return
        }
        
        // Generate cache key
        cacheKey := generateCacheKey(c, config.VaryBy)
        
        // Try to get cached response
        if cached, err := getFromCache(cacheKey); err == nil {
            c.Header("X-Cache", "HIT")
            c.Header("Cache-Control", config.CacheControl)
            c.Data(cached.StatusCode, cached.ContentType, cached.Body)
            return
        }
        
        // Capture response
        writer := &responseWriter{
            ResponseWriter: c.Writer,
            body:          &bytes.Buffer{},
        }
        c.Writer = writer
        
        c.Next()
        
        // Cache successful responses
        if writer.Status() == 200 {
            cached := CachedResponse{
                StatusCode:  writer.Status(),
                ContentType: writer.Header().Get("Content-Type"),
                Body:        writer.body.Bytes(),
            }
            setCache(cacheKey, cached, config.TTL)
            c.Header("X-Cache", "MISS")
        }
    }
}
```

## 3. API Performance Optimization

### 3.1 Request/Response Optimization
```go
// Compression middleware
func CompressionMiddleware() gin.HandlerFunc {
    return gzip.Gzip(gzip.DefaultCompression, gzip.WithExcludedExtensions([]string{
        ".png", ".gif", ".jpeg", ".jpg", ".ico", ".svg",
    }))
}

// Response pagination optimization
type PaginationConfig struct {
    DefaultLimit int `json:"default_limit" default:"20"`
    MaxLimit     int `json:"max_limit" default:"100"`
}

func OptimizedPagination(config PaginationConfig) gin.HandlerFunc {
    return func(c *gin.Context) {
        limit, _ := strconv.Atoi(c.DefaultQuery("limit", strconv.Itoa(config.DefaultLimit)))
        if limit > config.MaxLimit {
            limit = config.MaxLimit
        }
        
        offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))
        
        c.Set("limit", limit)
        c.Set("offset", offset)
        c.Next()
    }
}

// Field selection for reducing payload size
type FieldSelector struct {
    allowedFields map[string][]string
}

func (fs *FieldSelector) SelectFields(c *gin.Context, data interface{}) interface{} {
    fields := c.Query("fields")
    if fields == "" {
        return data
    }
    
    selectedFields := strings.Split(fields, ",")
    return fs.filterFields(data, selectedFields)
}
```

### 3.2 Concurrent Processing
```go
// Parallel data fetching
func (s *DashboardService) GetDashboardData(ctx context.Context, userID uint) (*DashboardData, error) {
    var wg sync.WaitGroup
    var mu sync.Mutex
    
    dashboard := &DashboardData{}
    errors := make([]error, 0)
    
    // Fetch user stats
    wg.Add(1)
    go func() {
        defer wg.Done()
        stats, err := s.getUserStats(ctx, userID)
        mu.Lock()
        if err != nil {
            errors = append(errors, err)
        } else {
            dashboard.UserStats = stats
        }
        mu.Unlock()
    }()
    
    // Fetch recent posts
    wg.Add(1)
    go func() {
        defer wg.Done()
        posts, err := s.getRecentPosts(ctx, userID)
        mu.Lock()
        if err != nil {
            errors = append(errors, err)
        } else {
            dashboard.RecentPosts = posts
        }
        mu.Unlock()
    }()
    
    // Fetch notifications
    wg.Add(1)
    go func() {
        defer wg.Done()
        notifications, err := s.getNotifications(ctx, userID)
        mu.Lock()
        if err != nil {
            errors = append(errors, err)
        } else {
            dashboard.Notifications = notifications
        }
        mu.Unlock()
    }()
    
    wg.Wait()
    
    if len(errors) > 0 {
        return nil, errors[0] // Return first error
    }
    
    return dashboard, nil
}
```

### 3.3 Background Job Optimization
```go
type JobQueue struct {
    workers    int
    jobChan    chan Job
    resultChan chan JobResult
    wg         sync.WaitGroup
}

type Job struct {
    ID       string
    Type     string
    Payload  interface{}
    Priority int
    Retry    int
    MaxRetry int
}

func NewJobQueue(workers int) *JobQueue {
    jq := &JobQueue{
        workers:    workers,
        jobChan:    make(chan Job, 1000),
        resultChan: make(chan JobResult, 1000),
    }
    
    // Start workers
    for i := 0; i < workers; i++ {
        go jq.worker(i)
    }
    
    return jq
}

func (jq *JobQueue) worker(id int) {
    for job := range jq.jobChan {
        result := jq.processJob(job)
        
        // Retry failed jobs
        if !result.Success && job.Retry < job.MaxRetry {
            job.Retry++
            // Exponential backoff
            time.Sleep(time.Duration(job.Retry*job.Retry) * time.Second)
            jq.jobChan <- job
        } else {
            jq.resultChan <- result
        }
    }
}

// Priority queue for jobs
type PriorityJobQueue struct {
    jobs []*Job
    mu   sync.Mutex
}

func (pq *PriorityJobQueue) Push(job *Job) {
    pq.mu.Lock()
    defer pq.mu.Unlock()
    
    pq.jobs = append(pq.jobs, job)
    sort.Slice(pq.jobs, func(i, j int) bool {
        return pq.jobs[i].Priority > pq.jobs[j].Priority
    })
}
```

## 4. Memory Optimization

### 4.1 Object Pooling
```go
// Object pool for frequently used objects
var userPool = sync.Pool{
    New: func() interface{} {
        return &User{}
    },
}

func GetUser() *User {
    return userPool.Get().(*User)
}

func PutUser(user *User) {
    // Reset user object
    *user = User{}
    userPool.Put(user)
}

// Buffer pool for JSON marshaling
var bufferPool = sync.Pool{
    New: func() interface{} {
        return &bytes.Buffer{}
    },
}

func MarshalJSON(v interface{}) ([]byte, error) {
    buf := bufferPool.Get().(*bytes.Buffer)
    defer func() {
        buf.Reset()
        bufferPool.Put(buf)
    }()
    
    encoder := json.NewEncoder(buf)
    if err := encoder.Encode(v); err != nil {
        return nil, err
    }
    
    return buf.Bytes(), nil
}
```

### 4.2 Memory-Efficient Data Structures
```go
// Use slices with pre-allocated capacity
func ProcessLargeDataset(size int) []Result {
    results := make([]Result, 0, size) // Pre-allocate capacity
    
    for i := 0; i < size; i++ {
        result := processItem(i)
        results = append(results, result)
    }
    
    return results
}

// Streaming JSON processing for large responses
func StreamJSONResponse(c *gin.Context, data <-chan interface{}) {
    c.Header("Content-Type", "application/json")
    c.Status(200)
    
    encoder := json.NewEncoder(c.Writer)
    c.Writer.WriteString("[")
    
    first := true
    for item := range data {
        if !first {
            c.Writer.WriteString(",")
        }
        encoder.Encode(item)
        first = false
        
        // Flush periodically
        if flusher, ok := c.Writer.(http.Flusher); ok {
            flusher.Flush()
        }
    }
    
    c.Writer.WriteString("]")
}
```

## 5. Network Optimization

### 5.1 HTTP/2 and Connection Reuse
```go
func ConfigureHTTPServer() *http.Server {
    return &http.Server{
        Addr:         ":8080",
        ReadTimeout:  15 * time.Second,
        WriteTimeout: 15 * time.Second,
        IdleTimeout:  60 * time.Second,
        Handler:      router,
        
        // Enable HTTP/2
        TLSConfig: &tls.Config{
            NextProtos: []string{"h2", "http/1.1"},
        },
    }
}

// Connection pooling for external API calls
var httpClient = &http.Client{
    Transport: &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
        DisableCompression:  false,
    },
    Timeout: 30 * time.Second,
}
```

### 5.2 CDN Integration
```go
type CDNConfig struct {
    Enabled    bool   `json:"enabled"`
    BaseURL    string `json:"base_url"`
    APIKey     string `json:"api_key"`
    CacheTTL   int    `json:"cache_ttl"`
}

func (c *CDNConfig) GetAssetURL(path string) string {
    if !c.Enabled {
        return path
    }
    return c.BaseURL + path
}

// Asset optimization middleware
func AssetOptimizationMiddleware(cdn CDNConfig) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Add cache headers for static assets
        if strings.HasPrefix(c.Request.URL.Path, "/static/") {
            c.Header("Cache-Control", "public, max-age=31536000")
            c.Header("ETag", generateETag(c.Request.URL.Path))
        }
        
        c.Next()
    }
}
```

## 6. Monitoring & Profiling

### 6.1 Performance Metrics
```go
type PerformanceMetrics struct {
    RequestDuration   prometheus.HistogramVec
    RequestCount      prometheus.CounterVec
    ActiveConnections prometheus.Gauge
    DatabaseQueries   prometheus.HistogramVec
    CacheHitRatio     prometheus.GaugeVec
}

func NewPerformanceMetrics() *PerformanceMetrics {
    return &PerformanceMetrics{
        RequestDuration: *prometheus.NewHistogramVec(
            prometheus.HistogramOpts{
                Name: "http_request_duration_seconds",
                Help: "HTTP request duration in seconds",
            },
            []string{"method", "endpoint", "status"},
        ),
        // ... other metrics
    }
}

func MetricsMiddleware(metrics *PerformanceMetrics) gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        c.Next()
        
        duration := time.Since(start).Seconds()
        status := strconv.Itoa(c.Writer.Status())
        
        metrics.RequestDuration.WithLabelValues(
            c.Request.Method,
            c.FullPath(),
            status,
        ).Observe(duration)
        
        metrics.RequestCount.WithLabelValues(
            c.Request.Method,
            c.FullPath(),
            status,
        ).Inc()
    }
}
```

### 6.2 Profiling Integration
```go
// Enable pprof in development
func EnableProfiling(router *gin.Engine) {
    if gin.Mode() == gin.DebugMode {
        pprof.Register(router)
    }
}

// Custom profiling middleware
func ProfilingMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        if c.Query("profile") == "true" {
            // Start CPU profiling
            f, err := os.Create("cpu.prof")
            if err == nil {
                pprof.StartCPUProfile(f)
                defer func() {
                    pprof.StopCPUProfile()
                    f.Close()
                }()
            }
        }
        
        c.Next()
    }
}
```

## 7. Implementation Roadmap

### Phase 1: Database Optimization (Week 1)
- [ ] Add missing database indexes
- [ ] Implement connection pooling
- [ ] Fix N+1 query problems
- [ ] Add query monitoring

### Phase 2: Caching Implementation (Week 2)
- [ ] Implement Redis caching
- [ ] Add response caching middleware
- [ ] Implement cache invalidation
- [ ] Add cache monitoring

### Phase 3: API Optimization (Week 3)
- [ ] Add compression middleware
- [ ] Implement field selection
- [ ] Optimize pagination
- [ ] Add concurrent processing

### Phase 4: Infrastructure (Week 4)
- [ ] Configure HTTP/2
- [ ] Implement CDN integration
- [ ] Add performance monitoring
- [ ] Setup profiling tools

## Performance Testing

### Load Testing Script
```bash
#!/bin/bash
# Load testing with Apache Bench
ab -n 10000 -c 100 -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/users

# Load testing with wrk
wrk -t12 -c400 -d30s --header "Authorization: Bearer $TOKEN" http://localhost:8080/api/users

# Database performance testing
pgbench -c 10 -j 2 -t 1000 your_database
```

### Benchmarking
```go
func BenchmarkGetUser(b *testing.B) {
    service := setupUserService()
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := service.GetUser(context.Background(), 1)
        if err != nil {
            b.Fatal(err)
        }
    }
}

func BenchmarkGetUserWithCache(b *testing.B) {
    service := setupUserServiceWithCache()
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := service.GetUser(context.Background(), 1)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

Việc implement những optimization này sẽ giúp cải thiện đáng kể performance của hệ thống, đặc biệt là khả năng xử lý concurrent requests và response time.