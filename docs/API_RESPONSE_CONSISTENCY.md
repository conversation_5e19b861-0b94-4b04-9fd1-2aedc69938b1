# API Response Consistency Guide

## Overview
This document outlines the standards for using `omitempty` JSON tags in DTO response structures to ensure consistent API responses.

## Core Principle
**API responses should be predictable and consistent.** Response DTOs should always return the same structure to provide stable API contracts for client applications.

## Rules for omitempty Usage

### ❌ **DO NOT use omitempty for:**
- **Core identity fields**: user_id, tenant_id, id, email
- **Status fields**: status, success, error codes, verification states
- **Count/numeric fields**: sessions_terminated, resend_count, max_resends
- **Boolean status flags**: email_verified, requires_onboarding, can_resend
- **Token data**: access_token, refresh_token, expires_in, token_type
- **Timestamp fields**: joined_at, last_used_at, expires_at (when part of core data)
- **Response arrays**: sessions, users, posts (return empty array [] instead of null)

### ✅ **DO use omitempty for:**
- **Optional personal data**: phone, bio, avatar_url, display_name
- **Nullable timestamps**: email_verified_at, phone_verified_at, last_login_at (null when not set)
- **Optional content**: excerpt, featured_image, description
- **Optional configuration**: device_name, local_username
- **Optional relationships**: category, tags (when they can be null)
- **Rate limiting fields**: next_resend_at (empty when no limit applies)
- **Conditional fields**: expires_at in verification responses (only when token exists)

## Implementation Pattern

### Standard Documentation Comments:
```go
// NO_OMITEMPTY: Core response fields should always be present for API consistency
type ResponseStruct struct {
    CoreField string `json:"core_field"`  // No omitempty
}

// KEEP_OMITEMPTY: Optional field explanation
type ResponseStruct struct {
    OptionalField string `json:"optional_field,omitempty"`  // Keep omitempty
}

// Mixed example:
// NO_OMITEMPTY: Status and counts always present
// KEEP_OMITEMPTY: Optional timestamps and personal data
type MixedResponse struct {
    Status      string     `json:"status"`                    // Always present
    UserID      uint       `json:"user_id"`                   // Always present
    Phone       string     `json:"phone,omitempty"`           // Optional personal data
    VerifiedAt  *time.Time `json:"verified_at,omitempty"`     // Null when not verified
}
```

## Search Guidelines

### Finding DTO Response Issues:
```bash
# Find all DTO files with omitempty
grep -r "omitempty" internal/modules/*/dto/

# Find specific patterns
grep -r "json:.*,omitempty" --include="*dto*.go"

# Search for specific comments
grep -r "NO_OMITEMPTY\|KEEP_OMITEMPTY" internal/modules/*/dto/
```

### Documentation Comments for Easy Identification:
- **NO_OMITEMPTY**: Marks fields that should NOT use omitempty
- **KEEP_OMITEMPTY**: Marks fields that should KEEP omitempty with reasoning

## Examples

### ✅ **Correct Usage:**
```go
// Authentication Response - Always return complete token data
type LoginResponse struct {
    AccessToken  string `json:"access_token"`     // NO_OMITEMPTY: Core auth data
    RefreshToken string `json:"refresh_token"`    // NO_OMITEMPTY: Core auth data
    ExpiresIn    int    `json:"expires_in"`       // NO_OMITEMPTY: Core auth data
    TokenType    string `json:"token_type"`       // NO_OMITEMPTY: Core auth data
}

// User Profile - Mix of core and optional data
type UserResponse struct {
    UserID      uint       `json:"user_id"`                 // NO_OMITEMPTY: Core identity
    Email       string     `json:"email"`                   // NO_OMITEMPTY: Core identity
    Status      string     `json:"status"`                  // NO_OMITEMPTY: Core status
    Phone       string     `json:"phone,omitempty"`         // KEEP_OMITEMPTY: Optional personal data
    VerifiedAt  *time.Time `json:"verified_at,omitempty"`   // KEEP_OMITEMPTY: Null when not verified
}
```

### ❌ **Incorrect Usage:**
```go
// BAD: Core response data should always be present
type LoginResponse struct {
    AccessToken string `json:"access_token,omitempty"`   // ❌ Should always be present
    UserID      uint   `json:"user_id,omitempty"`        // ❌ Core identity field
    Status      string `json:"status,omitempty"`         // ❌ Status should always be present
}
```

## Benefits of Consistency

1. **Predictable API Contracts**: Clients can always expect the same response structure
2. **Type Safety**: Prevents null reference errors in client applications  
3. **Better Documentation**: API schemas are more explicit and clear
4. **Easier Testing**: Consistent response structures are easier to validate
5. **Frontend Development**: Eliminates need for defensive coding around missing fields

## Implementation Status

### ✅ **Files Updated** (NO_OMITEMPTY pattern applied):
- `internal/modules/auth/dto/profile_dto.go`
- `internal/modules/auth/dto/tenant_dto.go` 
- `internal/modules/auth/dto/email_verification_dto.go`
- `internal/modules/auth/dto/session_dto.go`
- `internal/modules/auth/dto/refresh_token_dto.go`

### 📋 **Files Using omitempty Appropriately** (verified):
- `internal/modules/auth/dto/login_dto.go` - Only in request fields (correct)
- `internal/modules/auth/dto/register_dto.go` - Only in request fields (correct)
- `internal/modules/user/dto/user_dto.go` - Optional personal data fields (appropriate)
- `internal/modules/blog/dto/blog_post_dto.go` - Optional content fields (appropriate)

## Migration Strategy

When updating existing DTOs:
1. **Identify response structures** (not request structures)
2. **Review each field** against the rules above
3. **Add documentation comments** (NO_OMITEMPTY/KEEP_OMITEMPTY)
4. **Test API responses** to ensure no breaking changes
5. **Update API documentation** if response structure changes

## Related Files
- All DTO files in `internal/modules/*/dto/` directories
- API response handling in handlers
- Bruno API test files for response validation