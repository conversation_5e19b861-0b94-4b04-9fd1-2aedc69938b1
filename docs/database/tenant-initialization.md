# Tenant Initialization - Khởi tạo dữ liệu cho Tenant mới

## UPDATED ARCHITECTURE: Tổng quan

**THAY ĐỔI QUAN TRỌNG**: Quy trình khởi tạo tenant đã được cập nhật để phù hợp với kiến trúc mới:

### Key Changes:
- **Separated Creation**: Tenant creation now happens via Onboarding Module, not during registration
- **User-Initiated**: Users create organizations when they're ready (not automatic)
- **Context-Aware**: Initialization happens with user context and preferences
- **Progressive Setup**: Step-by-step setup instead of bulk initialization

Tài liệu này mô tả quy trình khởi tạo dữ liệu khi một organization/tenant mới được tạo thông qua Onboarding Module. Bao gồm việc import default data, thiết lập cấu hình, và chuẩn bị môi trường cho tenant.

## Mục tiêu

### Updated Goals
- **User-Centric Setup**: Khởi tạo dựa trên user preferences và context
- **Progressive Initialization**: Setup từng bước thay vì bulk
- **Context-Aware**: Sử dụng thông tin user để customize setup
- **Onboarding Integration**: Tích hợp với Onboarding Module flows

### Traditional Goals
- **Automated Setup**: Tự động khởi tạo dữ liệu cho tenant mới
- **Consistent Data**: Đảm bảo dữ liệu khởi tạo nhất quán
- **Customizable**: Cho phép tùy chỉnh data template
- **Scalable**: Quy trình có thể scale với nhiều tenant
- **Rollback**: Có thể rollback khi có lỗi

## UPDATED: Luồng khởi tạo Tenant

### 1. NEW: Organization Creation Flow (via Onboarding)

```mermaid
sequenceDiagram
    participant User as Registered User
    participant Onboarding as Onboarding API
    participant Tenant as Tenant Service
    participant DB as Database
    participant Queue as Message Queue
    participant Seeder as Tenant Seeder
    participant Auth as Auth Service
    
    User->>Onboarding: POST /api/cms/v1/onboarding/organization
    Note over User,Onboarding: {name, domain?, plan, settings, user_preferences}
    
    Onboarding->>Onboarding: Validate organization data
    Onboarding->>Tenant: Create tenant with user context
    Tenant->>DB: Create tenant record (owner_user_id = user.id)
    DB->>Tenant: Tenant created
    
    Onboarding->>Queue: Publish "organization.created" event
    Note over Onboarding,Queue: {tenant_id, user_id, plan_type, user_preferences, settings}
    
    Queue->>Seeder: Process tenant initialization with user context
    Seeder->>Seeder: Load templates based on user preferences
    Seeder->>DB: Import user-customized data
    
    Seeder->>Auth: Update user context with tenant_id
    Auth->>Auth: Generate new JWT tokens with tenant context
    
    alt Success
        Seeder->>Queue: Publish "organization.initialized"
        Queue->>Onboarding: Update organization status
        Onboarding->>User: Return success + new tokens
    else Failure
        Seeder->>Queue: Publish "organization.init_failed"
        Queue->>Onboarding: Update organization status
        Onboarding->>User: Return error response + rollback
    end
```

### 2. Website Creation Flow

```mermaid
sequenceDiagram
    participant Tenant as Tenant Admin
    participant API as Website API
    participant DB as Database
    participant Queue as Message Queue
    participant Seeder as Website Seeder
    
    Tenant->>API: POST /api/cms/v1/websites
    Note over Tenant,API: {name, domain, theme, settings}
    
    API->>DB: Create website record
    DB->>API: Website created
    
    API->>Queue: Publish "website.created" event
    Note over API,Queue: {website_id, tenant_id, theme}
    
    Queue->>Seeder: Process website initialization
    Seeder->>Seeder: Load website templates
    Seeder->>DB: Import website data
    
    alt Success
        Seeder->>Queue: Publish "website.initialized"
        Queue->>API: Update website status
        API->>Tenant: Return success response
    else Failure
        Seeder->>Queue: Publish "website.init_failed"
        Queue->>API: Update website status
        API->>Tenant: Return error response
    end
```

## Data Templates

### 1. Template Structure

```
resources/templates/
├── tenant/
│   ├── basic/                 # Basic plan template
│   │   ├── roles.json
│   │   ├── permissions.json
│   │   ├── settings.json
│   │   └── users.json
│   ├── pro/                   # Pro plan template
│   │   ├── roles.json
│   │   ├── permissions.json
│   │   ├── settings.json
│   │   ├── users.json
│   │   └── categories.json
│   └── enterprise/            # Enterprise plan template
│       ├── roles.json
│       ├── permissions.json
│       ├── settings.json
│       ├── users.json
│       ├── categories.json
│       └── workflows.json
├── website/
│   ├── blog/                  # Blog website template
│   │   ├── pages.json
│   │   ├── menus.json
│   │   ├── widgets.json
│   │   └── theme.json
│   ├── ecommerce/             # E-commerce template
│   │   ├── pages.json
│   │   ├── menus.json
│   │   ├── products.json
│   │   └── categories.json
│   └── corporate/             # Corporate template
│       ├── pages.json
│       ├── menus.json
│       ├── services.json
│       └── team.json
└── common/
    ├── seo-defaults.json
    ├── notification-templates.json
    └── media-folders.json
```

### 2. Template Examples

#### Tenant Roles Template
```json
{
  "roles": [
    {
      "name": "Administrator",
      "slug": "admin",
      "description": "Full system access",
      "is_system": true,
      "is_default": false,
      "level": 100,
      "permissions": [
        "user.manage",
        "content.manage",
        "settings.manage",
        "analytics.view",
        "export.data"
      ]
    },
    {
      "name": "Editor",
      "slug": "editor",
      "description": "Content management access",
      "is_system": true,
      "is_default": false,
      "level": 80,
      "permissions": [
        "content.create",
        "content.edit",
        "content.publish",
        "media.upload",
        "comments.moderate"
      ]
    },
    {
      "name": "Author",
      "slug": "author",
      "description": "Content creation access",
      "is_system": true,
      "is_default": false,
      "level": 60,
      "permissions": [
        "content.create",
        "content.edit_own",
        "media.upload",
        "profile.edit"
      ]
    },
    {
      "name": "Subscriber",
      "slug": "subscriber",
      "description": "Basic user access",
      "is_system": true,
      "is_default": true,
      "level": 10,
      "permissions": [
        "content.read",
        "comments.create",
        "profile.edit"
      ]
    }
  ]
}
```

#### Website Pages Template
```json
{
  "pages": [
    {
      "title": "Home",
      "slug": "home",
      "content": "<h1>Welcome to Your Website</h1><p>This is your homepage. You can edit this content in the admin panel.</p>",
      "meta_title": "Home - Your Website",
      "meta_description": "Welcome to your new website",
      "is_homepage": true,
      "status": "published",
      "template": "home.html"
    },
    {
      "title": "About",
      "slug": "about",
      "content": "<h1>About Us</h1><p>Tell your visitors about your company, mission, and values.</p>",
      "meta_title": "About Us - Your Website",
      "meta_description": "Learn more about our company",
      "status": "published",
      "template": "page.html"
    },
    {
      "title": "Contact",
      "slug": "contact",
      "content": "<h1>Contact Us</h1><p>Get in touch with us.</p>",
      "meta_title": "Contact - Your Website",
      "meta_description": "Contact us for more information",
      "status": "published",
      "template": "contact.html"
    },
    {
      "title": "Privacy Policy",
      "slug": "privacy-policy",
      "content": "<h1>Privacy Policy</h1><p>Your privacy policy content here.</p>",
      "meta_title": "Privacy Policy - Your Website",
      "meta_description": "Our privacy policy",
      "status": "published",
      "template": "page.html"
    }
  ]
}
```

#### Blog Categories Template
```json
{
  "categories": [
    {
      "name": "Technology",
      "slug": "technology",
      "description": "Latest technology news and trends",
      "parent_id": null,
      "sort_order": 1,
      "meta_title": "Technology - Blog",
      "meta_description": "Technology articles and news"
    },
    {
      "name": "Web Development",
      "slug": "web-development",
      "description": "Web development tutorials and tips",
      "parent_slug": "technology",
      "sort_order": 1,
      "meta_title": "Web Development - Blog",
      "meta_description": "Web development articles"
    },
    {
      "name": "Mobile Development",
      "slug": "mobile-development",
      "description": "Mobile app development guides",
      "parent_slug": "technology",
      "sort_order": 2,
      "meta_title": "Mobile Development - Blog",
      "meta_description": "Mobile development articles"
    },
    {
      "name": "Business",
      "slug": "business",
      "description": "Business insights and strategies",
      "parent_id": null,
      "sort_order": 2,
      "meta_title": "Business - Blog",
      "meta_description": "Business articles and insights"
    }
  ]
}
```

## UPDATED: Seeder Implementation

### 1. Organization Seeder Service (User-Centric)

```go
package seeder

import (
    "context"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "path/filepath"
    
    "gorm.io/gorm"
)

type OrganizationSeeder struct {
    db           *gorm.DB
    templatePath string
}

type UserContext struct {
    UserID      uint32 `json:"user_id"`
    Email       string `json:"email"`
    FirstName   string `json:"first_name"`
    LastName    string `json:"last_name"`
    Preferences map[string]interface{} `json:"preferences"`
}

type OrganizationSetupRequest struct {
    TenantID    uint32                 `json:"tenant_id"`
    PlanType    string                 `json:"plan_type"`
    Settings    map[string]interface{} `json:"settings"`
    UserContext UserContext            `json:"user_context"`
}

func NewOrganizationSeeder(db *gorm.DB, templatePath string) *OrganizationSeeder {
    return &OrganizationSeeder{
        db:           db,
        templatePath: templatePath,
    }
}

func (s *OrganizationSeeder) SeedOrganization(ctx context.Context, req OrganizationSetupRequest) error {
    // Start transaction
    tx := s.db.Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()

    // Load template based on plan type and user preferences
    templateDir := s.selectTemplate(req.PlanType, req.UserContext.Preferences)
    
    // Seed roles with user as owner
    if err := s.seedRolesWithOwner(tx, req.TenantID, req.UserContext.UserID, templateDir); err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to seed roles: %w", err)
    }
    
    // Seed permissions based on plan
    if err := s.seedPermissions(tx, req.TenantID, templateDir); err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to seed permissions: %w", err)
    }
    
    // Seed user-customized settings
    if err := s.seedCustomSettings(tx, req.TenantID, req.Settings, req.UserContext); err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to seed settings: %w", err)
    }
    
    // Create tenant membership for user
    if err := s.createOwnerMembership(tx, req.TenantID, req.UserContext.UserID); err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to create membership: %w", err)
    }
    
    // Seed default website
    if err := s.seedDefaultWebsite(tx, req.TenantID, req.Settings); err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to seed website: %w", err)
    }
    
    // Commit transaction
    if err := tx.Commit().Error; err != nil {
        return fmt.Errorf("failed to commit transaction: %w", err)
    }
    
    return nil
}

func (s *OrganizationSeeder) selectTemplate(planType string, userPrefs map[string]interface{}) string {
    // Select template based on plan and user preferences
    baseTemplate := filepath.Join(s.templatePath, "organization", planType)
    
    // Customize based on user preferences
    if industry, ok := userPrefs["industry"].(string); ok {
        customTemplate := filepath.Join(baseTemplate, industry)
        if s.templateExists(customTemplate) {
            return customTemplate
        }
    }
    
    return baseTemplate
}

func (s *TenantSeeder) seedRoles(tx *gorm.DB, tenantID uint32, templateDir string) error {
    // Load roles template
    rolesFile := filepath.Join(templateDir, "roles.json")
    data, err := ioutil.ReadFile(rolesFile)
    if err != nil {
        return err
    }
    
    var template struct {
        Roles []RoleTemplate `json:"roles"`
    }
    
    if err := json.Unmarshal(data, &template); err != nil {
        return err
    }
    
    // Create roles
    for _, roleTemplate := range template.Roles {
        role := &models.Role{
            TenantID:    tenantID,
            Name:        roleTemplate.Name,
            Slug:        roleTemplate.Slug,
            Description: roleTemplate.Description,
            IsSystem:    roleTemplate.IsSystem,
            IsDefault:   roleTemplate.IsDefault,
            Level:       roleTemplate.Level,
            Permissions: roleTemplate.Permissions,
        }
        
        if err := tx.Create(role).Error; err != nil {
            return err
        }
    }
    
    return nil
}

func (s *OrganizationSeeder) createOwnerMembership(tx *gorm.DB, tenantID uint32, userID uint32) error {
    // Create tenant membership for existing user (no new user creation)
    membership := &models.TenantMembership{
        UserID:    userID,
        TenantID:  tenantID,
        IsPrimary: true,
        Status:    "active",
    }
    
    if err := tx.Create(membership).Error; err != nil {
        return err
    }
    
    // Assign owner role to existing user
    var ownerRole models.Role
    if err := tx.Where("tenant_id = ? AND slug = ?", tenantID, "owner").First(&ownerRole).Error; err != nil {
        return err
    }
    
    userRole := &models.UserRole{
        UserID:      userID,
        TenantID:    tenantID,
        RoleID:      ownerRole.ID,
        ContextType: "organization",
        ContextID:   tenantID,
    }
    
    if err := tx.Create(userRole).Error; err != nil {
        return err
    }
    
    return nil
}

func (s *OrganizationSeeder) seedCustomSettings(tx *gorm.DB, tenantID uint32, settings map[string]interface{}, userCtx UserContext) error {
    // Create organization settings based on user preferences and input
    orgSettings := &models.TenantSettings{
        TenantID: tenantID,
        Settings: settings,
    }
    
    // Add user-specific customizations
    if orgSettings.Settings == nil {
        orgSettings.Settings = make(map[string]interface{})
    }
    
    // Set owner information
    orgSettings.Settings["owner_name"] = userCtx.FirstName + " " + userCtx.LastName
    orgSettings.Settings["owner_email"] = userCtx.Email
    
    // Apply user preferences
    if userCtx.Preferences != nil {
        for key, value := range userCtx.Preferences {
            if key != "password" { // Security: don't store sensitive data
                orgSettings.Settings["user_"+key] = value
            }
        }
    }
    
    return tx.Create(orgSettings).Error
}

func (s *OrganizationSeeder) seedDefaultWebsite(tx *gorm.DB, tenantID uint32, settings map[string]interface{}) error {
    // Create default website for the organization
    website := &models.Website{
        TenantID:  tenantID,
        Name:      "Main Website",
        IsPrimary: true,
        Status:    "active",
    }
    
    // Set domain if provided
    if domain, ok := settings["domain"].(string); ok && domain != "" {
        website.Domain = domain
    }
    
    return tx.Create(website).Error
}
```

### 2. Website Seeder Service

```go
func (s *WebsiteSeeder) SeedWebsite(ctx context.Context, websiteID uint32, templateType string) error {
    tx := s.db.Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()

    templateDir := filepath.Join(s.templatePath, "website", templateType)
    
    // Seed pages
    if err := s.seedPages(tx, websiteID, templateDir); err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to seed pages: %w", err)
    }
    
    // Seed menus
    if err := s.seedMenus(tx, websiteID, templateDir); err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to seed menus: %w", err)
    }
    
    // Seed categories (if blog template)
    if templateType == "blog" {
        if err := s.seedCategories(tx, websiteID, templateDir); err != nil {
            tx.Rollback()
            return fmt.Errorf("failed to seed categories: %w", err)
        }
    }
    
    // Seed common data
    if err := s.seedCommonData(tx, websiteID); err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to seed common data: %w", err)
    }
    
    if err := tx.Commit().Error; err != nil {
        return fmt.Errorf("failed to commit transaction: %w", err)
    }
    
    return nil
}
```

## Event Handlers

### 1. Tenant Created Event Handler

```go
package handlers

import (
    "context"
    "encoding/json"
    "log"
    
    "github.com/your-org/blog-api/internal/seeder"
    "github.com/your-org/blog-api/pkg/queue"
)

type TenantCreatedHandler struct {
    seeder *seeder.TenantSeeder
    queue  queue.Publisher
}

func NewTenantCreatedHandler(seeder *seeder.TenantSeeder, queue queue.Publisher) *TenantCreatedHandler {
    return &TenantCreatedHandler{
        seeder: seeder,
        queue:  queue,
    }
}

func (h *TenantCreatedHandler) Handle(ctx context.Context, message []byte) error {
    var event TenantCreatedEvent
    if err := json.Unmarshal(message, &event); err != nil {
        return err
    }
    
    // Seed tenant data
    if err := h.seeder.SeedTenant(ctx, event.TenantID, event.PlanType); err != nil {
        log.Printf("Failed to seed tenant %d: %v", event.TenantID, err)
        
        // Publish failure event
        failureEvent := TenantInitFailedEvent{
            TenantID: event.TenantID,
            Error:    err.Error(),
        }
        
        if err := h.queue.Publish("tenant.init_failed", failureEvent); err != nil {
            log.Printf("Failed to publish failure event: %v", err)
        }
        
        return err
    }
    
    // Publish success event
    successEvent := TenantInitializedEvent{
        TenantID: event.TenantID,
        Status:   "initialized",
    }
    
    if err := h.queue.Publish("tenant.initialized", successEvent); err != nil {
        log.Printf("Failed to publish success event: %v", err)
    }
    
    return nil
}

type TenantCreatedEvent struct {
    TenantID uint32   `json:"tenant_id"`
    PlanType string `json:"plan_type"`
    Settings map[string]interface{} `json:"settings"`
}

type TenantInitializedEvent struct {
    TenantID uint32   `json:"tenant_id"`
    Status   string `json:"status"`
}

type TenantInitFailedEvent struct {
    TenantID uint32   `json:"tenant_id"`
    Error    string `json:"error"`
}
```

### 2. Website Created Event Handler

```go
type WebsiteCreatedHandler struct {
    seeder *seeder.WebsiteSeeder
    queue  queue.Publisher
}

func (h *WebsiteCreatedHandler) Handle(ctx context.Context, message []byte) error {
    var event WebsiteCreatedEvent
    if err := json.Unmarshal(message, &event); err != nil {
        return err
    }
    
    // Determine template type from settings
    templateType := "blog" // default
    if event.Settings != nil {
        if t, ok := event.Settings["template_type"].(string); ok {
            templateType = t
        }
    }
    
    // Seed website data
    if err := h.seeder.SeedWebsite(ctx, event.WebsiteID, templateType); err != nil {
        log.Printf("Failed to seed website %d: %v", event.WebsiteID, err)
        
        // Publish failure event
        failureEvent := WebsiteInitFailedEvent{
            WebsiteID: event.WebsiteID,
            Error:     err.Error(),
        }
        
        if err := h.queue.Publish("website.init_failed", failureEvent); err != nil {
            log.Printf("Failed to publish failure event: %v", err)
        }
        
        return err
    }
    
    // Publish success event
    successEvent := WebsiteInitializedEvent{
        WebsiteID: event.WebsiteID,
        Status:    "initialized",
    }
    
    if err := h.queue.Publish("website.initialized", successEvent); err != nil {
        log.Printf("Failed to publish success event: %v", err)
    }
    
    return nil
}
```

## Configuration

### 1. Seeder Configuration

```yaml
# config/seeder.yaml
seeder:
  template_path: "resources/templates"
  timeout: 30s
  retry_attempts: 3
  retry_delay: 5s
  
  # Plan-specific settings
  plans:
    basic:
      max_websites: 1
      max_users: 10
      max_storage: "1GB"
    pro:
      max_websites: 5
      max_users: 50
      max_storage: "10GB"
    enterprise:
      max_websites: unlimited
      max_users: unlimited
      max_storage: "100GB"
```

### 2. Database Configuration

```sql
-- Create seeder tracking table
CREATE TABLE blog_seeder_log (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    entity_type ENUM('tenant', 'website') NOT NULL,
    entity_id BIGINT UNSIGNED NOT NULL,
    template_type VARCHAR(50) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_blog_seeder_log_entity (entity_type, entity_id),
    INDEX idx_blog_seeder_log_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Seeder execution log';
```

## CLI Commands

### 1. Manual Seeding Commands

```bash
# Seed tenant with specific plan
go run cmd/cli/main.go seed:tenant --id=1 --plan=pro

# Seed website with template
go run cmd/cli/main.go seed:website --id=1 --template=blog

# Seed all tenants
go run cmd/cli/main.go seed:all-tenants

# Rollback seeding
go run cmd/cli/main.go seed:rollback --entity=tenant --id=1
```

### 2. CLI Implementation

```go
package main

import (
    "context"
    "flag"
    "fmt"
    "log"
    "os"
    
    "github.com/your-org/blog-api/internal/seeder"
    "gorm.io/gorm"
)

func main() {
    var (
        command = flag.String("command", "", "Command to run")
        id      = flag.Uint32("id", 0, "Entity ID")
        plan    = flag.String("plan", "basic", "Plan type")
        template = flag.String("template", "blog", "Template type")
    )
    flag.Parse()
    
    db := initDB() // Initialize database connection
    
    switch *command {
    case "seed:tenant":
        if *id == 0 {
            log.Fatal("Tenant ID is required")
        }
        seedTenant(db, *id, *plan)
    case "seed:website":
        if *id == 0 {
            log.Fatal("Website ID is required")
        }
        seedWebsite(db, *id, *template)
    default:
        fmt.Println("Available commands:")
        fmt.Println("  seed:tenant --id=<id> --plan=<plan>")
        fmt.Println("  seed:website --id=<id> --template=<template>")
    }
}

func seedTenant(db *gorm.DB, tenantID uint32, planType string) {
    seeder := seeder.NewTenantSeeder(db, "resources/templates")
    
    ctx := context.Background()
    if err := seeder.SeedTenant(ctx, tenantID, planType); err != nil {
        log.Fatalf("Failed to seed tenant: %v", err)
    }
    
    fmt.Printf("Successfully seeded tenant %d with plan %s\n", tenantID, planType)
}

func seedWebsite(db *gorm.DB, websiteID uint32, templateType string) {
    seeder := seeder.NewWebsiteSeeder(db, "resources/templates")
    
    ctx := context.Background()
    if err := seeder.SeedWebsite(ctx, websiteID, templateType); err != nil {
        log.Fatalf("Failed to seed website: %v", err)
    }
    
    fmt.Printf("Successfully seeded website %d with template %s\n", websiteID, templateType)
}
```

## Monitoring & Alerts

### 1. Seeding Metrics

```go
type SeederMetrics struct {
    TenantSeedingDuration   prometheus.Histogram
    WebsiteSeedingDuration  prometheus.Histogram
    SeedingSuccessRate      prometheus.Counter
    SeedingFailureRate      prometheus.Counter
    ActiveSeedingJobs       prometheus.Gauge
}

func (m *SeederMetrics) RecordTenantSeeding(duration time.Duration, success bool) {
    m.TenantSeedingDuration.Observe(duration.Seconds())
    if success {
        m.SeedingSuccessRate.Inc()
    } else {
        m.SeedingFailureRate.Inc()
    }
}
```

### 2. Health Checks

```go
func (s *TenantSeeder) HealthCheck() error {
    // Check template files exist
    if err := s.validateTemplates(); err != nil {
        return fmt.Errorf("template validation failed: %w", err)
    }
    
    // Check database connection
    if err := s.db.Exec("SELECT 1").Error; err != nil {
        return fmt.Errorf("database connection failed: %w", err)
    }
    
    return nil
}
```

## Best Practices

### 1. Template Management
- Version control templates
- Validate template structure
- Use consistent naming
- Include rollback data

### 2. Error Handling
- Comprehensive error logging
- Graceful degradation
- Retry mechanisms
- Rollback procedures

### 3. Performance
- Batch inserts where possible
- Use transactions
- Optimize queries
- Monitor seeding time

### 4. Security
- Validate template data
- Sanitize inputs
- Secure default passwords
- Audit seeding operations

### 5. Maintenance
- Regular template updates
- Monitor seeding success rates
- Clean up failed seeding data
- Update templates for new features