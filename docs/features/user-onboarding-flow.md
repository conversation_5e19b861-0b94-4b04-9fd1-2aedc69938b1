# User Onboarding Flow

Complete user journey from registration to active tenant with website setup.

## Overview

This document outlines the complete user onboarding flow with API endpoints for frontend integration. The flow involves multiple modules working together to create a seamless user experience.

> **Implementation Note:** The onboarding module now provides comprehensive APIs for user onboarding flow. The implementation includes both end-user focused APIs (`/my` endpoints) and admin management APIs (`/admin` endpoints). This documentation reflects the current working implementation with the new API structure.

## Complete Flow Diagram

```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as Auth API
    participant O as Onboarding API
    participant T as Tenant API
    participant W as Website API
    participant E as Email Service

    Note over F,E: Registration Phase
    F->>A: POST /api/cms/v1/auth/register
    A->>A: Create user (pending verification)
    A->>E: Send verification email
    A->>F: 201 - Registration successful (email verification sent)

    Note over F,E: Email Verification Phase
    F->>A: POST /api/cms/v1/auth/verify-email
    A->>A: Verify token & activate user
    A->>F: 200 - Email verified

    Note over F,E: Login Phase
    F->>A: POST /api/cms/v1/auth/login
    A->>A: Validate credentials + check 2FA/email status
    alt 2FA Required
        A->>F: 200 - requires_two_factor: true
        F->>A: POST /api/cms/v1/auth/login (with 2FA code)
    end
    A->>F: 200 - Auth tokens + session data

    Note over F,E: Organization Setup Phase
    F->>O: GET /api/cms/v1/onboarding/my/organization-status
    O->>F: 200 - Status (requires_setup: true)
    F->>O: POST /api/cms/v1/onboarding/my/organization
    Note right of O: Headers: Authorization Bearer
    O->>O: Create organization, tenant membership & start journey
    O->>F: 201 - Organization created + journey started

    Note over F,E: Onboarding Steps Phase
    F->>O: GET /api/cms/v1/onboarding/my/current-step
    O->>F: 200 - Current step details
    F->>O: POST /api/cms/v1/onboarding/my/complete-step
    O->>F: 200 - Step completed, next step available
    
    Note over F,E: Success State
    F->>O: GET /api/cms/v1/onboarding/my/status
    O->>F: 200 - Onboarding completed
    F->>F: Redirect to dashboard
```

## Detailed API Flow

### 1. Registration Phase

**Endpoint:** `POST /api/cms/v1/auth/register`

```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "first_name": "John",
  "last_name": "Doe",
  "username": "johndoe",
  "invitation_token": ""
}
```

**Response (201):**
```json
{
  "status": {
    "code": 201,
    "message": "Registration successful",
    "success": true,
    "timestamp": "2024-01-15T10:30:00Z",
    "path": "/api/cms/v1/auth/register"
  },
  "data": {
    "message": "Registration successful. Please check your email to verify your account.",
    "email_verification_sent": true,
    "requires_email_verification": true
  }
}
```

**Frontend Actions:**
- Show email verification notice with success message
- Store user email for verification step
- Redirect to email verification page
- Display instructions to check email inbox

---

### 2. Email Verification Phase

**Endpoint:** `POST /api/cms/v1/auth/verify-email`

```json
{
  "token": "verification_token_from_email"
}
```

**Response (200):**
```json
{
  "status": {
    "code": 200,
    "message": "Email verified successfully",
    "success": true,
    "timestamp": "2024-01-15T10:35:00Z",
    "path": "/api/cms/v1/auth/verify-email"
  },
  "data": {
    "email": "<EMAIL>",
    "email_verified": true,
    "status": "verified",
    "user_id": 1
  }
}
```

**Frontend Actions:**
- Show success message
- Enable login or proceed to onboarding

---

### 3. Login Phase (if user returns later)

**Endpoint:** `POST /api/cms/v1/auth/login`

```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "device_name": "iPhone 15 Pro",
  "two_factor_code": ""
}
```

**Response (200):**
```json
{
  "status": {
    "code": 200,
    "message": "Login successful",
    "success": true,
    "timestamp": "2024-01-15T10:40:00Z",
    "path": "/api/cms/v1/auth/login"
  },
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "session_id": 12345,
    "requires_email_verification": false,
    "requires_two_factor": false,
    "requires_onboarding": false
  }
}
```

**Note:** `requires_onboarding` will be `true` for users who have registered and verified their email but have not yet created their first tenant/organization. This allows the frontend to automatically redirect them to the onboarding flow.

**Frontend Logic:**
```javascript
const loginData = response.data;

// Handle 2FA requirement
if (loginData.requires_two_factor) {
  router.push('/auth/2fa');
  return;
}

// Handle email verification requirement
if (loginData.requires_email_verification) {
  router.push('/auth/verify-email');
  return;
}

// Store tokens and session info
localStorage.setItem('access_token', loginData.access_token);
localStorage.setItem('refresh_token', loginData.refresh_token);
localStorage.setItem('session_id', loginData.session_id);

// Check onboarding requirement
if (loginData.requires_onboarding) {
  router.push('/onboarding');
  return;
}

// User has completed onboarding, redirect to dashboard
router.push('/dashboard');
```

---

### 4. Organization Setup Phase

**Onboarding Status Check**

**Endpoint:** `GET /api/cms/v1/onboarding/my/organization-status`

**Headers Required:**
```
Authorization: Bearer eyJ...
Content-Type: application/json
```

**Response (200):**
```json
{
  "status": {
    "code": 200,
    "message": "Organization status retrieved successfully",
    "success": true,
    "timestamp": "2024-01-15T10:42:00Z",
    "path": "/api/cms/v1/onboarding/my/organization-status"
  },
  "data": {
    "has_organization": false,
    "requires_setup": true,
    "user_id": 1,
    "current_tenant_count": 0
  }
}
```

**Organization Creation**

**Endpoint:** `POST /api/cms/v1/onboarding/my/organization`

**Headers Required:**
```
Authorization: Bearer eyJ...
Content-Type: application/json
```

```json
{
  "name": "My Company",
  "domain": "mycompany",
  "contact_email": "<EMAIL>",
  "plan_id": 1,
  "company_name": "My Company Inc.",
  "company_address": "123 Business St, City, State",
  "contact_phone": "+1234567890",
  "company_tax_id": "TAX123456"
}
```

**Response (201):**
```json
{
  "status": {
    "code": 201,
    "message": "Organization created successfully",
    "success": true,
    "timestamp": "2024-01-15T10:45:00Z",
    "path": "/api/cms/v1/onboarding/my/organization"
  },
  "data": {
    "organization": {
      "id": 1,
      "name": "My Company",
      "slug": "my-company",
      "domain": "mycompany",
      "contact_email": "<EMAIL>",
      "company_name": "My Company Inc.",
      "company_address": "123 Business St, City, State",
      "contact_phone": "+1234567890",
      "company_tax_id": "TAX123456",
      "status": "active",
      "plan_id": 1,
      "created_at": "2024-01-15T10:45:00Z",
      "updated_at": "2024-01-15T10:45:00Z"
    },
    "membership": {
      "id": 1,
      "user_id": 1,
      "tenant_id": 1,
      "role": "owner",
      "is_primary": true,
      "status": "active",
      "joined_at": "2024-01-15T10:45:00Z"
    },
    "onboarding_started": true,
    "journey_id": 1
  }
}
```

**Frontend Actions:**
- Store tenant_id for future API calls: `localStorage.setItem('tenant_id', response.data.organization.id)`
- Update API service to include X-Tenant-ID header for subsequent requests
- Show organization creation success message
- Store journey_id for onboarding progress tracking
- Redirect to onboarding steps or dashboard

**Note:** The user is automatically assigned as the owner of the created organization with `is_primary: true` indicating this is their primary tenant. An onboarding journey is automatically started with the new organization.

---

### 5. Onboarding Steps Phase

**Get Current Step**

**Endpoint:** `GET /api/cms/v1/onboarding/my/current-step`

**Headers Required:**
```
Authorization: Bearer eyJ...
Content-Type: application/json
```

**Response (200):**
```json
{
  "status": {
    "code": 200,
    "message": "Current step retrieved successfully",
    "success": true,
    "timestamp": "2024-01-15T10:50:00Z",
    "path": "/api/cms/v1/onboarding/my/current-step"
  },
  "data": {
    "step": {
      "id": 1,
      "journey_id": 1,
      "title": "Complete Profile",
      "description": "Add your personal information and preferences",
      "step_type": "form",
      "order": 1,
      "is_required": true,
      "estimated_duration": 5,
      "status": "active"
    },
    "progress": {
      "current_step": 1,
      "total_steps": 4,
      "completion_percentage": 25,
      "started_at": "2024-01-15T10:45:00Z"
    }
  }
}
```

**Complete Current Step**

**Endpoint:** `POST /api/cms/v1/onboarding/my/complete-step`

**Headers Required:**
```
Authorization: Bearer eyJ...
Content-Type: application/json
```

```json
{
  "step_data": {
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+1234567890",
    "company_role": "CEO",
    "industry": "Technology"
  },
  "metadata": {
    "time_spent": 180,
    "interaction_count": 5
  }
}
```

**Response (200):**
```json
{
  "status": {
    "code": 200,
    "message": "Step completed successfully",
    "success": true,
    "timestamp": "2024-01-15T10:55:00Z",
    "path": "/api/cms/v1/onboarding/my/complete-step"
  },
  "data": {
    "step_completed": true,
    "next_step": {
      "id": 2,
      "title": "Setup Preferences",
      "description": "Configure your dashboard and notification preferences",
      "step_type": "preferences",
      "order": 2,
      "is_required": false
    },
    "progress": {
      "current_step": 2,
      "total_steps": 4,
      "completion_percentage": 50,
      "completed_steps": [1],
      "remaining_steps": [2, 3, 4]
    },
    "journey_completed": false
  }
}
```

**Get Onboarding Status**

**Endpoint:** `GET /api/cms/v1/onboarding/my/status`

**Headers Required:**
```
Authorization: Bearer eyJ...
Content-Type: application/json
```

**Response (200) - Completed:**
```json
{
  "status": {
    "code": 200,
    "message": "Onboarding status retrieved successfully", 
    "success": true,
    "timestamp": "2024-01-15T11:15:00Z",
    "path": "/api/cms/v1/onboarding/my/status"
  },
  "data": {
    "onboarding_completed": true,
    "journey": {
      "id": 1,
      "name": "Welcome Journey",
      "completed_at": "2024-01-15T11:10:00Z",
      "completion_percentage": 100
    },
    "organization": {
      "id": 1,
      "name": "My Company",
      "status": "active"
    },
    "next_action": "dashboard",
    "redirect_url": "/dashboard"
  }
}
```

**Frontend Actions for Onboarding Steps:**
- Track progress through `completion_percentage` 
- Display step information and forms based on `step_type`
- Store step completion data for analytics
- Handle next step navigation automatically
- Show progress indicators to user
- Redirect to dashboard when `onboarding_completed: true`

---

## Frontend Implementation Guide

### Required Headers for API Calls

After onboarding completion, all API calls must include:

```javascript
const headers = {
  'Authorization': `Bearer ${accessToken}`,
  'X-Tenant-ID': tenantId,
  'Content-Type': 'application/json'
};
```

### Token Refresh with Tenant Context

**Endpoint:** `POST /api/cms/v1/auth/refresh-with-tenant`

**Headers:**
```javascript
{
  'Authorization': `Bearer ${currentAccessToken}`,
  'Content-Type': 'application/json'
}
```

**Request:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200):**
```json
{
  "status": {
    "code": 200,
    "message": "Token refreshed successfully",
    "success": true,
    "timestamp": "2024-01-15T11:00:00Z",
    "path": "/api/cms/v1/auth/refresh-with-tenant"
  },
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  }
}
```

**Frontend Implementation:**
```javascript
async function refreshTokenWithTenant(refreshToken) {
  const response = await fetch('/api/cms/v1/auth/refresh-with-tenant', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${currentAccessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ refresh_token: refreshToken })
  });
  
  const result = await response.json();
  
  if (result.status.success) {
    localStorage.setItem('access_token', result.data.access_token);
    localStorage.setItem('refresh_token', result.data.refresh_token);
    return result.data.access_token;
  }
  
  throw new Error(result.status.message);
}
```

### Error Handling

**Common Error Responses:**

All errors follow the standard response format with a `status` object:

```json
// Authentication failed (401)
{
  "status": {
    "code": 401,
    "message": "Authentication failed",
    "success": false,
    "timestamp": "2024-01-15T10:30:00Z",
    "path": "/api/cms/v1/auth/login",
    "error_code": "INVALID_CREDENTIALS"
  },
  "data": null
}

// Email already exists (409) 
{
  "status": {
    "code": 409,
    "message": "Email already exists",
    "success": false,
    "timestamp": "2024-01-15T10:30:00Z",
    "path": "/api/cms/v1/auth/register",
    "error_code": "EMAIL_EXISTS"
  },
  "data": null
}

// Validation errors (400)
{
  "status": {
    "code": 400,
    "message": "Invalid request format or validation error",
    "success": false,
    "timestamp": "2024-01-15T10:30:00Z",
    "path": "/api/cms/v1/auth/register",
    "error_code": "VALIDATION_ERROR",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      },
      {
        "field": "password", 
        "message": "Password must be at least 8 characters"
      }
    ]
  },
  "data": null
}

// 2FA Required (200 - Special case)
{
  "status": {
    "code": 200,
    "message": "Login successful",
    "success": true,
    "timestamp": "2024-01-15T10:30:00Z",
    "path": "/api/cms/v1/auth/login"
  },
  "data": {
    "requires_two_factor": true,
    "requires_email_verification": false,
    "session_id": 12345
  }
}
```

### State Management

```javascript
// User state after successful flow
const userState = {
  isAuthenticated: true,
  auth: {
    access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    token_type: "Bearer",
    expires_in: 3600,
    session_id: 12345,
    expires_at: new Date(Date.now() + 3600 * 1000).toISOString()
  },
  user: {
    id: 1,
    email: "<EMAIL>",
    email_verified: true,
    requires_two_factor: false
  },
  tenant: {
    id: 1,
    name: "My Company",
    domain: "mycompany.com",
    contact_email: "<EMAIL>",
    company_name: "My Company Inc.",
    status: "active",
    plan_id: 1
  },
  onboarding: {
    completed: true,
    current_step: null,
    completed_at: "2024-01-15T10:45:00Z"
  }
};

// Recommended state management pattern
class AuthState {
  constructor() {
    this.state = {
      isAuthenticated: false,
      auth: null,
      user: null,
      tenant: null,
      onboarding: { completed: false }
    };
  }

  updateFromLoginResponse(response) {
    this.state.isAuthenticated = true;
    this.state.auth = {
      ...response.data,
      expires_at: new Date(Date.now() + response.data.expires_in * 1000).toISOString()
    };
  }

  updateFromTenantCreation(response) {
    this.state.tenant = response.data;
    this.state.onboarding.completed = true;
    this.state.onboarding.completed_at = new Date().toISOString();
  }

  getAuthHeaders() {
    if (!this.state.isAuthenticated || !this.state.auth) {
      throw new Error('User not authenticated');
    }

    const headers = {
      'Authorization': `Bearer ${this.state.auth.access_token}`,
      'Content-Type': 'application/json'
    };

    if (this.state.tenant?.id) {
      headers['X-Tenant-ID'] = this.state.tenant.id.toString();
    }

    return headers;
  }

  isTokenExpired() {
    if (!this.state.auth?.expires_at) return true;
    return new Date() >= new Date(this.state.auth.expires_at);
  }
}
```

### Example: Login Response for User Needing Onboarding

When a user has verified their email but hasn't created a tenant yet:

```json
{
  "status": {
    "code": 200,
    "message": "Login successful",
    "success": true,
    "timestamp": "2024-01-15T10:40:00Z",
    "path": "/api/cms/v1/auth/login"
  },
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "session_id": 12345,
    "requires_email_verification": false,
    "requires_two_factor": false,
    "requires_onboarding": true  // ← This indicates user needs to create their first tenant
  }
}
```

## Flow Variations

### New User Path
```
Register → Verify Email → Login → Organization Setup → Onboarding Steps → Dashboard
```

### Returning User Path  
```
Login → Dashboard (if onboarded)
Login → Organization Setup → Onboarding Steps (if not onboarded)
```

### Email Verification Retry
```
Login → Resend Verification → Verify Email → Organization Setup → Onboarding Steps
```

### Password Reset Flow
```
Forgot Password → Check Email → Reset Password → Login
```

**Note:** The forgot-password endpoint works without requiring login or tenant context. It will use the default tenant ID if no tenant context is available from the request domain.

### Two-Factor Authentication Flow
```
Login → 2FA Code Entry → Dashboard/Onboarding
```

**2FA Login Process:**

1. **Initial Login** - User enters email/password
2. **2FA Required Response** - API returns `requires_two_factor: true`
3. **2FA Code Entry** - User enters authentication code
4. **Complete Login** - API returns full authentication tokens

**Frontend 2FA Handling:**
```javascript
async function handleLogin(email, password, twoFactorCode = '') {
  const response = await fetch('/api/cms/v1/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email,
      password,
      two_factor_code: twoFactorCode,
      device_name: navigator.userAgent
    })
  });

  const result = await response.json();

  if (result.status.success) {
    if (result.data.requires_two_factor && !twoFactorCode) {
      // Show 2FA code input form
      setShow2FAForm(true);
      setSessionId(result.data.session_id);
      return { requires2FA: true };
    }
    
    // Login successful, store tokens
    storeAuthTokens(result.data);
    return { success: true, data: result.data };
  }

  throw new Error(result.status.message);
}
```

## Key Success Indicators

1. ✅ User registration successful with `email_verification_sent: true`
2. ✅ Email verification completed → `email_verified: true`  
3. ✅ Login successful with proper token management and session tracking
4. ✅ 2FA handling (if enabled) completed successfully
5. ✅ Organization created via `/api/cms/v1/onboarding/my/organization` with tenant membership
6. ✅ Onboarding journey automatically started with organization creation
7. ✅ User completes onboarding steps through `/api/cms/v1/onboarding/my/*` endpoints
8. ✅ Onboarding status shows `onboarding_completed: true`
9. ✅ User redirected to dashboard with proper tenant context (`X-Tenant-ID` header)
10. ✅ Token refresh mechanism working with tenant awareness

## Related API Documentation

- **Auth APIs:** `/api/cms/v1/auth/*` - User registration, login, verification
- **Onboarding APIs:** `/api/cms/v1/onboarding/*` - Complete onboarding flow
  - **End-User APIs:** `/api/cms/v1/onboarding/my/*` - User-facing onboarding endpoints
  - **Admin APIs:** `/api/cms/v1/onboarding/admin/*` - Administrative management
- **Tenant APIs:** `/api/cms/v1/tenants/*` - Tenant management after onboarding
- **Website APIs:** `/api/cms/v1/websites/*` - Website management

For detailed API specifications, refer to the Swagger documentation at `/api/docs`.

## API Summary for UI Implementation

### Essential Onboarding APIs for Frontend:

1. **Check Organization Status**: `GET /api/cms/v1/onboarding/my/organization-status`
2. **Create Organization**: `POST /api/cms/v1/onboarding/my/organization`
3. **Get Current Step**: `GET /api/cms/v1/onboarding/my/current-step`
4. **Complete Step**: `POST /api/cms/v1/onboarding/my/complete-step`
5. **Get Onboarding Status**: `GET /api/cms/v1/onboarding/my/status`

All endpoints require JWT authentication via `Authorization: Bearer` header. The organization creation endpoint automatically creates tenant membership and starts the onboarding journey. Step completion automatically advances to the next step until journey completion.