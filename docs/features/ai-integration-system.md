# AI Integration System

Hệ thống tích hợp AI to<PERSON><PERSON> diện cho phép x<PERSON> lý các requests AI, quản lý chat sessions, và theo dõi analytics với đ<PERSON>y đủ tính năng enterprise như rate limiting, cost tracking, và security.

## <PERSON><PERSON><PERSON> lục

- [1. Tổng quan](#1-tổng-quan)
- [2. AI Request Management](#2-ai-request-management)
- [3. Chat System](#3-chat-system)
- [4. Security & Rate Limiting](#4-security--rate-limiting)
- [5. Analytics & Monitoring](#5-analytics--monitoring)
- [6. Cost Management](#6-cost-management)
- [7. Queue System](#7-queue-system)

---

## 1. Tổng quan

### Kiến trúc AI System

```mermaid
graph TB
    subgraph "AI Core Services"
        AIRequest[AI Request Management]
        ChatSystem[Chat System]
        Analytics[Analytics Engine]
    end
    
    subgraph "Security Layer"
        RateLimit[Rate Limiter]
        PIIDetector[PII Detection]
        SecurityService[Security Service]
    end
    
    subgraph "Infrastructure"
        Queue[Queue System]
        CostCalc[Cost Calculator]
        Monitoring[Monitoring]
    end
    
    AIRequest --> RateLimit
    AIRequest --> PIIDetector
    AIRequest --> Queue
    ChatSystem --> SecurityService
    Analytics --> CostCalc
    Analytics --> Monitoring
```

### Core Features

- **AI Request Processing**: Batch và real-time AI request handling
- **Chat Management**: Multi-session chat với context management
- **Enterprise Security**: PII detection, content filtering, access control
- **Rate Limiting**: Sophisticated rate limiting per user/tenant
- **Cost Tracking**: Detailed cost analysis và billing
- **Queue Management**: Background processing với retry logic
- **Analytics**: Comprehensive usage và performance metrics

---

## 2. AI Request Management

### 2.1. API Endpoints

#### Base URL: `/api/v1/ai/requests`

#### Create Request - `POST /ai/requests`

**Request:**
```json
{
  "user_id": 123,
  "tenant_id": 456,
  "website_id": 789,
  "request_type": "completion",
  "model_id": 1,
  "prompt": "Write a blog post about AI technology",
  "parameters": {
    "max_tokens": 1000,
    "temperature": 0.7,
    "top_p": 0.9
  },
  "metadata": {
    "source": "blog_editor",
    "category": "content_generation"
  },
  "priority": "normal"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 12345,
    "status": "pending",
    "estimated_cost": 150,
    "estimated_tokens": 1000,
    "queue_position": 5,
    "created_at": "2024-07-01T10:30:00Z"
  }
}
```

#### Process Request - `POST /ai/requests/{id}/process`

Xử lý ngay lập tức một request cụ thể.

#### Batch Operations

- `POST /ai/requests/batch` - Tạo multiple requests
- `PUT /ai/requests/batch/status` - Bulk update status
- `POST /ai/requests/batch/process` - Bulk processing

### 2.2. Request Lifecycle

```mermaid
stateDiagram-v2
    [*] --> pending : Create Request
    pending --> processing : Start Processing
    pending --> queued : Add to Queue
    queued --> processing : Dequeue
    processing --> completed : Success
    processing --> failed : Error
    processing --> cancelled : User Cancel
    failed --> pending : Retry
    completed --> [*]
    failed --> [*]
    cancelled --> [*]
```

### 2.3. Advanced Features

#### Search & Filtering - `GET /ai/requests/search`

**Query Parameters:**
```
?status=completed&user_id=123&start_date=2024-07-01&model_id=1&request_type=completion
```

#### Retry Logic - `POST /ai/requests/{id}/retry`

- Automatic retry với exponential backoff
- Configurable retry limits
- Error categorization for retry decisions

---

## 3. Chat System

### 3.1. Architecture Overview

```mermaid
graph TB
    User[User] --> ChatSession[Chat Session]
    ChatSession --> Message[Messages]
    ChatSession --> Context[Context Window]
    Message --> TokenMgmt[Token Management]
    Context --> ContextWindow[Context Window Manager]
    TokenMgmt --> CostCalc[Cost Calculator]
```

### 3.2. Chat Configuration

```go
type AIChatConfig struct {
    MaxSessionsPerUser     int
    MaxMessagesPerSession  int
    MaxMessageLength       int
    MaxTokensPerMessage    int
    MaxTokensPerSession    int
    ContextWindowTokens    int
    SessionTimeoutHours    int
    EnableContentFilter    bool
    EnablePIIDetection     bool
    EnableRateLimiting     bool
    DefaultSystemPrompt    string
}
```

### 3.3. Planned Chat API Endpoints

#### Session Management (TODO)

- `POST /ai/chat/sessions` - Create new chat session
- `GET /ai/chat/sessions/{id}` - Get session details
- `PUT /ai/chat/sessions/{id}` - Update session
- `DELETE /ai/chat/sessions/{id}` - Delete session
- `POST /ai/chat/sessions/{id}/archive` - Archive session

#### Message Management (TODO)

- `POST /ai/chat/messages` - Send message
- `GET /ai/chat/messages/{id}` - Get message
- `GET /ai/chat/sessions/{id}/messages` - Get session messages
- `GET /ai/chat/sessions/{id}/context-window` - Get context window

#### Analytics (TODO)

- `GET /ai/chat/analytics/sessions/stats` - Session statistics
- `GET /ai/chat/analytics/tokens/usage` - Token usage stats

---

## 4. Security & Rate Limiting

### 4.1. Rate Limiting System

#### Check Rate Limit - `GET /ai/rate-limits`

**Response:**
```json
{
  "success": true,
  "data": {
    "tenant_id": 456,
    "user_id": 123,
    "requests_remaining": 95,
    "tokens_remaining": 45000,
    "reset_time": "2024-07-01T11:00:00Z",
    "current_tier": "premium",
    "limits": {
      "requests_per_hour": 100,
      "tokens_per_hour": 50000
    }
  }
}
```

#### Rate Limiting Logic

```mermaid
sequenceDiagram
    participant User
    participant API
    participant RateLimiter
    participant AIService

    User->>+API: AI Request
    API->>+RateLimiter: Check Limits
    alt Limit OK
        RateLimiter-->>-API: Allowed
        API->>+AIService: Process Request
        AIService-->>-API: Response
        API-->>-User: AI Response
    else Limit Exceeded
        RateLimiter-->>-API: Rate Limited
        API-->>-User: 429 Too Many Requests
    end
```

### 4.2. PII Detection & Content Filtering

**PII Detection Pipeline:**
1. Scan input text for personal information
2. Redact sensitive data
3. Log detection events
4. Return sanitized content

**Supported PII Types:**
- Email addresses
- Phone numbers
- Social Security Numbers
- Credit card numbers
- IP addresses
- Names và addresses

### 4.3. Security Validation

- **Access Control**: User permission validation
- **Prompt Sanitization**: Malicious content filtering
- **Input Validation**: Schema và content validation
- **Audit Logging**: Complete request audit trail

---

## 5. Analytics & Monitoring

### 5.1. Analytics Dashboard - `GET /ai/analytics`

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "total_requests": 15000,
      "completed_requests": 14500,
      "failed_requests": 300,
      "success_rate": 96.7,
      "average_response_time": 2.3
    },
    "usage_by_model": [
      {
        "model_id": 1,
        "model_name": "GPT-4",
        "requests": 8000,
        "tokens_used": 2500000,
        "cost": 5000.00
      }
    ],
    "usage_by_tenant": [
      {
        "tenant_id": 456,
        "requests": 5000,
        "cost": 1500.00
      }
    ]
  }
}
```

### 5.2. Usage Statistics - `GET /ai/usage-stats`

**Query Parameters:**
- `period`: day, week, month, quarter
- `group_by`: user, tenant, model, request_type
- `start_date`, `end_date`: Custom date range

### 5.3. Performance Metrics - `GET /ai/performance-metrics`

**Key Metrics:**
- Response time percentiles
- Error rates by type
- Queue wait times
- Token processing rates
- Cost efficiency metrics

### 5.4. System Health - `GET /ai/health`

**Health Checks:**
- Database connectivity
- Service availability
- Queue status
- Rate limiter status
- External AI provider status

---

## 6. Cost Management

### 6.1. Cost Breakdown - `GET /ai/cost-breakdown`

**Response:**
```json
{
  "success": true,
  "data": {
    "total_cost": 2500.00,
    "period": "month",
    "breakdown": {
      "by_model": [
        {
          "model_id": 1,
          "model_name": "GPT-4",
          "cost": 2000.00,
          "tokens": 1000000,
          "requests": 5000
        }
      ],
      "by_tenant": [
        {
          "tenant_id": 456,
          "cost": 1500.00,
          "percentage": 60.0
        }
      ],
      "by_request_type": [
        {
          "type": "completion",
          "cost": 1800.00,
          "percentage": 72.0
        }
      ]
    }
  }
}
```

### 6.2. Cost Calculation Logic

```go
type CostCalculator interface {
    CalculateCost(ctx context.Context, modelID uint, tokensUsed int) (int, error)
}

// Example: $0.001 per token for GPT-4
func (c *costCalculator) CalculateCost(modelID uint, tokens int) int {
    rate := c.getModelRate(modelID) // e.g., 0.001
    return int(float64(tokens) * rate * 100) // Return cost in cents
}
```

### 6.3. Budget Management

- **Budget Alerts**: Notifications at thresholds
- **Auto-suspension**: Stop requests when budget exceeded
- **Cost Forecasting**: Predict monthly costs
- **Billing Integration**: Export for billing systems

---

## 7. Queue System

### 7.1. Queue Operations

#### Add to Queue - `POST /ai/queue`

**Request:**
```json
{
  "request_id": 12345,
  "priority": "high",
  "delay": 0,
  "retry_count": 3
}
```

#### Queue Status - `GET /ai/queue/status`

**Response:**
```json
{
  "success": true,
  "data": {
    "tenant_id": 456,
    "pending_count": 25,
    "processing_count": 5,
    "completed_count": 1000,
    "failed_count": 10,
    "average_wait_time": 30,
    "estimated_completion": "2024-07-01T11:15:00Z"
  }
}
```

### 7.2. Queue Management Logic

```mermaid
flowchart TD
    A[New Request] --> B{Check Priority}
    B -->|High| C[Priority Queue]
    B -->|Normal| D[Standard Queue]
    B -->|Low| E[Background Queue]
    
    C --> F[Worker Pool]
    D --> F
    E --> F
    
    F --> G{Processing}
    G -->|Success| H[Complete]
    G -->|Failure| I{Retry?}
    I -->|Yes| J[Retry Queue]
    I -->|No| K[Failed]
    J --> F
```

### 7.3. Background Processing

- **Worker Pool**: Configurable worker threads
- **Retry Logic**: Exponential backoff
- **Dead Letter Queue**: Failed requests storage
- **Monitoring**: Queue health monitoring

---

## Database Schema

### Core Tables

```sql
-- AI Requests
CREATE TABLE ai_requests (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED,
    user_id INT UNSIGNED NOT NULL,
    request_type ENUM('completion', 'chat', 'embedding', 'image') NOT NULL,
    model_id INT UNSIGNED NOT NULL,
    prompt TEXT NOT NULL,
    parameters JSON,
    response TEXT,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled'),
    tokens_used INT DEFAULT 0,
    cost_cents INT DEFAULT 0,
    processing_time_ms INT,
    error_message TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    INDEX idx_tenant_user (tenant_id, user_id),
    INDEX idx_status_created (status, created_at)
);

-- Chat Sessions
CREATE TABLE ai_chat_sessions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    title VARCHAR(255),
    system_prompt TEXT,
    context_data JSON,
    total_tokens INT DEFAULT 0,
    message_count INT DEFAULT 0,
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active',
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Chat Messages
CREATE TABLE ai_chat_messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    session_id BIGINT UNSIGNED NOT NULL,
    role ENUM('user', 'assistant', 'system') NOT NULL,
    content TEXT NOT NULL,
    tokens INT DEFAULT 0,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES ai_chat_sessions(id) ON DELETE CASCADE
);
```

---

## Integration & Deployment

### External Integrations

- **OpenAI API**: Primary AI provider
- **Anthropic Claude**: Alternative AI provider
- **Azure OpenAI**: Enterprise AI services
- **Google AI**: Additional AI capabilities

### Configuration

```yaml
ai_config:
  rate_limiting:
    enabled: true
    requests_per_hour: 1000
    tokens_per_hour: 100000
  
  security:
    pii_detection: true
    content_filtering: true
    prompt_sanitization: true
  
  queue:
    max_workers: 10
    retry_attempts: 3
    timeout_seconds: 300
  
  cost_management:
    budget_alerts: true
    auto_suspend: false
    cost_per_token: 0.001
```

### Monitoring & Alerts

- **Prometheus Metrics**: System performance metrics
- **Grafana Dashboards**: Visual monitoring
- **Alert Manager**: Automated alerting
- **Error Tracking**: Comprehensive error logging

---

## Future Enhancements

### Planned Features

1. **Multi-Model Support**: Support for multiple AI providers
2. **Advanced Analytics**: ML-powered usage insights
3. **Custom Models**: Fine-tuned model integration
4. **Real-time Chat**: WebSocket-based chat interface
5. **Advanced Security**: Enhanced threat detection
6. **Cost Optimization**: AI-powered cost optimization
7. **Plugin System**: Extensible AI capabilities
8. **Mobile SDK**: Native mobile integration