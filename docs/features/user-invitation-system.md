# User Invitation System - Multi-Tenant

<PERSON><PERSON> thống mời người dùng cho phép admin của tenant mời người dùng mới hoặc hiện có tham gia vào tenant của họ với các vai trò cụ thể.

## <PERSON><PERSON><PERSON> l<PERSON>

- [1. Tổng quan](#1-tổng-quan)
- [2. <PERSON><PERSON><PERSON> tr<PERSON><PERSON> hệ thống](#2-kiến-trúc-hệ-thống)
- [3. API Endpoints](#3-api-endpoints)
- [4. Luồng hoạt động](#4-luồng-hoạt-động)
- [5. <PERSON> Schema](#5-database-schema)
- [6. Security & Validation](#6-security--validation)
- [7. Integration với hệ thống khác](#7-integration-với-hệ-thống-khác)

---

## 1. Tổng quan

### Tính năng chính

- **Admin Functions**: <PERSON><PERSON><PERSON>, <PERSON><PERSON> hồ<PERSON>, g<PERSON><PERSON> lại lời mời
- **User Functions**: <PERSON><PERSON><PERSON>, từ chối lời mời
- **Multi-tenant Support**: L<PERSON>i mời scope theo tenant
- **Role Assignment**: Gán role cụ thể khi mời
- **Token Security**: Unique tokens với expiration
- **Status Tracking**: Theo dõi trạng thái lời mời

### Use Cases

1. **Onboarding Team Members**: Admin mời nhân viên mới
2. **Role-based Access**: Mời với vai trò cụ thể (editor, viewer, etc.)
3. **Bulk Invitations**: Mời nhiều người cùng lúc
4. **Guest Access**: Mời external users với quyền hạn chế
5. **Re-invitations**: Gửi lại lời mời đã hết hạn

---

## 2. Kiến trúc hệ thống

### Components

```mermaid
graph TB
    Admin[Admin User] --> Handler[InvitationHandler]
    Guest[Guest User] --> Handler
    Handler --> Service[InvitationService]
    Service --> Repo[InvitationRepository]
    Service --> TenantRepo[TenantMembershipRepository]
    Service --> NotificationService[NotificationService]
    Repo --> DB[(MySQL Database)]
    TenantRepo --> DB
    NotificationService --> Email[Email Service]
```

### Module Structure

```
internal/modules/user/
├── handlers/
│   └── user_invitation_handler.go       # HTTP handlers
├── services/
│   └── user_invitation_service.go       # Business logic
├── repositories/
│   ├── user_invitation_repository.go    # Interface
│   └── mysql/
│       └── user_invitation_repository.go # MySQL implementation
├── models/
│   └── user_invitation.go               # Domain models
├── dto/
│   └── user_invitation_dto.go           # Request/Response DTOs
└── routes.go                            # Route definitions
```

---

## 3. API Endpoints

### Base URL: `/api/cms/v1/user-invitations`

### 3.1. Admin Endpoints

#### Tạo lời mời - `POST /user-invitations`

**Request:**
```json
{
  "email": "<EMAIL>",
  "role_id": 3,
  "message": "Welcome to our team!",
  "expires_at": "2024-02-01T00:00:00Z"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 123,
    "token": "abc123def456",
    "email": "<EMAIL>",
    "role_id": 3,
    "status": "pending",
    "expires_at": "2024-02-01T00:00:00Z",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

#### Thu hồi lời mời - `POST /user-invitations/{id}/revoke`

**Request:**
```json
{
  "reason": "Position no longer available"
}
```

#### Gửi lại lời mời - `POST /user-invitations/{id}/resend`

Tạo token mới và gửi lại email.

#### Danh sách lời mời - `GET /user-invitations`

**Query Parameters:**
- `status`: pending, accepted, rejected, expired, revoked
- `email`: Filter by email
- `role_id`: Filter by role
- `page`, `limit`: Pagination

### 3.2. User Endpoints

#### Chấp nhận lời mời - `POST /user-invitations/accept`

**Request:**
```json
{
  "token": "abc123def456",
  "user_id": 456
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "invitation_id": 123,
    "tenant_membership": {
      "tenant_id": 789,
      "user_id": 456,
      "role_id": 3,
      "status": "active"
    }
  }
}
```

#### Từ chối lời mời - `POST /user-invitations/reject`

**Request:**
```json
{
  "token": "abc123def456",
  "reason": "Not interested at this time"
}
```

#### Xem thông tin lời mời - `GET /user-invitations/token/{token}`

Public endpoint để xem chi tiết lời mời trước khi quyết định.

---

## 4. Luồng hoạt động

### 4.1. Admin tạo lời mời

```mermaid
sequenceDiagram
    participant Admin
    participant API
    participant InvitationService
    participant DB
    participant NotificationService
    participant Email

    Admin->>+API: POST /user-invitations
    API->>+InvitationService: CreateInvitation(req)
    InvitationService->>InvitationService: ValidateRequest & Generate Token
    InvitationService->>+DB: Save invitation
    DB-->>-InvitationService: Invitation saved
    InvitationService->>+NotificationService: SendInvitationEmail(invitation)
    NotificationService->>+Email: Send email with token
    Email-->>-NotificationService: Email sent
    NotificationService-->>-InvitationService: Notification sent
    InvitationService-->>-API: Invitation created
    API-->>-Admin: 201 Created
```

### 4.2. User chấp nhận lời mời

```mermaid
sequenceDiagram
    participant User
    participant API
    participant InvitationService
    participant TenantMembershipService
    participant DB

    User->>+API: POST /user-invitations/accept
    API->>+InvitationService: AcceptInvitation(token, userID)
    InvitationService->>+DB: FindByToken(token)
    DB-->>-InvitationService: Invitation data
    InvitationService->>InvitationService: ValidateToken & Expiration
    InvitationService->>+TenantMembershipService: CreateMembership(invitation)
    TenantMembershipService->>+DB: Create tenant_membership
    DB-->>-TenantMembershipService: Membership created
    TenantMembershipService-->>-InvitationService: Membership created
    InvitationService->>+DB: Update invitation status to 'accepted'
    DB-->>-InvitationService: Status updated
    InvitationService-->>-API: Invitation accepted
    API-->>-User: 200 OK
```

### 4.3. Registration với invitation token

```mermaid
sequenceDiagram
    participant NewUser
    participant AuthAPI
    participant UserAPI
    participant AuthService
    participant InvitationService

    NewUser->>+AuthAPI: POST /auth/register?invitation_token=abc123
    AuthAPI->>+AuthService: Register(req with invitation_token)
    AuthService->>AuthService: Create user account
    AuthService->>+UserAPI: AcceptInvitation(token, newUserID)
    UserAPI->>+InvitationService: AcceptInvitation(token, userID)
    InvitationService->>InvitationService: Auto-accept invitation
    InvitationService-->>-UserAPI: Membership created
    UserAPI-->>-AuthAPI: Invitation processed
    AuthService-->>-AuthAPI: Registration complete
    AuthAPI-->>-NewUser: User + tenant membership created
```

---

## 5. Database Schema

### 5.1. Bảng user_invitations

```sql
CREATE TABLE IF NOT EXISTS user_invitations (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED,
    token VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    invited_by_user_id INT UNSIGNED NOT NULL,
    role_id INT UNSIGNED NOT NULL,
    message TEXT,
    status ENUM('pending', 'accepted', 'rejected', 'expired', 'revoked') DEFAULT 'pending',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    accepted_at TIMESTAMP NULL,
    rejected_at TIMESTAMP NULL,
    revoked_at TIMESTAMP NULL,
    
    -- Foreign Keys
    CONSTRAINT fk_user_invitations_tenant_id 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_invitations_website_id 
        FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_invitations_invited_by_user_id 
        FOREIGN KEY (invited_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_invitations_role_id 
        FOREIGN KEY (role_id) REFERENCES rbac_roles(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_user_invitations_tenant_id (tenant_id),
    INDEX idx_user_invitations_email (email),
    INDEX idx_user_invitations_status (status),
    INDEX idx_user_invitations_expires_at (expires_at),
    UNIQUE KEY uk_user_invitations_tenant_email_pending (tenant_id, email, status)
);
```

### 5.2. Status Flow

```mermaid
stateDiagram-v2
    [*] --> pending : Created
    pending --> accepted : User accepts
    pending --> rejected : User rejects
    pending --> expired : Time expires
    pending --> revoked : Admin revokes
    accepted --> [*]
    rejected --> [*]
    expired --> [*]
    revoked --> [*]
```

---

## 6. Security & Validation

### 6.1. Token Security

- **Unique Tokens**: Mỗi invitation có token duy nhất
- **Expiration**: Tokens có thời hạn (default 7 ngày)
- **Single Use**: Token invalid sau khi accepted/rejected
- **Secure Generation**: Crypto-random token generation

### 6.2. Access Control

- **Tenant Scoped**: Chỉ admin có thể mời vào tenant của họ
- **Role Validation**: Chỉ có thể gán roles trong phạm vi tenant
- **Email Uniqueness**: Không thể mời cùng email multiple times trong cùng tenant

### 6.3. Validation Rules

**CreateInvitationRequest:**
```go
type CreateInvitationRequest struct {
    Email     string     `json:"email" validate:"required,email"`
    RoleID    uint       `json:"role_id" validate:"required"`
    Message   *string    `json:"message" validate:"omitempty,max=500"`
    ExpiresAt *time.Time `json:"expires_at" validate:"omitempty"`
}
```

---

## 7. Integration với hệ thống khác

### 7.1. Tenant Membership System

Khi invitation được accepted:
1. Tạo record trong `tenant_memberships`
2. Gán role từ invitation
3. Set status = 'active'
4. Update invitation status = 'accepted'

### 7.2. RBAC Integration

- Invitation roles phải tồn tại trong tenant
- Role assignment automatic khi accept
- Permission inheritance theo RBAC rules

### 7.3. Notification System

**Email Templates:**
- **Invitation Email**: Template ID 9
- **Reminder Email**: Template ID 10 (for resend)

**Email Data:**
```json
{
  "invitation_token": "abc123",
  "tenant_name": "Company XYZ",
  "invited_by": "John Doe",
  "role_name": "Editor",
  "message": "Custom message",
  "accept_url": "https://app.com/invite/accept?token=abc123",
  "expires_at": "2024-02-01T00:00:00Z"
}
```

### 7.4. Authentication Integration

- **Registration Flow**: Support invitation token parameter
- **Auto-membership**: Automatic tenant membership on registration
- **JWT Claims**: Include new tenant in user claims

---

## Testing

### Bruno Test Collection

```
api-tests/bruno/UserInvitation/
├── Create Invitation.bru
├── Accept Invitation.bru
├── Reject Invitation.bru
├── Revoke Invitation.bru
├── Resend Invitation.bru
├── List Invitations.bru
├── Get Invitation.bru
└── Get Invitation by Token.bru
```

### Test Scenarios

1. **Happy Path**: Admin creates → User accepts → Membership created
2. **Rejection Flow**: Admin creates → User rejects → No membership
3. **Expiration**: Token expires → Cannot accept
4. **Revocation**: Admin revokes → Token becomes invalid
5. **Bulk Operations**: Multiple invitations management
6. **Edge Cases**: Invalid tokens, duplicate emails, unauthorized access

---

## Monitoring & Analytics

### Key Metrics

- **Invitation Rate**: Invitations created per tenant
- **Acceptance Rate**: % of invitations accepted
- **Time to Accept**: Average time from send to accept
- **Bounce Rate**: % of failed email deliveries
- **Role Distribution**: Most invited roles

### Alerts

- High rejection rates
- Expired invitations cleanup
- Failed email deliveries
- Suspicious invitation patterns

---

## Future Enhancements

### Planned Features

1. **Bulk Invitations**: CSV upload for mass invites
2. **Custom Templates**: Tenant-specific email templates
3. **Invitation Analytics**: Detailed reporting dashboard
4. **Integration APIs**: Webhooks for invitation events
5. **Mobile Deep Links**: Direct app opening from emails
6. **Guest Access**: Limited access without full registration