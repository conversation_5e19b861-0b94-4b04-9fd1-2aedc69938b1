# Advertisement Management System

Hệ thống quản lý quảng cáo toàn diện cho phép tạo, quản lý và theo dõi các chiến dịch quảng cáo với targeting rules và analytics chi tiết.

## <PERSON><PERSON><PERSON> lục

- [1. Tổng quan](#1-tổng-quan)
- [2. Core Features](#2-core-features)
- [3. API Endpoints](#3-api-endpoints)
- [4. Campaign Management](#4-campaign-management)
- [5. Advertisement System](#5-advertisement-system)
- [6. Placement & Targeting](#6-placement--targeting)
- [7. Analytics & Tracking](#7-analytics--tracking)
- [8. Scheduling System](#8-scheduling-system)

---

## 1. Tổng quan

### Kiến trúc module

```mermaid
graph TB
    Campaign[Campaign Management] --> Advertisement[Advertisement Management]
    Advertisement --> Placement[Placement Management]
    Advertisement --> Targeting[Targeting Rules]
    Advertisement --> Schedule[Schedule Management]
    Advertisement --> Analytics[Analytics & Tracking]
    
    Analytics --> Dashboard[Analytics Dashboard]
    Analytics --> Export[Export Reports]
    
    subgraph Public APIs
        Serve[Ad Serving]
        Track[Impression/Click Tracking]
    end
```

### Key Components

- **Campaign Management**: Tổ chức ads theo chiến dịch
- **Advertisement Management**: Quản lý content và metadata của ads
- **Placement Management**: Xác định vị trí hiển thị ads
- **Targeting Rules**: Logic targeting audience
- **Schedule Management**: Lập lịch hiển thị ads
- **Analytics System**: Theo dõi performance và ROI

---

## 2. Core Features

### 2.1. Campaign Features
- ✅ **Campaign CRUD**: Tạo, sửa, xóa campaigns
- ✅ **Status Management**: Active, Paused, Completed
- ✅ **Budget Control**: Ngân sách và spending limits
- ✅ **Campaign Analytics**: Performance tracking per campaign

### 2.2. Advertisement Features
- ✅ **Ad Content Management**: Text, image, video ads
- ✅ **Ad Formats**: Banner, popup, inline, sidebar
- ✅ **Status Control**: Active/Inactive state management
- ✅ **Targeting Integration**: Rule-based audience targeting

### 2.3. Placement System
- ✅ **Page Type Targeting**: Home, article, category pages
- ✅ **Position Control**: Header, footer, sidebar, content
- ✅ **Optimal Placement**: Algorithm-based position suggestions
- ✅ **Placement Validation**: Conflict detection

### 2.4. Analytics & Tracking
- ✅ **Real-time Tracking**: Impressions và clicks
- ✅ **Performance Metrics**: CTR, conversion rates
- ✅ **Dashboard Analytics**: Visual reporting
- ✅ **Export Functionality**: CSV/Excel reports

---

## 3. API Endpoints

### Base URL: `/api/cms/v1/ads`

### 3.1. Campaign Management

#### Create Campaign - `POST /ads/campaigns`

**Request:**
```json
{
  "name": "Summer Sale 2024",
  "description": "Promotional campaign for summer products",
  "start_date": "2024-07-01T00:00:00Z",
  "end_date": "2024-08-31T23:59:59Z",
  "budget": 5000.00,
  "status": "active",
  "metadata": {
    "target_audience": "young_adults",
    "campaign_type": "promotional"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 123,
    "name": "Summer Sale 2024",
    "status": "active",
    "budget": 5000.00,
    "spent": 0.00,
    "impressions": 0,
    "clicks": 0,
    "created_at": "2024-07-01T10:30:00Z"
  }
}
```

#### Campaign Status Control
- `POST /ads/campaigns/{id}/activate` - Kích hoạt campaign
- `POST /ads/campaigns/{id}/pause` - Tạm dừng campaign
- `GET /ads/campaigns/{id}/status` - Kiểm tra trạng thái

#### Campaign Analytics - `GET /ads/campaigns/{id}/analytics`

**Query Parameters:**
- `start_date`, `end_date`: Time range
- `metrics`: impressions,clicks,ctr,cost
- `group_by`: day,hour,placement

### 3.2. Advertisement Management

#### Create Advertisement - `POST /ads/advertisements`

**Request:**
```json
{
  "campaign_id": 123,
  "title": "Summer Sale - Up to 50% Off",
  "content": "Don't miss our biggest sale of the year!",
  "ad_type": "banner",
  "format": "image",
  "image_url": "https://cdn.example.com/ads/summer-sale.jpg",
  "click_url": "https://shop.example.com/summer-sale",
  "placement_config": {
    "positions": ["header", "sidebar"],
    "pages": ["home", "article"]
  },
  "status": "active"
}
```

#### Advertisement Operations
- `GET /ads/advertisements` - List với filtering
- `PUT /ads/advertisements/{id}` - Update content
- `POST /ads/advertisements/{id}/activate` - Kích hoạt
- `POST /ads/advertisements/{id}/pause` - Tạm dừng

#### Ad Serving - `GET /ads/serve` (Public API)

**Query Parameters:**
```
?page_type=article&position=sidebar&user_id=456&article_id=789
```

**Response:**
```json
{
  "ad": {
    "id": 123,
    "title": "Summer Sale",
    "content": "Up to 50% Off",
    "image_url": "https://cdn.example.com/ads/summer-sale.jpg",
    "click_url": "https://shop.example.com/summer-sale",
    "tracking_id": "ad_123_user_456_20240701"
  }
}
```

### 3.3. Placement Management

#### Create Placement - `POST /ads/placements`

**Request:**
```json
{
  "name": "Homepage Header Banner",
  "page_type": "home",
  "position": "header",
  "dimensions": {
    "width": 728,
    "height": 90
  },
  "priority": 10,
  "status": "active",
  "constraints": {
    "max_ads": 1,
    "rotation_interval": 30
  }
}
```

#### Placement Operations
- `GET /ads/placements/page/{page_type}` - Placements by page type
- `GET /ads/placements/optimal` - Algorithm-suggested placements
- `POST /ads/placements/validate` - Validate placement config

### 3.4. Targeting Rules

#### Create Targeting Rule - `POST /ads/targeting-rules`

**Request:**
```json
{
  "name": "Young Adults Mobile Users",
  "conditions": [
    {
      "field": "age",
      "operator": "between",
      "value": [18, 35]
    },
    {
      "field": "device_type",
      "operator": "equals",
      "value": "mobile"
    },
    {
      "field": "location",
      "operator": "in",
      "value": ["US", "CA", "UK"]
    }
  ],
  "logic": "AND",
  "priority": 5
}
```

#### Targeting Operations
- `POST /ads/targeting-rules/evaluate` - Test rule against user data
- `POST /ads/targeting-rules/validate` - Validate rule syntax
- `GET /ads/advertisements/{id}/targeting-rules` - Rules for specific ad

### 3.5. Schedule Management

#### Create Schedule - `POST /ads/schedules`

**Request:**
```json
{
  "advertisement_id": 123,
  "schedule_type": "recurring",
  "start_time": "09:00:00",
  "end_time": "17:00:00",
  "days_of_week": [1, 2, 3, 4, 5],
  "timezone": "America/New_York",
  "frequency": "daily",
  "status": "active"
}
```

#### Schedule Operations
- `GET /ads/schedules/active` - Currently active schedules
- `GET /ads/schedules/{id}/status` - Check schedule status

---

## 4. Campaign Management

### 4.1. Campaign Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Draft : Create Campaign
    Draft --> Active : Activate
    Draft --> Archived : Archive
    Active --> Paused : Pause
    Paused --> Active : Resume
    Active --> Completed : End Date Reached
    Active --> Archived : Manual Archive
    Completed --> [*]
    Archived --> [*]
```

### 4.2. Budget Management

- **Budget Tracking**: Real-time spent monitoring
- **Budget Alerts**: Notifications at 75%, 90%, 100%
- **Auto-pause**: Optional auto-pause when budget exceeded
- **Cost Models**: CPC, CPM, flat rate

### 4.3. Campaign Analytics

**Key Metrics:**
- Impressions, Clicks, CTR
- Cost per Click (CPC)
- Cost per Impression (CPM)
- Conversion tracking
- ROI calculations

---

## 5. Advertisement System

### 5.1. Ad Types & Formats

**Supported Ad Types:**
- **Banner Ads**: Standard display banners
- **Text Ads**: Pure text-based ads
- **Image Ads**: Static image advertisements
- **Rich Media**: Interactive HTML5 ads

**Ad Formats:**
- Leaderboard (728x90)
- Rectangle (300x250)
- Skyscraper (160x600)
- Square (250x250)
- Custom dimensions

### 5.2. Content Management

- **Rich Text Editor**: WYSIWYG ad content creation
- **Media Integration**: Image/video upload and management
- **Template System**: Pre-built ad templates
- **A/B Testing**: Multiple ad versions per campaign

### 5.3. Ad Serving Logic

```mermaid
sequenceDiagram
    participant User
    participant Website
    participant AdSystem
    participant Analytics

    User->>+Website: Visit page
    Website->>+AdSystem: Request ad (page_type, position, user_data)
    AdSystem->>AdSystem: Apply targeting rules
    AdSystem->>AdSystem: Check schedule constraints
    AdSystem->>AdSystem: Select optimal ad
    AdSystem-->>-Website: Return ad content
    Website-->>-User: Display ad
    User->>+Analytics: View impression (tracking pixel)
    Analytics-->>-AdSystem: Record impression
    
    alt User clicks ad
        User->>+AdSystem: Click ad
        AdSystem->>+Analytics: Record click
        Analytics-->>-AdSystem: Click recorded
        AdSystem-->>-User: Redirect to target URL
    end
```

---

## 6. Placement & Targeting

### 6.1. Placement Strategy

**Page Types:**
- Homepage
- Article/Blog pages
- Category pages
- Search results
- User profile pages

**Positions:**
- Header banner
- Sidebar (left/right)
- In-content
- Footer
- Popup/overlay

### 6.2. Targeting System

**Targeting Criteria:**
- **Demographics**: Age, gender, location
- **Behavioral**: Page views, time on site, purchase history
- **Technical**: Device type, browser, OS
- **Contextual**: Page content, keywords, categories
- **Temporal**: Time of day, day of week, season

**Targeting Logic:**
```javascript
// Example targeting rule evaluation
{
  "conditions": [
    {"field": "age", "operator": ">=", "value": 18},
    {"field": "location", "operator": "in", "value": ["US", "CA"]},
    {"field": "device", "operator": "equals", "value": "mobile"}
  ],
  "logic": "AND", // All conditions must match
  "weight": 1.0   // Targeting weight for optimization
}
```

---

## 7. Analytics & Tracking

### 7.1. Tracking Implementation

#### Impression Tracking - `POST /ads/track/impression`

**Request:**
```json
{
  "ad_id": 123,
  "user_id": 456,
  "session_id": "sess_789",
  "placement_id": 10,
  "page_url": "https://example.com/article/123",
  "user_agent": "Mozilla/5.0...",
  "timestamp": "2024-07-01T15:30:00Z"
}
```

#### Click Tracking - `POST /ads/track/click`

**Request:**
```json
{
  "ad_id": 123,
  "user_id": 456,
  "impression_id": "imp_12345",
  "click_position": {"x": 150, "y": 200},
  "timestamp": "2024-07-01T15:31:00Z"
}
```

### 7.2. Analytics Dashboard - `GET /ads/analytics/dashboard`

**Response:**
```json
{
  "status": "success",
  "data": {
    "overview": {
      "total_impressions": 1250000,
      "total_clicks": 25000,
      "average_ctr": 2.0,
      "total_spend": 4500.00
    },
    "top_campaigns": [
      {
        "id": 123,
        "name": "Summer Sale 2024",
        "impressions": 500000,
        "clicks": 12500,
        "ctr": 2.5,
        "spend": 2000.00
      }
    ],
    "performance_chart": {
      "period": "7d",
      "data": [
        {"date": "2024-07-01", "impressions": 45000, "clicks": 900},
        {"date": "2024-07-02", "impressions": 52000, "clicks": 1040}
      ]
    }
  }
}
```

### 7.3. Export Analytics - `GET /ads/analytics/export`

**Query Parameters:**
- `format`: csv, excel, json
- `start_date`, `end_date`: Date range
- `campaign_id`: Specific campaign
- `metrics`: Metrics to include

**Features:**
- Scheduled exports
- Custom report templates
- Email delivery
- API webhook delivery

---

## 8. Scheduling System

### 8.1. Schedule Types

**Time-based Scheduling:**
- Daily schedules with start/end times
- Weekly schedules with specific days
- Date range scheduling
- Timezone support

**Frequency Options:**
- One-time display
- Daily recurring
- Weekly recurring
- Custom intervals

### 8.2. Schedule Logic

```mermaid
flowchart TD
    A[Ad Request] --> B{Check Schedule}
    B -->|Active Schedule| C{Check Time Constraints}
    B -->|No Schedule| D[Serve Ad]
    C -->|Within Time| D
    C -->|Outside Time| E[No Ad]
    D --> F{Check Frequency}
    F -->|Within Limit| G[Display Ad]
    F -->|Limit Exceeded| E
```

### 8.3. Advanced Scheduling Features

- **Dayparting**: Different ads for different times of day
- **Seasonal Campaigns**: Holiday/event-based scheduling
- **Frequency Capping**: Limit ad impressions per user
- **Competitive Exclusion**: Prevent competitor ads on same page

---

## Database Schema

### Core Tables

```sql
-- Campaigns
CREATE TABLE ad_campaigns (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    budget DECIMAL(10,2),
    spent DECIMAL(10,2) DEFAULT 0.00,
    start_date DATE,
    end_date DATE,
    status ENUM('draft', 'active', 'paused', 'completed', 'archived'),
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Advertisements
CREATE TABLE advertisements (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    campaign_id INT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    ad_type ENUM('banner', 'text', 'image', 'rich_media'),
    format VARCHAR(50),
    image_url VARCHAR(500),
    click_url VARCHAR(500),
    status ENUM('active', 'inactive', 'pending'),
    placement_config JSON,
    FOREIGN KEY (campaign_id) REFERENCES ad_campaigns(id)
);

-- Analytics Events
CREATE TABLE ad_analytics_events (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    ad_id INT UNSIGNED NOT NULL,
    event_type ENUM('impression', 'click', 'conversion'),
    user_id INT UNSIGNED,
    session_id VARCHAR(100),
    user_agent TEXT,
    ip_address VARCHAR(45),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (ad_id) REFERENCES advertisements(id)
);
```

---

## Security & Performance

### Security Features
- **Input Validation**: All ad content sanitized
- **XSS Prevention**: Content filtering for malicious scripts
- **Click Fraud Detection**: Pattern analysis for suspicious clicks
- **Rate Limiting**: API rate limits to prevent abuse

### Performance Optimizations
- **CDN Integration**: Ad content served from CDN
- **Caching**: Multi-layer caching for ad serving
- **Lazy Loading**: Ads loaded asynchronously
- **Connection Pooling**: Optimized database connections

### Monitoring
- **Real-time Metrics**: Live performance dashboards
- **Alert System**: Automated alerts for anomalies
- **A/B Testing**: Built-in testing framework
- **Load Testing**: Performance testing under high traffic

---

## Integration Features

### External Integrations
- **Google Analytics**: Enhanced tracking integration
- **Facebook Pixel**: Social media tracking
- **Payment Gateways**: Budget and billing integration
- **Email Systems**: Campaign notifications

### API Features
- **REST API**: Full CRUD operations
- **Webhook Support**: Real-time event notifications
- **Bulk Operations**: Batch API operations
- **Rate Limiting**: API usage controls