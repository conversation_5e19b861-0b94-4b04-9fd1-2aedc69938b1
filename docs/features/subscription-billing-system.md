# Subscription & Billing System

Hệ thống subscription và billing enterprise-grade với đầy đủ tính năng plan management, payment processing, trial periods, và billing analytics.

## <PERSON><PERSON><PERSON> lụ<PERSON>

- [1. Tổng quan](#1-tổng-quan)
- [2. Subscription Management](#2-subscription-management)
- [3. Plan Management](#3-plan-management)
- [4. Feature Catalog](#4-feature-catalog)
- [5. Billing & Payment](#5-billing--payment)
- [6. Trial Management](#6-trial-management)
- [7. Analytics & Reporting](#7-analytics--reporting)

---

## 1. Tổng quan

### Kiến trúc Subscription System

```mermaid
graph TB
    subgraph "Plan Management"
        Plans[Tenant Plans]
        Features[Feature Catalog]
        Pricing[Pricing Tiers]
    end
    
    subgraph "Subscription Core"
        Subscription[Subscriptions]
        Billing[Billing Cycles]
        Usage[Usage Tracking]
    end
    
    subgraph "Payment Processing"
        Payment[Payment Methods]
        Invoice[Invoice Generation]
        Gateway[Payment Gateway]
    end
    
    subgraph "Feature Control"
        Access[Feature Access]
        Limits[Usage Limits]
        Quotas[Resource Quotas]
    end
    
    Plans --> Subscription
    Features --> Access
    Subscription --> Billing
    Billing --> Payment
    Payment --> Invoice
    Usage --> Limits
```

### Core Features

- **Multi-tier Plans**: Flexible pricing tiers với feature sets
- **Feature-based Access Control**: Granular feature permissions
- **Usage Tracking**: Real-time usage monitoring và limits
- **Trial Management**: Comprehensive trial period handling
- **Billing Automation**: Automated billing cycles và invoicing
- **Payment Integration**: Multiple payment gateway support
- **Subscription Lifecycle**: Complete subscription state management
- **Analytics Dashboard**: Revenue và usage analytics

---

## 2. Subscription Management

### 2.1. Subscription Lifecycle

```mermaid
stateDiagram-v2
    [*] --> trial : Create Trial
    [*] --> active : Direct Subscription
    trial --> active : Convert to Paid
    trial --> expired : Trial Ends
    active --> past_due : Payment Failed
    active --> canceled : User Cancels
    past_due --> active : Payment Recovered
    past_due --> canceled : Max Failures
    canceled --> active : Reactivate
    expired --> active : Convert
    canceled --> [*]
    expired --> [*]
```

### 2.2. Subscription API Endpoints

#### Base URL: `/api/cms/v1/tenant/subscriptions`

#### Create Subscription

**Request:**
```json
{
  "tenant_id": 123,
  "plan_id": 456,
  "billing_cycle": "monthly",
  "payment_method_id": "pm_12345",
  "coupon_code": "SAVE20",
  "metadata": {
    "source": "website",
    "campaign": "summer_sale"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 789,
    "tenant_id": 123,
    "plan": {
      "id": 456,
      "name": "Professional",
      "price": 49.99,
      "currency": "USD"
    },
    "status": "active",
    "billing_cycle": "monthly",
    "current_period_start": "2024-07-01T00:00:00Z",
    "current_period_end": "2024-08-01T00:00:00Z",
    "trial_end": null,
    "cancel_at_period_end": false,
    "created_at": "2024-07-01T10:30:00Z"
  }
}
```

### 2.3. Subscription Operations

#### Update Subscription

```json
{
  "plan_id": 789,
  "billing_cycle": "yearly",
  "proration_behavior": "create_prorations"
}
```

#### Cancel Subscription

```json
{
  "cancel_at_period_end": true,
  "cancellation_reason": "switching_service",
  "feedback": "Found a better alternative"
}
```

#### Pause/Resume Subscription

```json
{
  "action": "pause",
  "pause_collection": "mark_uncollectible",
  "resume_at": "2024-08-01T00:00:00Z"
}
```

---

## 3. Plan Management

### 3.1. Plan Structure

#### Create Plan

**Request:**
```json
{
  "name": "Professional",
  "description": "Advanced features for growing teams",
  "pricing": {
    "monthly": 49.99,
    "yearly": 499.99,
    "currency": "USD"
  },
  "features": [
    {
      "feature_code": "api_calls",
      "limit": 100000,
      "type": "usage"
    },
    {
      "feature_code": "team_members",
      "limit": 10,
      "type": "seat"
    },
    {
      "feature_code": "advanced_analytics",
      "enabled": true,
      "type": "boolean"
    }
  ],
  "metadata": {
    "popular": true,
    "recommended": false
  }
}
```

### 3.2. Plan Features

#### Feature Types

1. **Boolean Features**: On/off features
2. **Usage Features**: Metered usage với limits
3. **Seat Features**: Per-user licensing
4. **Storage Features**: Data storage limits
5. **API Features**: API call limits

#### Plan Comparison

**Response:**
```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": 1,
        "name": "Starter",
        "price_monthly": 9.99,
        "features": {
          "api_calls": 10000,
          "team_members": 3,
          "storage_gb": 10,
          "advanced_analytics": false
        }
      },
      {
        "id": 2,
        "name": "Professional",
        "price_monthly": 49.99,
        "features": {
          "api_calls": 100000,
          "team_members": 10,
          "storage_gb": 100,
          "advanced_analytics": true
        }
      }
    ]
  }
}
```

---

## 4. Feature Catalog

### 4.1. Feature Management

#### Feature Catalog Structure

```json
{
  "features": [
    {
      "code": "api_calls",
      "name": "API Calls",
      "description": "Number of API calls per month",
      "type": "usage",
      "unit": "calls",
      "category": "api",
      "plans": {
        "starter": 10000,
        "professional": 100000,
        "enterprise": -1
      }
    },
    {
      "code": "team_members",
      "name": "Team Members",
      "description": "Number of team members allowed",
      "type": "seat",
      "unit": "users",
      "category": "collaboration",
      "plans": {
        "starter": 3,
        "professional": 10,
        "enterprise": -1
      }
    }
  ]
}
```

### 4.2. Feature Access Control

#### Check Feature Access

**Request:**
```json
{
  "tenant_id": 123,
  "feature_code": "advanced_analytics",
  "usage_amount": 1
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "allowed": true,
    "feature": {
      "code": "advanced_analytics",
      "name": "Advanced Analytics",
      "enabled": true
    },
    "usage": {
      "current": 0,
      "limit": -1,
      "remaining": -1
    },
    "plan": {
      "name": "Professional",
      "features_included": true
    }
  }
}
```

---

## 5. Billing & Payment

### 5.1. Billing Cycles

#### Billing Configuration

```json
{
  "subscription_id": 789,
  "billing_cycle": "monthly",
  "billing_day": 1,
  "timezone": "UTC",
  "currency": "USD",
  "payment_method": {
    "type": "card",
    "card": {
      "brand": "visa",
      "last4": "4242",
      "exp_month": 12,
      "exp_year": 2025
    }
  }
}
```

### 5.2. Invoice Management

#### Generate Invoice

**Response:**
```json
{
  "success": true,
  "data": {
    "invoice": {
      "id": "inv_12345",
      "number": "INV-2024-001",
      "subscription_id": 789,
      "status": "paid",
      "amount_paid": 4999,
      "amount_due": 0,
      "currency": "USD",
      "period_start": "2024-07-01T00:00:00Z",
      "period_end": "2024-08-01T00:00:00Z",
      "line_items": [
        {
          "description": "Professional Plan - Monthly",
          "quantity": 1,
          "amount": 4999,
          "currency": "USD"
        }
      ],
      "paid_at": "2024-07-01T10:30:00Z",
      "hosted_invoice_url": "https://billing.example.com/invoices/inv_12345"
    }
  }
}
```

### 5.3. Payment Processing

#### Payment Flow

```mermaid
sequenceDiagram
    participant User
    participant System
    participant PaymentGateway
    participant Webhook

    User->>+System: Subscribe to Plan
    System->>+PaymentGateway: Create Payment Intent
    PaymentGateway-->>-System: Payment Intent Created
    System-->>-User: Client Secret
    
    User->>+PaymentGateway: Confirm Payment
    PaymentGateway->>+Webhook: Payment Succeeded
    Webhook->>+System: Update Subscription
    System-->>-Webhook: Confirmed
    PaymentGateway-->>-User: Payment Complete
```

#### Payment Methods

- **Credit/Debit Cards**: Stripe, PayPal
- **Digital Wallets**: Apple Pay, Google Pay
- **Bank Transfers**: ACH, SEPA
- **Cryptocurrencies**: Bitcoin, Ethereum (planned)

---

## 6. Trial Management

### 6.1. Trial Configuration

#### Start Trial

**Request:**
```json
{
  "tenant_id": 123,
  "plan_id": 456,
  "trial_period_days": 14,
  "require_payment_method": false,
  "trial_features": ["all"],
  "conversion_tracking": {
    "source": "landing_page",
    "campaign": "free_trial_2024"
  }
}
```

### 6.2. Trial Operations

#### Extend Trial

```json
{
  "extension_days": 7,
  "reason": "customer_request",
  "notification": true
}
```

#### Convert Trial

```json
{
  "payment_method_id": "pm_12345",
  "billing_cycle": "monthly",
  "proration_behavior": "none"
}
```

### 6.3. Trial Analytics

**Response:**
```json
{
  "success": true,
  "data": {
    "trial_metrics": {
      "total_trials": 1250,
      "active_trials": 89,
      "conversion_rate": 18.5,
      "average_trial_duration": 12.3,
      "top_conversion_sources": [
        {
          "source": "google_ads",
          "conversions": 156,
          "rate": 22.1
        }
      ]
    },
    "trial_funnel": {
      "started": 1250,
      "engaged": 890,
      "converted": 231,
      "churned": 1019
    }
  }
}
```

---

## 7. Analytics & Reporting

### 7.1. Revenue Analytics

#### Revenue Dashboard

**Response:**
```json
{
  "success": true,
  "data": {
    "revenue_metrics": {
      "mrr": 125000.00,
      "arr": 1500000.00,
      "growth_rate": 15.5,
      "churn_rate": 5.2,
      "ltv": 2400.00,
      "cac": 180.00
    },
    "revenue_breakdown": {
      "by_plan": [
        {
          "plan_name": "Professional",
          "revenue": 75000.00,
          "subscribers": 1500,
          "percentage": 60.0
        }
      ],
      "by_billing_cycle": {
        "monthly": 80000.00,
        "yearly": 45000.00
      }
    },
    "cohort_analysis": {
      "retention_rates": {
        "month_1": 95.0,
        "month_3": 85.0,
        "month_6": 75.0,
        "month_12": 65.0
      }
    }
  }
}
```

### 7.2. Usage Analytics

#### Feature Usage Tracking

**Response:**
```json
{
  "success": true,
  "data": {
    "usage_metrics": {
      "total_api_calls": 15000000,
      "active_tenants": 2500,
      "top_features": [
        {
          "feature_code": "api_calls",
          "usage": 15000000,
          "tenants_using": 2300
        }
      ]
    },
    "limit_analysis": {
      "approaching_limits": [
        {
          "tenant_id": 123,
          "feature": "api_calls",
          "usage_percentage": 85.0,
          "limit": 100000,
          "current": 85000
        }
      ]
    }
  }
}
```

### 7.3. Financial Reports

#### Monthly Financial Report

```json
{
  "report_period": "2024-07",
  "summary": {
    "total_revenue": 125000.00,
    "new_revenue": 15000.00,
    "churned_revenue": 5000.00,
    "net_revenue_change": 10000.00
  },
  "subscriptions": {
    "new_subscriptions": 125,
    "canceled_subscriptions": 35,
    "net_change": 90
  },
  "payment_metrics": {
    "successful_payments": 2450,
    "failed_payments": 78,
    "success_rate": 96.9
  }
}
```

---

## Database Schema

### Core Tables

```sql
-- Tenant Plans
CREATE TABLE tenant_plans (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    pricing JSON NOT NULL,
    features JSON NOT NULL,
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_status (status)
);

-- Subscriptions
CREATE TABLE tenant_subscriptions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    plan_id INT UNSIGNED NOT NULL,
    status ENUM('trialing', 'active', 'past_due', 'canceled', 'unpaid') NOT NULL,
    billing_cycle ENUM('monthly', 'yearly') NOT NULL,
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    trial_start TIMESTAMP NULL,
    trial_end TIMESTAMP NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    canceled_at TIMESTAMP NULL,
    payment_method_id VARCHAR(255),
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES tenant_plans(id),
    INDEX idx_tenant_status (tenant_id, status),
    INDEX idx_period_end (current_period_end)
);

-- Usage Tracking
CREATE TABLE tenant_usage_tracking (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    subscription_id INT UNSIGNED NOT NULL,
    feature_code VARCHAR(100) NOT NULL,
    usage_amount INT NOT NULL DEFAULT 0,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES tenant_subscriptions(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_feature_period (tenant_id, feature_code, period_start),
    INDEX idx_period_range (period_start, period_end)
);

-- Invoices
CREATE TABLE tenant_invoices (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    subscription_id INT UNSIGNED NOT NULL,
    invoice_number VARCHAR(100) NOT NULL UNIQUE,
    status ENUM('draft', 'open', 'paid', 'void', 'uncollectible') NOT NULL,
    amount_due INT NOT NULL,
    amount_paid INT NOT NULL DEFAULT 0,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    due_date TIMESTAMP NOT NULL,
    paid_at TIMESTAMP NULL,
    hosted_invoice_url VARCHAR(500),
    line_items JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (subscription_id) REFERENCES tenant_subscriptions(id) ON DELETE CASCADE,
    INDEX idx_subscription_status (subscription_id, status),
    INDEX idx_due_date (due_date)
);
```

---

## Integration Features

### Payment Gateway Integration

#### Stripe Integration
- Subscription management
- Webhook handling
- Payment method management
- Invoice hosting

#### PayPal Integration
- Subscription billing
- Express checkout
- Merchant account integration

### Accounting Integration

#### QuickBooks Integration
- Automatic invoice sync
- Revenue recognition
- Tax calculation

#### Xero Integration
- Financial reporting
- Bank reconciliation
- Multi-currency support

---

## Security & Compliance

### PCI Compliance
- Secure payment processing
- Tokenized payment methods
- Encrypted data storage
- Regular security audits

### GDPR Compliance
- Data privacy controls
- Right to be forgotten
- Data portability
- Consent management

### Financial Regulations
- Revenue recognition (ASC 606)
- Tax compliance
- Audit trails
- Financial reporting

---

## Performance & Scalability

### Caching Strategy
- Subscription data caching
- Feature access caching
- Usage metrics caching
- Plan comparison caching

### Background Processing
- Billing cycle processing
- Usage aggregation
- Invoice generation
- Payment retry logic

### Monitoring & Alerts
- Payment failure alerts
- Trial expiration notifications
- Usage limit warnings
- Revenue anomaly detection