# Website & Domain Management System

Hệ thống quản lý website và domain toàn diện cho phép multi-site management, custom domains, SSL certificates, và domain analytics trong kiến trúc multi-tenant.

## <PERSON><PERSON><PERSON> lục

- [1. Tổng quan](#1-tổng-quan)
- [2. Website Management](#2-website-management)
- [3. Domain Management](#3-domain-management)
- [4. SSL Certificate Management](#4-ssl-certificate-management)
- [5. DNS Management](#5-dns-management)
- [6. Multi-site Architecture](#6-multi-site-architecture)
- [7. Performance & Analytics](#7-performance--analytics)

---

## 1. Tổng quan

### Kiến trúc Website System

```mermaid
graph TB
    subgraph "Tenant Level"
        Tenant[Tenant]
        TenantSettings[Tenant Settings]
    end
    
    subgraph "Website Level"
        Website[Website]
        WebsiteConfig[Website Config]
        Theme[Theme Settings]
    end
    
    subgraph "Domain Level"
        Domain[Custom Domains]
        DNS[DNS Records]
        SSL[SSL Certificates]
    end
    
    subgraph "Content Level"
        Pages[Pages]
        Blog[Blog Posts]
        Media[Media Files]
    end
    
    subgraph "Infrastructure"
        CDN[CDN Distribution]
        LoadBalancer[Load Balancer]
        Cache[Caching Layer]
    end
    
    Tenant --> Website
    Website --> Domain
    Website --> Pages
    Domain --> DNS
    Domain --> SSL
    Website --> CDN
```

### Core Features

- **Multi-site Management**: Multiple websites per tenant
- **Custom Domain Support**: Unlimited custom domains
- **SSL/TLS Management**: Automatic certificate provisioning
- **DNS Management**: Complete DNS control
- **Performance Optimization**: CDN và caching
- **SEO Tools**: Meta tags, sitemaps, redirects
- **Analytics Integration**: Traffic và performance monitoring
- **Theme System**: Customizable website themes

---

## 2. Website Management

### 2.1. API Endpoints

#### Base URL: `/api/cms/v1/websites`

#### Create Website - `POST /websites`

**Request:**
```json
{
  "tenant_id": 123,
  "name": "Company Blog",
  "description": "Official company blog and news",
  "subdomain": "blog",
  "language": "en",
  "timezone": "America/New_York",
  "theme": {
    "name": "modern-blog",
    "version": "1.2.0",
    "custom_css": "/* Custom styles */",
    "settings": {
      "primary_color": "#007bff",
      "font_family": "Inter",
      "layout": "wide"
    }
  },
  "seo_settings": {
    "title_template": "{title} | Company Blog",
    "meta_description": "Latest news and insights from our company",
    "keywords": ["technology", "innovation", "blog"],
    "robots": "index,follow"
  },
  "social_settings": {
    "twitter": "@company",
    "facebook": "company",
    "linkedin": "company"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 456,
    "tenant_id": 123,
    "name": "Company Blog",
    "subdomain": "blog",
    "primary_domain": "blog.example.com",
    "status": "active",
    "language": "en",
    "timezone": "America/New_York",
    "theme": {
      "name": "modern-blog",
      "version": "1.2.0"
    },
    "created_at": "2024-07-01T10:30:00Z",
    "published_at": "2024-07-01T10:30:00Z",
    "stats": {
      "total_pages": 0,
      "total_posts": 0,
      "total_visitors": 0
    }
  }
}
```

### 2.2. Website Operations

#### List Websites - `GET /websites`

**Query Parameters:**
- `tenant_id`: Filter by tenant
- `status`: active, inactive, maintenance
- `search`: Search by name/domain
- `sort`: name, created_at, last_activity

#### Get Website Details - `GET /websites/{id}`

**Response:**
```json
{
  "success": true,
  "data": {
    "website": {
      "id": 456,
      "name": "Company Blog",
      "description": "Official company blog and news",
      "subdomain": "blog",
      "primary_domain": "blog.example.com",
      "status": "active"
    },
    "domains": [
      {
        "id": 789,
        "domain": "blog.company.com",
        "type": "custom",
        "ssl_status": "active",
        "verified": true
      }
    ],
    "theme": {
      "name": "modern-blog",
      "settings": {...}
    },
    "analytics": {
      "total_pages": 25,
      "total_posts": 150,
      "monthly_visitors": 12500,
      "monthly_pageviews": 45000
    }
  }
}
```

### 2.3. Website Configuration

#### Update Website Settings - `PUT /websites/{id}`

**Request:**
```json
{
  "name": "Updated Blog Name",
  "description": "Updated description",
  "theme": {
    "settings": {
      "primary_color": "#28a745",
      "header_style": "transparent"
    }
  },
  "seo_settings": {
    "title_template": "{title} | Updated Blog",
    "meta_description": "Updated meta description"
  },
  "maintenance_mode": false,
  "custom_css": "/* Updated custom CSS */",
  "custom_js": "/* Custom JavaScript */"
}
```

---

## 3. Domain Management

### 3.1. API Endpoints

#### Base URL: `/api/cms/v1/domains`

#### Add Custom Domain - `POST /domains`

**Request:**
```json
{
  "website_id": 456,
  "domain": "blog.company.com",
  "type": "custom",
  "is_primary": true,
  "ssl_enabled": true,
  "force_https": true,
  "www_redirect": "non_www"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 789,
    "website_id": 456,
    "domain": "blog.company.com",
    "type": "custom",
    "is_primary": true,
    "ssl_enabled": true,
    "ssl_status": "pending",
    "verified": false,
    "verification_record": {
      "type": "TXT",
      "name": "_verification.blog.company.com",
      "value": "verify-domain-12345"
    },
    "dns_records": [
      {
        "type": "A",
        "name": "@",
        "value": "*******",
        "ttl": 300
      },
      {
        "type": "CNAME",
        "name": "www",
        "value": "blog.company.com",
        "ttl": 300
      }
    ],
    "created_at": "2024-07-01T10:35:00Z"
  }
}
```

### 3.2. Domain Operations

#### Verify Domain - `POST /domains/{id}/verify`

Kiểm tra DNS records và verification status.

**Response:**
```json
{
  "success": true,
  "data": {
    "verified": true,
    "ssl_status": "active",
    "dns_check": {
      "a_record": true,
      "cname_record": true,
      "txt_record": true
    },
    "ssl_certificate": {
      "issuer": "Let's Encrypt",
      "expires_at": "2024-10-01T00:00:00Z",
      "auto_renew": true
    }
  }
}
```

#### Set Primary Domain - `POST /domains/{id}/set-primary`

#### Delete Domain - `DELETE /domains/{id}`

### 3.3. Domain Analytics

#### Domain Performance - `GET /domains/{id}/analytics`

**Query Parameters:**
- `period`: day, week, month, quarter
- `metrics`: traffic, performance, errors
- `start_date`, `end_date`: Custom range

**Response:**
```json
{
  "success": true,
  "data": {
    "domain": "blog.company.com",
    "period": "month",
    "traffic": {
      "total_visits": 12500,
      "unique_visitors": 8900,
      "pageviews": 45000,
      "bounce_rate": 65.5,
      "avg_session_duration": 180
    },
    "performance": {
      "avg_load_time": 1.2,
      "core_web_vitals": {
        "lcp": 1.8,
        "fid": 85,
        "cls": 0.05
      },
      "uptime_percentage": 99.9
    },
    "traffic_sources": [
      {
        "source": "organic",
        "visits": 7500,
        "percentage": 60.0
      },
      {
        "source": "direct",
        "visits": 3000,
        "percentage": 24.0
      }
    ]
  }
}
```

---

## 4. SSL Certificate Management

### 4.1. SSL Operations

#### Certificate Status - `GET /domains/{id}/ssl`

**Response:**
```json
{
  "success": true,
  "data": {
    "domain": "blog.company.com",
    "ssl_enabled": true,
    "certificate": {
      "status": "active",
      "issuer": "Let's Encrypt",
      "issued_at": "2024-07-01T10:40:00Z",
      "expires_at": "2024-10-01T10:40:00Z",
      "auto_renew": true,
      "san_domains": [
        "blog.company.com",
        "www.blog.company.com"
      ]
    },
    "security": {
      "grade": "A+",
      "protocols": ["TLSv1.2", "TLSv1.3"],
      "cipher_suites": ["TLS_AES_256_GCM_SHA384", "TLS_CHACHA20_POLY1305_SHA256"]
    }
  }
}
```

#### Force SSL Renewal - `POST /domains/{id}/ssl/renew`

### 4.2. SSL Configuration

#### SSL Settings

```json
{
  "force_https": true,
  "hsts_enabled": true,
  "hsts_max_age": 31536000,
  "hsts_include_subdomains": true,
  "redirect_www": "non_www",
  "certificate_transparency": true
}
```

---

## 5. DNS Management

### 5.1. DNS Records Management

#### Get DNS Records - `GET /domains/{id}/dns`

**Response:**
```json
{
  "success": true,
  "data": {
    "domain": "blog.company.com",
    "records": [
      {
        "id": 101,
        "type": "A",
        "name": "@",
        "value": "*******",
        "ttl": 300,
        "status": "active"
      },
      {
        "id": 102,
        "type": "CNAME",
        "name": "www",
        "value": "blog.company.com",
        "ttl": 300,
        "status": "active"
      },
      {
        "id": 103,
        "type": "MX",
        "name": "@",
        "value": "10 mail.company.com",
        "ttl": 3600,
        "status": "active"
      }
    ],
    "nameservers": [
      "ns1.example.com",
      "ns2.example.com"
    ]
  }
}
```

#### Create DNS Record - `POST /domains/{id}/dns`

**Request:**
```json
{
  "type": "CNAME",
  "name": "api",
  "value": "api.company.com",
  "ttl": 300
}
```

#### Update DNS Record - `PUT /domains/{id}/dns/{recordId}`

#### Delete DNS Record - `DELETE /domains/{id}/dns/{recordId}`

### 5.2. DNS Templates

#### Apply DNS Template - `POST /domains/{id}/dns/templates`

**Available Templates:**
- **Blog Template**: Standard blog DNS setup
- **E-commerce Template**: Online store DNS configuration
- **Corporate Template**: Business website DNS
- **API Template**: API service DNS setup

---

## 6. Multi-site Architecture

### 6.1. Site Isolation

#### Tenant Isolation

```mermaid
graph TB
    subgraph "Tenant A"
        WebsiteA1[Blog Site]
        WebsiteA2[Store Site]
        DomainA1[blog.companya.com]
        DomainA2[store.companya.com]
    end
    
    subgraph "Tenant B"
        WebsiteB1[Portfolio Site]
        WebsiteB2[News Site]
        DomainB1[portfolio.companyb.com]
        DomainB2[news.companyb.com]
    end
    
    subgraph "Shared Infrastructure"
        CDN[Global CDN]
        LoadBalancer[Load Balancer]
        Database[Database Cluster]
    end
    
    WebsiteA1 --> DomainA1
    WebsiteA2 --> DomainA2
    WebsiteB1 --> DomainB1
    WebsiteB2 --> DomainB2
    
    DomainA1 --> LoadBalancer
    DomainA2 --> LoadBalancer
    DomainB1 --> LoadBalancer
    DomainB2 --> LoadBalancer
    
    LoadBalancer --> CDN
    LoadBalancer --> Database
```

### 6.2. Cross-site Features

#### Shared Resources
- **Media Library**: Cross-site media sharing
- **User Management**: Single sign-on across sites
- **Analytics**: Unified analytics dashboard
- **SEO Tools**: Cross-site SEO optimization

#### Site-specific Features
- **Theme Customization**: Per-site themes
- **Content Management**: Site-specific content
- **Domain Configuration**: Independent domain settings
- **Performance Settings**: Site-specific optimizations

---

## 7. Performance & Analytics

### 7.1. Performance Monitoring

#### Core Web Vitals - `GET /websites/{id}/performance`

**Response:**
```json
{
  "success": true,
  "data": {
    "core_web_vitals": {
      "lcp": {
        "value": 1.8,
        "rating": "good",
        "target": "< 2.5s"
      },
      "fid": {
        "value": 85,
        "rating": "good",
        "target": "< 100ms"
      },
      "cls": {
        "value": 0.05,
        "rating": "good",
        "target": "< 0.1"
      }
    },
    "performance_metrics": {
      "page_load_time": 1.2,
      "time_to_first_byte": 0.3,
      "first_contentful_paint": 0.8,
      "time_to_interactive": 1.5
    },
    "lighthouse_score": {
      "performance": 95,
      "accessibility": 88,
      "best_practices": 92,
      "seo": 100
    }
  }
}
```

### 7.2. Traffic Analytics

#### Website Analytics - `GET /websites/{id}/analytics`

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "total_visits": 25000,
      "unique_visitors": 18500,
      "pageviews": 95000,
      "bounce_rate": 58.5,
      "avg_session_duration": 195
    },
    "top_pages": [
      {
        "path": "/blog/latest-tech-trends",
        "title": "Latest Tech Trends 2024",
        "visits": 3500,
        "unique_visitors": 2800
      }
    ],
    "traffic_sources": {
      "organic": 45.5,
      "direct": 28.2,
      "social": 15.3,
      "referral": 8.7,
      "email": 2.3
    },
    "geographic": [
      {
        "country": "United States",
        "visits": 12500,
        "percentage": 50.0
      }
    ],
    "devices": {
      "desktop": 55.2,
      "mobile": 38.7,
      "tablet": 6.1
    }
  }
}
```

### 7.3. SEO Analytics

#### SEO Performance - `GET /websites/{id}/seo`

**Response:**
```json
{
  "success": true,
  "data": {
    "search_performance": {
      "total_clicks": 15000,
      "total_impressions": 450000,
      "average_ctr": 3.33,
      "average_position": 8.5
    },
    "keyword_rankings": [
      {
        "keyword": "blog platform",
        "position": 3,
        "clicks": 2500,
        "impressions": 25000,
        "ctr": 10.0
      }
    ],
    "indexed_pages": 245,
    "crawl_errors": 3,
    "sitemap_status": "submitted",
    "mobile_usability": "good"
  }
}
```

---

## Database Schema

### Core Tables

```sql
-- Websites
CREATE TABLE websites (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    subdomain VARCHAR(100) NOT NULL,
    primary_domain VARCHAR(255),
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'UTC',
    theme_settings JSON,
    seo_settings JSON,
    social_settings JSON,
    custom_css TEXT,
    custom_js TEXT,
    maintenance_mode BOOLEAN DEFAULT FALSE,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_subdomain (tenant_id, subdomain),
    INDEX idx_status (status),
    INDEX idx_published (published_at)
);

-- Custom Domains
CREATE TABLE website_domains (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    domain VARCHAR(255) NOT NULL UNIQUE,
    type ENUM('subdomain', 'custom') NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    ssl_enabled BOOLEAN DEFAULT TRUE,
    ssl_status ENUM('pending', 'active', 'failed', 'expired') DEFAULT 'pending',
    ssl_certificate_id VARCHAR(255),
    verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    force_https BOOLEAN DEFAULT TRUE,
    www_redirect ENUM('www', 'non_www', 'none') DEFAULT 'non_www',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP NULL,
    
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    INDEX idx_website_primary (website_id, is_primary),
    INDEX idx_verification (verified, verification_token)
);

-- DNS Records
CREATE TABLE website_dns_records (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    domain_id INT UNSIGNED NOT NULL,
    type ENUM('A', 'AAAA', 'CNAME', 'MX', 'TXT', 'SRV') NOT NULL,
    name VARCHAR(255) NOT NULL,
    value TEXT NOT NULL,
    ttl INT UNSIGNED DEFAULT 300,
    priority INT UNSIGNED NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (domain_id) REFERENCES website_domains(id) ON DELETE CASCADE,
    INDEX idx_domain_type (domain_id, type),
    INDEX idx_status (status)
);

-- Website Analytics
CREATE TABLE website_analytics_daily (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    date DATE NOT NULL,
    visits INT UNSIGNED DEFAULT 0,
    unique_visitors INT UNSIGNED DEFAULT 0,
    pageviews INT UNSIGNED DEFAULT 0,
    bounce_rate DECIMAL(5,2) DEFAULT 0.00,
    avg_session_duration INT UNSIGNED DEFAULT 0,
    traffic_sources JSON,
    top_pages JSON,
    geographic_data JSON,
    device_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_website_date (website_id, date),
    INDEX idx_date (date)
);
```

---

## CDN & Caching

### CDN Configuration

#### Global Distribution
- **Edge Locations**: 200+ global edge locations
- **Cache Optimization**: Intelligent caching rules
- **Image Optimization**: Automatic image compression
- **Minification**: CSS/JS minification

#### Cache Management

```json
{
  "cache_settings": {
    "static_assets": {
      "ttl": 31536000,
      "types": ["css", "js", "images", "fonts"]
    },
    "html_pages": {
      "ttl": 3600,
      "vary_by": ["Accept-Encoding", "User-Agent"]
    },
    "api_responses": {
      "ttl": 300,
      "cache_keys": ["path", "query_params"]
    }
  },
  "purge_rules": {
    "auto_purge": true,
    "content_update_purge": true,
    "manual_purge": true
  }
}
```

---

## Security Features

### Security Headers
- **HSTS**: HTTP Strict Transport Security
- **CSP**: Content Security Policy
- **X-Frame-Options**: Clickjacking protection
- **X-XSS-Protection**: XSS filtering

### DDoS Protection
- **Rate Limiting**: Automated rate limiting
- **Bot Detection**: Advanced bot filtering
- **Geo-blocking**: Country-based blocking
- **IP Reputation**: Malicious IP filtering

### SSL/TLS Configuration
- **TLS 1.3**: Latest TLS protocol support
- **Perfect Forward Secrecy**: Enhanced security
- **Certificate Transparency**: CT monitoring
- **OCSP Stapling**: Performance optimization

---

## API Rate Limiting

### Rate Limit Tiers

```json
{
  "rate_limits": {
    "free_tier": {
      "requests_per_minute": 60,
      "daily_limit": 1000
    },
    "professional_tier": {
      "requests_per_minute": 300,
      "daily_limit": 10000
    },
    "enterprise_tier": {
      "requests_per_minute": 1000,
      "daily_limit": 100000
    }
  }
}
```

### Monitoring & Alerts
- **Usage Monitoring**: Real-time usage tracking
- **Threshold Alerts**: Automated notifications
- **Overage Protection**: Automatic throttling
- **Analytics Dashboard**: Usage analytics