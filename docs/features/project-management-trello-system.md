# Project Management System (Trello-like)

Hệ thống quản lý dự án kiểu Trello với đầy đủ tính năng workspaces, boards, lists, cards, checklists và member management.

## <PERSON><PERSON><PERSON> l<PERSON>

- [1. Tổng quan](#1-tổng-quan)
- [2. Workspace Management](#2-workspace-management)
- [3. Board Management](#3-board-management)
- [4. List Management](#4-list-management)
- [5. Card Management](#5-card-management)
- [6. Checklist System](#6-checklist-system)
- [7. Member Management](#7-member-management)
- [8. Advanced Features](#8-advanced-features)

---

## 1. Tổng quan

### Kiến trúc <PERSON>rello <PERSON>

```mermaid
graph TB
    subgraph "Workspace Level"
        Workspace[Workspace]
        WorkspaceMembers[Workspace Members]
    end
    
    subgraph "Board Level"
        Board[Board]
        BoardLabels[Board Labels]
        BoardMembers[Board Members]
    end
    
    subgraph "List Level"
        List[List]
        ListPosition[List Positioning]
    end
    
    subgraph "Card Level"
        Card[Card]
        CardMembers[Card Members]
        CardComments[Card Comments]
        CardAttachments[Card Attachments]
    end
    
    subgraph "Checklist Level"
        Checklist[Checklist]
        ChecklistItems[Checklist Items]
    end
    
    Workspace --> Board
    Board --> List
    List --> Card
    Card --> Checklist
    Card --> CardMembers
    Card --> CardComments
```

### Core Features

- **Hierarchical Structure**: Workspace → Board → List → Card → Checklist
- **Member Management**: Multi-level member assignments
- **Real-time Collaboration**: Live updates và notifications
- **Flexible Organization**: Drag & drop, positioning, archiving
- **Rich Content**: Comments, attachments, labels, due dates
- **Search & Filtering**: Advanced search capabilities
- **Activity Tracking**: Complete audit trail

---

## 2. Workspace Management

### 2.1. API Endpoints

#### Base URL: `/api/cms/v1/trello/workspaces`

#### Create Workspace - `POST /workspaces`

**Request:**
```json
{
  "name": "Marketing Team",
  "description": "Workspace for marketing projects and campaigns",
  "visibility": "private",
  "website_url": "https://marketing.company.com",
  "settings": {
    "allow_public_boards": false,
    "default_board_visibility": "workspace",
    "enable_comments": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "name": "Marketing Team",
    "description": "Workspace for marketing projects and campaigns",
    "slug": "marketing-team",
    "visibility": "private",
    "website_url": "https://marketing.company.com",
    "member_count": 1,
    "board_count": 0,
    "created_at": "2024-07-01T10:30:00Z",
    "owner": {
      "id": 456,
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }
}
```

### 2.2. Workspace Operations

#### List Workspaces - `GET /workspaces`

**Query Parameters:**
- `visibility`: public, private, all
- `role`: admin, member, observer
- `search`: Search by name/description
- `sort`: name, created_at, updated_at

#### Get Workspace with Boards - `GET /workspaces/{id}/boards`

**Response:**
```json
{
  "success": true,
  "data": {
    "workspace": {
      "id": 123,
      "name": "Marketing Team",
      "description": "Workspace for marketing projects"
    },
    "boards": [
      {
        "id": 789,
        "name": "Q3 Campaign",
        "visibility": "workspace",
        "starred": false,
        "list_count": 4,
        "member_count": 6
      }
    ]
  }
}
```

### 2.3. Member Management

#### Add Member - `POST /workspaces/{id}/members`

**Request:**
```json
{
  "email": "<EMAIL>",
  "role": "member",
  "message": "Welcome to our marketing workspace!"
}
```

#### Member Roles

- **Admin**: Full workspace control
- **Member**: Can create/edit boards
- **Observer**: Read-only access

---

## 3. Board Management

### 3.1. API Endpoints

#### Base URL: `/api/cms/v1/trello/boards`

#### Create Board - `POST /boards`

**Request:**
```json
{
  "workspace_id": 123,
  "name": "Q3 Marketing Campaign",
  "description": "Planning and execution of Q3 marketing initiatives",
  "visibility": "workspace",
  "background": {
    "type": "color",
    "value": "#0079bf"
  },
  "settings": {
    "enable_voting": true,
    "enable_comments": true,
    "enable_due_dates": true,
    "card_aging": false
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 789,
    "workspace_id": 123,
    "name": "Q3 Marketing Campaign",
    "slug": "q3-marketing-campaign",
    "description": "Planning and execution of Q3 marketing initiatives",
    "visibility": "workspace",
    "background": {
      "type": "color",
      "value": "#0079bf"
    },
    "starred": false,
    "closed": false,
    "list_count": 0,
    "member_count": 1,
    "created_at": "2024-07-01T10:35:00Z"
  }
}
```

### 3.2. Board Features

#### Get Board with Lists - `GET /boards/{id}/lists`

**Response:**
```json
{
  "success": true,
  "data": {
    "board": {
      "id": 789,
      "name": "Q3 Marketing Campaign",
      "description": "Planning and execution of Q3 marketing initiatives"
    },
    "lists": [
      {
        "id": 101,
        "name": "To Do",
        "position": 1,
        "card_count": 5,
        "archived": false
      },
      {
        "id": 102,
        "name": "In Progress",
        "position": 2,
        "card_count": 3,
        "archived": false
      }
    ]
  }
}
```

#### Label Management

##### Create Label - `POST /boards/labels`

**Request:**
```json
{
  "board_id": 789,
  "name": "High Priority",
  "color": "red"
}
```

##### List Labels - `GET /boards/{id}/labels`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 201,
      "name": "High Priority",
      "color": "red",
      "usage_count": 8
    },
    {
      "id": 202,
      "name": "Bug",
      "color": "orange",
      "usage_count": 3
    }
  ]
}
```

---

## 4. List Management

### 4.1. API Endpoints

#### Base URL: `/api/cms/v1/trello/lists`

#### Create List - `POST /lists`

**Request:**
```json
{
  "board_id": 789,
  "name": "To Do",
  "position": 1
}
```

#### List Operations

- `GET /lists/board/{boardId}` - Lists by board
- `GET /lists/{id}` - Get specific list
- `PUT /lists/{id}` - Update list
- `DELETE /lists/{id}` - Delete list
- `GET /lists/{id}/cards` - List với cards

### 4.2. Advanced List Operations

#### Move List - `PUT /lists/{id}/move`

**Request:**
```json
{
  "new_position": 3,
  "board_id": 789
}
```

#### Archive/Unarchive List

- `PUT /lists/{id}/archive` - Archive list
- `PUT /lists/{id}/unarchive` - Unarchive list

### 4.3. List with Cards - `GET /lists/{id}/cards`

**Response:**
```json
{
  "success": true,
  "data": {
    "list": {
      "id": 101,
      "name": "To Do",
      "position": 1,
      "archived": false
    },
    "cards": [
      {
        "id": 301,
        "name": "Create social media content",
        "description": "Design posts for Facebook and Instagram",
        "position": 1,
        "due_date": "2024-07-15T17:00:00Z",
        "labels": [
          {
            "id": 201,
            "name": "High Priority",
            "color": "red"
          }
        ],
        "member_count": 2,
        "checklist_count": 1,
        "comment_count": 3
      }
    ]
  }
}
```

---

## 5. Card Management

### 5.1. API Endpoints

#### Base URL: `/api/cms/v1/trello/cards`

#### Create Card - `POST /cards`

**Request:**
```json
{
  "list_id": 101,
  "name": "Create social media content",
  "description": "Design engaging posts for Facebook and Instagram campaigns",
  "position": 1,
  "due_date": "2024-07-15T17:00:00Z",
  "labels": [201, 202],
  "members": [456, 789]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 301,
    "list_id": 101,
    "name": "Create social media content",
    "description": "Design engaging posts for Facebook and Instagram campaigns",
    "position": 1,
    "due_date": "2024-07-15T17:00:00Z",
    "completed": false,
    "archived": false,
    "created_at": "2024-07-01T10:40:00Z",
    "labels": [
      {
        "id": 201,
        "name": "High Priority",
        "color": "red"
      }
    ],
    "members": [
      {
        "id": 456,
        "name": "John Doe",
        "avatar_url": "https://example.com/avatars/john.jpg"
      }
    ]
  }
}
```

### 5.2. Card Operations

#### Get Card Detail - `GET /cards/{id}/detail`

**Response:**
```json
{
  "success": true,
  "data": {
    "card": {
      "id": 301,
      "name": "Create social media content",
      "description": "Design engaging posts for Facebook and Instagram campaigns",
      "due_date": "2024-07-15T17:00:00Z",
      "completed": false
    },
    "list": {
      "id": 101,
      "name": "To Do"
    },
    "board": {
      "id": 789,
      "name": "Q3 Marketing Campaign"
    },
    "labels": [...],
    "members": [...],
    "checklists": [...],
    "comments": [...],
    "attachments": [...],
    "activities": [...]
  }
}
```

#### Move Card - `PUT /cards/{id}/move`

**Request:**
```json
{
  "list_id": 102,
  "position": 2,
  "board_id": 789
}
```

### 5.3. Member Assignment

#### Assign Member - `POST /cards/{id}/members`

**Request:**
```json
{
  "member_id": 789
}
```

#### Unassign Member - `DELETE /cards/{id}/members/{memberId}`

### 5.4. Comments System

#### Create Comment - `POST /cards/comments`

**Request:**
```json
{
  "card_id": 301,
  "content": "Updated the design mockups, please review!",
  "mentions": [456, 789]
}
```

### 5.5. Search Cards - `GET /cards/search/board/{boardId}`

**Query Parameters:**
- `q`: Search query
- `labels`: Filter by label IDs
- `members`: Filter by member IDs
- `due_date`: overdue, today, week, month
- `completed`: true, false

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": 301,
        "name": "Create social media content",
        "list_name": "To Do",
        "labels": [...],
        "members": [...],
        "due_date": "2024-07-15T17:00:00Z"
      }
    ],
    "total": 1,
    "query": "social media"
  }
}
```

---

## 6. Checklist System

### 6.1. API Endpoints

#### Base URL: `/api/cms/v1/trello/checklists`

#### Create Checklist - `POST /checklists`

**Request:**
```json
{
  "card_id": 301,
  "name": "Content Creation Tasks",
  "position": 1
}
```

#### Checklist Operations

- `GET /checklists/card/{cardId}` - Checklists by card
- `GET /checklists/{id}` - Get specific checklist
- `PUT /checklists/{id}` - Update checklist
- `DELETE /checklists/{id}` - Delete checklist

### 6.2. Checklist Items

#### Create Item - `POST /checklists/items`

**Request:**
```json
{
  "checklist_id": 401,
  "name": "Design Facebook post graphics",
  "position": 1,
  "due_date": "2024-07-10T12:00:00Z"
}
```

#### Item Operations

- `PUT /checklists/items/{itemId}` - Update item
- `DELETE /checklists/items/{itemId}` - Delete item
- `PUT /checklists/items/{itemId}/check` - Mark as completed
- `PUT /checklists/items/{itemId}/uncheck` - Mark as incomplete

#### Checklist Progress

**Response Example:**
```json
{
  "success": true,
  "data": {
    "checklist": {
      "id": 401,
      "name": "Content Creation Tasks",
      "progress": {
        "completed_items": 3,
        "total_items": 5,
        "percentage": 60.0
      }
    },
    "items": [
      {
        "id": 501,
        "name": "Design Facebook post graphics",
        "completed": true,
        "completed_at": "2024-07-08T14:30:00Z",
        "completed_by": {
          "id": 456,
          "name": "John Doe"
        }
      }
    ]
  }
}
```

---

## 7. Member Management

### 7.1. Multi-level Membership

#### Workspace Members
- Global workspace access
- Can see all workspace boards
- Role-based permissions

#### Board Members
- Board-specific access
- Can be added without workspace membership
- Inherited workspace permissions

#### Card Members
- Task-specific assignments
- Notification recipients
- Activity tracking

### 7.2. Permission Hierarchy

```mermaid
flowchart TD
    A[Workspace Admin] --> B[Board Admin]
    A --> C[Board Member]
    A --> D[Board Observer]
    
    B --> E[Full Board Access]
    C --> F[Create/Edit Cards]
    D --> G[Read Only]
    
    E --> H[Manage Members]
    E --> I[Archive Board]
    F --> J[Comment & Edit]
    G --> K[View Only]
```

### 7.3. Member Activities

#### Activity Tracking
- Card assignments
- Comments added
- Items completed
- Due date changes
- File attachments

#### Notification System
- Real-time notifications
- Email digests
- Mobile push notifications
- Activity feeds

---

## 8. Advanced Features

### 8.1. Board Templates

#### Template System
- Pre-defined board structures
- Industry-specific templates
- Custom template creation
- Template sharing

#### Available Templates
- **Kanban Board**: To Do, Doing, Done
- **Sprint Planning**: Backlog, Sprint, Review, Done
- **Content Calendar**: Ideas, Planning, Writing, Published
- **Bug Tracking**: New, In Progress, Testing, Closed

### 8.2. Automation (Butler-like)

#### Rule-based Automation
- Card movement triggers
- Due date automation
- Member assignment rules
- Label automation

#### Button Actions
- Quick card creation
- Bulk operations
- Status updates
- Notification triggers

### 8.3. Power-ups Integration

#### Available Power-ups
- **Calendar View**: Visualize due dates
- **Timeline View**: Gantt chart visualization
- **Analytics**: Board usage statistics
- **Time Tracking**: Track time spent on cards

### 8.4. Import/Export

#### Export Options
- JSON export
- CSV export
- PDF reports
- Excel spreadsheets

#### Import Sources
- Trello boards
- Asana projects
- JIRA issues
- CSV files

---

## Database Schema

### Core Tables

```sql
-- Workspaces
CREATE TABLE trello_workspaces (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    slug VARCHAR(255) NOT NULL,
    visibility ENUM('private', 'public') DEFAULT 'private',
    website_url VARCHAR(500),
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_tenant_slug (tenant_id, slug)
);

-- Boards
CREATE TABLE trello_boards (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    workspace_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    slug VARCHAR(255) NOT NULL,
    visibility ENUM('private', 'workspace', 'public') DEFAULT 'workspace',
    background JSON,
    starred BOOLEAN DEFAULT FALSE,
    closed BOOLEAN DEFAULT FALSE,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (workspace_id) REFERENCES trello_workspaces(id) ON DELETE CASCADE,
    UNIQUE KEY uk_workspace_slug (workspace_id, slug)
);

-- Lists
CREATE TABLE trello_lists (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    board_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    position DECIMAL(10,5) NOT NULL,
    archived BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (board_id) REFERENCES trello_boards(id) ON DELETE CASCADE,
    INDEX idx_board_position (board_id, position)
);

-- Cards
CREATE TABLE trello_cards (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    list_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    position DECIMAL(10,5) NOT NULL,
    due_date TIMESTAMP NULL,
    completed BOOLEAN DEFAULT FALSE,
    archived BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (list_id) REFERENCES trello_lists(id) ON DELETE CASCADE,
    INDEX idx_list_position (list_id, position),
    INDEX idx_due_date (due_date)
);

-- Checklists
CREATE TABLE trello_checklists (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    card_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    position DECIMAL(10,5) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (card_id) REFERENCES trello_cards(id) ON DELETE CASCADE
);

-- Checklist Items
CREATE TABLE trello_checklist_items (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    checklist_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    position DECIMAL(10,5) NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    due_date TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    completed_by_user_id INT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (checklist_id) REFERENCES trello_checklists(id) ON DELETE CASCADE
);
```

---

## Performance & Optimization

### Caching Strategy
- Board data caching
- Member permission caching
- Activity feed optimization
- Search result caching

### Real-time Updates
- WebSocket connections
- Event-driven updates
- Optimistic UI updates
- Conflict resolution

### Search Optimization
- Full-text search indexing
- Elasticsearch integration
- Faceted search
- Auto-complete suggestions

---

## Mobile & Desktop Apps

### API Compatibility
- RESTful API design
- Consistent response formats
- Offline sync capability
- Progressive web app support

### Real-time Features
- Live board updates
- Collaborative editing
- Presence indicators
- Activity notifications

### Keyboard Shortcuts
- Quick card creation
- Navigation shortcuts
- Bulk operations
- Power user features