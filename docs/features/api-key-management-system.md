# API Key Management System

Hệ thống quản lý API Key enterprise-grade với đầy đủ tính năng authentication, authorization, rate limiting, và analytics cho external API access.

## <PERSON><PERSON><PERSON> lục

- [1. Tổng quan](#1-tổng-quan)
- [2. API Key Management](#2-api-key-management)
- [3. Authentication & Authorization](#3-authentication--authorization)
- [4. Middleware System](#4-middleware-system)
- [5. Rate Limiting & Security](#5-rate-limiting--security)
- [6. Analytics & Monitoring](#6-analytics--monitoring)
- [7. Integration Examples](#7-integration-examples)

---

## 1. Tổng quan

### Kiến trúc API Key System

```mermaid
graph TB
    subgraph "API Key Management"
        Create[Create API Key]
        Rotate[Key Rotation]
        Revoke[Key Revocation]
        Analytics[Usage Analytics]
    end

    subgraph "Middleware Layer"
        Auth[Authentication]
        RateLimit[Rate Limiting]
        IPWhitelist[IP Whitelist]
        Usage[Usage Tracking]
    end

    subgraph "Authorization"
        Permissions[Permissions]
        Scopes[Scopes]
        RBAC[Role-Based Access]
    end

    subgraph "Security"
        Validation[Key Validation]
        Tracking[Activity Tracking]
        Alerts[Security Alerts]
    end

    Create --> Auth
    Auth --> Permissions
    Auth --> RateLimit
    RateLimit --> Usage
    Permissions --> Scopes
    Usage --> Analytics
```

### Core Features

- **API Key CRUD**: Complete lifecycle management
- **Authentication Middleware**: Secure API access control
- **Fine-grained Permissions**: Resource-level access control
- **Scope-based Authorization**: OAuth-style scope system
- **Rate Limiting**: Configurable request limits
- **IP Whitelisting**: IP-based access control
- **Usage Analytics**: Detailed usage tracking và reporting
- **Key Rotation**: Security best practices
- **Bulk Operations**: Efficient mass management

---

## 2. API Key Management

### 2.1. API Endpoints

#### Base URL: `/api/v1/api-keys`

#### Create API Key - `POST /api-keys`

**Request:**

```json
{
  "name": "Blog API Access",
  "description": "API key for blog content management",
  "scopes": ["blog:read", "blog:write", "users:read"],
  "rate_limit": {
    "requests_per_minute": 1000,
    "requests_per_hour": 50000
  },
  "ip_whitelist": ["*************", "10.0.0.0/8"],
  "expires_at": "2025-07-01T00:00:00Z",
  "metadata": {
    "client": "Mobile App",
    "environment": "production"
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": 123,
    "name": "Blog API Access",
    "key": "ak_live_1234567890abcdef1234567890abcdef",
    "key_prefix": "ak_live_1234",
    "scopes": ["blog:read", "blog:write", "users:read"],
    "status": "active",
    "created_at": "2024-07-01T10:30:00Z",
    "expires_at": "2025-07-01T00:00:00Z",
    "last_used_at": null,
    "usage_count": 0
  }
}
```

#### Key Operations

- `GET /api-keys` - List API keys với filtering
- `GET /api-keys/{id}` - Get API key details
- `GET /api-keys/{id}/detail` - Get detailed information
- `PUT /api-keys/{id}` - Update API key settings
- `DELETE /api-keys/{id}` - Delete API key

#### Key Management Operations

##### Rotate API Key - `POST /api-keys/{id}/rotate`

Tạo key mới, giữ key cũ active trong grace period.

**Response:**

```json
{
  "success": true,
  "data": {
    "new_key": "ak_live_newkey1234567890abcdef",
    "old_key_expires_at": "2024-07-08T10:30:00Z",
    "grace_period_days": 7
  }
}
```

##### Revoke API Key - `POST /api-keys/{id}/revoke`

Immediately disable API key.

#### Bulk Operations - `POST /api-keys/bulk-action`

**Request:**

```json
{
  "action": "revoke",
  "api_key_ids": [123, 124, 125],
  "reason": "Security incident"
}
```

### 2.2. Permission Management

#### Create Permission - `POST /api-keys/{id}/permissions`

**Request:**

```json
{
  "resource": "blog",
  "action": "write",
  "conditions": {
    "tenant_id": 456,
    "allowed_fields": ["title", "content", "status"]
  }
}
```

#### Permission Structure

```json
{
  "id": 789,
  "api_key_id": 123,
  "resource": "blog",
  "action": "write",
  "conditions": {
    "tenant_id": 456,
    "allowed_fields": ["title", "content", "status"],
    "time_restrictions": {
      "start_hour": 9,
      "end_hour": 17,
      "timezone": "UTC"
    }
  },
  "created_at": "2024-07-01T10:30:00Z"
}
```

---

## 3. Authentication & Authorization

### 3.1. API Key Authentication

#### Header-based Authentication

```http
GET /api/public/v1/blog/posts
Authorization: Bearer ak_live_1234567890abcdef1234567890abcdef
X-API-Key: ak_live_1234567890abcdef1234567890abcdef
```

#### Query Parameter Authentication

```http
GET /api/public/v1/blog/posts?api_key=ak_live_1234567890abcdef1234567890abcdef
```

### 3.2. Validation Process

```mermaid
sequenceDiagram
    participant Client
    participant Middleware
    participant APIKeyService
    participant Database

    Client->>+Middleware: Request with API Key
    Middleware->>+APIKeyService: ValidateAPIKey(key)
    APIKeyService->>+Database: FindActiveKey(key)
    Database-->>-APIKeyService: Key details

    alt Key Valid
        APIKeyService->>APIKeyService: Check expiration
        APIKeyService->>APIKeyService: Check IP whitelist
        APIKeyService->>APIKeyService: Check rate limits
        APIKeyService-->>-Middleware: Validation successful
        Middleware->>Middleware: Set API key context
        Middleware-->>-Client: Request processed
    else Key Invalid
        APIKeyService-->>-Middleware: Validation failed
        Middleware-->>-Client: 401 Unauthorized
    end
```

### 3.3. Scope-based Authorization

#### Scope Examples

- `blog:read` - Read blog posts
- `blog:write` - Create/update blog posts
- `blog:delete` - Delete blog posts
- `users:read` - Read user information
- `admin:all` - Full administrative access
- `analytics:read` - Read analytics data
- `webhooks:receive` - Receive webhook calls

#### Multi-scope Validation

```go
// Example: Endpoint requiring multiple scopes
router.GET("/admin/analytics",
    middleware.RequireScope("admin:all"),
    middleware.RequireScope("analytics:read"),
    handler.GetAdminAnalytics)
```

---

## 4. Middleware System

### 4.1. Available Middleware

#### Authentication Middleware

```go
// Basic authentication
routes.Use(apiKeyMiddleware.Authenticate())

// With custom error handling
routes.Use(apiKeyMiddleware.AuthenticateWithOptions(AuthOptions{
    RequireKey: true,
    ErrorHandler: customErrorHandler,
}))
```

#### Rate Limiting Middleware

```go
// Default rate limiting
routes.Use(apiKeyMiddleware.RateLimit())

// Custom rate limiting
routes.Use(apiKeyMiddleware.RateLimitWithOptions(RateLimitOptions{
    RequestsPerMinute: 100,
    BurstLimit: 10,
}))
```

#### IP Whitelist Middleware

```go
routes.Use(apiKeyMiddleware.IPWhitelist())
```

#### Usage Tracking Middleware

```go
routes.Use(apiKeyMiddleware.TrackUsage())
```

#### Permission Middleware

```go
// Resource-level permissions
routes.Use(apiKeyMiddleware.RequirePermission("blog", "read"))

// Scope-based permissions
routes.Use(apiKeyMiddleware.RequireScope("blog:write"))
```

### 4.2. Middleware Chain Example

```go
// Complete middleware chain for protected routes
publicAPI := router.Group("/api/public/v1")
publicAPI.Use(apiKeyMiddleware.Authenticate())
publicAPI.Use(apiKeyMiddleware.RateLimit())
publicAPI.Use(apiKeyMiddleware.IPWhitelist())
publicAPI.Use(apiKeyMiddleware.TrackUsage())

// Blog routes with specific permissions
blogRoutes := publicAPI.Group("/blog")
blogRoutes.Use(apiKeyMiddleware.RequireScope("blog:read"))
{
    blogRoutes.GET("/posts", handlers.GetPosts)
    blogRoutes.GET("/posts/:id", handlers.GetPost)
}
```

---

## 5. Rate Limiting & Security

### 5.1. Rate Limiting Configuration

#### Per-Key Rate Limits

```json
{
  "api_key_id": 123,
  "rate_limits": {
    "requests_per_minute": 1000,
    "requests_per_hour": 50000,
    "requests_per_day": 1000000,
    "burst_limit": 100
  },
  "current_usage": {
    "minute": 45,
    "hour": 2300,
    "day": 125000
  },
  "reset_times": {
    "minute": "2024-07-01T10:31:00Z",
    "hour": "2024-07-01T11:00:00Z",
    "day": "2024-07-02T00:00:00Z"
  }
}
```

#### Rate Limit Headers

```http
HTTP/1.1 200 OK
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 955
X-RateLimit-Reset: 1625140860
X-RateLimit-Window: 60
```

### 5.2. IP Whitelisting

#### Configuration

```json
{
  "api_key_id": 123,
  "ip_whitelist": [
    "*************",
    "10.0.0.0/8",
    "**********/12",
    "***********/24"
  ],
  "enforcement": "strict",
  "last_updated": "2024-07-01T10:30:00Z"
}
```

### 5.3. Security Features

#### Key Validation Endpoint - `POST /api/v1/validate-api-key`

**Request:**

```json
{
  "api_key": "ak_live_1234567890abcdef1234567890abcdef",
  "client_ip": "*************",
  "request_path": "/api/public/v1/blog/posts",
  "request_method": "GET"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "valid": true,
    "api_key_id": 123,
    "scopes": ["blog:read", "blog:write"],
    "rate_limit_remaining": 955,
    "permissions": [
      {
        "resource": "blog",
        "action": "read",
        "allowed": true
      }
    ]
  }
}
```

---

## 6. Analytics & Monitoring

### 6.1. Usage Analytics - `GET /api-keys/{id}/usage`

**Query Parameters:**

- `period`: hour, day, week, month
- `start_date`, `end_date`: Custom range
- `group_by`: endpoint, status_code, method

**Response:**

```json
{
  "success": true,
  "data": {
    "api_key_id": 123,
    "period": "day",
    "summary": {
      "total_requests": 125000,
      "successful_requests": 122500,
      "failed_requests": 2500,
      "success_rate": 98.0,
      "average_response_time": 150
    },
    "usage_by_endpoint": [
      {
        "endpoint": "/api/public/v1/blog/posts",
        "requests": 75000,
        "percentage": 60.0
      }
    ],
    "usage_by_hour": [
      {
        "hour": "2024-07-01T09:00:00Z",
        "requests": 5200,
        "errors": 12
      }
    ]
  }
}
```

### 6.2. System Analytics - `GET /api-keys/analytics`

**Response:**

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_api_keys": 450,
      "active_keys": 387,
      "revoked_keys": 63,
      "total_requests_today": 2500000,
      "unique_clients": 125
    },
    "top_api_keys": [
      {
        "id": 123,
        "name": "Blog API Access",
        "requests_today": 125000,
        "success_rate": 98.5
      }
    ],
    "usage_trends": {
      "daily_growth": 15.5,
      "weekly_growth": 8.2,
      "peak_hour": "14:00",
      "peak_requests": 350000
    }
  }
}
```

### 6.3. Monitoring Features

#### Real-time Dashboards

- Active API keys count
- Request volume per minute
- Error rates by API key
- Geographic distribution
- Response time percentiles

#### Alerts & Notifications

- Rate limit exceeded
- Unusual usage patterns
- Failed authentication attempts
- API key expiration warnings
- Security incidents

---

## 7. Integration Examples

### 7.1. Public API Routes

#### Blog API

```go
publicAPI := router.Group("/api/public/v1")
publicAPI.Use(apiKeyMiddleware.Authenticate())
publicAPI.Use(apiKeyMiddleware.RateLimit())
publicAPI.Use(apiKeyMiddleware.TrackUsage())

blogRoutes := publicAPI.Group("/blog")
blogRoutes.Use(apiKeyMiddleware.RequireScope("blog:read"))
{
    blogRoutes.GET("/posts", handlers.GetPosts)
    blogRoutes.GET("/posts/:id", handlers.GetPost)
    blogRoutes.GET("/categories", handlers.GetCategories)
}

// Write operations require different scope
blogWriteRoutes := publicAPI.Group("/blog")
blogWriteRoutes.Use(apiKeyMiddleware.RequireScope("blog:write"))
{
    blogWriteRoutes.POST("/posts", handlers.CreatePost)
    blogWriteRoutes.PUT("/posts/:id", handlers.UpdatePost)
}
```

### 7.2. Webhook Routes

```go
webhooks := router.Group("/webhooks")
webhooks.Use(apiKeyMiddleware.Authenticate())
webhooks.Use(apiKeyMiddleware.RequireScope("webhooks:receive"))
webhooks.Use(apiKeyMiddleware.TrackUsage())
{
    webhooks.POST("/blog/post-created", handlers.BlogPostCreated)
    webhooks.POST("/user/registered", handlers.UserRegistered)
    webhooks.POST("/payment/completed", handlers.PaymentCompleted)
}
```

### 7.3. Admin API Routes

```go
adminAPI := router.Group("/api/cms/v1")
adminAPI.Use(apiKeyMiddleware.Authenticate())
adminAPI.Use(apiKeyMiddleware.RequireScope("admin:all"))
adminAPI.Use(apiKeyMiddleware.RateLimit())
{
    adminAPI.GET("/system/health", handlers.SystemHealth)
    adminAPI.GET("/system/stats", handlers.SystemStats)
    adminAPI.GET("/users/analytics", handlers.UserAnalytics)
}
```

---

## Database Schema

### Core Tables

```sql
-- API Keys
CREATE TABLE api_keys (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    key_hash VARCHAR(255) NOT NULL UNIQUE,
    key_prefix VARCHAR(20) NOT NULL,
    scopes JSON,
    status ENUM('active', 'revoked', 'expired') DEFAULT 'active',
    rate_limit_config JSON,
    ip_whitelist JSON,
    metadata JSON,
    expires_at TIMESTAMP NULL,
    last_used_at TIMESTAMP NULL,
    usage_count BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_tenant_user (tenant_id, user_id),
    INDEX idx_status_expires (status, expires_at),
    INDEX idx_prefix (key_prefix)
);

-- API Key Permissions
CREATE TABLE api_key_permissions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    api_key_id INT UNSIGNED NOT NULL,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    conditions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    UNIQUE KEY uk_api_key_resource_action (api_key_id, resource, action)
);

-- API Key Usage Logs
CREATE TABLE api_key_usage_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    api_key_id INT UNSIGNED NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INT NOT NULL,
    response_time_ms INT,
    client_ip VARCHAR(45),
    user_agent TEXT,
    request_size BIGINT,
    response_size BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    INDEX idx_api_key_created (api_key_id, created_at),
    INDEX idx_endpoint_created (endpoint, created_at)
);
```

---

## Security Best Practices

### Key Generation

- Cryptographically secure random generation
- Prefix-based key identification (ak*live*, ak*test*)
- SHA-256 hashing for storage
- Minimum 32-character length

### Access Control

- Principle of least privilege
- Regular key rotation (recommended: 90 days)
- Automatic expiration dates
- Audit logging for all operations

### Rate Limiting

- Per-key limits
- Burst protection
- Sliding window algorithm
- Graceful degradation

### Monitoring

- Real-time usage monitoring
- Anomaly detection
- Security incident alerts
- Compliance reporting

---

## Client SDK Examples

### JavaScript SDK

```javascript
const ApiClient = require('@company/api-client');

const client = new ApiClient({
  apiKey: 'ak_live_1234567890abcdef1234567890abcdef',
  baseUrl: 'https://api.example.com',
});

// Get blog posts
const posts = await client.blog.getPosts({
  limit: 10,
  category: 'technology',
});

// Create new post
const newPost = await client.blog.createPost({
  title: 'API Integration Guide',
  content: 'Complete guide to API integration...',
  status: 'published',
});
```

### Python SDK

```python
from company_api import APIClient

client = APIClient(
    api_key='ak_live_1234567890abcdef1234567890abcdef',
    base_url='https://api.example.com'
)

# Get blog posts
posts = client.blog.get_posts(limit=10, category='technology')

# Create new post
new_post = client.blog.create_post(
    title='API Integration Guide',
    content='Complete guide to API integration...',
    status='published'
)
```

---

## Performance & Scalability

### Caching Strategy

- Redis-based key validation cache
- Rate limit counters in Redis
- Usage statistics aggregation

### Load Balancing

- Stateless middleware design
- Horizontal scaling support
- Database connection pooling

### Optimization

- Key validation optimization
- Batch usage logging
- Async analytics processing
