# Settings & Configuration System

Hệ thống quản lý cài đặt và cấu hình phân cấp với kiến trúc Hierarchical Resolution cho phép cấu hình linh hoạt ở nhiều cấp độ khác nhau.

## <PERSON><PERSON><PERSON> l<PERSON>

- [1. Tổng quan](#1-tổng-quan)
- [2. Hierarchical Resolution](#2-hierarchical-resolution)
- [3. Schema Management](#3-schema-management)
- [4. Settings API](#4-settings-api)
- [5. Encryption & Security](#5-encryption--security)
- [6. Bulk Operations](#6-bulk-operations)
- [7. Integration Examples](#7-integration-examples)

---

## 1. Tổng quan

### Kiến trúc Settings System

```mermaid
graph TB
    subgraph "Resolution Hierarchy"
        User[User Level Settings]
        Module[Module Level Settings]
        Tenant[Tenant Level Settings]
        System[System Level Settings]
        Default[Schema Defaults]
    end
    
    subgraph "Core Components"
        SettingsService[Settings Service]
        SchemaRegistry[Schema Registry]
        EncryptionService[Encryption Service]
        ValidationEngine[Validation Engine]
    end
    
    subgraph "Storage Layer"
        Database[Settings Database]
        Cache[Redis Cache]
        SchemaStore[Schema Storage]
    end
    
    User --> SettingsService
    Module --> SettingsService
    Tenant --> SettingsService
    System --> SettingsService
    Default --> SettingsService
    
    SettingsService --> SchemaRegistry
    SettingsService --> EncryptionService
    SettingsService --> ValidationEngine
    
    SettingsService --> Database
    SettingsService --> Cache
    SchemaRegistry --> SchemaStore
```

### Core Features

- **Hierarchical Resolution**: User > Module > Tenant > System > Default
- **Type-safe Settings**: Strongly typed configuration values
- **Schema Validation**: Comprehensive validation rules
- **Encryption Support**: Sensitive data encryption
- **Bulk Operations**: Efficient batch operations
- **Caching Layer**: High-performance setting access
- **Version Management**: Setting change tracking
- **Import/Export**: Configuration backup và restore

---

## 2. Hierarchical Resolution

### 2.1. Resolution Order

Hệ thống tự động tìm giá trị setting theo thứ tự ưu tiên:

1. **User Level** - Cài đặt cá nhân của user
2. **Module Level** - Cài đặt module-specific
3. **Tenant Level** - Cài đặt của organization/tenant
4. **System Level** - Cài đặt toàn hệ thống
5. **Schema Default** - Giá trị mặc định từ schema

### 2.2. Resolution Flow

```mermaid
flowchart TD
    A[Request Setting: 'site_name'] --> B{Check User Level}
    B -->|Found| C[Return User Value]
    B -->|Not Found| D{Check Module Level}
    D -->|Found| E[Return Module Value]
    D -->|Not Found| F{Check Tenant Level}
    F -->|Found| G[Return Tenant Value]
    F -->|Not Found| H{Check System Level}
    H -->|Found| I[Return System Value]
    H -->|Not Found| J{Check Schema Default}
    J -->|Has Default| K[Return Default Value]
    J -->|No Default| L[Return Error: Not Found]
```

### 2.3. Resolution API

#### Get Setting với Resolution - `SettingsService.Get()`

```go
// Basic get với automatic resolution
value, err := settingsService.Get(ctx, "general", "site_name")

// Get với detailed resolution info
result, err := settingsService.Resolve(ctx, "general", "site_name")
// Returns:
// {
//   "value": "My Company Blog",
//   "source": "tenant",
//   "effective_scope": "tenant",
//   "overridden": true,
//   "inheritance_chain": ["user", "module", "tenant"]
// }
```

#### Type-safe Getters

```go
// String settings
siteName := settingsService.GetString(ctx, "general", "site_name", "Default Site")

// Integer settings  
maxUsers := settingsService.GetInt(ctx, "limits", "max_users", 100)

// Boolean settings
emailEnabled := settingsService.GetBool(ctx, "email", "enabled", true)

// JSON settings
var config map[string]interface{}
err := settingsService.GetJSON(ctx, "api", "config", &config)
```

---

## 3. Schema Management

### 3.1. Schema Registration

#### Register Setting Schema

```go
schema := &models.SettingSchema{
    Category:    "general",
    Key:        "site_name",
    Type:       "string",
    Default:    "My Blog",
    Required:   true,
    Validation: map[string]interface{}{
        "min_length": 3,
        "max_length": 100,
        "pattern":    "^[a-zA-Z0-9\\s\\-_]+$",
    },
    Description: "The display name of the website",
    Sensitive:  false,
    Scopes:     []string{"system", "tenant", "module", "user"},
}

err := settingsService.RegisterSchema(ctx, schema)
```

### 3.2. API Endpoints

#### Base URL: `/api/cms/v1/settings`

#### Create Schema - `POST /settings/schemas`

**Request:**
```json
{
  "category": "email",
  "key": "smtp_password",
  "type": "string",
  "default": "",
  "required": true,
  "sensitive": true,
  "validation": {
    "min_length": 8,
    "encryption": "required"
  },
  "description": "SMTP server password for email delivery",
  "scopes": ["system", "tenant"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "category": "email",
    "key": "smtp_password",
    "type": "string",
    "sensitive": true,
    "scopes": ["system", "tenant"],
    "created_at": "2024-07-01T10:30:00Z"
  }
}
```

### 3.3. Schema Types

#### Supported Data Types

```json
{
  "data_types": {
    "string": {
      "validation": ["min_length", "max_length", "pattern", "enum"]
    },
    "integer": {
      "validation": ["min", "max", "enum"]
    },
    "float": {
      "validation": ["min", "max", "precision"]
    },
    "boolean": {
      "validation": []
    },
    "json": {
      "validation": ["schema", "max_size"]
    },
    "array": {
      "validation": ["min_items", "max_items", "item_type"]
    },
    "datetime": {
      "validation": ["format", "timezone"]
    },
    "email": {
      "validation": ["domain_whitelist", "mx_check"]
    },
    "url": {
      "validation": ["scheme", "domain_whitelist"]
    }
  }
}
```

---

## 4. Settings API

### 4.1. CRUD Operations

#### Set Setting - `PUT /settings/{category}/{key}`

**Request:**
```json
{
  "value": "My Company Blog",
  "scope": "tenant",
  "metadata": {
    "updated_by": "admin",
    "reason": "Rebranding"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "category": "general",
    "key": "site_name",
    "value": "My Company Blog",
    "scope": "tenant",
    "encrypted": false,
    "updated_at": "2024-07-01T10:35:00Z",
    "version": 2
  }
}
```

#### Get Setting - `GET /settings/{category}/{key}`

**Query Parameters:**
- `scope`: Specific scope to check (bypasses resolution)
- `include_metadata`: Include additional metadata
- `resolve`: Show resolution details

**Response:**
```json
{
  "success": true,
  "data": {
    "category": "general",
    "key": "site_name",
    "value": "My Company Blog",
    "effective_scope": "tenant",
    "source": "tenant",
    "overridden": true,
    "resolution_chain": [
      {
        "scope": "user",
        "found": false
      },
      {
        "scope": "module", 
        "found": false
      },
      {
        "scope": "tenant",
        "found": true,
        "value": "My Company Blog"
      }
    ],
    "schema": {
      "type": "string",
      "required": true,
      "sensitive": false
    }
  }
}
```

### 4.2. Batch Operations

#### Bulk Get Settings - `POST /settings/bulk/get`

**Request:**
```json
{
  "settings": [
    {"category": "general", "key": "site_name"},
    {"category": "email", "key": "from_address"},
    {"category": "api", "key": "rate_limit"}
  ],
  "scope": "tenant",
  "include_metadata": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "settings": [
      {
        "category": "general",
        "key": "site_name",
        "value": "My Company Blog",
        "effective_scope": "tenant"
      },
      {
        "category": "email", 
        "key": "from_address",
        "value": "<EMAIL>",
        "effective_scope": "system"
      }
    ],
    "errors": []
  }
}
```

#### Bulk Set Settings - `POST /settings/bulk/set`

**Request:**
```json
{
  "scope": "tenant",
  "settings": [
    {
      "category": "general",
      "key": "site_name", 
      "value": "Updated Blog Name"
    },
    {
      "category": "email",
      "key": "enabled",
      "value": true
    }
  ],
  "metadata": {
    "batch_id": "batch_12345",
    "updated_by": "admin"
  }
}
```

### 4.3. Category Management

#### List Categories - `GET /settings/categories`

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "name": "general",
        "description": "General site settings",
        "setting_count": 15,
        "schema_count": 12
      },
      {
        "name": "email",
        "description": "Email configuration",
        "setting_count": 8,
        "schema_count": 10
      }
    ]
  }
}
```

#### Get Category Settings - `GET /settings/categories/{category}`

**Response:**
```json
{
  "success": true,
  "data": {
    "category": "general",
    "settings": [
      {
        "key": "site_name",
        "value": "My Company Blog",
        "type": "string",
        "scope": "tenant",
        "sensitive": false
      }
    ],
    "schemas": [
      {
        "key": "site_name",
        "type": "string",
        "required": true,
        "default": "My Blog"
      }
    ]
  }
}
```

---

## 5. Encryption & Security

### 5.1. Sensitive Data Encryption

#### Automatic Encryption

```go
// Sensitive settings are automatically encrypted
schema := &models.SettingSchema{
    Category:   "email",
    Key:       "smtp_password",
    Type:      "string", 
    Sensitive: true, // This field will be encrypted
}

// Setting values are encrypted before storage
err := settingsService.Set(ctx, "email", "smtp_password", "secret123", models.ScopeTenant)
// Value is automatically encrypted using AES-256-GCM
```

#### Encryption Configuration

```json
{
  "encryption": {
    "algorithm": "AES-256-GCM",
    "key_derivation": "PBKDF2",
    "iterations": 100000,
    "salt_length": 32,
    "key_rotation": {
      "enabled": true,
      "rotation_days": 90,
      "keep_previous_keys": 3
    }
  }
}
```

### 5.2. Access Control

#### Permission-based Access

```go
// Check if user can read setting
canRead := settingsService.CanRead(ctx, userID, "email", "smtp_password")

// Check if user can write setting  
canWrite := settingsService.CanWrite(ctx, userID, "email", "smtp_password")

// Role-based access control
roles := []string{"admin", "settings_manager"}
hasAccess := settingsService.HasRoleAccess(ctx, userID, roles, "email", "smtp_password")
```

### 5.3. Audit Logging

#### Setting Change Tracking

**Response:**
```json
{
  "success": true,
  "data": {
    "audit_log": [
      {
        "id": 789,
        "category": "general",
        "key": "site_name",
        "action": "update",
        "old_value": "Old Blog Name",
        "new_value": "My Company Blog",
        "scope": "tenant",
        "user_id": 123,
        "user_name": "admin",
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0...",
        "timestamp": "2024-07-01T10:35:00Z"
      }
    ]
  }
}
```

---

## 6. Bulk Operations

### 6.1. Performance Optimizations

#### Efficient Bulk Processing

```go
// Bulk get với caching
settings := []models.SettingRequest{
    {Category: "general", Key: "site_name"},
    {Category: "email", Key: "enabled"},
    {Category: "api", Key: "rate_limit"},
}

results, err := settingsService.BulkGet(ctx, settings, models.ScopeTenant)

// Bulk set với transaction
updates := []models.SettingUpdate{
    {Category: "general", Key: "site_name", Value: "New Name"},
    {Category: "email", Key: "enabled", Value: true},
}

err = settingsService.BulkSet(ctx, updates, models.ScopeTenant)
```

### 6.2. Import/Export

#### Export Settings - `GET /settings/export`

**Query Parameters:**
- `scope`: Export specific scope
- `category`: Export specific category  
- `format`: json, yaml, csv
- `include_sensitive`: Include encrypted values

**Response:**
```json
{
  "export_id": "export_12345",
  "format": "json",
  "scope": "tenant",
  "created_at": "2024-07-01T10:40:00Z",
  "settings": {
    "general": {
      "site_name": "My Company Blog",
      "language": "en",
      "timezone": "America/New_York"
    },
    "email": {
      "enabled": true,
      "from_address": "<EMAIL>"
    }
  },
  "metadata": {
    "total_settings": 25,
    "categories": ["general", "email", "api"],
    "sensitive_count": 3
  }
}
```

#### Import Settings - `POST /settings/import`

**Request:**
```json
{
  "scope": "tenant",
  "merge_strategy": "overwrite",
  "settings": {
    "general": {
      "site_name": "Imported Blog Name",
      "language": "en"
    }
  },
  "options": {
    "validate_schemas": true,
    "skip_sensitive": false,
    "backup_existing": true
  }
}
```

---

## 7. Integration Examples

### 7.1. Module Integration

#### Email Service Integration

```go
// Email service using settings
type EmailService struct {
    settingsService *services.SettingsService
}

func (e *EmailService) SendEmail(ctx context.Context, email *models.Email) error {
    // Get email settings với hierarchical resolution
    smtpHost := e.settingsService.GetString(ctx, "email", "smtp_host", "localhost")
    smtpPort := e.settingsService.GetInt(ctx, "email", "smtp_port", 587)
    smtpUser := e.settingsService.GetString(ctx, "email", "smtp_user", "")
    smtpPass := e.settingsService.GetString(ctx, "email", "smtp_password", "")
    
    // smtpPass is automatically decrypted if it was stored as sensitive
    
    // Configure SMTP client
    config := &smtp.Config{
        Host:     smtpHost,
        Port:     smtpPort,
        Username: smtpUser,
        Password: smtpPass,
    }
    
    return e.sendViaSMTP(config, email)
}
```

### 7.2. Tenant Service Integration

#### Dynamic Tenant Configuration

```go
// Tenant service với per-tenant settings
func (t *TenantService) GetTenantConfig(ctx context.Context, tenantID uint) (*models.TenantConfig, error) {
    // Set tenant context for hierarchical resolution
    ctx = context.WithValue(ctx, "tenant_id", tenantID)
    
    config := &models.TenantConfig{
        SiteName:     t.settingsService.GetString(ctx, "general", "site_name", "Default Blog"),
        Language:     t.settingsService.GetString(ctx, "general", "language", "en"),
        Timezone:     t.settingsService.GetString(ctx, "general", "timezone", "UTC"),
        EmailEnabled: t.settingsService.GetBool(ctx, "email", "enabled", true),
        MaxUsers:     t.settingsService.GetInt(ctx, "limits", "max_users", 100),
    }
    
    return config, nil
}
```

### 7.3. User Preferences

#### Personal Settings

```go
// User preferences using User-level settings
func (u *UserService) SetUserPreferences(ctx context.Context, userID uint, prefs *models.UserPreferences) error {
    // Set user context
    ctx = context.WithValue(ctx, "user_id", userID)
    
    // Set user-specific settings (highest priority)
    err := u.settingsService.Set(ctx, "user", "theme", prefs.Theme, models.ScopeUser)
    err = u.settingsService.Set(ctx, "user", "language", prefs.Language, models.ScopeUser)
    err = u.settingsService.Set(ctx, "user", "notifications", prefs.NotificationSettings, models.ScopeUser)
    
    return err
}

func (u *UserService) GetUserPreferences(ctx context.Context, userID uint) (*models.UserPreferences, error) {
    ctx = context.WithValue(ctx, "user_id", userID)
    
    // These will resolve: User > Module > Tenant > System > Default
    theme := u.settingsService.GetString(ctx, "user", "theme", "default")
    language := u.settingsService.GetString(ctx, "user", "language", "en")
    
    var notifications map[string]bool
    u.settingsService.GetJSON(ctx, "user", "notifications", &notifications)
    
    return &models.UserPreferences{
        Theme:               theme,
        Language:            language,
        NotificationSettings: notifications,
    }, nil
}
```

---

## Database Schema

### Core Tables

```sql
-- Settings Storage
CREATE TABLE settings (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    scope ENUM('system', 'tenant', 'module', 'user') NOT NULL,
    scope_id INT UNSIGNED,
    value TEXT,
    encrypted BOOLEAN DEFAULT FALSE,
    version INT UNSIGNED DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_category_key_scope (category, setting_key, scope, scope_id),
    INDEX idx_category_key (category, setting_key),
    INDEX idx_scope_id (scope, scope_id)
);

-- Setting Schemas
CREATE TABLE setting_schemas (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    data_type ENUM('string', 'integer', 'float', 'boolean', 'json', 'array', 'datetime', 'email', 'url') NOT NULL,
    default_value TEXT,
    required BOOLEAN DEFAULT FALSE,
    sensitive BOOLEAN DEFAULT FALSE,
    validation_rules JSON,
    description TEXT,
    allowed_scopes JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_category_key (category, setting_key),
    INDEX idx_category (category),
    INDEX idx_sensitive (sensitive)
);

-- Audit Log
CREATE TABLE setting_audit_log (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    action ENUM('create', 'update', 'delete') NOT NULL,
    scope ENUM('system', 'tenant', 'module', 'user') NOT NULL,
    scope_id INT UNSIGNED,
    old_value TEXT,
    new_value TEXT,
    user_id INT UNSIGNED,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_category_key_created (category, setting_key, created_at),
    INDEX idx_user_created (user_id, created_at),
    INDEX idx_scope_created (scope, scope_id, created_at)
);
```

---

## Performance & Caching

### Caching Strategy
- **L1 Cache**: In-memory application cache
- **L2 Cache**: Redis distributed cache  
- **Schema Cache**: Long-lived schema caching
- **Resolution Cache**: Cached resolution paths

### Cache Invalidation
- **Setting Changes**: Automatic cache invalidation
- **Scope Changes**: Scope-specific invalidation
- **Schema Updates**: Schema cache clearing
- **Time-based**: TTL-based cache expiration

### Performance Optimizations
- **Bulk Operations**: Batch processing
- **Connection Pooling**: Database optimization
- **Lazy Loading**: On-demand schema loading
- **Compression**: Large value compression