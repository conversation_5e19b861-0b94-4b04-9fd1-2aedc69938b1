# Real-time Notification System

Hệ thống thông báo real-time toàn diện với đầy đủ tính năng WebSocket, email notifications, template management, và tracking analytics.

## <PERSON><PERSON><PERSON> lục

- [1. Tổng quan](#1-tổng-quan)
- [2. Notification Management](#2-notification-management)
- [3. Template System](#3-template-system)
- [4. Real-time Delivery](#4-real-time-delivery)
- [5. Email Notifications](#5-email-notifications)
- [6. Tracking & Analytics](#6-tracking--analytics)
- [7. Advanced Features](#7-advanced-features)

---

## 1. Tổng quan

### Kiến trúc Notification System

```mermaid
graph TB
    subgraph "Notification Core"
        NotificationEngine[Notification Engine]
        TemplateSystem[Template System]
        QueueSystem[Queue System]
    end
    
    subgraph "Delivery Channels"
        WebSocket[WebSocket Real-time]
        Email[Email Service]
        Push[Push Notifications]
        SMS[SMS Service]
    end
    
    subgraph "Template Management"
        EmailTemplates[Email Templates]
        PushTemplates[Push Templates]
        Variables[Template Variables]
    end
    
    subgraph "Tracking & Analytics"
        DeliveryTracking[Delivery Tracking]
        EngagementMetrics[Engagement Metrics]
        Analytics[Analytics Dashboard]
    end
    
    NotificationEngine --> WebSocket
    NotificationEngine --> Email
    NotificationEngine --> Push
    NotificationEngine --> SMS
    
    TemplateSystem --> EmailTemplates
    TemplateSystem --> PushTemplates
    TemplateSystem --> Variables
    
    Email --> DeliveryTracking
    Push --> DeliveryTracking
    DeliveryTracking --> Analytics
```

### Core Features

- **Multi-channel Delivery**: WebSocket, Email, Push, SMS
- **Template Management**: Rich template system với variables
- **Real-time Processing**: Instant notification delivery
- **Queue Management**: Reliable message queuing
- **Tracking & Analytics**: Comprehensive delivery tracking
- **Personalization**: User preference management
- **Scheduling**: Delayed và recurring notifications
- **A/B Testing**: Template performance testing

---

## 2. Notification Management

### 2.1. API Endpoints

#### Base URL: `/api/cms/v1/notifications`

#### Send Notification - `POST /notifications`

**Request:**
```json
{
  "tenant_id": 123,
  "website_id": 456,
  "template_id": 7,
  "recipients": [
    {
      "type": "user",
      "user_id": 789,
      "channels": ["websocket", "email"]
    },
    {
      "type": "email",
      "email": "<EMAIL>",
      "channels": ["email"]
    }
  ],
  "data": {
    "user_name": "John Doe",
    "verification_url": "https://app.com/verify/abc123",
    "company_name": "Company XYZ"
  },
  "channels": {
    "websocket": {
      "priority": "high",
      "persistent": true
    },
    "email": {
      "priority": "normal",
      "schedule_at": "2024-07-01T14:00:00Z"
    }
  },
  "metadata": {
    "campaign": "user_verification",
    "source": "registration"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "notification_id": "notif_12345",
    "status": "queued",
    "recipients_count": 2,
    "channels_used": ["websocket", "email"],
    "estimated_delivery": "2024-07-01T14:00:00Z",
    "tracking": {
      "tracking_id": "track_67890",
      "pixel_url": "https://track.example.com/p/track_67890.gif"
    }
  }
}
```

### 2.2. Notification Types

#### Real-time Notifications
- **System Alerts**: Critical system messages
- **User Actions**: Activity notifications
- **Status Updates**: Process status changes
- **Chat Messages**: Real-time messaging

#### Scheduled Notifications
- **Welcome Series**: Onboarding sequences
- **Reminders**: Task và deadline reminders
- **Newsletters**: Regular content updates
- **Marketing**: Promotional campaigns

### 2.3. Notification Operations

#### Get Notification Status - `GET /notifications/{id}`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "notif_12345",
    "status": "delivered",
    "template": {
      "id": 7,
      "name": "Email Verification"
    },
    "recipients": [
      {
        "type": "user",
        "user_id": 789,
        "status": "delivered",
        "delivered_at": "2024-07-01T14:01:23Z"
      }
    ],
    "delivery_stats": {
      "total_sent": 2,
      "delivered": 2,
      "failed": 0,
      "opened": 1,
      "clicked": 0
    },
    "created_at": "2024-07-01T14:00:00Z"
  }
}
```

#### Cancel Notification - `DELETE /notifications/{id}`

#### Retry Failed Notification - `POST /notifications/{id}/retry`

---

## 3. Template System

### 3.1. Template Management

#### Base URL: `/api/cms/v1/notification-templates`

#### Create Template - `POST /notification-templates`

**Request:**
```json
{
  "tenant_id": 123,
  "name": "Welcome Email",
  "description": "Welcome email for new users",
  "type": "email",
  "subject": "Welcome to {{company_name}}, {{user_name}}!",
  "content": {
    "html": "<h1>Welcome {{user_name}}!</h1><p>Thanks for joining {{company_name}}.</p>",
    "text": "Welcome {{user_name}}! Thanks for joining {{company_name}}."
  },
  "variables": [
    {
      "name": "user_name",
      "type": "string",
      "required": true,
      "description": "User's display name"
    },
    {
      "name": "company_name",
      "type": "string",
      "required": true,
      "description": "Company name"
    }
  ],
  "settings": {
    "from_email": "noreply@{{tenant_domain}}",
    "from_name": "{{company_name}}",
    "reply_to": "support@{{tenant_domain}}"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 15,
    "name": "Welcome Email",
    "type": "email",
    "status": "active",
    "version": 1,
    "variables": [
      {
        "name": "user_name",
        "type": "string",
        "required": true
      }
    ],
    "created_at": "2024-07-01T10:30:00Z",
    "updated_at": "2024-07-01T10:30:00Z"
  }
}
```

### 3.2. Template Types

#### Email Templates
- **HTML Templates**: Rich HTML emails
- **Text Templates**: Plain text fallback
- **Responsive Design**: Mobile-optimized layouts
- **Dynamic Content**: Conditional content blocks

#### Push Notification Templates
- **Title & Body**: Structured push content
- **Actions**: Interactive push buttons
- **Rich Media**: Images và videos
- **Deep Links**: App navigation

#### WebSocket Templates
- **JSON Structure**: Structured real-time data
- **Event Types**: Categorized events
- **Payload Schema**: Consistent data format

### 3.3. Template Variables

#### System Variables
- `{{user_name}}` - User display name
- `{{user_email}}` - User email address
- `{{company_name}}` - Tenant company name
- `{{tenant_domain}}` - Tenant domain
- `{{current_date}}` - Current date
- `{{unsubscribe_url}}` - Unsubscribe link

#### Custom Variables
- Dynamic data injection
- API data fetching
- Conditional rendering
- Localization support

---

## 4. Real-time Delivery

### 4.1. WebSocket Architecture

#### Connection Management

```mermaid
sequenceDiagram
    participant Client
    participant WebSocketServer
    participant NotificationService
    participant Database

    Client->>+WebSocketServer: Connect with JWT
    WebSocketServer->>+Database: Validate user & tenant
    Database-->>-WebSocketServer: User validated
    WebSocketServer->>WebSocketServer: Create user room
    WebSocketServer-->>-Client: Connection established
    
    NotificationService->>+WebSocketServer: Send notification
    WebSocketServer->>WebSocketServer: Route to user room
    WebSocketServer-->>-Client: Real-time notification
    Client->>+WebSocketServer: Acknowledge receipt
    WebSocketServer->>+NotificationService: Update delivery status
    NotificationService-->>-WebSocketServer: Status updated
```

### 4.2. WebSocket Events

#### Event Types

```json
{
  "event_types": {
    "notification": {
      "description": "New notification received",
      "payload": {
        "id": "notif_12345",
        "type": "user_action",
        "title": "New comment on your post",
        "message": "John Doe commented on your blog post",
        "data": {
          "post_id": 456,
          "comment_id": 789
        },
        "timestamp": "2024-07-01T14:30:00Z"
      }
    },
    "user_status": {
      "description": "User status change",
      "payload": {
        "user_id": 123,
        "status": "online",
        "last_seen": "2024-07-01T14:30:00Z"
      }
    },
    "system_alert": {
      "description": "System-wide alerts",
      "payload": {
        "level": "warning",
        "message": "Scheduled maintenance in 30 minutes",
        "action_required": false
      }
    }
  }
}
```

### 4.3. Real-time Features

#### User Presence
- Online/offline status
- Last seen timestamps
- Active session tracking
- Cross-device presence

#### Live Updates
- Real-time content updates
- Live commenting
- Activity feeds
- Status changes

---

## 5. Email Notifications

### 5.1. Email Service Integration

#### SMTP Configuration

```json
{
  "smtp_settings": {
    "host": "smtp.example.com",
    "port": 587,
    "encryption": "tls",
    "username": "<EMAIL>",
    "password": "encrypted_password",
    "max_connections": 10,
    "timeout": 30
  },
  "sending_options": {
    "rate_limit": 100,
    "retry_attempts": 3,
    "retry_delay": 300,
    "bounce_handling": true
  }
}
```

#### Email Providers
- **SendGrid**: High-volume email delivery
- **Mailgun**: Developer-friendly email API
- **Amazon SES**: Cost-effective email service
- **Custom SMTP**: Self-hosted email servers

### 5.2. Email Templates

#### Template Structure

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{email_subject}}</title>
    <style>
        /* Responsive email styles */
        @media only screen and (max-width: 600px) {
            .container { width: 100% !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{company_logo}}" alt="{{company_name}}" />
        </div>
        <div class="content">
            <h1>{{email_title}}</h1>
            <p>Dear {{user_name}},</p>
            <p>{{email_content}}</p>
            {{#if action_button}}
            <div class="action">
                <a href="{{action_url}}" class="button">{{action_text}}</a>
            </div>
            {{/if}}
        </div>
        <div class="footer">
            <p><a href="{{unsubscribe_url}}">Unsubscribe</a> | {{company_address}}</p>
        </div>
    </div>
    <!-- Tracking pixel -->
    <img src="{{tracking_pixel_url}}" width="1" height="1" style="display:none;" />
</body>
</html>
```

### 5.3. Email Analytics

#### Delivery Metrics

**Response:**
```json
{
  "success": true,
  "data": {
    "email_metrics": {
      "total_sent": 10000,
      "delivered": 9850,
      "bounced": 120,
      "opened": 4500,
      "clicked": 1200,
      "unsubscribed": 25,
      "complained": 5
    },
    "delivery_rate": 98.5,
    "open_rate": 45.7,
    "click_rate": 12.2,
    "unsubscribe_rate": 0.25,
    "engagement_metrics": {
      "top_clicked_links": [
        {
          "url": "https://example.com/product-page",
          "clicks": 800,
          "percentage": 66.7
        }
      ],
      "device_breakdown": {
        "desktop": 55.2,
        "mobile": 38.7,
        "tablet": 6.1
      }
    }
  }
}
```

---

## 6. Tracking & Analytics

### 6.1. Delivery Tracking

#### Track Notification - `GET /notifications/{id}/tracking`

**Response:**
```json
{
  "success": true,
  "data": {
    "notification_id": "notif_12345",
    "tracking_events": [
      {
        "event": "sent",
        "timestamp": "2024-07-01T14:00:00Z",
        "channel": "email",
        "recipient": "<EMAIL>"
      },
      {
        "event": "delivered",
        "timestamp": "2024-07-01T14:01:23Z",
        "channel": "email",
        "recipient": "<EMAIL>"
      },
      {
        "event": "opened",
        "timestamp": "2024-07-01T14:15:45Z",
        "channel": "email",
        "recipient": "<EMAIL>",
        "metadata": {
          "user_agent": "Mozilla/5.0...",
          "ip_address": "*************"
        }
      }
    ],
    "engagement_summary": {
      "total_events": 3,
      "opened": true,
      "clicked": false,
      "unsubscribed": false
    }
  }
}
```

### 6.2. Analytics Dashboard

#### Notification Analytics - `GET /notifications/analytics`

**Query Parameters:**
- `period`: day, week, month, quarter
- `template_id`: Filter by template
- `channel`: Filter by delivery channel
- `start_date`, `end_date`: Custom range

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "total_notifications": 50000,
      "delivered": 48500,
      "delivery_rate": 97.0,
      "total_opens": 22500,
      "open_rate": 45.0,
      "total_clicks": 6750,
      "click_rate": 13.5
    },
    "channel_performance": [
      {
        "channel": "email",
        "sent": 35000,
        "delivered": 34200,
        "delivery_rate": 97.7,
        "engagement_rate": 58.5
      },
      {
        "channel": "websocket",
        "sent": 15000,
        "delivered": 14800,
        "delivery_rate": 98.7,
        "engagement_rate": 85.2
      }
    ],
    "template_performance": [
      {
        "template_id": 7,
        "template_name": "Email Verification",
        "sent": 8000,
        "open_rate": 85.5,
        "click_rate": 75.2
      }
    ],
    "trends": {
      "daily_volume": [
        {
          "date": "2024-07-01",
          "sent": 1500,
          "delivered": 1455,
          "opened": 720
        }
      ]
    }
  }
}
```

### 6.3. A/B Testing

#### Create A/B Test - `POST /notification-templates/ab-test`

**Request:**
```json
{
  "name": "Welcome Email Test",
  "template_a_id": 15,
  "template_b_id": 16,
  "traffic_split": {
    "template_a": 50,
    "template_b": 50
  },
  "success_metric": "click_rate",
  "duration_days": 7,
  "minimum_sample_size": 1000
}
```

---

## 7. Advanced Features

### 7.1. User Preferences

#### Preference Management - `PUT /users/{id}/notification-preferences`

**Request:**
```json
{
  "channels": {
    "email": {
      "enabled": true,
      "frequency": "immediate",
      "types": ["system", "marketing", "social"]
    },
    "websocket": {
      "enabled": true,
      "types": ["system", "social"]
    },
    "push": {
      "enabled": false
    }
  },
  "quiet_hours": {
    "enabled": true,
    "start_time": "22:00",
    "end_time": "08:00",
    "timezone": "America/New_York"
  },
  "digest": {
    "enabled": true,
    "frequency": "daily",
    "time": "09:00"
  }
}
```

### 7.2. Scheduling & Automation

#### Schedule Notification - `POST /notifications/schedule`

**Request:**
```json
{
  "template_id": 15,
  "recipients": [...],
  "schedule": {
    "type": "delayed",
    "send_at": "2024-07-01T14:00:00Z"
  },
  "data": {...}
}
```

#### Recurring Notifications

```json
{
  "schedule": {
    "type": "recurring",
    "pattern": "weekly",
    "day_of_week": 1,
    "time": "09:00:00",
    "timezone": "UTC",
    "end_date": "2024-12-31T23:59:59Z"
  }
}
```

### 7.3. Integration Features

#### Webhook Integration

```json
{
  "webhook_config": {
    "url": "https://api.example.com/webhooks/notifications",
    "events": ["delivered", "opened", "clicked", "bounced"],
    "retry_policy": {
      "max_attempts": 3,
      "backoff_strategy": "exponential"
    },
    "signature_verification": {
      "enabled": true,
      "algorithm": "sha256"
    }
  }
}
```

#### Third-party Integrations
- **Slack**: Team notifications
- **Discord**: Community notifications  
- **Microsoft Teams**: Enterprise notifications
- **Zapier**: Workflow automation

---

## Database Schema

### Core Tables

```sql
-- Notifications
CREATE TABLE notifications (
    id VARCHAR(255) PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED,
    template_id INT UNSIGNED NOT NULL,
    status ENUM('queued', 'sending', 'delivered', 'failed', 'cancelled'),
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    data JSON,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES notification_templates(id),
    INDEX idx_status_scheduled (status, scheduled_at),
    INDEX idx_tenant_created (tenant_id, created_at)
);

-- Notification Templates
CREATE TABLE notification_templates (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type ENUM('email', 'push', 'websocket', 'sms') NOT NULL,
    subject VARCHAR(500),
    content JSON NOT NULL,
    variables JSON,
    settings JSON,
    status ENUM('active', 'inactive') DEFAULT 'active',
    version INT UNSIGNED DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant_type (tenant_id, type),
    INDEX idx_status (status)
);

-- Notification Recipients
CREATE TABLE notification_recipients (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    notification_id VARCHAR(255) NOT NULL,
    recipient_type ENUM('user', 'email', 'phone') NOT NULL,
    recipient_value VARCHAR(255) NOT NULL,
    channel ENUM('email', 'push', 'websocket', 'sms') NOT NULL,
    status ENUM('queued', 'sent', 'delivered', 'failed', 'bounced'),
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    error_message TEXT,
    tracking_data JSON,
    
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    INDEX idx_notification_status (notification_id, status),
    INDEX idx_recipient_channel (recipient_value, channel)
);

-- Tracking Events
CREATE TABLE notification_tracking_events (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    notification_id VARCHAR(255) NOT NULL,
    recipient_id BIGINT UNSIGNED NOT NULL,
    event_type ENUM('sent', 'delivered', 'opened', 'clicked', 'bounced', 'unsubscribed'),
    event_data JSON,
    user_agent TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES notification_recipients(id) ON DELETE CASCADE,
    INDEX idx_notification_event (notification_id, event_type),
    INDEX idx_created_at (created_at)
);
```

---

## Performance & Scalability

### Queue Management
- **Redis Queue**: High-performance message queuing
- **Priority Queues**: Priority-based processing
- **Dead Letter Queues**: Failed message handling
- **Rate Limiting**: Channel-specific rate limits

### Caching Strategy
- **Template Caching**: Template compilation caching
- **User Preference Caching**: Fast preference lookup
- **Analytics Caching**: Aggregated metrics caching

### Horizontal Scaling
- **Microservice Architecture**: Scalable service design
- **Load Balancing**: Distributed processing
- **Database Sharding**: Data distribution
- **CDN Integration**: Global content delivery