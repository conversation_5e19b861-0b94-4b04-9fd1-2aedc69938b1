# Golang Monitoring v<PERSON><PERSON>ana

## Tổng quan

Hệ thống monitoring Blog API V3 sử dụng Prometheus để thu thập metrics và Grafana để visualization. Dự án đã có sẵn hệ thống metrics cơ bản cho database và HTTP.

## Kiến trúc Monitoring

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Blog API V3   │───▶│   Prometheus    │───▶│    Grafana      │
│  (Go Backend)   │    │  (Metrics DB)   │    │ (Visualization) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Custom Metrics │    │   Time Series   │    │   Dashboards    │
│  - DB Metrics   │    │      Data       │    │   & Alerts      │
│  - HTTP Metrics │    │                 │    │                 │
│  - App Metrics  │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Cấu hình Infrastructure

### 1. Docker Compose Setup

Thêm vào `docker-compose.yml`:

```yaml
  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: wn-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - wnapi-network

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: wn-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - wnapi-network

  # Node Exporter (System Metrics)
  node-exporter:
    image: prom/node-exporter:latest
    container_name: wn-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - wnapi-network

volumes:
  prometheus_data:
  grafana_data:
```

### 2. Prometheus Configuration

Tạo file `infrastructure/monitoring/prometheus/prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets: []

scrape_configs:
  # Blog API V3 Application
  - job_name: 'blog-api-v3'
    static_configs:
      - targets: ['host.docker.internal:8080']
    scrape_interval: 15s
    metrics_path: /metrics
    
  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    
  # MySQL Exporter
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']
    scrape_interval: 15s
    
  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    
  # Prometheus Self-Monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
```

## Golang Metrics Implementation

### 1. Prometheus Metrics Package

Tạo file `pkg/metrics/prometheus.go`:

```go
package metrics

import (
	"net/http"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// PrometheusMetrics holds all Prometheus metrics
type PrometheusMetrics struct {
	// HTTP Metrics
	HttpRequestsTotal      *prometheus.CounterVec
	HttpRequestDuration    *prometheus.HistogramVec
	HttpRequestsInFlight   prometheus.Gauge
	HttpResponseSizeBytes  *prometheus.HistogramVec
	HttpRequestSizeBytes   *prometheus.HistogramVec

	// Database Metrics
	DbConnectionsOpen      prometheus.Gauge
	DbConnectionsIdle      prometheus.Gauge
	DbConnectionsInUse     prometheus.Gauge
	DbQueriesTotal         *prometheus.CounterVec
	DbQueryDuration        *prometheus.HistogramVec
	DbTransactionsTotal    *prometheus.CounterVec
	DbConnectionErrors     *prometheus.CounterVec

	// Application Metrics
	AppInfo                *prometheus.GaugeVec
	AppUptime              prometheus.Gauge
	AppRequestsPerSecond   prometheus.Gauge
	AppMemoryUsage         prometheus.Gauge
	AppGoroutines          prometheus.Gauge

	// Business Logic Metrics
	UserRegistrations      *prometheus.CounterVec
	UserLogins             *prometheus.CounterVec
	BlogPosts              *prometheus.CounterVec
	TenantOperations       *prometheus.CounterVec
	CacheHitRate          *prometheus.GaugeVec
	QueueSize             *prometheus.GaugeVec
}

// NewPrometheusMetrics creates a new instance of PrometheusMetrics
func NewPrometheusMetrics() *PrometheusMetrics {
	return &PrometheusMetrics{
		// HTTP Metrics
		HttpRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "http_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"method", "route", "status_code"},
		),
		HttpRequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "route", "status_code"},
		),
		HttpRequestsInFlight: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "http_requests_in_flight",
				Help: "Number of HTTP requests currently being processed",
			},
		),
		HttpResponseSizeBytes: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_response_size_bytes",
				Help:    "HTTP response size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 6),
			},
			[]string{"method", "route", "status_code"},
		),
		HttpRequestSizeBytes: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_size_bytes",
				Help:    "HTTP request size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 6),
			},
			[]string{"method", "route"},
		),

		// Database Metrics
		DbConnectionsOpen: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "db_connections_open",
				Help: "Number of open database connections",
			},
		),
		DbConnectionsIdle: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "db_connections_idle",
				Help: "Number of idle database connections",
			},
		),
		DbConnectionsInUse: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "db_connections_in_use",
				Help: "Number of database connections in use",
			},
		),
		DbQueriesTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "db_queries_total",
				Help: "Total number of database queries",
			},
			[]string{"query_type", "table", "status"},
		),
		DbQueryDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "db_query_duration_seconds",
				Help:    "Database query duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"query_type", "table", "status"},
		),
		DbTransactionsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "db_transactions_total",
				Help: "Total number of database transactions",
			},
			[]string{"status"},
		),
		DbConnectionErrors: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "db_connection_errors_total",
				Help: "Total number of database connection errors",
			},
			[]string{"error_type"},
		),

		// Application Metrics
		AppInfo: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "app_info",
				Help: "Application information",
			},
			[]string{"version", "environment", "commit"},
		),
		AppUptime: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "app_uptime_seconds",
				Help: "Application uptime in seconds",
			},
		),
		AppRequestsPerSecond: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "app_requests_per_second",
				Help: "Application requests per second",
			},
		),
		AppMemoryUsage: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "app_memory_usage_bytes",
				Help: "Application memory usage in bytes",
			},
		),
		AppGoroutines: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "app_goroutines",
				Help: "Number of goroutines",
			},
		),

		// Business Logic Metrics
		UserRegistrations: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "user_registrations_total",
				Help: "Total number of user registrations",
			},
			[]string{"tenant_id", "status"},
		),
		UserLogins: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "user_logins_total",
				Help: "Total number of user logins",
			},
			[]string{"tenant_id", "status"},
		),
		BlogPosts: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "blog_posts_total",
				Help: "Total number of blog posts",
			},
			[]string{"tenant_id", "status", "action"},
		),
		TenantOperations: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "tenant_operations_total",
				Help: "Total number of tenant operations",
			},
			[]string{"tenant_id", "operation", "status"},
		),
		CacheHitRate: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "cache_hit_rate",
				Help: "Cache hit rate percentage",
			},
			[]string{"cache_type"},
		),
		QueueSize: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "queue_size",
				Help: "Queue size by type",
			},
			[]string{"queue_type"},
		),
	}
}

// RecordHttpRequest records HTTP request metrics
func (m *PrometheusMetrics) RecordHttpRequest(method, route string, statusCode int, duration time.Duration, responseSize, requestSize int) {
	statusCodeStr := strconv.Itoa(statusCode)
	
	m.HttpRequestsTotal.WithLabelValues(method, route, statusCodeStr).Inc()
	m.HttpRequestDuration.WithLabelValues(method, route, statusCodeStr).Observe(duration.Seconds())
	m.HttpResponseSizeBytes.WithLabelValues(method, route, statusCodeStr).Observe(float64(responseSize))
	m.HttpRequestSizeBytes.WithLabelValues(method, route).Observe(float64(requestSize))
}

// RecordDbQuery records database query metrics
func (m *PrometheusMetrics) RecordDbQuery(queryType, table, status string, duration time.Duration) {
	m.DbQueriesTotal.WithLabelValues(queryType, table, status).Inc()
	m.DbQueryDuration.WithLabelValues(queryType, table, status).Observe(duration.Seconds())
}

// UpdateDbConnectionStats updates database connection statistics
func (m *PrometheusMetrics) UpdateDbConnectionStats(open, idle, inUse int) {
	m.DbConnectionsOpen.Set(float64(open))
	m.DbConnectionsIdle.Set(float64(idle))
	m.DbConnectionsInUse.Set(float64(inUse))
}

// RecordUserRegistration records user registration metrics
func (m *PrometheusMetrics) RecordUserRegistration(tenantID, status string) {
	m.UserRegistrations.WithLabelValues(tenantID, status).Inc()
}

// RecordUserLogin records user login metrics
func (m *PrometheusMetrics) RecordUserLogin(tenantID, status string) {
	m.UserLogins.WithLabelValues(tenantID, status).Inc()
}

// RecordBlogPost records blog post metrics
func (m *PrometheusMetrics) RecordBlogPost(tenantID, status, action string) {
	m.BlogPosts.WithLabelValues(tenantID, status, action).Inc()
}

// SetAppInfo sets application information
func (m *PrometheusMetrics) SetAppInfo(version, environment, commit string) {
	m.AppInfo.WithLabelValues(version, environment, commit).Set(1)
}

// Handler returns the Prometheus metrics handler
func (m *PrometheusMetrics) Handler() http.Handler {
	return promhttp.Handler()
}
```

### 2. Metrics Middleware

Tạo file `pkg/http/middleware/metrics.go`:

```go
package middleware

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/pkg/metrics"
)

// MetricsMiddleware creates a middleware that records HTTP metrics
func MetricsMiddleware(metricsCollector *metrics.PrometheusMetrics) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		// Track requests in flight
		metricsCollector.HttpRequestsInFlight.Inc()
		defer metricsCollector.HttpRequestsInFlight.Dec()

		// Get request size
		requestSize := int(c.Request.ContentLength)
		if requestSize < 0 {
			requestSize = 0
		}

		// Process request
		c.Next()

		// Record metrics
		duration := time.Since(start)
		route := c.FullPath()
		if route == "" {
			route = "unknown"
		}

		// Get response size
		responseSize := c.Writer.Size()
		if responseSize < 0 {
			responseSize = 0
		}

		// Record HTTP metrics
		metricsCollector.RecordHttpRequest(
			c.Request.Method,
			route,
			c.Writer.Status(),
			duration,
			responseSize,
			requestSize,
		)
	}
}
```

### 3. Database Metrics Integration

Cập nhật file `pkg/database/mysql.go`:

```go
// Thêm vào struct MySQLDatabase
type MySQLDatabase struct {
	db      *sql.DB
	config  *Config
	metrics *ConnectionMetrics
	promMetrics *metrics.PrometheusMetrics // Thêm dòng này
}

// Thêm method để update Prometheus metrics
func (d *MySQLDatabase) updatePrometheusMetrics() {
	if d.promMetrics != nil && d.db != nil {
		stats := d.db.Stats()
		d.promMetrics.UpdateDbConnectionStats(
			stats.OpenConnections,
			stats.Idle,
			stats.InUse,
		)
	}
}

// Wrapper cho Query với metrics
func (d *MySQLDatabase) QueryWithMetrics(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	start := time.Now()
	
	rows, err := d.db.QueryContext(ctx, query, args...)
	
	duration := time.Since(start)
	status := "success"
	if err != nil {
		status = "error"
	}
	
	// Record metrics
	if d.promMetrics != nil {
		d.promMetrics.RecordDbQuery("SELECT", "unknown", status, duration)
	}
	
	// Update connection stats
	d.updatePrometheusMetrics()
	
	return rows, err
}
```

## Grafana Dashboard Configuration

### 1. Grafana Provisioning

Tạo file `infrastructure/monitoring/grafana/provisioning/datasources/datasource.yml`:

```yaml
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
```

### 2. Dashboard JSON

Tạo file `infrastructure/monitoring/grafana/dashboards/blog-api-v3.json`:

```json
{
  "dashboard": {
    "id": null,
    "title": "Blog API V3 - Golang Application Metrics",
    "tags": ["golang", "blog-api", "monitoring"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "HTTP Request Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{route}}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "red", "value": 80}
              ]
            }
          }
        }
      },
      {
        "id": 2,
        "title": "HTTP Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "id": 3,
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "db_connections_open",
            "legendFormat": "Open"
          },
          {
            "expr": "db_connections_idle",
            "legendFormat": "Idle"
          },
          {
            "expr": "db_connections_in_use",
            "legendFormat": "In Use"
          }
        ]
      },
      {
        "id": 4,
        "title": "Error Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status_code=~\"5..\"}[5m])",
            "legendFormat": "5xx errors"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 0.01},
                {"color": "red", "value": 0.05}
              ]
            }
          }
        }
      },
      {
        "id": 5,
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "app_memory_usage_bytes",
            "legendFormat": "Memory Usage"
          }
        ]
      },
      {
        "id": 6,
        "title": "Goroutines",
        "type": "graph",
        "targets": [
          {
            "expr": "app_goroutines",
            "legendFormat": "Goroutines"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

## Alert Rules

### 1. Prometheus Alert Rules

Tạo file `infrastructure/monitoring/prometheus/rules/blog-api-v3.yml`:

```yaml
groups:
  - name: blog-api-v3
    rules:
      # High Error Rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      # High Response Time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1.0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      # Database Connection Issues
      - alert: DatabaseConnectionHigh
        expr: db_connections_in_use / db_connections_open > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Database connection usage high"
          description: "Database connection usage is {{ $value }}%"

      # Memory Usage High
      - alert: MemoryUsageHigh
        expr: app_memory_usage_bytes > 1000000000  # 1GB
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Memory usage high"
          description: "Memory usage is {{ $value }} bytes"

      # Application Down
      - alert: ApplicationDown
        expr: up{job="blog-api-v3"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Application is down"
          description: "Blog API V3 application is not responding"
```

## Integration với Code

### 1. Main Application

Cập nhật file `cmd/server/main.go`:

```go
package main

import (
    "log"
    "net/http"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/tranthanhloi/wn-api-v3/pkg/metrics"
    "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
)

func main() {
    // Initialize metrics
    promMetrics := metrics.NewPrometheusMetrics()
    
    // Set application info
    promMetrics.SetAppInfo("1.0.0", "production", "abc123")
    
    // Initialize Gin router
    r := gin.Default()
    
    // Add metrics middleware
    r.Use(middleware.MetricsMiddleware(promMetrics))
    
    // Metrics endpoint
    r.GET("/metrics", gin.WrapH(promMetrics.Handler()))
    
    // Health check endpoint
    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{"status": "ok"})
    })
    
    // Start background metrics collection
    go collectSystemMetrics(promMetrics)
    
    // Start server
    log.Fatal(r.Run(":8080"))
}

func collectSystemMetrics(promMetrics *metrics.PrometheusMetrics) {
    ticker := time.NewTicker(15 * time.Second)
    defer ticker.Stop()
    
    startTime := time.Now()
    
    for {
        select {
        case <-ticker.C:
            // Update uptime
            promMetrics.AppUptime.Set(time.Since(startTime).Seconds())
            
            // Update goroutines count
            promMetrics.AppGoroutines.Set(float64(runtime.NumGoroutine()))
            
            // Update memory usage
            var m runtime.MemStats
            runtime.ReadMemStats(&m)
            promMetrics.AppMemoryUsage.Set(float64(m.Alloc))
        }
    }
}
```

### 2. Service Layer Integration

Trong các service, thêm metrics:

```go
// Trong auth service
func (s *authService) Register(ctx context.Context, req *RegisterRequest) error {
    start := time.Now()
    
    err := s.repository.CreateUser(ctx, req)
    
    status := "success"
    if err != nil {
        status = "error"
    }
    
    // Record metrics
    if s.metrics != nil {
        s.metrics.RecordUserRegistration(req.TenantID, status)
    }
    
    return err
}
```

## Commands và Scripts

### 1. Makefile Commands

Thêm vào `Makefile`:

```makefile
# Monitoring commands
.PHONY: monitoring-up monitoring-down monitoring-restart monitoring-logs

monitoring-up:
	docker-compose up -d prometheus grafana node-exporter

monitoring-down:
	docker-compose down prometheus grafana node-exporter

monitoring-restart:
	docker-compose restart prometheus grafana node-exporter

monitoring-logs:
	docker-compose logs -f prometheus grafana

# Metrics validation
validate-metrics:
	curl -s http://localhost:8080/metrics | grep -E "(http_requests_total|db_connections_open)"

# Dashboard import
import-dashboards:
	curl -X POST \
		-H "Content-Type: application/json" \
		-d @infrastructure/monitoring/grafana/dashboards/blog-api-v3.json \
		************************************/api/dashboards/db
```

### 2. Health Check Script

Tạo file `scripts/health-check.sh`:

```bash
#!/bin/bash

# Health check script
echo "Checking application health..."

# Check application
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "✓ Application is healthy"
else
    echo "✗ Application is not responding"
    exit 1
fi

# Check Prometheus
if curl -f http://localhost:9090/-/healthy > /dev/null 2>&1; then
    echo "✓ Prometheus is healthy"
else
    echo "✗ Prometheus is not responding"
    exit 1
fi

# Check Grafana
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✓ Grafana is healthy"
else
    echo "✗ Grafana is not responding"
    exit 1
fi

echo "All services are healthy!"
```

## Monitoring Best Practices

### 1. Metrics Naming

- Sử dụng snake_case cho tên metrics
- Prefix với application name: `blog_api_v3_`
- Suffix với unit: `_total`, `_seconds`, `_bytes`

### 2. Label Best Practices

- Giữ số lượng label values thấp (<10)
- Không sử dụng high cardinality labels (user_id, request_id)
- Sử dụng meaningful labels (tenant_id, status, method)

### 3. Dashboard Organization

- Tách dashboards theo domain (HTTP, Database, Business)
- Sử dụng templating cho tenant filtering
- Đặt alert thresholds hợp lý

### 4. Storage và Retention

- Prometheus retention: 15 ngày
- Grafana dashboards: backup thường xuyên
- High-resolution metrics: 1 giờ retention

## Troubleshooting

### Common Issues

1. **Metrics không xuất hiện**
   - Kiểm tra `/metrics` endpoint
   - Verify Prometheus configuration
   - Check network connectivity

2. **High cardinality warnings**
   - Review label usage
   - Implement label filtering
   - Consider metric aggregation

3. **Performance impact**
   - Monitor metrics collection overhead
   - Optimize scrape intervals
   - Use sampling for high-frequency metrics

### Debug Commands

```bash
# Check metrics endpoint
curl http://localhost:8080/metrics

# Prometheus query
curl 'http://localhost:9090/api/v1/query?query=up'

# Grafana API
curl -u admin:admin123 http://localhost:3000/api/dashboards/home
```

## Kết luận

Hệ thống monitoring này cung cấp:

- ✅ Comprehensive metrics collection
- ✅ Real-time dashboards
- ✅ Proactive alerting
- ✅ Performance tracking
- ✅ Business metrics visibility
- ✅ Infrastructure monitoring
- ✅ Easy maintenance và scaling

Monitoring giúp đảm bảo reliability, performance, và user experience của Blog API V3.