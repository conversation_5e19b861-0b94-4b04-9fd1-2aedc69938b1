# Module <PERSON><PERSON><PERSON> (Authentication)

Tài liệu này mô tả chi tiết về module <PERSON><PERSON><PERSON>, bao gồ<PERSON> các API endpoint, luồng hoạt động và các biểu đồ sequence để minh họa.

## <PERSON><PERSON><PERSON> l<PERSON>

- [1. <PERSON><PERSON><PERSON> ký người dùng](#1-đăng-ký-người-dùng---post-apicmsv1authregister)
- [2. <PERSON><PERSON><PERSON>h<PERSON>](#2-đăng-nhập---post-apicmsv1authlogin)
- [3. <PERSON><PERSON><PERSON> xuất](#3-đăng-xuất---post-apicmsv1authlogout)
- [4. <PERSON><PERSON><PERSON> mớ<PERSON>](#4-làm-mới-token---post-apicmsv1authrefresh)
- [5. <PERSON><PERSON><PERSON> thông tin Profile](#5-lấy-thông-tin-profile---get-apicmsv1authprofile)
- [6. <PERSON><PERSON><PERSON><PERSON> & Tenant](#6-quản-lý-session--tenant)
- [7. <PERSON><PERSON><PERSON><PERSON> <PERSON> & <PERSON><PERSON><PERSON>](#7-qu<PERSON>n-lý-email--mật-kh<PERSON>u)
- [8. Xác thực hai yếu tố (2FA)](#8-xác-thực-hai-yếu-tố-2fa)
- [9. Endpoints cho Development](#9-endpoints-cho-development)

---

## 1. Đăng ký người dùng - `POST /api/cms/v1/auth/register`

Endpoint này cho phép người dùng mới tạo tài khoản. Sau khi đăng ký, hệ thống sẽ gửi một email xác thực nếu được cấu hình.

### Mermaid Diagram (Chi tiết)

```mermaid
sequenceDiagram
    actor User
    participant API
    participant AuthHandler
    participant AuthService
    participant PasswordService
    participant UserRepository
    participant EmailVerificationService
    participant JWTService
    participant SessionRepository

    User->>+API: POST /register (payload)
    API->>+AuthHandler: Register(c)
    AuthHandler->>AuthHandler: BindJSON & Validate Request
    AuthHandler->>+AuthService: Register(req)

    AuthService->>+PasswordService: ValidatePassword(pass)
    PasswordService-->>-AuthService: Password valid

    AuthService->>+UserRepository: Exists(email)
    UserRepository-->>-AuthService: Email not exists

    opt Username provided
        AuthService->>+UserRepository: ExistsByUsername(username)
        UserRepository-->>-AuthService: Username not exists
    end

    AuthService->>+PasswordService: HashPassword(pass)
    PasswordService-->>-AuthService: hashedPassword

    AuthService->>+UserRepository: Create(user)
    UserRepository-->>-AuthService: createdUser

    alt Email Verification Required
        AuthService->>+EmailVerificationService: CreateVerificationToken(req)
        EmailVerificationService-->>-AuthService: Token created
        Note over EmailVerificationService: Send email via NotificationService
        AuthService-->>-AuthHandler: AuthResponse with RequiresEmailVerification
    else No Email Verification
        AuthService->>+SessionRepository: Create(session)
        SessionRepository-->>-AuthService: createdSession
        AuthService->>+JWTService: GenerateTokenPair(claims)
        JWTService-->>-AuthService: tokens
        AuthService->>+SessionRepository: Update session with token
        SessionRepository-->>-AuthService: Session updated
        AuthService->>+UserRepository: UpdateLastLogin(userID)
        UserRepository-->>-AuthService: Last login updated
        AuthService-->>-AuthHandler: AuthResponse with Tokens and User
    end

    AuthHandler-->>-API: HTTP 201 Created
    API-->>-User: Registration Response
```

### Luồng hoạt động (Flow)

1.  **User** gửi yêu cầu `POST` đến `/api/cms/v1/auth/register` với các thông tin như `email`, `password`, `firstName`, `lastName`.
2.  **API (Gin Router)** nhận yêu cầu và chuyển đến `AuthHandler.Register`.
3.  **AuthHandler** thực hiện validate dữ liệu đầu vào (`BindJSON`, `Validate`).
4.  **AuthHandler** gọi `AuthService.Register` để xử lý logic nghiệp vụ.
5.  **AuthService** gọi `PasswordService.ValidatePassword` để kiểm tra độ mạnh của mật khẩu.
6.  **AuthService** kiểm tra xem `email` và `username` (nếu có) đã tồn tại trong `UserRepository` hay chưa.
7.  Nếu chưa tồn tại, **AuthService** gọi `PasswordService.HashPassword` để băm mật khẩu.
8.  **AuthService** tạo một đối tượng `User` mới và lưu vào database thông qua `UserRepository.Create`.
9.  **Nếu hệ thống yêu cầu xác thực email**:
    -   `AuthService` gọi `EmailVerificationService.CreateVerificationToken` để tạo token và gửi email (thông qua `NotificationService`).
    -   Hệ thống trả về thông báo yêu cầu người dùng kiểm tra email.
10. **Nếu không yêu cầu xác thực email**:
    -   `AuthService` tạo một `Session` mới qua `SessionRepository`.
    -   `AuthService` gọi `JWTService.GenerateTokenPair` để tạo `access_token` và `refresh_token`.
    -   `AuthService` cập nhật `Session` với refresh token.
    -   `AuthService` cập nhật thời gian đăng nhập cuối cùng qua `UserRepository.UpdateLastLogin`.
    -   Hệ thống trả về tokens để người dùng có thể đăng nhập ngay lập tức.
11. **API** trả về mã trạng thái `201 Created` cùng với thông tin tương ứng.

---

## 2. Đăng nhập - `POST /api/cms/v1/auth/login`

Endpoint này xác thực thông tin đăng nhập của người dùng và trả về một cặp token (access và refresh) nếu thành công.

**🔑 Kiến trúc Multi-Tenant**: Login **KHÔNG yêu cầu tenant ID**. Users là global entities có thể thuộc nhiều tenant thông qua `tenant_memberships`. Hệ thống tự động chọn primary tenant và include tenant context vào JWT token.

### Mermaid Diagram

```mermaid
sequenceDiagram
    participant User
    participant API
    participant AuthHandler
    participant AuthService
    participant UserRepo
    participant TenantMembershipRepo
    participant JWTService

    User->>+API: POST /login (email, password)
    API->>+AuthHandler: Login(req)
    AuthHandler->>+AuthService: Login(serviceReq)
    AuthService->>+UserRepo: GetByEmailOrUsername(email)
    UserRepo-->>-AuthService: user data
    alt Credentials valid
        AuthService->>AuthService: VerifyPassword(plain_password, hashed_password)
        AuthService->>AuthService: Check account status (active, verified)
        AuthService->>+TenantMembershipRepo: GetActiveByUserID(userID)
        TenantMembershipRepo-->>-AuthService: tenant memberships
        Note right of AuthService: Auto-select primary tenant membership
        AuthService->>+JWTService: GenerateTokenPair(claims with CurrentTenantID)
        JWTService-->>-AuthService: tokens with tenant context
        AuthService-->>-AuthHandler: response with tokens
        AuthHandler-->>-API: 200 OK
        API-->>-User: access_token, refresh_token + current_tenant_id
    else Credentials invalid
        AuthService-->>-AuthHandler: Error Invalid Credentials
        AuthHandler-->>-API: 401 Unauthorized
        API-->>-User: Authentication Error
    end
```

### Luồng hoạt động (Flow)

1.  **User** gửi yêu cầu `POST` đến `/api/cms/v1/auth/login` với `email` và `password` (**KHÔNG cần tenant_id**).
2.  **API** chuyển yêu cầu đến `AuthHandler.Login`.
3.  **AuthHandler** validate dữ liệu và gọi `AuthService.Login`.
4.  **AuthService** tìm kiếm người dùng trong database bằng `email` thông qua `UserRepo`.
5.  Nếu người dùng tồn tại, **AuthService** so sánh `password` người dùng gửi lên với `password_hash` được lưu trong database.
6.  **AuthService** kiểm tra các trạng thái của tài khoản (đã được kích hoạt, đã xác thực email chưa).
7.  **AuthService** truy vấn `TenantMembershipRepo` để lấy danh sách tenant memberships đang hoạt động của user.
8.  **AuthService** tự động chọn primary tenant membership (hoặc membership đầu tiên) làm context hiện tại.
9.  **AuthService** gọi `JWTService` để tạo ra cặp `access_token` và `refresh_token` với `CurrentTenantID` và `TenantMemberships` trong claims.
10. **API** trả về tokens + `current_tenant_id` cho **User** với mã trạng thái `200 OK`.
11. Nếu thông tin không hợp lệ hoặc user không có active membership, **API** trả về lỗi `401 Unauthorized`.

---

## 3. Đăng xuất - `POST /api/cms/v1/auth/logout`

Endpoint này cho phép người dùng đăng xuất khỏi hệ thống. Về mặt kỹ thuật, nó sẽ vô hiệu hóa session hoặc token hiện tại.

### Mermaid Diagram

```mermaid
sequenceDiagram
    participant User
    participant API
    participant AuthHandler
    participant AuthService
    participant SessionRepo

    User->>+API: POST /logout (Header: Authorization: Bearer <token>)
    API->>+AuthHandler: Logout(c)
    AuthHandler->>AuthHandler: Get UserID and SessionID from JWT
    AuthHandler->>+AuthService: Logout(userID, sessionID)
    AuthService->>+SessionRepo: InvalidateSession(sessionID)
    SessionRepo-->>-AuthService: Session invalidated
    AuthService-->>-AuthHandler: success response
    AuthHandler-->>-API: 200 OK
    API-->>-User: Logout successful
```

### Luồng hoạt động (Flow)

1.  **User** gửi yêu cầu `POST` đến `/api/cms/v1/auth/logout`, đính kèm `access_token` trong header `Authorization`.
2.  Middleware xác thực JWT và lấy thông tin `userID` và `sessionID` từ token.
3.  **API** chuyển yêu cầu đến `AuthHandler.Logout`.
4.  **AuthHandler** gọi `AuthService.Logout` với `userID` và `sessionID`.
5.  **AuthService** gọi `SessionRepo` để vô hiệu hóa session tương ứng trong database.
6.  (Tùy chọn) `JWTService` có thể thêm token vào danh sách đen (blacklist) để ngăn việc tái sử dụng.
7.  **API** trả về `200 OK` để xác nhận đăng xuất thành công.

---

## 4. Làm mới Token - `POST /api/cms/v1/auth/refresh`

Khi `access_token` hết hạn, người dùng có thể sử dụng `refresh_token` để lấy một `access_token` mới mà không cần đăng nhập lại.

### Mermaid Diagram

```mermaid
sequenceDiagram
    participant User
    participant API
    participant AuthHandler
    participant AuthService
    participant JWTService

    User->>+API: POST /refresh (body: {refresh_token})
    API->>+AuthHandler: RefreshToken(req)
    AuthHandler->>+AuthService: RefreshToken(refreshToken)
    AuthService->>+JWTService: ValidateRefreshToken(refreshToken)
    alt Refresh token valid
        JWTService-->>-AuthService: claims
        AuthService->>+JWTService: GenerateAccessToken(claims)
        JWTService-->>-AuthService: new_access_token
        AuthService-->>-AuthHandler: response with new_access_token
        AuthHandler-->>-API: 200 OK
        API-->>-User: new access_token
    else Refresh token invalid
        JWTService-->>-AuthService: Error Invalid Token
        AuthService-->>-AuthHandler: Error
        AuthHandler-->>-API: 401 Unauthorized
        API-->>-User: Authentication Error
    end
```

### Luồng hoạt động (Flow)

1.  **User** gửi yêu cầu `POST` đến `/api/cms/v1/auth/refresh` với `refresh_token` trong body.
2.  **API** chuyển yêu cầu đến `AuthHandler.RefreshToken`.
3.  **AuthHandler** gọi `AuthService.RefreshToken`.
4.  **AuthService** sử dụng `JWTService` để xác thực `refresh_token`.
5.  `JWTService` kiểm tra xem token có hợp lệ, chưa hết hạn và không nằm trong danh sách đen hay không.
6.  Nếu hợp lệ, `JWTService` tạo ra một `access_token` mới dựa trên thông tin (claims) từ `refresh_token`.
7.  **API** trả về `access_token` mới cho **User**.
8.  Nếu `refresh_token` không hợp lệ, **API** trả về lỗi `401 Unauthorized`, yêu cầu người dùng phải đăng nhập lại.

---

## 5. Lấy thông tin Profile - `GET /api/cms/v1/auth/profile`

Endpoint này trả về thông tin chi tiết của người dùng đang đăng nhập.

### Mermaid Diagram

```mermaid
sequenceDiagram
    participant User
    participant API
    participant AuthHandler
    participant AuthService
    participant UserRepo

    User->>+API: GET /profile (Header: Authorization: Bearer <token>)
    API->>+AuthHandler: GetProfile(c)
    AuthHandler->>AuthHandler: Get UserID from JWT
    AuthHandler->>+AuthService: GetProfile(userID)
    AuthService->>+UserRepo: GetByID(userID)
    UserRepo-->>-AuthService: user detailed data
    AuthService-->>-AuthHandler: user profile response
    AuthHandler-->>-API: 200 OK
    API-->>-User: user profile data
```

### Luồng hoạt động (Flow)

1.  **User** gửi yêu cầu `GET` đến `/api/cms/v1/auth/profile` với `access_token` trong header.
2.  Middleware xác thực JWT và trích xuất `userID`.
3.  **API** chuyển yêu cầu đến `AuthHandler.GetProfile`.
4.  **AuthHandler** gọi `AuthService.GetProfile` với `userID`.
5.  **AuthService** truy vấn `UserRepo` để lấy thông tin chi tiết của người dùng từ database.
6.  **API** trả về thông tin profile của người dùng (ví dụ: id, email, tên, vai trò, v.v.) dưới dạng JSON.

---

## 6. Quản lý Session & Tenant

Các endpoint để quản lý phiên đăng nhập và ngữ cảnh tenant của người dùng.

### 6.1. Lấy danh sách Session - `GET /api/cms/v1/auth/sessions`

Lấy tất cả các phiên đăng nhập đang hoạt động của người dùng.

```mermaid
sequenceDiagram
    User->>+API: GET /sessions
    API->>+AuthHandler: GetActiveSessions(c)
    AuthHandler->>AuthHandler: Get UserID from JWT
    AuthHandler->>+AuthService: GetActiveSessions(userID)
    AuthService->>+SessionRepo: GetActiveSessionsByUser(userID)
    SessionRepo-->>-AuthService: sessions[]
    AuthService-->>-AuthHandler: sessions response
    AuthHandler-->>-API: 200 OK
    API-->>-User: sessions list
```

### 6.2. Hủy Session - `DELETE /api/cms/v1/auth/sessions/{sessionId}`

Hủy một phiên đăng nhập cụ thể, buộc người dùng trên thiết bị đó phải đăng nhập lại.

```mermaid
sequenceDiagram
    User->>+API: DELETE /sessions/{sessionId}
    API->>+AuthHandler: RevokeSession(c)
    AuthHandler->>AuthHandler: Get UserID and SessionID
    AuthHandler->>+AuthService: RevokeSession(userID, sessionID)
    AuthService->>+SessionRepo: GetByID(sessionID)
    SessionRepo-->>-AuthService: session verify ownership
    AuthService->>+SessionRepo: InvalidateSession(sessionID)
    SessionRepo-->>-AuthService: Session invalidated
    AuthService-->>-AuthHandler: session revoked response
    AuthHandler-->>-API: 200 OK
    API-->>-User: Session revoked
```

### 6.3. Đăng xuất tất cả thiết bị - `POST /api/cms/v1/auth/logout-all`

Đăng xuất người dùng khỏi tất cả các thiết bị bằng cách vô hiệu hóa tất cả các session.

```mermaid
sequenceDiagram
    User->>+API: POST /logout-all
    API->>+AuthHandler: LogoutAllDevices(c)
    AuthHandler->>+AuthService: LogoutAllDevices(userID)
    AuthService->>+SessionRepo: InvalidateAllUserSessions(userID)
    SessionRepo-->>-AuthService: Session invalidated
    AuthService-->>-AuthHandler: sessions terminated response
    AuthHandler-->>-API: 200 OK
    API-->>-User: Logout successful
```

### 6.4. Chuyển Tenant - `POST /api/cms/v1/auth/switch-tenant`

Cho phép người dùng thuộc nhiều tenant chuyển đổi ngữ cảnh làm việc giữa các tenant.

**📋 Request Body:**
```json
{
  "tenant_id": 123
}
```

**🔄 Response:**
```json
{
  "status": "success",
  "data": {
    "access_token": "eyJ...",
    "refresh_token": "eyJ...",
    "current_tenant_id": 123,
    "expires_at": "2024-01-20T10:30:00Z"
  }
}
```

```mermaid
sequenceDiagram
    User->>+API: POST /switch-tenant (body: {tenant_id})
    API->>+AuthHandler: SwitchTenant(c)
    AuthHandler->>AuthHandler: Get UserID and validate request
    AuthHandler->>+TenantMembershipRepo: VerifyUserAccess(userID, tenantID)
    TenantMembershipRepo-->>-AuthHandler: membership verified
    AuthHandler->>+JWTService: GenerateTokenPair(newClaims)
    Note right of AuthHandler: New claims contain CurrentTenantID
    JWTService-->>-AuthHandler: new tokens
    AuthHandler-->>-API: 200 OK
    API-->>-User: tokens and current_tenant_id
```

---

## 7. Quản lý Email & Mật khẩu

Các endpoint liên quan đến việc xác thực email và khôi phục mật khẩu.

### 7.1. Xác thực Email - `POST /api/cms/v1/auth/verify-email`

Xác thực email của người dùng bằng token được gửi qua email.

```mermaid
sequenceDiagram
    User->>+API: POST /verify-email (body: {token})
    API->>+EmailHandler: VerifyEmail(c)
    EmailHandler->>+EmailVerificationService: VerifyEmail(token)
    EmailVerificationService->>+TokenRepo: FindByToken(token)
    TokenRepo-->>-EmailVerificationService: tokenData
    EmailVerificationService->>+UserRepo: Update user EmailVerified to true
    UserRepo-->>-EmailVerificationService: Email verified
    EmailVerificationService->>+TokenRepo: InvalidateToken(token)
    TokenRepo-->>-EmailVerificationService: Token invalidated
    EmailVerificationService-->>-EmailHandler: success response
    EmailHandler-->>-API: 200 OK
    API-->>-User: Email verified successfully
```

### 7.2. Gửi lại Email xác thực - `POST /api/cms/v1/auth/resend-verification`

Gửi lại email xác thực nếu người dùng không nhận được hoặc token hết hạn.

```mermaid
sequenceDiagram
    User->>+API: POST /resend-verification (body: {email})
    API->>+EmailHandler: ResendVerificationEmail(c)
    EmailHandler->>+EmailVerificationService: ResendVerificationEmail(email)
    EmailVerificationService->>+UserRepo: GetByEmail(email)
    UserRepo-->>-EmailVerificationService: user
    EmailVerificationService->>EmailVerificationService: Create new token & send email
    EmailVerificationService-->>-EmailHandler: success response
    EmailHandler-->>-API: 200 OK
    API-->>-User: Verification email sent
```

### 7.3. Quên mật khẩu - `POST /api/cms/v1/auth/forgot-password`

Bắt đầu quy trình đặt lại mật khẩu. Hệ thống sẽ gửi một email chứa link đặt lại mật khẩu.

```mermaid
sequenceDiagram
    User->>+API: POST /forgot-password (body: {email})
    API->>+EmailHandler: ForgotPassword(c)
    EmailHandler->>+PasswordResetService: RequestPasswordReset(email)
    PasswordResetService->>+UserRepo: GetByEmail(email)
    UserRepo-->>-PasswordResetService: user
    PasswordResetService->>+PasswordResetRepo: Create(token)
    PasswordResetRepo-->>-PasswordResetService: resetToken
    PasswordResetService->>+NotificationService: SendResetPasswordEmail(user, token)
    NotificationService-->>-PasswordResetService: Email sent
    PasswordResetService-->>-EmailHandler: Request processed
    EmailHandler-->>-API: 200 OK
    API-->>-User: Password reset email sent
```

### 7.4. Đặt lại mật khẩu - `POST /api/cms/v1/auth/reset-password`

Hoàn tất việc đặt lại mật khẩu bằng token và mật khẩu mới.

```mermaid
sequenceDiagram
    User->>+API: POST /reset-password (body: {token, new_password})
    API->>+EmailHandler: ResetPassword(c)
    EmailHandler->>+PasswordResetService: ResetPassword(token, newPassword)
    PasswordResetService->>+PasswordResetRepo: FindByToken(token)
    PasswordResetRepo-->>-PasswordResetService: tokenData
    PasswordResetService->>+PasswordService: HashPassword(newPassword)
    PasswordService-->>-PasswordResetService: hashedPassword
    PasswordResetService->>+UserRepo: UpdatePassword(userID, hashedPassword)
    UserRepo-->>-PasswordResetService: Password updated
    PasswordResetService->>+PasswordResetRepo: InvalidateToken(token)
    PasswordResetRepo-->>-PasswordResetService: Token invalidated
    PasswordResetService-->>-EmailHandler: Request processed
    EmailHandler-->>-API: 200 OK
    API-->>-User: Password has been reset
```

---

## 8. Xác thực hai yếu tố (2FA)

Các endpoint để quản lý xác thực hai yếu tố (Two-Factor Authentication).

### 8.1. Kích hoạt 2FA - `POST /api/cms/v1/auth/2fa/enable`

Bắt đầu quá trình kích hoạt 2FA, trả về secret key và QR code cho người dùng.

```mermaid
sequenceDiagram
    User->>+API: POST /2fa/enable
    API->>+TwoFactorHandler: EnableTwoFactor(c)
    TwoFactorHandler->>+AuthService: EnableTwoFactor(userID)
    AuthService->>AuthService: GenerateTOTPSecret()
    AuthService->>+UserRepo: Update(user with temp secret)
    UserRepo-->>-AuthService: User updated
    AuthService-->>-TwoFactorHandler: secret and QR code response
    TwoFactorHandler-->>-API: 200 OK
    API-->>-User: secret and QR code data
```

### 8.2. Xác minh mã 2FA - `POST /api/cms/v1/auth/2fa/verify`

Xác minh mã TOTP từ ứng dụng authenticator. Dùng để hoàn tất việc bật/tắt 2FA.

```mermaid
sequenceDiagram
    User->>+API: POST /2fa/verify (body: {code})
    API->>+TwoFactorHandler: VerifyTwoFactor(c)
    TwoFactorHandler->>+AuthService: VerifyTwoFactor(userID, code)
    AuthService->>+UserRepo: GetTwoFactorSecret(userID)
    UserRepo-->>-AuthService: secret
    AuthService->>AuthService: VerifyTOTPCode(secret, code)
    alt Code valid
        AuthService->>+UserRepo: Update user TwoFactorEnabled to true
        UserRepo-->>-AuthService: User updated
        AuthService-->>-TwoFactorHandler: verified response
    else Code invalid
        AuthService-->>-TwoFactorHandler: Error: Invalid code
    end
    TwoFactorHandler-->>-API: 200 OK or 400 Bad Request
    API-->>-User: Verification result
```

### 8.3. Hoàn tất đăng nhập 2FA - `POST /api/cms/v1/auth/2fa/complete-login`

Sau khi đăng nhập với email/mật khẩu, nếu tài khoản yêu cầu 2FA, người dùng sẽ gọi endpoint này để cung cấp mã 2FA và nhận token.

```mermaid
sequenceDiagram
    User->>+API: POST /2fa/complete-login (body: {email, password, two_factor_code})
    API->>+TwoFactorHandler: CompleteLogin(c)
    TwoFactorHandler->>+AuthService: Login(req)
    Note right of TwoFactorHandler: AuthService.Login called with two_factor_code
    AuthService->>AuthService: Login logic validation
    AuthService->>AuthService: VerifyTwoFactor(userID, code)
    alt Code valid
        AuthService->>+JWTService: GenerateTokenPair(claims)
        JWTService-->>-AuthService: tokens
        AuthService-->>-TwoFactorHandler: response with tokens
        TwoFactorHandler-->>-API: 200 OK
        API-->>-User: access_token and refresh_token
    else Code invalid
        AuthService-->>-TwoFactorHandler: Error: Invalid code
        TwoFactorHandler-->>-API: 401 Unauthorized
        API-->>-User: Authentication Error
    end
```

---

## 9. Endpoints cho Development

Các endpoint này chỉ dành cho môi trường phát triển và kiểm thử.

-   **`POST /api/cms/v1/auth/test-email`**: Gửi một email mẫu để kiểm tra dịch vụ `NotificationService`.
-   **`POST /api/cms/v1/auth/test-smtp`**: Kiểm tra kết nối đến máy chủ SMTP đã được cấu hình.