# Module Thông báo (Notification)

Tà<PERSON> liệu này mô tả chi tiết về module <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>, chị<PERSON> trách nhiệm g<PERSON>i các thông báo đa kênh (email, push, SMS, v.v.) và quản lý các mẫu (template) thông báo.

## <PERSON><PERSON><PERSON> lụ<PERSON>

- [1. <PERSON><PERSON><PERSON><PERSON> lý <PERSON>hông báo (Notifications)](#1-quản-lý-thông-báo-notifications)
  - [1.1. <PERSON><PERSON><PERSON> da<PERSON> sách thông báo - `GET /notifications`](#11-lấy-danh-sách-thông-báo---get-notifications)
  - [1.2. <PERSON><PERSON><PERSON> thông báo mới - `POST /notifications`](#12-tạo-thông-báo-mới---post-notifications)
  - [1.3. <PERSON><PERSON><PERSON> thông báo - `POST /notifications/{id}/send`](#13-gửi-thông-báo---post-notificationsidsend)
- [2. <PERSON><PERSON><PERSON><PERSON> lý Mẫu (Templates)](#2-qu<PERSON>n-lý-mẫu-templates)
  - [2.1. <PERSON><PERSON><PERSON> danh sách mẫu - `GET /templates`](#21-lấy-danh-sách-mẫu---get-templates)
  - [2.2. Tạo mẫu mới - `POST /templates`](#22-tạo-mẫu-mới---post-templates)

---

## 1. Quản lý Thông báo (Notifications)

Các API để tạo, gửi và quản lý lịch sử các thông báo.

### 1.1. Lấy danh sách thông báo - `GET /notifications`

Lấy danh sách các thông báo đã được tạo, hỗ trợ lọc theo trạng thái, kênh, v.v.

```mermaid
sequenceDiagram
    User->>+API: GET /notifications?channel=email
    API->>+NotificationHandler: ListNotifications(c)
    NotificationHandler->>NotificationHandler: Parse query params
    NotificationHandler->>+NotificationService: ListNotifications(filter)
    NotificationService->>+NotificationRepo: Find(filter)
    NotificationRepo-->>-NotificationService: notifications[], total
    NotificationService-->>-NotificationHandler: response
    NotificationHandler-->>-API: 200 OK
    API-->>-User: {notifications: [...], pagination: {...}}
```

### 1.2. Tạo thông báo mới - `POST /notifications`

Tạo một yêu cầu gửi thông báo mới. Thông báo có thể được gửi ngay lập tức hoặc được đưa vào hàng đợi.

```mermaid
sequenceDiagram
    System->>+API: POST /notifications (body: {type, channel, recipients, ...})
    API->>+NotificationHandler: CreateNotification(c)
    NotificationHandler->>NotificationHandler: Validate input & get TenantID
    NotificationHandler->>+NotificationService: CreateNotification(req)
    NotificationService->>+NotificationRepo: Create(notification)
    NotificationRepo-->>-NotificationService: createdNotification
    NotificationService-->>-NotificationHandler: createdNotification
    NotificationHandler-->>-API: 201 Created
    API-->>-System: {id, status: "pending", ...}
```

### 1.3. Gửi thông báo - `POST /notifications/{id}/send`

Kích hoạt việc gửi một thông báo đang ở trạng thái `pending`.

```mermaid
sequenceDiagram
    System->>+API: POST /notifications/123/send
    API->>+NotificationHandler: SendNotification(c)
    NotificationHandler->>+NotificationService: SendNotification(tenantID, 123)
    NotificationService->>+DeliveryService: Deliver(notification)
    DeliveryService->>+EmailProvider: Send(email)
    EmailProvider-->>-DeliveryService: (ok)
    DeliveryService->>+NotificationRepo: UpdateStatus(sent)
    NotificationRepo-->>-DeliveryService: (ok)
    DeliveryService-->>-NotificationService: (ok)
    NotificationService-->>-NotificationHandler: (ok)
    NotificationHandler-->>-API: 200 OK
    API-->>-System: {message: "Notification sent"}
```

---

## 2. Quản lý Mẫu (Templates)

Các API để tạo và quản lý các mẫu nội dung cho thông báo, giúp tái sử dụng và dễ dàng chỉnh sửa.

### 2.1. Lấy danh sách mẫu - `GET /templates`

```mermaid
sequenceDiagram
    Admin->>+API: GET /templates
    API->>+TemplateHandler: ListTemplates(c)
    TemplateHandler->>+TemplateService: ListTemplates(filter)
    TemplateService->>+TemplateRepo: Find(filter)
    TemplateRepo-->>-TemplateService: templates[], total
    TemplateService-->>-TemplateHandler: response
    TemplateHandler-->>-API: 200 OK
    API-->>-Admin: {templates: [...], pagination: {...}}
```

### 2.2. Tạo mẫu mới - `POST /templates`

```mermaid
sequenceDiagram
    Admin->>+API: POST /templates (body: {name, content, ...})
    API->>+TemplateHandler: CreateTemplate(c)
    TemplateHandler->>+TemplateService: CreateTemplate(req)
    TemplateService->>+TemplateRepo: Create(template)
    TemplateRepo-->>-TemplateService: createdTemplate
    TemplateService-->>-TemplateHandler: createdTemplate
    TemplateHandler-->>-API: 201 Created
    API-->>-Admin: {id, name, ...}
```
