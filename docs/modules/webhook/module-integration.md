# Module Integration với Webhook System

## Quick Start Guide

### 1. Implement WebhookProvider Interface

Mỗi module cần implement interface này để register với webhook system:

```go
type WebhookProvider interface {
    // Module name
    GetModuleName() string
    
    // List of supported events
    GetSupportedEvents() []WebhookEventType
    
    // Validate event payload
    ValidateEventPayload(eventType string, payload interface{}) error
    
    // Transform internal data to webhook payload
    TransformEventPayload(eventType string, data interface{}) (interface{}, error)
}
```

### 2. Register Module với Webhook System

Trong module initialization:

```go
func InitializeModule(webhookService *webhook.WebhookService) {
    provider := &BlogWebhookProvider{}
    webhookService.RegisterProvider(provider)
}
```

## Module-Specific Integration Examples

### Blog Module

#### Event Types
```go
const (
    EventBlogPostCreated   = "blog.post.created"
    EventBlogPostPublished = "blog.post.published"
    EventBlogPostUpdated   = "blog.post.updated"
    EventBlogPostDeleted   = "blog.post.deleted"
    EventBlogPostScheduled = "blog.post.scheduled"
    
    EventBlogCommentCreated  = "blog.comment.created"
    EventBlogCommentApproved = "blog.comment.approved"
    EventBlogCommentRejected = "blog.comment.rejected"
    EventBlogCommentDeleted  = "blog.comment.deleted"
)
```

#### Payload Examples

**blog.post.published**
```json
{
    "event_id": "evt_123456",
    "event_type": "blog.post.published",
    "timestamp": "2024-01-15T10:30:00Z",
    "tenant_id": 123,
    "module": "blog",
    "data": {
        "post": {
            "id": 456,
            "title": "Introduction to Webhooks",
            "slug": "introduction-to-webhooks",
            "excerpt": "Learn how to integrate webhooks...",
            "author": {
                "id": 789,
                "name": "John Doe",
                "email": "<EMAIL>"
            },
            "category": {
                "id": 12,
                "name": "Technology",
                "slug": "technology"
            },
            "tags": ["webhooks", "api", "integration"],
            "status": "published",
            "visibility": "public",
            "published_at": "2024-01-15T10:30:00Z",
            "url": "https://example.com/blog/introduction-to-webhooks"
        },
        "changes": {
            "status": {
                "from": "draft",
                "to": "published"
            }
        }
    }
}
```

### User Module

#### Event Types
```go
const (
    EventUserCreated         = "user.created"
    EventUserUpdated         = "user.updated"
    EventUserDeleted         = "user.deleted"
    EventUserActivated       = "user.activated"
    EventUserDeactivated     = "user.deactivated"
    EventUserPasswordChanged = "user.password_changed"
    EventUserEmailVerified   = "user.email_verified"
    EventUserLoggedIn        = "user.logged_in"
    EventUserLoggedOut       = "user.logged_out"
)
```

#### Integration Code
```go
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
    user, err := s.repo.CreateUser(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // Emit webhook event
    s.emitUserEvent(ctx, EventUserCreated, user, nil)
    
    return user, nil
}

func (s *UserService) emitUserEvent(ctx context.Context, eventType string, user *User, changes map[string]interface{}) {
    payload := map[string]interface{}{
        "user": map[string]interface{}{
            "id":           user.ID,
            "email":        user.Email,
            "username":     user.Username,
            "status":       user.Status,
            "roles":        user.GetRoles(),
            "created_at":   user.CreatedAt,
            "verified":     user.EmailVerifiedAt != nil,
        },
    }
    
    if changes != nil {
        payload["changes"] = changes
    }
    
    event := &webhook.WebhookEvent{
        TenantID:   user.TenantID,
        ModuleName: "user",
        EventType:  eventType,
        EventID:    fmt.Sprintf("user_%d_%s", user.ID, uuid.New()),
        Payload:    payload,
    }
    
    s.webhookService.EmitEvent(ctx, event)
}
```

### Media Module

#### Event Types
```go
const (
    EventMediaUploaded   = "media.uploaded"
    EventMediaProcessed  = "media.processed"
    EventMediaDeleted    = "media.deleted"
    EventMediaFailed     = "media.failed"
    EventMediaOptimized  = "media.optimized"
)
```

#### Payload Example
```json
{
    "event_type": "media.processed",
    "data": {
        "file": {
            "id": 789,
            "filename": "product-image.jpg",
            "mime_type": "image/jpeg",
            "size": 245678,
            "dimensions": {
                "width": 1200,
                "height": 800
            },
            "url": "https://cdn.example.com/media/789/product-image.jpg",
            "thumbnails": [
                {
                    "size": "small",
                    "url": "https://cdn.example.com/media/789/thumb-small.jpg",
                    "dimensions": {"width": 150, "height": 100}
                },
                {
                    "size": "medium",
                    "url": "https://cdn.example.com/media/789/thumb-medium.jpg",
                    "dimensions": {"width": 300, "height": 200}
                }
            ],
            "metadata": {
                "exif": {...},
                "duration": null
            }
        },
        "processing": {
            "duration_ms": 1234,
            "operations": ["resize", "optimize", "generate_thumbnails"]
        }
    }
}
```

### E-commerce Module (Example)

#### Event Types
```go
const (
    EventOrderCreated    = "order.created"
    EventOrderPaid       = "order.paid"
    EventOrderShipped    = "order.shipped"
    EventOrderDelivered  = "order.delivered"
    EventOrderCanceled   = "order.canceled"
    EventOrderRefunded   = "order.refunded"
    
    EventProductCreated      = "product.created"
    EventProductUpdated      = "product.updated"
    EventProductOutOfStock   = "product.out_of_stock"
    EventProductBackInStock  = "product.back_in_stock"
    
    EventCartAbandoned = "cart.abandoned"
)
```

## Best Practices cho Module Integration

### 1. Event Naming Convention

Format: `{module}.{resource}.{action}`

Examples:
- `blog.post.published` ✓
- `user.profile.updated` ✓
- `media.file.uploaded` ✓
- `postPublished` ✗ (missing module prefix)
- `blog_post_published` ✗ (use dots, not underscores)

### 2. Payload Structure

Always include:
- Resource data (current state)
- Changes (if update event)
- Metadata (timestamps, versions)
- Related resources (minimal)

```go
type StandardEventPayload struct {
    Resource interface{}            `json:"resource"`
    Changes  map[string]Change     `json:"changes,omitempty"`
    Metadata EventMetadata         `json:"metadata"`
    Related  map[string]interface{} `json:"related,omitempty"`
}

type Change struct {
    From interface{} `json:"from"`
    To   interface{} `json:"to"`
}

type EventMetadata struct {
    Version   string    `json:"version"`
    Timestamp time.Time `json:"timestamp"`
    Source    string    `json:"source"`
}
```

### 3. Error Handling

```go
func (s *Service) emitEvent(ctx context.Context, eventType string, data interface{}) {
    // Don't let webhook failures affect main operation
    go func() {
        ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()
        
        if err := s.webhookService.EmitEvent(ctx, event); err != nil {
            s.logger.Error("Failed to emit webhook", 
                "event_type", eventType,
                "error", err)
            
            // Optionally, queue for retry or alert
            s.metrics.IncrementWebhookFailure(eventType)
        }
    }()
}
```

### 4. Testing Webhook Events

```go
func TestBlogPostPublishedWebhook(t *testing.T) {
    // Setup
    mockWebhook := &MockWebhookService{}
    service := NewBlogService(mockWebhook)
    
    // Expect webhook call
    mockWebhook.On("EmitEvent", mock.MatchedBy(func(event *WebhookEvent) bool {
        return event.EventType == "blog.post.published" &&
               event.ModuleName == "blog"
    })).Return(nil)
    
    // Execute
    err := service.PublishPost(ctx, postID)
    
    // Verify
    assert.NoError(t, err)
    mockWebhook.AssertExpectations(t)
}
```

### 5. Webhook Event Documentation

Create a markdown file for each module's webhook events:

```markdown
# Blog Module Webhook Events

## blog.post.published

Triggered when a blog post is published.

### Payload

| Field | Type | Description |
|-------|------|-------------|
| post.id | integer | Unique post ID |
| post.title | string | Post title |
| post.slug | string | URL slug |
| post.author | object | Author details |
| post.published_at | string | ISO 8601 timestamp |

### Example

\```json
{
    "event_type": "blog.post.published",
    "data": {
        "post": {
            "id": 123,
            "title": "Hello World",
            ...
        }
    }
}
\```
```

## Module Registration Example

```go
// internal/modules/blog/webhook_provider.go
package blog

import (
    "github.com/tranthanhloi/wn-api-v3/internal/webhook"
)

type BlogWebhookProvider struct {
    service *BlogService
}

func NewBlogWebhookProvider(service *BlogService) *BlogWebhookProvider {
    return &BlogWebhookProvider{service: service}
}

func (p *BlogWebhookProvider) GetModuleName() string {
    return "blog"
}

func (p *BlogWebhookProvider) GetSupportedEvents() []webhook.EventType {
    return []webhook.EventType{
        {
            Name:        "blog.post.created",
            Description: "Triggered when a new blog post is created",
            Schema:      PostEventSchema,
        },
        {
            Name:        "blog.post.published",
            Description: "Triggered when a blog post is published",
            Schema:      PostEventSchema,
        },
        // ... more events
    }
}

func (p *BlogWebhookProvider) ValidateEventPayload(eventType string, payload interface{}) error {
    switch eventType {
    case "blog.post.created", "blog.post.published":
        return p.validatePostPayload(payload)
    case "blog.comment.created":
        return p.validateCommentPayload(payload)
    default:
        return fmt.Errorf("unknown event type: %s", eventType)
    }
}

// Module initialization
func InitBlogModule(db *gorm.DB, webhookService *webhook.WebhookService) {
    repo := repositories.NewBlogRepository(db)
    service := services.NewBlogService(repo)
    
    // Register webhook provider
    provider := NewBlogWebhookProvider(service)
    webhookService.RegisterProvider(provider)
    
    // Inject webhook service into blog service
    service.SetWebhookService(webhookService)
}
```

## Webhook Configuration UI

Each module should provide webhook configuration in admin panel:

```typescript
// Frontend webhook configuration component
interface WebhookConfig {
    endpoint: string;
    events: string[];
    headers?: Record<string, string>;
    secret?: string;
}

const BlogWebhookConfig: React.FC = () => {
    const [config, setConfig] = useState<WebhookConfig>();
    const availableEvents = [
        'blog.post.created',
        'blog.post.published',
        'blog.post.updated',
        'blog.post.deleted',
        'blog.comment.created',
    ];
    
    return (
        <div>
            <h3>Blog Webhook Configuration</h3>
            <EventSelector 
                events={availableEvents}
                selected={config.events}
                onChange={(events) => setConfig({...config, events})}
            />
            <WebhookTester 
                endpoint={config.endpoint}
                events={config.events}
            />
        </div>
    );
};
```