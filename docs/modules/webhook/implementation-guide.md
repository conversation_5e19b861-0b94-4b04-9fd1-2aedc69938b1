# Webhook Implementation Guide

## 1. Webhook Service Implementation

### Core Webhook Service

```go
package webhook

import (
    "context"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "encoding/json"
    "time"
    
    "github.com/google/uuid"
    "gorm.io/gorm"
)

type WebhookService struct {
    db         *gorm.DB
    queue      Queue
    httpClient HTTPClient
    logger     Logger
    config     *Config
}

type Config struct {
    MaxRetries       int
    RetryBackoff     time.Duration
    RequestTimeout   time.Duration
    MaxPayloadSize   int
    WorkerPoolSize   int
    QueueBufferSize  int
}

// EmitEvent creates a new webhook event and queues deliveries
func (s *WebhookService) EmitEvent(ctx context.Context, event *WebhookEvent) error {
    // Validate event
    if err := s.validateEvent(event); err != nil {
        return fmt.Errorf("invalid event: %w", err)
    }
    
    // Store event
    if err := s.storeEvent(ctx, event); err != nil {
        return fmt.Errorf("failed to store event: %w", err)
    }
    
    // Find matching endpoints
    endpoints, err := s.findMatchingEndpoints(ctx, event)
    if err != nil {
        return fmt.Errorf("failed to find endpoints: %w", err)
    }
    
    // Queue deliveries
    for _, endpoint := range endpoints {
        delivery := &WebhookDelivery{
            TenantID:       event.TenantID,
            EndpointID:     endpoint.ID,
            EventID:        event.ID,
            DeliveryStatus: DeliveryStatusPending,
        }
        
        if err := s.queue.Push(ctx, delivery); err != nil {
            s.logger.Error("Failed to queue delivery", 
                "endpoint_id", endpoint.ID, 
                "error", err)
        }
    }
    
    return nil
}
```

### Webhook Delivery Worker

```go
type DeliveryWorker struct {
    service    *WebhookService
    workerID   string
    shutdownCh chan struct{}
}

func (w *DeliveryWorker) Start(ctx context.Context) {
    w.workerID = uuid.New().String()
    
    for {
        select {
        case <-ctx.Done():
            return
        case <-w.shutdownCh:
            return
        default:
            w.processNextDelivery(ctx)
        }
    }
}

func (w *DeliveryWorker) processNextDelivery(ctx context.Context) {
    delivery, err := w.service.queue.Pop(ctx)
    if err != nil {
        if err != ErrQueueEmpty {
            w.service.logger.Error("Failed to pop from queue", "error", err)
        }
        time.Sleep(100 * time.Millisecond)
        return
    }
    
    // Process delivery
    if err := w.deliverWebhook(ctx, delivery); err != nil {
        w.handleDeliveryError(ctx, delivery, err)
    }
}

func (w *DeliveryWorker) deliverWebhook(ctx context.Context, delivery *WebhookDelivery) error {
    // Load endpoint and event
    endpoint, event, err := w.loadDeliveryData(ctx, delivery)
    if err != nil {
        return err
    }
    
    // Check circuit breaker
    if endpoint.CircuitStatus == CircuitStatusOpen {
        if time.Since(endpoint.UpdatedAt) < time.Duration(endpoint.RecoveryTimeout)*time.Second {
            return ErrCircuitOpen
        }
        // Try half-open
        endpoint.CircuitStatus = CircuitStatusHalfOpen
    }
    
    // Prepare request
    payload, err := json.Marshal(event.Payload)
    if err != nil {
        return err
    }
    
    signature := w.generateSignature(payload, endpoint.Secret)
    
    headers := map[string]string{
        "Content-Type":         "application/json",
        "X-Webhook-Signature":  signature,
        "X-Webhook-Event":      event.EventType,
        "X-Webhook-Delivery":   delivery.ID,
        "X-Webhook-Timestamp":  time.Now().Format(time.RFC3339),
    }
    
    // Merge custom headers
    for k, v := range endpoint.Headers {
        headers[k] = v.(string)
    }
    
    // Send request
    resp, err := w.service.httpClient.PostWithTimeout(
        ctx,
        endpoint.URL,
        payload,
        headers,
        time.Duration(endpoint.TimeoutSeconds)*time.Second,
    )
    
    // Record delivery result
    delivery.ResponseStatus = resp.StatusCode
    delivery.ResponseHeaders = resp.Headers
    delivery.ResponseBody = resp.Body
    delivery.DeliveredAt = time.Now()
    
    if err != nil || resp.StatusCode >= 400 {
        delivery.DeliveryStatus = DeliveryStatusFailed
        w.updateCircuitBreaker(endpoint, false)
        return fmt.Errorf("delivery failed: %w", err)
    }
    
    delivery.DeliveryStatus = DeliveryStatusSuccess
    w.updateCircuitBreaker(endpoint, true)
    
    return w.service.updateDelivery(ctx, delivery)
}
```

### Retry Logic Implementation

```go
type RetryStrategy interface {
    NextDelay(attempt int) time.Duration
    ShouldRetry(attempt int, err error) bool
}

type ExponentialBackoffStrategy struct {
    BaseDelay   time.Duration
    MaxDelay    time.Duration
    Multiplier  float64
    MaxAttempts int
}

func (s *ExponentialBackoffStrategy) NextDelay(attempt int) time.Duration {
    delay := float64(s.BaseDelay) * math.Pow(s.Multiplier, float64(attempt-1))
    if delay > float64(s.MaxDelay) {
        return s.MaxDelay
    }
    
    // Add jitter to prevent thundering herd
    jitter := rand.Float64() * 0.1 * delay
    return time.Duration(delay + jitter)
}

func (s *ExponentialBackoffStrategy) ShouldRetry(attempt int, err error) bool {
    if attempt >= s.MaxAttempts {
        return false
    }
    
    // Don't retry on permanent errors
    if IsPermanentError(err) {
        return false
    }
    
    return true
}

func (w *DeliveryWorker) handleDeliveryError(ctx context.Context, delivery *WebhookDelivery, err error) {
    delivery.AttemptNumber++
    
    strategy := &ExponentialBackoffStrategy{
        BaseDelay:   time.Second,
        MaxDelay:    5 * time.Minute,
        Multiplier:  2.0,
        MaxAttempts: delivery.Endpoint.MaxRetries,
    }
    
    if strategy.ShouldRetry(delivery.AttemptNumber, err) {
        delay := strategy.NextDelay(delivery.AttemptNumber)
        delivery.DeliveryStatus = DeliveryStatusRetrying
        delivery.NextRetryAt = time.Now().Add(delay)
        
        // Re-queue for retry
        w.service.queue.PushDelayed(ctx, delivery, delay)
    } else {
        delivery.DeliveryStatus = DeliveryStatusFailed
        w.recordFinalFailure(ctx, delivery, err)
    }
}
```

## 2. Module Integration Examples

### Blog Module Integration

```go
package blog

type BlogWebhookProvider struct {
    webhookService *webhook.WebhookService
}

func (p *BlogWebhookProvider) GetSupportedEvents() []webhook.EventType {
    return []webhook.EventType{
        {Name: "blog.post.created", Description: "A new blog post is created"},
        {Name: "blog.post.published", Description: "A blog post is published"},
        {Name: "blog.post.updated", Description: "A blog post is updated"},
        {Name: "blog.post.deleted", Description: "A blog post is deleted"},
        {Name: "blog.comment.created", Description: "A new comment is added"},
        {Name: "blog.comment.approved", Description: "A comment is approved"},
    }
}

func (s *BlogService) PublishPost(ctx context.Context, postID uint) error {
    post, err := s.repo.GetPost(ctx, postID)
    if err != nil {
        return err
    }
    
    // Update post status
    post.Status = "published"
    post.PublishedAt = time.Now()
    
    if err := s.repo.UpdatePost(ctx, post); err != nil {
        return err
    }
    
    // Emit webhook event
    s.emitPostEvent(ctx, "blog.post.published", post)
    
    return nil
}

func (s *BlogService) emitPostEvent(ctx context.Context, eventType string, post *Post) {
    event := &webhook.WebhookEvent{
        TenantID:   post.TenantID,
        ModuleName: "blog",
        EventType:  eventType,
        EventID:    fmt.Sprintf("blog_post_%d_%s", post.ID, uuid.New().String()),
        Payload: map[string]interface{}{
            "post": map[string]interface{}{
                "id":           post.ID,
                "title":        post.Title,
                "slug":         post.Slug,
                "status":       post.Status,
                "author_id":    post.AuthorID,
                "category_id":  post.CategoryID,
                "published_at": post.PublishedAt,
                "url":          fmt.Sprintf("/blog/%s", post.Slug),
            },
            "event_metadata": map[string]interface{}{
                "timestamp": time.Now().Unix(),
                "version":   "1.0",
            },
        },
    }
    
    if err := s.webhookProvider.EmitEvent(ctx, event); err != nil {
        s.logger.Error("Failed to emit webhook event",
            "event_type", eventType,
            "post_id", post.ID,
            "error", err)
    }
}
```

### User Module Integration

```go
package user

func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
    // Create user logic...
    user, err := s.repo.CreateUser(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // Emit webhook
    s.webhookService.EmitEvent(ctx, &webhook.WebhookEvent{
        TenantID:   req.TenantID,
        ModuleName: "user",
        EventType:  "user.created",
        EventID:    fmt.Sprintf("user_%d_%s", user.ID, uuid.New().String()),
        Payload: map[string]interface{}{
            "user": map[string]interface{}{
                "id":         user.ID,
                "email":      user.Email,
                "username":   user.Username,
                "status":     user.Status,
                "created_at": user.CreatedAt,
            },
        },
    })
    
    return user, nil
}
```

## 3. API Endpoints

### Webhook Endpoint Management

```go
package handlers

type WebhookHandler struct {
    service *webhook.WebhookService
}

// POST /api/webhooks/endpoints
func (h *WebhookHandler) CreateEndpoint(c *gin.Context) {
    var req CreateEndpointRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    tenantID := GetTenantID(c)
    
    endpoint := &webhook.Endpoint{
        TenantID:         tenantID,
        ModuleName:       req.ModuleName,
        Name:             req.Name,
        URL:              req.URL,
        Secret:           GenerateWebhookSecret(),
        Events:           req.Events,
        Headers:          req.Headers,
        MaxRetries:       req.MaxRetries,
        TimeoutSeconds:   req.TimeoutSeconds,
        RetryStrategy:    req.RetryStrategy,
        IsActive:         true,
    }
    
    if err := h.service.CreateEndpoint(c.Request.Context(), endpoint); err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(201, endpoint)
}

// GET /api/webhooks/endpoints
func (h *WebhookHandler) ListEndpoints(c *gin.Context) {
    tenantID := GetTenantID(c)
    module := c.Query("module")
    
    endpoints, err := h.service.ListEndpoints(c.Request.Context(), tenantID, module)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, endpoints)
}

// GET /api/webhooks/events
func (h *WebhookHandler) ListEvents(c *gin.Context) {
    module := c.Query("module")
    
    events, err := h.service.GetSupportedEvents(c.Request.Context(), module)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, events)
}

// GET /api/webhooks/deliveries
func (h *WebhookHandler) ListDeliveries(c *gin.Context) {
    tenantID := GetTenantID(c)
    endpointID := c.Query("endpoint_id")
    status := c.Query("status")
    
    filter := &webhook.DeliveryFilter{
        TenantID:   tenantID,
        EndpointID: endpointID,
        Status:     status,
    }
    
    deliveries, err := h.service.ListDeliveries(c.Request.Context(), filter)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, deliveries)
}

// POST /api/webhooks/test
func (h *WebhookHandler) TestEndpoint(c *gin.Context) {
    var req TestEndpointRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    result, err := h.service.TestEndpoint(c.Request.Context(), req.EndpointID, req.EventType)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(200, result)
}
```

## 4. Testing Webhooks

### Webhook Testing Service

```go
package webhook

type TestingService struct {
    service *WebhookService
}

func (t *TestingService) TestEndpoint(ctx context.Context, endpointID uint, eventType string) (*TestResult, error) {
    endpoint, err := t.service.GetEndpoint(ctx, endpointID)
    if err != nil {
        return nil, err
    }
    
    // Create test event
    testEvent := &WebhookEvent{
        TenantID:   endpoint.TenantID,
        ModuleName: endpoint.ModuleName,
        EventType:  eventType,
        EventID:    fmt.Sprintf("test_%s", uuid.New().String()),
        Payload: map[string]interface{}{
            "test": true,
            "timestamp": time.Now().Unix(),
            "message": "This is a test webhook delivery",
        },
    }
    
    // Deliver immediately (bypass queue)
    delivery := &WebhookDelivery{
        TenantID:   endpoint.TenantID,
        EndpointID: endpoint.ID,
        EventID:    testEvent.ID,
    }
    
    start := time.Now()
    err = t.deliverTestWebhook(ctx, endpoint, testEvent, delivery)
    duration := time.Since(start)
    
    return &TestResult{
        Success:        err == nil,
        Duration:       duration,
        StatusCode:     delivery.ResponseStatus,
        ResponseBody:   delivery.ResponseBody,
        Error:          err,
    }, nil
}

// Webhook Echo Server for testing
func (t *TestingService) StartEchoServer(port int) {
    router := gin.New()
    
    router.POST("/webhook-echo", func(c *gin.Context) {
        var body interface{}
        c.ShouldBindJSON(&body)
        
        response := gin.H{
            "received": true,
            "headers": c.Request.Header,
            "body": body,
            "timestamp": time.Now().Unix(),
        }
        
        // Verify signature if present
        signature := c.GetHeader("X-Webhook-Signature")
        if signature != "" {
            response["signature_valid"] = t.verifyTestSignature(c, signature)
        }
        
        c.JSON(200, response)
    })
    
    router.Run(fmt.Sprintf(":%d", port))
}
```

## 5. Monitoring và Metrics

### Prometheus Metrics

```go
package webhook

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    webhookEventsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "webhook_events_total",
            Help: "Total number of webhook events created",
        },
        []string{"tenant_id", "module", "event_type"},
    )
    
    webhookDeliveriesTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "webhook_deliveries_total",
            Help: "Total number of webhook deliveries",
        },
        []string{"tenant_id", "status"},
    )
    
    webhookDeliveryDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "webhook_delivery_duration_seconds",
            Help: "Duration of webhook deliveries",
            Buckets: prometheus.DefBuckets,
        },
        []string{"tenant_id", "status"},
    )
    
    webhookQueueSize = promauto.NewGauge(
        prometheus.GaugeOpts{
            Name: "webhook_queue_size",
            Help: "Current size of webhook delivery queue",
        },
    )
)

func (s *WebhookService) recordMetrics(event *WebhookEvent, delivery *WebhookDelivery, duration time.Duration) {
    webhookEventsTotal.WithLabelValues(
        fmt.Sprintf("%d", event.TenantID),
        event.ModuleName,
        event.EventType,
    ).Inc()
    
    webhookDeliveriesTotal.WithLabelValues(
        fmt.Sprintf("%d", delivery.TenantID),
        string(delivery.DeliveryStatus),
    ).Inc()
    
    webhookDeliveryDuration.WithLabelValues(
        fmt.Sprintf("%d", delivery.TenantID),
        string(delivery.DeliveryStatus),
    ).Observe(duration.Seconds())
}
```

### Health Check Endpoint

```go
func (h *WebhookHandler) HealthCheck(c *gin.Context) {
    stats, err := h.service.GetHealthStats(c.Request.Context())
    if err != nil {
        c.JSON(500, gin.H{"status": "unhealthy", "error": err.Error()})
        return
    }
    
    healthy := stats.QueueSize < 10000 && 
               stats.FailureRate < 0.1 &&
               stats.AverageDeliveryTime < 5*time.Second
    
    status := "healthy"
    if !healthy {
        status = "degraded"
    }
    
    c.JSON(200, gin.H{
        "status": status,
        "stats": stats,
        "timestamp": time.Now().Unix(),
    })
}
```