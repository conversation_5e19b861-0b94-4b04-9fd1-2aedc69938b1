# Module Onboarding

Tài liệu này mô tả chi tiết về module Onboarding, có chức năng hướng dẫn người dùng mới thông qua các quy trình thiết lập ban đầu của ứng dụng.

## C<PERSON><PERSON> khái niệm chính

-   **Journey (Hành trình)**: <PERSON><PERSON> một chuỗi các bước mà người dùng cần hoàn thành. V<PERSON> dụ: "Thiết lập tài khoản", "Tạo website đầu tiên". Mỗi journey có thể được gán cho một tenant cụ thể.
-   **Step (Bước)**: Là một hành động cụ thể trong một Journey. Ví dụ: "Xác thực email", "Điền thông tin hồ sơ", "Mời thành viên". <PERSON><PERSON><PERSON> bước có thể được sắp xếp theo thứ tự và có sự phụ thuộc lẫn nhau.
-   **Progress (Tiến trình)**: <PERSON><PERSON> lại tiến trình của một người dùng cụ thể trong một Journey. Nó theo dõi bước nào đã hoàn thành, bước nào đang thực hiện, và bước nào đã bỏ qua.

## Mục lục

- [1. Quản lý Hành trình (Journeys)](#1-quản-lý-hành-trình-journeys)
  - [1.1. Lấy danh sách Journeys - `GET /onboarding/journeys`](#11-lấy-danh-sách-journeys---get-onboardingjourneys)
  - [1.2. Tạo Journey mới - `POST /onboarding/journeys`](#12-tạo-journey-mới---post-onboardingjourneys)
- [2. Quản lý Tiến trình (Progress)](#2-quản-lý-tiến-trình-progress)
  - [2.1. Bắt đầu một Journey - `POST /onboarding/progress/start-journey`](#21-bắt-đầu-một-journey---post-onboardingprogressstart-journey)
  - [2.2. Hoàn thành một Step - `POST /onboarding/progress/complete-step`](#22-hoàn-thành-một-step---post-onboardingprogresscomplete-step)
  - [2.3. Lấy tiến trình của người dùng - `GET /onboarding/progress/user/{user_id}`](#23-lấy-tiến-trình-của-người-dùng---get-onboardingprogressuseruser_id)

---

## 1. Quản lý Hành trình (Journeys)

Các API để tạo và quản lý các hành trình onboarding.

### 1.1. Lấy danh sách Journeys - `GET /onboarding/journeys`

Lấy danh sách các hành trình có sẵn cho tenant hiện tại.

```mermaid
sequenceDiagram
    Admin->>+API: GET /onboarding/journeys
    API->>+JourneyHandler: ListJourneys(c)
    JourneyHandler->>+JourneyService: ListJourneys(tenantID, ...)
    JourneyService->>+JourneyRepo: Find(filter)
    JourneyRepo-->>-JourneyService: journeys[]
    JourneyService-->>-JourneyHandler: journeys[]
    JourneyHandler-->>-API: 200 OK
    API-->>-Admin: {journeys: [...]}
```

### 1.2. Tạo Journey mới - `POST /onboarding/journeys`

Tạo một hành trình onboarding mới với các bước được định nghĩa sẵn.

```mermaid
sequenceDiagram
    Admin->>+API: POST /onboarding/journeys (body: {name, steps, ...})
    API->>+JourneyHandler: CreateJourney(c)
    JourneyHandler->>+JourneyService: CreateJourney(req, tenantID)
    JourneyService->>+JourneyRepo: Create(journey)
    JourneyRepo-->>-JourneyService: createdJourney
    Note right of JourneyService: Tạo các Step liên quan
    JourneyService-->>-JourneyHandler: createdJourney
    JourneyHandler-->>-API: 201 Created
    API-->>-Admin: {id, name, ...}
```

---

## 2. Quản lý Tiến trình (Progress)

Các API để theo dõi và cập nhật tiến trình của người dùng qua các hành trình.

### 2.1. Bắt đầu một Journey - `POST /onboarding/progress/start-journey`

Kích hoạt một hành trình cho người dùng, tạo ra một bản ghi tiến trình mới.

```mermaid
sequenceDiagram
    User->>+API: POST /progress/start-journey (body: {user_id, journey_id})
    API->>+ProgressHandler: StartJourney(c)
    ProgressHandler->>+ProgressService: StartJourney(userID, journeyID)
    ProgressService->>+ProgressRepo: Create(progress)
    ProgressRepo-->>-ProgressService: createdProgress
    ProgressService-->>-ProgressHandler: createdProgress
    ProgressHandler-->>-API: 201 Created
    API-->>-User: {id, user_id, journey_id, status: "started", ...}
```

### 2.2. Hoàn thành một Step - `POST /onboarding/progress/complete-step`

Đánh dấu một bước trong hành trình là đã hoàn thành.

```mermaid
sequenceDiagram
    User->>+API: POST /progress/complete-step (body: {user_id, step_id})
    API->>+ProgressHandler: CompleteStep(c)
    ProgressHandler->>+ProgressService: CompleteStep(userID, stepID, data)
    ProgressService->>+ProgressRepo: FindProgress(userID, stepID)
    ProgressRepo-->>-ProgressService: progress
    ProgressService->>ProgressService: Cập nhật trạng thái & kiểm tra logic tiếp theo
    ProgressService->>+ProgressRepo: Update(progress)
    ProgressRepo-->>-ProgressService: updatedProgress
    ProgressService-->>-ProgressHandler: updatedProgress
    ProgressHandler-->>-API: 200 OK
    API-->>-User: {progress_data}
```

### 2.3. Lấy tiến trình của người dùng - `GET /onboarding/progress/user/{user_id}`

Lấy tất cả các tiến trình onboarding của một người dùng cụ thể.

```mermaid
sequenceDiagram
    User->>+API: GET /progress/user/123
    API->>+ProgressHandler: GetUserProgress(c)
    ProgressHandler->>+ProgressService: GetUserProgress(123, ...)
    ProgressService->>+ProgressRepo: FindByUserID(123)
    ProgressRepo-->>-ProgressService: progress[]
    ProgressService-->>-ProgressHandler: progress[]
    ProgressHandler-->>-API: 200 OK
    API-->>-User: [{journey_id, status, completed_steps, ...}]
```
