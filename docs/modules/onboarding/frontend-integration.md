# Frontend Integration Guide

## Overview

This guide provides detailed instructions for frontend developers to implement the onboarding user experience using the WN API v3 onboarding module. It covers API integration, state management, UI patterns, and best practices.

## Prerequisites

Before implementing the onboarding flow, ensure you have:

1. **Authentication System**: JWT token management and refresh logic
2. **HTTP Client**: Configured with proper error handling and interceptors
3. **State Management**: Redux, Zustand, or similar for managing onboarding state
4. **UI Components**: Form components, progress indicators, and error displays

## Authentication Setup

### JWT Token Management

All onboarding API calls require a valid JWT token in the Authorization header:

```javascript
// Example using Axios
const apiClient = axios.create({
  baseURL: 'http://localhost:9033/api/cms/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add JWT token to all requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token refresh on 401 errors
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Implement token refresh logic
      await refreshToken();
      return apiClient.request(error.config);
    }
    return Promise.reject(error);
  }
);
```

### User Context Validation

Ensure the JWT token contains valid user information:

```javascript
// Decode JWT to get user context
import jwt_decode from 'jwt-decode';

function getUserFromToken() {
  const token = localStorage.getItem('access_token');
  if (!token) return null;
  
  try {
    const decoded = jwt_decode(token);
    return {
      userId: decoded.user_id,
      email: decoded.email,
      // other user fields
    };
  } catch (error) {
    console.error('Invalid token:', error);
    return null;
  }
}
```

## API Integration

### 1. Check Onboarding Status

First, check if the user needs onboarding:

```javascript
async function checkOnboardingStatus() {
  try {
    const response = await apiClient.get('/onboarding/required');
    return response.data.required;
  } catch (error) {
    console.error('Failed to check onboarding status:', error);
    return false;
  }
}

// Usage
const needsOnboarding = await checkOnboardingStatus();
if (needsOnboarding) {
  // Redirect to onboarding flow
  router.push('/onboarding');
}
```

### 2. Get Current Progress

Retrieve the user's current onboarding progress:

```javascript
async function getOnboardingProgress() {
  try {
    const response = await apiClient.get('/onboarding/progress');
    return response.data;
  } catch (error) {
    if (error.response?.status === 404) {
      // No onboarding record exists yet
      return null;
    }
    throw error;
  }
}

// Usage
const progress = await getOnboardingProgress();
console.log('Current step:', progress?.step);
console.log('Status:', progress?.status);
```

### 3. Start Onboarding

Initialize the onboarding process:

```javascript
async function startOnboarding() {
  try {
    const response = await apiClient.post('/onboarding/start');
    return response.data;
  } catch (error) {
    if (error.response?.status === 409) {
      // Onboarding already completed
      throw new Error('Onboarding already completed');
    }
    throw error;
  }
}

// Usage
try {
  const result = await startOnboarding();
  console.log('Onboarding started:', result);
} catch (error) {
  console.error('Failed to start onboarding:', error.message);
}
```

### 4. Update Onboarding Step

Progress to the next step:

```javascript
async function updateOnboardingStep(step, metadata = {}) {
  try {
    const response = await apiClient.put('/onboarding/step', {
      step,
      metadata
    });
    return response.data;
  } catch (error) {
    console.error('Failed to update step:', error);
    throw error;
  }
}

// Usage - Moving to website creation step
await updateOnboardingStep('create_website', {
  tenant_id: 123,
  tenant_name: 'My Company'
});
```

### 5. Complete Onboarding

Mark onboarding as finished:

```javascript
async function completeOnboarding(metadata = {}) {
  try {
    const response = await apiClient.post('/onboarding/complete', {
      metadata: {
        completion_method: 'manual',
        ...metadata
      }
    });
    return response.data;
  } catch (error) {
    console.error('Failed to complete onboarding:', error);
    throw error;
  }
}

// Usage
await completeOnboarding({
  website_id: 456,
  website_name: 'My Website'
});
```

## State Management

### Redux Example

```javascript
// onboardingSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Async thunks
export const fetchOnboardingProgress = createAsyncThunk(
  'onboarding/fetchProgress',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/onboarding/progress');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const startOnboarding = createAsyncThunk(
  'onboarding/start',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.post('/onboarding/start');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateStep = createAsyncThunk(
  'onboarding/updateStep',
  async ({ step, metadata }, { rejectWithValue }) => {
    try {
      const response = await apiClient.put('/onboarding/step', {
        step,
        metadata
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Slice
const onboardingSlice = createSlice({
  name: 'onboarding',
  initialState: {
    progress: null,
    loading: false,
    error: null,
    currentStep: null,
    isRequired: false,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentStep: (state, action) => {
      state.currentStep = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchOnboardingProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOnboardingProgress.fulfilled, (state, action) => {
        state.loading = false;
        state.progress = action.payload;
        state.currentStep = action.payload?.step;
      })
      .addCase(fetchOnboardingProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(startOnboarding.fulfilled, (state, action) => {
        state.progress = action.payload;
        state.currentStep = action.payload.step;
      })
      .addCase(updateStep.fulfilled, (state, action) => {
        state.progress = action.payload;
        state.currentStep = action.payload.step;
      });
  },
});

export const { clearError, setCurrentStep } = onboardingSlice.actions;
export default onboardingSlice.reducer;
```

### Zustand Example

```javascript
// onboardingStore.js
import { create } from 'zustand';

const useOnboardingStore = create((set, get) => ({
  progress: null,
  loading: false,
  error: null,
  
  fetchProgress: async () => {
    set({ loading: true, error: null });
    try {
      const response = await apiClient.get('/onboarding/progress');
      set({ progress: response.data, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },
  
  startOnboarding: async () => {
    set({ loading: true, error: null });
    try {
      const response = await apiClient.post('/onboarding/start');
      set({ progress: response.data, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },
  
  updateStep: async (step, metadata) => {
    set({ loading: true, error: null });
    try {
      const response = await apiClient.put('/onboarding/step', {
        step,
        metadata
      });
      set({ progress: response.data, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },
  
  completeOnboarding: async (metadata) => {
    set({ loading: true, error: null });
    try {
      const response = await apiClient.post('/onboarding/complete', {
        metadata
      });
      set({ progress: response.data, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },
  
  clearError: () => set({ error: null }),
}));

export default useOnboardingStore;
```

## UI Implementation

### 1. Onboarding Router Setup

```javascript
// React Router example
import { Routes, Route, Navigate } from 'react-router-dom';

function OnboardingRouter() {
  const { progress, loading } = useOnboardingStore();
  
  if (loading) return <LoadingSpinner />;
  
  // Redirect based on current step
  const getCurrentStepRoute = () => {
    if (!progress) return '/onboarding/start';
    
    switch (progress.step) {
      case 'create_tenant':
        return '/onboarding/tenant';
      case 'create_website':
        return '/onboarding/website';
      case 'completed':
        return '/dashboard';
      default:
        return '/onboarding/start';
    }
  };
  
  return (
    <Routes>
      <Route path="/onboarding" element={<Navigate to={getCurrentStepRoute()} />} />
      <Route path="/onboarding/start" element={<OnboardingStart />} />
      <Route path="/onboarding/tenant" element={<TenantCreation />} />
      <Route path="/onboarding/website" element={<WebsiteCreation />} />
      <Route path="/onboarding/complete" element={<OnboardingComplete />} />
    </Routes>
  );
}
```

### 2. Progress Indicator Component

```javascript
function OnboardingProgress({ currentStep }) {
  const steps = [
    { key: 'create_tenant', label: 'Create Organization', icon: '🏢' },
    { key: 'create_website', label: 'Setup Website', icon: '🌐' },
    { key: 'completed', label: 'Complete', icon: '✅' },
  ];
  
  const currentIndex = steps.findIndex(step => step.key === currentStep);
  
  return (
    <div className="onboarding-progress">
      {steps.map((step, index) => (
        <div
          key={step.key}
          className={`step ${index <= currentIndex ? 'completed' : 'pending'}`}
        >
          <div className="step-icon">{step.icon}</div>
          <div className="step-label">{step.label}</div>
          {index < steps.length - 1 && (
            <div className={`connector ${index < currentIndex ? 'completed' : 'pending'}`} />
          )}
        </div>
      ))}
    </div>
  );
}
```

### 3. Tenant Creation Component

```javascript
function TenantCreation() {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    industry: '',
  });
  const [loading, setLoading] = useState(false);
  const { updateStep } = useOnboardingStore();
  const navigate = useNavigate();
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Create tenant via tenant API
      const tenantResponse = await apiClient.post('/tenants', formData);
      const tenant = tenantResponse.data;
      
      // Update onboarding step
      await updateStep('create_website', {
        tenant_id: tenant.id,
        tenant_name: tenant.name,
      });
      
      // Navigate to next step
      navigate('/onboarding/website');
    } catch (error) {
      console.error('Failed to create tenant:', error);
      // Handle error (show toast, etc.)
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="onboarding-step">
      <OnboardingProgress currentStep="create_tenant" />
      
      <div className="step-content">
        <h2>Create Your Organization</h2>
        <p>Let's start by setting up your organization details.</p>
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="name">Organization Name *</label>
            <input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="industry">Industry</label>
            <select
              id="industry"
              value={formData.industry}
              onChange={(e) => setFormData({ ...formData, industry: e.target.value })}
            >
              <option value="">Select Industry</option>
              <option value="technology">Technology</option>
              <option value="healthcare">Healthcare</option>
              <option value="finance">Finance</option>
              <option value="education">Education</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          <div className="form-actions">
            <button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Continue'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
```

### 4. Error Handling Component

```javascript
function OnboardingErrorBoundary({ children }) {
  const [hasError, setHasError] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const handleError = (error) => {
      setHasError(true);
      setError(error);
    };
    
    window.addEventListener('unhandledrejection', handleError);
    return () => window.removeEventListener('unhandledrejection', handleError);
  }, []);
  
  if (hasError) {
    return (
      <div className="onboarding-error">
        <h2>Something went wrong</h2>
        <p>We encountered an error during the onboarding process.</p>
        <button onClick={() => window.location.reload()}>
          Reload Page
        </button>
        <button onClick={() => {
          setHasError(false);
          setError(null);
        }}>
          Try Again
        </button>
      </div>
    );
  }
  
  return children;
}
```

## Error Handling Patterns

### 1. Network Error Handling

```javascript
function useOnboardingWithRetry() {
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;
  
  const executeWithRetry = async (apiCall) => {
    try {
      const result = await apiCall();
      setRetryCount(0); // Reset on success
      return result;
    } catch (error) {
      if (retryCount < maxRetries && isRetryableError(error)) {
        setRetryCount(prev => prev + 1);
        // Exponential backoff
        await new Promise(resolve => 
          setTimeout(resolve, Math.pow(2, retryCount) * 1000)
        );
        return executeWithRetry(apiCall);
      }
      throw error;
    }
  };
  
  return { executeWithRetry, retryCount };
}

function isRetryableError(error) {
  const retryableStatuses = [408, 429, 500, 502, 503, 504];
  return retryableStatuses.includes(error.response?.status);
}
```

### 2. Validation Error Display

```javascript
function ValidationErrors({ errors }) {
  if (!errors || Object.keys(errors).length === 0) return null;
  
  return (
    <div className="validation-errors">
      {Object.entries(errors).map(([field, message]) => (
        <div key={field} className="error-item">
          <strong>{field}:</strong> {message}
        </div>
      ))}
    </div>
  );
}
```

## Performance Optimization

### 1. Lazy Loading

```javascript
// Lazy load onboarding components
const TenantCreation = lazy(() => import('./TenantCreation'));
const WebsiteCreation = lazy(() => import('./WebsiteCreation'));

function OnboardingRouter() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        <Route path="/onboarding/tenant" element={<TenantCreation />} />
        <Route path="/onboarding/website" element={<WebsiteCreation />} />
      </Routes>
    </Suspense>
  );
}
```

### 2. Progress Caching

```javascript
// Cache progress in localStorage
function useOnboardingCache() {
  const cacheKey = 'onboarding_progress';
  
  const getCachedProgress = () => {
    try {
      const cached = localStorage.getItem(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch {
      return null;
    }
  };
  
  const setCachedProgress = (progress) => {
    try {
      localStorage.setItem(cacheKey, JSON.stringify(progress));
    } catch (error) {
      console.warn('Failed to cache progress:', error);
    }
  };
  
  const clearCache = () => {
    localStorage.removeItem(cacheKey);
  };
  
  return { getCachedProgress, setCachedProgress, clearCache };
}
```

## Testing Considerations

### 1. Mock API Responses

```javascript
// Mock for testing
const mockOnboardingAPI = {
  getProgress: () => Promise.resolve({
    id: 1,
    user_id: 123,
    status: 'processing',
    step: 'create_tenant',
    started_at: new Date().toISOString(),
    metadata: {}
  }),
  
  startOnboarding: () => Promise.resolve({
    id: 1,
    user_id: 123,
    status: 'processing',
    step: 'create_tenant',
    started_at: new Date().toISOString(),
    message: 'Onboarding started successfully'
  }),
  
  updateStep: (step, metadata) => Promise.resolve({
    id: 1,
    user_id: 123,
    status: 'processing',
    step,
    metadata,
    updated_at: new Date().toISOString(),
    message: `Onboarding step updated to ${step}`
  })
};
```

### 2. Component Testing

```javascript
// Jest + React Testing Library example
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import TenantCreation from './TenantCreation';

test('submits tenant creation form', async () => {
  render(<TenantCreation />);
  
  fireEvent.change(screen.getByLabelText(/organization name/i), {
    target: { value: 'Test Company' }
  });
  
  fireEvent.click(screen.getByRole('button', { name: /continue/i }));
  
  await waitFor(() => {
    expect(screen.getByText(/creating/i)).toBeInTheDocument();
  });
});
```

## Best Practices Summary

1. **Always handle authentication errors** with token refresh
2. **Implement proper loading states** for better UX
3. **Cache progress locally** to survive page refreshes
4. **Use optimistic updates** where appropriate
5. **Provide clear error messages** and recovery options
6. **Test offline scenarios** and network failures
7. **Make the flow resumable** from any point
8. **Track analytics** for onboarding optimization
9. **Implement proper accessibility** features
10. **Use progressive enhancement** for better performance
