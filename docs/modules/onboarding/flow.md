# Onboarding Flow Documentation

## Overview

This document describes the complete onboarding flow for new users in the WN API v3 system, including state transitions, decision points, and error handling scenarios.

## Complete Onboarding Flow

```mermaid
flowchart TD
    A[User Registration] --> B{Onboarding Record Exists?}
    B -->|No| C[Create Onboarding Record]
    B -->|Yes| D{Check Status}
    
    C --> E[Status: pending<br/>Step: create_tenant]
    D -->|pending| E
    D -->|processing| F[Resume Current Step]
    D -->|completed| G[Onboarding Complete]
    
    E --> H[Start Onboarding]
    H --> I[Status: processing<br/>Step: create_tenant]
    
    I --> J[Create Tenant Step]
    J --> K{Tenant Created?}
    K -->|Yes| L[Update Step: create_website]
    K -->|No| M[Show Tenant Creation Error]
    M --> J
    
    L --> N[Create Website Step]
    N --> O{Website Created?}
    O -->|Yes| P[Complete Onboarding]
    O -->|No| Q[Show Website Creation Error]
    Q --> N
    
    P --> R[Status: completed<br/>Step: completed<br/>Set completed_at]
    R --> G
    
    F --> S{Current Step?}
    S -->|create_tenant| J
    S -->|create_website| N
    S -->|completed| G
    
    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style M fill:#ffcdd2
    style Q fill:#ffcdd2
```

## State Transition Diagram

```mermaid
stateDiagram-v2
    [*] --> pending : User Registration
    pending --> processing : Start Onboarding
    processing --> processing : Update Step
    processing --> completed : Complete Onboarding
    completed --> [*] : Onboarding Finished
    
    processing --> pending : Reset Onboarding
    completed --> pending : Reset Onboarding (Admin)
    
    note right of pending
        Step: create_tenant
        started_at: null
        completed_at: null
    end note
    
    note right of processing
        Step: create_tenant | create_website
        started_at: timestamp
        completed_at: null
    end note
    
    note right of completed
        Step: completed
        started_at: timestamp
        completed_at: timestamp
    end note
```

## Step-by-Step Breakdown

### Step 1: User Registration and Initialization

1. **User registers** in the system
2. **Check for existing onboarding record**
   - If exists: Resume from current state
   - If not exists: Create new record with `pending` status

```mermaid
sequenceDiagram
    participant U as User
    participant A as Auth API
    participant O as Onboarding API
    participant DB as Database
    
    U->>A: Register Account
    A->>DB: Create User
    A->>O: Check Onboarding Status
    O->>DB: Query onboarding_progress
    DB-->>O: No record found
    O->>DB: Create onboarding record
    DB-->>O: Record created
    O-->>A: Status: pending, Step: create_tenant
    A-->>U: Registration complete + onboarding required
```

### Step 2: Start Onboarding Process

1. **User initiates onboarding** (manual or automatic)
2. **Update status** from `pending` to `processing`
3. **Set started_at** timestamp
4. **Begin first step**: create_tenant

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant O as Onboarding API
    participant DB as Database
    
    U->>UI: Click "Start Setup"
    UI->>O: POST /onboarding/start
    O->>DB: Update status to 'processing'
    O->>DB: Set started_at timestamp
    DB-->>O: Record updated
    O-->>UI: Status: processing, Step: create_tenant
    UI-->>U: Show tenant creation form
```

### Step 3: Create Tenant

1. **User fills tenant information**
2. **Submit tenant creation request**
3. **On success**: Update step to `create_website`
4. **On failure**: Show error, remain on current step

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant O as Onboarding API
    participant T as Tenant API
    participant DB as Database
    
    U->>UI: Fill tenant form
    UI->>T: POST /tenants
    T->>DB: Create tenant
    DB-->>T: Tenant created (ID: 123)
    T-->>UI: Success: tenant_id=123
    UI->>O: PUT /onboarding/step
    Note over UI,O: step: "create_website"<br/>metadata: {tenant_id: 123}
    O->>DB: Update step and metadata
    DB-->>O: Record updated
    O-->>UI: Step updated to create_website
    UI-->>U: Show website creation form
```

### Step 4: Create Website

1. **User configures website settings**
2. **Submit website creation request**
3. **On success**: Complete onboarding
4. **On failure**: Show error, remain on current step

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant O as Onboarding API
    participant W as Website API
    participant DB as Database
    
    U->>UI: Fill website form
    UI->>W: POST /websites
    W->>DB: Create website
    DB-->>W: Website created (ID: 456)
    W-->>UI: Success: website_id=456
    UI->>O: POST /onboarding/complete
    Note over UI,O: metadata: {<br/>  website_id: 456,<br/>  completion_method: "manual"<br/>}
    O->>DB: Set status='completed', step='completed'
    O->>DB: Set completed_at timestamp
    DB-->>O: Record updated
    O-->>UI: Onboarding completed
    UI-->>U: Show success message + dashboard
```

## Error Handling and Edge Cases

### Common Error Scenarios

#### 1. Network Connectivity Issues

```mermaid
flowchart TD
    A[API Request] --> B{Network Available?}
    B -->|No| C[Show Offline Message]
    B -->|Yes| D[Send Request]
    D --> E{Request Successful?}
    E -->|No| F[Show Retry Option]
    E -->|Yes| G[Process Response]
    
    C --> H[Retry When Online]
    F --> I[User Clicks Retry]
    H --> D
    I --> D
    
    style C fill:#ffcdd2
    style F fill:#ffcdd2
```

#### 2. Validation Errors

```mermaid
flowchart TD
    A[User Submits Form] --> B[Client Validation]
    B --> C{Valid?}
    C -->|No| D[Show Field Errors]
    C -->|Yes| E[Send to API]
    E --> F{API Validation}
    F -->|Fail| G[Show API Errors]
    F -->|Pass| H[Process Success]
    
    D --> I[User Fixes Errors]
    G --> I
    I --> A
    
    style D fill:#fff3e0
    style G fill:#fff3e0
```

#### 3. Session Expiration

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant O as Onboarding API
    participant A as Auth API
    
    U->>UI: Continue onboarding
    UI->>O: API Request
    O-->>UI: 401 Unauthorized
    UI->>A: Refresh token
    A-->>UI: New access token
    UI->>O: Retry original request
    O-->>UI: Success response
    UI-->>U: Continue flow
```

### Recovery Mechanisms

#### 1. Auto-Save Progress

- **Metadata tracking**: Store intermediate form data
- **Step validation**: Ensure each step completion is verified
- **Resume capability**: Allow users to continue from last valid step

#### 2. Graceful Degradation

- **Optional steps**: Mark certain steps as skippable
- **Partial completion**: Allow progression with warnings
- **Manual intervention**: Admin tools for stuck users

#### 3. Rollback Scenarios

```mermaid
flowchart TD
    A[Error Detected] --> B{Error Type?}
    B -->|Validation| C[Show Error Message]
    B -->|Server Error| D[Rollback to Previous Step]
    B -->|Critical Error| E[Reset Onboarding]
    
    C --> F[User Corrects Input]
    D --> G[Restore Previous State]
    E --> H[Start Fresh]
    
    F --> I[Retry Operation]
    G --> I
    H --> J[Begin Onboarding Again]
```

## Progress Tracking and Analytics

### Key Metrics to Track

1. **Completion Rate**: Percentage of users who complete onboarding
2. **Drop-off Points**: Steps where users most commonly abandon
3. **Time to Complete**: Average duration for full onboarding
4. **Step Duration**: Time spent on each individual step
5. **Error Frequency**: Most common errors encountered

### Metadata Structure

```json
{
  "started_at": "2025-07-22T10:00:00Z",
  "step_timestamps": {
    "create_tenant": "2025-07-22T10:05:00Z",
    "create_website": "2025-07-22T10:15:00Z",
    "completed": "2025-07-22T10:25:00Z"
  },
  "tenant_id": 123,
  "website_id": 456,
  "completion_method": "manual",
  "errors_encountered": [
    {
      "step": "create_tenant",
      "error": "validation_error",
      "timestamp": "2025-07-22T10:03:00Z"
    }
  ],
  "user_agent": "Mozilla/5.0...",
  "referrer": "registration_page"
}
```

## Integration Points

### 1. User Registration Flow

- **Trigger**: Automatically start onboarding after email verification
- **Context**: Pass registration source and user preferences
- **Customization**: Adapt flow based on user type or plan

### 2. Dashboard Integration

- **Progress Indicator**: Show completion percentage
- **Quick Actions**: Direct links to incomplete steps
- **Dismissal**: Option to skip onboarding (if allowed)

### 3. Admin Dashboard

- **User Management**: View and modify user onboarding status
- **Analytics**: Onboarding funnel and conversion metrics
- **Support Tools**: Reset or advance stuck users

## Best Practices

### 1. User Experience

- **Clear Progress Indication**: Show current step and remaining steps
- **Save and Resume**: Allow users to complete onboarding over multiple sessions
- **Help and Support**: Provide contextual help at each step
- **Mobile Responsive**: Ensure onboarding works on all devices

### 2. Technical Implementation

- **Idempotent Operations**: Ensure API calls can be safely retried
- **Optimistic Updates**: Update UI immediately, handle errors gracefully
- **State Synchronization**: Keep frontend and backend state in sync
- **Error Boundaries**: Prevent onboarding errors from breaking the entire app

### 3. Performance

- **Lazy Loading**: Load step content as needed
- **Caching**: Cache static content and user progress
- **Minimal Requests**: Batch operations where possible
- **Background Sync**: Sync progress in background when possible
