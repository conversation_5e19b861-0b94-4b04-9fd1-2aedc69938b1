# Customer Module Architecture for E-commerce

## Module Placement và Organization

### Recommended Structure: Separate Customer Module

```
internal/modules/
├── auth/              # System authentication (admins, staff)
├── user/              # System user management  
├── customer/          # Customer module (e-commerce users)
│   ├── models/
│   │   ├── customer.go
│   │   ├── customer_profile.go
│   │   ├── customer_address.go
│   │   ├── customer_preferences.go
│   │   └── customer_wallet.go
│   ├── repositories/
│   │   ├── interfaces.go
│   │   └── mysql/
│   │       ├── customer_repository.go
│   │       └── address_repository.go
│   ├── services/
│   │   ├── customer_service.go
│   │   ├── auth_service.go
│   │   ├── profile_service.go
│   │   └── address_service.go
│   ├── handlers/
│   │   ├── auth_handler.go
│   │   ├── profile_handler.go
│   │   └── address_handler.go
│   └── routes.go
└── ecommerce/         # E-commerce features
    ├── cart/
    ├── order/
    ├── payment/
    └── product/
```

## Rationale cho Separate Customer Module

### 1. Clear Separation of Concerns
- **System Users**: Admin, staff, content creators
- **Customers**: End users mua hàng trên website
- Khác biệt về use cases, permissions, và lifecycle

### 2. Different Authentication Requirements
- **System Users**: 
  - Complex RBAC
  - Cross-tenant access (có thể)
  - Backend/CMS access
  
- **Customers**:
  - Simple role (customer/guest)
  - Tenant-specific only
  - Frontend/store access only

### 3. Scalability Benefits
- Customer data thường grow nhanh hơn system users
- Có thể scale và optimize riêng
- Different caching strategies

## Customer Module Components

### 1. Authentication Component

#### Features
- Email/password registration
- Social login (Google, Facebook, Apple)
- Phone number authentication (OTP)
- Guest checkout support
- Remember me functionality
- Multi-device session management

#### Security Measures
- Rate limiting per IP/email
- Account lockout after failed attempts
- Email/phone verification
- Two-factor authentication (optional)
- Suspicious login detection

### 2. Profile Management Component

#### Customer Profile Data
- Basic info (name, DOB, gender)
- Contact info (email, phone, addresses)
- Preferences (language, currency, notifications)
- Loyalty/rewards info
- Purchase history reference
- Wishlists and favorites

#### Address Management
- Multiple shipping addresses
- Default billing/shipping address
- Address validation
- Geolocation support

### 3. Customer Segmentation

#### Automatic Segments
- New customers
- Returning customers
- VIP customers (based on spending)
- Inactive customers
- Geographic segments

#### Custom Segments
- Tag-based grouping
- Behavior-based (frequent buyers, cart abandoners)
- Demographic-based
- Purchase pattern-based

## Integration with E-commerce Modules

### 1. Cart Module Integration

```
Customer → Cart relationship:
- Persistent cart for logged-in customers
- Cart merge on login (guest → registered)
- Abandoned cart tracking
- Cart recovery emails
```

### 2. Order Module Integration

```
Customer → Order relationship:
- Order history
- Order tracking
- Reorder functionality
- Order-based recommendations
```

### 3. Payment Module Integration

```
Customer → Payment methods:
- Saved payment methods
- Payment preferences
- Billing addresses
- Transaction history
```

### 4. Product Module Integration

```
Customer → Product interactions:
- Product reviews and ratings
- Q&A participation
- Recently viewed products
- Product recommendations
```

## Multi-Tenant Considerations

### 1. Data Isolation

```
Customer Registration Scenarios:

Scenario A: Complete Isolation (Recommended)
- Customer registers separately for each tenant/store
- No data sharing between tenants
- Simple and secure

Scenario B: Shared Identity (Complex)
- Central authentication service
- Customer chooses which tenants to share data with
- Requires consent management
```

### 2. Tenant Detection

```
Methods for identifying tenant context:

1. Domain-based
   - store1.example.com → Tenant 1
   - store2.example.com → Tenant 2

2. Path-based
   - example.com/store1 → Tenant 1
   - example.com/store2 → Tenant 2

3. Header-based
   - X-Tenant-ID: tenant_123
```

### 3. Cross-Tenant Customer Scenarios

```
Use Cases:

1. Marketplace Model
   - Single customer account
   - Multiple vendor stores (tenants)
   - Unified checkout

2. Brand Portfolio
   - Customer can use same account
   - Different brands (tenants)
   - Shared loyalty points

3. Franchise Model
   - Each franchise is a tenant
   - Customers are tenant-specific
   - No cross-franchise access
```

## API Design Patterns

### 1. Customer Authentication Flow

```
1. Registration
   POST /api/customer/auth/register
   - Validates tenant context
   - Creates customer in tenant scope
   - Sends verification email

2. Login
   POST /api/customer/auth/login
   - Validates credentials against tenant
   - Returns JWT with tenant_id claim
   - Creates session record

3. Token Refresh
   POST /api/customer/auth/refresh
   - Validates refresh token
   - Checks tenant context
   - Issues new access token
```

### 2. Customer Context in Requests

```
JWT Token Claims:
{
  "sub": "customer_123",
  "tenant_id": "tenant_456",
  "type": "customer",
  "email": "<EMAIL>",
  "exp": **********
}

Request Flow:
1. Extract tenant from domain/path
2. Validate JWT token
3. Verify tenant_id in token matches request context
4. Load customer data scoped to tenant
```

## Database Considerations

### 1. Customer Tables with Tenant Scope

All customer-related tables include tenant_id:
- customers
- customer_profiles
- customer_addresses
- customer_sessions
- customer_preferences
- customer_social_accounts
- customer_devices
- customer_notifications

### 2. Indexing Strategy

```
Key Indexes:
- (tenant_id, email) - for login
- (tenant_id, phone) - for phone auth
- (tenant_id, status) - for customer lists
- (tenant_id, created_at) - for reports
- (tenant_id, customer_id, provider) - for social login
```

### 3. Data Retention

```
Policies:
- Active customers: Keep indefinitely
- Inactive customers: Archive after 2 years
- Deleted accounts: Soft delete, purge after 30 days
- Session data: Clean up after expiry
- Guest customers: Convert or purge after 90 days
```

## Performance Optimization

### 1. Caching Strategy

```
Cache Layers:
1. Session cache (Redis)
   - Customer sessions
   - Cart data
   - Recently viewed

2. Profile cache (Redis)
   - Basic customer info
   - Addresses
   - Preferences

3. CDN cache
   - Avatar images
   - Static assets
```

### 2. Query Optimization

```
Common Queries to Optimize:
- Customer login (email + password check)
- Session validation (every request)
- Customer profile with addresses
- Order history pagination
- Customer search and filtering
```

## Security Best Practices

### 1. Authentication Security
- Bcrypt for password hashing
- Secure session token generation
- IP-based rate limiting
- Device fingerprinting

### 2. Data Protection
- PII encryption at rest
- Audit logging for data access
- GDPR compliance tools
- Customer data export/deletion

### 3. API Security
- Customer tokens separate from admin tokens
- Tenant validation on every request
- Input validation and sanitization
- SQL injection prevention

## Monitoring and Analytics

### 1. Customer Metrics
- Registration rate
- Login frequency
- Cart abandonment rate
- Customer lifetime value
- Churn rate

### 2. Security Metrics
- Failed login attempts
- Suspicious activities
- Account takeover attempts
- API abuse patterns

### 3. Performance Metrics
- Authentication response time
- Session validation latency
- Database query performance
- Cache hit rates