# Module Media

Tài liệu này mô tả chi tiết về module Media, chịu trách nhiệm quản lý tất cả các tệp tin đa phương tiện như hình <PERSON>nh, video, và tài liệu trong hệ thống.

## Tổng quan

Module Media không cung cấp các API endpoint trực tiếp ra bên ngoài. Thay vào đó, nó hoạt động như một service nền tảng, cung cấp các chức năng để các module khác (như Blog, User) c<PERSON> thể tải lên, <PERSON><PERSON><PERSON> tr<PERSON>, và quản lý các tệp tin.

Thành phần cốt lõi của module này là `MediaFileService`.

## Chức năng chính của `MediaFileService`

-   **`UploadFile`**: <PERSON><PERSON><PERSON> tệp tin lên hệ thống.
-   **`GetFile`**: L<PERSON>y thông tin chi tiết của một tệp tin.
-   **`UpdateFile`**: <PERSON><PERSON><PERSON> nhật thông tin metadata của tệp (ví dụ: tiêu đề, mô tả).
-   **`DeleteFile`**: Xóa một tệp tin.
-   **`ListFiles`**: Liệt kê các tệp tin với bộ lọc và phân trang.
-   **`MoveFile`**: Di chuyển tệp tin sang một thư mục khác.
-   **`CopyFile`**: Tạo một bản sao của tệp tin.
-   **`SearchFiles`**: Tìm kiếm tệp tin.

---

## Luồng tải tệp tin lên (Upload Flow)

Đây là chức năng quan trọng nhất, cho phép các module khác lưu trữ tệp tin.

### Mermaid Diagram

```mermaid
flowchart TD
    A[Module khác, vd: Blog] --> B{Gọi MediaFileService.UploadFile};
    B --> C{Validate tệp tin};
    C -->|Hợp lệ| D[Trích xuất metadata (kích thước, loại)];
    C -->|Không hợp lệ| E[Trả về lỗi];
    D --> F{Lưu tệp vào Storage (S3, Local, ...)}
    F --> G{Tạo thumbnails nếu là ảnh};
    G --> H{Lưu thông tin vào Database};
    H --> I[Gắn tags (nếu có)];
    I --> J[Trả về thông tin tệp đã lưu];
    J --> A;
```

### Mô tả luồng hoạt động

1.  Một module khác (ví dụ: `BlogPostService` khi người dùng thêm ảnh vào bài viết) gọi hàm `MediaFileService.UploadFile` và truyền vào dữ liệu tệp.
2.  **MediaFileService** thực hiện kiểm tra tính hợp lệ của tệp (kích thước, loại tệp được cho phép).
3.  Nếu tệp hợp lệ, service sẽ trích xuất các thông tin metadata cần thiết.
4.  Tệp tin được lưu trữ vật lý vào một nơi lưu trữ như Amazon S3 hoặc trên ổ đĩa cục bộ, tùy theo cấu hình.
5.  Nếu tệp là hình ảnh, hệ thống sẽ tự động tạo ra các phiên bản ảnh thu nhỏ (thumbnails) với các kích thước khác nhau để tối ưu hóa việc hiển thị.
6.  Thông tin metadata của tệp (tên, đường dẫn, kích thước, loại, người tải lên, v.v.) được lưu vào bảng `media_files` trong database.
7.  Nếu có tags được cung cấp, chúng sẽ được gắn vào tệp tin.
8.  Service trả về đối tượng `MediaFile` chứa thông tin đầy đủ của tệp vừa được tải lên cho module đã gọi nó.
