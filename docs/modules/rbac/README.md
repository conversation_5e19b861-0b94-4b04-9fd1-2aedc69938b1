# Module RBAC (Role-Based Access Control)

Tài liệu này mô tả chi tiết về module RBAC, chịu trách nhiệm quản lý vai trò, quyền hạn và kiểm soát truy cập trong toàn bộ hệ thống.

## Các khái niệm chính

-   **Permission (Quyền)**: <PERSON><PERSON> một hành động cụ thể mà người dùng có thể thực hiện. Ví dụ: `posts.create`, `users.delete`. Mỗi quyền được định nghĩa rõ ràng và thuộc về một module cụ thể.
-   **Role (Vai trò)**: <PERSON><PERSON> một tập hợp các quyền. Ví dụ: vai trò "Editor" có thể có các quyền `posts.create`, `posts.edit`, `posts.publish`. <PERSON>ai trò được định nghĩa trong ngữ cảnh của một tenant.
-   **User-Role Assignment**: Người dùng được gán các vai trò khác nhau. Một người dùng có thể có nhiều vai trò.

## Mục lục

- [1. Quản lý Vai trò (Roles)](#1-quản-lý-vai-trò-roles)
  - [1.1. Lấy danh sách Roles - `GET /rbac/roles`](#11-lấy-danh-sách-roles---get-rbacroles)
  - [1.2. Tạo Role mới - `POST /rbac/roles`](#12-tạo-role-mới---post-rbacroles)
  - [1.3. Gán Quyền cho Role - `POST /rbac/roles/{id}/permissions`](#13-gán-quyền-cho-role---post-rbacrolesidpermissions)
- [2. Quản lý Quyền (Permissions)](#2-quản-lý-quyền-permissions)
  - [2.1. Lấy danh sách Permissions - `GET /rbac/admin/permissions`](#21-lấy-danh-sách-permissions---get-rbacadminpermissions)
- [3. Gán Vai trò cho Người dùng](#3-gán-vai-trò-cho-người-dùng)
  - [3.1. Gán Role cho User - `POST /rbac/users/{id}/roles`](#31-gán-role-cho-user---post-rbacusersidroles)

---

## 1. Quản lý Vai trò (Roles)

Các API để tạo và quản lý các vai trò trong một tenant.

### 1.1. Lấy danh sách Roles - `GET /rbac/roles`

```mermaid
sequenceDiagram
    Admin->>+API: GET /rbac/roles
    API->>+RoleHandler: ListRoles(c)
    RoleHandler->>+RoleService: GetRolesByTenant(tenantID, ...)
    RoleService->>+RoleRepo: FindByTenantID(tenantID)
    RoleRepo-->>-RoleService: roles[]
    RoleService-->>-RoleHandler: roles[]
    RoleHandler-->>-API: 200 OK
    API-->>-Admin: {data: [...]}
```

### 1.2. Tạo Role mới - `POST /rbac/roles`

```mermaid
sequenceDiagram
    Admin->>+API: POST /rbac/roles (body: {name, ...})
    API->>+RoleHandler: CreateRole(c)
    RoleHandler->>+RoleService: CreateRole(req)
    RoleService->>+RoleRepo: Create(role)
    RoleRepo-->>-RoleService: createdRole
    RoleService-->>-RoleHandler: createdRole
    RoleHandler-->>-API: 201 Created
    API-->>-Admin: {data: {id, name, ...}}
```

### 1.3. Gán Quyền cho Role - `POST /rbac/roles/{id}/permissions`

```mermaid
sequenceDiagram
    Admin->>+API: POST /roles/1/permissions (body: {permission_ids: [1,2,3]})
    API->>+RoleHandler: AssignPermissionsToRole(c)
    RoleHandler->>+RoleService: AssignPermissionsToRole(1, [1,2,3])
    RoleService->>+RolePermissionRepo: BulkCreate(...)
    RolePermissionRepo-->>-RoleService: (ok)
    Note right of RoleService: Xóa cache RBAC
    RoleService-->>-RoleHandler: (ok)
    RoleHandler-->>-API: 200 OK
    API-->>-Admin: {message: "Permissions assigned"}
```

---

## 2. Quản lý Quyền (Permissions)

Các API để xem các quyền có sẵn trong hệ thống.

### 2.1. Lấy danh sách Permissions - `GET /rbac/admin/permissions`

```mermaid
sequenceDiagram
    Admin->>+API: GET /rbac/admin/permissions
    API->>+PermissionHandler: ListPermissions(c)
    PermissionHandler->>+PermissionService: GetActivePermissions()
    PermissionService->>+PermissionRepo: FindAllActive()
    PermissionRepo-->>-PermissionService: permissions[]
    PermissionService-->>-PermissionHandler: permissions[]
    PermissionHandler-->>-API: 200 OK
    API-->>-Admin: {data: [...]}
```

---

## 3. Gán Vai trò cho Người dùng

### 3.1. Gán Role cho User - `POST /rbac/users/{id}/roles`

```mermaid
sequenceDiagram
    Admin->>+API: POST /users/123/roles (body: {role_id: 1})
    API->>+UserRoleHandler: AssignRole(c)
    UserRoleHandler->>+UserRoleService: AssignRoleToUser(123, 1)
    UserRoleService->>+UserRoleRepo: Create(userRole)
    UserRoleRepo-->>-UserRoleService: (ok)
    Note right of UserRoleService: Xóa cache RBAC cho user 123
    UserRoleService-->>-UserRoleHandler: (ok)
    UserRoleHandler-->>-API: 200 OK
    API-->>-Admin: {message: "Role assigned"}
```
