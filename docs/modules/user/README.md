# Module <PERSON> dù<PERSON> (User)

Tài liệu này mô tả chi tiết về module <PERSON><PERSON><PERSON><PERSON> dù<PERSON>, bao gồm c<PERSON><PERSON> chức năng quản lý người dùng, <PERSON><PERSON>, v<PERSON> các mối quan hệ trong hệ thống multi-tenant.

## <PERSON><PERSON><PERSON>

- [1. <PERSON><PERSON><PERSON><PERSON> lý người dùng (User Management)](#1-quản-lý-người-dùng-user-management)
  - [1.1. <PERSON><PERSON><PERSON> danh sách người dùng - `GET /users`](#11-lấy-danh-sách-người-dùng---get-users)
  - [1.2. Tạo người dùng mới - `POST /users`](#12-tạo-người-dùng-mới---post-users)
  - [1.3. <PERSON><PERSON><PERSON> thông tin chi tiết người dùng - `GET /users/{id}`](#13-lấy-thông-tin-chi-tiết-người-dùng---get-usersid)
  - [1.4. <PERSON><PERSON><PERSON> nhật người dùng - `PUT /users/{id}`](#14-cập-nhật-người-dùng---put-usersid)
  - [1.5. Xóa người dùng - `DELETE /users/{id}`](#15-xóa-người-dùng---delete-usersid)
- [2. Quản lý hồ sơ (User Profile)](#2-quản-lý-hồ-sơ-user-profile)
  - [2.1. Lấy hồ sơ người dùng - `GET /user-profiles/user/{user_id}`](#21-lấy-hồ-sơ-người-dùng---get-user-profilesuseruser_id)
  - [2.2. Cập nhật hồ sơ người dùng - `PUT /user-profiles/user/{user_id}`](#22-cập-nhật-hồ-sơ-người-dùng---put-user-profilesuseruser_id)
- [3. Quản lý thành viên Tenant (Tenant Membership)](#3-quản-lý-thành-viên-tenant-tenant-membership)
- [4. Mời người dùng (User Invitation)](#4-mời-người-dùng-user-invitation)

---

## 1. Quản lý người dùng (User Management)

Các API cơ bản để thực hiện các thao tác CRUD (Tạo, Đọc, Cập nhật, Xóa) trên đối tượng người dùng.

### 1.1. Lấy danh sách người dùng - `GET /users`

Lấy danh sách người dùng với các tùy chọn lọc và phân trang.

```mermaid
sequenceDiagram
    Admin->>+API: GET /users?limit=10&status=active
    API->>+UserHandler: ListUsers(c)
    UserHandler->>UserHandler: Parse query params (pagination, filters)
    UserHandler->>+UserService: List(filter)
    UserService->>+UserRepo: List(pagination)
    UserRepo-->>-UserService: users[], paginationResponse
    UserService-->>-UserHandler: UserListResponse
    UserHandler-->>-API: 200 OK
    API-->>-Admin: {users: [...], pagination: {...}}
```

### 1.2. Tạo người dùng mới - `POST /users`

Tạo một người dùng mới trong hệ thống.

```mermaid
sequenceDiagram
    Admin->>+API: POST /users (body: {email, password, ...})
    API->>+UserHandler: CreateUser(c)
    UserHandler->>UserHandler: Validate input
    UserHandler->>+UserService: Create(input)
    UserService->>UserService: Validate email/username uniqueness
    UserService->>UserService: Hash password
    UserService->>+UserRepo: Create(user)
    UserRepo-->>-UserService: createdUser
    UserService-->>-UserHandler: createdUser
    UserHandler-->>-API: 201 Created
    API-->>-Admin: {id, email, ...}
```

### 1.3. Lấy thông tin chi tiết người dùng - `GET /users/{id}`

Lấy thông tin chi tiết của một người dùng cụ thể bằng ID.

```mermaid
sequenceDiagram
    Admin->>+API: GET /users/123
    API->>+UserHandler: GetUser(c)
    UserHandler->>+UserService: GetByID(123)
    UserService->>+UserRepo: GetByID(123)
    UserRepo-->>-UserService: user
    UserService-->>-UserHandler: user
    UserHandler-->>-API: 200 OK
    API-->>-Admin: {id, email, first_name, ...}
```

### 1.4. Cập nhật người dùng - `PUT /users/{id}`

Cập nhật thông tin cho một người dùng đã tồn tại.

```mermaid
sequenceDiagram
    Admin->>+API: PUT /users/123 (body: {first_name, ...})
    API->>+UserHandler: UpdateUser(c)
    UserHandler->>+UserService: Update(123, input)
    UserService->>+UserRepo: GetByID(123)
    UserRepo-->>-UserService: existingUser
    UserService->>UserService: Update user fields
    UserService->>+UserRepo: Update(user)
    UserRepo-->>-UserService: updatedUser
    UserService-->>-UserHandler: updatedUser
    UserHandler-->>-API: 200 OK
    API-->>-Admin: {id, email, first_name, ...}
```

### 1.5. Xóa người dùng - `DELETE /users/{id}`

Xóa mềm (soft delete) một người dùng khỏi hệ thống.

```mermaid
sequenceDiagram
    Admin->>+API: DELETE /users/123
    API->>+UserHandler: DeleteUser(c)
    UserHandler->>+UserService: Delete(123)
    UserService->>+UserRepo: SoftDelete(123)
    UserRepo-->>-UserService: (ok)
    UserService-->>-UserHandler: (ok)
    UserHandler-->>-API: 204 No Content
    API-->>-Admin: (empty response)
```

---

## 2. Quản lý hồ sơ (User Profile)

Các API để quản lý các thông tin mở rộng của người dùng như tiểu sử, kỹ năng, địa chỉ, v.v.

### 2.1. Lấy hồ sơ người dùng - `GET /user-profiles/user/{user_id}`

```mermaid
sequenceDiagram
    User->>+API: GET /user-profiles/user/123
    API->>+UserProfileHandler: GetUserProfile(c)
    UserProfileHandler->>+UserProfileService: GetByUserID(123)
    UserProfileService->>+UserProfileRepo: GetByUserID(123)
    UserProfileRepo-->>-UserProfileService: userProfile
    UserProfileService-->>-UserProfileHandler: userProfile
    UserProfileHandler-->>-API: 200 OK
    API-->>-User: {bio, location, skills, ...}
```

### 2.2. Cập nhật hồ sơ người dùng - `PUT /user-profiles/user/{user_id}`

```mermaid
sequenceDiagram
    User->>+API: PUT /user-profiles/user/123 (body: {bio, ...})
    API->>+UserProfileHandler: UpdateUserProfile(c)
    UserProfileHandler->>+UserProfileService: Update(123, input)
    UserProfileService->>+UserProfileRepo: GetByUserID(123)
    UserProfileRepo-->>-UserProfileService: existingProfile
    UserProfileService->>UserProfileService: Update profile fields
    UserProfileService->>+UserProfileRepo: Update(profile)
    UserProfileRepo-->>-UserProfileService: updatedProfile
    UserProfileService-->>-UserProfileHandler: updatedProfile
    UserProfileHandler-->>-API: 200 OK
    API-->>-User: {bio, location, skills, ...}
```

---

## 3. Quản lý thành viên Tenant (Tenant Membership)

(Sẽ được bổ sung chi tiết trong tài liệu của module `Tenant`)

---

## 4. Mời người dùng (User Invitation)

(Sẽ được bổ sung chi tiết trong một tài liệu riêng)
