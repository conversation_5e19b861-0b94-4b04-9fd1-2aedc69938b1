# Module Blog

Tài liệu này mô tả chi tiết về module Blog, bao gồm các chức năng quản lý <PERSON><PERSON><PERSON> viế<PERSON> (Posts), <PERSON><PERSON> (Categories), <PERSON><PERSON> <PERSON> (Tags).

## <PERSON><PERSON><PERSON> lụ<PERSON>

- [1. <PERSON>u<PERSON><PERSON> lý <PERSON>à<PERSON> viết (Posts)](#1-quản-lý-bài-viết-posts)
  - [1.1. <PERSON><PERSON><PERSON> danh sách bài viết - `GET /blog/posts`](#11-lấy-danh-sách-bài-viết---get-blogposts)
  - [1.2. <PERSON><PERSON><PERSON> bài viết mới - `POST /blog/posts`](#12-tạo-bài-viết-mới---post-blogposts)
  - [1.3. L<PERSON>y chi tiết bài viết - `GET /blog/posts/{id}`](#13-lấy-chi-tiết-bài-viết---get-blogpostsid)
  - [1.4. <PERSON><PERSON><PERSON> nh<PERSON>t bài viết - `PUT /blog/posts/{id}`](#14-cập-nh<PERSON>t-bà<PERSON>-viết---put-blogpostsid)
  - [1.5. Xóa bài viết - `DELETE /blog/posts/{id}`](#15-xóa-bài-viết---delete-blogpostsid)
- [2. Quản lý Danh mục (Categories)](#2-quản-lý-danh-mục-categories)
  - [2.1. Lấy danh sách danh mục - `GET /blog/categories`](#21-lấy-danh-sách-danh-mục---get-blogcategories)
  - [2.2. Tạo danh mục mới - `POST /blog/categories`](#22-tạo-danh-mục-mới---post-blogcategories)
- [3. Quản lý Thẻ (Tags)](#3-quản-lý-thẻ-tags)
  - [3.1. Lấy danh sách thẻ - `GET /blog/tags`](#31-lấy-danh-sách-thẻ---get-blogtags)
  - [3.2. Tạo thẻ mới - `POST /blog/tags`](#32-tạo-thẻ-mới---post-blogtags)

---

## 1. Quản lý Bài viết (Posts)

Các API để quản lý nội dung bài viết.

### 1.1. Lấy danh sách bài viết - `GET /blog/posts`

Lấy danh sách các bài viết với các bộ lọc (filter) và phân trang (pagination).

```mermaid
sequenceDiagram
    User->>+API: GET /blog/posts?status=published
    API->>+BlogPostHandler: ListPosts(c)
    BlogPostHandler->>BlogPostHandler: Parse query params
    BlogPostHandler->>+BlogPostService: List(filter)
    BlogPostService->>+BlogPostRepo: Find(filter)
    BlogPostRepo-->>-BlogPostService: posts[], total
    BlogPostService-->>-BlogPostHandler: response
    BlogPostHandler-->>-API: 200 OK
    API-->>-User: {posts: [...], pagination: {...}}
```

### 1.2. Tạo bài viết mới - `POST /blog/posts`

Tạo một bài viết mới với các thông tin cơ bản.

```mermaid
sequenceDiagram
    User->>+API: POST /blog/posts (body: {title, content, ...})
    API->>+BlogPostHandler: CreatePost(c)
    BlogPostHandler->>BlogPostHandler: Validate input & get TenantID
    BlogPostHandler->>+BlogPostService: Create(req)
    BlogPostService->>+BlogPostRepo: Create(post)
    BlogPostRepo-->>-BlogPostService: createdPost
    BlogPostService-->>-BlogPostHandler: createdPost
    BlogPostHandler-->>-API: 201 Created
    API-->>-User: {id, title, ...}
```

### 1.3. Lấy chi tiết bài viết - `GET /blog/posts/{id}`

Lấy thông tin chi tiết của một bài viết dựa trên ID.

```mermaid
sequenceDiagram
    User->>+API: GET /blog/posts/123
    API->>+BlogPostHandler: GetPost(c)
    BlogPostHandler->>+BlogPostService: GetByID(tenantID, 123)
    BlogPostService->>+BlogPostRepo: GetByID(tenantID, 123)
    BlogPostRepo-->>-BlogPostService: post
    BlogPostService-->>-BlogPostHandler: post
    BlogPostHandler-->>-API: 200 OK
    API-->>-User: {id, title, content, ...}
```

### 1.4. Cập nhật bài viết - `PUT /blog/posts/{id}`

Cập nhật nội dung, tiêu đề, hoặc các thuộc tính khác của bài viết.

```mermaid
sequenceDiagram
    User->>+API: PUT /blog/posts/123 (body: {title, ...})
    API->>+BlogPostHandler: UpdatePost(c)
    BlogPostHandler->>+BlogPostService: Update(tenantID, 123, req)
    BlogPostService->>+BlogPostRepo: GetByID(tenantID, 123)
    BlogPostRepo-->>-BlogPostService: existingPost
    BlogPostService->>BlogPostService: Update post fields
    BlogPostService->>+BlogPostRepo: Update(post)
    BlogPostRepo-->>-BlogPostService: updatedPost
    BlogPostService-->>-BlogPostHandler: updatedPost
    BlogPostHandler-->>-API: 200 OK
    API-->>-User: {id, title, ...}
```

### 1.5. Xóa bài viết - `DELETE /blog/posts/{id}`

Xóa một bài viết khỏi hệ thống.

```mermaid
sequenceDiagram
    User->>+API: DELETE /blog/posts/123
    API->>+BlogPostHandler: DeletePost(c)
    BlogPostHandler->>+BlogPostService: Delete(tenantID, 123)
    BlogPostService->>+BlogPostRepo: Delete(tenantID, 123)
    BlogPostRepo-->>-BlogPostService: (ok)
    BlogPostService-->>-BlogPostHandler: (ok)
    BlogPostHandler-->>-API: 200 OK
    API-->>-User: (empty response)
```

---

## 2. Quản lý Danh mục (Categories)

Các API để tổ chức bài viết vào các danh mục phân cấp.

### 2.1. Lấy danh sách danh mục - `GET /blog/categories`

```mermaid
sequenceDiagram
    User->>+API: GET /blog/categories
    API->>+BlogCategoryHandler: ListCategories(c)
    BlogCategoryHandler->>+BlogCategoryService: List(filter)
    BlogCategoryService->>+BlogCategoryRepo: Find(filter)
    BlogCategoryRepo-->>-BlogCategoryService: categories[], total
    BlogCategoryService-->>-BlogCategoryHandler: response
    BlogCategoryHandler-->>-API: 200 OK
    API-->>-User: {categories: [...], pagination: {...}}
```

### 2.2. Tạo danh mục mới - `POST /blog/categories`

```mermaid
sequenceDiagram
    User->>+API: POST /blog/categories (body: {name, ...})
    API->>+BlogCategoryHandler: CreateCategory(c)
    BlogCategoryHandler->>+BlogCategoryService: Create(req)
    BlogCategoryService->>+BlogCategoryRepo: Create(category)
    BlogCategoryRepo-->>-BlogCategoryService: createdCategory
    BlogCategoryService-->>-BlogCategoryHandler: createdCategory
    BlogCategoryHandler-->>-API: 201 Created
    API-->>-User: {id, name, ...}
```

---

## 3. Quản lý Thẻ (Tags)

Các API để gắn thẻ (tag) cho bài viết, giúp cho việc tìm kiếm và phân loại.

### 3.1. Lấy danh sách thẻ - `GET /blog/tags`

```mermaid
sequenceDiagram
    User->>+API: GET /blog/tags
    API->>+BlogTagHandler: ListTags(c)
    BlogTagHandler->>+BlogTagService: List(filter)
    BlogTagService->>+BlogTagRepo: Find(filter)
    BlogTagRepo-->>-BlogTagService: tags[], total
    BlogTagService-->>-BlogTagHandler: response
    BlogTagHandler-->>-API: 200 OK
    API-->>-User: {tags: [...], pagination: {...}}
```

### 3.2. Tạo thẻ mới - `POST /blog/tags`

```mermaid
sequenceDiagram
    User->>+API: POST /blog/tags (body: {name, ...})
    API->>+BlogTagHandler: CreateTag(c)
    BlogTagHandler->>+BlogTagService: Create(req)
    BlogTagService->>+BlogTagRepo: Create(tag)
    BlogTagRepo-->>-BlogTagService: createdTag
    BlogTagService-->>-BlogTagHandler: createdTag
    BlogTagHandler-->>-API: 201 Created
    API-->>-User: {id, name, ...}
```
