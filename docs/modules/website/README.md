# Module Website

Tài liệu này mô tả chi tiết về module Website, chịu trách nhiệm quản lý các trang web (website) thuộc về một tenant. Mỗi tenant có thể sở hữu nhiều website.

## <PERSON><PERSON><PERSON> lục

- [1. Quản lý Website](#1-quản-lý-website)
  - [1.1. <PERSON><PERSON><PERSON> danh sách Website - `GET /websites`](#11-lấy-danh-sách-website---get-websites)
  - [1.2. Tạo Website mới - `POST /websites`](#12-tạo-website-mới---post-websites)
  - [1.3. Lấy thông tin Website - `GET /websites/{id}`](#13-lấy-thông-tin-website---get-websitesid)
  - [1.4. Cập nhật Website - `PUT /websites/{id}`](#14-cập-nhật-website---put-websitesid)
  - [1.5. Xóa Website - `DELETE /websites/{id}`](#15-xóa-website---delete-websitesid)

---

## 1. Quản lý Website

Các API để thực hiện các thao tác CRUD trên đối tượng Website.

### 1.1. Lấy danh sách Website - `GET /websites`

Lấy danh sách tất cả các website thuộc về tenant hiện tại.

```mermaid
sequenceDiagram
    User->>+API: GET /websites
    API->>+WebsiteHandler: ListWebsites(c)
    WebsiteHandler->>WebsiteHandler: Get TenantID from context
    WebsiteHandler->>+WebsiteService: ListWebsites(tenantID, filter)
    WebsiteService->>+WebsiteRepo: FindByTenantID(tenantID, filter)
    WebsiteRepo-->>-WebsiteService: websites[]
    WebsiteService-->>-WebsiteHandler: websites[]
    WebsiteHandler-->>-API: 200 OK
    API-->>-User: {websites: [...]}
```

### 1.2. Tạo Website mới - `POST /websites`

Tạo một website mới cho tenant hiện tại.

```mermaid
sequenceDiagram
    User->>+API: POST /websites (body: {name, domain, ...})
    API->>+WebsiteHandler: CreateWebsite(c)
    WebsiteHandler->>WebsiteHandler: Validate input & get TenantID
    WebsiteHandler->>+WebsiteService: CreateWebsite(tenantID, req)
    WebsiteService->>+WebsiteRepo: Create(website)
    WebsiteRepo-->>-WebsiteService: createdWebsite
    WebsiteService-->>-WebsiteHandler: createdWebsite
    WebsiteHandler-->>-API: 201 Created
    API-->>-User: {id, name, domain, ...}
```

### 1.3. Lấy thông tin Website - `GET /websites/{id}`

Lấy thông tin chi tiết của một website cụ thể.

```mermaid
sequenceDiagram
    User->>+API: GET /websites/1
    API->>+WebsiteHandler: GetWebsite(c)
    WebsiteHandler->>+WebsiteService: GetWebsite(tenantID, 1)
    WebsiteService->>+WebsiteRepo: GetByID(tenantID, 1)
    WebsiteRepo-->>-WebsiteService: website
    WebsiteService-->>-WebsiteHandler: website
    WebsiteHandler-->>-API: 200 OK
    API-->>-User: {id, name, domain, ...}
```

### 1.4. Cập nhật Website - `PUT /websites/{id}`

Cập nhật thông tin của một website.

```mermaid
sequenceDiagram
    User->>+API: PUT /websites/1 (body: {name, ...})
    API->>+WebsiteHandler: UpdateWebsite(c)
    WebsiteHandler->>+WebsiteService: UpdateWebsite(tenantID, 1, req)
    WebsiteService->>+WebsiteRepo: GetByID(tenantID, 1)
    WebsiteRepo-->>-WebsiteService: existingWebsite
    WebsiteService->>WebsiteService: Update website fields
    WebsiteService->>+WebsiteRepo: Update(website)
    WebsiteRepo-->>-WebsiteService: updatedWebsite
    WebsiteService-->>-WebsiteHandler: updatedWebsite
    WebsiteHandler-->>-API: 200 OK
    API-->>-User: {id, name, ...}
```

### 1.5. Xóa Website - `DELETE /websites/{id}`

Xóa một website.

```mermaid
sequenceDiagram
    User->>+API: DELETE /websites/1
    API->>+WebsiteHandler: DeleteWebsite(c)
    WebsiteHandler->>+WebsiteService: DeleteWebsite(tenantID, 1)
    WebsiteService->>+WebsiteRepo: Delete(tenantID, 1)
    WebsiteRepo-->>-WebsiteService: (ok)
    WebsiteService-->>-WebsiteHandler: (ok)
    WebsiteHandler-->>-API: 200 OK
    API-->>-User: {message: "Website deleted"}
```
