# Module AI

Tài liệu này mô tả chi tiết về module AI, chịu trách nhiệm quản lý, xử lý và theo dõi các yêu cầu đến các mô hình ngôn ngữ lớn (LLM).

## <PERSON><PERSON><PERSON> lục

- [1. <PERSON><PERSON><PERSON><PERSON> lý Yêu cầu AI (AI Requests)](#1-qu<PERSON>n-lý-yêu-cầu-ai-ai-requests)
  - [1.1. <PERSON><PERSON><PERSON> danh sách yêu cầu - `GET /ai/requests`](#11-lấy-danh-sách-yêu-cầu---get-airequests)
  - [1.2. Tạo yêu cầu mới - `POST /ai/requests`](#12-tạo-yêu-cầu-mới---post-airequests)
  - [1.3. <PERSON><PERSON> lý yêu cầu - `POST /ai/requests/{id}/process`](#13-xử-lý-yêu-cầu---post-airequestsidprocess)

---

## 1. <PERSON><PERSON><PERSON><PERSON> lý Yêu cầu <PERSON> (AI Requests)

Các <PERSON> để tạo, theo dõi và quản lý các yêu cầu được gửi đến AI.

### 1.1. Lấy danh sách yêu cầu - `GET /ai/requests`

Lấy danh sách các yêu cầu AI đã được tạo, hỗ trợ lọc và phân trang.

```mermaid
sequenceDiagram
    User->>+API: GET /ai/requests?status=completed
    API->>+AIRequestHandler: ListRequests(c)
    AIRequestHandler->>AIRequestHandler: Parse query params
    AIRequestHandler->>+AIRequestService: ListRequestsWithRelations(input)
    AIRequestService->>+AIRequestRepo: Find(filter)
    AIRequestRepo-->>-AIRequestService: requests[], pagination
    AIRequestService-->>-AIRequestHandler: response
    AIRequestHandler-->>-API: 200 OK
    API-->>-User: {data: [...], pagination: {...}}
```

### 1.2. Tạo yêu cầu mới - `POST /ai/requests`

Tạo một yêu cầu mới để gửi đến AI. Yêu cầu này ban đầu sẽ ở trạng thái `pending`.

```mermaid
sequenceDiagram
    User->>+API: POST /ai/requests (body: {prompt, ...})
    API->>+AIRequestHandler: CreateRequest(c)
    AIRequestHandler->>+AIRequestService: CreateRequest(input)
    AIRequestService->>+AIRequestRepo: Create(request)
    AIRequestRepo-->>-AIRequestService: createdRequest
    AIRequestService-->>-AIRequestHandler: createdRequest
    AIRequestHandler-->>-API: 201 Created
    API-->>-User: {data: {id, status: "pending", ...}}
```

### 1.3. Xử lý yêu cầu - `POST /ai/requests/{id}/process`

Kích hoạt quá trình xử lý một yêu cầu đang chờ. Service sẽ gửi prompt đến mô hình AI và lưu lại kết quả.

```mermaid
sequenceDiagram
    System->>+API: POST /ai/requests/123/process
    API->>+AIRequestHandler: ProcessRequest(c)
    AIRequestHandler->>+AIRequestService: ProcessRequest(123)
    AIRequestService->>+AIRequestRepo: GetByID(123)
    AIRequestRepo-->>-AIRequestService: request
    Note right of AIRequestService: Gửi prompt đến AI Model (External)
    AIRequestService->>+AIRequestRepo: Update(request with response)
    AIRequestRepo-->>-AIRequestService: updatedRequest
    AIRequestService-->>-AIRequestHandler: updatedRequest
    AIRequestHandler-->>-API: 200 OK
    API-->>-System: {data: {id, status: "completed", response: "...", ...}}
```
