# Module Tenant

Tài liệu này mô tả chi tiết về module Tenant, chịu trách nhiệm quản lý các không gian làm việc (tenant) riêng biệt trong hệ thống.

## <PERSON><PERSON><PERSON> lục

- [1. <PERSON><PERSON><PERSON><PERSON> lý Tenant](#1-quản-lý-tenant)
  - [1.1. <PERSON><PERSON><PERSON> danh sách Tenant - `GET /tenants`](#11-lấy-danh-sách-tenant---get-tenants)
  - [1.2. T<PERSON><PERSON> Tenant mới - `POST /tenants`](#12-tạo-tenant-mới---post-tenants)
  - [1.3. <PERSON><PERSON><PERSON> thông tin Tenant - `GET /tenants/{id}`](#13-lấy-thông-tin-tenant---get-tenantsid)
  - [1.4. <PERSON><PERSON><PERSON> nhật Tenant - `PUT /tenants/{id}`](#14-cập-nhật-tenant---put-tenantsid)
  - [1.5. <PERSON><PERSON><PERSON>ant - `DELETE /tenants/{id}`](#15-xóa-tenant---delete-tenantsid)

---

## 1. <PERSON><PERSON><PERSON><PERSON> lý Tenant

Các API để thực hiện các thao tác CRUD trên đối tượng Tenant.

### 1.1. Lấy danh sách Tenant - `GET /tenants`

Lấy danh sách tất cả các tenant trong hệ thống, hỗ trợ lọc và phân trang.

```mermaid
sequenceDiagram
    Admin->>+API: GET /tenants?status=active
    API->>+TenantHandler: ListTenants(c)
    TenantHandler->>TenantHandler: Parse query params
    TenantHandler->>+TenantService: List(filter)
    TenantService->>+TenantRepo: Find(filter)
    TenantRepo-->>-TenantService: tenants[], total
    TenantService-->>-TenantHandler: response
    TenantHandler-->>-API: 200 OK
    API-->>-Admin: {tenants: [...], pagination: {...}}
```

### 1.2. Tạo Tenant mới - `POST /tenants`

Tạo một tenant mới. Thường được gọi trong quá trình onboarding của một khách hàng mới.

```mermaid
sequenceDiagram
    Admin/System->>+API: POST /tenants (body: {name, domain, ...})
    API->>+TenantHandler: CreateTenant(c)
    TenantHandler->>TenantHandler: Validate input
    TenantHandler->>+TenantService: Create(input)
    TenantService->>+TenantRepo: Create(tenant)
    TenantRepo-->>-TenantService: createdTenant
    TenantService-->>-TenantHandler: createdTenant
    TenantHandler-->>-API: 201 Created
    API-->>-Admin/System: {id, name, domain, ...}
```

### 1.3. Lấy thông tin Tenant - `GET /tenants/{id}`

Lấy thông tin chi tiết của một tenant cụ thể bằng ID.

```mermaid
sequenceDiagram
    User->>+API: GET /tenants/1
    API->>+TenantHandler: GetTenant(c)
    TenantHandler->>+TenantService: GetByID(1)
    TenantService->>+TenantRepo: GetByID(1)
    TenantRepo-->>-TenantService: tenant
    TenantService-->>-TenantHandler: tenant
    TenantHandler-->>-API: 200 OK
    API-->>-User: {id, name, domain, ...}
```

### 1.4. Cập nhật Tenant - `PUT /tenants/{id}`

Cập nhật thông tin của một tenant, ví dụ như tên, thông tin liên hệ, v.v.

```mermaid
sequenceDiagram
    Admin->>+API: PUT /tenants/1 (body: {name, ...})
    API->>+TenantHandler: UpdateTenant(c)
    TenantHandler->>+TenantService: Update(1, input)
    TenantService->>+TenantRepo: GetByID(1)
    TenantRepo-->>-TenantService: existingTenant
    TenantService->>TenantService: Update tenant fields
    TenantService->>+TenantRepo: Update(tenant)
    TenantRepo-->>-TenantService: updatedTenant
    TenantService-->>-TenantHandler: updatedTenant
    TenantHandler-->>-API: 200 OK
    API-->>-Admin: {id, name, ...}
```

### 1.5. Xóa Tenant - `DELETE /tenants/{id}`

Xóa một tenant khỏi hệ thống. Đây là một hành động nguy hiểm và thường là xóa mềm (soft delete).

```mermaid
sequenceDiagram
    Admin->>+API: DELETE /tenants/1
    API->>+TenantHandler: DeleteTenant(c)
    TenantHandler->>+TenantService: Delete(1)
    TenantService->>+TenantRepo: Delete(1)
    TenantRepo-->>-TenantService: (ok)
    TenantService-->>-TenantHandler: (ok)
    TenantHandler-->>-API: 204 No Content
    API-->>-Admin: (empty response)
```
