# Module API Key

Tài liệu này mô tả chi tiết về module API Key, chịu trách nhiệ<PERSON> tạo, quản lý và xác thực các khóa API cho các ứng dụng bên ngoài.

## <PERSON><PERSON><PERSON> lục

- [1. Quản lý API Key](#1-quản-lý-api-key)
  - [1.1. <PERSON><PERSON><PERSON> danh sách API Keys - `GET /api-keys`](#11-lấy-danh-sách-api-keys---get-api-keys)
  - [1.2. Tạo API Key mới - `POST /api-keys`](#12-tạo-api-key-mới---post-api-keys)
  - [1.3. Xóa API Key - `DELETE /api-keys/{id}`](#13-xóa-api-key---delete-api-keysid)
- [2. Xác thực bằng API Key](#2-xác-thực-bằng-api-key)

---

## 1. Quản lý API Key

Các API để thực hiện các thao tác CRUD trên đối tượng API Key.

### 1.1. <PERSON><PERSON><PERSON> danh sách API Keys - `GET /api-keys`

Lấy danh sách tất cả các khóa API thuộc về tenant và website hiện tại.

```mermaid
sequenceDiagram
    User->>+API: GET /api-keys
    API->>+APIKeyHandler: ListAPIKeys(c)
    APIKeyHandler->>APIKeyHandler: Get TenantID & WebsiteID
    APIKeyHandler->>+APIKeyService: ListAPIKeys(tenantID, websiteID, filter)
    APIKeyService->>+APIKeyRepo: Find(filter)
    APIKeyRepo-->>-APIKeyService: apiKeys[]
    APIKeyService-->>-APIKeyHandler: response
    APIKeyHandler-->>-API: 200 OK
    API-->>-User: {data: {keys: [...], ...}}
```

### 1.2. Tạo API Key mới - `POST /api-keys`

Tạo một khóa API mới với các quyền (permissions) và phạm vi (scopes) được chỉ định.

```mermaid
sequenceDiagram
    User->>+API: POST /api-keys (body: {name, permissions, ...})
    API->>+APIKeyHandler: CreateAPIKey(c)
    APIKeyHandler->>+APIKeyService: CreateAPIKey(tenantID, ..., req)
    APIKeyService->>APIKeyService: Generate & Hash Key
    APIKeyService->>+APIKeyRepo: Create(apiKey)
    APIKeyRepo-->>-APIKeyService: createdAPIKey
    APIKeyService-->>-APIKeyHandler: createdAPIKey
    APIKeyHandler-->>-API: 201 Created
    API-->>-User: {data: {id, name, key, ...}}
```

### 1.3. Xóa API Key - `DELETE /api-keys/{id}`

Vô hiệu hóa và xóa một khóa API.

```mermaid
sequenceDiagram
    User->>+API: DELETE /api-keys/1
    API->>+APIKeyHandler: DeleteAPIKey(c)
    APIKeyHandler->>+APIKeyService: DeleteAPIKey(tenantID, ..., 1)
    APIKeyService->>+APIKeyRepo: Delete(1)
    APIKeyRepo-->>-APIKeyService: (ok)
    APIKeyService-->>-APIKeyHandler: (ok)
    APIKeyHandler-->>-API: 200 OK
    API-->>-User: {message: "API key deleted"}
```

---

## 2. Xác thực bằng API Key

Module này cung cấp một middleware để xác thực các yêu cầu đến các endpoint được bảo vệ.

```mermaid
sequenceDiagram
    ClientApp->>+API: GET /example/data (Header: X-API-KEY: <key>)
    API->>+APIKeyMiddleware: Authenticate()
    APIKeyMiddleware->>+APIKeyService: ValidateAPIKey(key)
    APIKeyService->>+APIKeyRepo: FindByKey(hashedKey)
    APIKeyRepo-->>-APIKeyService: apiKeyData
    alt Key hợp lệ
        APIKeyService-->>-APIKeyMiddleware: (valid)
        APIKeyMiddleware->>API: Cho phép truy cập
        API-->>ClientApp: 200 OK {data: [...]}
    else Key không hợp lệ
        APIKeyService-->>-APIKeyMiddleware: (invalid)
        APIKeyMiddleware->>API: Từ chối truy cập
        API-->>ClientApp: 401 Unauthorized
    end
```
