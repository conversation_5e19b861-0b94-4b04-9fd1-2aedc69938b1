# Module Cài đặt (Settings)

Tài liệu này mô tả chi tiết về module Cài đặt, một service nền tảng mạnh mẽ để quản lý cấu hình và cài đặt cho toàn bộ ứng dụng.

## Tổng quan

Module Cài đặt không cung cấp các API endpoint độc lập. Thay vào đó, nó cung cấp một `SettingsService` được sử dụng bởi các module khác (ví dụ: `Tenant`) để quản lý các cài đặt ở nhiều cấp độ khác nhau.

Điểm đặc biệt của module này là **kiến trúc phân giải phân cấp (Hierarchical Resolution)**. Một cài đặt có thể được định nghĩa ở nhiều cấp, và hệ thống sẽ tự động tìm và trả về giá trị ở cấp ưu tiên cao nhất.

Thứ tự ưu tiên phân giải: **User > Module > Tenant > System**.

## Chức năng chính của `SettingsService`

-   **`RegisterSchema`**: Đăng ký một "lược đồ" (schema) cho một cài đặt, định nghĩa kiểu dữ liệu, giá trị mặc định, và các quy tắc validation.
-   **`Set`**: Thiết lập giá trị cho một cài đặt ở một cấp cụ thể (ví dụ: `ScopeTenant`).
-   **`Get`**: Lấy giá trị của một cài đặt. Hàm này sẽ tự động thực hiện việc phân giải phân cấp.
-   **`Resolve`**: Tương tự như `Get`, nhưng trả về thêm thông tin chi tiết về nguồn gốc của giá trị (ví dụ: nó được kế thừa từ cấp `System` hay được ghi đè ở cấp `Tenant`).
-   **Các hàm Type-safe**: Cung cấp các hàm tiện ích như `GetString`, `GetInt`, `GetBool` để lấy giá trị một cách an toàn.
-   **`BulkSet` / `BulkGet`**: Thực hiện các thao tác đọc/ghi hàng loạt để tăng hiệu năng.

---

## Luồng phân giải cài đặt (Setting Resolution Flow)

Đây là logic cốt lõi của module, quyết định giá trị nào sẽ được sử dụng.

### Mermaid Diagram

```mermaid
flowchart TD
    A[Module khác gọi SettingsService.Get("site_name")] --> B{Bắt đầu phân giải};
    B --> C{Tìm trong cấp User};
    C -->|Tìm thấy| D[Trả về giá trị của User];
    C -->|Không tìm thấy| E{Tìm trong cấp Module};
    E -->|Tìm thấy| F[Trả về giá trị của Module];
    E -->|Không tìm thấy| G{Tìm trong cấp Tenant};
    G -->|Tìm thấy| H[Trả về giá trị của Tenant];
    G -->|Không tìm thấy| I{Tìm trong cấp System};
    I -->|Tìm thấy| J[Trả về giá trị của System];
    I -->|Không tìm thấy| K{Sử dụng giá trị mặc định từ Schema};
    K -->|Có giá trị mặc định| L[Trả về giá trị mặc định];
    K -->|Không có| M[Trả về lỗi "Not Found"];
```

### Mô tả luồng hoạt động

1.  Một service từ module khác (ví dụ: `TenantService`) gọi `SettingsService.Get(context, "general", "site_name")`.
2.  **SettingsService** bắt đầu quá trình phân giải theo thứ tự ưu tiên.
3.  Nó kiểm tra xem cài đặt `site_name` có được định nghĩa riêng cho **User** hiện tại không. Nếu có, giá trị này được trả về ngay lập tức.
4.  Nếu không, nó tiếp tục kiểm tra ở cấp **Module**.
5.  Nếu vẫn không có, nó kiểm tra ở cấp **Tenant**.
6.  Cuối cùng, nó kiểm tra ở cấp **System** (toàn cục).
7.  Nếu không có giá trị nào được tìm thấy ở tất cả các cấp, service sẽ kiểm tra **Schema** của cài đặt này để xem có giá trị mặc định (`Default`) nào được định nghĩa không.
8.  Nếu có giá trị mặc định, nó sẽ được trả về. Nếu không, service sẽ trả về lỗi `ErrSettingNotFound`.
