# Module SEO

Tài liệu này mô tả chi tiết về module SEO, bao gồm c<PERSON><PERSON> chứ<PERSON> năng quản lý Chu<PERSON>ển hướng (Redirects) và Sơ đồ trang web (Sitemaps).

## <PERSON><PERSON><PERSON> l<PERSON>

- [1. <PERSON>u<PERSON><PERSON> lý <PERSON>ể<PERSON> hướng (Redirects)](#1-quản-lý-chuyển-hướng-redirects)
  - [1.1. <PERSON><PERSON><PERSON> danh sách Redirects - `GET /seo/redirects`](#11-lấy-danh-sách-redirects---get-seoredirects)
  - [1.2. Tạo Redirect mới - `POST /seo/redirects`](#12-tạo-redirect-mới---post-seoredirects)
- [2. Quản lý Sơ đồ trang web (Sitemaps)](#2-quản-lý-sơ-đồ-trang-web-sitemaps)
  - [2.1. <PERSON><PERSON><PERSON> danh sách Sitemaps - `GET /seo/sitemaps`](#21-lấy-danh-sách-sitemaps---get-seositemaps)
  - [2.2. <PERSON><PERSON><PERSON> Sitemap mới - `POST /seo/sitemaps`](#22-tạo-sitemap-mới---post-seositemaps)
  - [2.3. Tạo lại (Generate) Sitemap - `POST /seo/sitemaps/generate/{...}`](#23-tạo-lại-generate-sitemap---post-seositemapsgenerate)

---

## 1. Quản lý Chuyển hướng (Redirects)

Các API để quản lý các quy tắc chuyển hướng 301/302, giúp duy trì thứ hạng SEO khi thay đổi URL.

### 1.1. Lấy danh sách Redirects - `GET /seo/redirects`

```mermaid
sequenceDiagram
    Admin->>+API: GET /seo/redirects
    API->>+SEORedirectHandler: ListRedirects(c)
    SEORedirectHandler->>+SEORedirectService: List(req)
    SEORedirectService->>+SEORedirectRepo: Find(filter)
    SEORedirectRepo-->>-SEORedirectService: redirects[], total
    SEORedirectService-->>-SEORedirectHandler: response
    SEORedirectHandler-->>-API: 200 OK
    API-->>-Admin: {data: {redirects: [...], ...}}
```

### 1.2. Tạo Redirect mới - `POST /seo/redirects`

```mermaid
sequenceDiagram
    Admin->>+API: POST /seo/redirects (body: {source_url, ...})
    API->>+SEORedirectHandler: CreateRedirect(c)
    SEORedirectHandler->>+SEORedirectService: Create(req)
    SEORedirectService->>+SEORedirectRepo: Create(redirect)
    SEORedirectRepo-->>-SEORedirectService: createdRedirect
    SEORedirectService-->>-SEORedirectHandler: createdRedirect
    SEORedirectHandler-->>-API: 201 Created
    API-->>-Admin: {data: {id, source_url, ...}}
```

---

## 2. Quản lý Sơ đồ trang web (Sitemaps)

Các API để tạo và quản lý các tệp sitemap.xml, giúp các công cụ tìm kiếm hiểu rõ hơn về cấu trúc trang web.

### 2.1. Lấy danh sách Sitemaps - `GET /seo/sitemaps`

```mermaid
sequenceDiagram
    Admin->>+API: GET /seo/sitemaps
    API->>+SEOSitemapHandler: ListSitemaps(c)
    SEOSitemapHandler->>+SEOSitemapService: List(req)
    SEOSitemapService->>+SEOSitemapRepo: Find(filter)
    SEOSitemapRepo-->>-SEOSitemapService: sitemaps[], total
    SEOSitemapService-->>-SEOSitemapHandler: response
    SEOSitemapHandler-->>-API: 200 OK
    API-->>-Admin: {data: {sitemaps: [...], ...}}
```

### 2.2. Tạo Sitemap mới - `POST /seo/sitemaps`

Đăng ký một sitemap mới để hệ thống quản lý và tự động tạo.

```mermaid
sequenceDiagram
    Admin->>+API: POST /seo/sitemaps (body: {website_id, type, ...})
    API->>+SEOSitemapHandler: CreateSitemap(c)
    SEOSitemapHandler->>+SEOSitemapService: Create(req)
    SEOSitemapService->>+SEOSitemapRepo: Create(sitemap)
    SEOSitemapRepo-->>-SEOSitemapService: createdSitemap
    SEOSitemapService-->>-SEOSitemapHandler: createdSitemap
    SEOSitemapHandler-->>-API: 201 Created
    API-->>-Admin: {data: {id, type, ...}}
```

### 2.3. Tạo lại (Generate) Sitemap - `POST /seo/sitemaps/generate/{...}`

Kích hoạt quá trình quét và tạo lại nội dung tệp XML cho một sitemap.

```mermaid
sequenceDiagram
    Admin->>+API: POST /sitemaps/generate/1/1/posts
    API->>+SEOSitemapHandler: GenerateSitemap(c)
    SEOSitemapHandler->>+SEOSitemapService: GenerateSitemap(websiteID, tenantID, type)
    Note right of SEOSitemapService: Quét DB để lấy URLs (vd: posts)
    SEOSitemapService->>SEOSitemapService: Tạo nội dung XML
    SEOSitemapService->>+SEOSitemapRepo: Update(sitemap with new content)
    SEOSitemapRepo-->>-SEOSitemapService: updatedSitemap
    SEOSitemapService-->>-SEOSitemapHandler: updatedSitemap
    SEOSitemapHandler-->>-API: 200 OK
    API-->>-Admin: {data: {id, ...}}
```
