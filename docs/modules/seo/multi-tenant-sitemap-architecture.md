# Kiến trúc Multi-Tenant Sitemap Management

## Tổng quan hệ thống

Hệ thống Blog API v3 sử dụng kiến trúc **Multi-Tenant, Multi-Website** với cơ chế quản lý sitemap đư<PERSON><PERSON> thiết kế để xử lý hàng ngàn websites độc lập trong cùng một instance.

## Cấu trúc dữ liệu phân cấp

```mermaid
erDiagram
    TENANTS ||--o{ WEBSITES : owns
    WEBSITES ||--o{ SEO_SITEMAPS : generates
    SEO_SITEMAPS ||--o{ SITEMAP_ENTRIES : contains
    WEBSITES ||--o{ BLOG_POSTS : hosts
    WEBSITES ||--o{ BLOG_CATEGORIES : categorizes
    
    TENANTS {
        uint id PK
        string name
        string domain
        varchar status
        json settings
    }
    
    WEBSITES {
        uint id PK
        uint tenant_id FK
        string subdomain
        string domain
        varchar status
        json seo_settings
    }
    
    SEO_SITEMAPS {
        uint id PK
        uint website_id FK
        uint tenant_id FK
        enum sitemap_type
        varchar filename
        varchar sitemap_url
        int total_entries
        timestamp last_generated_at
        json generation_settings
    }
    
    BLOG_POSTS {
        uint id PK
        uint website_id FK
        uint tenant_id FK
        string slug
        varchar status
        timestamp published_at
    }
```

## Flow quản lý Sitemap Multi-Tenant

### 1. Luồng tạo Sitemap tự động

```mermaid
flowchart TD
    A[Content Event] --> B{Event Type}
    B -->|Post Published| C[Blog Post Trigger]
    B -->|Page Created| D[Page Content Trigger]
    B -->|Category Added| E[Category Trigger]
    
    C --> F[Identify Website & Tenant]
    D --> F
    E --> F
    
    F --> G[Load Website Configuration]
    G --> H[Check Sitemap Settings]
    
    H --> I{Auto-generation Enabled?}
    I -->|Yes| J[Queue Sitemap Job]
    I -->|No| K[Skip Generation]
    
    J --> L[Background Worker]
    L --> M[Generate Sitemap XML]
    M --> N[Store in File System]
    N --> O[Update Database Record]
    O --> P[Cache Sitemap]
    P --> Q[Notify Search Engines]
    
    Q --> R[End]
    K --> R
```

### 2. Cơ chế lưu trữ phân tán

```mermaid
flowchart LR
    subgraph "File System Storage"
        A["/storage/sitemaps/"]
        A --> B["tenant-{id}/"]
        B --> C["website-{id}/"]
        C --> D["sitemap.xml"]
        C --> E["sitemap-posts.xml"]
        C --> F["sitemap-pages.xml"]
        C --> G["sitemap-images.xml"]
    end
    
    subgraph "Redis Cache Layer"
        H["sitemap:tenant:1:website:5:main"]
        I["sitemap:tenant:1:website:5:posts"]
        J["sitemap:tenant:2:website:10:main"]
        K["sitemap:tenant:3:website:15:posts"]
    end
    
    subgraph "Database Records"
        L[seo_sitemaps table]
        L --> M[tenant_id + website_id indexes]
        L --> N[generation metadata]
        L --> O[file paths & URLs]
    end
    
    C --> H
    C --> I
    D --> L
```

## Cơ chế lưu trữ và caching

### 1. File System Storage

**Cấu trúc thư mục chi tiết:**
```
/storage/sitemaps/
├── tenant-1/
│   ├── website-5/
│   │   ├── sitemap.xml                    # Main index
│   │   ├── sitemap-posts-2024.xml        # Posts by year
│   │   ├── sitemap-posts-2023.xml        
│   │   ├── sitemap-pages.xml              # Static pages
│   │   ├── sitemap-categories.xml         # Category pages
│   │   ├── sitemap-tags.xml               # Tag pages
│   │   ├── sitemap-authors.xml            # Author pages
│   │   ├── sitemap-images-2024.xml       # Images by year
│   │   ├── sitemap-videos.xml             # Video content
│   │   ├── sitemap-news.xml               # News sitemap (last 48h)
│   │   └── temp/
│   │       ├── sitemap-posts-2024-temp.xml  # Temp files for atomic updates
│   │       └── sitemap-images-2024-temp.xml
│   └── website-6/
│       ├── sitemap.xml
│       ├── sitemap-posts-2024.xml
│       └── sitemap-pages.xml
├── tenant-2/
│   └── website-10/
│       ├── sitemap.xml
│       ├── sitemap-posts-2024-01.xml      # Monthly split for large sites
│       ├── sitemap-posts-2024-02.xml
│       └── sitemap-pages.xml
└── tenant-3/
    └── website-15/
        ├── sitemap.xml
        ├── sitemap-posts-popular.xml         # Popular posts separate
        ├── sitemap-posts-recent.xml          # Recent posts
        └── sitemap-posts-archive.xml         # Archived posts
```

### Filename Convention và Strategy

#### 1. Naming Pattern
```go
type SitemapNamingStrategy struct {
    Pattern     string
    MaxEntries  int
    SplitBy     string // "year", "month", "category", "size"
}

// Các pattern filename
const (
    MainSitemap        = "sitemap.xml"
    PostsSitemap       = "sitemap-posts-%s.xml"      // %s = year/month/category
    PagesSitemap       = "sitemap-pages.xml"
    CategoriesSitemap  = "sitemap-categories.xml"
    TagsSitemap        = "sitemap-tags.xml"
    AuthorsSitemap     = "sitemap-authors.xml"
    ImagesSitemap      = "sitemap-images-%s.xml"     // %s = year/month
    VideosSitemap      = "sitemap-videos.xml"
    NewsSitemap        = "sitemap-news.xml"          // Last 48 hours
    TempSitemap        = "temp/sitemap-%s-temp.xml"  // For atomic updates
)

func (s *SitemapService) GenerateFilename(sitemapType, timeframe string) string {
    switch sitemapType {
    case "posts":
        if timeframe != "" {
            return fmt.Sprintf("sitemap-posts-%s.xml", timeframe)
        }
        return "sitemap-posts.xml"
    case "images":
        if timeframe != "" {
            return fmt.Sprintf("sitemap-images-%s.xml", timeframe)
        }
        return "sitemap-images.xml"
    case "news":
        return "sitemap-news.xml"
    default:
        return fmt.Sprintf("sitemap-%s.xml", sitemapType)
    }
}
```

#### 2. Dynamic Splitting Strategy
```go
type SitemapSplittingConfig struct {
    MaxURLsPerFile    int           // Default: 50,000
    MaxFileSizeMB     int           // Default: 50MB
    SplitStrategy     string        // "size", "time", "category"
    TimeframeSplit    string        // "year", "month", "week"
    PreferredFileSize int           // Target file size in MB
}

func (s *SitemapService) DetermineSplitting(websiteID uint, contentType string) *SitemapSplittingConfig {
    // Get content count statistics
    stats := s.getContentStats(websiteID, contentType)
    
    config := &SitemapSplittingConfig{
        MaxURLsPerFile: 50000,
        MaxFileSizeMB:  50,
    }
    
    switch {
    case stats.TotalCount > 100000:
        // Large site: split by month
        config.SplitStrategy = "time"
        config.TimeframeSplit = "month"
    case stats.TotalCount > 50000:
        // Medium site: split by year
        config.SplitStrategy = "time"
        config.TimeframeSplit = "year"
    case stats.TotalCount > 10000:
        // Small-medium site: split by category if many categories
        if stats.CategoryCount > 10 {
            config.SplitStrategy = "category"
        } else {
            config.SplitStrategy = "size"
        }
    default:
        // Small site: single file
        config.SplitStrategy = "single"
    }
    
    return config
}
```

### 2. Redis Caching Strategy

```mermaid
sequenceDiagram
    participant Client
    participant App
    participant Redis
    participant FileSystem
    participant Database
    
    Client->>App: Request sitemap.xml
    App->>Redis: GET sitemap:tenant:1:website:5:main
    
    alt Cache Hit
        Redis-->>App: Return cached XML
        App-->>Client: Serve sitemap
    else Cache Miss
        Redis-->>App: Cache miss
        App->>FileSystem: Read sitemap file
        FileSystem-->>App: Return XML content
        App->>Redis: SET sitemap:tenant:1:website:5:main (TTL: 1h)
        App-->>Client: Serve sitemap
    end
    
    Note over App: Cache key format: sitemap:{tenant_id}:{website_id}:{type}
```

**Cache Key Strategy:**
- **Pattern**: `sitemap:{tenant_id}:{website_id}:{type}`
- **TTL**: 1 giờ cho sitemap thường, 15 phút cho sitemap có content thay đổi thường xuyên
- **Invalidation**: Khi có content mới publish/update

### 3. Database Metadata Management

```sql
-- Ví dụ records trong bảng seo_sitemaps
INSERT INTO seo_sitemaps (
    website_id, tenant_id, sitemap_type, sitemap_name, 
    sitemap_url, sitemap_path, total_entries, 
    last_generated_at, generation_settings
) VALUES 
-- Tenant 1, Website 5
(5, 1, 'xml', 'Main Sitemap', 
 'https://blog1.example.com/sitemap.xml', 
 '/storage/sitemaps/tenant-1/website-5/sitemap.xml', 
 150, NOW(), '{"auto_update": true, "include_images": true}'),

-- Tenant 2, Website 10  
(10, 2, 'xml', 'Blog Sitemap',
 'https://myblog.custom-domain.com/sitemap.xml',
 '/storage/sitemaps/tenant-2/website-10/sitemap.xml',
 45, NOW(), '{"auto_update": false, "include_images": false}');
```

## Isolation và Security trong Multi-Tenant

### 1. Tenant Isolation Flow

```mermaid
flowchart TD
    A[Incoming Request] --> B[Extract Tenant Context]
    B --> C{Valid Tenant?}
    C -->|No| D[Return 403 Forbidden]
    C -->|Yes| E[Load Tenant Config]
    
    E --> F[Extract Website Context]
    F --> G{Website belongs to Tenant?}
    G -->|No| H[Return 404 Not Found]
    G -->|Yes| I[Load Website Config]
    
    I --> J[Generate Sitemap Path]
    J --> K["/storage/sitemaps/tenant-{id}/website-{id}/"]
    K --> L[Serve Sitemap File]
    
    L --> M[Log Access]
    M --> N[Return Response]
```

### 2. Path Generation Security

```go
// An toàn trong việc tạo đường dẫn file
func (s *SitemapService) generateSecurePath(tenantID, websiteID uint, filename string) (string, error) {
    // Validate inputs
    if tenantID == 0 || websiteID == 0 {
        return "", errors.New("invalid tenant or website ID")
    }
    
    // Sanitize filename
    filename = filepath.Clean(filename)
    if strings.Contains(filename, "..") {
        return "", errors.New("invalid filename")
    }
    
    // Verify website belongs to tenant
    if !s.websiteRepo.BelongsToTenant(websiteID, tenantID) {
        return "", errors.New("website does not belong to tenant")
    }
    
    // Generate secure path
    return fmt.Sprintf("/storage/sitemaps/tenant-%d/website-%d/%s", 
        tenantID, websiteID, filename), nil
}
```

## Background Processing và Queue Management

### 1. Sitemap Generation Queue

```mermaid
sequenceDiagram
    participant Content as Content System
    participant Queue as Redis Queue
    participant Worker as Background Worker
    participant Storage as File System
    participant Cache as Redis Cache
    
    Content->>Queue: Enqueue sitemap job
    Note over Queue: Job: {tenant_id: 1, website_id: 5, type: "posts"}
    
    Worker->>Queue: Dequeue job
    Worker->>Worker: Validate tenant/website
    Worker->>Worker: Generate sitemap XML
    Worker->>Storage: Save sitemap file
    Worker->>Cache: Invalidate old cache
    Worker->>Cache: Pre-warm new cache
    
    Note over Worker: Job completed
```

### 2. Batch Processing cho Multiple Websites

```go
type SitemapBatchProcessor struct {
    redis    *redis.Client
    workers  int
    jobQueue chan SitemapJob
}

type SitemapJob struct {
    TenantID   uint   `json:"tenant_id"`
    WebsiteID  uint   `json:"website_id"`
    Type       string `json:"type"`
    Priority   int    `json:"priority"`
    Timestamp  int64  `json:"timestamp"`
}

func (p *SitemapBatchProcessor) ProcessBatch() {
    for i := 0; i < p.workers; i++ {
        go p.worker()
    }
}

func (p *SitemapBatchProcessor) worker() {
    for job := range p.jobQueue {
        if err := p.processJob(job); err != nil {
            log.Printf("Failed to process sitemap job for tenant %d, website %d: %v", 
                job.TenantID, job.WebsiteID, err)
            // Retry logic hoặc dead letter queue
        }
    }
}
```

## Incremental Update System

### 1. Change Tracking và Delta Updates

```mermaid
flowchart TD
    A[Content Change Event] --> B[Change Tracker]
    B --> C{Change Type}
    
    C -->|Create| D[Add to Sitemap]
    C -->|Update| E[Update Entry]
    C -->|Delete| F[Remove from Sitemap]
    C -->|Publish| G[Move Draft→Published]
    C -->|Unpublish| H[Move Published→Draft]
    
    D --> I[Identify Target Sitemaps]
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J[Load Existing XML]
    J --> K[Apply Delta Change]
    K --> L[Validate XML Structure]
    L --> M[Atomic File Update]
    M --> N[Update Cache]
    N --> O[Update Database Metadata]
```

### 2. Delta Change Processing

```go
type SitemapDeltaProcessor struct {
    websiteID   uint
    tenantID    uint
    fileService SitemapFileService
    cache       CacheService
    validator   SitemapValidator
}

type ContentChangeEvent struct {
    ContentID   uint      `json:"content_id"`
    ContentType string    `json:"content_type"` // "post", "page", "category"
    Action      string    `json:"action"`      // "create", "update", "delete", "publish", "unpublish"
    URL         string    `json:"url"`
    LastMod     time.Time `json:"last_modified"`
    Priority    float64   `json:"priority"`
    ChangeFreq  string    `json:"change_freq"`
    Images      []ImageInfo `json:"images,omitempty"`
    Categories  []string  `json:"categories,omitempty"`
    Tags        []string  `json:"tags,omitempty"`
}

func (p *SitemapDeltaProcessor) ProcessChange(event ContentChangeEvent) error {
    // 1. Determine affected sitemap files
    affectedFiles := p.getAffectedSitemapFiles(event)
    
    // 2. Process each affected file
    for _, file := range affectedFiles {
        if err := p.processFileChange(file, event); err != nil {
            return fmt.Errorf("failed to process file %s: %w", file.Name, err)
        }
    }
    
    // 3. Update main sitemap index if needed
    if p.shouldUpdateIndex(event) {
        return p.updateMainIndex()
    }
    
    return nil
}

func (p *SitemapDeltaProcessor) processFileChange(file SitemapFile, event ContentChangeEvent) error {
    // 1. Create temp file for atomic update
    tempPath := p.generateTempPath(file.Path)
    
    // 2. Load existing sitemap
    sitemap, err := p.fileService.LoadSitemap(file.Path)
    if err != nil {
        if os.IsNotExist(err) {
            sitemap = p.createEmptySitemap(file.Type)
        } else {
            return err
        }
    }
    
    // 3. Apply change based on action
    switch event.Action {
    case "create", "publish":
        sitemap = p.addURL(sitemap, event)
    case "update":
        sitemap = p.updateURL(sitemap, event)
    case "delete", "unpublish":
        sitemap = p.removeURL(sitemap, event)
    }
    
    // 4. Validate updated sitemap
    if err := p.validator.ValidateSitemap(sitemap); err != nil {
        return fmt.Errorf("validation failed: %w", err)
    }
    
    // 5. Write to temp file
    if err := p.fileService.WriteSitemap(tempPath, sitemap); err != nil {
        return err
    }
    
    // 6. Atomic replace
    if err := os.Rename(tempPath, file.Path); err != nil {
        return err
    }
    
    // 7. Update cache
    p.cache.InvalidatePattern(fmt.Sprintf("sitemap:%d:%d:*", p.tenantID, p.websiteID))
    
    // 8. Update metadata
    return p.updateFileMetadata(file, sitemap)
}
```

### 3. Intelligent URL Management

```go
type SitemapURLManager struct {
    urlIndex map[string]*SitemapEntry // URL -> Entry mapping for fast lookup
    urlOrder []string                  // Maintain insertion order
    maxURLs  int                      // Max URLs per sitemap
}

func (m *SitemapURLManager) AddURL(url SitemapEntry) error {
    // Check if URL already exists
    if existing, exists := m.urlIndex[url.Loc]; exists {
        // Update existing entry
        existing.LastMod = url.LastMod
        existing.Priority = url.Priority
        existing.ChangeFreq = url.ChangeFreq
        return nil
    }
    
    // Check capacity
    if len(m.urlIndex) >= m.maxURLs {
        return ErrSitemapFull
    }
    
    // Add new URL
    m.urlIndex[url.Loc] = &url
    m.urlOrder = append(m.urlOrder, url.Loc)
    
    return nil
}

func (m *SitemapURLManager) RemoveURL(urlLoc string) bool {
    if _, exists := m.urlIndex[urlLoc]; !exists {
        return false
    }
    
    // Remove from index
    delete(m.urlIndex, urlLoc)
    
    // Remove from order slice
    for i, url := range m.urlOrder {
        if url == urlLoc {
            m.urlOrder = append(m.urlOrder[:i], m.urlOrder[i+1:]...)
            break
        }
    }
    
    return true
}

func (m *SitemapURLManager) GetOrderedURLs() []SitemapEntry {
    urls := make([]SitemapEntry, 0, len(m.urlOrder))
    for _, urlLoc := range m.urlOrder {
        if entry, exists := m.urlIndex[urlLoc]; exists {
            urls = append(urls, *entry)
        }
    }
    return urls
}
```

### 4. Smart File Splitting & Management

```go
type SmartSitemapManager struct {
    websiteID      uint
    tenantID       uint
    splittingRules SitemapSplittingConfig
    fileManager    SitemapFileManager
}

func (m *SmartSitemapManager) HandleOverflow(sitemap *Sitemap, newEntry SitemapEntry) error {
    if len(sitemap.URLs) < m.splittingRules.MaxURLsPerFile {
        sitemap.URLs = append(sitemap.URLs, newEntry)
        return nil
    }
    
    // Need to split or create new file
    switch m.splittingRules.SplitStrategy {
    case "time":
        return m.splitByTime(sitemap, newEntry)
    case "category":
        return m.splitByCategory(sitemap, newEntry)
    case "size":
        return m.splitBySize(sitemap, newEntry)
    default:
        return ErrSitemapFull
    }
}

func (m *SmartSitemapManager) splitByTime(sitemap *Sitemap, newEntry SitemapEntry) error {
    // Determine timeframe for new entry
    timeframe := m.extractTimeframe(newEntry.LastMod)
    
    // Check if we have a sitemap for this timeframe
    timeframeSitemap := m.getOrCreateTimeframeSitemap(sitemap.Type, timeframe)
    
    // Add to appropriate timeframe sitemap
    timeframeSitemap.URLs = append(timeframeSitemap.URLs, newEntry)
    
    // Save updated sitemap
    return m.fileManager.SaveSitemap(timeframeSitemap)
}

func (m *SmartSitemapManager) extractTimeframe(lastMod *time.Time) string {
    if lastMod == nil {
        return time.Now().Format("2006")
    }
    
    switch m.splittingRules.TimeframeSplit {
    case "month":
        return lastMod.Format("2006-01")
    case "year":
        return lastMod.Format("2006")
    case "week":
        year, week := lastMod.ISOWeek()
        return fmt.Sprintf("%d-W%02d", year, week)
    default:
        return lastMod.Format("2006")
    }
}
```

### 5. Atomic Update Operations

```go
type AtomicSitemapUpdater struct {
    basePath   string
    tempDir    string
    lockManager LockManager
}

func (u *AtomicSitemapUpdater) UpdateSitemapAtomic(filePath string, updateFunc func(*Sitemap) error) error {
    // 1. Acquire file lock
    lock, err := u.lockManager.AcquireLock(filePath, 30*time.Second)
    if err != nil {
        return fmt.Errorf("failed to acquire lock: %w", err)
    }
    defer lock.Release()
    
    // 2. Create temp file
    tempPath := filepath.Join(u.tempDir, fmt.Sprintf("%s-temp-%d", 
        filepath.Base(filePath), time.Now().UnixNano()))
    
    // 3. Load existing sitemap
    sitemap, err := u.loadSitemap(filePath)
    if err != nil {
        return err
    }
    
    // 4. Apply update function
    if err := updateFunc(sitemap); err != nil {
        return err
    }
    
    // 5. Write to temp file
    if err := u.writeSitemap(tempPath, sitemap); err != nil {
        return err
    }
    
    // 6. Atomic replace
    if err := os.Rename(tempPath, filePath); err != nil {
        os.Remove(tempPath) // Cleanup on failure
        return err
    }
    
    return nil
}
```

### 6. Change Event Queue và Processing

```mermaid
sequenceDiagram
    participant Blog as Blog Service
    participant Queue as Redis Queue
    participant Worker as Sitemap Worker
    participant File as File System
    participant Cache as Redis Cache
    
    Blog->>Queue: Enqueue change event
    Note over Queue: {action: "publish", post_id: 123, url: "/posts/new-post"}
    
    Worker->>Queue: Dequeue event
    Worker->>Worker: Determine affected files
    Worker->>File: Load existing sitemap
    Worker->>Worker: Apply delta change
    Worker->>File: Atomic file update
    Worker->>Cache: Invalidate cache
    Worker->>Cache: Pre-warm new content
    
    Note over Worker: Change processed successfully
```

## Performance Optimization Strategies

### 1. Lazy Loading và On-Demand Generation

```mermaid
flowchart TD
    A[Sitemap Request] --> B{Cache Exists?}
    B -->|Yes| C[Return Cached]
    B -->|No| D{File Exists?}
    
    D -->|Yes| E[Read File]
    E --> F[Cache File Content]
    F --> G[Return Content]
    
    D -->|No| H[Check Last Generation Time]
    H --> I{Recently Generated?}
    I -->|Yes| J[Return 404]
    I -->|No| K[Queue Generation Job]
    K --> L[Return 202 Accepted]
    
    L --> M[Background Generation]
    M --> N[File Created]
    N --> O[Cache Populated]
```

### 2. Smart Invalidation Strategy

```go
func (s *SitemapService) InvalidateCache(tenantID, websiteID uint, contentType string) {
    // Xác định sitemap types bị ảnh hưởng
    affectedTypes := s.getAffectedSitemapTypes(contentType)
    
    for _, sitemapType := range affectedTypes {
        // Invalidate cache
        cacheKey := fmt.Sprintf("sitemap:%d:%d:%s", tenantID, websiteID, sitemapType)
        s.cache.Delete(cacheKey)
        
        // Mark for regeneration
        s.markForRegeneration(tenantID, websiteID, sitemapType)
    }
    
    // Always invalidate main sitemap index
    mainCacheKey := fmt.Sprintf("sitemap:%d:%d:main", tenantID, websiteID)
    s.cache.Delete(mainCacheKey)
}

func (s *SitemapService) getAffectedSitemapTypes(contentType string) []string {
    switch contentType {
    case "blog_post":
        return []string{"posts", "main"}
    case "blog_category":
        return []string{"categories", "main"}
    case "page":
        return []string{"pages", "main"}
    case "media":
        return []string{"images", "main"}
    default:
        return []string{"main"}
    }
}
```

## Monitoring và Analytics

### 1. Sitemap Performance Metrics

```mermaid
graph LR
    A[Sitemap Metrics] --> B[Generation Time]
    A --> C[File Size]
    A --> D[Cache Hit Rate]
    A --> E[Search Engine Crawl Rate]
    
    B --> F[Per Tenant Average]
    C --> G[Size Distribution]
    D --> H[Cache Efficiency]
    E --> I[SEO Performance]
    
    F --> J[Performance Dashboard]
    G --> J
    H --> J
    I --> J
```

### 2. Real-time Monitoring

```go
type SitemapMetrics struct {
    TenantID           uint      `json:"tenant_id"`
    WebsiteID          uint      `json:"website_id"`
    GenerationTime     time.Duration `json:"generation_time_ms"`
    FileSize           int64     `json:"file_size_bytes"`
    EntryCount         int       `json:"entry_count"`
    CacheHitRate       float64   `json:"cache_hit_rate"`
    LastCrawledAt      *time.Time `json:"last_crawled_at"`
    SearchEngineStatus map[string]string `json:"search_engine_status"`
}

func (s *SitemapService) RecordMetrics(metrics SitemapMetrics) {
    // Store in InfluxDB/Prometheus for monitoring
    s.metricsCollector.Record("sitemap_generation", metrics)
    
    // Alert on performance issues
    if metrics.GenerationTime > 30*time.Second {
        s.alertManager.Alert("slow_sitemap_generation", metrics)
    }
    
    if metrics.FileSize > 50*1024*1024 { // 50MB limit
        s.alertManager.Alert("large_sitemap_file", metrics)
    }
}
```

## Kết luận

Hệ thống sitemap multi-tenant được thiết kế với:

1. **Isolation hoàn toàn** giữa các tenant và website
2. **Cache layer thông minh** với Redis để tối ưu performance  
3. **Background processing** để không ảnh hưởng user experience
4. **Auto-scaling** với queue system và multiple workers
5. **Security đầy đủ** với path validation và tenant verification
6. **Monitoring comprehensive** để theo dõi performance và health

### **🔄 Incremental Update Benefits:**

**Performance:**
- ⚡ **Fast updates**: Chỉ modify files cần thiết
- 🎯 **Delta processing**: Chỉ xử lý thay đổi, không rebuild toàn bộ
- 💾 **Memory efficient**: Không load toàn bộ content vào memory
- 🔒 **Atomic operations**: Đảm bảo consistency với file locks

**Scalability:**
- 📈 **Smart splitting**: Tự động chia nhỏ khi vượt threshold
- 📅 **Time-based organization**: Sitemap được tổ chức theo thời gian
- 🗂️ **Category separation**: Tách biệt theo category khi cần thiết
- 🔄 **Background processing**: Không ảnh hưởng user experience

**Reliability:**
- 🛡️ **Rollback support**: Giữ backup versions
- ✅ **Validation**: Kiểm tra XML validity sau mỗi update
- 🔐 **File locking**: Tránh race conditions
- 📊 **Change tracking**: Audit trail đầy đủ

**🎯 Điểm mạnh của kiến trúc:**

✅ **Scalable**: Xử lý hàng ngàn websites đồng thời  
✅ **Isolated**: An toàn giữa các tenant  
✅ **Fast**: Multi-layer caching + incremental updates
✅ **Reliable**: Background processing + retry logic + atomic operations
✅ **SEO-optimized**: Auto-submit to search engines  
✅ **Maintainable**: Clean separation of concerns
✅ **Efficient**: Delta updates thay vì full regeneration
✅ **Smart**: Auto-splitting và dynamic filename strategy
✅ **Consistent**: File locking và atomic updates
✅ **Traceable**: Change tracking và audit logs

Cấu trúc này cho phép hệ thống xử lý **hàng ngàn websites** đồng thời với performance cao và isolation bảo mật tốt, đặc biệt tối ưu cho **incremental updates** và **multi-tenant isolation**! 🎯