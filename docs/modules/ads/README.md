# Module <PERSON> cáo (Ads)

Tài liệu này mô tả chi tiết về module <PERSON><PERSON><PERSON><PERSON>á<PERSON>, một hệ thống hoàn chỉnh để quản lý và hiển thị quảng cáo trên các trang web.

## C<PERSON>c khái niệm chính

-   **Campaign (Chiến dịch)**: <PERSON><PERSON> một chiến dịch quảng cáo tổng thể, c<PERSON> ngân sách, thờ<PERSON> gian bắt đầu và kết thúc. Mỗi chiến dịch có thể chứa nhiều quảng cáo.
-   **Advertisement (Quảng cáo)**: L<PERSON> một mẩu quảng cáo cụ thể, chứa nội dung (hình ảnh, text, link) và các quy tắc nhắm mục tiêu (targeting).
-   **Placement (Vị trí)**: <PERSON><PERSON> mộ<PERSON> vị trí cụ thể trên trang web nơi quảng cáo sẽ được hiển thị (ví dụ: `sidebar-top`, `header-banner`).

## M<PERSON><PERSON> lục

- [1. <PERSON><PERSON><PERSON><PERSON> lý Chiến dịch (Campaigns)](#1-quản-lý-chiến-dịch-campaigns)
  - [1.1. Lấy danh sách Campaigns - `GET /ads/campaigns`](#11-lấy-danh-sách-campaigns---get-adscampaigns)
  - [1.2. Tạo Campaign mới - `POST /ads/campaigns`](#12-tạo-campaign-mới---post-adscampaigns)
- [2. Quản lý Quảng cáo (Advertisements)](#2-quản-lý-quảng-cáo-advertisements)
  - [2.1. Lấy danh sách Advertisements - `GET /ads/advertisements`](#21-lấy-danh-sách-advertisements---get-adsadvertisements)
  - [2.2. Tạo Advertisement mới - `POST /ads/advertisements`](#22-tạo-advertisement-mới---post-adsadvertisements)
- [3. Hiển thị Quảng cáo (Ad Serving)](#3-hiển-thị-quảng-cáo-ad-serving)
  - [3.1. Lấy quảng cáo để hiển thị - `GET /ads/serve`](#31-lấy-quảng-cáo-để-hiển-thị---get-adsserve)

---

## 1. Quản lý Chiến dịch (Campaigns)

### 1.1. Lấy danh sách Campaigns - `GET /ads/campaigns`

```mermaid
sequenceDiagram
    Admin->>+API: GET /ads/campaigns
    API->>+CampaignHandler: ListCampaigns(c)
    CampaignHandler->>+CampaignService: ListCampaigns(tenantID, filter)
    CampaignService->>+CampaignRepo: Find(filter)
    CampaignRepo-->>-CampaignService: campaigns[], total
    CampaignService-->>-CampaignHandler: response
    CampaignHandler-->>-API: 200 OK
    API-->>-Admin: {campaigns: [...], total: X}
```

### 1.2. Tạo Campaign mới - `POST /ads/campaigns`

```mermaid
sequenceDiagram
    Admin->>+API: POST /ads/campaigns (body: {name, budget, ...})
    API->>+CampaignHandler: CreateCampaign(c)
    CampaignHandler->>+CampaignService: CreateCampaign(tenantID, req)
    CampaignService->>+CampaignRepo: Create(campaign)
    CampaignRepo-->>-CampaignService: createdCampaign
    CampaignService-->>-CampaignHandler: createdCampaign
    CampaignHandler-->>-API: 201 Created
    API-->>-Admin: {id, name, ...}
```

---

## 2. Quản lý Quảng cáo (Advertisements)

### 2.1. Lấy danh sách Advertisements - `GET /ads/advertisements`

```mermaid
sequenceDiagram
    Admin->>+API: GET /ads/advertisements
    API->>+AdvertisementHandler: ListAdvertisements(c)
    AdvertisementHandler->>+AdvertisementService: ListAdvertisements(tenantID, filter)
    AdvertisementService->>+AdvertisementRepo: Find(filter)
    AdvertisementRepo-->>-AdvertisementService: advertisements[], total
    AdvertisementService-->>-AdvertisementHandler: response
    AdvertisementHandler-->>-API: 200 OK
    API-->>-Admin: {advertisements: [...], total: X}
```

### 2.2. Tạo Advertisement mới - `POST /ads/advertisements`

```mermaid
sequenceDiagram
    Admin->>+API: POST /ads/advertisements (body: {name, campaign_id, ...})
    API->>+AdvertisementHandler: CreateAdvertisement(c)
    AdvertisementHandler->>+AdvertisementService: CreateAdvertisement(tenantID, req)
    AdvertisementService->>+AdvertisementRepo: Create(advertisement)
    AdvertisementRepo-->>-AdvertisementService: createdAd
    AdvertisementService-->>-AdvertisementHandler: createdAd
    AdvertisementHandler-->>-API: 201 Created
    API-->>-Admin: {id, name, ...}
```

---

## 3. Hiển thị Quảng cáo (Ad Serving)

### 3.1. Lấy quảng cáo để hiển thị - `GET /ads/serve`

Đây là endpoint công khai mà frontend sẽ gọi để lấy quảng cáo phù hợp cho một vị trí cụ thể.

```mermaid
sequenceDiagram
    Frontend->>+API: GET /ads/serve?placement_id=1&...
    API->>+AdvertisementHandler: ServeAd(c)
    AdvertisementHandler->>+PlacementService: GetPlacementByID(tenantID, 1)
    PlacementService-->>-AdvertisementHandler: placement
    AdvertisementHandler->>+AdvertisementService: GetAdvertisementsForPlacement(placement, context)
    Note right of AdvertisementService: Lọc quảng cáo dựa trên targeting rules
    AdvertisementService-->>-AdvertisementHandler: suitableAds[]
    AdvertisementHandler-->>-API: 200 OK
    API-->>-Frontend: {advertisements: [...]}
```
