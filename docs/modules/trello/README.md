# Mo<PERSON>le <PERSON>rello

Tài liệu này mô tả chi tiết về module <PERSON><PERSON><PERSON>, một hệ thống quản lý công việc theo phong cá<PERSON>, b<PERSON> <PERSON><PERSON><PERSON> (Boards), <PERSON><PERSON> (Lists), <PERSON><PERSON> <PERSON> (Cards).

## C<PERSON><PERSON> kh<PERSON>i niệ<PERSON> ch<PERSON>h

-   **Board (Bảng)**: <PERSON><PERSON><PERSON> diện cho một dự án hoặc một không gian làm việc. Mỗi board chứa nhiều danh sách.
-   **List (Danh sách)**: L<PERSON> một cột trong board, đại diện cho một giai đoạn của quy trình làm việc (ví dụ: "To Do", "In Progress", "Done"). Mỗi danh sách chứa nhiều thẻ.
-   **Card (Thẻ)**: L<PERSON> một công việc hoặc một mục cụ thể trong một danh sách.

## <PERSON><PERSON><PERSON> l<PERSON>

- [1. <PERSON><PERSON><PERSON><PERSON> <PERSON> (Boards)](#1-quản-lý-bảng-boards)
  - [1.1. <PERSON><PERSON><PERSON> danh sách Boards - `GET /trello/boards`](#11-lấy-danh-sách-boards---get-trelloboards)
  - [1.2. Tạo Board mới - `POST /trello/boards`](#12-tạo-board-mới---post-trelloboards)
- [2. Quản lý Danh sách (Lists)](#2-quản-lý-danh-sách-lists)
  - [2.1. Lấy danh sách Lists của một Board - `GET /trello/lists/board/{boardId}`](#21-lấy-danh-sách-lists-của-một-board---get-trellolistsboardboardid)
  - [2.2. Tạo List mới - `POST /trello/lists`](#22-tạo-list-mới---post-trellolists)
- [3. Quản lý Thẻ (Cards)](#3-quản-lý-thẻ-cards)
  - [3.1. Lấy danh sách Cards của một List - `GET /trello/cards/list/{listId}`](#31-lấy-danh-sách-cards-của-một-list---get-trellocardslistlistid)
  - [3.2. Tạo Card mới - `POST /trello/cards`](#32-tạo-card-mới---post-trellocards)

---

## 1. Quản lý Bảng (Boards)

### 1.1. Lấy danh sách Boards - `GET /trello/boards`

```mermaid
sequenceDiagram
    User->>+API: GET /trello/boards
    API->>+BoardHandler: List(c)
    BoardHandler->>+BoardService: List(tenantID, userID, ...)
    BoardService->>+BoardRepo: FindByUser(userID)
    BoardRepo-->>-BoardService: boards[]
    BoardService-->>-BoardHandler: boards[], cursor
    BoardHandler-->>-API: 200 OK
    API-->>-User: {data: [...], pagination: {...}}
```

### 1.2. Tạo Board mới - `POST /trello/boards`

```mermaid
sequenceDiagram
    User->>+API: POST /trello/boards (body: {name, workspace_id, ...})
    API->>+BoardHandler: Create(c)
    BoardHandler->>+BoardService: Create(tenantID, userID, req)
    BoardService->>+BoardRepo: Create(board)
    BoardRepo-->>-BoardService: createdBoard
    BoardService-->>-BoardHandler: createdBoard
    BoardHandler-->>-API: 201 Created
    API-->>-User: {id, name, ...}
```

---

## 2. Quản lý Danh sách (Lists)

### 2.1. Lấy danh sách Lists của một Board - `GET /trello/lists/board/{boardId}`

```mermaid
sequenceDiagram
    User->>+API: GET /trello/lists/board/1
    API->>+ListHandler: ListByBoard(c)
    ListHandler->>+ListService: ListByBoard(tenantID, userID, 1)
    ListService->>+ListRepo: FindByBoardID(1)
    ListRepo-->>-ListService: lists[]
    ListService-->>-ListHandler: lists[]
    ListHandler-->>-API: 200 OK
    API-->>-User: {data: [...]}
```

### 2.2. Tạo List mới - `POST /trello/lists`

```mermaid
sequenceDiagram
    User->>+API: POST /trello/lists (body: {name, board_id, ...})
    API->>+ListHandler: Create(c)
    ListHandler->>+ListService: Create(tenantID, userID, req)
    ListService->>+ListRepo: Create(list)
    ListRepo-->>-ListService: createdList
    ListService-->>-ListHandler: createdList
    ListHandler-->>-API: 201 Created
    API-->>-User: {id, name, ...}
```

---

## 3. Quản lý Thẻ (Cards)

### 3.1. Lấy danh sách Cards của một List - `GET /trello/cards/list/{listId}`

```mermaid
sequenceDiagram
    User->>+API: GET /trello/cards/list/1
    API->>+CardHandler: ListByList(c)
    CardHandler->>+CardService: ListByList(tenantID, userID, 1, ...)
    CardService->>+CardRepo: FindByListID(1)
    CardRepo-->>-CardService: cards[]
    CardService-->>-CardHandler: cards[], cursor
    CardHandler-->>-API: 200 OK
    API-->>-User: {data: [...], pagination: {...}}
```

### 3.2. Tạo Card mới - `POST /trello/cards`

```mermaid
sequenceDiagram
    User->>+API: POST /trello/cards (body: {name, list_id, ...})
    API->>+CardHandler: Create(c)
    CardHandler->>+CardService: Create(tenantID, userID, req)
    CardService->>+CardRepo: Create(card)
    CardRepo-->>-CardService: createdCard
    CardService-->>-CardHandler: createdCard
    CardHandler-->>-API: 201 Created
    API-->>-User: {id, name, ...}
```
