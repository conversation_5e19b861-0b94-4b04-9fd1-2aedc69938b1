# Trello Entity Relationship Diagrams (Á<PERSON> dụng theo cấu trúc module hiện tại)

## 1. Core Multi-Tenant Structure

```mermaid
erDiagram
    TENANTS ||--o{ TENANT_PLANS : has
    TENANTS ||--o{ TENANT_SETTINGS : has
    TENANTS ||--o{ TENANT_FEATURES : has
    TENANTS ||--o{ WORKSPACES : contains
    
    TENANTS {
        int id PK
        string name
        string domain
        string logo_url
        enum status
        timestamp created_at
        timestamp updated_at
    }
    
    WORKSPACES ||--o{ BOARDS : contains
    WORKSPACES ||--o{ WORKSPACE_MEMBERS : has
    WORKSPACES ||--o{ TEAMS : has
    
    WORKSPACES {
        int id PK
        int tenant_id FK
        string name
        string description
        enum visibility
        json settings
        timestamp created_at
    }
```

## 2. User và Authentication (Theo module auth & user)

```mermaid
erDiagram
    USERS ||--o{ USER_PROFILES : has
    USERS ||--o{ USER_PREFERENCES : has
    USERS ||--o{ USER_SOCIAL_LINKS : has
    USERS ||--o{ TENANT_MEMBERSHIPS : has
    USERS ||--o{ AUTH_SESSIONS : has
    USERS ||--o{ AUTH_TOKENS : has
    
    USERS {
        int id PK
        string email UK
        string username UK
        string password_hash
        enum status
        timestamp email_verified_at
        timestamp created_at
        timestamp updated_at
    }
    
    TENANT_MEMBERSHIPS {
        int id PK
        int tenant_id FK
        int user_id FK
        enum role
        enum status
        timestamp joined_at
    }
    
    AUTH_SESSIONS {
        int id PK
        int tenant_id FK
        int user_id FK
        string session_token UK
        string ip_address
        string user_agent
        timestamp expires_at
        timestamp created_at
    }
```

## 3. Board Structure (Áp dụng multi-tenancy)

```mermaid
erDiagram
    BOARDS ||--o{ LISTS : contains
    BOARDS ||--o{ BOARD_MEMBERS : has
    BOARDS ||--o{ BOARD_LABELS : defines
    BOARDS ||--o{ BOARD_SETTINGS : has
    
    BOARDS {
        int id PK
        int tenant_id FK
        int workspace_id FK
        string name
        string description
        string background_type
        string background_value
        enum visibility
        enum status
        json preferences
        timestamp created_at
    }
    
    LISTS ||--o{ CARDS : contains
    
    LISTS {
        int id PK
        int tenant_id FK
        int board_id FK
        string name
        int position
        enum status
        json settings
        timestamp created_at
    }
```

## 4. Card Management (Với RBAC integration)

```mermaid
erDiagram
    CARDS ||--o{ CARD_MEMBERS : assigned_to
    CARDS ||--o{ CARD_LABELS : has
    CARDS ||--o{ CHECKLISTS : contains
    CARDS ||--o{ CARD_ATTACHMENTS : has
    CARDS ||--o{ CARD_COMMENTS : has
    CARDS ||--o{ CARD_ACTIVITIES : logs
    
    CARDS {
        int id PK
        int tenant_id FK
        int list_id FK
        int board_id FK
        string title
        text description
        int position
        datetime due_date
        datetime start_date
        boolean due_complete
        string cover_type
        string cover_value
        enum status
        json badges
        timestamp created_at
        timestamp updated_at
    }
    
    RBAC_USERS ||--o{ CARD_MEMBERS : validates
    RBAC_PERMISSIONS ||--o{ CARD_ACTIVITIES : checks
```

## 5. Media Integration (Theo module media)

```mermaid
erDiagram
    CARDS ||--o{ CARD_ATTACHMENTS : has
    MEDIA_FILES ||--o{ CARD_ATTACHMENTS : references
    MEDIA_FOLDERS ||--o{ MEDIA_FILES : contains
    
    CARD_ATTACHMENTS {
        int id PK
        int tenant_id FK
        int card_id FK
        int media_file_id FK
        int uploaded_by FK
        string type
        timestamp attached_at
    }
    
    MEDIA_FILES {
        int id PK
        int tenant_id FK
        int folder_id FK
        string filename
        string mime_type
        bigint size_bytes
        string storage_path
        json metadata
        timestamp created_at
    }
```

## 6. Notification System (Theo module notification)

```mermaid
erDiagram
    CARD_ACTIVITIES ||--o{ NOTIFICATIONS : triggers
    NOTIFICATIONS ||--o{ NOTIFICATION_RECIPIENTS : sends_to
    NOTIFICATION_TEMPLATES ||--o{ NOTIFICATIONS : uses
    
    NOTIFICATIONS {
        int id PK
        int tenant_id FK
        int template_id FK
        string type
        string title
        text content
        json data
        enum priority
        timestamp created_at
    }
    
    NOTIFICATION_RECIPIENTS {
        int id PK
        int notification_id FK
        int user_id FK
        enum channel
        enum status
        timestamp sent_at
        timestamp read_at
    }
```

## 7. Automation (Butler) với RBAC

```mermaid
erDiagram
    BOARDS ||--o{ AUTOMATION_RULES : has
    AUTOMATION_RULES ||--o{ AUTOMATION_TRIGGERS : defines
    AUTOMATION_RULES ||--o{ AUTOMATION_ACTIONS : performs
    RBAC_PERMISSIONS ||--o{ AUTOMATION_RULES : validates
    
    AUTOMATION_RULES {
        int id PK
        int tenant_id FK
        int board_id FK
        int created_by FK
        string name
        enum type
        boolean enabled
        json configuration
        timestamp created_at
    }
    
    AUTOMATION_LOGS {
        int id PK
        int rule_id FK
        int triggered_by FK
        json trigger_data
        json results
        enum status
        timestamp executed_at
    }
```

## 8. Team Collaboration

```mermaid
erDiagram
    WORKSPACES ||--o{ TEAMS : has
    TEAMS ||--o{ TEAM_MEMBERS : contains
    TEAMS ||--o{ TEAM_BOARDS : manages
    
    TEAMS {
        int id PK
        int tenant_id FK
        int workspace_id FK
        string name
        string description
        enum visibility
        timestamp created_at
    }
    
    TEAM_MEMBERS {
        int id PK
        int team_id FK
        int user_id FK
        enum role
        timestamp joined_at
    }
    
    TEAM_BOARDS {
        int id PK
        int team_id FK
        int board_id FK
        enum permission_level
        timestamp assigned_at
    }
```

## 9. Activity và Audit Logging

```mermaid
erDiagram
    CARD_ACTIVITIES {
        int id PK
        int tenant_id FK
        int board_id FK
        int card_id FK
        int list_id FK
        int user_id FK
        string type
        json data
        json old_value
        json new_value
        timestamp created_at
    }
    
    BOARD_ACTIVITIES {
        int id PK
        int tenant_id FK
        int board_id FK
        int user_id FK
        string type
        json data
        timestamp created_at
    }
    
    WORKSPACE_ACTIVITIES {
        int id PK
        int tenant_id FK
        int workspace_id FK
        int user_id FK
        string type
        json data
        timestamp created_at
    }
```

## 10. Custom Fields và Templates

```mermaid
erDiagram
    BOARDS ||--o{ CUSTOM_FIELDS : defines
    CUSTOM_FIELDS ||--o{ CUSTOM_FIELD_VALUES : has_values
    CARDS ||--o{ CUSTOM_FIELD_VALUES : contains
    
    CUSTOM_FIELDS {
        int id PK
        int tenant_id FK
        int board_id FK
        string name
        enum type
        json options
        boolean required
        int position
        timestamp created_at
    }
    
    BOARD_TEMPLATES {
        int id PK
        int tenant_id FK
        int source_board_id FK
        string name
        string description
        string category
        boolean is_public
        int usage_count
        json configuration
        timestamp created_at
    }
```

## 11. Search và Performance Optimization

```mermaid
erDiagram
    SEARCH_INDICES {
        int id PK
        int tenant_id FK
        string entity_type
        int entity_id
        text content
        json metadata
        tsvector search_vector
        timestamp indexed_at
    }
    
    USER_RECENT_BOARDS {
        int id PK
        int tenant_id FK
        int user_id FK
        int board_id FK
        int view_count
        timestamp last_viewed
    }
    
    BOARD_STARS {
        int id PK
        int tenant_id FK
        int board_id FK
        int user_id FK
        int position
        timestamp starred_at
    }
```

## 12. Integration với các module khác

```mermaid
erDiagram
    BOARDS ||--o{ WEBSITES : can_embed_in
    CARDS ||--o{ BLOG_POSTS : can_reference
    CARDS ||--o{ AI_REQUESTS : can_generate_with
    
    BOARD_WEBHOOKS {
        int id PK
        int tenant_id FK
        int board_id FK
        int created_by FK
        string callback_url
        string secret
        boolean active
        json events
        timestamp created_at
    }
    
    BOARD_INTEGRATIONS {
        int id PK
        int tenant_id FK
        int board_id FK
        string integration_type
        json configuration
        boolean active
        timestamp created_at
    }
```

## Data Type Definitions (Theo chuẩn MySQL của project)

### Common Fields
- **id**: INT UNSIGNED - Primary key cho tất cả tables
- **tenant_id**: INT UNSIGNED - Multi-tenant isolation
- **created_at**: TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

### Enum Types (MySQL ENUM)
- **status**: 'active' | 'inactive' | 'archived' | 'deleted'
- **visibility**: 'private' | 'workspace' | 'public'
- **role**: 'owner' | 'admin' | 'member' | 'observer' | 'guest'
- **activity_type**: 'create_card' | 'update_card' | 'move_card' | 'comment_card' | 'assign_member' | 'add_attachment' | 'complete_checklist'

### JSON Field Structures
- **settings**: { theme: string, locale: string, notifications: object }
- **badges**: { attachments: number, comments: number, check_items: number, check_items_checked: number, due_date: boolean }
- **preferences**: { card_cover: boolean, card_aging: boolean, calendar_feed: boolean }

### Index Strategy
- Composite indexes: (tenant_id, board_id), (tenant_id, user_id)
- Unique constraints: (tenant_id, workspace_id, name) cho boards
- Full-text search indexes cho card titles và descriptions