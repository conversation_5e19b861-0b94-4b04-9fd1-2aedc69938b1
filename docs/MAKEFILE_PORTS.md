# Makefile Port Management Commands

## Tổng quan

Makefile đã được mở rộng để hỗ trợ quản lý processes trên các ports được định nghĩa trong file `.env`. <PERSON><PERSON><PERSON> lệnh này giúp dễ dàng kill processes khi cần thiết.

## Các lệnh có sẵn

### Kiểm tra processes

```bash
# Hiển thị processes đang chạy trên các ports từ .env
make ps-ports
```

### Kill processes theo port cụ thể

```bash
# Kill process trên app port (mặc định: 9077)
make kill-app

# Kill process trên metrics port (mặc định: 9090)
make kill-metrics

# Kill process trên database port (mặc định: 3307)
make kill-db
```

### Kill tất cả processes

```bash
# Kill processes trên tất cả ports từ .env
make kill-all-ports
```

### Kill process trên port tùy chọn

```bash
# Kill process trên port bất kỳ
make kill-port PORT=8080
```

## Cách hoạt động

### Tự động load .env
- Makefile tự động load các biến môi trường từ file `.env`
- Sử dụng các giá trị: `APP_PORT`, `METRICS_PORT`, `DB_PORT`
- Có fallback values nếu `.env` không tồn tại

### Kill process an toàn
1. **TERM signal**: Gửi signal TERM trước (graceful shutdown)
2. **Wait**: Đợi 2 giây cho process tự thoát
3. **KILL signal**: Nếu process vẫn chạy, gửi signal KILL (force kill)

### Error handling
- Kiểm tra process có tồn tại trước khi kill
- Hiển thị thông báo rõ ràng với màu sắc
- Xử lý lỗi một cách graceful

## Ví dụ sử dụng

### Scenario 1: Restart application
```bash
# Kill app hiện tại
make kill-app

# Start lại app
make run
```

### Scenario 2: Cleanup toàn bộ
```bash
# Kill tất cả processes
make kill-all-ports

# Rebuild và restart
make build && make run
```

### Scenario 3: Debug port conflicts
```bash
# Kiểm tra processes đang chạy
make ps-ports

# Kill process cụ thể
make kill-port PORT=3000
```

## Ports được quản lý

Từ file `.env` hiện tại:

- **APP_PORT=9077**: Main application server
- **METRICS_PORT=9090**: Metrics/monitoring server  
- **DB_PORT=3307**: Database connection port

## Lưu ý an toàn

⚠️ **Cảnh báo**: Các lệnh kill có thể terminate processes quan trọng. Hãy chắc chắn bạn hiểu process nào đang chạy trước khi kill.

✅ **Best practices**:
- Luôn chạy `make ps-ports` trước để kiểm tra
- Sử dụng `kill-app` thay vì `kill-all-ports` khi chỉ cần restart app
- Backup dữ liệu quan trọng trước khi kill database processes

## Troubleshooting

### Lỗi "command not found: lsof"
```bash
# macOS (thường có sẵn)
which lsof

# Linux
sudo apt-get install lsof  # Ubuntu/Debian
sudo yum install lsof      # CentOS/RHEL
```

### Lỗi permission denied
```bash
# Thêm sudo nếu cần thiết
sudo make kill-app
```

### Process vẫn chạy sau khi kill
```bash
# Kiểm tra lại
make ps-ports

# Force kill bằng PID cụ thể
kill -9 <PID>
```