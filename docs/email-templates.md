# Email Template System Documentation

## Overview

The Blog API v3 uses a sophisticated email template system built on Go's native `text/template` and `html/template` packages. This system provides powerful template rendering capabilities with built-in security features, custom functions, and support for both HTML and plain text emails.

## Template Engine Features

### 1. Dual Template Engine Support

The system automatically detects and uses the appropriate template engine:

- **HTML Templates**: Uses `html/template` for HTML content with XSS protection
- **Text Templates**: Uses `text/template` for plain text content

### 2. Template Detection

Templates are automatically classified based on content:

```go
// HTML indicators that trigger html/template usage
htmlIndicators := []string{
    "<html", "<body", "<div", "<p>", "<h1", "<h2", "<h3", 
    "<a ", "<br", "<img", "<!DOCTYPE"
}
```

### 3. Template Syntax

Uses Go template syntax instead of custom placeholders:

```html
<!-- ✅ Correct Go Template Syntax -->
<h2>Hello {{.user.name}}! 📧</h2>
<p>Welcome to {{.brand.name}}</p>
<p>{{if .brand.logo_url}}<img src="{{.brand.logo_url}}" alt="{{.brand.name}}" />{{end}}</p>

<!-- ❌ Old Custom Syntax (No longer supported) -->
<h2>Hello {{user.name}}! 📧</h2>
<p>Welcome to {{brand.name}}</p>
```

## Built-in Template Functions

### Date Functions

```html
<p>Current Year: {{currentYear}}</p>
<p>Current Date: {{formatDate (now) "2006-01-02"}}</p>
<p>Custom Date: {{formatDate .created_at "January 2, 2006"}}</p>
```

### String Functions

```html
<p>Uppercase: {{.user.name | upper}}</p>
<p>Lowercase: {{.user.email | lower}}</p>
<p>Title Case: {{.blog.title | title}}</p>
<p>Trimmed: {{.user.bio | trimSpace}}</p>
```

### Utility Functions

```html
<!-- Default values -->
<p>Name: {{.user.display_name | default "Guest User"}}</p>

<!-- Safe HTML rendering (HTML templates only) -->
<div>{{.content | safe}}</div>

<!-- Safe URL rendering (HTML templates only) -->
<a href="{{.verification_url | url}}">Verify</a>
```

### Conditional Functions

```html
<!-- Equality checks -->
{{if eq .user.status "active"}}
    <p>Welcome back!</p>
{{end}}

<!-- Numeric comparisons -->
{{if gt .user.login_count 5}}
    <p>Frequent user detected</p>
{{end}}

<!-- Boolean checks -->
{{if .user.is_premium}}
    <p>Premium features available</p>
{{end}}
```

## Template Data Structure

### Standard Data Variables

All email templates receive these standard variables:

```go
data := map[string]interface{}{
    "current_year": 2025,
    "user": map[string]interface{}{
        "name":  "John Doe",
        "email": "<EMAIL>",
    },
    "brand": map[string]interface{}{
        "name":          "BlogAPI",
        "support_email": "<EMAIL>",
        "logo_url":      "https://blogapi.com/logo.png",
    },
}
```

### Accessing Nested Data

```html
<!-- Direct access -->
<p>{{.user.name}}</p>
<p>{{.brand.support_email}}</p>

<!-- With conditionals -->
{{if .user.profile.avatar_url}}
    <img src="{{.user.profile.avatar_url}}" alt="Avatar" />
{{end}}

<!-- With defaults -->
<p>{{.user.first_name | default .user.name}}</p>
```

## Template Examples

### 1. Basic HTML Template

```html
<!DOCTYPE html>
<html>
<head>
    <title>{{.subject}}</title>
</head>
<body>
    {{if .brand.logo_url}}
    <img src="{{.brand.logo_url}}" alt="{{.brand.name}}" />
    {{end}}
    
    <h1>Hello {{.user.name}}!</h1>
    <p>Welcome to {{.brand.name}}.</p>
    
    <a href="{{.verification_url}}">Verify Your Email</a>
    
    <footer>
        <p>&copy; {{currentYear}} {{.brand.name}}</p>
    </footer>
</body>
</html>
```

### 2. Plain Text Template

```text
Hello {{.user.name}}!

Welcome to {{.brand.name}}.

Please verify your email by clicking this link:
{{.verification_url}}

If you need help, contact us at {{.brand.support_email}}

© {{currentYear}} {{.brand.name}}. All rights reserved.
```

### 3. Complex Template with Functions

```html
<div class="email-content">
    <h2>{{.user.name | title}}</h2>
    
    {{if eq .notification_type "welcome"}}
        <p>Welcome to our platform!</p>
    {{else if eq .notification_type "reminder"}}
        <p>This is a friendly reminder.</p>
    {{else}}
        <p>Thank you for using our service.</p>
    {{end}}
    
    <p>Member since: {{formatDate .user.created_at "January 2006"}}</p>
    
    {{if gt .user.post_count 0}}
        <p>You have {{.user.post_count}} published posts.</p>
    {{else}}
        <p>{{.user.name | default "User"}}, start by creating your first post!</p>
    {{end}}
</div>
```

## Email Template Types

### 1. Email Verification

**Variables Available:**
- `user.name`, `user.email`
- `verification_url`, `expiry_time`
- `brand.name`, `brand.support_email`, `brand.logo_url`

### 2. Welcome Email

**Variables Available:**
- `user.name`, `user.email`
- `activation_url`
- `brand.name`, `brand.support_email`, `brand.logo_url`

### 3. Password Reset

**Variables Available:**
- `user.name`
- `reset_url`, `expiry_time`
- `brand.name`, `brand.support_email`

### 4. Email Verification Resend

**Variables Available:**
- `user.name`, `user.email`
- `verification_url`, `expiry_time`
- `resend_count`, `max_resends`
- `brand.name`, `brand.support_email`

## Security Features

### 1. XSS Protection (HTML Templates)

HTML templates automatically escape user input:

```html
<!-- Safe: User input is automatically escaped -->
<p>Hello {{.user.input}}</p>

<!-- Dangerous: Only use for trusted content -->
<div>{{.trusted_html_content | safe}}</div>
```

### 2. URL Safety

```html
<!-- Safe: URLs are automatically validated -->
<a href="{{.verification_url | url}}">Verify</a>
```

### 3. Input Validation

All template variables are validated before rendering:

```go
// Template data validation ensures safe rendering
data := map[string]interface{}{
    "user": map[string]interface{}{
        "name": template.HTMLEscapeString(user.Name), // For HTML templates
    },
}
```

## Error Handling

### Template Parsing Errors

```go
// Parsing errors provide detailed information
template: email:5:38: undefined variable "user.invalid_field"
```

### Template Execution Errors

```go
// Execution errors help debug data issues
failed to execute HTML template: template: email:12:8: 
executing "email" at <.missing_data>: map has no entry for key "missing_data"
```

## Performance Considerations

### 1. Template Caching

Templates are parsed once and cached for reuse:

```go
// Templates are cached per template version
tmpl, err := template.New("email").Funcs(functions).Parse(content)
```

### 2. Function Optimization

Built-in functions are optimized for performance:

```go
// Efficient string operations
"upper": strings.ToUpper,
"lower": strings.ToLower,
```

### 3. Memory Management

Template rendering uses memory-efficient buffer pooling:

```go
var buf bytes.Buffer
tmpl.Execute(&buf, data)
return buf.String()
```

## Best Practices

### 1. Template Structure

```html
<!-- ✅ Good: Clear structure with fallbacks -->
<div class="header">
    {{if .brand.logo_url}}
        <img src="{{.brand.logo_url}}" alt="{{.brand.name}}" />
    {{end}}
    <h1>{{.brand.name | default "Our Platform"}}</h1>
</div>

<!-- ❌ Bad: No fallbacks or structure -->
<img src="{{.brand.logo_url}}" />
<h1>{{.brand.name}}</h1>
```

### 2. Variable Usage

```html
<!-- ✅ Good: Use defaults and conditionals -->
<p>Hello {{.user.first_name | default .user.name | default "User"}}!</p>

<!-- ❌ Bad: No fallbacks -->
<p>Hello {{.user.first_name}}!</p>
```

### 3. Function Chaining

```html
<!-- ✅ Good: Logical function chaining -->
<h2>{{.title | trimSpace | title}}</h2>

<!-- ❌ Bad: Unnecessary or conflicting functions -->
<h2>{{.title | upper | lower | title}}</h2>
```

## Migration Guide

### From Custom Syntax to Go Templates

```html
<!-- Old Syntax -->
{{user.name}} → {{.user.name}}
{{brand.name}} → {{.brand.name}}
{{#if user.premium}}...{{/if}} → {{if .user.premium}}...{{end}}

<!-- Function Migration -->
{{user.name|uppercase}} → {{.user.name | upper}}
{{date|format:Y-m-d}} → {{formatDate .date "2006-01-02"}}
```

### Template Variables Update

```go
// Old: Flat structure
data["user.name"] = "John"
data["brand.name"] = "BlogAPI"

// New: Nested structure
data["user"] = map[string]interface{}{
    "name": "John",
}
data["brand"] = map[string]interface{}{
    "name": "BlogAPI",
}
```

## Testing Templates

### Unit Testing

```go
func TestEmailTemplate(t *testing.T) {
    service := &templateService{}
    
    version := &models.NotificationTemplateVersion{
        BodyHTML: `<h1>Hello {{.user.name}}!</h1>`,
    }
    
    data := map[string]interface{}{
        "user": map[string]interface{}{
            "name": "John Doe",
        },
    }
    
    result, err := service.RenderTemplate(version, data)
    assert.NoError(t, err)
    assert.Contains(t, result, "Hello John Doe!")
}
```

### Integration Testing

```go
func TestEmailVerificationFlow(t *testing.T) {
    // Test complete email verification with template rendering
    emailService := setupEmailService()
    
    user := createTestUser()
    err := emailService.SendVerificationEmail(user)
    
    assert.NoError(t, err)
    // Verify email was sent with correct template content
}
```

## Troubleshooting

### Common Issues

1. **Template Variable Not Found**
   ```
   Error: map has no entry for key "missing_field"
   Solution: Use default values or check data structure
   ```

2. **HTML Escaping Issues**
   ```
   Problem: HTML content appears as text
   Solution: Use {{.content | safe}} for trusted HTML
   ```

3. **Date Formatting Errors**
   ```
   Problem: formatDate function fails
   Solution: Ensure date is time.Time type and format is valid
   ```

### Debug Tips

1. **Enable Template Debugging**
   ```go
   // Add debug logging in template service
   log.Printf("Template data: %+v", data)
   ```

2. **Validate Template Syntax**
   ```bash
   # Use Go template validator
   go run template_validator.go template.html
   ```

3. **Test Template Rendering**
   ```bash
   # Run template service tests
   go test ./internal/modules/notification/services/ -v
   ```