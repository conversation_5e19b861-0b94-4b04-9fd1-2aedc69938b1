# Hybrid SEO and Blog Module Integration - API Examples

This document provides comprehensive examples for frontend integration of the hybrid SEO and Blog module API approach.

## Overview

The hybrid approach provides two ways to manage SEO metadata for blog posts:

1. **Direct SEO API**: Use `/api/cms/v1/seo/meta/*` endpoints for direct SEO management
2. **Convenience Blog API**: Use `/api/cms/v1/blog/posts/:id/seo/*` endpoints for blog-post-specific SEO operations

## Frontend Integration Patterns

### Pattern 1: Separate API Calls (Traditional)

```javascript
// Creating a blog post with SEO metadata (separate calls)
class BlogPostService {
  async createPostWithSEO(postData, seoData) {
    try {
      // 1. Create the blog post first
      const postResponse = await api.post('/api/cms/v1/blog/posts', postData);
      const post = postResponse.data;
      
      // 2. Create SEO metadata for the post
      const seoPayload = {
        ...seoData,
        website_id: postData.website_id,
        page_type: 'post',
        page_id: post.id,
        page_url: `/blog/posts/${post.slug}`,
        page_path: `/blog/posts/${post.slug}`
      };
      
      const seoResponse = await api.post('/api/cms/v1/seo/meta', seoPayload);
      
      return {
        post,
        seo: seoResponse.data
      };
    } catch (error) {
      console.error('Failed to create post with SEO:', error);
      throw error;
    }
  }
  
  async getPostWithSEO(postId) {
    try {
      // Parallel requests for better performance
      const [postResponse, seoResponse] = await Promise.all([
        api.get(`/api/cms/v1/blog/posts/${postId}`),
        api.get(`/api/cms/v1/seo/meta/by-page?page_type=post&page_id=${postId}`)
      ]);
      
      return {
        post: postResponse.data,
        seo: seoResponse.data
      };
    } catch (error) {
      console.error('Failed to get post with SEO:', error);
      throw error;
    }
  }
}
```

### Pattern 2: Convenience API (Hybrid Approach)

```javascript
// Using convenience endpoints for streamlined operations
class BlogPostSEOService {
  async createPostSEO(postId, seoData) {
    try {
      const response = await api.post(`/api/cms/v1/blog/posts/${postId}/seo`, seoData);
      return response.data;
    } catch (error) {
      console.error('Failed to create post SEO:', error);
      throw error;
    }
  }
  
  async getPostSEO(postId) {
    try {
      const response = await api.get(`/api/cms/v1/blog/posts/${postId}/seo`);
      return response.data;
    } catch (error) {
      console.error('Failed to get post SEO:', error);
      throw error;
    }
  }
  
  async updatePostSEO(postId, seoData) {
    try {
      const response = await api.put(`/api/cms/v1/blog/posts/${postId}/seo`, seoData);
      return response.data;
    } catch (error) {
      console.error('Failed to update post SEO:', error);
      throw error;
    }
  }
  
  async analyzePostSEO(postId) {
    try {
      const response = await api.post(`/api/cms/v1/blog/posts/${postId}/seo/analyze`);
      return response.data;
    } catch (error) {
      console.error('Failed to analyze post SEO:', error);
      throw error;
    }
  }
  
  async generatePostMetaTags(postId) {
    try {
      const response = await api.get(`/api/cms/v1/blog/posts/${postId}/seo/tags`);
      return response.data;
    } catch (error) {
      console.error('Failed to generate post meta tags:', error);
      throw error;
    }
  }
}
```

### Pattern 3: Combined Frontend Service (Best Practice)

```javascript
// Unified service combining both approaches
class BlogPostManager {
  constructor() {
    this.blogAPI = new BlogPostService();
    this.seoAPI = new BlogPostSEOService();
  }
  
  // Complete blog post creation workflow
  async createCompletePost(postData, seoData) {
    try {
      // Create the post first
      const post = await this.blogAPI.createPost(postData);
      
      // Add SEO metadata using convenience endpoint
      const seo = await this.seoAPI.createPostSEO(post.id, {
        ...seoData,
        website_id: postData.website_id,
        page_url: `/blog/posts/${post.slug}`,
        page_path: `/blog/posts/${post.slug}`
      });
      
      return { post, seo };
    } catch (error) {
      console.error('Failed to create complete post:', error);
      throw error;
    }
  }
  
  // Get post with SEO and analysis
  async getPostWithAnalysis(postId) {
    try {
      const [post, seo, analysis] = await Promise.all([
        this.blogAPI.getPost(postId),
        this.seoAPI.getPostSEO(postId),
        this.seoAPI.analyzePostSEO(postId)
      ]);
      
      return { post, seo, analysis };
    } catch (error) {
      console.error('Failed to get post with analysis:', error);
      throw error;
    }
  }
  
  // Bulk SEO operations for multiple posts
  async bulkOptimizePosts(postIds) {
    try {
      const results = await Promise.allSettled(
        postIds.map(async (postId) => {
          const analysis = await this.seoAPI.analyzePostSEO(postId);
          return { postId, analysis };
        })
      );
      
      return results
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value);
    } catch (error) {
      console.error('Failed bulk SEO optimization:', error);
      throw error;
    }
  }
}
```

## React Component Examples

### SEO Management Component

```jsx
import React, { useState, useEffect } from 'react';
import { BlogPostManager } from '../services/BlogPostManager';

const PostSEOManager = ({ postId }) => {
  const [seoData, setSeoData] = useState(null);
  const [analysis, setAnalysis] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  const blogManager = new BlogPostManager();
  
  useEffect(() => {
    loadPostSEO();
  }, [postId]);
  
  const loadPostSEO = async () => {
    try {
      setLoading(true);
      const data = await blogManager.getPostWithAnalysis(postId);
      setSeoData(data.seo);
      setAnalysis(data.analysis);
    } catch (error) {
      console.error('Failed to load SEO data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSeoUpdate = async (updatedSeo) => {
    try {
      setSaving(true);
      const updated = await blogManager.seoAPI.updatePostSEO(postId, updatedSeo);
      setSeoData(updated);
      
      // Re-analyze after update
      const newAnalysis = await blogManager.seoAPI.analyzePostSEO(postId);
      setAnalysis(newAnalysis);
    } catch (error) {
      console.error('Failed to update SEO:', error);
    } finally {
      setSaving(false);
    }
  };
  
  const generateMetaTags = async () => {
    try {
      const tags = await blogManager.seoAPI.generatePostMetaTags(postId);
      return tags;
    } catch (error) {
      console.error('Failed to generate meta tags:', error);
    }
  };
  
  if (loading) return <div>Loading SEO data...</div>;
  
  return (
    <div className="post-seo-manager">
      <h3>SEO Management</h3>
      
      {/* SEO Form */}
      <SEOForm 
        data={seoData} 
        onSave={handleSeoUpdate} 
        saving={saving}
      />
      
      {/* SEO Analysis */}
      <SEOAnalysis analysis={analysis} />
      
      {/* Meta Tags Preview */}
      <MetaTagsPreview onGenerate={generateMetaTags} />
    </div>
  );
};

const SEOForm = ({ data, onSave, saving }) => {
  const [formData, setFormData] = useState(data || {});
  
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };
  
  return (
    <form onSubmit={handleSubmit} className="seo-form">
      <div className="form-group">
        <label>Meta Title</label>
        <input
          type="text"
          value={formData.meta_title || ''}
          onChange={(e) => setFormData({...formData, meta_title: e.target.value})}
          maxLength="60"
        />
        <small>{(formData.meta_title || '').length}/60 characters</small>
      </div>
      
      <div className="form-group">
        <label>Meta Description</label>
        <textarea
          value={formData.meta_description || ''}
          onChange={(e) => setFormData({...formData, meta_description: e.target.value})}
          maxLength="160"
          rows="3"
        />
        <small>{(formData.meta_description || '').length}/160 characters</small>
      </div>
      
      <div className="form-group">
        <label>Focus Keyword</label>
        <input
          type="text"
          value={formData.focus_keyword || ''}
          onChange={(e) => setFormData({...formData, focus_keyword: e.target.value})}
        />
      </div>
      
      <button type="submit" disabled={saving}>
        {saving ? 'Saving...' : 'Save SEO'}
      </button>
    </form>
  );
};

const SEOAnalysis = ({ analysis }) => {
  if (!analysis) return null;
  
  return (
    <div className="seo-analysis">
      <h4>SEO Analysis</h4>
      <div className="score">
        Overall Score: {analysis.overall_score}/100
      </div>
      
      {analysis.recommendations && (
        <div className="recommendations">
          <h5>Recommendations:</h5>
          <ul>
            {analysis.recommendations.map((rec, index) => (
              <li key={index}>{rec}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
```

### Blog Post Editor Integration

```jsx
import React, { useState, useEffect } from 'react';

const BlogPostEditor = ({ postId, isNew = false }) => {
  const [post, setPost] = useState({});
  const [seo, setSeo] = useState({});
  const [activeTab, setActiveTab] = useState('content');
  
  const blogManager = new BlogPostManager();
  
  useEffect(() => {
    if (!isNew && postId) {
      loadPostData();
    }
  }, [postId, isNew]);
  
  const loadPostData = async () => {
    try {
      const data = await blogManager.getPostWithAnalysis(postId);
      setPost(data.post);
      setSeo(data.seo);
    } catch (error) {
      console.error('Failed to load post:', error);
    }
  };
  
  const handleSave = async () => {
    try {
      if (isNew) {
        // Create new post with SEO
        const result = await blogManager.createCompletePost(post, seo);
        setPost(result.post);
        setSeo(result.seo);
      } else {
        // Update existing post and SEO
        await Promise.all([
          blogManager.blogAPI.updatePost(postId, post),
          blogManager.seoAPI.updatePostSEO(postId, seo)
        ]);
      }
      
      alert('Post saved successfully!');
    } catch (error) {
      console.error('Failed to save post:', error);
      alert('Failed to save post');
    }
  };
  
  return (
    <div className="blog-post-editor">
      <div className="editor-tabs">
        <button 
          className={activeTab === 'content' ? 'active' : ''}
          onClick={() => setActiveTab('content')}
        >
          Content
        </button>
        <button 
          className={activeTab === 'seo' ? 'active' : ''}
          onClick={() => setActiveTab('seo')}
        >
          SEO
        </button>
      </div>
      
      <div className="editor-content">
        {activeTab === 'content' && (
          <PostContentEditor 
            data={post} 
            onChange={setPost} 
          />
        )}
        
        {activeTab === 'seo' && (
          <PostSEOManager 
            postId={postId}
            data={seo}
            onChange={setSeo}
          />
        )}
      </div>
      
      <div className="editor-actions">
        <button onClick={handleSave} className="save-btn">
          Save Post
        </button>
      </div>
    </div>
  );
};
```

## API Endpoints Summary

### Direct SEO API Endpoints
```
POST   /api/cms/v1/seo/meta                    # Create SEO metadata
GET    /api/cms/v1/seo/meta/:id               # Get SEO metadata by ID
PUT    /api/cms/v1/seo/meta/:id               # Update SEO metadata
DELETE /api/cms/v1/seo/meta/:id               # Delete SEO metadata
GET    /api/cms/v1/seo/meta                   # List SEO metadata
GET    /api/cms/v1/seo/meta/by-page           # Get SEO by page
POST   /api/cms/v1/seo/meta/bulk              # Bulk create SEO
POST   /api/cms/v1/seo/meta/:id/analyze       # Analyze SEO
POST   /api/cms/v1/seo/meta/:id/validate      # Validate SEO
GET    /api/cms/v1/seo/meta/:id/tags          # Generate meta tags
```

### Convenience Blog SEO API Endpoints
```
POST   /api/cms/v1/blog/posts/:id/seo           # Create SEO for post
GET    /api/cms/v1/blog/posts/:id/seo           # Get SEO for post
PUT    /api/cms/v1/blog/posts/:id/seo           # Update SEO for post
DELETE /api/cms/v1/blog/posts/:id/seo           # Delete SEO for post
POST   /api/cms/v1/blog/posts/:id/seo/analyze   # Analyze post SEO
POST   /api/cms/v1/blog/posts/:id/seo/validate  # Validate post SEO
GET    /api/cms/v1/blog/posts/:id/seo/tags      # Generate post meta tags
```

## Best Practices

1. **Use Convenience Endpoints for Blog Posts**: For blog-specific operations, use the convenience endpoints to reduce complexity.

2. **Parallel API Calls**: When fetching related data, use `Promise.all()` for better performance.

3. **Error Handling**: Implement comprehensive error handling for both blog and SEO operations.

4. **Validation**: Validate SEO data on the frontend before sending to the API.

5. **Real-time Analysis**: Provide real-time SEO analysis as users type content.

6. **Caching**: Implement appropriate caching strategies for SEO data.

7. **Progressive Enhancement**: Load SEO features progressively to maintain good UX.

This hybrid approach provides maximum flexibility while maintaining ease of use for common blog post SEO operations.