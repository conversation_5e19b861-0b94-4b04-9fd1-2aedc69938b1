# Database Reset & Migration Guide

## Tổng quan

Dự án cung cấp nhiều cách để reset database, chạy migrations và seeders:

## 🛠️ <PERSON><PERSON><PERSON> l<PERSON>nh có sẵn

### 1. Reset hoàn toàn database

```bash
# Cách 1: Sử dụng Makefile
make reset-db-full

# Cách 2: Sử dụng script trực tiếp
./scripts/reset-db-full.sh

# Cách 3: Sử dụng wrapper utility
./scripts/db-reset.sh full
```

### 2. Chỉ migrate và seed (không reset)

```bash
# Cách 1: Sử dụng Makefile
make migrate-and-seed

# Cách 2: Sử dụng script trực tiếp
./scripts/migrate-and-seed.sh

# Cách 3: Sử dụng wrapper utility
./scripts/db-reset.sh ms
```

### 3. Chỉ xóa tất cả bảng

```bash
# Cách 1: Sử dụng Makefile
make reset-db

# Cách 2: Sử dụng Go command
go run ./cmd/reset-db

# Cách 3: Sử dụng wrapper utility
./scripts/db-reset.sh drop
```

## 🎯 Lệnh khuyến nghị

### Wrapper utility (dễ nhớ nhất):

```bash
# Reset hoàn toàn database
./scripts/db-reset.sh full

# Reset với xác nhận tự động
./scripts/db-reset.sh full -y

# Chỉ migrate và seed
./scripts/db-reset.sh ms

# Chỉ migrations
./scripts/db-reset.sh migrate

# Chỉ seeders
./scripts/db-reset.sh seed

# Chỉ xóa tables
./scripts/db-reset.sh drop
```

## 🔧 Tùy chọn nâng cao

### 1. Chỉ làm việc với module cụ thể

```bash
# Reset chỉ module tenant
./scripts/db-reset.sh full -m tenant

# Migrate chỉ module auth
./scripts/db-reset.sh migrate -m auth
```

### 2. Chỉ chạy seeders cụ thể

```bash
# Chỉ chạy user và tenant seeders
./scripts/db-reset.sh seed -s user,tenant

# Reset nhưng chỉ chạy seeders cụ thể
./scripts/reset-db-full.sh -s user,tenant
```

### 3. Bỏ qua seeders

```bash
# Reset nhưng không chạy seeders
./scripts/db-reset.sh full --no-seeds

# Migrate nhưng không chạy seeders
./scripts/migrate-and-seed.sh --no-seeds
```

## 📋 Makefile commands

```bash
# Xem tất cả lệnh database
make help | grep -E "(reset-db|migrate|seed)"

# Database operations
make reset-db              # Chỉ xóa tables
make reset-db-full         # Reset + migrate + seed
make migrate-and-seed      # Migrate + seed (no reset)
make migrate-up            # Chỉ migrate
make seed-run              # Chỉ seed

# Wrapper utility
make db-reset COMMAND=full # Sử dụng wrapper
```

## 🔍 Kiểm tra trạng thái

```bash
# Xem migration status
make migrate-status

# Xem danh sách seeders
make seed-list

# Xem version hiện tại
make migrate-version
```

## ⚠️ Lưu ý quan trọng

1. **Backup trước khi reset**: Lệnh reset sẽ XÓA HOÀN TOÀN tất cả data
2. **Kiểm tra .env**: Đảm bảo DATABASE_URL đúng trong file .env
3. **Database phải running**: MySQL phải đang chạy và accessible
4. **Quyền truy cập**: User database phải có quyền CREATE/DROP tables

## 🔧 Troubleshooting

### Database connection failed
```bash
# Kiểm tra MySQL đang chạy
mysql -h localhost -P 3307 -u root -p

# Kiểm tra .env file
cat .env | grep DATABASE_URL
```

### Migration failed
```bash
# Kiểm tra migration status
make migrate-status

# Rollback nếu cần
make migrate-down
```

### Seeders failed
```bash
# Xem list seeders
make seed-list

# Rollback seeders
make seed-rollback
```

## 📁 File locations

- **Scripts**: `./scripts/`
  - `reset-db-full.sh` - Reset hoàn toàn
  - `migrate-and-seed.sh` - Migrate + seed
  - `db-reset.sh` - Wrapper utility
- **Commands**: `./cmd/`
  - `reset-db/` - Drop all tables
  - `migrate/` - Migration tool
  - `seed/` - Seeder tool
- **Migrations**: `./internal/database/migrations/`
- **Makefile**: `./Makefile` - Tất cả lệnh make