# DTO and Swagger Documentation Guidelines

This document outlines the rules and best practices for implementing DTOs (Data Transfer Objects) and Swagger documentation in the Blog API v3 project.

## 📋 Table of Contents

- [DTO Structure and Naming](#dto-structure-and-naming)
- [Swagger Annotations](#swagger-annotations)
- [Validation Rules](#validation-rules)
- [Type Conversion](#type-conversion)
- [Module Organization](#module-organization)
- [Best Practices](#best-practices)
- [Examples](#examples)

## 🏗️ DTO Structure and Naming

### File Organization
```
internal/modules/{module}/dto/
├── {entity}_dto.go       # Core entity DTOs
├── {feature}_dto.go      # Feature-specific DTOs
└── conversion.go         # Optional: conversion utilities
```

### Naming Conventions

#### Request DTOs
- **Pattern**: `{Entity}{Action}Request`
- **Examples**: 
  - `UserCreateRequest`
  - `BlogPostUpdateRequest`
  - `NotificationSendRequest`

#### Response DTOs
- **Pattern**: `{Entity}Response` or `{Entity}{Action}Response`
- **Examples**:
  - `UserResponse`
  - `BlogPostListResponse`
  - `NotificationStatsResponse`

#### Filter/Query DTOs
- **Pattern**: `{Entity}Filter` or `{Entity}Query`
- **Examples**:
  - `UserFilter`
  - `BlogPostQuery`
  - `NotificationLogFilter`

### Required DTO Types per Module

Each module must implement these core DTO categories:

1. **CRUD Operations**
   - `{Entity}CreateRequest`
   - `{Entity}UpdateRequest`
   - `{Entity}Response`
   - `{Entity}ListResponse`

2. **Query/Filter**
   - `{Entity}Filter`
   - `{Entity}SearchRequest`

3. **Statistics/Analytics** (if applicable)
   - `{Entity}StatsRequest`
   - `{Entity}StatsResponse`

4. **Bulk Operations** (if applicable)
   - `{Entity}BulkActionRequest`
   - `{Entity}BulkActionResponse`

## 🏷️ Swagger Annotations

### JSON Tags
```go
type UserCreateRequest struct {
    Name     string `json:"name" validate:"required,max=255" example:"John Doe"`
    Email    string `json:"email" validate:"required,email" example:"<EMAIL>"`
    Age      *int   `json:"age,omitempty" validate:"omitempty,min=0,max=150" example:"25"`
}
```

### Required Tag Components
1. **`json`**: JSON serialization name
2. **`validate`**: Validation rules
3. **`example`**: Example value for Swagger docs

### Validation Tags

#### Common Validators
- `required`: Field is mandatory
- `omitempty`: Skip validation if empty
- `min=N`: Minimum value/length
- `max=N`: Maximum value/length
- `email`: Valid email format
- `url`: Valid URL format
- `oneof=a b c`: Value must be one of specified options

#### String Validators
```go
Name        string `validate:"required,max=255"`
Email       string `validate:"required,email"`
Status      string `validate:"required,oneof=active inactive"`
Description string `validate:"omitempty,max=1000"`
```

#### Number Validators
```go
Age   int     `validate:"min=0,max=150"`
Price float64 `validate:"min=0"`
Count uint    `validate:"min=1,max=100"`
```

#### Array/Slice Validators
```go
Tags       []string `validate:"omitempty,dive,max=50"`
Recipients []string `validate:"required,min=1,max=10"`
```

### Handler Swagger Annotations

```go
// CreateUser creates a new user
// @Summary Create user
// @Description Create a new user account with validation
// @Tags users
// @Accept json
// @Produce json
// @Param user body dto.UserCreateRequest true "User data"
// @Success 201 {object} dto.UserResponse
// @Failure 400 {object} response.Response "Invalid request data"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
    // Implementation
}
```

#### Required Annotation Elements
1. **`@Summary`**: Brief action description
2. **`@Description`**: Detailed explanation
3. **`@Tags`**: API grouping category
4. **`@Accept`/`@Produce`**: Content types
5. **`@Param`**: Input parameters
6. **`@Success`**: Success responses
7. **`@Failure`**: Error responses
8. **`@Router`**: HTTP method and path

## ✅ Validation Rules

### Field Validation Patterns

#### Required vs Optional Fields
```go
// Required fields
Name  string `json:"name" validate:"required"`

// Optional fields with validation
Age   *int   `json:"age,omitempty" validate:"omitempty,min=0"`

// Optional fields without validation
Notes *string `json:"notes,omitempty"`
```

#### String Length Limits
```go
// Database constraints
Title       string `validate:"required,max=255"`      // VARCHAR(255)
Description string `validate:"omitempty,max=1000"`    // TEXT with limit
Content     string `validate:"required"`              // LONGTEXT
```

#### Enum Validation
```go
type UserStatus string

const (
    UserStatusActive   UserStatus = "active"
    UserStatusInactive UserStatus = "inactive"
    UserStatusSuspended UserStatus = "suspended"
)

type UserUpdateRequest struct {
    Status UserStatus `json:"status" validate:"omitempty,oneof=active inactive suspended"`
}
```

#### Date/Time Validation
```go
type EventCreateRequest struct {
    StartDate time.Time  `json:"start_date" validate:"required"`
    EndDate   *time.Time `json:"end_date,omitempty"`
}
```

## 🔄 Type Conversion

### Service Model Conversion
Each request DTO must implement `ToServiceModel()` method:

```go
// ToServiceModel converts DTO to service layer model
func (r *UserCreateRequest) ToServiceModel() models.CreateUserRequest {
    return models.CreateUserRequest{
        Name:     r.Name,
        Email:    r.Email,
        Age:      r.Age,
        Metadata: r.Metadata,
    }
}
```

### Response Conversion
Service models should have `ToResponse()` method:

```go
// In models package
func (u *User) ToResponse() *dto.UserResponse {
    return &dto.UserResponse{
        ID:        u.ID,
        Name:      u.Name,
        Email:     u.Email,
        Status:    u.Status,
        CreatedAt: u.CreatedAt,
        UpdatedAt: u.UpdatedAt,
    }
}
```

### Handler Integration
```go
func (h *UserHandler) CreateUser(c *gin.Context) {
    var req dto.UserCreateRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid request", err)
        return
    }

    user, err := h.userService.CreateUser(tenantID, req.ToServiceModel())
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "Failed to create user", err)
        return
    }

    response.Success(c, http.StatusCreated, "User created", user.ToResponse())
}
```

## 📁 Module Organization

### DTO File Structure
```go
// user_dto.go
package dto

import (
    "time"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

// User CRUD DTOs
type UserCreateRequest struct { /* ... */ }
type UserUpdateRequest struct { /* ... */ }
type UserResponse struct { /* ... */ }
type UserListResponse struct { /* ... */ }

// User Query DTOs
type UserFilter struct { /* ... */ }
type UserSearchRequest struct { /* ... */ }

// User Statistics DTOs
type UserStatsRequest struct { /* ... */ }
type UserStatsResponse struct { /* ... */ }

// Conversion methods
func (r *UserCreateRequest) ToServiceModel() models.CreateUserRequest { /* ... */ }
func (r *UserUpdateRequest) ToServiceModel() models.UpdateUserRequest { /* ... */ }
```

### Import Organization
```go
import (
    "encoding/json"
    "time"

    "github.com/tranthanhloi/wn-api-v3/internal/modules/{module}/models"
)
```

## 🎯 Best Practices

### 1. Consistent Field Naming
- Use `camelCase` for JSON tags
- Use `PascalCase` for Go struct fields
- Be consistent across all DTOs

### 2. Example Values
- Provide realistic example values
- Use consistent example data across related DTOs
- Examples should pass validation rules

### 3. Validation Rules
- Always validate required fields
- Set appropriate length limits based on database constraints
- Use `omitempty` for optional fields with validation

### 4. Documentation
- Write clear, concise Swagger summaries
- Provide detailed descriptions for complex operations
- Group related endpoints with consistent tags

### 5. Error Handling
- Use standardized error response format
- Provide meaningful error messages
- Include appropriate HTTP status codes

### 6. Pagination
```go
type UserListResponse struct {
    Users      []UserResponse `json:"users"`
    Total      int64         `json:"total" example:"150"`
    Page       int           `json:"page" example:"1"`
    PageSize   int           `json:"page_size" example:"20"`
    TotalPages int           `json:"total_pages" example:"8"`
}

type UserFilter struct {
    Page     int    `json:"page,omitempty" validate:"min=1" example:"1"`
    PageSize int    `json:"page_size,omitempty" validate:"min=1,max=100" example:"20"`
    SortBy   string `json:"sort_by,omitempty" validate:"omitempty,oneof=id name email created_at" example:"created_at"`
    SortOrder string `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc" example:"desc"`
}
```

### 7. Nested Objects
```go
type UserCreateRequest struct {
    Name    string              `json:"name" validate:"required,max=255" example:"John Doe"`
    Profile UserProfileRequest  `json:"profile" validate:"required"`
    Tags    []string           `json:"tags,omitempty" validate:"dive,max=50"`
}

type UserProfileRequest struct {
    FirstName string `json:"first_name" validate:"required,max=100" example:"John"`
    LastName  string `json:"last_name" validate:"required,max=100" example:"Doe"`
    Bio       string `json:"bio,omitempty" validate:"max=500" example:"Software developer"`
}
```

## 📝 Examples

### Complete DTO Implementation

```go
// blog_dto.go
package dto

import (
    "time"
    "github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
)

// BlogPostCreateRequest represents request to create a blog post
type BlogPostCreateRequest struct {
    Title       string                 `json:"title" validate:"required,max=255" example:"Getting Started with Go"`
    Content     string                 `json:"content" validate:"required" example:"This is a comprehensive guide..."`
    Excerpt     *string                `json:"excerpt,omitempty" validate:"omitempty,max=500" example:"Learn Go programming from scratch"`
    Status      models.BlogPostStatus  `json:"status" validate:"required,oneof=draft published scheduled" example:"draft"`
    CategoryID  uint                   `json:"category_id" validate:"required" example:"1"`
    Tags        []string               `json:"tags,omitempty" validate:"dive,max=50" example:"go,programming,tutorial"`
    ScheduledAt *time.Time             `json:"scheduled_at,omitempty" example:"2024-01-15T10:00:00Z"`
    Metadata    map[string]interface{} `json:"metadata,omitempty" example:"{\"author_notes\":\"First post in series\"}"`
}

// BlogPostResponse represents blog post data in responses
type BlogPostResponse struct {
    ID          uint                   `json:"id" example:"1"`
    TenantID    uint                   `json:"tenant_id" example:"1"`
    Title       string                 `json:"title" example:"Getting Started with Go"`
    Slug        string                 `json:"slug" example:"getting-started-with-go"`
    Content     string                 `json:"content" example:"This is a comprehensive guide..."`
    Excerpt     *string                `json:"excerpt,omitempty" example:"Learn Go programming"`
    Status      models.BlogPostStatus  `json:"status" example:"published"`
    CategoryID  uint                   `json:"category_id" example:"1"`
    AuthorID    uint                   `json:"author_id" example:"123"`
    ViewCount   uint                   `json:"view_count" example:"1500"`
    ScheduledAt *time.Time             `json:"scheduled_at,omitempty"`
    PublishedAt *time.Time             `json:"published_at,omitempty"`
    CreatedAt   time.Time              `json:"created_at"`
    UpdatedAt   time.Time              `json:"updated_at"`
    
    // Optional relationships
    Category *BlogCategoryResponse `json:"category,omitempty"`
    Tags     []BlogTagResponse     `json:"tags,omitempty"`
    Author   *UserResponse         `json:"author,omitempty"`
}

// BlogPostListResponse represents response for listing blog posts
type BlogPostListResponse struct {
    Posts      []BlogPostResponse `json:"posts"`
    Total      int64              `json:"total" example:"150"`
    Page       int                `json:"page" example:"1"`
    PageSize   int                `json:"page_size" example:"20"`
    TotalPages int                `json:"total_pages" example:"8"`
}

// BlogPostFilter represents filter parameters for listing posts
type BlogPostFilter struct {
    Status     models.BlogPostStatus `json:"status,omitempty" validate:"omitempty,oneof=draft published scheduled archived" example:"published"`
    CategoryID *uint                 `json:"category_id,omitempty" example:"1"`
    AuthorID   *uint                 `json:"author_id,omitempty" example:"123"`
    Search     string                `json:"search,omitempty" example:"golang tutorial"`
    Tags       []string              `json:"tags,omitempty" example:"go,programming"`
    DateFrom   *time.Time            `json:"date_from,omitempty" example:"2024-01-01T00:00:00Z"`
    DateTo     *time.Time            `json:"date_to,omitempty" example:"2024-12-31T23:59:59Z"`
    Page       int                   `json:"page,omitempty" validate:"min=1" example:"1"`
    PageSize   int                   `json:"page_size,omitempty" validate:"min=1,max=100" example:"20"`
    SortBy     string                `json:"sort_by,omitempty" validate:"omitempty,oneof=id title created_at published_at view_count" example:"created_at"`
    SortOrder  string                `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc" example:"desc"`
}

// ToServiceModel converts BlogPostCreateRequest to models.CreateBlogPostRequest
func (r *BlogPostCreateRequest) ToServiceModel() models.CreateBlogPostRequest {
    return models.CreateBlogPostRequest{
        Title:       r.Title,
        Content:     r.Content,
        Excerpt:     r.Excerpt,
        Status:      r.Status,
        CategoryID:  r.CategoryID,
        Tags:        r.Tags,
        ScheduledAt: r.ScheduledAt,
        Metadata:    r.Metadata,
    }
}
```

### Handler with Swagger Documentation

```go
// CreateBlogPost creates a new blog post
// @Summary Create blog post
// @Description Create a new blog post with content, category, and optional scheduling
// @Tags blog-posts
// @Accept json
// @Produce json
// @Param post body dto.BlogPostCreateRequest true "Blog post data"
// @Success 201 {object} dto.BlogPostResponse "Created blog post"
// @Failure 400 {object} response.Response "Invalid request data"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/posts [post]
func (h *BlogHandler) CreateBlogPost(c *gin.Context) {
    var req dto.BlogPostCreateRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid request data", err)
        return
    }

    tenantID, exists := c.Get("tenant_id")
    if !exists {
        response.Error(c, http.StatusUnauthorized, "Tenant ID not found", nil)
        return
    }

    post, err := h.blogService.CreatePost(tenantID.(uint), req.ToServiceModel())
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "Failed to create post", err)
        return
    }

    response.Success(c, http.StatusCreated, "Post created successfully", post.ToResponse())
}
```

## ⚠️ Common Pitfalls to Avoid

1. **Missing Validation Tags**: Always include appropriate validation
2. **Inconsistent Naming**: Follow naming conventions strictly
3. **Missing Examples**: Include realistic example values
4. **Poor Error Messages**: Provide clear, actionable error descriptions
5. **Incorrect Status Codes**: Use appropriate HTTP status codes
6. **Missing Conversion Methods**: Always implement `ToServiceModel()`
7. **Inconsistent Field Types**: Use consistent types across related DTOs
8. **Missing Swagger Tags**: Group related endpoints properly

## 🔧 Tools and Commands

### Generate Swagger Documentation
```bash
make swagger-gen
```

### Test Compilation
```bash
go run cmd/server/main.go --help
```

### View Swagger UI
Navigate to: `http://localhost:9077/swagger/index.html`

---

## 📚 Additional Resources

- [Go Validator Documentation](https://github.com/go-playground/validator)
- [Gin Binding Documentation](https://gin-gonic.com/docs/examples/binding-and-validation/)
- [Swaggo Documentation](https://github.com/swaggo/swag)
- [API Design Best Practices](https://swagger.io/resources/articles/best-practices-in-api-design/)

---

**Last Updated**: 2025-01-18  
**Version**: 1.0.0  
**Maintained By**: Blog API v3 Development Team