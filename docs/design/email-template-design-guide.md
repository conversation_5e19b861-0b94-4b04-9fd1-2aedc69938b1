# Email Template Design Guide

## Overview

This guide provides practical instructions for designing email templates with dynamic variables. It complements the existing technical documentation by focusing on design principles and variable usage patterns.

## Table of Contents

1. [Design Principles](#design-principles)
2. [Variable System](#variable-system)
3. [Template Structure](#template-structure)
4. [Common Variables](#common-variables)
5. [Advanced Variables](#advanced-variables)
6. [Best Practices](#best-practices)
7. [Template Examples](#template-examples)
8. [Testing & Validation](#testing--validation)
9. [CSS Guidelines](#css-guidelines)
10. [Responsive Design](#responsive-design)
11. [Brand Guidelines](#brand-guidelines)
12. [Performance Optimization](#performance-optimization)
13. [Accessibility Guidelines](#accessibility-guidelines)
14. [Implementation Integration](#implementation-integration)

---

## Design Principles

### 1. Mobile-First Approach
- Design for mobile devices first (320px minimum width)
- Use responsive tables and flexible layouts
- Test on multiple email clients

### 2. Variable-Driven Content
- Use variables for all dynamic content
- Avoid hardcoded values
- Provide fallback values for optional variables

### 3. Accessibility
- Use semantic HTML structure
- Provide alt text for images
- Ensure sufficient color contrast
- Use readable fonts and sizes

---

## Variable System

### Variable Syntax
Variables use **Handlebars** syntax with double curly braces:

```html
{{variable_name}}
```

### Variable Types

| Type | Description | Example |
|------|-------------|---------|
| `string` | Text content | `{{user.name}}` |
| `email` | Email addresses | `{{user.email}}` |
| `url` | Web URLs | `{{verification_url}}` |
| `number` | Numeric values | `{{order.total}}` |
| `boolean` | True/false values | `{{user.is_premium}}` |
| `date` | Date/time values | `{{order.created_at}}` |
| `object` | Nested objects | `{{user.profile.avatar}}` |
| `array` | Lists of items | `{{order.items}}` |

### Conditional Variables
```html
{{#if user.is_premium}}
    <p>Welcome back, Premium Member!</p>
{{else}}
    <p>Upgrade to Premium for more features!</p>
{{/if}}
```

### Loop Variables
```html
{{#each order.items}}
    <tr>
        <td>{{this.name}}</td>
        <td>{{this.price}}</td>
    </tr>
{{/each}}
```

---

## Template Structure

### Basic Template Structure
```html
<!DOCTYPE html>
<html lang="{{website.language}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{email.subject}}</title>
    <style>
        /* CSS styles here */
    </style>
</head>
<body>
    <!-- Header -->
    <table class="header">
        <tr>
            <td>
                <img src="{{website.logo_url}}" alt="{{website.name}}" />
                <h1>{{website.name}}</h1>
            </td>
        </tr>
    </table>
    
    <!-- Content -->
    <table class="content">
        <tr>
            <td>
                {{content.body}}
            </td>
        </tr>
    </table>
    
    <!-- Footer -->
    <table class="footer">
        <tr>
            <td>
                <p>&copy; {{current_year}} {{website.name}}</p>
                <p>{{website.address}}</p>
            </td>
        </tr>
    </table>
</body>
</html>
```

---

## Common Variables

### User Variables
```html
<!-- User Information -->
{{user.id}}           <!-- User ID -->
{{user.name}}          <!-- Full name -->
{{user.first_name}}    <!-- First name -->
{{user.last_name}}     <!-- Last name -->
{{user.email}}         <!-- Email address -->
{{user.phone}}         <!-- Phone number -->
{{user.language}}      <!-- User's preferred language -->
{{user.timezone}}      <!-- User's timezone -->
{{user.avatar_url}}    <!-- Profile picture URL -->
{{user.created_at}}    <!-- Account creation date -->
{{user.is_premium}}    <!-- Premium status (boolean) -->
{{user.subscription_plan}} <!-- Subscription plan name -->
```

### Website/Brand Variables
```html
<!-- Website Information -->
{{website.name}}           <!-- Website/brand name -->
{{website.url}}            <!-- Website URL -->
{{website.logo_url}}       <!-- Logo image URL -->
{{website.favicon_url}}    <!-- Favicon URL -->
{{website.support_email}}  <!-- Support email -->
{{website.phone}}          <!-- Contact phone -->
{{website.address}}        <!-- Physical address -->
{{website.language}}       <!-- Default language -->
{{website.timezone}}       <!-- Default timezone -->
{{website.social_media}}   <!-- Social media links -->
```

### System Variables
```html
<!-- System Information -->
{{current_year}}           <!-- Current year -->
{{current_date}}           <!-- Current date -->
{{current_time}}           <!-- Current time -->
{{app_version}}            <!-- Application version -->
{{environment}}            <!-- Environment (dev/staging/prod) -->
```

### Email-Specific Variables
```html
<!-- Email Metadata -->
{{email.subject}}          <!-- Email subject -->
{{email.preheader}}        <!-- Email preheader text -->
{{email.sender_name}}      <!-- Sender name -->
{{email.sender_email}}     <!-- Sender email -->
{{email.reply_to}}         <!-- Reply-to email -->
{{email.unsubscribe_url}}  <!-- Unsubscribe URL -->
{{email.view_online_url}}  <!-- View online URL -->
```

---

## Advanced Variables

### Authentication Variables
```html
<!-- Authentication & Security -->
{{verification_url}}       <!-- Email verification URL -->
{{verification_token}}     <!-- Verification token -->
{{verification_code}}      <!-- Verification code (6-digit) -->
{{password_reset_url}}     <!-- Password reset URL -->
{{password_reset_token}}   <!-- Password reset token -->
{{login_url}}              <!-- Login page URL -->
{{two_factor_code}}        <!-- 2FA code -->
{{login_ip}}               <!-- Login IP address -->
{{login_location}}         <!-- Login location -->
{{login_device}}           <!-- Login device info -->
{{session_expires_at}}     <!-- Session expiration -->
```

### E-commerce Variables
```html
<!-- Order & Payment -->
{{order.id}}               <!-- Order ID -->
{{order.number}}           <!-- Order number -->
{{order.total}}            <!-- Order total -->
{{order.subtotal}}         <!-- Order subtotal -->
{{order.tax}}              <!-- Tax amount -->
{{order.shipping}}         <!-- Shipping cost -->
{{order.discount}}         <!-- Discount amount -->
{{order.currency}}         <!-- Currency code -->
{{order.status}}           <!-- Order status -->
{{order.created_at}}       <!-- Order date -->
{{order.items}}            <!-- Array of order items -->
{{order.shipping_address}} <!-- Shipping address -->
{{order.billing_address}}  <!-- Billing address -->
{{order.tracking_number}}  <!-- Tracking number -->
{{order.tracking_url}}     <!-- Tracking URL -->

<!-- Payment Information -->
{{payment.method}}         <!-- Payment method -->
{{payment.status}}         <!-- Payment status -->
{{payment.amount}}         <!-- Payment amount -->
{{payment.currency}}       <!-- Payment currency -->
{{payment.last_four}}      <!-- Last 4 digits of card -->
{{payment.expiry_date}}    <!-- Card expiry date -->
```

### Notification Variables
```html
<!-- Notification Context -->
{{notification.type}}      <!-- Notification type -->
{{notification.priority}}  <!-- Priority level -->
{{notification.created_at}}<!-- Notification time -->
{{notification.data}}      <!-- Custom notification data -->
{{notification.action_url}}<!-- Action button URL -->
{{notification.action_text}}<!-- Action button text -->
```

### Content Variables
```html
<!-- Dynamic Content -->
{{content.title}}          <!-- Content title -->
{{content.body}}           <!-- Main content body -->
{{content.excerpt}}        <!-- Content excerpt -->
{{content.author}}         <!-- Content author -->
{{content.published_at}}   <!-- Publication date -->
{{content.featured_image}} <!-- Featured image URL -->
{{content.tags}}           <!-- Content tags -->
{{content.category}}       <!-- Content category -->
{{content.read_time}}      <!-- Estimated read time -->
{{content.url}}            <!-- Content URL -->
```

---

## Best Practices

### 1. Variable Naming
- Use descriptive, hierarchical names: `user.profile.avatar_url`
- Follow consistent naming conventions: `snake_case`
- Group related variables: `order.items`, `order.total`

### 2. Fallback Values
Always provide fallback values for optional variables:
```html
{{user.name || "Valued Customer"}}
{{website.logo_url || "/default-logo.png"}}
{{user.first_name || "there"}}
```

### 3. Conditional Content
Use conditionals to show relevant content:
```html
{{#if user.is_premium}}
    <div class="premium-content">
        <h2>Premium Features</h2>
        <p>Enjoy exclusive content and features!</p>
    </div>
{{/if}}

{{#if order.discount}}
    <tr>
        <td>Discount:</td>
        <td>-{{order.discount}}</td>
    </tr>
{{/if}}
```

### 4. Date Formatting
Use helper functions for date formatting:
```html
{{formatDate order.created_at "YYYY-MM-DD"}}
{{formatDateTime notification.created_at "YYYY-MM-DD HH:mm"}}
{{timeAgo user.last_login}}
```

### 5. Currency Formatting
Format currency values properly:
```html
{{formatCurrency order.total order.currency}}
{{formatPrice product.price "USD"}}
```

### 6. URL Safety
Ensure URLs are properly encoded:
```html
<a href="{{encodeURL verification_url}}">Verify Email</a>
<img src="{{encodeURL user.avatar_url}}" alt="Avatar" />
```

---

## Template Examples

### 1. Email Verification Template
```html
<!DOCTYPE html>
<html lang="{{website.language}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email - {{website.name}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { background: white; padding: 30px; border: 1px solid #ddd; }
        .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{website.name}}</h1>
        </div>
        
        <div class="content">
            <h2>Verify Your Email Address</h2>
            <p>Hello {{user.first_name || "there"}},</p>
            <p>Welcome to {{website.name}}! To complete your registration, please verify your email address by clicking the button below:</p>
            
            <p style="text-align: center;">
                <a href="{{verification_url}}" class="button">Verify Email</a>
            </p>
            
            <p>If you can't click the button, copy and paste this link into your browser:</p>
            <p><a href="{{verification_url}}">{{verification_url}}</a></p>
            
            <p>This verification link will expire in 24 hours.</p>
            
            <p>If you didn't create an account with us, please ignore this email.</p>
        </div>
        
        <div class="footer">
            <p>&copy; {{current_year}} {{website.name}}</p>
            <p>{{website.address}}</p>
            <p>Need help? Contact us at <a href="mailto:{{website.support_email}}">{{website.support_email}}</a></p>
        </div>
    </div>
</body>
</html>
```

### 2. Order Confirmation Template
```html
<!DOCTYPE html>
<html lang="{{website.language}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - {{website.name}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background: #28a745; color: white; padding: 20px; text-align: center; }
        .content { background: white; padding: 30px; border: 1px solid #ddd; }
        .order-info { background: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .items-table th, .items-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        .items-table th { background: #f8f9fa; }
        .total { font-weight: bold; font-size: 1.2em; }
        .footer { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Order Confirmed!</h1>
        </div>
        
        <div class="content">
            <p>Hello {{user.first_name}},</p>
            <p>Thank you for your order! We're excited to confirm that your order has been received and is being processed.</p>
            
            <div class="order-info">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> {{order.number}}</p>
                <p><strong>Order Date:</strong> {{formatDate order.created_at "MMMM D, YYYY"}}</p>
                <p><strong>Total:</strong> {{formatCurrency order.total order.currency}}</p>
                <p><strong>Status:</strong> {{order.status}}</p>
            </div>
            
            <h3>Items Ordered</h3>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Item</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each order.items}}
                    <tr>
                        <td>{{this.name}}</td>
                        <td>{{this.quantity}}</td>
                        <td>{{formatCurrency this.price ../order.currency}}</td>
                        <td>{{formatCurrency this.total ../order.currency}}</td>
                    </tr>
                    {{/each}}
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3"><strong>Subtotal:</strong></td>
                        <td><strong>{{formatCurrency order.subtotal order.currency}}</strong></td>
                    </tr>
                    {{#if order.discount}}
                    <tr>
                        <td colspan="3"><strong>Discount:</strong></td>
                        <td><strong>-{{formatCurrency order.discount order.currency}}</strong></td>
                    </tr>
                    {{/if}}
                    <tr>
                        <td colspan="3"><strong>Tax:</strong></td>
                        <td><strong>{{formatCurrency order.tax order.currency}}</strong></td>
                    </tr>
                    <tr>
                        <td colspan="3"><strong>Shipping:</strong></td>
                        <td><strong>{{formatCurrency order.shipping order.currency}}</strong></td>
                    </tr>
                    <tr class="total">
                        <td colspan="3"><strong>Total:</strong></td>
                        <td><strong>{{formatCurrency order.total order.currency}}</strong></td>
                    </tr>
                </tfoot>
            </table>
            
            <h3>Shipping Address</h3>
            <p>
                {{order.shipping_address.name}}<br>
                {{order.shipping_address.street}}<br>
                {{order.shipping_address.city}}, {{order.shipping_address.state}} {{order.shipping_address.zip}}<br>
                {{order.shipping_address.country}}
            </p>
            
            {{#if order.tracking_number}}
            <p><strong>Tracking Number:</strong> <a href="{{order.tracking_url}}">{{order.tracking_number}}</a></p>
            {{/if}}
            
            <p>We'll send you another email when your order ships.</p>
        </div>
        
        <div class="footer">
            <p>&copy; {{current_year}} {{website.name}}</p>
            <p>Need help? Contact us at <a href="mailto:{{website.support_email}}">{{website.support_email}}</a></p>
        </div>
    </div>
</body>
</html>
```

### 3. Welcome Email Template
```html
<!DOCTYPE html>
<html lang="{{website.language}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{website.name}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .content { background: white; padding: 30px; border: 1px solid #ddd; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        .features { display: flex; flex-wrap: wrap; gap: 20px; margin: 20px 0; }
        .feature { flex: 1; min-width: 200px; text-align: center; padding: 20px; background: #f8f9fa; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to {{website.name}}!</h1>
            <p>We're thrilled to have you join our community</p>
        </div>
        
        <div class="content">
            <h2>Hello {{user.first_name}},</h2>
            <p>Welcome to {{website.name}}! We're excited to have you on board.</p>
            
            <p>Here's what you can do next:</p>
            
            <div class="features">
                <div class="feature">
                    <h3>Complete Your Profile</h3>
                    <p>Add your information to get personalized recommendations</p>
                    <a href="{{profile_url}}" class="button">Edit Profile</a>
                </div>
                
                <div class="feature">
                    <h3>Explore Features</h3>
                    <p>Discover all the amazing features we have to offer</p>
                    <a href="{{features_url}}" class="button">Learn More</a>
                </div>
                
                <div class="feature">
                    <h3>Join Our Community</h3>
                    <p>Connect with other users and share your experiences</p>
                    <a href="{{community_url}}" class="button">Join Community</a>
                </div>
            </div>
            
            {{#if user.is_premium}}
            <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3>🎉 Premium Member Benefits</h3>
                <p>As a premium member, you have access to exclusive features and priority support!</p>
            </div>
            {{/if}}
            
            <p>If you have any questions, don't hesitate to reach out to our support team.</p>
            
            <p>Thanks again for joining us!</p>
            <p>The {{website.name}} Team</p>
        </div>
        
        <div class="footer">
            <p>&copy; {{current_year}} {{website.name}}</p>
            <p>Follow us on social media:</p>
            <p>
                {{#if website.social_media.facebook}}
                <a href="{{website.social_media.facebook}}">Facebook</a>
                {{/if}}
                {{#if website.social_media.twitter}}
                <a href="{{website.social_media.twitter}}">Twitter</a>
                {{/if}}
                {{#if website.social_media.instagram}}
                <a href="{{website.social_media.instagram}}">Instagram</a>
                {{/if}}
            </p>
            <p><a href="{{email.unsubscribe_url}}">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>
```

---

## Testing & Validation

### 1. Variable Testing
Create test data objects to validate your templates:

```json
{
  "user": {
    "id": 1,
    "name": "John Doe",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "is_premium": true,
    "created_at": "2024-01-15T10:30:00Z"
  },
  "website": {
    "name": "My Website",
    "url": "https://example.com",
    "logo_url": "https://example.com/logo.png",
    "support_email": "<EMAIL>",
    "language": "en",
    "social_media": {
      "facebook": "https://facebook.com/mywebsite",
      "twitter": "https://twitter.com/mywebsite"
    }
  },
  "verification_url": "https://example.com/verify?token=abc123",
  "current_year": 2024
}
```

### 2. Email Client Testing
Test your templates across different email clients:

- **Desktop**: Outlook, Apple Mail, Thunderbird
- **Mobile**: iOS Mail, Gmail App, Outlook Mobile
- **Webmail**: Gmail, Yahoo Mail, Outlook.com

### 3. Device Testing
Test responsive design on various screen sizes:

- **Mobile**: 320px - 480px
- **Tablet**: 768px - 1024px
- **Desktop**: 1200px+

### 4. Accessibility Testing
Ensure your templates are accessible:

- Use semantic HTML
- Provide alt text for images
- Ensure sufficient color contrast
- Test with screen readers

### 5. Performance Testing
Optimize for email performance:

- Keep HTML under 100KB
- Optimize images
- Use web-safe fonts
- Minimize CSS

---

## Variable Validation Rules

### Required Variables
Define which variables are required for each template:

```json
{
  "required_variables": [
    "user.email",
    "website.name",
    "verification_url"
  ],
  "optional_variables": [
    "user.first_name",
    "user.avatar_url",
    "website.logo_url"
  ]
}
```

### Variable Types & Validation
```json
{
  "variables": {
    "user.email": {
      "type": "email",
      "required": true,
      "validation": "email"
    },
    "verification_url": {
      "type": "url",
      "required": true,
      "validation": "url"
    },
    "user.first_name": {
      "type": "string",
      "required": false,
      "default": "there",
      "max_length": 50
    }
  }
}
```

---

## Conclusion

This design guide provides a comprehensive framework for creating effective email templates with dynamic variables. By following these guidelines, you can create professional, accessible, and responsive email templates that provide a great user experience across all devices and email clients.

Remember to always test your templates thoroughly and validate that all variables are properly handled before deploying to production.

For technical implementation details, refer to the existing documentation in:
- `/docs/modules/notification/templates.md`
- `/docs/modules/email/templates.md`
- `/internal/modules/notification/models/template.go`

---

## CSS Guidelines

### Email-Safe CSS Properties

#### Supported Properties
```css
/* Layout & Spacing */
width: 100%;
max-width: 600px;
height: auto;
padding: 20px;
margin: 0 auto;
border: 1px solid #ddd;
border-radius: 5px;

/* Typography */
font-family: Arial, Helvetica, sans-serif;
font-size: 16px;
font-weight: bold;
line-height: 1.5;
text-align: center;
text-decoration: none;
color: #333333;

/* Background & Colors */
background-color: #ffffff;
background-image: url('image.jpg');
background-repeat: no-repeat;
background-position: center;
```

#### Properties to Avoid
```css
/* These may not work in all email clients */
position: absolute;
position: fixed;
float: left;
display: flex;
display: grid;
box-shadow: 0 2px 4px rgba(0,0,0,0.1);
transform: translateX(10px);
opacity: 0.8;
```

### CSS Organization

#### Inline vs. Internal CSS
```html
<!-- Critical styles - use inline -->
<td style="padding: 20px; background-color: #f8f9fa; font-family: Arial, sans-serif;">
    Content here
</td>

<!-- Non-critical styles - use internal -->
<style>
    .secondary-text {
        color: #6c757d;
        font-size: 14px;
    }
    
    .button-link {
        display: inline-block;
        padding: 12px 24px;
        background-color: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 4px;
    }
</style>
```

#### CSS Reset for Emails
```css
<style>
    /* Email client reset */
    body, table, td, p, a, li, blockquote {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
    }
    
    table, td {
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
    }
    
    img {
        -ms-interpolation-mode: bicubic;
        border: 0;
        height: auto;
        line-height: 100%;
        outline: none;
        text-decoration: none;
    }
    
    /* Remove spacing around Outlook tables */
    table {
        border-collapse: collapse !important;
    }
    
    /* Force Outlook to provide a "view in browser" link */
    #outlook a {
        padding: 0;
    }
</style>
```

---

## Responsive Design

### Media Queries for Email

#### Mobile-First Approach
```css
<style>
    /* Mobile styles (default) */
    .container {
        width: 100% !important;
        max-width: 600px !important;
    }
    
    .mobile-padding {
        padding: 15px !important;
    }
    
    .mobile-center {
        text-align: center !important;
    }
    
    /* Desktop styles */
    @media only screen and (min-width: 600px) {
        .container {
            width: 600px !important;
        }
        
        .desktop-padding {
            padding: 30px !important;
        }
        
        .desktop-left {
            text-align: left !important;
        }
    }
    
    /* Mobile overrides */
    @media only screen and (max-width: 599px) {
        .mobile-hide {
            display: none !important;
        }
        
        .mobile-full-width {
            width: 100% !important;
            display: block !important;
        }
        
        .mobile-stack {
            display: block !important;
            width: 100% !important;
        }
        
        .mobile-font-size {
            font-size: 14px !important;
        }
    }
</style>
```

#### Responsive Table Layouts
```html
<!-- Two-column responsive layout -->
<table width="100%" cellpadding="0" cellspacing="0">
    <tr>
        <td style="padding: 20px;">
            <table width="100%" cellpadding="0" cellspacing="0">
                <tr>
                    <td class="mobile-stack" style="width: 50%; vertical-align: top;">
                        <h3>Column 1</h3>
                        <p>Content for first column</p>
                    </td>
                    <td class="mobile-stack" style="width: 50%; vertical-align: top;">
                        <h3>Column 2</h3>
                        <p>Content for second column</p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
```

#### Responsive Images
```html
<!-- Fluid images -->
<img src="{{image_url}}" 
     alt="{{image_alt}}" 
     style="width: 100%; max-width: 400px; height: auto; display: block;">

<!-- Responsive image with different sources -->
<img src="{{image_mobile_url}}" 
     class="mobile-img"
     alt="{{image_alt}}" 
     style="width: 100%; max-width: 300px; height: auto; display: block;">

<style>
    @media only screen and (min-width: 600px) {
        .mobile-img {
            content: url('{{image_desktop_url}}');
            max-width: 600px !important;
        }
    }
</style>
```

---

## Brand Guidelines

### Color Palette

#### Primary Colors
```css
/* Brand primary colors */
:root {
    --primary-blue: #007bff;
    --primary-blue-hover: #0056b3;
    --primary-blue-light: #cce7ff;
    
    --secondary-gray: #6c757d;
    --secondary-gray-light: #f8f9fa;
    --secondary-gray-dark: #495057;
}

/* Usage in email */
<td style="background-color: #007bff; color: white; padding: 15px;">
    Primary content
</td>
```

#### Status Colors
```css
/* Status and notification colors */
.success-color { background-color: #28a745; }
.warning-color { background-color: #ffc107; }
.error-color { background-color: #dc3545; }
.info-color { background-color: #17a2b8; }

/* Usage examples */
<div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;">
    <p style="color: #155724; margin: 0;">Success message</p>
</div>

<div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;">
    <p style="color: #721c24; margin: 0;">Error message</p>
</div>
```

### Typography

#### Font Hierarchy
```css
/* Font stack for better compatibility */
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

/* Heading styles */
h1 { font-size: 28px; font-weight: bold; color: #333; line-height: 1.2; margin: 0 0 20px 0; }
h2 { font-size: 24px; font-weight: bold; color: #333; line-height: 1.3; margin: 0 0 15px 0; }
h3 { font-size: 20px; font-weight: bold; color: #333; line-height: 1.4; margin: 0 0 10px 0; }

/* Body text */
p { font-size: 16px; line-height: 1.6; color: #333; margin: 0 0 15px 0; }
.small-text { font-size: 14px; color: #666; }
.caption { font-size: 12px; color: #999; }
```

#### Font Loading
```html
<!-- Web font loading with fallbacks -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    .brand-font {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
    }
</style>
```

### Logo and Branding

#### Logo Implementation
```html
<!-- Responsive logo -->
<table width="100%" cellpadding="0" cellspacing="0">
    <tr>
        <td style="text-align: center; padding: 20px;">
            <img src="{{website.logo_url}}" 
                 alt="{{website.name}}" 
                 style="width: auto; max-width: 200px; height: auto; display: block; margin: 0 auto;">
        </td>
    </tr>
</table>

<!-- Dark mode logo handling -->
<div>
    <!--[if !mso]><!-->
    <img src="{{website.logo_dark_url}}" 
         alt="{{website.name}}" 
         style="width: auto; max-width: 200px; height: auto; display: none;">
    <!--<![endif]-->
    
    <img src="{{website.logo_url}}" 
         alt="{{website.name}}" 
         style="width: auto; max-width: 200px; height: auto; display: block;">
</div>
```

---

## Performance Optimization

### Image Optimization

#### Image Best Practices
```html
<!-- Optimized image usage -->
<img src="{{optimized_image_url}}" 
     alt="{{image_description}}" 
     width="600" 
     height="300" 
     style="width: 100%; max-width: 600px; height: auto; display: block;">

<!-- Image with loading optimization -->
<img src="{{image_url}}" 
     alt="{{image_alt}}" 
     loading="lazy"
     style="width: 100%; max-width: 400px; height: auto;">
```

#### Image Formats
```json
{
  "image_guidelines": {
    "photos": "JPEG (80-90% quality)",
    "graphics": "PNG (8-bit when possible)",
    "logos": "SVG or high-quality PNG",
    "icons": "SVG or PNG",
    "max_file_size": "250KB per image",
    "total_email_size": "< 100KB HTML + CSS"
  }
}
```

### HTML Optimization

#### Minimize HTML
```html
<!-- Optimized table structure -->
<table width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse;">
    <tr>
        <td style="padding: 20px; background-color: #f8f9fa;">
            <h2 style="margin: 0 0 15px 0; font-size: 24px; color: #333;">{{title}}</h2>
            <p style="margin: 0 0 15px 0; font-size: 16px; line-height: 1.6; color: #666;">{{content}}</p>
        </td>
    </tr>
</table>
```

#### CSS Optimization
```css
/* Combine similar styles */
.text-content {
    font-family: Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    margin: 0 0 15px 0;
}

/* Use shorthand properties */
.button {
    padding: 12px 24px;
    margin: 10px 0;
    border: none;
    border-radius: 5px;
    background: #007bff;
    color: white;
    text-decoration: none;
    display: inline-block;
}
```

---

## Accessibility Guidelines

### WCAG 2.1 Compliance

#### Color Contrast
```css
/* Ensure minimum contrast ratios */
.high-contrast {
    background-color: #ffffff;
    color: #000000; /* 21:1 contrast ratio */
}

.good-contrast {
    background-color: #f8f9fa;
    color: #333333; /* 12.6:1 contrast ratio */
}

.minimum-contrast {
    background-color: #007bff;
    color: #ffffff; /* 4.5:1 contrast ratio */
}

/* Avoid low contrast combinations */
.avoid {
    background-color: #f8f9fa;
    color: #999999; /* 2.8:1 - fails WCAG AA */
}
```

#### Semantic HTML
```html
<!-- Use proper heading hierarchy -->
<h1 style="font-size: 28px; color: #333; margin: 0 0 20px 0;">
    Main Email Subject
</h1>

<h2 style="font-size: 24px; color: #333; margin: 0 0 15px 0;">
    Section Heading
</h2>

<h3 style="font-size: 20px; color: #333; margin: 0 0 10px 0;">
    Subsection Heading
</h3>

<!-- Use proper list structure -->
<ul style="margin: 0 0 15px 0; padding-left: 20px;">
    <li style="margin-bottom: 5px;">List item one</li>
    <li style="margin-bottom: 5px;">List item two</li>
    <li style="margin-bottom: 5px;">List item three</li>
</ul>
```

#### Alt Text Guidelines
```html
<!-- Descriptive alt text -->
<img src="{{chart_url}}" 
     alt="Sales chart showing 25% increase in Q4 2023" 
     style="width: 100%; max-width: 500px; height: auto;">

<!-- Decorative images -->
<img src="{{decoration_url}}" 
     alt="" 
     style="width: 100%; max-width: 200px; height: auto;">

<!-- Functional images -->
<img src="{{social_icon_url}}" 
     alt="Follow us on Facebook" 
     style="width: 24px; height: 24px;">
```

#### Screen Reader Support
```html
<!-- ARIA labels for better screen reader support -->
<table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
        <td role="main" style="padding: 20px;">
            <h1 id="main-heading">{{email_subject}}</h1>
            <div role="article" aria-labelledby="main-heading">
                {{email_content}}
            </div>
        </td>
    </tr>
</table>

<!-- Skip links for screen readers -->
<a href="#main-content" 
   style="position: absolute; left: -9999px; font-size: 1px; color: transparent;">
    Skip to main content
</a>
```

---

## Implementation Integration

### Template Engine Integration

#### Handlebars Configuration
```javascript
// Configure Handlebars helpers
const handlebars = require('handlebars');

// Date formatting helper
handlebars.registerHelper('formatDate', (date, format) => {
    return moment(date).format(format);
});

// Currency formatting helper
handlebars.registerHelper('formatCurrency', (amount, currency) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency || 'USD'
    }).format(amount);
});

// URL encoding helper
handlebars.registerHelper('encodeURL', (url) => {
    return encodeURIComponent(url);
});

// Conditional comparison helper
handlebars.registerHelper('eq', (a, b) => a === b);
handlebars.registerHelper('gt', (a, b) => a > b);
handlebars.registerHelper('lt', (a, b) => a < b);
```

#### Template Compilation
```javascript
// Template compilation service
class EmailTemplateService {
    constructor() {
        this.templates = new Map();
    }
    
    async compileTemplate(templateName, templateContent) {
        const compiled = handlebars.compile(templateContent);
        this.templates.set(templateName, compiled);
        return compiled;
    }
    
    async renderTemplate(templateName, data) {
        const template = this.templates.get(templateName);
        if (!template) {
            throw new Error(`Template ${templateName} not found`);
        }
        
        return template(data);
    }
    
    async renderWithLayout(templateName, layoutName, data) {
        const content = await this.renderTemplate(templateName, data);
        const layout = await this.renderTemplate(layoutName, {
            ...data,
            content: content
        });
        
        return layout;
    }
}
```

### Database Integration

#### Template Storage
```sql
-- Template storage schema
CREATE TABLE email_templates (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    type ENUM('transactional', 'marketing', 'notification') NOT NULL,
    subject VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    layout_id INT UNSIGNED,
    variables JSON,
    metadata JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_tenant_template (tenant_id, name),
    INDEX idx_tenant_type (tenant_id, type),
    INDEX idx_active (is_active)
);
```

#### Template Variables Schema
```sql
-- Template variables schema
CREATE TABLE email_template_variables (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    template_id INT UNSIGNED NOT NULL,
    variable_name VARCHAR(255) NOT NULL,
    variable_type ENUM('string', 'email', 'url', 'number', 'boolean', 'date', 'object', 'array') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    default_value TEXT,
    validation_rules JSON,
    description TEXT,
    
    FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE CASCADE,
    UNIQUE KEY uk_template_variable (template_id, variable_name)
);
```

### API Integration

#### Template Management API
```javascript
// Template CRUD operations
app.get('/api/templates', async (req, res) => {
    const templates = await emailTemplateService.listTemplates(req.tenantId);
    res.json(templates);
});

app.post('/api/templates', async (req, res) => {
    const template = await emailTemplateService.createTemplate(req.tenantId, req.body);
    res.json(template);
});

app.put('/api/templates/:id', async (req, res) => {
    const template = await emailTemplateService.updateTemplate(req.params.id, req.body);
    res.json(template);
});

app.post('/api/templates/:id/preview', async (req, res) => {
    const html = await emailTemplateService.previewTemplate(req.params.id, req.body.data);
    res.json({ html });
});

app.post('/api/templates/:id/send', async (req, res) => {
    const result = await emailTemplateService.sendTemplate(req.params.id, req.body);
    res.json(result);
});
```

#### Template Validation API
```javascript
// Template validation service
app.post('/api/templates/validate', async (req, res) => {
    const { template, variables } = req.body;
    
    try {
        // Validate template syntax
        const compiled = handlebars.compile(template);
        
        // Validate variables
        const validation = await validateTemplateVariables(template, variables);
        
        // Test rendering
        const rendered = compiled(variables);
        
        res.json({
            valid: true,
            validation: validation,
            rendered: rendered
        });
    } catch (error) {
        res.json({
            valid: false,
            error: error.message
        });
    }
});
```

### Testing Integration

#### Automated Template Testing
```javascript
// Template testing suite
describe('Email Templates', () => {
    describe('Welcome Template', () => {
        it('should render with required variables', async () => {
            const data = {
                user: { name: 'John Doe', email: '<EMAIL>' },
                website: { name: 'Test Site', url: 'https://test.com' }
            };
            
            const html = await templateService.renderTemplate('welcome', data);
            
            expect(html).toContain('John Doe');
            expect(html).toContain('Test Site');
            expect(html).toContain('https://test.com');
        });
        
        it('should handle missing optional variables', async () => {
            const data = {
                user: { email: '<EMAIL>' },
                website: { name: 'Test Site' }
            };
            
            const html = await templateService.renderTemplate('welcome', data);
            
            expect(html).toContain('there'); // fallback for missing name
            expect(html).not.toContain('undefined');
        });
    });
});
```

---

## Advanced Features

### Multi-language Support

#### Language-specific Templates
```javascript
// Multi-language template handling
class MultiLanguageTemplateService {
    async renderTemplate(templateName, data, language = 'en') {
        const localizedTemplate = `${templateName}_${language}`;
        
        // Try localized template first
        if (this.templates.has(localizedTemplate)) {
            return this.templates.get(localizedTemplate)(data);
        }
        
        // Fall back to default language
        return this.templates.get(`${templateName}_en`)(data);
    }
}
```

#### Translation Variables
```html
<!-- Multi-language template -->
<h1>{{t 'email.welcome.title' name=user.name}}</h1>
<p>{{t 'email.welcome.message' website=website.name}}</p>

<!-- Translation helper -->
<script>
handlebars.registerHelper('t', function(key, options) {
    return i18n.t(key, options.hash);
});
</script>
```

### Dynamic Content

#### Content Personalization
```html
<!-- Personalized content based on user data -->
{{#if user.is_premium}}
    <div class="premium-content">
        <h2>{{t 'email.premium.title'}}</h2>
        <p>{{t 'email.premium.message'}}</p>
    </div>
{{else}}
    <div class="standard-content">
        <h2>{{t 'email.standard.title'}}</h2>
        <p>{{t 'email.standard.message'}}</p>
        <a href="{{upgrade_url}}">{{t 'email.upgrade.cta'}}</a>
    </div>
{{/if}}

<!-- Time-based content -->
{{#if (isBusinessHours current_time user.timezone)}}
    <p>{{t 'email.business_hours.message'}}</p>
{{else}}
    <p>{{t 'email.after_hours.message'}}</p>
{{/if}}
```

### A/B Testing Integration

#### Template Variants
```javascript
// A/B testing for email templates
class ABTestingService {
    async selectTemplateVariant(templateName, userId) {
        const variants = await this.getTemplateVariants(templateName);
        const selectedVariant = this.selectVariantForUser(variants, userId);
        
        // Track variant selection
        await this.trackVariantSelection(templateName, selectedVariant, userId);
        
        return selectedVariant;
    }
    
    async trackEmailOpen(templateName, variant, userId) {
        await this.recordMetric('email_open', {
            template: templateName,
            variant: variant,
            user_id: userId,
            timestamp: new Date()
        });
    }
}
```

---

## Conclusion

This comprehensive email template design guide provides everything needed to create professional, accessible, and effective email templates for the blog-api-v3 system. By following these guidelines, you ensure consistency, maintainability, and excellent user experience across all email communications.

Key takeaways:
- Use semantic HTML and proper CSS for better email client compatibility
- Implement responsive design for mobile and desktop experiences
- Follow accessibility guidelines for inclusive design
- Utilize the variable system for dynamic, personalized content
- Test thoroughly across different email clients and devices
- Integrate with the existing notification system architecture

For additional support or questions about implementation, refer to the technical documentation or contact the development team.