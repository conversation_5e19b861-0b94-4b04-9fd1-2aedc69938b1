# Multi-Tenant User Management - Quản lý User đa Tenant

## KIẾN TRÚC MỚI: Tổng quan

**CẬP NHẬT QUAN TRỌNG**: <PERSON>ệ thống Multi-Tenant User Management đã được cập nhật với kiến trúc mới:

### Thay Đổi Chính:
- **Separated Registration & Onboarding**: User registration không tạo tenant nữa
- **Global User Identity**: Users tồn tại globally, không phụ thuộc tenant
- **Organization Creation Flow**: Onboarding Module xử lý tạo organization/tenant
- **User-Only JWT Tokens**: JWT tokens chỉ chứa thông tin user, không có tenant context
- **Header-Based Tenant Context**: Sử dụng `X-Tenant-ID` header thay vì JWT payload
- **Flexible Membership Management**: Users có thể delay việc tạo organization
- **Real-time Tenant Validation**: Kiểm tra quyền truy cập tenant cho mỗi request

H<PERSON> thống cho phép một user có thể thuộc nhiều tenant khá<PERSON> nhau, với vai trò (role) khác nhau trên mỗi tenant và website. Tài liệu này mô tả cách thiết kế và implement user management system với kiến trúc mới.

## Mục tiêu

### Core Architectural Goals
- **Global User Identity**: Single user account across all tenants
- **Flexible Organization Creation**: Users can create organizations when ready
- **Seamless Context Switching**: Smooth transition between tenant contexts
- **Progressive Disclosure**: Step-by-step feature introduction

### Multi-Tenancy Goals
- **Cross-Tenant Access**: User có thể truy cập nhiều tenant
- **Role-based Permissions**: Role khác nhau trên mỗi tenant/website
- **Context Switching**: Chuyển đổi giữa các tenant/website
- **Permission Isolation**: Phân quyền riêng biệt cho từng context
- **Unified Identity**: Single identity across multiple tenants

## UPDATED: Kiến trúc User-Tenant Relationship

### 1. NEW ARCHITECTURE: Database Schema Design

**IMPORTANT CHANGES**: The database schema has been updated to support the new registration and onboarding flows:

#### Key Changes:
- **Users Table**: Now global, no longer tied to specific tenants
- **Tenant Memberships**: Separate table for user-tenant relationships
- **Context-Aware Sessions**: Sessions support both with/without tenant context
- **Flexible Role Assignments**: Roles can be assigned at different context levels

The multi-tenant user management system uses the following database tables. For complete schema definitions, see the migration files:

#### Core User Tables
- **Global Users**: `internal/database/migrations/c_user/201_create_users_table.up.sql`
- **User Profiles**: `internal/database/migrations/c_user/202_create_user_profiles_table.up.sql`
- **User Preferences**: `internal/database/migrations/c_user/203_create_user_preferences_table.up.sql`
- **Social Links**: `internal/database/migrations/c_user/204_create_user_social_links_table.up.sql`

#### Multi-Tenant Membership Tables
- **Tenant Memberships**: `internal/database/migrations/c_user/205_create_tenant_memberships_table.up.sql`
- **User Invitations**: `internal/database/migrations/c_user/206_create_user_invitations_table.up.sql`

#### Authentication & Sessions
- **User Sessions**: `internal/database/migrations/d_auth/301_create_sessions_table.up.sql`
- **Auth Tokens**: `internal/database/migrations/d_auth/302_create_tokens_table.up.sql`
- **OAuth Connections**: `internal/database/migrations/d_auth/308_create_oauth_connections_table.up.sql`

#### RBAC & Permissions
- **User Roles**: `internal/database/migrations/e_rbac/404_create_rbac_user_roles_table.up.sql`
- **Role Permissions**: `internal/database/migrations/e_rbac/403_create_rbac_role_permissions_table.up.sql`

#### Key Architecture Features
- **Global User Identity**: Single `users` table for cross-tenant identity
- **Website-scoped Sessions**: Each session is tied to a specific website (`website_id` in auth_sessions)
- **Context-aware Role Assignments**: RBAC user roles support `context_type='website'` and `context_id=website_id`
- **Tenant Isolation**: All tenant-specific data is properly isolated with foreign key constraints

### 2. Context-Aware Data Models

The multi-tenant user management system uses the following Go models. For complete model definitions, see the implementation files:

#### Core User Models
- **User**: `internal/modules/user/models/user.go` - Global user identity and authentication
- **User Profile**: `internal/modules/user/models/user_profile.go` - Extended user profile information
- **User Preferences**: `internal/modules/user/models/user_preferences.go` - User preferences and settings
- **Social Links**: `internal/modules/user/models/user_social_link.go` - User social media connections

#### Multi-Tenant Membership Models
- **Tenant Membership**: `internal/modules/user/models/tenant_membership.go` - User membership in tenants
- **User Invitation**: `internal/modules/user/models/user_invitation.go` - Tenant invitation system

#### Authentication Models
- **Session**: `internal/modules/auth/models/session.go` - User sessions with website context
- **Token**: `internal/modules/auth/models/token.go` - JWT and refresh tokens
- **OAuth**: `internal/modules/auth/models/oauth.go` - OAuth connections and providers

#### RBAC Models
- **User Role**: `internal/modules/rbac/models/user_role.go` - Context-aware role assignments
- **Role**: `internal/modules/rbac/models/role.go` - Role definitions with hierarchy
- **Permission**: `internal/modules/rbac/models/permission.go` - Permission definitions and scoping

#### Key Model Features
- **Context Awareness**: All models support tenant and website scoping
- **Soft Deletes**: Use status-based soft deletes instead of deleted_at timestamps
- **JSON Fields**: Flexible JSON fields for preferences and device info
- **Temporal Constraints**: Support for role expiration and validity periods
- **Audit Fields**: Complete audit trail with created/updated timestamps

## JWT Token Architecture

### NEW: User-Only JWT Tokens

**BREAKING CHANGE**: JWT tokens have been redesigned to contain only user-level information, removing all tenant-specific data.

#### Previous JWT Structure (DEPRECATED):
```json
{
  "user_id": 123,
  "email": "<EMAIL>",
  "current_tenant_id": 456,    // ❌ REMOVED
  "current_website_id": 789,   // ❌ REMOVED
  "role": "admin",             // ❌ REMOVED
  "tenant_memberships": [...], // ❌ REMOVED
  "permissions": [...],        // ❌ REMOVED
  "exp": 1640995200
}
```

#### New JWT Structure (CURRENT):
```json
{
  "user_id": 123,
  "email": "<EMAIL>",
  "session_id": 456,
  "scopes": ["read", "write"],
  "token_type": "access",
  "iss": "blog-api-v3",
  "exp": 1640995200,
  "iat": 1640991600,
  "nbf": 1640991600
}
```

#### Key Benefits:
- **Security**: No sensitive tenant information exposed in tokens
- **Flexibility**: Users can access multiple tenants without token regeneration
- **Scalability**: Smaller token size, faster token validation
- **Simplicity**: Clear separation between authentication and authorization

### Tenant Context Management

#### Header-Based Approach (Recommended):
```http
GET /api/cms/v1/blog/posts
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
X-Tenant-ID: 456
```

#### Validation Flow:
1. **JWT Validation**: Extract user_id from token
2. **Header Extraction**: Read tenant_id from X-Tenant-ID header
3. **Membership Check**: Validate `UserBelongsToTenant(user_id, tenant_id)`
4. **Context Setting**: Add tenant_id to request context
5. **Request Processing**: All operations scoped to tenant

#### Migration Impact:
- **Frontend**: Must include X-Tenant-ID header for tenant-scoped requests
- **Backend**: All handlers receive tenant_id from context, not JWT
- **Security**: Real-time tenant access validation on every request
- **Performance**: Minimal impact due to efficient membership caching

## UPDATED: User Registration & Organization Creation Flow

### 1. NEW ARCHITECTURE: User Registration (No Tenant Creation)

**Scenario 1: User registration creates ONLY global user account**

```mermaid
sequenceDiagram
    participant User as New User
    participant Auth as Auth Service
    participant UserSvc as User Service
    participant Onboarding as Onboarding Service
    participant DB as Database
    participant Email as Email Service
    participant Queue as Message Queue
    
    User->>Auth: POST /auth/register
    Note over User,Auth: {email, password, first_name, last_name}
    
    Auth->>Auth: Validate request data
    Auth->>UserSvc: Check if email exists globally
    UserSvc->>DB: SELECT * FROM users WHERE email = ?
    
    alt Email already exists
        UserSvc->>Auth: User exists
        Auth->>User: 409 Email already registered
    else Email not exists
        Auth->>DB: BEGIN TRANSACTION
        
        # Create global user ONLY
        Auth->>UserSvc: Create global user
        UserSvc->>DB: INSERT INTO users (email, password_hash, first_name, last_name, status='active')
        DB->>UserSvc: user_id
        
        # Create user profile (no tenant context)
        Auth->>UserSvc: Create user profile
        UserSvc->>DB: INSERT INTO user_profiles (user_id, tenant_id=NULL)
        
        # Generate JWT tokens WITHOUT tenant context
        Auth->>Auth: Generate JWT tokens (no tenant_id)
        Auth->>DB: INSERT INTO auth_sessions (user_id, tenant_id=NULL)
        
        Auth->>DB: COMMIT TRANSACTION
        
        # Publish registration event
        Auth->>Queue: Publish "user.registered" event
        Note over Queue: {user_id, email, first_name, last_name, needs_onboarding: true}
        
        # Send welcome email with onboarding invitation
        Auth->>Email: Send welcome email
        Email->>User: Welcome email with organization setup invitation
        
        Auth->>User: 201 User registered successfully
        Note over User,Auth: {user: {...}, access_token, refresh_token, needs_onboarding: true}
    end
```

**Điểm chính của kiến trúc mới:**
- **Chỉ tạo user account**: Registration không tạo tenant hoặc organization
- **Global user identity**: User tồn tại globally, không phụ thuộc tenant
- **No tenant context initially**: JWT tokens ban đầu không chứa tenant_id
- **Onboarding invitation**: User được mời tạo organization thông qua onboarding
- **Flexible timing**: User có thể tạo organization ngay lập tức hoặc sau

### 2. NEW: Organization Creation Flow (Onboarding)

**Scenario 2: User creates organization after registration (via onboarding)**

```mermaid
sequenceDiagram
    participant User as Registered User
    participant Onboarding as Onboarding Service
    participant TenantSvc as Tenant Service
    participant UserSvc as User Service
    participant RBAC as RBAC Service
    participant Auth as Auth Service
    participant DB as Database
    participant Queue as Message Queue
    
    User->>Onboarding: POST /onboarding/organization
    Note over User,Onboarding: {name, domain?, plan?, settings?}
    
    Onboarding->>Onboarding: Validate organization data
    Onboarding->>DB: Check domain availability
    
    alt Validation successful
        Onboarding->>DB: BEGIN TRANSACTION
        
        # Create tenant
        Onboarding->>TenantSvc: Create tenant
        TenantSvc->>DB: INSERT INTO tenants (name, slug, owner_user_id, status='active')
        DB->>TenantSvc: tenant_id
        
        # Create tenant membership
        Onboarding->>UserSvc: Create membership
        UserSvc->>DB: INSERT INTO tenant_memberships (user_id, tenant_id, is_primary=true)
        
        # Create default website
        Onboarding->>TenantSvc: Create default website
        TenantSvc->>DB: INSERT INTO websites (tenant_id, name, is_primary=true)
        DB->>TenantSvc: website_id
        
        # Assign owner role
        Onboarding->>RBAC: Assign owner role
        RBAC->>DB: INSERT INTO rbac_user_roles (user_id, tenant_id, role='owner')
        
        # Update user profiles with tenant context
        Onboarding->>UserSvc: Update user profile
        UserSvc->>DB: UPDATE user_profiles SET tenant_id = ? WHERE user_id = ? AND tenant_id IS NULL
        
        Onboarding->>DB: COMMIT TRANSACTION
        
        # Generate new JWT tokens WITH tenant context
        Onboarding->>Auth: Request context switch
        Auth->>Auth: Generate new JWT tokens (with tenant_id, website_id)
        
        # Publish organization created event
        Onboarding->>Queue: Publish "organization.created" event
        Note over Queue: {user_id, tenant_id, website_id, organization_name}
        
        Onboarding->>User: 201 Organization created successfully
        Note over User,Onboarding: {organization: {...}, membership: {...}, tokens: {...}}
    else Validation failed
        Onboarding->>User: 422 Validation errors
    end
```

### 3. User Invitation Flow - Tham gia tenant khác

**Scenario 3: User hiện tại được mời vào tenant khác**

```mermaid
sequenceDiagram
    participant TenantAdmin as Tenant Admin
    participant TenantSvc as Tenant Service
    participant UserSvc as User Service
    participant Email as Email Service
    participant ExistingUser as Existing User
    participant Auth as Auth Service
    participant DB as Database
    
    TenantAdmin->>TenantSvc: POST /tenant/{id}/invite
    Note over TenantAdmin,TenantSvc: {email, role, website_id?, message?}
    
    TenantSvc->>UserSvc: Check if user exists globally
    UserSvc->>DB: SELECT * FROM blog_user WHERE email = ?
    
    alt User exists globally
        UserSvc->>TenantSvc: User found
        TenantSvc->>DB: Check existing membership
        
        alt Already member
            TenantSvc->>TenantAdmin: 409 User already member
        else Not member
            TenantSvc->>DB: CREATE invitation record
            TenantSvc->>Email: Send invitation email
            Email->>ExistingUser: "You're invited to join [Tenant Name]"
            TenantSvc->>TenantAdmin: 200 Invitation sent
            
            # User accepts invitation
            ExistingUser->>Auth: POST /invitation/{token}/accept
            Auth->>TenantSvc: Accept invitation
            TenantSvc->>DB: INSERT INTO blog_tenant_user
            TenantSvc->>DB: INSERT INTO blog_website_user_role
            Auth->>ExistingUser: 200 Invitation accepted
        end
    else User doesn't exist
        TenantSvc->>DB: CREATE invitation record
        TenantSvc->>Email: Send signup invitation
        Email->>ExistingUser: "Join [Tenant Name] - Sign up required"
        TenantSvc->>TenantAdmin: 200 Invitation sent
        
        # User registers via invitation
        ExistingUser->>Auth: POST /auth/register?invitation_token=xyz
        Auth->>Auth: Validate invitation token
        Auth->>UserSvc: Create global user
        Auth->>TenantSvc: Create tenant membership
        Auth->>ExistingUser: 201 User registered + joined tenant
    end
```

**Điểm chính:**
- **Existing user**: Nếu user đã tồn tại, chỉ cần tạo membership mới
- **New user**: Nếu user chưa tồn tại, tạo global user + membership
- **Multiple memberships**: User có thể thuộc nhiều tenant
- **Role-based access**: Mỗi membership có role riêng biệt

### 3. Combined Registration Flow - Tổng hợp

```mermaid
sequenceDiagram
    participant User as User
    participant Auth as Auth Service
    participant UserSvc as User Service
    participant TenantSvc as Tenant Service
    participant DB as Database
    
    User->>Auth: POST /auth/register
    Auth->>Auth: Parse request
    
    alt Has invitation_token
        Auth->>TenantSvc: Validate invitation token
        TenantSvc->>DB: Get invitation details
        
        alt Valid invitation
            Auth->>UserSvc: Create global user
            Auth->>TenantSvc: Create tenant membership with invited role
            Auth->>User: 201 User registered + joined tenant
        else Invalid invitation
            Auth->>User: 400 Invalid invitation
        end
    else No invitation_token
        Auth->>UserSvc: Create global user
        Auth->>TenantSvc: Create personal tenant
        Auth->>TenantSvc: Create tenant membership as owner
        Auth->>User: 201 User registered + personal tenant created
    end
```

**Logic mới:**
1. **Không có invitation_token**: User register → Tạo tenant mới → User là owner
2. **Có invitation_token**: User register → Tham gia tenant được mời → User có role được assign

### 2. Cross-Tenant Invitation

```mermaid
sequenceDiagram
    participant Admin as Tenant Admin
    participant Auth as Auth Service
    participant User as User Service
    participant Tenant as Tenant Service
    participant Email as Email Service
    participant ExistingUser as Existing User
    
    Admin->>Tenant: POST /tenant/{id}/invite
    Note over Admin,Tenant: {email, role, website_id?, message?}
    
    Tenant->>User: Check if global user exists
    User->>User: Query by email
    
    alt User exists globally
        User->>Tenant: User found, check tenant membership
        Tenant->>Tenant: Check existing membership
        
        alt Already member
            Tenant->>Admin: 409 User already member
        else Not member
            Tenant->>Tenant: Create invitation
            Tenant->>Email: Send invitation email
            Email->>ExistingUser: Invitation email with link
            Tenant->>Admin: 200 Invitation sent
        end
    else User doesn't exist
        Tenant->>Tenant: Create invitation record
        Tenant->>Email: Send signup invitation
        Email->>ExistingUser: Signup invitation email
        Tenant->>Admin: 200 Invitation sent
    end
```

### 3. Accept Invitation Flow

```mermaid
sequenceDiagram
    participant User as User
    participant Auth as Auth Service
    participant Tenant as Tenant Service
    participant RBAC as RBAC Service
    participant DB as Database
    
    User->>Auth: GET /auth/invitation/{token}
    Auth->>Tenant: Validate invitation token
    Tenant->>DB: Get invitation details
    
    alt Invalid or expired token
        Tenant->>User: 404 Invalid invitation
    end
    
    Tenant->>Auth: Valid invitation data
    
    alt User not logged in
        Auth->>User: Redirect to login/register
    else User logged in
        Auth->>Tenant: Accept invitation
        Tenant->>DB: Create tenant membership
        Tenant->>RBAC: Assign role
        RBAC->>DB: Create role assignment
        Tenant->>User: 200 Invitation accepted
    end
```

## Context Switching

### 1. Tenant/Website Context Switching

The context switching functionality allows users to switch between different tenant/website contexts. For implementation details, see:

#### Service Implementation
- **User Service**: `internal/modules/user/services/user_service.go` - Tenant membership management
- **RBAC Engine**: `internal/modules/rbac/services/rbac_engine.go` - Context-aware permission checking  
- **Website Service**: `internal/modules/website/services/website_service.go` - Website context validation
- **Context Models**: `internal/modules/website/models/context.go` - Context data structures

#### Key Functionality
- **Context Validation**: Validates user access to tenant/website combinations
- **Role Resolution**: Retrieves user roles for specific website contexts  
- **Permission Caching**: Caches user permissions with context-specific keys
- **Membership Tracking**: Manages user memberships across multiple tenants

### 2. Context-Aware Middleware

Middleware for handling tenant/website context switching is implemented in the RBAC module:

- **Permission Middleware**: `internal/modules/rbac/handlers/middleware.go` - Context-aware permission checking
- **Auth Middleware**: Authentication middleware with context support
- **Tenant Isolation**: Ensures all requests are properly scoped to tenant/website context

## Permission System

The permission system provides context-aware authorization for multi-tenant, multi-website scenarios. For implementation details, see:

### 1. Context-Aware Permission Checking

#### Core RBAC Services
- **RBAC Engine**: `internal/modules/rbac/services/rbac_engine.go` - Fast permission lookup with caching
- **Permission Service**: `internal/modules/rbac/services/permission_service.go` - Permission CRUD and validation
- **User Role Service**: `internal/modules/rbac/services/user_role_service.go` - Role assignment management

#### Permission Checking Handlers
- **Permission Check Handler**: `internal/modules/rbac/handlers/permission_check_handler.go` - Real-time permission validation endpoints

### 2. Permission Middleware

RBAC middleware provides declarative permission checking for HTTP routes:

- **Permission Middleware**: `internal/modules/rbac/handlers/middleware.go` - Complete middleware suite including:
  - `RequirePermission()` - Check specific permissions
  - `RequireRole()` - Check user roles  
  - `RequireAnyPermission()` - Check multiple permissions (OR logic)
  - `RequireAllPermissions()` - Check multiple permissions (AND logic)
  - `RequireOwnership()` - Check resource ownership
  - `RequireContext()` - Validate tenant/website context

#### Key Features
- **Caching**: 15-minute permission cache with LRU eviction
- **Context Isolation**: All permissions scoped to current tenant/website
- **Pattern Matching**: Wildcard permission patterns (e.g., `posts.*`, `admin.users.*.read`)
- **Resource Ownership**: Automatic ownership validation for user-owned resources

## API Endpoints

The multi-tenant user management system provides REST API endpoints for user context switching, tenant management, and role assignments. For complete API implementations, see:

### 1. User Context Management

User context switching and management endpoints are implemented in:

- **User Handler**: `internal/modules/user/handlers/user_handler.go` - Core user management endpoints
- **User Analytics Handler**: `internal/modules/user/handlers/user_analytics_handler.go` - User analytics and insights
- **User Search Handler**: `internal/modules/user/handlers/user_search_handler.go` - User search functionality

#### Key Endpoints
- `GET /api/user/contexts` - Get user's accessible tenant/website contexts
- `POST /api/user/context/switch` - Switch to different tenant/website context
- `GET /api/user/context/current` - Get current active context
- `GET /api/user/memberships` - List user's tenant memberships

### 2. Tenant Invitation Management

Tenant invitation and membership management endpoints:

- **Tenant Handler**: `internal/modules/tenant/handlers/tenant_handler.go` - Tenant management and invitations
- **User Invitation Models**: `internal/modules/user/models/user_invitation.go` - Invitation data structures

#### Key Endpoints
- `POST /api/tenant/{id}/invite` - Send invitation to join tenant
- `POST /api/invitation/{token}/accept` - Accept tenant invitation
- `GET /api/tenant/{id}/members` - List tenant members
- `PUT /api/tenant/{id}/members/{userId}/role` - Update member role

### 3. RBAC Management

Role and permission management for multi-tenant contexts:

- **RBAC Routes**: `internal/modules/rbac/routes.go` - Complete RBAC API routing
- **User Role Handler**: `internal/modules/rbac/handlers/user_role_handler.go` - User role assignments
- **Permission Check Handler**: `internal/modules/rbac/handlers/permission_check_handler.go` - Real-time permission checking

#### Key Features
- **Context-aware role assignments**: All roles scoped to specific tenant/website
- **Real-time permission checking**: Fast permission validation with caching
- **Temporal role assignments**: Support for temporary roles with expiration
- **Bulk operations**: Efficient batch role assignments and permission checks

## Best Practices

### 1. Security Considerations
- **Context Validation**: Always validate user access to tenant/website context
- **Permission Caching**: Cache permissions with short TTL to balance performance and security
- **Session Context**: Store current context in session for seamless experience
- **Audit Logging**: Log all context switches and permission checks

### 2. Performance Optimization
- **Context Caching**: Cache user contexts to avoid repeated database queries
- **Permission Batching**: Batch permission checks where possible
- **Lazy Loading**: Load role details only when needed
- **Index Optimization**: Proper database indexes for multi-tenant queries

### 3. User Experience
- **Context Picker**: Provide clear UI for context switching
- **Context Persistence**: Remember last used context per user
- **Role Visibility**: Show user's role on current context
- **Permission Feedback**: Clear error messages for permission denials

### 4. Development Guidelines
- **Context Injection**: Always inject user context in request handlers
- **Permission Decorators**: Use RBAC middleware for permission checks
- **Resource Scoping**: Scope all queries to current tenant/website context
- **Migration Strategy**: Plan for migrating existing single-tenant users
- **Testing**: Use website-scoped test data for integration tests
- **Documentation**: Reference actual implementation files instead of embedded code