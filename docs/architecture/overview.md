# Architecture Overview

## Introduction

Blog API v3 is a RESTful API built with Go, following a modular architecture pattern that emphasizes separation of concerns, scalability, and maintainability. The system is designed to support blog management with extensible modules and plugins.

## Core Technologies

### Backend Stack
- **Go 1.21+**: Modern Go with generics support
- **Gin**: High-performance HTTP web framework
- **GORM**: Full-featured ORM for Go with MySQL support
- **JWT**: JSON Web Tokens for authentication
- **Redis**: Caching and session management (optional)

### Database
- **MySQL 8.0+**: Primary relational database
- **Database Migrations**: Version-controlled schema changes
- **Connection Pooling**: Optimized database connections

### Development Tools
- **Air**: Live reload for development
- **Mockery**: Mock generation for testing
- **Golangci-lint**: Code linting and formatting
- **Docker**: Containerization support

## Architectural Principles

### 1. Modular Design
The application is organized into self-contained modules that can be developed, tested, and deployed independently.

### 2. Clean Architecture
Following clean architecture principles with clear separation between:
- **Handlers**: HTTP request/response handling
- **Services**: Business logic implementation
- **Repositories**: Data access abstraction
- **Models**: Data structures and domain entities

### 3. Dependency Injection
Dependencies are injected at runtime, making the system testable and flexible.

### 4. Plugin System
Extensible plugin architecture allows adding new functionality without modifying core code.

## System Components

### Core Layer
- **Configuration Management**: Environment-based configuration
- **Database Connection**: GORM-based database abstraction
- **HTTP Server**: Gin-based REST API server
- **Middleware**: Authentication, logging, rate limiting

### Module Layer
- **Auth Module**: User authentication and authorization
- **Blog Module**: Post, category, and comment management
- **Notification Module**: Multi-channel notification system

### Plugin Layer
- **Rate Limiter**: Request rate limiting
- **Cache**: Redis-based caching
- **Storage**: File storage abstraction

## Request Flow

### Standard Request Flow
```
Client Request
    ↓
Gin Router
    ↓
JWT Auth Middleware (validates token, extracts user_id)
    ↓
Tenant Context Middleware (validates X-Tenant-ID header)
    ↓
Other Middleware (CORS, Rate Limit, Logging)
    ↓
Module Handler
    ↓
Service Layer (Business Logic with tenant_id from context)
    ↓
Repository Layer (Data Access with tenant isolation)
    ↓
GORM/Database (tenant-scoped queries)
    ↓
Response
```

### Multi-Tenant Request Example
```
GET /api/cms/v1/blog/posts
Headers:
  Authorization: Bearer eyJhbGciOiJIUzI1NiI...
  X-Tenant-ID: 456

Flow:
1. JWT Middleware → extracts user_id: 123
2. Tenant Middleware → validates user 123 has access to tenant 456
3. Blog Handler → receives context with tenant_id: 456
4. Blog Service → applies business logic for tenant 456
5. Blog Repository → queries: WHERE tenant_id = 456
6. Response → tenant-scoped blog posts
```

## Data Flow

### Write Operations
1. Client sends request with data
2. Handler validates input
3. Service applies business rules
4. Repository persists to database
5. Response returned to client

### Read Operations
1. Client requests data
2. Handler processes request
3. Service applies filters/permissions
4. Repository queries database
5. Data transformed and returned

## Security Architecture

### Authentication
- **User-only JWT tokens**: Contain minimal user information (user_id, email, session_id)
- **Stateless design**: No tenant or role information stored in tokens
- **Refresh token mechanism**: Secure token rotation for extended sessions
- **Multi-tenant access**: Header-based tenant context with real-time validation

### Authorization
- **Two-tier middleware**: JWT authentication + tenant context validation
- **Header-based tenant context**: `X-Tenant-ID` header for tenant-scoped operations
- **Real-time membership validation**: `UserBelongsToTenant` checks for each request
- **Role-based access control (RBAC)**: Per-tenant role assignments via membership
- **API key support**: Service-to-service communication (future enhancement)

### Data Protection
- Input validation and sanitization
- SQL injection prevention (GORM)
- XSS protection
- HTTPS enforcement

## Scalability Considerations

### Horizontal Scaling
- Stateless design enables horizontal scaling
- Database connection pooling
- Load balancer support

### Performance Optimization
- Database indexing strategy
- Query optimization
- Caching layers (Redis)
- Pagination for large datasets

### Resource Management
- Graceful shutdown handling
- Connection pool management
- Memory optimization

## Monitoring and Observability

### Logging
- Structured logging with different levels
- Request/response logging
- Error tracking and alerting

### Metrics
- Performance metrics collection
- Database query monitoring
- Resource utilization tracking

### Health Checks
- Application health endpoints
- Database connectivity checks
- External service monitoring

## Development Philosophy

### Code Quality
- Comprehensive testing strategy
- Code review requirements
- Automated linting and formatting

### Documentation
- Code documentation standards
- API documentation (OpenAPI/Swagger)
- Architecture decision records

### Maintainability
- Clear naming conventions
- Consistent error handling
- Modular design patterns

## Technology Decisions

### Why Go?
- Performance and concurrency
- Simple deployment (single binary)
- Strong typing and error handling
- Excellent standard library

### Why Gin?
- High performance HTTP framework
- Minimal boilerplate
- Excellent middleware ecosystem
- JSON binding and validation

### Why GORM?
- Feature-rich ORM
- Migration support
- Association handling
- Query builder with type safety

### Why MySQL?
- ACID compliance
- Mature ecosystem
- Excellent performance
- Wide adoption and support

## Future Considerations

### Microservices Migration
The modular architecture provides a clear path for migrating to microservices if needed.

### Event-Driven Architecture
Plugin system can be extended to support event-driven patterns.

### Multi-Database Support
Repository pattern allows for easy database backend switching.

### API Versioning
Current structure supports API versioning strategies.

## Related Documentation

- [Project Structure](./project-structure.md)
- [Database Design](./database.md)
- [Module System](../modules/overview.md)
- [Plugin System](../plugins/overview.md)