# Security Best Practices Implementation Guide

## 1. Authentication & Authorization Enhancements

### 1.1 JWT Token Security
**Current Implementation Review**: <PERSON><PERSON>n c<PERSON><PERSON> thiện JWT handling

**Recommended Improvements**:

```go
// Enhanced JWT configuration
type JWTConfig struct {
    AccessTokenTTL     time.Duration `json:"access_token_ttl" validate:"required,min=5m"`
    RefreshTokenTTL    time.Duration `json:"refresh_token_ttl" validate:"required,min=24h"`
    SigningMethod      string        `json:"signing_method" validate:"required,oneof=HS256 RS256"`
    PrivateKeyPath     string        `json:"private_key_path,omitempty"`
    PublicKeyPath      string        `json:"public_key_path,omitempty"`
    Issuer             string        `json:"issuer" validate:"required"`
    Audience           string        `json:"audience" validate:"required"`
    TokenRotationEnabled bool        `json:"token_rotation_enabled"`
}

// Token blacklist for logout
type TokenBlacklist interface {
    BlacklistToken(ctx context.Context, tokenID string, expiresAt time.Time) error
    IsBlacklisted(ctx context.Context, tokenID string) (bool, error)
    CleanupExpired(ctx context.Context) error
}
```

### 1.2 Password Security Enhancement
```go
// Enhanced password validation
type PasswordPolicy struct {
    MinLength        int  `json:"min_length" validate:"min=8"`
    RequireUppercase bool `json:"require_uppercase"`
    RequireLowercase bool `json:"require_lowercase"`
    RequireNumbers   bool `json:"require_numbers"`
    RequireSymbols   bool `json:"require_symbols"`
    MaxAge           time.Duration `json:"max_age"` // Force password change
    PreventReuse     int  `json:"prevent_reuse"` // Number of previous passwords to check
}

func ValidatePassword(password string, policy PasswordPolicy, userID uint) error {
    // Implementation with comprehensive checks
    if len(password) < policy.MinLength {
        return errors.New("password too short")
    }
    
    // Check against common passwords
    if isCommonPassword(password) {
        return errors.New("password is too common")
    }
    
    // Check against user's previous passwords
    if policy.PreventReuse > 0 {
        if err := checkPasswordReuse(userID, password, policy.PreventReuse); err != nil {
            return err
        }
    }
    
    return nil
}
```

### 1.3 Multi-Factor Authentication Enhancement
```go
// Enhanced 2FA implementation
type TwoFactorAuth struct {
    Type        string    `json:"type" validate:"oneof=totp sms email"`
    Secret      string    `json:"secret,omitempty"`
    BackupCodes []string  `json:"backup_codes,omitempty"`
    LastUsed    time.Time `json:"last_used"`
    FailCount   int       `json:"fail_count"`
    LockedUntil time.Time `json:"locked_until,omitempty"`
}

// Rate limiting for 2FA attempts
type TwoFactorRateLimit struct {
    MaxAttempts     int           `json:"max_attempts"`
    WindowDuration  time.Duration `json:"window_duration"`
    LockoutDuration time.Duration `json:"lockout_duration"`
}
```

## 2. Input Validation & Sanitization

### 2.1 Comprehensive Input Validation
```go
// Custom validators for business logic
func RegisterCustomValidators(v *validator.Validate) {
    v.RegisterValidation("username", validateUsername)
    v.RegisterValidation("strong_password", validateStrongPassword)
    v.RegisterValidation("safe_html", validateSafeHTML)
    v.RegisterValidation("no_sql_injection", validateNoSQLInjection)
}

func validateUsername(fl validator.FieldLevel) bool {
    username := fl.Field().String()
    // Only alphanumeric and underscore, 3-30 chars
    matched, _ := regexp.MatchString(`^[a-zA-Z0-9_]{3,30}$`, username)
    return matched
}

func validateSafeHTML(fl validator.FieldLevel) bool {
    content := fl.Field().String()
    // Use bluemonday or similar to sanitize HTML
    policy := bluemonday.UGCPolicy()
    sanitized := policy.Sanitize(content)
    return sanitized == content
}
```

### 2.2 File Upload Security
```go
type FileUploadConfig struct {
    MaxFileSize      int64    `json:"max_file_size"`
    AllowedMimeTypes []string `json:"allowed_mime_types"`
    AllowedExtensions []string `json:"allowed_extensions"`
    ScanForMalware   bool     `json:"scan_for_malware"`
    QuarantinePath   string   `json:"quarantine_path"`
}

func ValidateFileUpload(file *multipart.FileHeader, config FileUploadConfig) error {
    // Check file size
    if file.Size > config.MaxFileSize {
        return errors.New("file too large")
    }
    
    // Check file extension
    ext := strings.ToLower(filepath.Ext(file.Filename))
    if !contains(config.AllowedExtensions, ext) {
        return errors.New("file type not allowed")
    }
    
    // Check MIME type
    src, err := file.Open()
    if err != nil {
        return err
    }
    defer src.Close()
    
    buffer := make([]byte, 512)
    _, err = src.Read(buffer)
    if err != nil {
        return err
    }
    
    mimeType := http.DetectContentType(buffer)
    if !contains(config.AllowedMimeTypes, mimeType) {
        return errors.New("invalid file type")
    }
    
    return nil
}
```

## 3. Rate Limiting & DDoS Protection

### 3.1 Advanced Rate Limiting
```go
type RateLimitConfig struct {
    Global struct {
        RequestsPerSecond int `json:"requests_per_second"`
        BurstSize         int `json:"burst_size"`
    } `json:"global"`
    
    PerIP struct {
        RequestsPerMinute int `json:"requests_per_minute"`
        RequestsPerHour   int `json:"requests_per_hour"`
    } `json:"per_ip"`
    
    PerUser struct {
        RequestsPerMinute int `json:"requests_per_minute"`
        RequestsPerHour   int `json:"requests_per_hour"`
    } `json:"per_user"`
    
    PerEndpoint map[string]struct {
        RequestsPerMinute int `json:"requests_per_minute"`
        RequestsPerHour   int `json:"requests_per_hour"`
    } `json:"per_endpoint"`
}

// Sliding window rate limiter
type SlidingWindowRateLimiter struct {
    redis  redis.Client
    window time.Duration
    limit  int
}

func (r *SlidingWindowRateLimiter) Allow(ctx context.Context, key string) (bool, error) {
    now := time.Now()
    pipeline := r.redis.Pipeline()
    
    // Remove expired entries
    pipeline.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", now.Add(-r.window).UnixNano()))
    
    // Count current requests
    pipeline.ZCard(ctx, key)
    
    // Add current request
    pipeline.ZAdd(ctx, key, &redis.Z{
        Score:  float64(now.UnixNano()),
        Member: fmt.Sprintf("%d", now.UnixNano()),
    })
    
    // Set expiration
    pipeline.Expire(ctx, key, r.window)
    
    results, err := pipeline.Exec(ctx)
    if err != nil {
        return false, err
    }
    
    count := results[1].(*redis.IntCmd).Val()
    return count < int64(r.limit), nil
}
```

### 3.2 IP Blocking & Geolocation
```go
type IPSecurityConfig struct {
    BlockedIPs      []string `json:"blocked_ips"`
    BlockedCountries []string `json:"blocked_countries"`
    AllowedCountries []string `json:"allowed_countries"`
    EnableGeoBlocking bool    `json:"enable_geo_blocking"`
    TrustedProxies   []string `json:"trusted_proxies"`
}

func IPSecurityMiddleware(config IPSecurityConfig) gin.HandlerFunc {
    return func(c *gin.Context) {
        clientIP := c.ClientIP()
        
        // Check if IP is blocked
        if contains(config.BlockedIPs, clientIP) {
            c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
                "error": "IP address is blocked",
            })
            return
        }
        
        // Geolocation check
        if config.EnableGeoBlocking {
            country, err := getCountryByIP(clientIP)
            if err == nil {
                if len(config.AllowedCountries) > 0 && !contains(config.AllowedCountries, country) {
                    c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
                        "error": "Access from this country is not allowed",
                    })
                    return
                }
                
                if contains(config.BlockedCountries, country) {
                    c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
                        "error": "Access from this country is blocked",
                    })
                    return
                }
            }
        }
        
        c.Next()
    }
}
```

## 4. Data Protection & Encryption

### 4.1 Data Encryption at Rest
```go
type EncryptionConfig struct {
    Algorithm    string `json:"algorithm" validate:"oneof=AES-256-GCM ChaCha20-Poly1305"`
    KeyRotation  bool   `json:"key_rotation"`
    KeyRotationInterval time.Duration `json:"key_rotation_interval"`
}

type DataEncryption interface {
    Encrypt(ctx context.Context, plaintext []byte) ([]byte, error)
    Decrypt(ctx context.Context, ciphertext []byte) ([]byte, error)
    RotateKey(ctx context.Context) error
}

// Field-level encryption for sensitive data
type EncryptedField struct {
    Value     string `json:"value"`
    KeyID     string `json:"key_id"`
    Algorithm string `json:"algorithm"`
}

func (e *EncryptedField) Encrypt(plaintext string, encryption DataEncryption) error {
    ciphertext, err := encryption.Encrypt(context.Background(), []byte(plaintext))
    if err != nil {
        return err
    }
    e.Value = base64.StdEncoding.EncodeToString(ciphertext)
    return nil
}
```

### 4.2 PII Data Handling
```go
type PIIField struct {
    Value     string    `json:"value"`
    Encrypted bool      `json:"encrypted"`
    Hashed    bool      `json:"hashed"`
    Masked    bool      `json:"masked"`
    LastAccess time.Time `json:"last_access"`
}

// Automatic PII detection and protection
func ProtectPII(data interface{}) interface{} {
    // Use reflection to find and protect PII fields
    // Email, phone, SSN, credit card numbers, etc.
    return data
}

// Data retention policy
type DataRetentionPolicy struct {
    PIIRetentionPeriod     time.Duration `json:"pii_retention_period"`
    LogRetentionPeriod     time.Duration `json:"log_retention_period"`
    BackupRetentionPeriod  time.Duration `json:"backup_retention_period"`
    AutoDeleteEnabled      bool          `json:"auto_delete_enabled"`
}
```

## 5. Audit Logging & Monitoring

### 5.1 Comprehensive Audit Logging
```go
type AuditLog struct {
    ID          uint      `json:"id"`
    UserID      uint      `json:"user_id,omitempty"`
    TenantID    uint      `json:"tenant_id,omitempty"`
    Action      string    `json:"action"`
    Resource    string    `json:"resource"`
    ResourceID  string    `json:"resource_id,omitempty"`
    IPAddress   string    `json:"ip_address"`
    UserAgent   string    `json:"user_agent"`
    RequestID   string    `json:"request_id"`
    Success     bool      `json:"success"`
    ErrorCode   string    `json:"error_code,omitempty"`
    Details     JSON      `json:"details,omitempty"`
    Timestamp   time.Time `json:"timestamp"`
}

// Audit middleware
func AuditMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        // Capture request details
        requestBody, _ := io.ReadAll(c.Request.Body)
        c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
        
        c.Next()
        
        // Log after request completion
        audit := AuditLog{
            Action:    c.Request.Method + " " + c.Request.URL.Path,
            IPAddress: c.ClientIP(),
            UserAgent: c.Request.UserAgent(),
            RequestID: c.GetString("request_id"),
            Success:   c.Writer.Status() < 400,
            Timestamp: start,
        }
        
        if userID, exists := c.Get("user_id"); exists {
            audit.UserID = userID.(uint)
        }
        
        // Save audit log asynchronously
        go saveAuditLog(audit)
    }
}
```

### 5.2 Security Event Detection
```go
type SecurityEvent struct {
    Type        string                 `json:"type"`
    Severity    string                 `json:"severity"`
    UserID      uint                   `json:"user_id,omitempty"`
    IPAddress   string                 `json:"ip_address"`
    Details     map[string]interface{} `json:"details"`
    Timestamp   time.Time              `json:"timestamp"`
    Resolved    bool                   `json:"resolved"`
    ResolvedBy  uint                   `json:"resolved_by,omitempty"`
    ResolvedAt  time.Time              `json:"resolved_at,omitempty"`
}

// Security event types
const (
    EventBruteForceAttempt = "brute_force_attempt"
    EventSuspiciousLogin   = "suspicious_login"
    EventMultipleFailedLogins = "multiple_failed_logins"
    EventUnusualActivity   = "unusual_activity"
    EventPrivilegeEscalation = "privilege_escalation"
    EventDataExfiltration  = "data_exfiltration"
)

func DetectSecurityEvents() {
    // Implement real-time security event detection
    // - Multiple failed login attempts
    // - Login from unusual locations
    // - Privilege escalation attempts
    // - Unusual data access patterns
}
```

## 6. API Security Headers

### 6.1 Comprehensive Security Headers
```go
func SecurityHeadersMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Prevent MIME type sniffing
        c.Header("X-Content-Type-Options", "nosniff")
        
        // Prevent clickjacking
        c.Header("X-Frame-Options", "DENY")
        
        // XSS protection
        c.Header("X-XSS-Protection", "1; mode=block")
        
        // HSTS
        c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload")
        
        // CSP
        csp := "default-src 'self'; " +
               "script-src 'self' 'unsafe-inline'; " +
               "style-src 'self' 'unsafe-inline'; " +
               "img-src 'self' data: https:; " +
               "font-src 'self'; " +
               "connect-src 'self'; " +
               "frame-ancestors 'none'"
        c.Header("Content-Security-Policy", csp)
        
        // Referrer policy
        c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
        
        // Permissions policy
        c.Header("Permissions-Policy", "geolocation=(), microphone=(), camera=()")
        
        // Remove server information
        c.Header("Server", "")
        
        c.Next()
    }
}
```

## 7. Database Security

### 7.1 SQL Injection Prevention
```go
// Always use parameterized queries
func GetUserByEmail(db *gorm.DB, email string) (*User, error) {
    var user User
    err := db.Where("email = ?", email).First(&user).Error
    return &user, err
}

// Input sanitization for dynamic queries
func SanitizeInput(input string) string {
    // Remove or escape dangerous characters
    input = strings.ReplaceAll(input, "'", "''")
    input = strings.ReplaceAll(input, ";", "")
    input = strings.ReplaceAll(input, "--", "")
    return input
}
```

### 7.2 Database Connection Security
```go
type DatabaseConfig struct {
    Host            string        `json:"host"`
    Port            int           `json:"port"`
    Username        string        `json:"username"`
    Password        string        `json:"password"`
    Database        string        `json:"database"`
    SSLMode         string        `json:"ssl_mode" validate:"oneof=disable require verify-ca verify-full"`
    SSLCert         string        `json:"ssl_cert,omitempty"`
    SSLKey          string        `json:"ssl_key,omitempty"`
    SSLRootCert     string        `json:"ssl_root_cert,omitempty"`
    MaxConnections  int           `json:"max_connections"`
    ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
    EncryptionKey   string        `json:"encryption_key"`
}
```

## 8. Implementation Checklist

### Phase 1: Critical Security (Week 1)
- [ ] Implement comprehensive input validation
- [ ] Add security headers middleware
- [ ] Enhance password policies
- [ ] Implement rate limiting
- [ ] Add audit logging

### Phase 2: Authentication & Authorization (Week 2)
- [ ] Enhance JWT security
- [ ] Implement token blacklisting
- [ ] Improve 2FA implementation
- [ ] Add session management
- [ ] Implement IP blocking

### Phase 3: Data Protection (Week 3)
- [ ] Implement data encryption
- [ ] Add PII protection
- [ ] Enhance database security
- [ ] Implement data retention policies
- [ ] Add backup encryption

### Phase 4: Monitoring & Detection (Week 4)
- [ ] Implement security event detection
- [ ] Add real-time monitoring
- [ ] Create security dashboards
- [ ] Implement alerting system
- [ ] Add incident response procedures

## Testing Security Implementation

### Security Testing Checklist
- [ ] Penetration testing
- [ ] Vulnerability scanning
- [ ] Authentication bypass testing
- [ ] Authorization testing
- [ ] Input validation testing
- [ ] Session management testing
- [ ] Encryption testing
- [ ] Rate limiting testing

### Automated Security Testing
```bash
# Add security testing to CI/CD pipeline
go test -tags=security ./...
gosec ./...
nancy sleuth
```

## Compliance Considerations

### GDPR Compliance
- Data minimization
- Right to be forgotten
- Data portability
- Consent management
- Privacy by design

### SOC 2 Compliance
- Access controls
- System monitoring
- Data protection
- Incident response
- Vendor management

Việc implement những security best practices này sẽ đảm bảo hệ thống có khả năng chống chịu cao trước các mối đe dọa bảo mật hiện đại.