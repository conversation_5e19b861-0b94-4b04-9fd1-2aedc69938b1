# Testing Strategy & Implementation Guide

## 1. Testing Architecture Overview

### 1.1 Testing Pyramid
```
                    /\
                   /  \
                  /    \
                 / E2E  \
                /  Tests \
               /          \
              /____________\
             /              \
            /  Integration   \
           /     Tests       \
          /                  \
         /____________________\
        /                      \
       /      Unit Tests        \
      /________________________\
```

**Distribution**:
- Unit Tests: 70%
- Integration Tests: 20%
- E2E Tests: 10%

### 1.2 Test Categories
```go
// Test build tags for different test types
// +build unit
// +build integration
// +build e2e
// +build performance
```

## 2. Unit Testing Strategy

### 2.1 Service Layer Testing
```go
// Example: User Service Unit Tests
func TestUserService_CreateUser(t *testing.T) {
    tests := []struct {
        name          string
        input         *CreateUserRequest
        mockSetup     func(*mocks.MockUserRepository)
        expectedError string
        expectedUser  *User
    }{
        {
            name: "successful user creation",
            input: &CreateUserRequest{
                Email:    "<EMAIL>",
                Username: "testuser",
                Password: "SecurePass123!",
            },
            mockSetup: func(mockRepo *mocks.MockUserRepository) {
                mockRepo.EXPECT().
                    GetByEmail(gomock.Any(), "<EMAIL>").
                    Return(nil, gorm.ErrRecordNotFound)
                
                mockRepo.EXPECT().
                    GetByUsername(gomock.Any(), "testuser").
                    Return(nil, gorm.ErrRecordNotFound)
                
                mockRepo.EXPECT().
                    Create(gomock.Any(), gomock.Any()).
                    Return(nil)
            },
            expectedUser: &User{
                Email:    "<EMAIL>",
                Username: "testuser",
            },
        },
        {
            name: "email already exists",
            input: &CreateUserRequest{
                Email:    "<EMAIL>",
                Username: "testuser",
                Password: "SecurePass123!",
            },
            mockSetup: func(mockRepo *mocks.MockUserRepository) {
                mockRepo.EXPECT().
                    GetByEmail(gomock.Any(), "<EMAIL>").
                    Return(&User{Email: "<EMAIL>"}, nil)
            },
            expectedError: "email already exists",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ctrl := gomock.NewController(t)
            defer ctrl.Finish()
            
            mockRepo := mocks.NewMockUserRepository(ctrl)
            tt.mockSetup(mockRepo)
            
            service := NewUserService(mockRepo, nil, nil)
            
            user, err := service.CreateUser(context.Background(), tt.input)
            
            if tt.expectedError != "" {
                assert.Error(t, err)
                assert.Contains(t, err.Error(), tt.expectedError)
                assert.Nil(t, user)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, user)
                assert.Equal(t, tt.expectedUser.Email, user.Email)
                assert.Equal(t, tt.expectedUser.Username, user.Username)
            }
        })
    }
}
```

### 2.2 Repository Layer Testing
```go
func TestUserRepository_Create(t *testing.T) {
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    repo := NewUserRepository(db)
    
    user := &User{
        Email:     "<EMAIL>",
        Username:  "testuser",
        FirstName: "Test",
        LastName:  "User",
    }
    
    err := repo.Create(context.Background(), user)
    
    assert.NoError(t, err)
    assert.NotZero(t, user.ID)
    assert.NotZero(t, user.CreatedAt)
    
    // Verify user was actually created
    var dbUser User
    err = db.First(&dbUser, user.ID).Error
    assert.NoError(t, err)
    assert.Equal(t, user.Email, dbUser.Email)
}

// Test with database transactions
func TestUserRepository_CreateWithTransaction(t *testing.T) {
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    repo := NewUserRepository(db)
    
    t.Run("successful transaction", func(t *testing.T) {
        tx := db.Begin()
        defer tx.Rollback()
        
        user := &User{Email: "<EMAIL>", Username: "testuser"}
        err := repo.CreateWithTx(context.Background(), tx, user)
        
        assert.NoError(t, err)
        tx.Commit()
        
        // Verify user exists after commit
        var count int64
        db.Model(&User{}).Where("email = ?", user.Email).Count(&count)
        assert.Equal(t, int64(1), count)
    })
    
    t.Run("failed transaction rollback", func(t *testing.T) {
        tx := db.Begin()
        defer tx.Rollback()
        
        user := &User{Email: "<EMAIL>", Username: "testuser"}
        err := repo.CreateWithTx(context.Background(), tx, user)
        assert.NoError(t, err)
        
        // Don't commit - should rollback
        
        // Verify user doesn't exist after rollback
        var count int64
        db.Model(&User{}).Where("email = ?", user.Email).Count(&count)
        assert.Equal(t, int64(0), count)
    })
}
```

### 2.3 Handler Layer Testing
```go
func TestAuthHandler_Register(t *testing.T) {
    tests := []struct {
        name           string
        requestBody    interface{}
        mockSetup      func(*mocks.MockAuthService)
        expectedStatus int
        expectedBody   map[string]interface{}
    }{
        {
            name: "successful registration",
            requestBody: map[string]interface{}{
                "email":     "<EMAIL>",
                "username":  "testuser",
                "password":  "SecurePass123!",
                "firstName": "Test",
                "lastName":  "User",
            },
            mockSetup: func(mockService *mocks.MockAuthService) {
                mockService.EXPECT().
                    Register(gomock.Any(), gomock.Any()).
                    Return(&dto.RegisterResponse{
                        Success: true,
                        Message: "Registration successful",
                        User: &userModels.User{
                            ID:       1,
                            Email:    "<EMAIL>",
                            Username: "testuser",
                        },
                        AccessToken:  "access_token",
                        RefreshToken: "refresh_token",
                    }, nil)
            },
            expectedStatus: 201,
            expectedBody: map[string]interface{}{
                "success": true,
                "message": "Registration successful",
            },
        },
        {
            name: "invalid email format",
            requestBody: map[string]interface{}{
                "email":    "invalid-email",
                "username": "testuser",
                "password": "SecurePass123!",
            },
            mockSetup:      func(mockService *mocks.MockAuthService) {},
            expectedStatus: 400,
            expectedBody: map[string]interface{}{
                "success": false,
                "message": "Validation failed",
            },
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ctrl := gomock.NewController(t)
            defer ctrl.Finish()
            
            mockService := mocks.NewMockAuthService(ctrl)
            tt.mockSetup(mockService)
            
            handler := NewAuthHandler(mockService)
            
            // Setup Gin test context
            gin.SetMode(gin.TestMode)
            router := gin.New()
            router.POST("/register", handler.Register)
            
            // Create request
            body, _ := json.Marshal(tt.requestBody)
            req := httptest.NewRequest("POST", "/register", bytes.NewBuffer(body))
            req.Header.Set("Content-Type", "application/json")
            
            // Record response
            w := httptest.NewRecorder()
            router.ServeHTTP(w, req)
            
            // Assertions
            assert.Equal(t, tt.expectedStatus, w.Code)
            
            var response map[string]interface{}
            err := json.Unmarshal(w.Body.Bytes(), &response)
            assert.NoError(t, err)
            
            for key, expectedValue := range tt.expectedBody {
                assert.Equal(t, expectedValue, response[key])
            }
        })
    }
}
```

## 3. Integration Testing

### 3.1 Database Integration Tests
```go
// +build integration

func TestUserIntegration(t *testing.T) {
    db := setupIntegrationDB(t)
    defer cleanupIntegrationDB(t, db)
    
    // Setup dependencies
    userRepo := repositories.NewUserRepository(db)
    authService := services.NewAuthService(userRepo, nil, nil)
    
    t.Run("complete user registration flow", func(t *testing.T) {
        // Test user registration
        registerReq := &dto.RegisterRequest{
            Email:     "<EMAIL>",
            Username:  "integrationuser",
            Password:  "SecurePass123!",
            FirstName: "Integration",
            LastName:  "Test",
        }
        
        response, err := authService.Register(context.Background(), registerReq)
        assert.NoError(t, err)
        assert.True(t, response.Success)
        assert.NotNil(t, response.User)
        
        // Verify user exists in database
        var user models.User
        err = db.Where("email = ?", registerReq.Email).First(&user).Error
        assert.NoError(t, err)
        assert.Equal(t, registerReq.Email, user.Email)
        
        // Test user login
        loginReq := &dto.LoginRequest{
            Email:    registerReq.Email,
            Password: registerReq.Password,
        }
        
        loginResponse, err := authService.Login(context.Background(), loginReq)
        assert.NoError(t, err)
        assert.NotEmpty(t, loginResponse.AccessToken)
    })
}
```

### 3.2 API Integration Tests
```go
func TestAPIIntegration(t *testing.T) {
    // Setup test server
    server := setupTestServer(t)
    defer server.Close()
    
    client := &http.Client{Timeout: 10 * time.Second}
    
    t.Run("user registration and authentication flow", func(t *testing.T) {
        // Register user
        registerData := map[string]interface{}{
            "email":     "<EMAIL>",
            "username":  "apiuser",
            "password":  "SecurePass123!",
            "firstName": "API",
            "lastName":  "Test",
        }
        
        registerResp := makeRequest(t, client, "POST", server.URL+"/api/auth/register", registerData)
        assert.Equal(t, 201, registerResp.StatusCode)
        
        var registerResult map[string]interface{}
        json.NewDecoder(registerResp.Body).Decode(&registerResult)
        assert.True(t, registerResult["success"].(bool))
        
        // Login user
        loginData := map[string]interface{}{
            "email":    "<EMAIL>",
            "password": "SecurePass123!",
        }
        
        loginResp := makeRequest(t, client, "POST", server.URL+"/api/auth/login", loginData)
        assert.Equal(t, 200, loginResp.StatusCode)
        
        var loginResult map[string]interface{}
        json.NewDecoder(loginResp.Body).Decode(&loginResult)
        
        accessToken := loginResult["access_token"].(string)
        assert.NotEmpty(t, accessToken)
        
        // Test authenticated endpoint
        req, _ := http.NewRequest("GET", server.URL+"/api/users/profile", nil)
        req.Header.Set("Authorization", "Bearer "+accessToken)
        
        profileResp, err := client.Do(req)
        assert.NoError(t, err)
        assert.Equal(t, 200, profileResp.StatusCode)
    })
}
```

### 3.3 External Service Integration Tests
```go
func TestEmailServiceIntegration(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping email service integration test in short mode")
    }
    
    // Use test email service or mock SMTP server
    emailService := setupTestEmailService(t)
    
    t.Run("send welcome email", func(t *testing.T) {
        email := &models.Email{
            To:      "<EMAIL>",
            Subject: "Welcome!",
            Body:    "Welcome to our platform!",
            Type:    "welcome",
        }
        
        err := emailService.Send(context.Background(), email)
        assert.NoError(t, err)
        
        // Verify email was sent (check test mailbox or mock)
        sentEmails := emailService.GetSentEmails()
        assert.Len(t, sentEmails, 1)
        assert.Equal(t, email.To, sentEmails[0].To)
    })
}
```

## 4. End-to-End Testing

### 4.1 API E2E Tests
```go
// +build e2e

func TestE2EUserJourney(t *testing.T) {
    // Setup complete test environment
    testEnv := setupE2EEnvironment(t)
    defer testEnv.Cleanup()
    
    client := testEnv.HTTPClient
    baseURL := testEnv.BaseURL
    
    t.Run("complete user journey", func(t *testing.T) {
        // 1. User Registration
        registerData := map[string]interface{}{
            "email":     "<EMAIL>",
            "username":  "e2euser",
            "password":  "SecurePass123!",
            "firstName": "E2E",
            "lastName":  "Test",
        }
        
        registerResp := makeAPIRequest(t, client, "POST", baseURL+"/api/auth/register", registerData)
        assert.Equal(t, 201, registerResp.StatusCode)
        
        // 2. Email Verification (if required)
        if requiresEmailVerification(registerResp) {
            verificationToken := getVerificationTokenFromEmail(t, "<EMAIL>")
            verifyResp := makeAPIRequest(t, client, "POST", baseURL+"/api/auth/verify-email", map[string]interface{}{
                "token": verificationToken,
            })
            assert.Equal(t, 200, verifyResp.StatusCode)
        }
        
        // 3. User Login
        loginData := map[string]interface{}{
            "email":    "<EMAIL>",
            "password": "SecurePass123!",
        }
        
        loginResp := makeAPIRequest(t, client, "POST", baseURL+"/api/auth/login", loginData)
        assert.Equal(t, 200, loginResp.StatusCode)
        
        var loginResult map[string]interface{}
        json.NewDecoder(loginResp.Body).Decode(&loginResult)
        accessToken := loginResult["access_token"].(string)
        
        // 4. Create Blog Post
        postData := map[string]interface{}{
            "title":   "My First E2E Post",
            "content": "This is a test post created during E2E testing",
            "status":  "published",
        }
        
        postResp := makeAuthenticatedRequest(t, client, "POST", baseURL+"/api/posts", postData, accessToken)
        assert.Equal(t, 201, postResp.StatusCode)
        
        var postResult map[string]interface{}
        json.NewDecoder(postResp.Body).Decode(&postResult)
        postID := postResult["id"].(float64)
        
        // 5. Get User's Posts
        postsResp := makeAuthenticatedRequest(t, client, "GET", baseURL+"/api/posts?author=me", nil, accessToken)
        assert.Equal(t, 200, postsResp.StatusCode)
        
        var postsResult map[string]interface{}
        json.NewDecoder(postsResp.Body).Decode(&postsResult)
        posts := postsResult["data"].([]interface{})
        assert.Len(t, posts, 1)
        
        // 6. Update Post
        updateData := map[string]interface{}{
            "title": "My Updated E2E Post",
        }
        
        updateResp := makeAuthenticatedRequest(t, client, "PUT", 
            fmt.Sprintf("%s/api/posts/%.0f", baseURL, postID), updateData, accessToken)
        assert.Equal(t, 200, updateResp.StatusCode)
        
        // 7. Delete Post
        deleteResp := makeAuthenticatedRequest(t, client, "DELETE", 
            fmt.Sprintf("%s/api/posts/%.0f", baseURL, postID), nil, accessToken)
        assert.Equal(t, 204, deleteResp.StatusCode)
        
        // 8. Logout
        logoutResp := makeAuthenticatedRequest(t, client, "POST", baseURL+"/api/auth/logout", nil, accessToken)
        assert.Equal(t, 200, logoutResp.StatusCode)
    })
}
```

### 4.2 Browser E2E Tests (with Selenium)
```go
func TestBrowserE2E(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping browser E2E test in short mode")
    }
    
    // Setup Selenium WebDriver
    driver := setupWebDriver(t)
    defer driver.Quit()
    
    baseURL := "http://localhost:3000" // Frontend URL
    
    t.Run("user registration through UI", func(t *testing.T) {
        // Navigate to registration page
        driver.Get(baseURL + "/register")
        
        // Fill registration form
        emailInput := findElement(t, driver, selenium.ByName, "email")
        emailInput.SendKeys("<EMAIL>")
        
        usernameInput := findElement(t, driver, selenium.ByName, "username")
        usernameInput.SendKeys("uitest")
        
        passwordInput := findElement(t, driver, selenium.ByName, "password")
        passwordInput.SendKeys("SecurePass123!")
        
        // Submit form
        submitButton := findElement(t, driver, selenium.ByXPATH, "//button[@type='submit']")
        submitButton.Click()
        
        // Wait for success message
        waitForElement(t, driver, selenium.ByClassName, "success-message", 10*time.Second)
        
        // Verify redirect to dashboard
        currentURL, _ := driver.CurrentURL()
        assert.Contains(t, currentURL, "/dashboard")
    })
}
```

## 5. Performance Testing

### 5.1 Load Testing
```go
func TestLoadPerformance(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping load test in short mode")
    }
    
    server := setupTestServer(t)
    defer server.Close()
    
    t.Run("concurrent user registration", func(t *testing.T) {
        concurrency := 100
        requests := 1000
        
        var wg sync.WaitGroup
        results := make(chan TestResult, requests)
        
        // Create worker pool
        for i := 0; i < concurrency; i++ {
            wg.Add(1)
            go func(workerID int) {
                defer wg.Done()
                
                client := &http.Client{Timeout: 30 * time.Second}
                
                for j := 0; j < requests/concurrency; j++ {
                    start := time.Now()
                    
                    userData := map[string]interface{}{
                        "email":    fmt.Sprintf("<EMAIL>", workerID, j),
                        "username": fmt.Sprintf("load%d_%d", workerID, j),
                        "password": "SecurePass123!",
                    }
                    
                    resp := makeRequest(t, client, "POST", server.URL+"/api/auth/register", userData)
                    duration := time.Since(start)
                    
                    results <- TestResult{
                        StatusCode: resp.StatusCode,
                        Duration:   duration,
                        Success:    resp.StatusCode == 201,
                    }
                }
            }(i)
        }
        
        wg.Wait()
        close(results)
        
        // Analyze results
        var totalDuration time.Duration
        var successCount, errorCount int
        var maxDuration, minDuration time.Duration = 0, time.Hour
        
        for result := range results {
            totalDuration += result.Duration
            
            if result.Success {
                successCount++
            } else {
                errorCount++
            }
            
            if result.Duration > maxDuration {
                maxDuration = result.Duration
            }
            if result.Duration < minDuration {
                minDuration = result.Duration
            }
        }
        
        avgDuration := totalDuration / time.Duration(requests)
        successRate := float64(successCount) / float64(requests) * 100
        
        t.Logf("Load Test Results:")
        t.Logf("  Total Requests: %d", requests)
        t.Logf("  Success Rate: %.2f%%", successRate)
        t.Logf("  Average Duration: %v", avgDuration)
        t.Logf("  Min Duration: %v", minDuration)
        t.Logf("  Max Duration: %v", maxDuration)
        
        // Assertions
        assert.Greater(t, successRate, 95.0, "Success rate should be > 95%")
        assert.Less(t, avgDuration, 1*time.Second, "Average response time should be < 1s")
    })
}
```

### 5.2 Benchmark Tests
```go
func BenchmarkUserService_GetUser(b *testing.B) {
    service := setupUserService(b)
    ctx := context.Background()
    
    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            _, err := service.GetUser(ctx, 1)
            if err != nil {
                b.Fatal(err)
            }
        }
    })
}

func BenchmarkUserService_CreateUser(b *testing.B) {
    service := setupUserService(b)
    ctx := context.Background()
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        req := &dto.CreateUserRequest{
            Email:    fmt.Sprintf("<EMAIL>", i),
            Username: fmt.Sprintf("bench%d", i),
            Password: "SecurePass123!",
        }
        
        _, err := service.CreateUser(ctx, req)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

## 6. Test Utilities and Helpers

### 6.1 Test Database Setup
```go
func setupTestDB(t *testing.T) *gorm.DB {
    dsn := "host=localhost user=test password=test dbname=test_db port=5432 sslmode=disable"
    db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Silent),
    })
    require.NoError(t, err)
    
    // Auto migrate tables
    err = db.AutoMigrate(&models.User{}, &models.Post{}, &models.Session{})
    require.NoError(t, err)
    
    return db
}

func cleanupTestDB(t *testing.T, db *gorm.DB) {
    // Clean up test data
    db.Exec("TRUNCATE TABLE users, posts, sessions CASCADE")
}
```

### 6.2 Test Data Factories
```go
type UserFactory struct {
    db *gorm.DB
}

func NewUserFactory(db *gorm.DB) *UserFactory {
    return &UserFactory{db: db}
}

func (f *UserFactory) Create(overrides ...func(*models.User)) *models.User {
    user := &models.User{
        Email:     "<EMAIL>",
        Username:  "testuser",
        FirstName: "Test",
        LastName:  "User",
        Status:    "active",
    }
    
    for _, override := range overrides {
        override(user)
    }
    
    f.db.Create(user)
    return user
}

func (f *UserFactory) CreateWithEmail(email string) *models.User {
    return f.Create(func(u *models.User) {
        u.Email = email
    })
}

// Usage in tests
func TestSomething(t *testing.T) {
    db := setupTestDB(t)
    factory := NewUserFactory(db)
    
    user := factory.CreateWithEmail("<EMAIL>")
    // Use user in test...
}
```

### 6.3 Mock Generation
```go
//go:generate mockgen -source=interfaces.go -destination=mocks/mock_interfaces.go

// Generate mocks for all interfaces
//go:generate mockgen -source=internal/modules/user/repositories/interfaces.go -destination=internal/modules/user/repositories/mocks/mock_repository.go
//go:generate mockgen -source=internal/modules/user/services/interfaces.go -destination=internal/modules/user/services/mocks/mock_service.go
```

## 7. CI/CD Integration

### 7.1 GitHub Actions Workflow
```yaml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.21
    
    - name: Run unit tests
      run: |
        go test -tags=unit -v -race -coverprofile=coverage.out ./...
        go tool cover -html=coverage.out -o coverage.html
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.21
    
    - name: Run integration tests
      run: go test -tags=integration -v ./...
      env:
        DATABASE_URL: postgres://postgres:test@localhost:5432/test_db?sslmode=disable
        REDIS_URL: redis://localhost:6379

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.21
    
    - name: Start application
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30
    
    - name: Run E2E tests
      run: go test -tags=e2e -v ./tests/e2e/...
    
    - name: Cleanup
      run: docker-compose -f docker-compose.test.yml down
```

### 7.2 Test Coverage Requirements
```go
// coverage.sh
#!/bin/bash

# Run tests with coverage
go test -tags=unit -coverprofile=coverage.out ./...

# Check coverage threshold
COVERAGE=$(go tool cover -func=coverage.out | grep total | awk '{print $3}' | sed 's/%//')
THRESHOLD=80

if (( $(echo "$COVERAGE < $THRESHOLD" | bc -l) )); then
    echo "Coverage $COVERAGE% is below threshold $THRESHOLD%"
    exit 1
fi

echo "Coverage $COVERAGE% meets threshold $THRESHOLD%"
```

## 8. Testing Best Practices

### 8.1 Test Organization
```
tests/
├── unit/
│   ├── services/
│   ├── repositories/
│   └── handlers/
├── integration/
│   ├── api/
│   └── database/
├── e2e/
│   ├── user_journey/
│   └── admin_workflow/
├── performance/
│   ├── load/
│   └── stress/
└── fixtures/
    ├── data/
    └── mocks/
```

### 8.2 Test Naming Conventions
```go
// Pattern: Test[UnitOfWork]_[Scenario]_[ExpectedBehavior]
func TestUserService_CreateUser_WithValidData_ReturnsUser(t *testing.T) {}
func TestUserService_CreateUser_WithDuplicateEmail_ReturnsError(t *testing.T) {}
func TestUserRepository_GetByID_WithExistingUser_ReturnsUser(t *testing.T) {}
func TestUserRepository_GetByID_WithNonExistentUser_ReturnsNotFoundError(t *testing.T) {}
```

### 8.3 Test Data Management
```go
// Use table-driven tests for multiple scenarios
func TestPasswordValidation(t *testing.T) {
    tests := []struct {
        name     string
        password string
        want     bool
        wantErr  string
    }{
        {"valid password", "SecurePass123!", true, ""},
        {"too short", "short", false, "password too short"},
        {"no uppercase", "lowercase123!", false, "password must contain uppercase"},
        {"no numbers", "NoNumbers!", false, "password must contain numbers"},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := ValidatePassword(tt.password)
            
            if tt.wantErr != "" {
                assert.Error(t, err)
                assert.Contains(t, err.Error(), tt.wantErr)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.want, got)
            }
        })
    }
}
```

Việc implement comprehensive testing strategy này sẽ đảm bảo chất lượng code cao và giảm thiểu bugs trong production.