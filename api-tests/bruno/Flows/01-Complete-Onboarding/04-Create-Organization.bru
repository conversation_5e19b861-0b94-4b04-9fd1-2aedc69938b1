meta {
  name: 04 - Create Organization
  type: http
  seq: 4
}

post {
  url: {{api_url}}/onboarding/my/organization
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "organization_name": "{{test_org_name}}",
    "slug": "{{test_org_slug}}",
    "admin_email": "{{test_email}}",
    "admin_name": "Test User"
  }
}

script:pre-request {
  if (!bru.getEnvVar("access_token")) {
    throw new Error("❌ Not authenticated. Run step 04-Login first!");
  }
  
  if (!bru.getEnvVar("test_org_name")) {
    throw new Error("❌ No organization data. Run registration flow first!");
  }
  
  console.log("🏢 Creating organization:", bru.getEnvVar("test_org_name"));
  console.log("🔗 Slug:", bru.getEnvVar("test_org_slug"));
}

script:post-response {
  if (res.status === 201) {
    console.log("✅ Organization created successfully");
    
    // Save organization data
    if (res.body.data.tenant_id) {
      bru.setEnvVar("tenant_id", res.body.data.tenant_id.toString());
      console.log("🏢 Tenant ID:", res.body.data.tenant_id);
    }
    
    if (res.body.data.organization_id) {
      bru.setEnvVar("organization_id", res.body.data.organization_id.toString());
      console.log("🆔 Organization ID:", res.body.data.organization_id);
    }
    
    if (res.body.data.membership_id) {
      bru.setEnvVar("membership_id", res.body.data.membership_id.toString());
      console.log("👥 Membership ID:", res.body.data.membership_id);
    }
    
  } else {
    console.log("❌ Organization creation failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Organization created successfully", function() {
    expect(res.status).to.equal(201);
  });
  
  test("Tenant ID provided", function() {
    expect(res.body.data).to.have.property("tenant_id");
    expect(res.body.data.tenant_id).to.be.a("number");
    expect(res.body.data.tenant_id).to.be.greaterThan(0);
  });
  
  test("Organization ID provided", function() {
    expect(res.body.data).to.have.property("organization_id");
    expect(res.body.data.organization_id).to.be.a("number");
  });
  
  test("Membership created", function() {
    expect(res.body.data).to.have.property("membership_id");
    expect(res.body.data.membership_id).to.be.a("number");
  });
  
  test("User is primary member", function() {
    expect(res.body.data).to.have.property("is_primary");
    expect(res.body.data.is_primary).to.equal(true);
  });
}