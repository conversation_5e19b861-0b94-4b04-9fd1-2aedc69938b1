meta {
  name: Check Organization Status
  type: http
  seq: 4.1
}

get {
  url: {{api_url}}/onboarding/my/organization-status
  body: none
  auth: inherit
}

headers {
  Authorization: {{auth_header}}
  X-Tenant-ID: {{tenant_id}}
}

tests {
  test("Request returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains organization status", function() {
    const body = res.getBody();
    expect(body).to.have.property('data');
    expect(body.data).to.have.property('has_organization');
    expect(body.data).to.have.property('requires_setup');
  });
  
  test("Status values are boolean", function() {
    const body = res.getBody();
    const data = body.data;
    
    expect(data.has_organization).to.be.a('boolean');
    expect(data.requires_setup).to.be.a('boolean');
    
    // They should be opposite values
    expect(data.has_organization).to.equal(!data.requires_setup);
  });
  
  test("If has organization, should include details", function() {
    const body = res.getBody();
    const data = body.data;
    
    if (data.has_organization) {
      expect(data).to.have.property('organization');
      expect(data).to.have.property('membership');
      expect(data.organization).to.have.property('id');
      expect(data.organization).to.have.property('name');
      expect(data.membership).to.have.property('id');
      expect(data.membership).to.have.property('is_primary');
    }
    
    console.log('Organization status:', JSON.stringify(data, null, 2));
  });
}

docs {
  Check the current user's organization setup status.
  This API should return:
  1. has_organization: boolean - whether user has completed org setup
  2. requires_setup: boolean - whether organization setup is needed
  3. organization: object (if exists) - organization details
  4. membership: object (if exists) - user's membership details
  
  This test should be run before attempting to create organization.
}