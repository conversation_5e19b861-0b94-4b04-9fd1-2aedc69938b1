meta {
  name: 03 - Login User
  type: http
  seq: 3
}

post {
  url: {{api_url}}/auth/login
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "email": "{{test_email}}",
    "password": "Test@123456"
  }
}

script:pre-request {
  if (!bru.getEnvVar("test_email")) {
    throw new Error("❌ No test email found. Run registration flow first!");
  }
  
  console.log("🔐 Logging in with email:", bru.getEnvVar("test_email"));
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Login successful");
    
    // Save authentication tokens
    bru.setEnvVar("access_token", res.body.data.access_token);
    bru.setEnvVar("refresh_token", res.body.data.refresh_token); 
    bru.setEnvVar("bearer_token", "Bearer " + res.body.data.access_token);
    
    if (res.body.data.session_id) {
      bru.setEnvVar("session_id", res.body.data.session_id.toString());
    }
    
    console.log("🔑 Authentication tokens saved");
    console.log("🆔 Session ID:", res.body.data.session_id);
    console.log("🎯 Requires onboarding:", res.body.data.requires_onboarding);
    
  } else {
    console.log("❌ Login failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Login successful", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Access token provided", function() {
    expect(res.body.data).to.have.property("access_token");
    expect(res.body.data.access_token).to.be.a("string");
    expect(res.body.data.access_token.length).to.be.greaterThan(0);
  });
  
  test("Refresh token provided", function() {
    expect(res.body.data).to.have.property("refresh_token");
    expect(res.body.data.refresh_token).to.be.a("string");
  });
  
  test("Session ID provided", function() {
    expect(res.body.data).to.have.property("session_id");
    expect(res.body.data.session_id).to.be.a("number");
  });
  
  test("Onboarding status provided", function() {
    expect(res.body.data).to.have.property("requires_onboarding");
    expect(res.body.data.requires_onboarding).to.be.a("boolean");
  });
}