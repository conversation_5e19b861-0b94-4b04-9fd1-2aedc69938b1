meta {
  name: 07 - Create Website
  type: http
  seq: 7
}

post {
  url: {{api_url}}/onboarding/my/website
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "name": "{{test_website_name}}",
    "subdomain": "{{test_subdomain}}",
    "description": "Test website created via Bruno API testing",
    "template_id": {{selected_template_id}},
    "theme": "default",
    "timezone": "UTC", 
    "language": "en"
  }
}

script:pre-request {
  if (!bru.getEnvVar("access_token")) {
    throw new Error("❌ Not authenticated. Run login flow first!");
  }
  
  if (!bru.getEnvVar("tenant_id")) {
    throw new Error("❌ No tenant context. Run organization creation first!");
  }
  
  if (!bru.getEnvVar("test_website_name")) {
    throw new Error("❌ No website data. Run registration flow first!");
  }
  
  console.log("🌐 Creating website:", bru.getEnvVar("test_website_name"));
  console.log("🔗 Subdomain:", bru.getEnvVar("test_subdomain"));
  console.log("🎨 Template ID:", bru.getEnvVar("selected_template_id") || "1 (default)");
}

script:post-response {
  if (res.status === 201) {
    console.log("✅ Website created successfully!");
    
    // Save website data
    if (res.body.data && res.body.data.id) {
      bru.setEnvVar("website_id", res.body.data.id.toString());
      console.log("🆔 Website ID:", res.body.data.id);
    }
    
    if (res.body.data && res.body.data.subdomain) {
      bru.setEnvVar("website_subdomain", res.body.data.subdomain);
      console.log("🔗 Subdomain:", res.body.data.subdomain);
    }
    
    if (res.body.data && res.body.data.domain) {
      bru.setEnvVar("website_domain", res.body.data.domain);
      console.log("🌐 Domain:", res.body.data.domain);
    }
    
    console.log("🎉 Complete onboarding flow finished!");
    console.log("📊 Summary:");
    console.log("   👤 User:", bru.getEnvVar("test_email"));
    console.log("   🏢 Organization:", bru.getEnvVar("test_org_name"));
    console.log("   🌐 Website:", bru.getEnvVar("test_website_name"));
    console.log("   🔗 URL: http://" + bru.getEnvVar("test_subdomain") + ".localhost");
    
  } else {
    console.log("❌ Website creation failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Website created successfully", function() {
    expect(res.status).to.equal(201);
  });
  
  test("Website ID provided", function() {
    expect(res.body.data).to.have.property("id");
    expect(res.body.data.id).to.be.a("number");
    expect(res.body.data.id).to.be.greaterThan(0);
  });
  
  test("Website name matches", function() {
    expect(res.body.data).to.have.property("name");
    expect(res.body.data.name).to.equal(bru.getEnvVar("test_website_name"));
  });
  
  test("Subdomain assigned", function() {
    expect(res.body.data).to.have.property("subdomain");
    expect(res.body.data.subdomain).to.be.a("string");
    expect(res.body.data.subdomain.length).to.be.greaterThan(0);
  });
  
  test("Website is active", function() {
    expect(res.body.data).to.have.property("status");
    expect(res.body.data.status).to.equal("active");
  });
}