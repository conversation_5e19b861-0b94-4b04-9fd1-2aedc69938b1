meta {
  name: 05 - Check Website Status
  type: http
  seq: 5
}

get {
  url: {{api_url}}/onboarding/my/website-status
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  X-Tenant-ID: {{tenant_id}}
}

script:pre-request {
  if (!bru.getEnvVar("access_token")) {
    throw new Error("❌ Not authenticated. Run login flow first!");
  }
  
  if (!bru.getEnvVar("tenant_id")) {
    throw new Error("❌ No tenant context. Run organization creation first!");
  }
  
  console.log("🔍 Checking website status for tenant:", bru.getEnvVar("tenant_id"));
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Website status check successful");
    
    if (res.body.data && res.body.data.has_website) {
      console.log("🌐 User already has a website");
      if (res.body.data.website) {
        bru.setEnvVar("website_id", res.body.data.website.id.toString());
        console.log("🆔 Existing website ID:", res.body.data.website.id);
      }
    } else {
      console.log("📝 No website found - ready for website creation");
    }
    
  } else {
    console.log("❌ Website status check failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Status check successful", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Has website status provided", function() {
    expect(res.body).to.have.property("has_website");
    expect(res.body.has_website).to.be.a("boolean");
  });
  
  test("Requires setup flag provided", function() {
    expect(res.body).to.have.property("requires_setup");
    expect(res.body.requires_setup).to.be.a("boolean");
  });
}