meta {
  name: 01 - Register User
  type: http
  seq: 1
}

post {
  url: {{api_url}}/auth/register
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "first_name": "Test",
    "last_name": "User", 
    "email": "<EMAIL>",
    "password": "Test@123456"
  }
}

script:pre-request {
  // Use static test data from environment for consistent flow testing
  console.log("📧 Using email:", bru.getEnvVar("test_email"));
  console.log("🏢 Org name:", bru.getEnvVar("test_org_name"));
  console.log("🌐 Subdomain:", bru.getEnvVar("test_subdomain"));
}

script:post-response {
  if (res.status === 201) {
    console.log("✅ Registration successful");
    
    // Save test data for next requests
    bru.setEnvVar("test_email", bru.getVar("test_email"));
    bru.setEnvVar("test_org_name", bru.getVar("test_org_name"));
    bru.setEnvVar("test_org_slug", bru.getVar("test_org_slug"));
    bru.setEnvVar("test_website_name", bru.getVar("test_website_name"));
    bru.setEnvVar("test_subdomain", bru.getVar("test_subdomain"));
    
    console.log("📧 Test Email:", bru.getVar("test_email"));
    console.log("🏢 Org Name:", bru.getVar("test_org_name"));
    console.log("🌐 Subdomain:", bru.getVar("test_subdomain"));
    
  } else {
    console.log("❌ Registration failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Registration successful", function() {
    expect(res.status).to.equal(201);
  });
  
  test("Requires email verification", function() {
    expect(res.body.data.requires_email_verification).to.equal(true);
  });
  
  test("Email verification sent", function() {
    expect(res.body.data.email_verification_sent).to.equal(true);
  });
}