meta {
  name: 08 - Verify Website Created
  type: http
  seq: 8
}

get {
  url: {{api_url}}/onboarding/my/website
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  X-Tenant-ID: {{tenant_id}}
}

script:pre-request {
  if (!bru.getEnvVar("website_id")) {
    throw new Error("❌ No website ID found. Run website creation first!");
  }
  
  console.log("🔍 Verifying created website ID:", bru.getEnvVar("website_id"));
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Website verification successful");
    
    if (res.body.data) {
      console.log("📋 Website Details:");
      console.log("   🆔 ID:", res.body.data.id);
      console.log("   📝 Name:", res.body.data.name);
      console.log("   🔗 Subdomain:", res.body.data.subdomain);
      console.log("   🎨 Theme:", res.body.data.active_theme);
      console.log("   📅 Created:", res.body.data.created_at);
      console.log("   ⚡ Status:", res.body.data.status);
      
      if (res.body.data.seo_settings) {
        console.log("   🔍 SEO configured:", Object.keys(res.body.data.seo_settings).length > 0);
      }
    }
    
  } else {
    console.log("❌ Website verification failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Website retrieved successfully", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Website data complete", function() {
    expect(res.body.data).to.have.property("id");
    expect(res.body.data).to.have.property("name");
    expect(res.body.data).to.have.property("subdomain");
    expect(res.body.data).to.have.property("status");
  });
  
  test("Website matches created data", function() {
    expect(res.body.data.id.toString()).to.equal(bru.getEnvVar("website_id"));
    expect(res.body.data.name).to.equal(bru.getEnvVar("test_website_name"));
  });
}