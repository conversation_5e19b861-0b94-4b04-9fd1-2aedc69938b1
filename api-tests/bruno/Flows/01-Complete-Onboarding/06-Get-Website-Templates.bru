meta {
  name: 06 - Get Website Templates
  type: http
  seq: 6
}

get {
  url: {{api_url}}/onboarding/my/website-templates
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  X-Tenant-ID: {{tenant_id}}
}

script:pre-request {
  if (!bru.getEnvVar("access_token")) {
    throw new Error("❌ Not authenticated. Run login flow first!");
  }
  
  console.log("🎨 Getting available website templates");
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Website templates retrieved");
    
    if (res.body.templates && Array.isArray(res.body.templates)) {
      console.log("📋 Found", res.body.templates.length, "templates");
      
      // Select first template for testing
      if (res.body.templates.length > 0) {
        bru.setEnvVar("selected_template_id", res.body.templates[0].id.toString());
        console.log("🎯 Selected template:", res.body.templates[0].name, "(ID:", res.body.templates[0].id + ")");
      }
      
      // Display available templates
      res.body.templates.forEach((template, index) => {
        console.log(`   ${index + 1}. ${template.name} (${template.category}) - ${template.is_free ? 'FREE' : 'PAID'}`);
      });
    }
    
  } else {
    console.log("❌ Failed to get templates:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Templates retrieved successfully", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Templates array provided", function() {
    expect(res.body.templates).to.be.an("array");
  });
  
  test("Templates have required fields", function() {
    if (res.body.templates && res.body.templates.length > 0) {
      const template = res.body.templates[0];
      expect(template).to.have.property("id");
      expect(template).to.have.property("name");
      expect(template).to.have.property("category");
      expect(template).to.have.property("is_free");
    }
  });
}