name: Complete Onboarding Flow
type: collection

docs: |
  # Complete Onboarding Flow Test Collection
  
  This collection tests the complete user onboarding journey from registration to website creation.
  
  ## Test Flow:
  1. **Register User** - Create new user account
  2. **Get Verification Token** - Retrieve email verification token from system  
  3. **Verify Email** - Confirm email address
  4. **Login** - Authenticate user and get tokens
  5. **Create Organization** - Setup tenant/organization
  6. **Check Website Status** - Verify no existing website
  7. **Get Website Templates** - Retrieve available templates
  8. **Create Website** - Create website with selected template
  9. **Verify Website** - Confirm website creation successful
  
  ## Auto-Generated Data:
  - Email: test{timestamp}{random}@example.com
  - Organization: Test Organization {random}
  - Website: Test Website {random}
  - Subdomain: testsite{timestamp}
  
  ## Environment Variables Set:
  - `test_email`, `test_org_name`, `test_website_name`
  - `access_token`, `refresh_token`, `user_id`
  - `tenant_id`, `organization_id`, `membership_id`
  - `website_id`, `website_subdomain`
  
  ## Usage:
  ```bash
  bru run "api-tests/bruno/Flows/01-Complete-Onboarding" --env Local
  ```