meta {
  name: 02 - Verify Email Address
  type: http
  seq: 2
}

post {
  url: {{api_url}}/auth/verify-email
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "token": "{{verification_token}}"
  }
}

script:pre-request {
  if (!bru.getEnvVar("verification_token")) {
    throw new Error("❌ No verification token found. Run step 02-Get-Verification-Token first!");
  }
  
  console.log("🔐 Using token:", bru.getEnvVar("verification_token").substring(0, 10) + "...");
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Email verification successful");
    
    // Check if login credentials are provided
    if (res.body.data && res.body.data.access_token) {
      bru.setEnvVar("access_token", res.body.data.access_token);
      bru.setEnvVar("refresh_token", res.body.data.refresh_token);
      bru.setEnvVar("user_id", res.body.data.user.id.toString());
      console.log("🔑 Auto-login tokens saved");
    }
    
  } else {
    console.log("❌ Email verification failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Email verified successfully", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Verification status returned", function() {
    expect(res.body.data).to.have.property("status");
    expect(res.body.data.status).to.equal("verified");
  });
  
  test("Email verified flag set", function() {
    expect(res.body.data).to.have.property("email_verified");
    expect(res.body.data.email_verified).to.equal(true);
  });
  
  test("User ID provided", function() {
    expect(res.body.data).to.have.property("user_id");
    expect(res.body.data.user_id).to.be.a("number");
  });
}