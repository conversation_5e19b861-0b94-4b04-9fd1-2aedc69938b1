meta {
  name: Check My Status
  type: http
  seq: 3.1
}

get {
  url: {{api_url}}/onboarding/my/status
  body: none
  auth: inherit
}

headers {
  Authorization: {{auth_header}}
  X-Tenant-ID: {{tenant_id}}
}

tests {
  test("Request returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains status data", function() {
    const body = res.getBody();
    expect(body).to.have.property('data');
    expect(body.data).to.have.property('status');
  });
  
  test("Status contains expected properties", function() {
    const body = res.getBody();
    const status = body.data.status;
    
    // Should contain overall completion info
    expect(status).to.be.an('object');
    
    // Log status for debugging
    console.log('My onboarding status:', JSON.stringify(status, null, 2));
  });
}

docs {
  Check the current user's overall onboarding status.
  This API should return:
  1. Overall completion percentage
  2. Current step/stage
  3. What needs to be completed next
  4. Any blocking issues
  
  This test verifies the main onboarding status endpoint works correctly.
}