meta {
  name: 06 - List Published Posts
  type: http
  seq: 6
}

get {
  url: {{api_url}}/blog/posts?status=published&limit=10&sort=published_at&order=desc
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

script:pre-request {
  if (!bru.getEnvVar("access_token")) {
    throw new Error("❌ Not authenticated. Run onboarding flow first!");
  }
  
  console.log("📋 Listing published posts for tenant:", bru.getEnvVar("tenant_id"));
  console.log("🔍 Filters: status=published, limit=10, sort by published_at desc");
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Published posts retrieved successfully");
    
    if (res.body.data && res.body.data.posts) {
      const posts = res.body.data.posts;
      console.log("📊 Total published posts:", res.body.data.pagination?.total || posts.length);
      console.log("📄 Posts in response:", posts.length);
      
      if (posts.length > 0) {
        console.log("📝 Latest post:", posts[0].title);
        console.log("📅 Published at:", posts[0].published_at);
        
        // Find our test post
        const testPost = posts.find(p => p.slug === bru.getEnvVar("blog_post_slug"));
        if (testPost) {
          console.log("✅ Found our test post:", testPost.title);
          console.log("👀 Views:", testPost.view_count || 0);
        }
      }
    }
    
  } else {
    console.log("❌ Failed to retrieve published posts:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Posts retrieved successfully", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Response has posts array", function() {
    expect(res.body.data).to.have.property("posts");
    expect(res.body.data.posts).to.be.an("array");
  });
  
  test("All posts are published", function() {
    if (res.body.data.posts.length > 0) {
      res.body.data.posts.forEach(post => {
        expect(post.status).to.equal("published");
      });
    }
  });
  
  test("Posts have required fields", function() {
    if (res.body.data.posts.length > 0) {
      const post = res.body.data.posts[0];
      expect(post).to.have.property("id");
      expect(post).to.have.property("title");
      expect(post).to.have.property("slug");
      expect(post).to.have.property("published_at");
    }
  });
  
  test("Posts sorted by published date", function() {
    if (res.body.data.posts.length > 1) {
      const posts = res.body.data.posts;
      for (let i = 0; i < posts.length - 1; i++) {
        const current = new Date(posts[i].published_at);
        const next = new Date(posts[i + 1].published_at);
        expect(current >= next).to.be.true;
      }
    }
  });
  
  test("Pagination data present", function() {
    expect(res.body.data).to.have.property("pagination");
    expect(res.body.data.pagination).to.have.property("total");
    expect(res.body.data.pagination).to.have.property("limit");
    expect(res.body.data.pagination).to.have.property("offset");
  });
}