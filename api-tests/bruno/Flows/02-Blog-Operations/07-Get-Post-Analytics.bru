meta {
  name: 07 - Get Post Analytics
  type: http
  seq: 7
}

get {
  url: {{api_url}}/blog/posts/{{blog_post_id}}/analytics
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

script:pre-request {
  if (!bru.getEnvVar("blog_post_id")) {
    throw new Error("❌ No blog post. Run step 03-Create-Draft-Post first!");
  }
  
  console.log("📊 Getting analytics for post ID:", bru.getEnvVar("blog_post_id"));
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Post analytics retrieved successfully");
    
    if (res.body.data) {
      const analytics = res.body.data;
      console.log("👀 Total views:", analytics.view_count || 0);
      console.log("💬 Comments count:", analytics.comment_count || 0);
      console.log("❤️  Likes:", analytics.like_count || 0);
      console.log("📤 Shares:", analytics.share_count || 0);
      console.log("⏱️  Average read time:", analytics.avg_read_time || "N/A");
      
      if (analytics.traffic_sources) {
        console.log("📈 Top traffic sources:");
        Object.entries(analytics.traffic_sources).slice(0, 3).forEach(([source, count]) => {
          console.log(`  - ${source}: ${count} visits`);
        });
      }
      
      if (analytics.popular_sections && analytics.popular_sections.length > 0) {
        console.log("🔥 Most engaged sections:");
        analytics.popular_sections.slice(0, 3).forEach((section, idx) => {
          console.log(`  ${idx + 1}. ${section.heading}: ${section.engagement_score}% engagement`);
        });
      }
    }
    
  } else if (res.status === 404) {
    console.log("⚠️  Analytics not available yet (post may be too new)");
  } else {
    console.log("❌ Failed to retrieve analytics:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Analytics retrieved or not found", function() {
    expect([200, 404]).to.include(res.status);
  });
  
  test("Analytics data structure (if available)", function() {
    if (res.status === 200) {
      expect(res.body.data).to.have.property("view_count");
      expect(res.body.data.view_count).to.be.a("number");
    }
  });
  
  test("Post metadata included", function() {
    if (res.status === 200) {
      expect(res.body.data).to.have.property("post_id");
      expect(res.body.data.post_id.toString()).to.equal(bru.getEnvVar("blog_post_id"));
    }
  });
  
  test("Traffic sources data", function() {
    if (res.status === 200 && res.body.data.traffic_sources) {
      expect(res.body.data.traffic_sources).to.be.an("object");
    }
  });
  
  test("Engagement metrics", function() {
    if (res.status === 200) {
      expect(res.body.data).to.have.property("comment_count");
      expect(res.body.data.comment_count).to.be.a("number");
      
      if (res.body.data.avg_read_time) {
        expect(res.body.data.avg_read_time).to.be.a("number");
      }
    }
  });
}