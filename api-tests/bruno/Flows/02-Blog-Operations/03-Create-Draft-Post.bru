meta {
  name: 03 - Create Draft Blog Post
  type: http
  seq: 3
}

post {
  url: {{api_url}}/blog/posts
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "title": "The Future of AI in Web Development",
    "slug": "future-ai-web-development",
    "content": "# Introduction\n\nArtificial Intelligence is revolutionizing how we approach web development. From automated code generation to intelligent user interfaces, AI tools are becoming essential for modern developers.\n\n## Key Areas of Impact\n\n### 1. Code Generation\nAI-powered tools like GitHub Copilot and ChatGPT are helping developers write code faster and more efficiently.\n\n### 2. Design Automation\nAI can now generate layouts, color schemes, and even complete designs based on simple descriptions.\n\n### 3. Performance Optimization\nMachine learning algorithms can analyze user behavior and automatically optimize website performance.\n\n## Conclusion\n\nThe integration of AI in web development is not just a trend—it's the future. Developers who embrace these tools will have a significant advantage in creating better, faster, and more user-friendly applications.",
    "excerpt": "Exploring how artificial intelligence is transforming the landscape of web development, from automated code generation to intelligent user interfaces.",
    "status": "draft",
    "category_ids": [{{blog_category_id}}],
    "tag_ids": [{{blog_tag_id}}],
    "featured_image": "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800",
    "meta_title": "AI in Web Development: Future Trends and Technologies",
    "meta_description": "Discover how AI is revolutionizing web development with automated tools, design optimization, and performance enhancements.",
    "reading_time": 5,
    "allow_comments": true
  }
}

script:pre-request {
  if (!bru.getEnvVar("access_token")) {
    throw new Error("❌ Not authenticated. Run onboarding flow first!");
  }
  
  if (!bru.getEnvVar("blog_category_id")) {
    throw new Error("❌ No blog category. Run step 01-Create-Category first!");
  }
  
  if (!bru.getEnvVar("blog_tag_id")) {
    throw new Error("❌ No blog tag. Run step 02-Create-Tags first!");
  }
  
  console.log("✍️  Creating draft blog post: The Future of AI in Web Development");
  console.log("📂 Using category ID:", bru.getEnvVar("blog_category_id"));
  console.log("🏷️  Using tag ID:", bru.getEnvVar("blog_tag_id"));
}

script:post-response {
  if (res.status === 201) {
    console.log("✅ Draft post created successfully");
    
    if (res.body.data && res.body.data.id) {
      bru.setEnvVar("blog_post_id", res.body.data.id.toString());
      bru.setEnvVar("blog_post_slug", res.body.data.slug);
      bru.setEnvVar("blog_post_title", res.body.data.title);
      
      console.log("🆔 Post ID:", res.body.data.id);
      console.log("🔗 Post Slug:", res.body.data.slug);
      console.log("📝 Status:", res.body.data.status);
    }
    
  } else {
    console.log("❌ Post creation failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Post created successfully", function() {
    expect(res.status).to.equal(201);
  });
  
  test("Post ID provided", function() {
    expect(res.body.data).to.have.property("id");
    expect(res.body.data.id).to.be.a("number");
    expect(res.body.data.id).to.be.greaterThan(0);
  });
  
  test("Post title matches", function() {
    expect(res.body.data).to.have.property("title");
    expect(res.body.data.title).to.equal("The Future of AI in Web Development");
  });
  
  test("Post slug generated", function() {
    expect(res.body.data).to.have.property("slug");
    expect(res.body.data.slug).to.equal("future-ai-web-development");
  });
  
  test("Post status is draft", function() {
    expect(res.body.data).to.have.property("status");
    expect(res.body.data.status).to.equal("draft");
  });
  
  test("Post has categories", function() {
    expect(res.body.data).to.have.property("categories");
    expect(res.body.data.categories).to.be.an("array");
    expect(res.body.data.categories.length).to.be.greaterThan(0);
  });
  
  test("Post has tags", function() {
    expect(res.body.data).to.have.property("tags");
    expect(res.body.data.tags).to.be.an("array");
    expect(res.body.data.tags.length).to.be.greaterThan(0);
  });
}