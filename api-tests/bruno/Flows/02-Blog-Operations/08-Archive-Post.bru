meta {
  name: 08 - Archive Blog Post
  type: http
  seq: 8
}

put {
  url: {{api_url}}/blog/posts/{{blog_post_id}}/archive
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "reason": "Content outdated - newer version available",
    "redirect_url": "/blog/future-ai-web-development-2026",
    "notify_subscribers": false
  }
}

script:pre-request {
  if (!bru.getEnvVar("blog_post_id")) {
    throw new Error("❌ No blog post. Run step 03-Create-Draft-Post first!");
  }
  
  console.log("📦 Archiving post ID:", bru.getEnvVar("blog_post_id"));
  console.log("📝 Title:", bru.getEnvVar("blog_post_title"));
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Post archived successfully");
    
    if (res.body.data) {
      console.log("📊 Final status:", res.body.data.status);
      console.log("📅 Archived at:", res.body.data.archived_at);
      console.log("🔗 Redirect URL:", res.body.data.redirect_url);
      console.log("📧 Subscribers notified:", res.body.data.subscribers_notified || false);
    }
    
    // Clean up environment variables since post is archived
    console.log("🧹 Cleaning up test data environment variables");
    
  } else {
    console.log("❌ Post archiving failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Post archived successfully", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Post status changed to archived", function() {
    expect(res.body.data).to.have.property("status");
    expect(res.body.data.status).to.equal("archived");
  });
  
  test("Archived timestamp set", function() {
    expect(res.body.data).to.have.property("archived_at");
    expect(res.body.data.archived_at).to.not.be.null;
  });
  
  test("Archive reason recorded", function() {
    expect(res.body.data).to.have.property("archive_reason");
    expect(res.body.data.archive_reason).to.include("outdated");
  });
  
  test("Redirect URL set", function() {
    expect(res.body.data).to.have.property("redirect_url");
    expect(res.body.data.redirect_url).to.equal("/blog/future-ai-web-development-2026");
  });
  
  test("Post still accessible by ID", function() {
    expect(res.body.data).to.have.property("id");
    expect(res.body.data.id.toString()).to.equal(bru.getEnvVar("blog_post_id"));
  });
}