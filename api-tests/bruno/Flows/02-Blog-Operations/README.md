# Blog Operations Flow - Bruno Test Collection

## Overview
Complete end-to-end blog management workflow testing the full lifecycle of blog content creation, publication, and management.

## Test Flow Sequence

### 1. Content Setup (Steps 1-2)
- **01-Create-Category.bru** - Creates "Technology" category with SEO metadata
- **02-Create-Tags.bru** - Creates "AI & Machine Learning" tag with styling

### 2. Content Creation (Steps 3-4)
- **03-Create-Draft-Post.bru** - Creates comprehensive draft blog post about AI in web development
- **04-Update-Post-Content.bru** - Updates content with expanded sections and 2025 edition

### 3. Publication & Analytics (Steps 5-7)
- **05-Publish-Post.bru** - Publishes the draft post with social sharing enabled
- **06-List-Published-Posts.bru** - Retrieves published posts with pagination and filtering
- **07-Get-Post-Analytics.bru** - Fetches post analytics and engagement metrics

### 4. Content Lifecycle (Step 8)
- **08-Archive-Post.bru** - Archives the post with redirect URL and notification settings

## Features Tested

### ✅ Content Management
- Category and tag creation with metadata
- Draft post creation with rich content
- Content updates and revisions
- Publication workflow with scheduling

### ✅ SEO & Metadata
- Category SEO fields (meta_title, meta_description)
- Post excerpts and reading time
- Featured images and social sharing
- URL slug generation and validation

### ✅ Multi-tenant Architecture
- All requests include `X-Tenant-ID` header
- Data isolation verification
- Tenant-scoped authentication

### ✅ Analytics & Insights
- Post view counts and engagement metrics
- Traffic source analysis
- Content section engagement tracking
- Comment and social interaction data

### ✅ Lifecycle Management
- Draft → Published → Archived workflow
- Content archiving with redirect URLs
- Subscriber notification handling
- Soft delete implementation

## Environment Variables Used

### Required (from Onboarding Flow)
- `access_token` - JWT authentication token
- `tenant_id` - Current tenant context
- `api_url` - API base URL

### Generated During Flow
- `blog_category_id` - Created category ID
- `blog_category_name` - Category name
- `blog_category_slug` - Category URL slug
- `blog_tag_id` - Created tag ID
- `blog_tag_name` - Tag name
- `blog_tag_slug` - Tag URL slug
- `blog_post_id` - Created post ID
- `blog_post_slug` - Post URL slug
- `blog_post_title` - Post title
- `published_post_id` - Published post reference

## Test Validations

### HTTP Response Validation
- Status codes (201 for creation, 200 for updates)
- Response structure and data types
- Error handling and validation messages

### Business Logic Validation
- Content relationships (posts → categories → tags)
- Status transitions (draft → published → archived)
- Timestamp consistency and updates
- SEO metadata completeness

### Data Integrity Validation
- Unique slug generation
- Required field validation
- Multi-tenant data isolation
- Relationship integrity constraints

## Running the Tests

### Full Blog Operations Flow
```bash
bru run api-tests/bruno/Flows/02-Blog-Operations --env local
```

### Individual Steps
```bash
bru run api-tests/bruno/Flows/02-Blog-Operations/01-Create-Category.bru --env local
bru run api-tests/bruno/Flows/02-Blog-Operations/03-Create-Draft-Post.bru --env local
```

## Prerequisites

1. **Authentication**: Complete onboarding flow first to get valid `access_token`
2. **Server Running**: Blog API server must be running on configured port
3. **Database**: MySQL database with blog module migrations applied

## Expected Results

- **Categories**: 1 technology category created
- **Tags**: 1 AI/ML tag created  
- **Posts**: 1 comprehensive blog post through full lifecycle
- **Analytics**: Post engagement and traffic data
- **Status Flow**: Draft → Published → Archived

## Cleanup

The archive step (08) serves as cleanup by archiving the test post. The post remains in database but is no longer publicly visible, maintaining referential integrity while cleaning up active content.

## Integration Points

This flow integrates with:
- **Authentication module** for JWT tokens
- **Tenant module** for multi-tenant isolation
- **Analytics system** for engagement tracking
- **SEO module** for metadata management
- **Notification system** for subscriber updates