meta {
  name: 05 - Publish Draft Post
  type: http
  seq: 5
}

put {
  url: {{api_url}}/blog/posts/{{blog_post_id}}/publish
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "publish_date": "2025-01-22T10:00:00Z",
    "featured": true,
    "allow_comments": true,
    "social_share": {
      "facebook": true,
      "twitter": true,
      "linkedin": true
    }
  }
}

script:pre-request {
  if (!bru.getEnvVar("blog_post_id")) {
    throw new Error("❌ No blog post. Run step 03-Create-Draft-Post first!");
  }
  
  console.log("🚀 Publishing post ID:", bru.getEnvVar("blog_post_id"));
  console.log("📅 Scheduled for: 2025-01-22T10:00:00Z");
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Post published successfully");
    
    if (res.body.data) {
      console.log("📝 Title:", res.body.data.title);
      console.log("📊 Status:", res.body.data.status);
      console.log("📅 Published at:", res.body.data.published_at);
      console.log("🌟 Featured:", res.body.data.featured);
      
      // Store published post data for analytics
      bru.setEnvVar("published_post_id", res.body.data.id.toString());
      bru.setEnvVar("published_post_slug", res.body.data.slug);
    }
    
  } else {
    console.log("❌ Post publication failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Post published successfully", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Post status changed to published", function() {
    expect(res.body.data).to.have.property("status");
    expect(res.body.data.status).to.equal("published");
  });
  
  test("Published date set", function() {
    expect(res.body.data).to.have.property("published_at");
    expect(res.body.data.published_at).to.not.be.null;
  });
  
  test("Post marked as featured", function() {
    expect(res.body.data).to.have.property("featured");
    expect(res.body.data.featured).to.equal(true);
  });
  
  test("Comments allowed", function() {
    expect(res.body.data).to.have.property("allow_comments");
    expect(res.body.data.allow_comments).to.equal(true);
  });
  
  test("Social share settings applied", function() {
    expect(res.body.data).to.have.property("social_share");
    expect(res.body.data.social_share).to.be.an("object");
  });
}