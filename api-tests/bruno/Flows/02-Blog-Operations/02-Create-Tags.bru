meta {
  name: 02 - Create Blog Tags
  type: http
  seq: 2
}

post {
  url: {{api_url}}/blog/tags
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "name": "AI & Machine Learning",
    "slug": "ai-machine-learning",
    "description": "Content related to artificial intelligence and machine learning",
    "color": "#3B82F6"
  }
}

script:pre-request {
  if (!bru.getEnvVar("access_token")) {
    throw new Error("❌ Not authenticated. Run onboarding flow first!");
  }
  
  console.log("🏷️ Creating blog tag: AI & Machine Learning");
}

script:post-response {
  if (res.status === 201) {
    console.log("✅ Tag created successfully");
    
    if (res.body.data && res.body.data.id) {
      bru.setEnvVar("blog_tag_id", res.body.data.id.toString());
      bru.setEnvVar("blog_tag_name", res.body.data.name);
      bru.setEnvVar("blog_tag_slug", res.body.data.slug);
      
      console.log("🆔 Tag ID:", res.body.data.id);
      console.log("🏷️  Tag Slug:", res.body.data.slug);
    }
    
  } else {
    console.log("❌ Tag creation failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Tag created successfully", function() {
    expect(res.status).to.equal(201);
  });
  
  test("Tag ID provided", function() {
    expect(res.body.data).to.have.property("id");
    expect(res.body.data.id).to.be.a("number");
  });
  
  test("Tag name matches", function() {
    expect(res.body.data).to.have.property("name");
    expect(res.body.data.name).to.equal("AI & Machine Learning");
  });
  
  test("Tag slug generated", function() {
    expect(res.body.data).to.have.property("slug");
    expect(res.body.data.slug).to.equal("ai-machine-learning");
  });
  
  test("Tag color set", function() {
    expect(res.body.data).to.have.property("color");
    expect(res.body.data.color).to.equal("#3B82F6");
  });
}