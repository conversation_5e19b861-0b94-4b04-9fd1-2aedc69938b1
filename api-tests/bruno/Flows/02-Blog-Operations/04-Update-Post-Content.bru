meta {
  name: 04 - Update Post Content
  type: http
  seq: 4
}

put {
  url: {{api_url}}/blog/posts/{{blog_post_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "title": "The Future of AI in Web Development - 2025 Edition",
    "content": "# Introduction\n\nArtificial Intelligence is revolutionizing how we approach web development. From automated code generation to intelligent user interfaces, AI tools are becoming essential for modern developers in 2025.\n\n## Key Areas of Impact\n\n### 1. Code Generation\nAI-powered tools like GitHub Copilot, ChatGPT, and Claude are helping developers write code faster and more efficiently than ever before.\n\n### 2. Design Automation\nAI can now generate layouts, color schemes, and even complete designs based on simple descriptions. Tools like Midjourney and DALL-E are changing the creative process.\n\n### 3. Performance Optimization\nMachine learning algorithms can analyze user behavior and automatically optimize website performance, reducing load times and improving user experience.\n\n### 4. Testing and QA\nAI-driven testing tools can automatically generate test cases, detect bugs, and even suggest fixes.\n\n## Real-World Applications\n\n- **Automated deployments** with intelligent rollback capabilities\n- **Smart content management** with AI-powered SEO optimization\n- **Personalized user experiences** based on behavioral analysis\n- **Voice and conversational interfaces** powered by natural language processing\n\n## Conclusion\n\nThe integration of AI in web development is not just a trend—it's the future. Developers who embrace these tools will have a significant advantage in creating better, faster, and more user-friendly applications. The question is not whether AI will transform web development, but how quickly we can adapt to leverage its full potential.",
    "excerpt": "Exploring how artificial intelligence is transforming the landscape of web development in 2025, from automated code generation to intelligent user interfaces and beyond.",
    "reading_time": 7
  }
}

script:pre-request {
  if (!bru.getEnvVar("blog_post_id")) {
    throw new Error("❌ No blog post. Run step 03-Create-Draft-Post first!");
  }
  
  console.log("✏️  Updating post content for ID:", bru.getEnvVar("blog_post_id"));
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Post updated successfully");
    
    if (res.body.data) {
      console.log("📝 Updated title:", res.body.data.title);
      console.log("⏱️  Reading time:", res.body.data.reading_time, "minutes");
      console.log("📊 Word count:", res.body.data.word_count || "N/A");
    }
    
  } else {
    console.log("❌ Post update failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Post updated successfully", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Title updated", function() {
    expect(res.body.data).to.have.property("title");
    expect(res.body.data.title).to.include("2025 Edition");
  });
  
  test("Content updated", function() {
    expect(res.body.data).to.have.property("content");
    expect(res.body.data.content).to.include("Real-World Applications");
  });
  
  test("Reading time updated", function() {
    expect(res.body.data).to.have.property("reading_time");
    expect(res.body.data.reading_time).to.equal(7);
  });
  
  test("Post still in draft", function() {
    expect(res.body.data).to.have.property("status");
    expect(res.body.data.status).to.equal("draft");
  });
}