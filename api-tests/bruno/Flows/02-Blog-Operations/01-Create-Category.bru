meta {
  name: 01 - Create Blog Category
  type: http
  seq: 1
}

post {
  url: {{api_url}}/blog/categories
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "name": "Technology",
    "slug": "technology",
    "description": "Posts about technology trends and news",
    "meta_title": "Technology Blog Category",
    "meta_description": "Latest technology articles and insights"
  }
}

script:pre-request {
  if (!bru.getEnvVar("access_token")) {
    throw new Error("❌ Not authenticated. Run onboarding flow first!");
  }
  
  if (!bru.getEnvVar("tenant_id")) {
    throw new Error("❌ No tenant context. Run onboarding flow first!");
  }
  
  console.log("📂 Creating blog category: Technology");
}

script:post-response {
  if (res.status === 201) {
    console.log("✅ Category created successfully");
    
    if (res.body.data && res.body.data.id) {
      bru.setEnvVar("blog_category_id", res.body.data.id.toString());
      bru.setEnvVar("blog_category_name", res.body.data.name);
      bru.setEnvVar("blog_category_slug", res.body.data.slug);
      
      console.log("🆔 Category ID:", res.body.data.id);
      console.log("🏷️  Category Slug:", res.body.data.slug);
    }
    
  } else {
    console.log("❌ Category creation failed:", res.status);
    console.log("Response:", res.body);
  }
}

tests {
  test("Category created successfully", function() {
    expect(res.status).to.equal(201);
  });
  
  test("Category ID provided", function() {
    expect(res.body.data).to.have.property("id");
    expect(res.body.data.id).to.be.a("number");
    expect(res.body.data.id).to.be.greaterThan(0);
  });
  
  test("Category name matches", function() {
    expect(res.body.data).to.have.property("name");
    expect(res.body.data.name).to.equal("Technology");
  });
  
  test("Category slug generated", function() {
    expect(res.body.data).to.have.property("slug");
    expect(res.body.data.slug).to.equal("technology");
  });
  
  test("Category description set", function() {
    expect(res.body.data).to.have.property("description");
    expect(res.body.data.description).to.be.a("string");
  });
}