meta {
  name: 03 - Test Blog API Access
  type: http
  seq: 3
}

get {
  url: {{api_url}}/blog/categories
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

script:pre-request {
  const token = bru.getEnvVar("access_token");
  const tenantId = bru.getEnvVar("tenant_id");
  
  if (!token) {
    throw new Error("❌ No access token. Run step 01-Simple-Login first!");
  }
  
  if (!tenantId) {
    throw new Error("❌ No tenant ID. Run step 02-Create-Tenant-Membership first!");
  }
  
  console.log("🧪 Testing blog API access");
  console.log("🏢 Tenant ID:", tenantId);
  console.log("🔑 Using token:", token.substring(0, 20) + "...");
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Blog API access successful");
    
    if (res.body.data && res.body.data.categories) {
      const categories = res.body.data.categories;
      console.log("📂 Found", categories.length, "categories");
      
      if (categories.length > 0) {
        console.log("📂 Example category:", categories[0].name);
      }
    }
    
    console.log("🎯 Ready for blog operations testing!");
    
  } else if (res.status === 401) {
    console.log("❌ Authentication failed - tenant membership issue");
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  } else {
    console.log("❌ Blog API test failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Blog API accessible or identified auth issue", function() {
    expect([200, 401]).to.include(res.status);
  });
  
  test("Response has proper structure", function() {
    if (res.status === 200) {
      expect(res.body).to.have.property("data");
    } else if (res.status === 401) {
      expect(res.body).to.have.property("status");
      expect(res.body.status).to.have.property("message");
    }
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(3000);
  });
}