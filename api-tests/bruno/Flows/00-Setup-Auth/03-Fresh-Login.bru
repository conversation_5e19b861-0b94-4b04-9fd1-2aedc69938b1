meta {
  name: 03 - Fresh Login
  type: http
  seq: 3
}

post {
  url: {{api_url}}/auth/login
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "email": "{{fresh_test_email}}",
    "password": "{{fresh_test_password}}"
  }
}

script:pre-request {
  const email = bru.getEnvVar("fresh_test_email");
  if (!email) {
    throw new Error("❌ No test email. Run step 01-Fresh-Register first!");
  }
  
  console.log("🔐 Fresh login with email:", email);
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Fresh login successful");
    
    if (res.body.data) {
      const data = res.body.data;
      
      // Save fresh auth tokens
      if (data.access_token) {
        bru.setEnvVar("access_token", data.access_token);
        bru.setEnvVar("fresh_access_token", data.access_token);
        console.log("🔑 Fresh access token saved");
      }
      
      if (data.refresh_token) {
        bru.setEnvVar("refresh_token", data.refresh_token);
        bru.setEnvVar("fresh_refresh_token", data.refresh_token);
      }
      
      if (data.session_id) {
        bru.setEnvVar("fresh_session_id", data.session_id.toString());
        console.log("🆔 Session ID:", data.session_id);
      }
      
      // Save user info
      if (data.user) {
        bru.setEnvVar("user_id", data.user.id.toString());
        bru.setEnvVar("fresh_user_id", data.user.id.toString());
        console.log("👤 User ID:", data.user.id);
      }
      
      // Check onboarding requirement
      if (data.requires_onboarding !== undefined) {
        bru.setEnvVar("fresh_requires_onboarding", data.requires_onboarding.toString());
        console.log("🎯 Requires onboarding:", data.requires_onboarding);
      }
    }
    
  } else {
    console.log("❌ Fresh login failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Login successful", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Access token provided", function() {
    expect(res.body.data).to.have.property("access_token");
    expect(res.body.data.access_token).to.be.a("string");
    expect(res.body.data.access_token.length).to.be.greaterThan(0);
  });
  
  test("Refresh token provided", function() {
    expect(res.body.data).to.have.property("refresh_token");
    expect(res.body.data.refresh_token).to.be.a("string");
  });
  
  test("Session ID provided", function() {
    expect(res.body.data).to.have.property("session_id");
    expect(res.body.data.session_id).to.be.a("number");
  });
  
  test("User data provided", function() {
    expect(res.body.data).to.have.property("user");
    expect(res.body.data.user).to.have.property("id");
  });
  
  test("Onboarding status provided", function() {
    expect(res.body.data).to.have.property("requires_onboarding");
    expect(res.body.data.requires_onboarding).to.be.a("boolean");
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(3000);
  });
}