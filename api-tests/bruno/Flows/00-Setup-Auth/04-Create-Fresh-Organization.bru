meta {
  name: 04 - Create Fresh Organization
  type: http
  seq: 4
}

post {
  url: {{api_url}}/onboarding/create-organization
  body: json
  auth: bearer
}

auth:bearer {
  token: {{fresh_access_token}}
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "name": "Fresh API Test Org {{$timestamp}}",
    "slug": "fresh-api-org-{{$timestamp}}",
    "description": "Organization created for Bruno API testing",
    "industry": "Technology",
    "company_size": "1-10",
    "website_name": "Fresh Test Website",
    "subdomain": "freshtest{{$timestamp}}",
    "template_id": 1
  }
}

script:pre-request {
  const token = bru.getEnvVar("fresh_access_token");
  if (!token) {
    throw new Error("❌ No fresh access token. Run step 03-Fresh-Login first!");
  }
  
  const timestamp = Date.now().toString().slice(-8);
  const orgName = `Fresh API Test Org ${timestamp}`;
  const orgSlug = `fresh-api-org-${timestamp}`;
  const subdomain = `freshtest${timestamp}`;
  
  bru.setEnvVar("fresh_org_name", orgName);
  bru.setEnvVar("fresh_org_slug", orgSlug);
  bru.setEnvVar("fresh_subdomain", subdomain);
  
  console.log("🏢 Creating organization:", orgName);
  console.log("🔗 Slug:", orgSlug);
  console.log("🌐 Subdomain:", subdomain);
}

script:post-response {
  if (res.status === 201) {
    console.log("✅ Organization created successfully");
    
    if (res.body.data) {
      const data = res.body.data;
      
      // Save tenant info
      if (data.tenant && data.tenant.id) {
        bru.setEnvVar("tenant_id", data.tenant.id.toString());
        bru.setEnvVar("fresh_tenant_id", data.tenant.id.toString());
        console.log("🏢 Tenant ID:", data.tenant.id);
      }
      
      // Save membership info
      if (data.membership && data.membership.id) {
        bru.setEnvVar("membership_id", data.membership.id.toString());
        bru.setEnvVar("fresh_membership_id", data.membership.id.toString());
        console.log("👤 Membership ID:", data.membership.id);
        console.log("👑 Is primary:", data.membership.is_primary);
      }
      
      // Save website info if created
      if (data.website && data.website.id) {
        bru.setEnvVar("website_id", data.website.id.toString());
        bru.setEnvVar("fresh_website_id", data.website.id.toString());
        console.log("🌐 Website ID:", data.website.id);
        console.log("🏷️  Website subdomain:", data.website.subdomain);
      }
    }
    
  } else {
    console.log("❌ Organization creation failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Organization created successfully", function() {
    expect(res.status).to.equal(201);
  });
  
  test("Tenant data provided", function() {
    expect(res.body.data).to.have.property("tenant");
    expect(res.body.data.tenant).to.have.property("id");
    expect(res.body.data.tenant.id).to.be.a("number");
  });
  
  test("Organization name matches", function() {
    expect(res.body.data.tenant.name).to.equal(bru.getEnvVar("fresh_org_name"));
  });
  
  test("Membership created", function() {
    expect(res.body.data).to.have.property("membership");
    expect(res.body.data.membership).to.have.property("id");
    expect(res.body.data.membership.id).to.be.a("number");
  });
  
  test("User is primary member", function() {
    expect(res.body.data.membership.is_primary).to.equal(true);
  });
  
  test("Website created", function() {
    expect(res.body.data).to.have.property("website");
    expect(res.body.data.website).to.have.property("id");
    expect(res.body.data.website.id).to.be.a("number");
  });
  
  test("Website subdomain matches", function() {
    expect(res.body.data.website.subdomain).to.equal(bru.getEnvVar("fresh_subdomain"));
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(5000);
  });
}