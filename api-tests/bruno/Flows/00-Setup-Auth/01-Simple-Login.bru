meta {
  name: 01 - Simple Working Login
  type: http
  seq: 1
}

post {
  url: {{api_url}}/auth/login
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "TestPassword123"
  }
}

script:pre-request {
  console.log("🔐 Logging in with existing working credentials");
  console.log("📧 Email: <EMAIL>");
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Login successful");
    
    if (res.body.data) {
      const data = res.body.data;
      
      // Update all auth tokens globally
      if (data.access_token) {
        bru.setEnvVar("access_token", data.access_token);
        console.log("🔑 Global access token updated");
      }
      
      if (data.refresh_token) {
        bru.setEnvVar("refresh_token", data.refresh_token);
      }
      
      if (data.session_id) {
        bru.setEnvVar("session_id", data.session_id.toString());
        console.log("🆔 Session ID:", data.session_id);
      }
      
      // Save user info globally
      if (data.user) {
        bru.setEnvVar("user_id", data.user.id.toString());
        console.log("👤 User ID:", data.user.id);
      }
      
      console.log("🎯 Ready for API testing with fresh tokens");
    }
    
  } else {
    console.log("❌ Login failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Login successful", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Access token provided", function() {
    expect(res.body.data).to.have.property("access_token");
    expect(res.body.data.access_token).to.be.a("string");
    expect(res.body.data.access_token.length).to.be.greaterThan(0);
  });
  
  test("User data provided", function() {
    expect(res.body.data).to.have.property("user");
    expect(res.body.data.user).to.have.property("id");
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(3000);
  });
}