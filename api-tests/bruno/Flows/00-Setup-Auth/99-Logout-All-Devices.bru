meta {
  name: 99 - Logout All Devices & Cleanup
  type: http
  seq: 99
}

post {
  url: {{api_url}}/auth/logout-all
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
}

script:pre-request {
  const token = bru.getEnvVar("access_token");
  if (!token) {
    throw new Error("❌ No access token available to logout from!");
  }
  
  console.log("🚪 Logging out from all devices and cleaning up session...");
  console.log("🔑 Using token:", token.substring(0, 20) + "...");
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Successfully logged out from all devices");
    
    if (res.body.data && res.body.data.sessions_terminated !== undefined) {
      console.log("🔒 Sessions terminated:", res.body.data.sessions_terminated);
    }
    
    // Clear all authentication data
    bru.setEnvVar("access_token", "");
    bru.setEnvVar("refresh_token", "");
    bru.setEnvVar("bearer_token", "");
    bru.setEnvVar("session_id", "");
    
    // Clear user context
    bru.setEnvVar("user_id", "");
    bru.setEnvVar("fresh_user_id", "");
    
    // Clear tenant context
    bru.setEnvVar("tenant_id", "");
    bru.setEnvVar("fresh_tenant_id", "");
    bru.setEnvVar("membership_id", "");
    
    // Clear test data
    bru.setEnvVar("fresh_test_email", "");
    bru.setEnvVar("fresh_org_name", "");
    bru.setEnvVar("fresh_subdomain", "");
    
    console.log("🧹 All authentication tokens and user data cleared");
    console.log("🔄 Ready for fresh authentication flow");
    
  } else if (res.status === 401) {
    console.log("⚠️  Already logged out or token expired");
    // Clear tokens anyway for cleanup
    bru.setEnvVar("access_token", "");
    bru.setEnvVar("refresh_token", "");
    bru.setEnvVar("bearer_token", "");
    bru.setEnvVar("session_id", "");
  } else {
    console.log("❌ Logout failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Logout completed", function() {
    expect([200, 401]).to.include(res.status);
  });
  
  test("Access token cleared", function() {
    expect(bru.getEnvVar("access_token")).to.equal("");
  });
  
  test("Session data cleared", function() {
    expect(bru.getEnvVar("session_id")).to.equal("");
  });
  
  test("If successful, response structure valid", function() {
    if (res.status === 200) {
      expect(res.body).to.have.property("status");
      expect(res.body).to.have.property("data");
      expect(res.body.status.success).to.equal(true);
      expect(res.body.data).to.have.property("sessions_terminated");
    }
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(3000);
  });
}