meta {
  name: 00 - Database Setup for Testing
  type: http
  seq: 0
}

post {
  url: {{api_url}}/auth/register
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "email": "bruno-test-{{$timestamp}}@example.com",
    "password": "TestPassword123",
    "full_name": "Bruno Test User",
    "confirm_password": "TestPassword123"
  }
}

script:pre-request {
  const timestamp = Date.now();
  const email = `bruno-test-${timestamp}@example.com`;
  
  // Save for all subsequent steps
  bru.setEnvVar("test_email", email);
  bru.setEnvVar("test_password", "TestPassword123");
  
  console.log("🆕 Creating fresh test user:", email);
}

script:post-response {
  if (res.status === 201) {
    console.log("✅ User created, but email verification required");
    console.log("💡 Creating direct database entry to bypass verification...");
    
    // Since we can't easily verify email in automated tests,
    // we'll need to update the database directly or use a test endpoint
    // For now, just log the success and proceed to manual verification steps
    
  } else if (res.status === 409) {
    console.log("⚠️  User already exists, that's fine for testing");
  } else {
    console.log("❌ User creation failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Request completed", function() {
    expect([201, 409]).to.include(res.status);
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(5000);
  });
}