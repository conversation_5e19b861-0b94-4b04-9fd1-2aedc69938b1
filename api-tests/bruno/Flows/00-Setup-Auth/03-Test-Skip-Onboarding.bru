meta {
  name: Test Skip Onboarding
  type: http
  seq: 3.5
}

post {
  url: {{api_url}}/auth/register
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "Password123!",
    "first_name": "<PERSON><PERSON>",
    "last_name": "Test"
  }
}

tests {
  test("Register returns 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains user data", function() {
    const body = res.getBody();
    expect(body).to.have.property('data');
    expect(body.data).to.have.property('user');
    expect(body.data.user).to.have.property('email', '<EMAIL>');
    
    // Store user data for verification test
    bru.setVar('skip_test_user_id', body.data.user.id);
    bru.setVar('skip_test_email', body.data.user.email);
  });
  
  test("User should be pending verification", function() {
    const body = res.getBody();
    expect(body.data.user).to.have.property('status', 'pending_verification');
    expect(body.data.user).to.have.property('email_verified', false);
  });
}

docs {
  Register a new user for testing skip onboarding flow.
  This test creates a user that will be used to test email verification
  with SKIP_ONBOARDING=true environment variable.
  
  When SKIP_ONBOARDING is enabled, email verification should:
  1. Verify the user's email
  2. Auto-create a tenant/organization
  3. Auto-create a website
  4. Set up tenant membership with is_primary=true
  
  This allows users to skip the manual onboarding flow entirely.
}