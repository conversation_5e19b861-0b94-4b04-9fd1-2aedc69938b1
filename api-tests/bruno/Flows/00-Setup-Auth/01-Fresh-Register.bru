meta {
  name: 01 - Fresh Registration
  type: http
  seq: 1
}

post {
  url: {{api_url}}/auth/register
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "email": "apitest{{$timestamp}}@example.com",
    "password": "TestPassword123",
    "full_name": "API Test User",
    "confirm_password": "TestPassword123"
  }
}

script:pre-request {
  const timestamp = Date.now();
  const email = `apitest${timestamp}@example.com`;
  
  bru.setEnvVar("fresh_test_email", email);
  bru.setEnvVar("fresh_test_password", "TestPassword123");
  
  console.log("📧 Fresh registration with email:", email);
}

script:post-response {
  if (res.status === 201 || res.status === 200) {
    console.log("✅ Fresh registration successful");
    
    if (res.body.data) {
      const data = res.body.data;
      
      // Check email verification requirement
      if (data.requires_email_verification !== undefined) {
        bru.setEnvVar("fresh_requires_verification", data.requires_email_verification.toString());
        console.log("📧 Requires verification:", data.requires_email_verification);
      }
      
      if (data.email_verification_sent !== undefined) {
        console.log("📨 Verification email sent:", data.email_verification_sent);
      }
      
      // For systems that require email verification, we'll need to get user data after login
      // The registration only confirms the account was created and verification email sent
      console.log("📧 Email verification required before login");
    }
    
  } else {
    console.log("❌ Registration failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Registration successful", function() {
    expect([200, 201]).to.include(res.status);
  });
  
  test("Response has success status", function() {
    expect(res.body.status.success).to.equal(true);
  });
  
  test("Email verification flag provided", function() {
    expect(res.body.data).to.have.property("requires_email_verification");
    expect(res.body.data.requires_email_verification).to.be.a("boolean");
  });
  
  test("Email verification sent flag provided", function() {
    expect(res.body.data).to.have.property("email_verification_sent");
    expect(res.body.data.email_verification_sent).to.be.a("boolean");
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(5000);
  });
}