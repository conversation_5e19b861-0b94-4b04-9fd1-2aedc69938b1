# Fresh Auth Setup Flow - Bruno Collection

## Overview
This flow creates completely fresh authentication data for Bruno API testing, avoiding token expiration issues and ensuring clean test state.

## Test Flow Sequence

### 1. Fresh Registration (Step 1)
- **01-Fresh-Register.bru** - Creates new user with timestamp-based unique email
- **Purpose**: Avoid conflicts with existing test data
- **Output**: Fresh user credentials, verification token

### 2. Email Verification (Step 2)
- **02-Verify-Email.bru** - Verifies the fresh email address
- **Purpose**: Complete user activation for login
- **Output**: Email verification confirmation

### 3. Fresh Login (Step 3)
- **03-Fresh-Login.bru** - Authenticates with fresh credentials
- **Purpose**: Obtain valid access tokens
- **Output**: Fresh access/refresh tokens, session data

### 4. Organization Creation (Step 4)
- **04-Create-Fresh-Organization.bru** - Creates tenant with website
- **Purpose**: Establish tenant membership for multi-tenant APIs
- **Output**: Tenant ID, membership, website data

## Environment Variables Created

### User Data
- `fresh_test_email` - Generated unique email
- `fresh_test_password` - Test password
- `fresh_user_id` - Created user ID
- `fresh_email_verified` - Verification status

### Authentication
- `fresh_access_token` - Valid JWT token
- `fresh_refresh_token` - Refresh token
- `fresh_session_id` - Session identifier

### Organization Data
- `fresh_tenant_id` - Created tenant ID
- `fresh_membership_id` - User tenant membership
- `fresh_website_id` - Created website ID
- `fresh_org_name` - Organization name
- `fresh_org_slug` - Organization slug
- `fresh_subdomain` - Website subdomain

### Global Updates
- `access_token` - Updated with fresh token
- `tenant_id` - Updated with fresh tenant
- `user_id` - Updated with fresh user
- `website_id` - Updated with fresh website
- `membership_id` - Updated with fresh membership

## Usage

### Run Complete Flow
```bash
bru run Flows/00-Setup-Auth --env Local
```

### Run Individual Steps
```bash
bru run Flows/00-Setup-Auth/01-Fresh-Register.bru --env Local
bru run Flows/00-Setup-Auth/02-Verify-Email.bru --env Local
bru run Flows/00-Setup-Auth/03-Fresh-Login.bru --env Local
bru run Flows/00-Setup-Auth/04-Create-Fresh-Organization.bru --env Local
```

## Logout Endpoints Available

### Single Session Logout
- **File**: `Auth/Logout.bru`
- **Endpoint**: `POST /api/cms/v1/auth/logout`
- **Purpose**: Logout from current session only
- **Effect**: Invalidates current access token and session

### Multi-Device Logout  
- **File**: `Auth/Logout All Devices.bru`
- **Endpoint**: `POST /api/cms/v1/auth/logout-all`  
- **Purpose**: Logout from ALL active sessions/devices
- **Effect**: Invalidates ALL user sessions across all devices
- **Response**: Returns count of sessions terminated

## Benefits

### Authentication Management
- ✅ Single session logout (current device only)
- ✅ Multi-device logout (all sessions terminated)
- ✅ Token cleanup and environment variable clearing
- ✅ Proper error handling for expired tokens

## Integration

After running this flow, all other Bruno collections can use:
- `{{access_token}}` - Valid JWT token
- `{{tenant_id}}` - Valid tenant context
- `{{user_id}}` - Authenticated user
- `{{website_id}}` - Associated website

## Error Handling

Each step includes:
- Pre-request validation (dependencies)
- Detailed console logging
- Comprehensive test assertions
- Error message display