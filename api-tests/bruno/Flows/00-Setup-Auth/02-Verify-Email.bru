meta {
  name: 02 - Verify Fresh Email
  type: http
  seq: 2
}

post {
  url: {{api_url}}/auth/verify-email
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "token": "{{fresh_verification_token}}"
  }
}

script:pre-request {
  const token = bru.getEnvVar("fresh_verification_token");
  if (!token) {
    throw new Error("❌ No verification token. Run step 01-Fresh-Register first!");
  }
  
  console.log("🔐 Verifying email with token:", token.substring(0, 8) + "...");
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Email verification successful");
    
    if (res.body.data) {
      console.log("📧 Email verified for:", bru.getEnvVar("fresh_test_email"));
      
      // Update verification status
      bru.setEnvVar("fresh_email_verified", "true");
    }
    
  } else {
    console.log("❌ Email verification failed:", res.status);
    if (res.body && res.body.status) {
      console.log("Error:", res.body.status.message);
    }
  }
}

tests {
  test("Email verification successful", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Success response provided", function() {
    expect(res.body.status.success).to.equal(true);
  });
  
  test("Verification message included", function() {
    expect(res.body.status.message).to.be.a("string");
  });
  
  test("Response time is reasonable", function() {
    expect(res.responseTime).to.be.lessThan(3000);
  });
}