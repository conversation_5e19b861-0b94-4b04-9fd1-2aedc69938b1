meta {
  name: Send Notification
  type: http
  seq: 4
}

post {
  url: {{api_url}}/notifications/{{notification_id}}/send
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Send notification returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains success message", function() {
    const body = res.getBody();
    expect(body).to.have.property('message');
    expect(body.message).to.include('sent');
  });
  
  test("Response indicates delivery started", function() {
    const body = res.getBody();
    // Check if notification status changed or delivery initiated
    expect(res.getStatus()).to.be.lessThan(400);
  });
}