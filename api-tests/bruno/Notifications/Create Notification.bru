meta {
  name: Create Notification
  type: http
  seq: 1
}

post {
  url: {{api_url}}/notifications
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "type": "user_welcome",
    "channel": "email",
    "subject": "Welcome to Blog API! 🎉",
    "content": "Hello {{user.name}}, welcome to our platform!",
    "template_id": 1,
    "template_data": {
      "user": {
        "name": "Test User",
        "email": "<EMAIL>"
      },
      "brand": {
        "name": "Blog API",
        "logo_url": "https://example.com/logo.png",
        "support_email": "<EMAIL>"
      },
      "activation_url": "https://example.com/activate/abc123",
      "unsubscribe_url": "https://example.com/unsubscribe/abc123",
      "preferences_url": "https://example.com/preferences/abc123",
      "current_year": "2025"
    },
    "recipients": [
      {
        "email": "<EMAIL>",
        "name": "Test User",
        "type": "to"
      }
    ],
    "priority": "normal",
    "send_at": null,
    "metadata": {
      "test_notification": true,
      "source": "bruno_test"
    }
  }
}

script:post-response {
  if (res.getStatus() === 201) {
    const body = res.getBody();
    if (body.success && body.data) {
      bru.setEnvVar("notification_id", body.data.id);
      console.log("✅ Notification created with ID:", body.data.id);
    }
  }
}

tests {
  test("Create notification returns 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains notification data", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('id');
    expect(body.data).to.have.property('type', 'user_welcome');
    expect(body.data).to.have.property('channel', 'email');
    expect(body.data).to.have.property('status');
  });
  
  test("Notification ID is saved to environment", function() {
    expect(bru.getEnvVar("notification_id")).to.not.be.empty;
  });
  
  test("Recipients are properly formatted", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('recipients');
    expect(body.data.recipients).to.be.an('array');
    expect(body.data.recipients[0]).to.have.property('email', '<EMAIL>');
  });
}