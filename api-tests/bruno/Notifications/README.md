# Notification API Testing Collection

This Bruno collection provides comprehensive testing for the Notification module of the Blog API v3 platform.

## Overview

The notification system supports multi-channel delivery with template management, tracking, and analytics. This collection tests all major features including:

- ✅ **Core Notification Management**: Create, list, view, and send notifications
- ✅ **Template System**: Multi-version templates with variable substitution  
- ✅ **Multi-Channel Delivery**: Email, Socket.IO, Push notifications, SMS
- ✅ **Tracking & Analytics**: Open tracking, click tracking, engagement metrics
- ✅ **Multi-Tenant Support**: Tenant-isolated notifications and templates

## Quick Start

### Prerequisites
1. **Server Running**: Ensure the Blog API server is running at `http://localhost:9077`
2. **Database Setup**: Run migrations and seed data:
   ```bash
   make migrate-up MODULE=l_notification
   make seed-run SEEDERS=notification_seeder
   ```
3. **Authentication**: Get access token by running Auth/Login request first

### Test Execution Order

1. **Setup**: Run `Auth/Login` to get access token
2. **Core Tests**: Run notification CRUD operations
3. **Templates**: Test template management features  
4. **Tracking**: Test engagement tracking endpoints
5. **Demo**: Run complete workflow demonstration

## Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `api_url` | Base API URL | `http://localhost:9077/api/cms/v1` |
| `access_token` | JWT authentication token | Set by Auth/Login |
| `notification_id` | ID of created notification | Set by Create Notification |
| `template_id` | ID of notification template | Set by List Templates |

## Test Files Structure

```
Notifications/
├── collection.bru                 # Collection metadata and docs
├── Create Notification.bru        # Create new notification
├── List Notifications.bru         # List with filtering/pagination  
├── Get Notification.bru           # Get notification details
├── Send Notification.bru          # Send notification immediately
├── Complete Workflow Demo.bru     # End-to-end demo
├── Templates/
│   ├── List Templates.bru         # List notification templates
│   └── Get Template.bru           # Get template with versions
├── Tracking/
│   ├── Track Email Open.bru       # Track email open events
│   ├── Track Click.bru            # Track link clicks
│   ├── Get Tracking Pixel.bru     # 1x1 pixel for open tracking
│   └── Redirect and Track.bru     # Redirect with click tracking
└── README.md                      # This documentation
```

## Sample Notification Data

The collection includes realistic test data for:

- **Welcome Emails**: User onboarding notifications with HTML/text templates
- **Multi-Language Support**: English and Vietnamese template versions
- **Dynamic Variables**: User data, brand information, URLs
- **Engagement Tracking**: Open/click tracking with user agent and IP

## Template System Features

- **Multi-Version Templates**: Support for template versioning
- **Variable Substitution**: Dynamic content with handlebars-style variables
- **Multi-Language**: Templates in multiple languages (English/Vietnamese)
- **Channel Support**: Email, Socket.IO, Push, SMS templates
- **Rich Content**: HTML and text versions for email templates

## Tracking & Analytics

- **Open Tracking**: 1x1 pixel tracking for email opens
- **Click Tracking**: Track clicks on links within notifications
- **Redirect Tracking**: Track clicks while redirecting to target URLs  
- **Engagement Data**: User agent, IP address, timestamp tracking
- **Delivery Status**: Track delivery success/failure across channels

## Error Handling

All requests include comprehensive test assertions for:
- HTTP status codes
- Response structure validation
- Data integrity checks
- Error message validation
- Authentication requirements

## Multi-Channel Support

The notification system supports:

- **📧 Email**: SMTP, SendGrid, Mailtrap with HTML/text content
- **🔔 Socket.IO**: Real-time notifications for web applications
- **📱 Push**: FCM (Android), APNS (iOS) push notifications  
- **💬 SMS**: Twilio, Amazon SNS text message delivery

## Development Notes

- **Mock Delivery**: All delivery services use mock implementations for testing
- **Seed Data**: Comprehensive templates and configurations are seeded
- **Tenant Isolation**: All notifications are tenant-scoped for multi-tenancy
- **Error Logging**: Comprehensive audit logging for all notification events

## Next Steps

After running these tests, you can:

1. **Integrate with Auth**: Set up welcome emails for new user registration
2. **Blog Integration**: Add notification for published posts
3. **Real Delivery**: Implement actual email/SMS/push delivery services
4. **Dashboard**: Create admin interface for notification management
5. **Analytics**: Add detailed statistics and reporting endpoints