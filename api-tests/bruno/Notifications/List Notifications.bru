meta {
  name: List Notifications
  type: http
  seq: 2
}

get {
  url: {{api_url}}/notifications?page=1&limit=20&channel=email&status=pending&sort_by=created_at&sort_order=desc
  body: none
  auth: bearer
}

params:query {
  page: 1
  limit: 20
  channel: email
  status: pending
  sort_by: created_at
  sort_order: desc
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("List notifications returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains paginated data", function() {
    const body = res.getBody();
    expect(body).to.have.property('data');
    expect(body.data).to.have.property('data'); // notifications array
    expect(body.data).to.have.property('meta'); // pagination info
  });
  
  test("Pagination metadata is correct", function() {
    const body = res.getBody();
    expect(body.data.meta).to.have.property('total');
    expect(body.data.meta).to.have.property('page', 1);
    expect(body.data.meta).to.have.property('limit', 20);
  });
  
  test("Notifications have required fields", function() {
    const body = res.getBody();
    if (body.data.data.length > 0) {
      const notification = body.data.data[0];
      expect(notification).to.have.property('id');
      expect(notification).to.have.property('type');
      expect(notification).to.have.property('channel');
      expect(notification).to.have.property('status');
      expect(notification).to.have.property('created_at');
    }
  });
}