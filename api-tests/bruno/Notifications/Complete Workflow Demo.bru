meta {
  name: Complete Workflow Demo
  type: http
  seq: 30
}

post {
  url: {{api_url}}/notifications
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "type": "user_welcome",
    "channel": "email",
    "subject": "🎉 Welcome to Blog API Platform!",
    "content": "Welcome {{user.name}}! Your journey starts here.",
    "template_id": 1,
    "template_data": {
      "user": {
        "name": "Demo User",
        "email": "<EMAIL>"
      },
      "brand": {
        "name": "Blog API",
        "logo_url": "https://blogapi.com/logo.png",
        "support_email": "<EMAIL>"
      },
      "activation_url": "https://blogapi.com/activate/demo123",
      "unsubscribe_url": "https://blogapi.com/unsubscribe/demo123",
      "preferences_url": "https://blogapi.com/preferences/demo123",
      "current_year": "2025"
    },
    "recipients": [
      {
        "email": "<EMAIL>",
        "name": "Demo User",
        "type": "to"
      }
    ],
    "priority": "high",
    "send_immediately": true,
    "metadata": {
      "demo": true,
      "source": "bruno_workflow_test",
      "campaign": "user_onboarding"
    }
  }
}

script:post-response {
  if (res.getStatus() === 201) {
    const body = res.getBody();
    if (body.success && body.data) {
      bru.setEnvVar("demo_notification_id", body.data.id);
      console.log("🎉 Demo notification created successfully!");
      console.log("📧 Notification ID:", body.data.id);
      console.log("📊 Status:", body.data.status);
      console.log("🎯 Channel:", body.data.channel);
      console.log("👥 Recipients:", body.data.recipients.length);
    }
  }
}

tests {
  test("🚀 Demo workflow creates notification successfully", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("📧 Notification contains welcome template data", function() {
    const body = res.getBody();
    expect(body.data.template_data.user.name).to.equal('Demo User');
    expect(body.data.template_data.brand.name).to.equal('Blog API');
  });
  
  test("🎯 High priority notification configured", function() {
    const body = res.getBody();
    expect(body.data.priority).to.equal('high');
  });
  
  test("👥 Demo recipient configured correctly", function() {
    const body = res.getBody();
    expect(body.data.recipients[0].email).to.equal('<EMAIL>');
    expect(body.data.recipients[0].name).to.equal('Demo User');
  });
  
  test("📊 Notification metadata preserved", function() {
    const body = res.getBody();
    expect(body.data.metadata.demo).to.equal(true);
    expect(body.data.metadata.campaign).to.equal('user_onboarding');
  });
}