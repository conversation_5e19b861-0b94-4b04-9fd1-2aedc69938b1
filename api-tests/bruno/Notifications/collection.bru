meta {
  name: Notifications
  type: collection
}

docs {
  # Notification API Tests
  
  This collection contains API tests for the Notification module, including:
  
  ## Core Features
  - Create notifications with multi-channel delivery (email, socket, push, SMS)
  - List notifications with filtering and pagination
  - Get notification details by ID
  - Send notifications immediately or schedule for later
  - Track notification delivery status and engagement
  
  ## Templates & Versioning
  - Manage notification templates with multiple versions
  - Support for multi-language templates (English/Vietnamese)
  - Template rendering with dynamic variables
  - Template activation and deactivation
  
  ## Tracking & Analytics
  - Track email opens and clicks
  - Get engagement statistics
  - Pixel tracking and redirect tracking
  - Delivery status monitoring
  
  ## Multi-Channel Support
  - Email delivery (SMTP, SendGrid, Mailtrap)
  - Socket.IO real-time notifications
  - Push notifications (FCM, APNS)
  - SMS delivery (Twilio, SNS)
  
  ## Authentication
  All endpoints require a valid bearer token. Make sure to run the Auth/Login request first to get an access token.
  
  ## Testing Order
  1. Run Auth tests to get access token
  2. Create test notification with email template
  3. Send notification and verify delivery
  4. Test tracking endpoints (opens/clicks)
  5. Verify template management
  
  ## Environment Variables
  - `api_url`: Base API URL (http://localhost:9077/api/cms/v1)
  - `access_token`: JWT token from login
  - `notification_id`: ID of created notification
  - `template_id`: ID of notification template
}