meta {
  name: Redirect and Track
  type: http
  seq: 23
}

get {
  url: {{api_url}}/tracking/1/recipients/1/redirect?url=https://example.com/target-page
  body: none
  auth: none
}

params:query {
  url: https://example.com/target-page
}

headers {
  User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Bruno/1.0 Test Client
}

tests {
  test("Redirect returns 302", function() {
    expect(res.getStatus()).to.equal(302);
  });
  
  test("Response has location header", function() {
    expect(res.getHeaders()).to.have.property('location');
    expect(res.getHeaders()['location']).to.equal('https://example.com/target-page');
  });
}