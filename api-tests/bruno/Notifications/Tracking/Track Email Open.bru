meta {
  name: Track Email Open
  type: http
  seq: 20
}

post {
  url: {{api_url}}/tracking/1/recipients/1/open
  body: none
  auth: none
}

headers {
  User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Bruno/1.0 Test Client
}

tests {
  test("Track open returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains success message", function() {
    const body = res.getBody();
    expect(body).to.have.property('message');
    expect(body.message).to.include('tracked');
  });
}