meta {
  name: Get Tracking Pixel
  type: http
  seq: 22
}

get {
  url: {{api_url}}/tracking/1/recipients/1/pixel.gif
  body: none
  auth: none
}

headers {
  User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Bruno/1.0 Test Client
}

tests {
  test("Tracking pixel returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has correct content type", function() {
    expect(res.getHeaders()['content-type']).to.include('image/gif');
  });
  
  test("Response has cache headers", function() {
    expect(res.getHeaders()).to.have.property('cache-control');
    expect(res.getHeaders()['cache-control']).to.include('no-cache');
  });
}