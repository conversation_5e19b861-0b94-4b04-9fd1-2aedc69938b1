meta {
  name: Track Click
  type: http
  seq: 21
}

post {
  url: {{api_url}}/tracking/1/recipients/1/click
  body: json
  auth: none
}

headers {
  User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Bruno/1.0 Test Client
}

body:json {
  {
    "url": "https://example.com/welcome"
  }
}

tests {
  test("Track click returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains success message", function() {
    const body = res.getBody();
    expect(body).to.have.property('message');
    expect(body.message).to.include('tracked');
  });
}