meta {
  name: Get Template
  type: http
  seq: 11
}

get {
  url: {{api_url}}/templates/{{template_id}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Get template returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains template details", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('id');
    expect(body.data).to.have.property('name');
    expect(body.data).to.have.property('code');
    expect(body.data).to.have.property('type');
    expect(body.data).to.have.property('channel');
    expect(body.data).to.have.property('description');
    expect(body.data).to.have.property('variables');
    expect(body.data).to.have.property('versions');
  });
  
  test("Template has versions", function() {
    const body = res.getBody();
    expect(body.data.versions).to.be.an('array');
    if (body.data.versions.length > 0) {
      const version = body.data.versions[0];
      expect(version).to.have.property('version_number');
      expect(version).to.have.property('subject');
      expect(version).to.have.property('body_html');
      expect(version).to.have.property('body_text');
    }
  });
  
  test("Template variables are defined", function() {
    const body = res.getBody();
    expect(body.data.variables).to.be.an('array');
    if (body.data.variables.length > 0) {
      expect(body.data.variables).to.include('user.name');
      expect(body.data.variables).to.include('brand.name');
    }
  });
}