meta {
  name: List Templates
  type: http
  seq: 10
}

get {
  url: {{api_url}}/templates?type=transactional&channel=email&is_active=true&page=1&limit=10
  body: none
  auth: bearer
}

params:query {
  type: transactional
  channel: email
  is_active: true
  page: 1
  limit: 10
}

auth:bearer {
  token: {{access_token}}
}

script:post-response {
  if (res.getStatus() === 200) {
    const body = res.getBody();
    if (body.success && body.data && body.data.length > 0) {
      bru.setEnvVar("template_id", body.data[0].id);
      console.log("✅ Template ID saved:", body.data[0].id);
    }
  }
}

tests {
  test("List templates returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains paginated templates", function() {
    const body = res.getBody();
    expect(body).to.have.property('data');
    expect(body.data).to.have.property('data'); // templates array
    expect(body.data).to.have.property('meta'); // pagination info
  });
  
  test("Templates have required fields", function() {
    const body = res.getBody();
    if (body.data.data.length > 0) {
      const template = body.data.data[0];
      expect(template).to.have.property('id');
      expect(template).to.have.property('name');
      expect(template).to.have.property('code');
      expect(template).to.have.property('type');
      expect(template).to.have.property('channel');
      expect(template).to.have.property('is_active');
    }
  });
  
  test("Filtered by channel", function() {
    const body = res.getBody();
    if (body.data.data.length > 0) {
      body.data.data.forEach(template => {
        expect(template.channel).to.equal('email');
      });
    }
  });
}