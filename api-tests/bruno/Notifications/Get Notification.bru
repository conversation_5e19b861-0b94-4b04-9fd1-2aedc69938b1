meta {
  name: Get Notification
  type: http
  seq: 3
}

get {
  url: {{api_url}}/notifications/{{notification_id}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Get notification returns 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has success status", function() {
    const body = res.getBody();
    expect(body).to.have.property('success', true);
  });
  
  test("Response contains notification details", function() {
    const body = res.getBody();
    expect(body.data).to.have.property('id');
    expect(body.data).to.have.property('type');
    expect(body.data).to.have.property('channel');
    expect(body.data).to.have.property('status');
    expect(body.data).to.have.property('subject');
    expect(body.data).to.have.property('content');
    expect(body.data).to.have.property('recipients');
  });
  
  test("Notification ID matches requested ID", function() {
    const body = res.getBody();
    const requestedId = parseInt(bru.getEnvVar("notification_id"));
    expect(body.data.id).to.equal(requestedId);
  });
  
  test("Recipients data is complete", function() {
    const body = res.getBody();
    expect(body.data.recipients).to.be.an('array');
    if (body.data.recipients.length > 0) {
      const recipient = body.data.recipients[0];
      expect(recipient).to.have.property('email');
      expect(recipient).to.have.property('status');
      expect(recipient).to.have.property('type');
    }
  });
  
  test("Template data is preserved", function() {
    const body = res.getBody();
    if (body.data.template_data) {
      expect(body.data.template_data).to.be.an('object');
      expect(body.data.template_data).to.have.property('user');
      expect(body.data.template_data).to.have.property('brand');
    }
  });
}