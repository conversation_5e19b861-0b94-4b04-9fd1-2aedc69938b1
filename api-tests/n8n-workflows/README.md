# Blog API v3 - N8N Workflow Tests

Bộ workflows n8n để test tự động các API flows của Blog API v3.

## 📋 Danh sách Workflows

### 1. `01-register-user.json` - Đăng ký User
- **Mục đích**: Test API đăng ký user mới
- **Tự động**: Tạo random email, password, tên
- **Kết quả**: User được tạo với status `pending_verification`

### 2. `02-login-user.json` - Đăng nhập User  
- **M<PERSON><PERSON> đích**: Test API đăng nhập
- **Lưu ý**: Cần user đã verified email
- **Kết quả**: JWT token (không có tenant context)

### 3. `03-onboarding-organization.json` - Tạo Organization
- **M<PERSON><PERSON> đích**: Test API tạo organization/tenant
- **Yêu cầu**: JWT token hợp lệ
- **Kết quả**: Organization + Website + JWT token mới (có tenant context)

### 4. `04-full-flow.json` - 🚀 Full Flow Test
- **M<PERSON><PERSON> đích**: Test toàn bộ luồng Register → Login → Onboarding
- **Tự động**: Tất cả data được generate random
- **Một click**: Chạy toàn bộ flow chỉ với một lần bấm
- **Kết quả**: User hoàn chỉnh với organization và website

## 🚀 Cách sử dụng

### Chuẩn bị
1. **Chạy API server**: `go run cmd/server/main.go` (port 9077)
2. **Cài đặt Docker**: [Docker Desktop](https://docs.docker.com/get-docker/)

### Khởi động N8N (Docker) - Recommended
```bash
# Option 1: Sử dụng Docker Compose (Recommended)
./start-n8n.sh

# Option 2: Hoặc chạy setup script đầy đủ
./setup.sh

# Option 3: Hoặc chạy manual
docker-compose up -d
```

### Import Workflows
1. Mở n8n UI (http://localhost:5678)
2. **Import** từng workflow file JSON
3. **Activate** workflows cần test

### Khởi động N8N (Manual - không Docker)
```bash
# Cài đặt n8n
npm install -g n8n

# Chạy n8n
n8n start
```

### Test nhanh - Full Flow
1. Mở workflow `04-full-flow.json`
2. Bấm **"Execute Workflow"**
3. Chờ kết quả ✅

### Test từng bước
1. Chạy `01-register-user.json` 
2. **Lưu ý**: Cần verify email trước khi login
3. Chạy `02-login-user.json`
4. Copy JWT token
5. Paste vào `03-onboarding-organization.json`
6. Chạy onboarding

## 📊 Kết quả mong đợi

### Full Flow Success
```json
{
  "flow_completed": true,
  "success": true,
  "user": {
    "id": 123,
    "email": "<EMAIL>"
  },
  "organization": {
    "tenant_id": 456,
    "name": "Full Flow Org 1234",
    "slug": "full-flow-org-1234"
  },
  "website": {
    "created": true,
    "id": 789,
    "name": "Main Website"
  },
  "tokens": {
    "access_token": "eyJ0eXAiOiJKV1Q...",
    "auth_header": "Bearer eyJ0eXAiOiJKV1Q..."
  },
  "message": "🎉 COMPLETE SUCCESS! Full flow validation complete!"
}
```

## 🔧 Tùy chỉnh

### Thay đổi test data
Chỉnh sửa trong node **"Generate Test Data"**:
```javascript
const userData = {
  first_name: "YourName",
  last_name: "YourLastName", 
  email: "<EMAIL>",
  password: "YourPassword123!"
};
```

### Thay đổi API endpoint
Chỉnh sửa URL trong HTTP Request nodes:
```
# Với Docker (default)
http://host.docker.internal:9077/api/cms/v1/auth/register

# Với n8n local (không Docker)
http://localhost:9077/api/cms/v1/auth/register
```

### Thay đổi organization data
Chỉnh sửa trong node **"Prepare Organization Data"**:
```javascript
const organizationData = {
  name: "Your Organization",
  industry: "Your Industry",
  size: "small|medium|large|enterprise"
};
```

## 🎯 Kiến trúc mới được test

### ✅ Luồng chính xác
1. **Register**: Tạo user (không có tenant)
2. **Login**: JWT token (không có tenant context)  
3. **Onboarding**: Tạo organization + JWT token mới (có tenant context)

### ✅ Validation
- Email verification required
- Password strength validation
- Organization name uniqueness
- Multi-tenant isolation
- JWT token management

## 🐛 Troubleshooting

### Lỗi thường gặp

**Registration fails**:
- Check email format
- Check password strength
- Check server logs

**Login fails**:
- User chưa verify email
- Invalid credentials
- Account suspended

**Onboarding fails**:
- JWT token expired
- User đã có organization
- Invalid organization data

### Debug
1. Check n8n execution logs
2. Check server logs: `tail -f logs/app.log`
3. Check database: `mysql -u root -p blog_api_v3`

## 📝 Lưu ý

- **Email verification**: Workflow không tự động verify email
- **JWT tokens**: Có thời hạn, cần renew nếu cần
- **Rate limiting**: Có thể bị giới hạn request
- **Database**: Mỗi lần chạy tạo user/org mới

## 🔄 Tích hợp CI/CD

Có thể chạy workflows từ command line:
```bash
# Install n8n CLI
npm install -g n8n

# Run workflow
n8n execute --file 04-full-flow.json
```

## 🎉 Kết luận

Workflows này giúp test tự động và validate kiến trúc mới của Blog API v3:
- ✅ **Separation of concerns**: Auth vs Onboarding
- ✅ **Multi-tenant architecture**: Proper tenant isolation  
- ✅ **JWT token management**: Context-aware tokens
- ✅ **API consistency**: Proper error handling và responses

**Chỉ cần 1 click để test toàn bộ flow!** 🚀