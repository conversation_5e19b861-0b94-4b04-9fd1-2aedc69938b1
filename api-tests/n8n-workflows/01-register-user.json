{"name": "Blog API v3 - Register User", "nodes": [{"parameters": {}, "id": "1a2b3c4d-e5f6-7890-abcd-123456789abc", "name": "Start", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Generate random user data for testing\nconst timestamp = Date.now();\nconst randomSuffix = Math.floor(Math.random() * 10000);\n\nconst userData = {\n  first_name: `Test${randomSuffix}`,\n  last_name: `User${randomSuffix}`,\n  email: `test${timestamp}@example.com`,\n  password: \"Test123!@#\",\n  password_confirmation: \"Test123!@#\"\n};\n\nconsole.log('Generated user data:', userData);\n\nreturn {\n  json: {\n    userData,\n    timestamp,\n    randomSuffix\n  }\n};"}, "id": "2b3c4d5e-f6g7-8901-bcde-234567890bcd", "name": "Generate Random User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "http://host.docker.internal:9077/api/cms/v1/auth/register", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "first_name", "value": "={{ $json.userData.first_name }}"}, {"name": "last_name", "value": "={{ $json.userData.last_name }}"}, {"name": "email", "value": "={{ $json.userData.email }}"}, {"name": "password", "value": "={{ $json.userData.password }}"}, {"name": "password_confirmation", "value": "={{ $json.userData.password_confirmation }}"}]}, "options": {}}, "id": "3c4d5e6f-g7h8-9012-cdef-345678901cde", "name": "Register User API Call", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [680, 300]}, {"parameters": {"jsCode": "// Process registration response\nconst response = $input.all()[0].json;\nconst userData = $('Generate Random User Data').first().json.userData;\n\nconsole.log('Registration response:', response);\n\nif (response.status === 'success') {\n  return {\n    json: {\n      success: true,\n      message: 'User registered successfully',\n      user_id: response.data?.user?.id,\n      email: userData.email,\n      password: userData.password,\n      status: response.data?.user?.status,\n      email_verified: response.data?.user?.email_verified_at !== null,\n      created_at: response.data?.user?.created_at,\n      full_response: response\n    }\n  };\n} else {\n  return {\n    json: {\n      success: false,\n      error: response.message || 'Registration failed',\n      errors: response.errors || {},\n      full_response: response\n    }\n  };\n}"}, "id": "4d5e6f7g-h8i9-0123-defg-456789012def", "name": "Process Registration Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "abc123", "leftValue": "={{ $json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "5e6f7g8h-i9j0-1234-efgh-567890123efg", "name": "Check Registration Success", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"jsCode": "// Registration successful - prepare data for next steps\nconst data = $input.all()[0].json;\n\nconsole.log('✅ Registration successful!');\nconsole.log('User ID:', data.user_id);\nconsole.log('Email:', data.email);\nconsole.log('Status:', data.status);\nconsole.log('Email Verified:', data.email_verified);\n\nreturn {\n  json: {\n    ...data,\n    next_step: data.email_verified ? 'login' : 'email_verification',\n    message: `User registered successfully. ${data.email_verified ? 'Ready to login.' : 'Email verification required.'}`\n  }\n};"}, "id": "6f7g8h9i-j0k1-2345-fghi-678901234fgh", "name": "Registration Success", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"jsCode": "// Registration failed - show error details\nconst data = $input.all()[0].json;\n\nconsole.log('❌ Registration failed!');\nconsole.log('Error:', data.error);\nconsole.log('Validation Errors:', data.errors);\n\nreturn {\n  json: {\n    ...data,\n    message: `Registration failed: ${data.error}`,\n    troubleshooting: {\n      common_issues: [\n        'Email already exists',\n        'Password too weak',\n        'Invalid email format',\n        'Missing required fields'\n      ],\n      validation_errors: data.errors\n    }\n  }\n};"}, "id": "7g8h9i0j-k1l2-3456-ghij-789012345ghi", "name": "Registration Failed", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 400]}], "connections": {"Start": {"main": [[{"node": "Generate Random User Data", "type": "main", "index": 0}]]}, "Generate Random User Data": {"main": [[{"node": "Register User API Call", "type": "main", "index": 0}]]}, "Register User API Call": {"main": [[{"node": "Process Registration Response", "type": "main", "index": 0}]]}, "Process Registration Response": {"main": [[{"node": "Check Registration Success", "type": "main", "index": 0}]]}, "Check Registration Success": {"main": [[{"node": "Registration Success", "type": "main", "index": 0}], [{"node": "Registration Failed", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1.0.0", "meta": {"templateCredsSetupCompleted": true}, "id": "register-user-workflow", "tags": ["blog-api-v3", "auth", "register"]}