{"name": "Blog API v3 - Full Flow (Register → Login → Onboarding)", "nodes": [{"parameters": {}, "id": "start-node", "name": "🚀 Start Full Flow", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 400]}, {"parameters": {"jsCode": "// Generate complete test data for full flow\nconst timestamp = Date.now();\nconst randomSuffix = Math.floor(Math.random() * 10000);\n\n// User data for registration\nconst userData = {\n  first_name: `TestUser${randomSuffix}`,\n  last_name: `FullFlow${randomSuffix}`,\n  email: `fullflow${timestamp}@example.com`,\n  password: \"FullFlow123!@#\",\n  password_confirmation: \"FullFlow123!@#\"\n};\n\n// Organization data for onboarding\nconst organizationData = {\n  name: `Full Flow Org ${randomSuffix}`,\n  description: `Complete test organization created via full flow at ${new Date().toISOString()}`,\n  website_url: `https://fullflow-${randomSuffix}.example.com`,\n  industry: \"Technology\",\n  size: \"startup\",\n  settings: {\n    timezone: \"Asia/Ho_Chi_Minh\",\n    language: \"vi\",\n    currency: \"VND\",\n    features: {\n      blog: true,\n      ecommerce: true,\n      analytics: true,\n      ai_tools: true\n    },\n    preferences: {\n      theme: \"modern\",\n      layout: \"dashboard\",\n      notifications: true\n    }\n  }\n};\n\nconsole.log('🎯 Full Flow Test Data Generated');\nconsole.log('User:', userData.email);\nconsole.log('Organization:', organizationData.name);\n\nreturn {\n  json: {\n    userData,\n    organizationData,\n    timestamp,\n    randomSuffix,\n    flow_id: `full-flow-${timestamp}-${randomSuffix}`\n  }\n};"}, "id": "generate-data", "name": "📋 Generate Test Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 400]}, {"parameters": {"url": "http://host.docker.internal:9077/api/cms/v1/auth/register", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "first_name", "value": "={{ $json.userData.first_name }}"}, {"name": "last_name", "value": "={{ $json.userData.last_name }}"}, {"name": "email", "value": "={{ $json.userData.email }}"}, {"name": "password", "value": "={{ $json.userData.password }}"}, {"name": "password_confirmation", "value": "={{ $json.userData.password_confirmation }}"}]}, "options": {}}, "id": "register-call", "name": "👤 Register User", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [680, 400]}, {"parameters": {"jsCode": "// Process registration and prepare for login\nconst response = $input.all()[0].json;\nconst userData = $('📋 Generate Test Data').first().json.userData;\n\nconsole.log('📝 Registration Status:', response.status);\n\nif (response.status === 'success') {\n  console.log('✅ User registered successfully');\n  console.log('User ID:', response.data?.user?.id);\n  console.log('Email verified:', response.data?.user?.email_verified_at !== null);\n  \n  return {\n    json: {\n      success: true,\n      user_id: response.data?.user?.id,\n      email: userData.email,\n      password: userData.password,\n      email_verified: response.data?.user?.email_verified_at !== null,\n      registration_response: response,\n      next_step: 'login',\n      message: 'Registration successful, proceeding to login'\n    }\n  };\n} else {\n  console.log('❌ Registration failed:', response.message);\n  throw new Error(`Registration failed: ${response.message}`);\n}"}, "id": "process-registration", "name": "✅ Process Registration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 400]}, {"parameters": {"amount": 2, "unit": "seconds"}, "id": "wait-before-login", "name": "⏱️ Wait Before Login", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1120, 400]}, {"parameters": {"url": "http://host.docker.internal:9077/api/cms/v1/auth/login", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "email", "value": "={{ $json.email }}"}, {"name": "password", "value": "={{ $json.password }}"}]}, "options": {}}, "id": "login-call", "name": "🔐 Login User", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [1340, 400]}, {"parameters": {"jsCode": "// Process login and prepare for onboarding\nconst response = $input.all()[0].json;\nconst userData = $('✅ Process Registration').first().json;\n\nconsole.log('🔐 Login Status:', response.status);\n\nif (response.status === 'success') {\n  console.log('✅ Login successful');\n  console.log('Access Token:', response.data?.access_token?.substring(0, 20) + '...');\n  console.log('User has tenant:', response.data?.user?.tenant_id !== null);\n  \n  return {\n    json: {\n      success: true,\n      access_token: response.data?.access_token,\n      refresh_token: response.data?.refresh_token,\n      token_type: response.data?.token_type || 'Bearer',\n      auth_header: `Bearer ${response.data?.access_token}`,\n      user: response.data?.user,\n      has_tenant: response.data?.user?.tenant_id !== null,\n      tenant_id: response.data?.user?.tenant_id,\n      email: userData.email,\n      user_id: userData.user_id,\n      login_response: response,\n      next_step: 'onboarding',\n      message: 'Login successful, proceeding to create organization'\n    }\n  };\n} else {\n  console.log('❌ Login failed:', response.message);\n  throw new Error(`Login failed: ${response.message}`);\n}"}, "id": "process-login", "name": "✅ Process Login", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 400]}, {"parameters": {"amount": 1, "unit": "seconds"}, "id": "wait-before-onboarding", "name": "⏱️ Wait Before Onboarding", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1780, 400]}, {"parameters": {"jsCode": "// Prepare organization data with login context\nconst loginData = $input.all()[0].json;\nconst orgData = $('📋 Generate Test Data').first().json.organizationData;\n\nconsole.log('🏢 Preparing organization creation with authenticated user');\n\nreturn {\n  json: {\n    ...loginData,\n    organizationData: orgData,\n    message: 'Ready to create organization'\n  }\n};"}, "id": "prepare-onboarding", "name": "🏢 Prepare Onboarding", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 400]}, {"parameters": {"url": "http://host.docker.internal:9077/api/cms/v1/onboarding/organization", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}, {"name": "Authorization", "value": "={{ $json.auth_header }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $json.organizationData.name }}"}, {"name": "description", "value": "={{ $json.organizationData.description }}"}, {"name": "website_url", "value": "={{ $json.organizationData.website_url }}"}, {"name": "industry", "value": "={{ $json.organizationData.industry }}"}, {"name": "size", "value": "={{ $json.organizationData.size }}"}, {"name": "settings", "value": "={{ JSON.stringify($json.organizationData.settings) }}"}]}, "options": {}}, "id": "onboarding-call", "name": "🎯 Create Organization", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [2220, 400]}, {"parameters": {"jsCode": "// Final processing of complete flow\nconst response = $input.all()[0].json;\nconst prepData = $('🏢 Prepare Onboarding').first().json;\nconst flowData = $('📋 Generate Test Data').first().json;\n\nconsole.log('🎯 Organization Creation Status:', response.status);\n\nif (response.status === 'success') {\n  const data = response.data;\n  console.log('🎉 FULL FLOW COMPLETED SUCCESSFULLY!');\n  console.log('📊 Flow Summary:');\n  console.log('  - User Registration: ✅');\n  console.log('  - User Login: ✅');\n  console.log('  - Organization Creation: ✅');\n  console.log('  - Tenant ID:', data.tenant?.id);\n  console.log('  - Organization:', data.tenant?.name);\n  console.log('  - Website Created:', data.website !== null);\n  console.log('  - New Auth Tokens:', data.tokens !== null);\n  \n  return {\n    json: {\n      flow_completed: true,\n      flow_id: flowData.flow_id,\n      success: true,\n      \n      // User data\n      user: {\n        id: prepData.user_id,\n        email: prepData.email,\n        registered_at: new Date().toISOString()\n      },\n      \n      // Organization data\n      organization: {\n        tenant_id: data.tenant?.id,\n        name: data.tenant?.name,\n        slug: data.tenant?.slug,\n        industry: flowData.organizationData.industry,\n        size: flowData.organizationData.size\n      },\n      \n      // Membership data\n      membership: {\n        id: data.membership?.id,\n        is_primary: data.membership?.is_primary,\n        role: data.membership?.role || 'owner',\n        status: data.membership?.status || 'active'\n      },\n      \n      // Website data\n      website: {\n        created: data.website !== null,\n        id: data.website?.id,\n        name: data.website?.name,\n        url: flowData.organizationData.website_url\n      },\n      \n      // Auth tokens\n      tokens: {\n        access_token: data.tokens?.access_token,\n        refresh_token: data.tokens?.refresh_token,\n        token_type: data.tokens?.token_type || 'Bearer',\n        auth_header: data.tokens?.access_token ? `Bearer ${data.tokens.access_token}` : null\n      },\n      \n      // Flow metadata\n      flow_summary: {\n        steps_completed: ['registration', 'login', 'onboarding'],\n        total_time: 'Completed in workflow execution',\n        architecture_validated: 'New auth/onboarding flow working correctly'\n      },\n      \n      // Next steps\n      next_steps: [\n        'User can now access all tenant-specific features',\n        'Create and manage websites',\n        'Add blog posts and content',\n        'Invite team members',\n        'Configure organization settings',\n        'Access analytics and reports'\n      ],\n      \n      message: `🎉 COMPLETE SUCCESS! User ${prepData.email} has been registered, logged in, and organization '${data.tenant?.name}' has been created. Full flow validation complete!`,\n      \n      full_responses: {\n        registration: prepData.registration_response,\n        login: prepData.login_response,\n        onboarding: response\n      }\n    }\n  };\n} else {\n  console.log('❌ Organization creation failed:', response.message);\n  throw new Error(`Organization creation failed: ${response.message}`);\n}"}, "id": "final-success", "name": "🎉 Full Flow Success", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2440, 400]}], "connections": {"🚀 Start Full Flow": {"main": [[{"node": "📋 Generate Test Data", "type": "main", "index": 0}]]}, "📋 Generate Test Data": {"main": [[{"node": "👤 Register User", "type": "main", "index": 0}]]}, "👤 Register User": {"main": [[{"node": "✅ Process Registration", "type": "main", "index": 0}]]}, "✅ Process Registration": {"main": [[{"node": "⏱️ Wait Before Login", "type": "main", "index": 0}]]}, "⏱️ Wait Before Login": {"main": [[{"node": "🔐 Login User", "type": "main", "index": 0}]]}, "🔐 Login User": {"main": [[{"node": "✅ Process Login", "type": "main", "index": 0}]]}, "✅ Process Login": {"main": [[{"node": "⏱️ Wait Before Onboarding", "type": "main", "index": 0}]]}, "⏱️ Wait Before Onboarding": {"main": [[{"node": "🏢 Prepare Onboarding", "type": "main", "index": 0}]]}, "🏢 Prepare Onboarding": {"main": [[{"node": "🎯 Create Organization", "type": "main", "index": 0}]]}, "🎯 Create Organization": {"main": [[{"node": "🎉 Full Flow Success", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": {"errorWorkflow": "", "saveDataErrorExecution": "all"}}, "versionId": "1.0.0", "meta": {"templateCredsSetupCompleted": true}, "id": "full-flow-workflow", "tags": ["blog-api-v3", "full-flow", "integration-test", "auth", "onboarding"]}