#!/bin/bash

# Blog API v3 - N8N Workflow Setup Script with Docker
# Tự động setup và chạy tests sử dụng Docker

echo "🚀 Blog API v3 - N8N Workflow Setup (Docker)"
echo "============================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker chưa được cài đặt"
    echo "📥 Vui lòng cài đặt Docker từ: https://docs.docker.com/get-docker/"
    exit 1
else
    echo "✅ Docker đã được cài đặt"
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker daemon không chạy"
    echo "🔧 Vui lòng khởi động Docker Desktop hoặc Docker service"
    exit 1
else
    echo "✅ Docker daemon đang chạy"
fi

# Check if API server is running
echo ""
echo "🔍 Kiểm tra API server..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:9077/health 2>/dev/null)

if [ "$response" = "200" ]; then
    echo "✅ API server đang chạy trên port 9077"
elif [ "$response" = "000" ]; then
    echo "❌ API server không chạy"
    echo "🔧 Vui lòng chạy: go run cmd/server/main.go"
    echo "⏳ Đợi server khởi động..."
    sleep 3
else
    echo "⚠️  API server response: $response"
fi

# Create Docker volume for n8n data persistence
echo ""
echo "📂 Tạo Docker volume cho n8n data..."
docker volume create n8n_data 2>/dev/null || echo "📋 Volume n8n_data đã tồn tại"

# Create workflows directory if not exists
mkdir -p workflows-imported

# Check if n8n container is already running
if docker ps | grep -q "n8n-blogapi"; then
    echo "✅ N8N container đã chạy"
    echo "🌐 N8N UI: http://localhost:5678"
else
    echo ""
    echo "🚀 Khởi động N8N container..."
    
    # Run n8n in Docker with proper configuration
    docker run -d \
        --name n8n-blogapi \
        -p 5678:5678 \
        -v n8n_data:/home/<USER>/.n8n \
        -v $(pwd):/workflows \
        -e N8N_HOST=localhost \
        -e N8N_PORT=5678 \
        -e N8N_PROTOCOL=http \
        -e N8N_BASIC_AUTH_ACTIVE=false \
        -e N8N_EXECUTION_DATA_SAVE_ON_ERROR=all \
        -e N8N_EXECUTION_DATA_SAVE_ON_SUCCESS=all \
        -e N8N_EXECUTION_DATA_SAVE_ON_MANUAL_EXECUTION=true \
        -e N8N_LOG_LEVEL=info \
        -e N8N_LOG_OUTPUT=console \
        -e GENERIC_TIMEZONE=Asia/Ho_Chi_Minh \
        -e BLOG_API_BASE_URL=http://docker.for.mac.localhost:9077 \
        -e BLOG_API_VERSION=v1 \
        -e BLOG_API_PREFIX=/api/cms/v1 \
        -e N8N_WORKFLOW_EDITOR_SAVE_ON_EXECUTION=true \
        --restart unless-stopped \
        n8nio/n8n:latest
    
    if [ $? -eq 0 ]; then
        echo "✅ N8N container đã khởi động thành công"
        echo "⏳ Đang chờ N8N sẵn sàng..."
        sleep 10
        
        # Wait for n8n to be ready
        for i in {1..30}; do
            if curl -s http://localhost:5678 > /dev/null 2>&1; then
                echo "✅ N8N đã sẵn sàng!"
                break
            fi
            if [ $i -eq 30 ]; then
                echo "⚠️  N8N có thể chưa sẵn sàng, nhưng container đã chạy"
            fi
            sleep 2
        done
    else
        echo "❌ Lỗi khởi động N8N container"
        exit 1
    fi
fi

echo ""
echo "📋 Danh sách workflows có sẵn:"
echo "1. 01-register-user.json - Đăng ký user"
echo "2. 02-login-user.json - Đăng nhập"  
echo "3. 03-onboarding-organization.json - Tạo organization"
echo "4. 04-full-flow.json - 🚀 Full flow test"

echo ""
echo "🎯 Hướng dẫn sử dụng:"
echo "1. Mở N8N UI: http://localhost:5678"
echo "2. Import workflows từ thư mục này"
echo "3. Chạy workflow '04-full-flow.json' để test toàn bộ"

echo ""
echo "📊 Test endpoints (từ trong Docker):"
echo "- Register: POST http://docker.for.mac.localhost:9077/api/cms/v1/auth/register"
echo "- Login: POST http://docker.for.mac.localhost:9077/api/cms/v1/auth/login"
echo "- Onboarding: POST http://docker.for.mac.localhost:9077/api/cms/v1/onboarding/organization"

echo ""
echo "🔧 Docker commands hữu ích:"
echo "- Xem logs: docker logs n8n-blogapi"
echo "- Dừng container: docker stop n8n-blogapi"
echo "- Khởi động lại: docker restart n8n-blogapi"
echo "- Xóa container: docker rm -f n8n-blogapi"
echo "- Xóa volume: docker volume rm n8n_data"

echo ""
echo "✅ Setup hoàn tất!"
echo "🌐 N8N UI: http://localhost:5678"
echo "📂 Workflows volume: /workflows (mapped to current directory)"
echo "💾 Data persistence: n8n_data volume"

# Optional: Open browser
read -p "🌐 Bạn có muốn mở N8N UI trong browser không? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v open &> /dev/null; then
        open http://localhost:5678
    elif command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:5678
    else
        echo "🌐 Vui lòng mở: http://localhost:5678"
    fi
fi

echo ""
echo "🚀 Bây giờ bạn có thể:"
echo "1. Import workflows vào N8N"
echo "2. Chạy full flow test với 1 click"
echo "3. Test từng API endpoint riêng biệt"
echo ""
echo "🎉 Happy testing!"