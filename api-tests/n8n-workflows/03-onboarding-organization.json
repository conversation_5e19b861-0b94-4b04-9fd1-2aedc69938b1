{"name": "Blog API v3 - Onboarding Organization", "nodes": [{"parameters": {}, "id": "1a2b3c4d-e5f6-7890-abcd-123456789abc", "name": "Start", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Prepare organization data and auth token\nconst timestamp = Date.now();\nconst randomSuffix = Math.floor(Math.random() * 10000);\n\n// Organization data\nconst organizationData = {\n  name: `Test Organization ${randomSuffix}`,\n  description: `This is a test organization created at ${new Date().toISOString()}`,\n  website_url: `https://test-org-${randomSuffix}.example.com`,\n  industry: \"Technology\",\n  size: \"small\",\n  settings: {\n    timezone: \"Asia/Ho_Chi_Minh\",\n    language: \"vi\",\n    currency: \"VND\",\n    features: {\n      blog: true,\n      ecommerce: false,\n      analytics: true\n    }\n  }\n};\n\n// Test auth token - replace with actual token from login\nconst authToken = \"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...\";\n\n// If you have token from previous workflow, use it\n// const authToken = $('Previous Login')?.first()?.json?.auth_header || \"Bearer test-token\";\n\nconsole.log('Organization data:', organizationData);\n\nreturn {\n  json: {\n    organizationData,\n    authToken,\n    timestamp,\n    randomSuffix\n  }\n};"}, "id": "2b3c4d5e-f6g7-8901-bcde-234567890bcd", "name": "Prepare Organization Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "http://host.docker.internal:9077/api/cms/v1/onboarding/organization", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}, {"name": "Authorization", "value": "={{ $json.authToken }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $json.organizationData.name }}"}, {"name": "description", "value": "={{ $json.organizationData.description }}"}, {"name": "website_url", "value": "={{ $json.organizationData.website_url }}"}, {"name": "industry", "value": "={{ $json.organizationData.industry }}"}, {"name": "size", "value": "={{ $json.organizationData.size }}"}, {"name": "settings", "value": "={{ JSON.stringify($json.organizationData.settings) }}"}]}, "options": {}}, "id": "3c4d5e6f-g7h8-9012-cdef-345678901cde", "name": "Create Organization API Call", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [680, 300]}, {"parameters": {"jsCode": "// Process organization creation response\nconst response = $input.all()[0].json;\nconst orgData = $('Prepare Organization Data').first().json.organizationData;\n\nconsole.log('Organization creation response:', response);\n\nif (response.status === 'success') {\n  const data = response.data;\n  return {\n    json: {\n      success: true,\n      message: 'Organization created successfully',\n      tenant_id: data.tenant?.id,\n      tenant_name: data.tenant?.name,\n      tenant_slug: data.tenant?.slug,\n      membership_id: data.membership?.id,\n      is_primary: data.membership?.is_primary,\n      role: data.membership?.role,\n      new_tokens: data.tokens || null,\n      access_token: data.tokens?.access_token,\n      refresh_token: data.tokens?.refresh_token,\n      website_created: data.website !== null,\n      website_id: data.website?.id,\n      website_name: data.website?.name,\n      full_response: response\n    }\n  };\n} else {\n  return {\n    json: {\n      success: false,\n      error: response.message || 'Organization creation failed',\n      errors: response.errors || {},\n      status_code: response.status_code,\n      full_response: response\n    }\n  };\n}"}, "id": "4d5e6f7g-h8i9-0123-defg-456789012def", "name": "Process Organization Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "abc123", "leftValue": "={{ $json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "5e6f7g8h-i9j0-1234-efgh-567890123efg", "name": "Check Organization Success", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"jsCode": "// Organization creation successful\nconst data = $input.all()[0].json;\n\nconsole.log('✅ Organization created successfully!');\nconsole.log('Tenant ID:', data.tenant_id);\nconsole.log('Tenant Name:', data.tenant_name);\nconsole.log('Is Primary:', data.is_primary);\nconsole.log('Website Created:', data.website_created);\nconsole.log('New Tokens Generated:', data.new_tokens !== null);\n\nreturn {\n  json: {\n    ...data,\n    auth_header: data.access_token ? `Bearer ${data.access_token}` : null,\n    onboarding_complete: true,\n    next_steps: [\n      'User can now access tenant-specific features',\n      'Create websites and manage content',\n      'Invite other users to the organization',\n      'Configure organization settings'\n    ],\n    ready_for: [\n      'Website management',\n      'Content creation',\n      'User management',\n      'Analytics dashboard'\n    ],\n    message: `Organization '${data.tenant_name}' created successfully. ${data.website_created ? 'Default website also created.' : ''} User is now ready to use the platform.`\n  }\n};"}, "id": "6f7g8h9i-j0k1-2345-fghi-678901234fgh", "name": "Organization Success", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"jsCode": "// Organization creation failed - analyze error\nconst data = $input.all()[0].json;\n\nconsole.log('❌ Organization creation failed!');\nconsole.log('Error:', data.error);\nconsole.log('Status Code:', data.status_code);\n\n// Analyze common error scenarios\nlet errorAnalysis = 'Unknown error';\nlet nextAction = 'Review request data';\n\nif (data.status_code === 401) {\n  errorAnalysis = 'Authentication failed - invalid or expired token';\n  nextAction = 'Login again to get fresh token';\n} else if (data.status_code === 403) {\n  errorAnalysis = 'Permission denied - user may already have an organization';\n  nextAction = 'Check if user already belongs to an organization';\n} else if (data.status_code === 400) {\n  errorAnalysis = 'Invalid request data';\n  nextAction = 'Check validation errors and fix request data';\n} else if (data.status_code === 409) {\n  errorAnalysis = 'Organization name or slug already exists';\n  nextAction = 'Use different organization name';\n} else if (data.status_code === 500) {\n  errorAnalysis = 'Server error';\n  nextAction = 'Check server logs and try again';\n}\n\nreturn {\n  json: {\n    ...data,\n    error_analysis: errorAnalysis,\n    next_action: nextAction,\n    troubleshooting: {\n      common_issues: [\n        'Invalid or expired authentication token',\n        'User already has primary organization',\n        'Organization name already exists',\n        'Invalid request data format',\n        'Server error'\n      ],\n      validation_errors: data.errors\n    },\n    message: `Organization creation failed: ${data.error}`\n  }\n};"}, "id": "7g8h9i0j-k1l2-3456-ghij-789012345ghi", "name": "Organization Failed", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 400]}], "connections": {"Start": {"main": [[{"node": "Prepare Organization Data", "type": "main", "index": 0}]]}, "Prepare Organization Data": {"main": [[{"node": "Create Organization API Call", "type": "main", "index": 0}]]}, "Create Organization API Call": {"main": [[{"node": "Process Organization Response", "type": "main", "index": 0}]]}, "Process Organization Response": {"main": [[{"node": "Check Organization Success", "type": "main", "index": 0}]]}, "Check Organization Success": {"main": [[{"node": "Organization Success", "type": "main", "index": 0}], [{"node": "Organization Failed", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1.0.0", "meta": {"templateCredsSetupCompleted": true}, "id": "onboarding-organization-workflow", "tags": ["blog-api-v3", "onboarding", "organization"]}