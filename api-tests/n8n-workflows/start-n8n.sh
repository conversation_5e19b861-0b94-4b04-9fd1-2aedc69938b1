#!/bin/bash

# Blog API v3 - N8N Quick Start với Docker Compose
# Script nhanh để khởi động N8N

echo "🐳 Blog API v3 - N8N Quick Start"
echo "================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker chưa được cài đặt"
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null 2>&1; then
    echo "❌ Docker Compose chưa có sẵn"
    exit 1
fi

# Use docker compose (new) or docker-compose (legacy)
if docker compose version &> /dev/null 2>&1; then
    COMPOSE_CMD="docker compose"
else
    COMPOSE_CMD="docker-compose"
fi

echo "✅ Sử dụng: $COMPOSE_CMD"

# Check if API server is running
echo ""
echo "🔍 Kiểm tra API server..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:9077/health 2>/dev/null)

if [ "$response" = "200" ]; then
    echo "✅ API server đang chạy trên port 9077"
elif [ "$response" = "000" ]; then
    echo "⚠️  API server không chạy trên port 9077"
    echo "📝 Lưu ý: N8N sẽ sử dụng host.docker.internal:9077 để kết nối"
    echo "🔧 Hãy đảm bảo API server chạy trước khi test workflows"
else
    echo "⚠️  API server response: $response"
fi

# Start N8N with docker-compose
echo ""
echo "🚀 Khởi động N8N với Docker Compose..."

$COMPOSE_CMD up -d

if [ $? -eq 0 ]; then
    echo "✅ N8N đã khởi động thành công!"
    
    # Wait for N8N to be ready
    echo "⏳ Đang chờ N8N sẵn sàng..."
    for i in {1..30}; do
        if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
            echo "✅ N8N đã sẵn sàng!"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "⚠️  N8N có thể chưa sẵn sàng, kiểm tra logs: docker logs n8n-blogapi"
        fi
        sleep 2
    done
    
    echo ""
    echo "🎉 Setup hoàn tất!"
    echo "🌐 N8N UI: http://localhost:5678"
    echo "📂 Workflows: Available in /workflows inside container"
    echo "💾 Data: Persistent in n8n_data volume"
    
    echo ""
    echo "📋 Workflows có sẵn để import:"
    echo "1. 01-register-user.json"
    echo "2. 02-login-user.json"
    echo "3. 03-onboarding-organization.json"
    echo "4. 04-full-flow.json (🚀 Recommended)"
    
    echo ""
    echo "🔧 Useful commands:"
    echo "- Xem logs: docker logs n8n-blogapi"
    echo "- Dừng: $COMPOSE_CMD down"
    echo "- Khởi động lại: $COMPOSE_CMD restart"
    echo "- Xóa hết: $COMPOSE_CMD down -v"
    
    # Optional: Open browser
    echo ""
    read -p "🌐 Mở N8N UI trong browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if command -v open &> /dev/null; then
            open http://localhost:5678
        elif command -v xdg-open &> /dev/null; then
            xdg-open http://localhost:5678
        else
            echo "🌐 Mở: http://localhost:5678"
        fi
    fi
    
else
    echo "❌ Lỗi khởi động N8N"
    echo "🔍 Kiểm tra logs: $COMPOSE_CMD logs"
fi