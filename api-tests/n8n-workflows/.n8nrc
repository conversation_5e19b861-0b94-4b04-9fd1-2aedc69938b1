# N8N Configuration for Blog API v3 Testing

# Basic settings
N8N_BASIC_AUTH_ACTIVE=false
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http

# Database (optional - uses SQLite by default)
# N8N_DATABASE_TYPE=sqlite
# N8N_DATABASE_SQLITE_FILE=~/.n8n/database.sqlite

# Security
N8N_SECURITY_AUDIT_ON_POST_LOGIN=false

# Execution
N8N_EXECUTION_DATA_SAVE_ON_ERROR=all
N8N_EXECUTION_DATA_SAVE_ON_SUCCESS=all
N8N_EXECUTION_DATA_SAVE_ON_MANUAL_EXECUTION=true

# Logging
N8N_LOG_LEVEL=info
N8N_LOG_OUTPUT=console

# Workflow settings
N8N_WORKFLOW_EDITOR_SAVE_ON_EXECUTION=true
N8N_WORKFLOW_EDITOR_SAVE_ON_CHANGE=true

# HTTP settings for API calls
N8N_DEFAULT_BINARY_DATA_MODE=filesystem
N8N_BINARY_DATA_TTL=1440

# Timezone
GENERIC_TIMEZONE=Asia/Ho_Chi_Minh

# Custom variables for Blog API v3
BLOG_API_BASE_URL=http://localhost:9077
BLOG_API_VERSION=v1
BLOG_API_PREFIX=/api/cms/v1