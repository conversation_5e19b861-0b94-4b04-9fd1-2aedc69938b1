{"name": "Blog API v3 - Login User", "nodes": [{"parameters": {}, "id": "1a2b3c4d-e5f6-7890-abcd-123456789abc", "name": "Start", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Test credentials - you can modify these or use data from previous workflow\nconst testCredentials = {\n  // Option 1: Use specific test user\n  email: \"<EMAIL>\",\n  password: \"Test123!@#\",\n  \n  // Option 2: Use data from previous workflow if available\n  // email: $('Previous Workflow')?.first()?.json?.email || \"<EMAIL>\",\n  // password: $('Previous Workflow')?.first()?.json?.password || \"Test123!@#\"\n};\n\n// Or generate new test user for demo\nconst timestamp = Date.now();\nconst demoCredentials = {\n  email: `demo${timestamp}@example.com`,\n  password: \"Demo123!@#\"\n};\n\nconsole.log('Login credentials:', testCredentials);\n\nreturn {\n  json: {\n    credentials: testCredentials,\n    demo_credentials: demoCredentials,\n    timestamp\n  }\n};"}, "id": "2b3c4d5e-f6g7-8901-bcde-234567890bcd", "name": "Prepare Login Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "http://host.docker.internal:9077/api/cms/v1/auth/login", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "email", "value": "={{ $json.credentials.email }}"}, {"name": "password", "value": "={{ $json.credentials.password }}"}]}, "options": {}}, "id": "3c4d5e6f-g7h8-9012-cdef-345678901cde", "name": "Login API Call", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [680, 300]}, {"parameters": {"jsCode": "// Process login response\nconst response = $input.all()[0].json;\nconst credentials = $('Prepare Login Data').first().json.credentials;\n\nconsole.log('Login response status:', response.status);\n\nif (response.status === 'success') {\n  const tokens = response.data;\n  return {\n    json: {\n      success: true,\n      message: 'Login successful',\n      access_token: tokens.access_token,\n      refresh_token: tokens.refresh_token,\n      token_type: tokens.token_type || 'Bearer',\n      expires_in: tokens.expires_in,\n      user: tokens.user || {},\n      email: credentials.email,\n      has_tenant: tokens.user?.tenant_id !== null,\n      tenant_id: tokens.user?.tenant_id,\n      full_response: response\n    }\n  };\n} else {\n  return {\n    json: {\n      success: false,\n      error: response.message || 'Login failed',\n      errors: response.errors || {},\n      email: credentials.email,\n      status_code: response.status_code,\n      full_response: response\n    }\n  };\n}"}, "id": "4d5e6f7g-h8i9-0123-defg-456789012def", "name": "Process Login Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "abc123", "leftValue": "={{ $json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "5e6f7g8h-i9j0-1234-efgh-567890123efg", "name": "Check Login Success", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"jsCode": "// Login successful - prepare for next steps\nconst data = $input.all()[0].json;\n\nconsole.log('✅ Login successful!');\nconsole.log('Access Token:', data.access_token?.substring(0, 20) + '...');\nconsole.log('User has tenant:', data.has_tenant);\nconsole.log('Tenant ID:', data.tenant_id);\n\nreturn {\n  json: {\n    ...data,\n    next_step: data.has_tenant ? 'api_calls_with_tenant' : 'onboarding_create_organization',\n    auth_header: `${data.token_type} ${data.access_token}`,\n    message: `Login successful. ${data.has_tenant ? 'User has tenant, ready for API calls.' : 'User needs to create organization via onboarding.'}`\n  }\n};"}, "id": "6f7g8h9i-j0k1-2345-fghi-678901234fgh", "name": "Login Success", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"jsCode": "// <PERSON><PERSON> failed - analyze error\nconst data = $input.all()[0].json;\n\nconsole.log('❌ Login failed!');\nconsole.log('Error:', data.error);\nconsole.log('Status Code:', data.status_code);\n\n// Analyze common error scenarios\nlet errorAnalysis = 'Unknown error';\nlet nextAction = 'Check credentials';\n\nif (data.status_code === 403) {\n  errorAnalysis = 'Email not verified or account suspended';\n  nextAction = 'Verify email or check account status';\n} else if (data.status_code === 401) {\n  errorAnalysis = 'Invalid credentials';\n  nextAction = 'Check email and password';\n} else if (data.status_code === 404) {\n  errorAnalysis = 'User not found';\n  nextAction = 'Register new account';\n} else if (data.status_code === 429) {\n  errorAnalysis = 'Too many login attempts';\n  nextAction = 'Wait before retrying';\n}\n\nreturn {\n  json: {\n    ...data,\n    error_analysis: errorAnalysis,\n    next_action: nextAction,\n    troubleshooting: {\n      common_issues: [\n        'Email not verified',\n        'Invalid password',\n        'Account not found',\n        'Account suspended',\n        'Rate limit exceeded'\n      ]\n    }\n  }\n};"}, "id": "7g8h9i0j-k1l2-3456-ghij-789012345ghi", "name": "Login Failed", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 400]}], "connections": {"Start": {"main": [[{"node": "Prepare Login Data", "type": "main", "index": 0}]]}, "Prepare Login Data": {"main": [[{"node": "Login API Call", "type": "main", "index": 0}]]}, "Login API Call": {"main": [[{"node": "Process Login Response", "type": "main", "index": 0}]]}, "Process Login Response": {"main": [[{"node": "Check Login Success", "type": "main", "index": 0}]]}, "Check Login Success": {"main": [[{"node": "Login Success", "type": "main", "index": 0}], [{"node": "Login Failed", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1.0.0", "meta": {"templateCredsSetupCompleted": true}, "id": "login-user-workflow", "tags": ["blog-api-v3", "auth", "login"]}