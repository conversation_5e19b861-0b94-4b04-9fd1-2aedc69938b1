# Blog API v3 - N8N Workflows Makefile
# Các command tiện ích để quản lý N8N testing

# Colors for output
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

.PHONY: help start stop restart logs clean status setup import-workflows

help: ## Show this help message
	@echo "$(GREEN)Blog API v3 - N8N Workflows$(NC)"
	@echo "============================="
	@echo ""
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

start: ## Start N8N container
	@echo "$(GREEN)🚀 Starting N8N container...$(NC)"
	@docker-compose up -d
	@echo "$(GREEN)✅ N8N started successfully!$(NC)"
	@echo "$(YELLOW)🌐 N8N UI: http://localhost:5678$(NC)"

stop: ## Stop N8N container
	@echo "$(YELLOW)🛑 Stopping N8N container...$(NC)"
	@docker-compose down
	@echo "$(GREEN)✅ N8N stopped successfully!$(NC)"

restart: ## Restart N8N container
	@echo "$(YELLOW)🔄 Restarting N8N container...$(NC)"
	@docker-compose restart
	@echo "$(GREEN)✅ N8N restarted successfully!$(NC)"

logs: ## Show N8N container logs
	@echo "$(GREEN)📋 N8N Container Logs:$(NC)"
	@docker-compose logs -f n8n

status: ## Check N8N container status
	@echo "$(GREEN)📊 N8N Container Status:$(NC)"
	@docker-compose ps
	@echo ""
	@echo "$(GREEN)📊 N8N Health Check:$(NC)"
	@curl -s http://localhost:5678/healthz > /dev/null 2>&1 && echo "$(GREEN)✅ N8N is healthy$(NC)" || echo "$(RED)❌ N8N is not responding$(NC)"

clean: ## Stop and remove N8N container and volumes
	@echo "$(RED)🧹 Cleaning up N8N container and volumes...$(NC)"
	@docker-compose down -v
	@docker volume prune -f
	@echo "$(GREEN)✅ Cleanup completed!$(NC)"

setup: ## Full setup - start container and open UI
	@echo "$(GREEN)🔧 Setting up N8N for Blog API v3 testing...$(NC)"
	@./start-n8n.sh

quick-start: ## Quick start - just start container
	@echo "$(GREEN)⚡ Quick starting N8N...$(NC)"
	@make start
	@sleep 5
	@echo "$(GREEN)🎉 Ready to test!$(NC)"
	@echo "$(YELLOW)Next steps:$(NC)"
	@echo "1. Open: http://localhost:5678"
	@echo "2. Import workflows from this directory"
	@echo "3. Run '04-full-flow.json' to test complete flow"

check-api: ## Check if Blog API server is running
	@echo "$(GREEN)🔍 Checking Blog API server...$(NC)"
	@curl -s -o /dev/null -w "%{http_code}" http://localhost:9077/health 2>/dev/null | grep -q "200" && \
		echo "$(GREEN)✅ API server is running on port 9077$(NC)" || \
		echo "$(RED)❌ API server is not running on port 9077$(NC)"

test-connection: ## Test connection from N8N to API server
	@echo "$(GREEN)🔗 Testing N8N to API server connection...$(NC)"
	@docker-compose exec n8n curl -s -o /dev/null -w "%{http_code}" http://host.docker.internal:9077/health 2>/dev/null | grep -q "200" && \
		echo "$(GREEN)✅ N8N can connect to API server$(NC)" || \
		echo "$(RED)❌ N8N cannot connect to API server$(NC)"

workflows: ## List available workflows
	@echo "$(GREEN)📋 Available Workflows:$(NC)"
	@echo "1. $(YELLOW)01-register-user.json$(NC) - User registration test"
	@echo "2. $(YELLOW)02-login-user.json$(NC) - User login test"
	@echo "3. $(YELLOW)03-onboarding-organization.json$(NC) - Organization creation test"
	@echo "4. $(YELLOW)04-full-flow.json$(NC) - 🚀 Complete flow test (Recommended)"

dev: ## Start development environment (API + N8N)
	@echo "$(GREEN)🚀 Starting development environment...$(NC)"
	@echo "$(YELLOW)📝 Make sure to run 'go run cmd/server/main.go' in another terminal$(NC)"
	@make start
	@sleep 5
	@make check-api
	@echo "$(GREEN)🎉 Development environment ready!$(NC)"

# Shortcuts
up: start
down: stop
ps: status