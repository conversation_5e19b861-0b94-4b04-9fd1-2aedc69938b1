version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n-blogapi
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      # Basic settings
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - N8N_BASIC_AUTH_ACTIVE=false
      
      # Execution settings
      - N8N_EXECUTION_DATA_SAVE_ON_ERROR=all
      - N8N_EXECUTION_DATA_SAVE_ON_SUCCESS=all
      - N8N_EXECUTION_DATA_SAVE_ON_MANUAL_EXECUTION=true
      
      # Logging
      - N8N_LOG_LEVEL=info
      - N8N_LOG_OUTPUT=console
      
      # Timezone
      - GENERIC_TIMEZONE=Asia/Ho_Chi_Minh
      
      # Workflow editor
      - N8N_WORKFLOW_EDITOR_SAVE_ON_EXECUTION=true
      - N8N_WORKFLOW_EDITOR_SAVE_ON_CHANGE=true
      
      # Custom variables for Blog API v3
      - BLOG_API_BASE_URL=http://host.docker.internal:9077
      - BLOG_API_VERSION=v1
      - BLOG_API_PREFIX=/api/cms/v1
      
      # Security
      - N8N_SECURITY_AUDIT_ON_POST_LOGIN=false
      
      # Binary data
      - N8N_DEFAULT_BINARY_DATA_MODE=filesystem
      - N8N_BINARY_DATA_TTL=1440
      
    volumes:
      # Persistent data
      - n8n_data:/home/<USER>/.n8n
      
      # Workflows directory (for easy import)
      - ./:/workflows:ro
      
    networks:
      - n8n-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

volumes:
  n8n_data:
    driver: local
    
networks:
  n8n-network:
    driver: bridge