# MinIO Configuration Example
# Copy this file to minio.yaml and update with your MinIO server details

storage:
  providers:
    minio:
      # Provider type
      provider: "minio"
      
      # MinIO server endpoint (without http/https prefix)
      endpoint: "localhost:9000"
      
      # Bucket name for media files
      bucket: "blog-media"
      
      # Access credentials
      access_key: "minioadmin"
      secret_key: "minioadmin"
      
      # SSL settings
      use_ssl: false
      insecure_skip_verify: false
      
      # Optional: base path for all files
      base_path: "uploads"
      
      # Optional: custom public URL (for load balancer or custom domain)
      public_url: "http://localhost:9000/blog-media"
      
      # Optional: CDN endpoint for optimized delivery
      cdn_endpoint: ""
      
      # Optional: region (for AWS S3 compatibility)
      region: "us-east-1"
      
      # Default settings
      default_acl: "private"
      default_storage_class: "STANDARD"
      default_cache_control: "max-age=31536000"
      
      # Upload settings
      max_file_size: 104857600  # 100MB in bytes
      allowed_types:
        - "image/jpeg"
        - "image/png" 
        - "image/gif"
        - "image/webp"
        - "application/pdf"
        - "text/plain"
        - "video/mp4"
        - "video/webm"
      
      # Performance settings
      part_size: 5242880        # 5MB for multipart uploads
      max_concurrency: 10
      timeout: "30s"
      retry_count: 3
      retry_delay: "1s"
      
      # Image processing
      enable_image_processing: true
      image_processor: "native"
      thumbnail_sizes:
        - name: "thumb"
          width: 150
          height: 150
          crop: true
        - name: "medium"
          width: 500
          height: 500
          crop: false
        - name: "large"
          width: 1000
          height: 1000
          crop: false
      
      # Advanced settings
      enable_cleanup: true
      cleanup_interval: "24h"
      max_age: "90d"
      enable_metrics: true
      enable_compression: false
      enable_caching: true
      cache_size: 524288000     # 500MB

  # Default storage provider
  default_provider: "minio"

# Multi-tenant settings
multi_tenant:
  # Path structure: tenant-{tenant_id}/website-{website_id}/{file_path}
  enable_tenant_isolation: true
  enable_website_isolation: true
  
  # Bucket strategy: "shared" (single bucket with prefixes) or "separate" (bucket per tenant)
  bucket_strategy: "shared"
  
  # Optional: custom bucket naming for separate strategy
  # bucket_template: "tenant-{tenant_id}-media"

# Media processing settings
media:
  # Enable automatic thumbnail generation
  auto_thumbnails: true
  
  # Enable image optimization
  auto_optimize: true
  
  # Enable virus scanning (requires ClamAV or similar)
  virus_scanning: false
  
  # Enable watermarking
  watermark:
    enabled: false
    text: "© Your Company"
    position: "bottom-right"
    opacity: 0.5
    
# Security settings
security:
  # Require authentication for uploads
  require_auth: true
  
  # Allow anonymous downloads (for public files)
  allow_anonymous_downloads: true
  
  # Rate limiting
  rate_limits:
    uploads_per_minute: 10
    uploads_per_hour: 100
    uploads_per_day: 1000
    bytes_per_minute: 52428800   # 50MB
    bytes_per_hour: 1073741824   # 1GB
    bytes_per_day: 10737418240   # 10GB

# Logging settings
logging:
  level: "info"
  enable_access_logs: true
  enable_error_logs: true
  log_file_uploads: true
  log_file_downloads: false