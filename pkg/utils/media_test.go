package utils

import (
	"testing"
)

func TestGetMediaType(t *testing.T) {
	tests := []struct {
		filename string
		expected MediaType
	}{
		{"image.jpg", MediaTypeImage},
		{"video.mp4", MediaTypeVideo},
		{"audio.mp3", MediaTypeAudio},
		{"document.pdf", MediaTypeDocument},
		{"unknown.xyz", MediaTypeUnknown},
	}

	for _, test := range tests {
		result := GetMediaType(test.filename)
		if result != test.expected {
			t.Erro<PERSON>("GetMediaType(%s) = %v, expected %v", test.filename, result, test.expected)
		}
	}
}

func TestGetMimeType(t *testing.T) {
	tests := []struct {
		filename string
		expected string
	}{
		{"image.jpg", "image/jpeg"},
		{"video.mp4", "video/mp4"},
		{"audio.mp3", "audio/mpeg"},
		{"document.pdf", "application/pdf"},
		{"unknown.xyz", "application/octet-stream"},
	}

	for _, test := range tests {
		result := GetMimeType(test.filename)
		if result != test.expected {
			t.Errorf("GetMimeType(%s) = %v, expected %v", test.filename, result, test.expected)
		}
	}
}

func TestIsValidImageSize(t *testing.T) {
	tests := []struct {
		width, height int
		expected      bool
	}{
		{100, 100, true},
		{0, 100, false},
		{100, 0, false},
		{10001, 100, false},
		{100, 10001, false},
	}

	for _, test := range tests {
		result := IsValidImageSize(test.width, test.height)
		if result != test.expected {
			t.Errorf("IsValidImageSize(%d, %d) = %v, expected %v", test.width, test.height, result, test.expected)
		}
	}
}

func TestCalculateAspectRatio(t *testing.T) {
	tests := []struct {
		width, height int
		expected      string
	}{
		{100, 100, "1:1"},
		{400, 300, "4:3"},
		{1920, 1080, "16:9"},
		{0, 100, "unknown"},
		{100, 0, "unknown"},
	}

	for _, test := range tests {
		result := CalculateAspectRatio(test.width, test.height)
		if result != test.expected {
			t.Errorf("CalculateAspectRatio(%d, %d) = %v, expected %v", test.width, test.height, result, test.expected)
		}
	}
}

func TestSanitizeFilename(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"normal-file.jpg", "normal-file.jpg"},
		{"file with spaces.jpg", "file_with_spaces.jpg"},
		{"file/with\\path.jpg", "file_with_path.jpg"},
		{"file<>:\"|?*.jpg", "file.jpg"},
		{"__multiple__underscores__.jpg", "multiple_underscores.jpg"},
	}

	for _, test := range tests {
		result := SanitizeFilename(test.input)
		if result != test.expected {
			t.Errorf("SanitizeFilename(%s) = %v, expected %v", test.input, result, test.expected)
		}
	}
}

func TestValidateFileSize(t *testing.T) {
	tests := []struct {
		size      int64
		mediaType MediaType
		expected  bool
	}{
		{5 * 1024 * 1024, MediaTypeImage, true},   // 5MB image
		{15 * 1024 * 1024, MediaTypeImage, false}, // 15MB image (too large)
		{50 * 1024 * 1024, MediaTypeVideo, true},  // 50MB video
		{150 * 1024 * 1024, MediaTypeVideo, false}, // 150MB video (too large)
	}

	for _, test := range tests {
		result := ValidateFileSize(test.size, test.mediaType)
		if result != test.expected {
			t.Errorf("ValidateFileSize(%d, %v) = %v, expected %v", test.size, test.mediaType, result, test.expected)
		}
	}
}

func TestFormatFileSize(t *testing.T) {
	tests := []struct {
		size     int64
		expected string
	}{
		{512, "512 B"},
		{1024, "1.0 KB"},
		{1024 * 1024, "1.0 MB"},
		{1024 * 1024 * 1024, "1.0 GB"},
	}

	for _, test := range tests {
		result := FormatFileSize(test.size)
		if result != test.expected {
			t.Errorf("FormatFileSize(%d) = %v, expected %v", test.size, result, test.expected)
		}
	}
}

func TestGenerateImageVariantFilename(t *testing.T) {
	tests := []struct {
		original        string
		variant         string
		width, height   int
		expected        string
	}{
		{"image.jpg", "thumbnail", 150, 150, "image_thumbnail.jpg"},
		{"image.jpg", "", 300, 200, "image_300x200.jpg"},
		{"path/to/image.png", "small", 100, 100, "path/to/image_small.png"},
	}

	for _, test := range tests {
		result := GenerateImageVariantFilename(test.original, test.variant, test.width, test.height)
		if result != test.expected {
			t.Errorf("GenerateImageVariantFilename(%s, %s, %d, %d) = %v, expected %v", 
				test.original, test.variant, test.width, test.height, result, test.expected)
		}
	}
}

func TestGenerateWebPFilename(t *testing.T) {
	tests := []struct {
		original string
		expected string
	}{
		{"image.jpg", "image.webp"},
		{"photo.png", "photo.webp"},
		{"path/to/image.jpeg", "path/to/image.webp"},
	}

	for _, test := range tests {
		result := GenerateWebPFilename(test.original)
		if result != test.expected {
			t.Errorf("GenerateWebPFilename(%s) = %v, expected %v", test.original, result, test.expected)
		}
	}
}

func TestIsValidImageFormat(t *testing.T) {
	tests := []struct {
		format   string
		expected bool
	}{
		{"jpeg", true},
		{"jpg", true},
		{"png", true},
		{"webp", true},
		{"gif", true},
		{"bmp", true},
		{"tiff", true},
		{"xyz", false},
		{"", false},
	}

	for _, test := range tests {
		result := IsValidImageFormat(test.format)
		if result != test.expected {
			t.Errorf("IsValidImageFormat(%s) = %v, expected %v", test.format, result, test.expected)
		}
	}
}

func BenchmarkGetMediaType(b *testing.B) {
	filename := "test-image.jpg"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GetMediaType(filename)
	}
}

func BenchmarkSanitizeFilename(b *testing.B) {
	filename := "test file with spaces and special<>characters.jpg"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		SanitizeFilename(filename)
	}
}

func BenchmarkCalculateAspectRatio(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CalculateAspectRatio(1920, 1080)
	}
}