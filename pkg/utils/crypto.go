package utils

import (
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"strings"
)

// Hash utilities for cryptographic operations

// GenerateRandomString generates a random string of specified length
func GenerateRandomString(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes)[:length], nil
}

// GenerateRandomBytes generates random bytes
func GenerateRandomBytes(length int) ([]byte, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return nil, err
	}
	return bytes, nil
}

// MD5 generates MD5 hash
func MD5(data string) string {
	hash := md5.Sum([]byte(data))
	return hex.EncodeToString(hash[:])
}

// SHA1 generates SHA1 hash
func SHA1(data string) string {
	hash := sha1.Sum([]byte(data))
	return hex.EncodeToString(hash[:])
}

// SHA256 generates SHA256 hash
func SHA256(data string) string {
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// SHA512 generates SHA512 hash
func SHA512(data string) string {
	hash := sha512.Sum512([]byte(data))
	return hex.EncodeToString(hash[:])
}

// Base64Encode encodes data to base64
func Base64Encode(data []byte) string {
	return base64.StdEncoding.EncodeToString(data)
}

// Base64Decode decodes base64 data
func Base64Decode(data string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(data)
}

// Base64URLEncode encodes data to base64 URL safe
func Base64URLEncode(data []byte) string {
	return base64.URLEncoding.EncodeToString(data)
}

// Base64URLDecode decodes base64 URL safe data
func Base64URLDecode(data string) ([]byte, error) {
	return base64.URLEncoding.DecodeString(data)
}

// GenerateToken generates a secure random token
func GenerateToken(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// GenerateAPIKey generates a secure API key
func GenerateAPIKey() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// GenerateSecretKey generates a secure secret key
func GenerateSecretKey() (string, error) {
	bytes := make([]byte, 64)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// HashPassword creates a hash of a password (simple implementation)
// Note: In production, use bcrypt or similar
func HashPassword(password string) string {
	return SHA256(password)
}

// ValidatePasswordHash validates a password against a hash
func ValidatePasswordHash(password, hash string) bool {
	return SHA256(password) == hash
}

// GenerateChecksum generates a checksum for data
func GenerateChecksum(data []byte) string {
	return SHA256(string(data))
}

// GenerateFileChecksum generates a checksum for a file
func GenerateFileChecksum(reader io.Reader) (string, error) {
	hasher := sha256.New()
	if _, err := io.Copy(hasher, reader); err != nil {
		return "", err
	}
	return hex.EncodeToString(hasher.Sum(nil)), nil
}

// GenerateUUID generates a simple UUID-like string
func GenerateUUID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	
	// Set version (4) and variant bits
	bytes[6] = (bytes[6] & 0x0f) | 0x40
	bytes[8] = (bytes[8] & 0x3f) | 0x80
	
	return fmt.Sprintf("%x-%x-%x-%x-%x", bytes[0:4], bytes[4:6], bytes[6:8], bytes[8:10], bytes[10:16]), nil
}

// GenerateNonce generates a cryptographic nonce
func GenerateNonce() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// GenerateCSRFToken generates a CSRF token
func GenerateCSRFToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// GenerateSessionID generates a session ID
func GenerateSessionID() (string, error) {
	bytes := make([]byte, 24)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// MaskString masks sensitive string data
func MaskString(s string, maskChar rune, visibleChars int) string {
	runes := []rune(s)
	if len(runes) <= visibleChars*2 {
		return string([]rune{maskChar, maskChar, maskChar})
	}
	
	// Show visibleChars at beginning and end
	for i := visibleChars; i < len(runes)-visibleChars; i++ {
		runes[i] = maskChar
	}
	
	return string(runes)
}

// MaskEmail masks email address
func MaskEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return "***@***.***"
	}
	
	username := parts[0]
	domain := parts[1]
	
	if len(username) <= 2 {
		username = "***"
	} else {
		username = MaskString(username, '*', 1)
	}
	
	domainParts := strings.Split(domain, ".")
	if len(domainParts) >= 2 {
		domain = "***." + domainParts[len(domainParts)-1]
	}
	
	return username + "@" + domain
}

// MaskCreditCard masks credit card number
func MaskCreditCard(cardNumber string) string {
	// Remove spaces and dashes
	cardNumber = strings.ReplaceAll(cardNumber, " ", "")
	cardNumber = strings.ReplaceAll(cardNumber, "-", "")
	
	if len(cardNumber) < 4 {
		return "****-****-****-****"
	}
	
	return "****-****-****-" + cardNumber[len(cardNumber)-4:]
}

// MaskPhoneNumber masks phone number
func MaskPhoneNumber(phone string) string {
	// Remove common formatting
	phone = strings.ReplaceAll(phone, " ", "")
	phone = strings.ReplaceAll(phone, "-", "")
	phone = strings.ReplaceAll(phone, "(", "")
	phone = strings.ReplaceAll(phone, ")", "")
	
	if len(phone) < 4 {
		return "***-***-****"
	}
	
	return "***-***-" + phone[len(phone)-4:]
}