package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
)

// JSON utilities for JSON operations

// ToJSON converts an object to JSON string
func ToJSON(obj interface{}) (string, error) {
	bytes, err := json.Marshal(obj)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// ToJSONPretty converts an object to pretty JSON string
func ToJSONPretty(obj interface{}) (string, error) {
	bytes, err := json.MarshalIndent(obj, "", "  ")
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// FromJSON parses JSON string to object
func FromJSON(jsonStr string, obj interface{}) error {
	return json.Unmarshal([]byte(jsonStr), obj)
}

// ToJSONBytes converts an object to JSON bytes
func ToJSONBytes(obj interface{}) ([]byte, error) {
	return json.Marshal(obj)
}

// FromJSONBytes parses JSON bytes to object
func FromJSONBytes(jsonBytes []byte, obj interface{}) error {
	return json.Unmarshal(jsonBytes, obj)
}

// IsValidJSON checks if a string is valid JSON
func IsValidJSON(jsonStr string) bool {
	var js json.RawMessage
	return json.Unmarshal([]byte(jsonStr), &js) == nil
}

// IsValidJSONBytes checks if bytes are valid JSON
func IsValidJSONBytes(jsonBytes []byte) bool {
	var js json.RawMessage
	return json.Unmarshal(jsonBytes, &js) == nil
}

// JSONEqual compares two JSON strings for equality
func JSONEqual(json1, json2 string) bool {
	var obj1, obj2 interface{}
	
	if err := json.Unmarshal([]byte(json1), &obj1); err != nil {
		return false
	}
	
	if err := json.Unmarshal([]byte(json2), &obj2); err != nil {
		return false
	}
	
	return reflect.DeepEqual(obj1, obj2)
}

// JSONMerge merges two JSON objects
func JSONMerge(json1, json2 string) (string, error) {
	var obj1, obj2 map[string]interface{}
	
	if err := json.Unmarshal([]byte(json1), &obj1); err != nil {
		return "", err
	}
	
	if err := json.Unmarshal([]byte(json2), &obj2); err != nil {
		return "", err
	}
	
	// Merge obj2 into obj1
	for key, value := range obj2 {
		obj1[key] = value
	}
	
	return ToJSON(obj1)
}

// JSONPath extracts value from JSON using simple path notation
func JSONPath(jsonStr, path string) (interface{}, error) {
	var obj interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return nil, err
	}
	
	return getValueByPath(obj, path)
}

// getValueByPath navigates through nested objects using dot notation
func getValueByPath(obj interface{}, path string) (interface{}, error) {
	if path == "" {
		return obj, nil
	}
	
	parts := strings.Split(path, ".")
	current := obj
	
	for _, part := range parts {
		switch v := current.(type) {
		case map[string]interface{}:
			if val, exists := v[part]; exists {
				current = val
			} else {
				return nil, fmt.Errorf("key '%s' not found", part)
			}
		case []interface{}:
			// Handle array index
			if idx, err := parseArrayIndex(part); err == nil {
				if idx < len(v) {
					current = v[idx]
				} else {
					return nil, fmt.Errorf("index %d out of bounds", idx)
				}
			} else {
				return nil, fmt.Errorf("invalid array index: %s", part)
			}
		default:
			return nil, fmt.Errorf("cannot navigate to '%s' in non-object type", part)
		}
	}
	
	return current, nil
}

// parseArrayIndex parses array index from string
func parseArrayIndex(s string) (int, error) {
	if len(s) < 3 || s[0] != '[' || s[len(s)-1] != ']' {
		return 0, fmt.Errorf("invalid array index format")
	}
	
	indexStr := s[1 : len(s)-1]
	var index int
	if _, err := fmt.Sscanf(indexStr, "%d", &index); err != nil {
		return 0, err
	}
	
	return index, nil
}

// JSONKeys extracts all keys from a JSON object
func JSONKeys(jsonStr string) ([]string, error) {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return nil, err
	}
	
	keys := make([]string, 0, len(obj))
	for key := range obj {
		keys = append(keys, key)
	}
	
	return keys, nil
}

// JSONValues extracts all values from a JSON object
func JSONValues(jsonStr string) ([]interface{}, error) {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return nil, err
	}
	
	values := make([]interface{}, 0, len(obj))
	for _, value := range obj {
		values = append(values, value)
	}
	
	return values, nil
}

// JSONSize returns the number of top-level keys in a JSON object
func JSONSize(jsonStr string) (int, error) {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return 0, err
	}
	
	return len(obj), nil
}

// JSONHasKey checks if a JSON object has a specific key
func JSONHasKey(jsonStr, key string) (bool, error) {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return false, err
	}
	
	_, exists := obj[key]
	return exists, nil
}

// JSONGet gets a value from JSON object by key
func JSONGet(jsonStr, key string) (interface{}, error) {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return nil, err
	}
	
	if value, exists := obj[key]; exists {
		return value, nil
	}
	
	return nil, fmt.Errorf("key '%s' not found", key)
}

// JSONSet sets a value in JSON object
func JSONSet(jsonStr, key string, value interface{}) (string, error) {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return "", err
	}
	
	obj[key] = value
	return ToJSON(obj)
}

// JSONDelete deletes a key from JSON object
func JSONDelete(jsonStr, key string) (string, error) {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return "", err
	}
	
	delete(obj, key)
	return ToJSON(obj)
}

// JSONFilter filters JSON object by keys
func JSONFilter(jsonStr string, keys []string) (string, error) {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return "", err
	}
	
	filtered := make(map[string]interface{})
	for _, key := range keys {
		if value, exists := obj[key]; exists {
			filtered[key] = value
		}
	}
	
	return ToJSON(filtered)
}

// JSONOmit omits specified keys from JSON object
func JSONOmit(jsonStr string, keys []string) (string, error) {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return "", err
	}
	
	result := make(map[string]interface{})
	for key, value := range obj {
		omit := false
		for _, omitKey := range keys {
			if key == omitKey {
				omit = true
				break
			}
		}
		if !omit {
			result[key] = value
		}
	}
	
	return ToJSON(result)
}

// JSONFlatten flattens nested JSON object
func JSONFlatten(jsonStr string) (string, error) {
	var obj interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return "", err
	}
	
	flattened := make(map[string]interface{})
	flatten(obj, "", flattened)
	
	return ToJSON(flattened)
}

// flatten recursively flattens nested objects
func flatten(obj interface{}, prefix string, result map[string]interface{}) {
	switch v := obj.(type) {
	case map[string]interface{}:
		for key, value := range v {
			newKey := key
			if prefix != "" {
				newKey = prefix + "." + key
			}
			flatten(value, newKey, result)
		}
	case []interface{}:
		for i, value := range v {
			newKey := fmt.Sprintf("%s[%d]", prefix, i)
			flatten(value, newKey, result)
		}
	default:
		result[prefix] = v
	}
}

// JSONMinify removes whitespace from JSON
func JSONMinify(jsonStr string) (string, error) {
	var obj interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return "", err
	}
	
	return ToJSON(obj)
}

// JSONValidate validates JSON schema (basic implementation)
func JSONValidate(jsonStr string, schema map[string]interface{}) error {
	var obj map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
		return err
	}
	
	// Basic validation - check required fields
	if required, exists := schema["required"]; exists {
		if requiredFields, ok := required.([]string); ok {
			for _, field := range requiredFields {
				if _, exists := obj[field]; !exists {
					return fmt.Errorf("required field '%s' is missing", field)
				}
			}
		}
	}
	
	return nil
}

// JSONEncode encodes object to JSON with custom encoding
func JSONEncode(obj interface{}, options ...func(*json.Encoder)) ([]byte, error) {
	var buf strings.Builder
	encoder := json.NewEncoder(&buf)
	
	// Apply options
	for _, option := range options {
		option(encoder)
	}
	
	if err := encoder.Encode(obj); err != nil {
		return nil, err
	}
	
	return []byte(strings.TrimSpace(buf.String())), nil
}

// JSONDecode decodes JSON with custom decoding
func JSONDecode(data []byte, obj interface{}, options ...func(*json.Decoder)) error {
	decoder := json.NewDecoder(strings.NewReader(string(data)))
	
	// Apply options
	for _, option := range options {
		option(decoder)
	}
	
	return decoder.Decode(obj)
}

// Common JSON encoder options
func JSONEscapeHTML(escape bool) func(*json.Encoder) {
	return func(encoder *json.Encoder) {
		encoder.SetEscapeHTML(escape)
	}
}

func JSONIndent(prefix, indent string) func(*json.Encoder) {
	return func(encoder *json.Encoder) {
		encoder.SetIndent(prefix, indent)
	}
}

// Common JSON decoder options
func JSONDisallowUnknownFields() func(*json.Decoder) {
	return func(decoder *json.Decoder) {
		decoder.DisallowUnknownFields()
	}
}

func JSONUseNumber() func(*json.Decoder) {
	return func(decoder *json.Decoder) {
		decoder.UseNumber()
	}
}