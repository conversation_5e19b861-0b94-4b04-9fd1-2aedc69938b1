package utils

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// File utilities for file operations

// FileExists checks if a file exists
func FileExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// DirExists checks if a directory exists
func DirExists(path string) bool {
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		return false
	}
	return info.IsDir()
}

// CreateDir creates a directory if it doesn't exist
func CreateDir(path string) error {
	if DirExists(path) {
		return nil
	}
	return os.MkdirAll(path, 0755)
}

// CreateFile creates a file with the given content
func CreateFile(path, content string) error {
	// Create directory if it doesn't exist
	dir := filepath.Dir(path)
	if err := CreateDir(dir); err != nil {
		return err
	}
	
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	defer file.Close()
	
	_, err = file.WriteString(content)
	return err
}

// ReadFile reads content from a file
func ReadFile(path string) (string, error) {
	content, err := os.ReadFile(path)
	if err != nil {
		return "", err
	}
	return string(content), nil
}

// WriteFile writes content to a file
func WriteFile(path, content string) error {
	return os.WriteFile(path, []byte(content), 0644)
}

// AppendFile appends content to a file
func AppendFile(path, content string) error {
	file, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer file.Close()
	
	_, err = file.WriteString(content)
	return err
}

// CopyFile copies a file from source to destination
func CopyFile(src, dst string) error {
	// Create destination directory if it doesn't exist
	dir := filepath.Dir(dst)
	if err := CreateDir(dir); err != nil {
		return err
	}
	
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()
	
	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()
	
	_, err = io.Copy(destFile, sourceFile)
	return err
}

// MoveFile moves a file from source to destination
func MoveFile(src, dst string) error {
	// Create destination directory if it doesn't exist
	dir := filepath.Dir(dst)
	if err := CreateDir(dir); err != nil {
		return err
	}
	
	return os.Rename(src, dst)
}

// DeleteFile deletes a file
func DeleteFile(path string) error {
	return os.Remove(path)
}

// DeleteDir deletes a directory and all its contents
func DeleteDir(path string) error {
	return os.RemoveAll(path)
}

// GetFileSize returns the size of a file in bytes
func GetFileSize(path string) (int64, error) {
	info, err := os.Stat(path)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// GetFileInfo returns information about a file
func GetFileInfo(path string) (os.FileInfo, error) {
	return os.Stat(path)
}

// GetFileExtension returns the file extension
func GetFileExtension(path string) string {
	return filepath.Ext(path)
}

// GetFileName returns the file name without extension
func GetFileName(path string) string {
	base := filepath.Base(path)
	ext := filepath.Ext(base)
	return strings.TrimSuffix(base, ext)
}

// GetFileNameWithExt returns the file name with extension
func GetFileNameWithExt(path string) string {
	return filepath.Base(path)
}

// GetDirName returns the directory name
func GetDirName(path string) string {
	return filepath.Dir(path)
}

// GetAbsolutePath returns the absolute path
func GetAbsolutePath(path string) (string, error) {
	return filepath.Abs(path)
}

// IsDirectory checks if a path is a directory
func IsDirectory(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return info.IsDir()
}

// IsFile checks if a path is a file
func IsFile(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return !info.IsDir()
}

// ListFiles lists all files in a directory
func ListFiles(dir string) ([]string, error) {
	var files []string
	
	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}
	
	for _, entry := range entries {
		if !entry.IsDir() {
			files = append(files, entry.Name())
		}
	}
	
	return files, nil
}

// ListDirs lists all directories in a directory
func ListDirs(dir string) ([]string, error) {
	var dirs []string
	
	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}
	
	for _, entry := range entries {
		if entry.IsDir() {
			dirs = append(dirs, entry.Name())
		}
	}
	
	return dirs, nil
}

// ListAll lists all files and directories
func ListAll(dir string) ([]string, error) {
	var items []string
	
	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}
	
	for _, entry := range entries {
		items = append(items, entry.Name())
	}
	
	return items, nil
}

// WalkDir walks a directory tree and calls a function for each file
func WalkDir(root string, fn func(path string, info os.FileInfo, err error) error) error {
	return filepath.Walk(root, fn)
}

// FindFiles finds files matching a pattern
func FindFiles(root, pattern string) ([]string, error) {
	var matches []string
	
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if !info.IsDir() {
			matched, err := filepath.Match(pattern, filepath.Base(path))
			if err != nil {
				return err
			}
			if matched {
				matches = append(matches, path)
			}
		}
		
		return nil
	})
	
	return matches, err
}

// ReadLines reads a file line by line
func ReadLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	
	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	
	return lines, scanner.Err()
}

// WriteLines writes lines to a file
func WriteLines(path string, lines []string) error {
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	defer file.Close()
	
	writer := bufio.NewWriter(file)
	for _, line := range lines {
		_, err := writer.WriteString(line + "\n")
		if err != nil {
			return err
		}
	}
	
	return writer.Flush()
}

// CountLines counts the number of lines in a file
func CountLines(path string) (int, error) {
	file, err := os.Open(path)
	if err != nil {
		return 0, err
	}
	defer file.Close()
	
	count := 0
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		count++
	}
	
	return count, scanner.Err()
}

// EnsureDir ensures a directory exists, creating it if necessary
func EnsureDir(path string) error {
	return CreateDir(path)
}

// TempFile creates a temporary file
func TempFile(dir, pattern string) (*os.File, error) {
	return os.CreateTemp(dir, pattern)
}

// TempDir creates a temporary directory
func TempDir(dir, pattern string) (string, error) {
	return os.MkdirTemp(dir, pattern)
}

// GetWorkingDir returns the current working directory
func GetWorkingDir() (string, error) {
	return os.Getwd()
}

// ChangeDir changes the current working directory
func ChangeDir(dir string) error {
	return os.Chdir(dir)
}

// GetHomeDir returns the user's home directory
func GetHomeDir() (string, error) {
	return os.UserHomeDir()
}

// JoinPath joins path elements
func JoinPath(elements ...string) string {
	return filepath.Join(elements...)
}

// CleanPath cleans a path
func CleanPath(path string) string {
	return filepath.Clean(path)
}

// RelativePath returns a relative path
func RelativePath(basepath, targpath string) (string, error) {
	return filepath.Rel(basepath, targpath)
}

// IsValidPath checks if a path is valid
func IsValidPath(path string) bool {
	// Check for invalid characters
	invalidChars := []string{"\x00", "<", ">", ":", "\"", "|", "?", "*"}
	for _, char := range invalidChars {
		if strings.Contains(path, char) {
			return false
		}
	}
	
	// Check path length (Windows limitation)
	if len(path) > 260 {
		return false
	}
	
	return true
}

// SanitizePath sanitizes a path for safe usage
func SanitizePath(path string) string {
	// Remove or replace invalid characters
	path = strings.ReplaceAll(path, "\x00", "")
	path = strings.ReplaceAll(path, "<", "_")
	path = strings.ReplaceAll(path, ">", "_")
	path = strings.ReplaceAll(path, ":", "_")
	path = strings.ReplaceAll(path, "\"", "_")
	path = strings.ReplaceAll(path, "|", "_")
	path = strings.ReplaceAll(path, "?", "_")
	path = strings.ReplaceAll(path, "*", "_")
	
	// Clean the path
	path = filepath.Clean(path)
	
	return path
}

// GetFilePermissions returns file permissions as string
func GetFilePermissions(path string) (string, error) {
	info, err := os.Stat(path)
	if err != nil {
		return "", err
	}
	return info.Mode().String(), nil
}

// SetFilePermissions sets file permissions
func SetFilePermissions(path string, perm os.FileMode) error {
	return os.Chmod(path, perm)
}

// IsHidden checks if a file is hidden (starts with .)
func IsHidden(path string) bool {
	name := filepath.Base(path)
	return strings.HasPrefix(name, ".")
}

// IsExecutable checks if a file is executable
func IsExecutable(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return info.Mode()&0111 != 0
}

// IsReadable checks if a file is readable
func IsReadable(path string) bool {
	file, err := os.Open(path)
	if err != nil {
		return false
	}
	file.Close()
	return true
}

// IsWritable checks if a file is writable
func IsWritable(path string) bool {
	file, err := os.OpenFile(path, os.O_WRONLY, 0666)
	if err != nil {
		return false
	}
	file.Close()
	return true
}

// BackupFile creates a backup of a file
func BackupFile(path string) error {
	backupPath := path + ".backup"
	return CopyFile(path, backupPath)
}

// RestoreFile restores a file from backup
func RestoreFile(path string) error {
	backupPath := path + ".backup"
	if !FileExists(backupPath) {
		return fmt.Errorf("backup file does not exist: %s", backupPath)
	}
	return CopyFile(backupPath, path)
}