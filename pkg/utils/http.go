package utils

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// HTTP utilities for HTTP operations

// HTTPClient represents an HTTP client with utilities
type HTTPClient struct {
	client  *http.Client
	baseURL string
	headers map[string]string
}

// NewHTTPClient creates a new HTTP client
func NewHTTPClient(timeout time.Duration) *HTTPClient {
	return &HTTPClient{
		client: &http.Client{
			Timeout: timeout,
		},
		headers: make(map[string]string),
	}
}

// SetBaseURL sets the base URL for all requests
func (c *HTTPClient) SetBaseURL(baseURL string) {
	c.baseURL = strings.TrimSuffix(baseURL, "/")
}

// SetHeader sets a default header for all requests
func (c *HTTPClient) SetHeader(key, value string) {
	c.headers[key] = value
}

// SetHeaders sets multiple default headers
func (c *HTTPClient) SetHeaders(headers map[string]string) {
	for key, value := range headers {
		c.headers[key] = value
	}
}

// SetTimeout sets the request timeout
func (c *HTTPClient) SetTimeout(timeout time.Duration) {
	c.client.Timeout = timeout
}

// GET performs a GET request
func (c *HTTPClient) GET(path string, params map[string]string) (*http.Response, error) {
	return c.Request("GET", path, nil, params)
}

// POST performs a POST request
func (c *HTTPClient) POST(path string, body interface{}) (*http.Response, error) {
	return c.Request("POST", path, body, nil)
}

// PUT performs a PUT request
func (c *HTTPClient) PUT(path string, body interface{}) (*http.Response, error) {
	return c.Request("PUT", path, body, nil)
}

// DELETE performs a DELETE request
func (c *HTTPClient) DELETE(path string) (*http.Response, error) {
	return c.Request("DELETE", path, nil, nil)
}

// PATCH performs a PATCH request
func (c *HTTPClient) PATCH(path string, body interface{}) (*http.Response, error) {
	return c.Request("PATCH", path, body, nil)
}

// Request performs a generic HTTP request
func (c *HTTPClient) Request(method, path string, body interface{}, params map[string]string) (*http.Response, error) {
	// Build URL
	fullURL := c.buildURL(path, params)
	
	// Prepare body
	var bodyReader io.Reader
	var contentType string
	
	if body != nil {
		switch v := body.(type) {
		case string:
			bodyReader = strings.NewReader(v)
			contentType = "text/plain"
		case []byte:
			bodyReader = bytes.NewReader(v)
			contentType = "application/octet-stream"
		default:
			jsonBody, err := json.Marshal(body)
			if err != nil {
				return nil, err
			}
			bodyReader = bytes.NewReader(jsonBody)
			contentType = "application/json"
		}
	}
	
	// Create request
	req, err := http.NewRequest(method, fullURL, bodyReader)
	if err != nil {
		return nil, err
	}
	
	// Set content type
	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}
	
	// Set default headers
	for key, value := range c.headers {
		req.Header.Set(key, value)
	}
	
	// Perform request
	return c.client.Do(req)
}

// RequestWithContext performs a request with context
func (c *HTTPClient) RequestWithContext(ctx context.Context, method, path string, body interface{}, params map[string]string) (*http.Response, error) {
	// Build URL
	fullURL := c.buildURL(path, params)
	
	// Prepare body
	var bodyReader io.Reader
	var contentType string
	
	if body != nil {
		switch v := body.(type) {
		case string:
			bodyReader = strings.NewReader(v)
			contentType = "text/plain"
		case []byte:
			bodyReader = bytes.NewReader(v)
			contentType = "application/octet-stream"
		default:
			jsonBody, err := json.Marshal(body)
			if err != nil {
				return nil, err
			}
			bodyReader = bytes.NewReader(jsonBody)
			contentType = "application/json"
		}
	}
	
	// Create request with context
	req, err := http.NewRequestWithContext(ctx, method, fullURL, bodyReader)
	if err != nil {
		return nil, err
	}
	
	// Set content type
	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}
	
	// Set default headers
	for key, value := range c.headers {
		req.Header.Set(key, value)
	}
	
	// Perform request
	return c.client.Do(req)
}

// buildURL builds the full URL with query parameters
func (c *HTTPClient) buildURL(path string, params map[string]string) string {
	fullURL := path
	if c.baseURL != "" {
		fullURL = c.baseURL + "/" + strings.TrimPrefix(path, "/")
	}
	
	if len(params) > 0 {
		values := url.Values{}
		for key, value := range params {
			values.Add(key, value)
		}
		fullURL += "?" + values.Encode()
	}
	
	return fullURL
}

// ReadResponseBody reads and returns the response body
func ReadResponseBody(resp *http.Response) ([]byte, error) {
	defer resp.Body.Close()
	return io.ReadAll(resp.Body)
}

// ReadResponseBodyAsString reads response body as string
func ReadResponseBodyAsString(resp *http.Response) (string, error) {
	body, err := ReadResponseBody(resp)
	if err != nil {
		return "", err
	}
	return string(body), nil
}

// ReadResponseBodyAsJSON reads response body as JSON
func ReadResponseBodyAsJSON(resp *http.Response, target interface{}) error {
	body, err := ReadResponseBody(resp)
	if err != nil {
		return err
	}
	return json.Unmarshal(body, target)
}

// IsSuccessStatus checks if HTTP status is successful (2xx)
func IsSuccessStatus(statusCode int) bool {
	return statusCode >= 200 && statusCode < 300
}

// IsClientError checks if HTTP status is client error (4xx)
func IsClientError(statusCode int) bool {
	return statusCode >= 400 && statusCode < 500
}

// IsServerError checks if HTTP status is server error (5xx)
func IsServerError(statusCode int) bool {
	return statusCode >= 500 && statusCode < 600
}

// GetStatusText returns status text for HTTP status code
func GetStatusText(statusCode int) string {
	return http.StatusText(statusCode)
}

// ParseURL parses a URL string
func ParseURL(rawURL string) (*url.URL, error) {
	return url.Parse(rawURL)
}

// BuildURL builds a URL from components
func BuildURL(scheme, host, path string, params map[string]string) string {
	u := &url.URL{
		Scheme: scheme,
		Host:   host,
		Path:   path,
	}
	
	if len(params) > 0 {
		values := url.Values{}
		for key, value := range params {
			values.Add(key, value)
		}
		u.RawQuery = values.Encode()
	}
	
	return u.String()
}

// GetQueryParam gets a query parameter from URL
func GetQueryParam(u *url.URL, key string) string {
	return u.Query().Get(key)
}

// GetQueryParams gets all query parameters from URL
func GetQueryParams(u *url.URL) map[string]string {
	params := make(map[string]string)
	for key, values := range u.Query() {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}
	return params
}

// AddQueryParam adds a query parameter to URL
func AddQueryParam(u *url.URL, key, value string) {
	q := u.Query()
	q.Add(key, value)
	u.RawQuery = q.Encode()
}

// SetQueryParam sets a query parameter in URL
func SetQueryParam(u *url.URL, key, value string) {
	q := u.Query()
	q.Set(key, value)
	u.RawQuery = q.Encode()
}

// RemoveQueryParam removes a query parameter from URL
func RemoveQueryParam(u *url.URL, key string) {
	q := u.Query()
	q.Del(key)
	u.RawQuery = q.Encode()
}

// GetClientIP extracts client IP from request
func GetClientIP(req *http.Request) string {
	// Check X-Forwarded-For header
	if xff := req.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}
	
	// Check X-Real-IP header
	if xri := req.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}
	
	// Check X-Forwarded header
	if xf := req.Header.Get("X-Forwarded"); xf != "" {
		return xf
	}
	
	// Fall back to RemoteAddr
	return req.RemoteAddr
}

// GetUserAgent extracts user agent from request
func GetUserAgent(req *http.Request) string {
	return req.Header.Get("User-Agent")
}

// GetReferer extracts referer from request
func GetReferer(req *http.Request) string {
	return req.Header.Get("Referer")
}

// GetBearerToken extracts Bearer token from Authorization header
func GetBearerToken(req *http.Request) string {
	auth := req.Header.Get("Authorization")
	if auth == "" {
		return ""
	}
	
	parts := strings.Split(auth, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {
		return ""
	}
	
	return parts[1]
}

// SetBearerToken sets Bearer token in Authorization header
func SetBearerToken(req *http.Request, token string) {
	req.Header.Set("Authorization", "Bearer "+token)
}

// IsAjaxRequest checks if request is an AJAX request
func IsAjaxRequest(req *http.Request) bool {
	return req.Header.Get("X-Requested-With") == "XMLHttpRequest"
}

// IsJSONRequest checks if request content type is JSON
func IsJSONRequest(req *http.Request) bool {
	contentType := req.Header.Get("Content-Type")
	return strings.Contains(contentType, "application/json")
}

// IsMultipartRequest checks if request is multipart form data
func IsMultipartRequest(req *http.Request) bool {
	contentType := req.Header.Get("Content-Type")
	return strings.Contains(contentType, "multipart/form-data")
}

// SetCORS sets CORS headers
func SetCORS(w http.ResponseWriter, allowOrigin, allowMethods, allowHeaders string) {
	w.Header().Set("Access-Control-Allow-Origin", allowOrigin)
	w.Header().Set("Access-Control-Allow-Methods", allowMethods)
	w.Header().Set("Access-Control-Allow-Headers", allowHeaders)
}

// SetNoCacheHeaders sets no-cache headers
func SetNoCacheHeaders(w http.ResponseWriter) {
	w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
	w.Header().Set("Pragma", "no-cache")
	w.Header().Set("Expires", "0")
}

// SetJSONResponse sets JSON response headers
func SetJSONResponse(w http.ResponseWriter) {
	w.Header().Set("Content-Type", "application/json")
}

// WriteJSONResponse writes JSON response
func WriteJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	
	return json.NewEncoder(w).Encode(data)
}

// WriteErrorResponse writes error response
func WriteErrorResponse(w http.ResponseWriter, statusCode int, message string) error {
	return WriteJSONResponse(w, statusCode, map[string]string{
		"error": message,
	})
}

// WriteSuccessResponse writes success response
func WriteSuccessResponse(w http.ResponseWriter, data interface{}) error {
	return WriteJSONResponse(w, http.StatusOK, data)
}

// Retry performs HTTP request with retry logic
func Retry(req *http.Request, maxRetries int, backoff time.Duration) (*http.Response, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	
	var resp *http.Response
	var err error
	
	for i := 0; i <= maxRetries; i++ {
		resp, err = client.Do(req)
		if err == nil && IsSuccessStatus(resp.StatusCode) {
			return resp, nil
		}
		
		if i < maxRetries {
			time.Sleep(backoff * time.Duration(i+1))
		}
	}
	
	return resp, err
}

// DownloadFile downloads a file from URL
func DownloadFile(url, filename string) error {
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if !IsSuccessStatus(resp.StatusCode) {
		return fmt.Errorf("failed to download file: %s", resp.Status)
	}
	
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	
	return WriteFile(filename, string(data))
}

// GetContentType returns content type from response
func GetContentType(resp *http.Response) string {
	return resp.Header.Get("Content-Type")
}

// GetContentLength returns content length from response
func GetContentLength(resp *http.Response) int64 {
	return resp.ContentLength
}

// IsHTTPS checks if URL is HTTPS
func IsHTTPS(u *url.URL) bool {
	return u.Scheme == "https"
}

// IsHTTP checks if URL is HTTP
func IsHTTP(u *url.URL) bool {
	return u.Scheme == "http"
}