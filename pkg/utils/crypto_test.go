package utils

import (
	"strings"
	"testing"
)

func TestGenerateRandomString(t *testing.T) {
	length := 10
	result, err := GenerateRandomString(length)
	if err != nil {
		t.<PERSON><PERSON>("GenerateRandomString failed: %v", err)
	}
	
	if len(result) != length {
		t.<PERSON><PERSON>rf("GenerateRandomString length = %d, expected %d", len(result), length)
	}
	
	// Test that two calls produce different results
	result2, err := GenerateRandomString(length)
	if err != nil {
		t.<PERSON><PERSON>("GenerateRandomString failed: %v", err)
	}
	
	if result == result2 {
		t.Error("GenerateRandomString should produce different results")
	}
}

func TestGenerateRandomBytes(t *testing.T) {
	length := 16
	result, err := GenerateRandomBytes(length)
	if err != nil {
		t.<PERSON><PERSON>("GenerateRandomBytes failed: %v", err)
	}
	
	if len(result) != length {
		t.<PERSON><PERSON>("GenerateRandomBytes length = %d, expected %d", len(result), length)
	}
}

func TestMD5(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"hello", "5d41402abc4b2a76b9719d911017c592"},
		{"world", "7d793037a0760186574b0282f2f435e7"},
		{"", "d41d8cd98f00b204e9800998ecf8427e"},
	}

	for _, test := range tests {
		result := MD5(test.input)
		if result != test.expected {
			t.Errorf("MD5(%s) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestSHA256(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"hello", "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824"},
		{"world", "486ea46224d1bb4fb680f34f7c9ad96a8f24ec88be73ea8e5a6c65260e9cb8a7"},
		{"", "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"},
	}

	for _, test := range tests {
		result := SHA256(test.input)
		if result != test.expected {
			t.Errorf("SHA256(%s) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestBase64Encode(t *testing.T) {
	tests := []struct {
		input    []byte
		expected string
	}{
		{[]byte("hello"), "aGVsbG8="},
		{[]byte("world"), "d29ybGQ="},
		{[]byte(""), ""},
	}

	for _, test := range tests {
		result := Base64Encode(test.input)
		if result != test.expected {
			t.Errorf("Base64Encode(%s) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestBase64Decode(t *testing.T) {
	tests := []struct {
		input    string
		expected []byte
	}{
		{"aGVsbG8=", []byte("hello")},
		{"d29ybGQ=", []byte("world")},
		{"", []byte{}},
	}

	for _, test := range tests {
		result, err := Base64Decode(test.input)
		if err != nil {
			t.Errorf("Base64Decode(%s) failed: %v", test.input, err)
		}
		if string(result) != string(test.expected) {
			t.Errorf("Base64Decode(%s) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestGenerateToken(t *testing.T) {
	length := 16
	result, err := GenerateToken(length)
	if err != nil {
		t.Errorf("GenerateToken failed: %v", err)
	}
	
	// Token should be hex encoded, so length should be length * 2
	if len(result) != length*2 {
		t.Errorf("GenerateToken length = %d, expected %d", len(result), length*2)
	}
	
	// Check that it's valid hex
	for _, char := range result {
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f')) {
			t.Errorf("GenerateToken produced invalid hex character: %c", char)
		}
	}
}

func TestGenerateAPIKey(t *testing.T) {
	result, err := GenerateAPIKey()
	if err != nil {
		t.Errorf("GenerateAPIKey failed: %v", err)
	}
	
	// API key should be 32 bytes hex encoded = 64 characters
	if len(result) != 64 {
		t.Errorf("GenerateAPIKey length = %d, expected 64", len(result))
	}
}

func TestGenerateUUID(t *testing.T) {
	result, err := GenerateUUID()
	if err != nil {
		t.Errorf("GenerateUUID failed: %v", err)
	}
	
	// UUID format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
	parts := strings.Split(result, "-")
	if len(parts) != 5 {
		t.Errorf("GenerateUUID format incorrect: %s", result)
	}
	
	expectedLengths := []int{8, 4, 4, 4, 12}
	for i, part := range parts {
		if len(part) != expectedLengths[i] {
			t.Errorf("GenerateUUID part %d length = %d, expected %d", i, len(part), expectedLengths[i])
		}
	}
}

func TestMaskString(t *testing.T) {
	tests := []struct {
		input        string
		maskChar     rune
		visibleChars int
		expected     string
	}{
		{"hello world", '*', 2, "he*******ld"},
		{"short", '*', 1, "s***t"},
		{"password123", '*', 3, "pas*****123"},
	}

	for _, test := range tests {
		result := MaskString(test.input, test.maskChar, test.visibleChars)
		if result != test.expected {
			t.Errorf("MaskString(%s, %c, %d) = %s, expected %s", 
				test.input, test.maskChar, test.visibleChars, result, test.expected)
		}
	}
}

func TestMaskEmail(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"<EMAIL>", "u**r@***.com"},
		{"<EMAIL>", "***@***.co"},
		{"<EMAIL>", "t*******r@***.org"},
		{"invalid-email", "***@***.***"},
	}

	for _, test := range tests {
		result := MaskEmail(test.input)
		if result != test.expected {
			t.Errorf("MaskEmail(%s) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestMaskCreditCard(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"1234567890123456", "****-****-****-3456"},
		{"1234 5678 9012 3456", "****-****-****-3456"},
		{"1234-5678-9012-3456", "****-****-****-3456"},
		{"123", "****-****-****-****"},
	}

	for _, test := range tests {
		result := MaskCreditCard(test.input)
		if result != test.expected {
			t.Errorf("MaskCreditCard(%s) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestMaskPhoneNumber(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"1234567890", "***-***-7890"},
		{"(*************", "***-***-7890"},
		{"************", "***-***-7890"},
		{"************", "***-***-7890"},
		{"123", "***-***-****"},
	}

	for _, test := range tests {
		result := MaskPhoneNumber(test.input)
		if result != test.expected {
			t.Errorf("MaskPhoneNumber(%s) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestHashPassword(t *testing.T) {
	password := "mypassword123"
	hash := HashPassword(password)
	
	// Hash should be consistent
	hash2 := HashPassword(password)
	if hash != hash2 {
		t.Error("HashPassword should be consistent")
	}
	
	// Different passwords should produce different hashes
	hash3 := HashPassword("differentpassword")
	if hash == hash3 {
		t.Error("Different passwords should produce different hashes")
	}
}

func TestValidatePasswordHash(t *testing.T) {
	password := "mypassword123"
	hash := HashPassword(password)
	
	// Valid password should validate
	if !ValidatePasswordHash(password, hash) {
		t.Error("Valid password should validate")
	}
	
	// Invalid password should not validate
	if ValidatePasswordHash("wrongpassword", hash) {
		t.Error("Invalid password should not validate")
	}
}

func BenchmarkMD5(b *testing.B) {
	data := "hello world"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		MD5(data)
	}
}

func BenchmarkSHA256(b *testing.B) {
	data := "hello world"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		SHA256(data)
	}
}

func BenchmarkGenerateToken(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GenerateToken(16)
	}
}

func BenchmarkGenerateUUID(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GenerateUUID()
	}
}