package pagination

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
)

// CursorRequest represents a cursor-based pagination request
type CursorRequest struct {
	Cursor string `json:"cursor,omitempty" form:"cursor"`
	Limit  int    `json:"limit,omitempty" form:"limit"`
}

// CursorResponse represents a cursor-based pagination response
type CursorResponse struct {
	HasNext        bool   `json:"has_next"`
	NextCursor     string `json:"next_cursor,omitempty"`
	HasPrevious    bool   `json:"has_previous"`
	PreviousCursor string `json:"previous_cursor,omitempty"`
	Count          int    `json:"count"`
	Has<PERSON><PERSON>        bool   `json:"has_more"` // Alias for HasNext
	Limit          int    `json:"limit"`
}

// CursorData represents the internal cursor data
type CursorData struct {
	ID        uint      `json:"id"`
	CreatedAt time.Time `json:"created_at"`
}

// <PERSON>ursor represents a cursor with ID, time and optional score
type Cursor struct {
	ID    int64      `json:"id"`
	Time  time.Time  `json:"time"`
	Score *float64   `json:"score,omitempty"`
}

// DefaultLimit is the default pagination limit
const DefaultLimit = 50

// MaxLimit is the maximum pagination limit
const MaxLimit = 100

// NewCursorRequest creates a new cursor request with validation
func NewCursorRequest(cursor string, limit int) *CursorRequest {
	if limit <= 0 {
		limit = DefaultLimit
	}
	if limit > MaxLimit {
		limit = MaxLimit
	}

	return &CursorRequest{
		Cursor: cursor,
		Limit:  limit,
	}
}

// ParseCursor parses a cursor string into CursorData
func ParseCursor(cursor string) (*CursorData, error) {
	if cursor == "" {
		return nil, nil
	}

	// Decode base64
	decoded, err := base64.URLEncoding.DecodeString(cursor)
	if err != nil {
		return nil, fmt.Errorf("invalid cursor format: %w", err)
	}

	// Parse JSON
	var cursorData CursorData
	if err := json.Unmarshal(decoded, &cursorData); err != nil {
		return nil, fmt.Errorf("invalid cursor data: %w", err)
	}

	return &cursorData, nil
}

// EncodeCursor encodes cursor data into a cursor string
func EncodeCursor(id uint, createdAt time.Time) (string, error) {
	cursorData := CursorData{
		ID:        id,
		CreatedAt: createdAt,
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(cursorData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cursor data: %w", err)
	}

	// Encode to base64
	encoded := base64.URLEncoding.EncodeToString(jsonData)
	return encoded, nil
}

// GetLimitWithDefault returns the limit with default value if not set
func (cr *CursorRequest) GetLimitWithDefault() int {
	if cr.Limit <= 0 {
		return DefaultLimit
	}
	if cr.Limit > MaxLimit {
		return MaxLimit
	}
	return cr.Limit
}

// ParseLimitFromString parses limit from string with validation
func ParseLimitFromString(limitStr string) int {
	if limitStr == "" {
		return DefaultLimit
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		return DefaultLimit
	}

	if limit <= 0 {
		return DefaultLimit
	}
	if limit > MaxLimit {
		return MaxLimit
	}

	return limit
}

// NewCursorResponse creates a new cursor response
func NewCursorResponse(hasNext bool, nextCursor string, count int) *CursorResponse {
	return &CursorResponse{
		HasNext:    hasNext,
		NextCursor: nextCursor,
		Count:      count,
		HasMore:    hasNext,
	}
}

// DecodeCursor decodes a cursor string into a Cursor struct
func DecodeCursor(encoded string) (*Cursor, error) {
	if encoded == "" {
		return nil, nil
	}

	// Decode base64
	decoded, err := base64.URLEncoding.DecodeString(encoded)
	if err != nil {
		return nil, fmt.Errorf("invalid cursor format: %w", err)
	}

	// Parse JSON
	var cursor Cursor
	if err := json.Unmarshal(decoded, &cursor); err != nil {
		return nil, fmt.Errorf("invalid cursor data: %w", err)
	}

	return &cursor, nil
}

// NewCursorFromEntity creates a new cursor from entity data
func NewCursorFromEntity(id int64, time time.Time, score *float64) *Cursor {
	return &Cursor{
		ID:    id,
		Time:  time,
		Score: score,
	}
}

// String encodes the cursor to a base64 string
func (c *Cursor) String() (string, error) {
	// Marshal to JSON
	jsonData, err := json.Marshal(c)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cursor: %w", err)
	}

	// Encode to base64
	encoded := base64.URLEncoding.EncodeToString(jsonData)
	return encoded, nil
}

// CursorPagination represents cursor-based pagination parameters
type CursorPagination struct {
	Cursor string
	Limit  int
}

// ValidateLimit validates and normalizes a limit value
func ValidateLimit(limit int) int {
	if limit <= 0 {
		return DefaultLimit
	}
	if limit > MaxLimit {
		return MaxLimit
	}
	return limit
}

// NewCursorPaginationFromQuery creates a CursorPagination from Gin context query parameters
func NewCursorPaginationFromQuery(c interface{}) *CursorPagination {
	// This is a temporary implementation - ideally we'd extract actual query params
	// For now, we'll create a simple implementation with defaults
	return &CursorPagination{
		Cursor: "", // This would be extracted from query params
		Limit:  DefaultLimit,
	}
}