package response

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// ============================================================================
// Core Response Types
// ============================================================================

// Status contains response status information
type Status struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Success   bool        `json:"success"`
	ErrorCode string      `json:"error_code,omitempty"`
	Path      string      `json:"path"`
	Timestamp string      `json:"timestamp"`
	Details   interface{} `json:"details,omitempty"`
}

// Meta contains metadata for paginated responses
type Meta struct {
	NextCursor string `json:"next_cursor,omitempty"`
	HasMore    bool   `json:"has_more,omitempty"`
}

// PaginatedResponse represents a paginated response with offset-based pagination
type PaginatedResponse struct {
	Items    interface{} `json:"items"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"page_size"`
	Pages    int         `json:"pages"`
}

// Response represents the standard API response format
type Response struct {
	Status Status      `json:"status"`
	Data   interface{} `json:"data,omitempty"`
}

type ResponseMeta struct {
	Status Status      `json:"status"`
	Data   interface{} `json:"data,omitempty"`
	Meta   *Meta       `json:"meta,omitempty"`
}

// Detail represents validation error details
type Detail struct {
	Field   string `json:"field,omitempty"`
	Message string `json:"message"`
}

// ResponseWriter provides helper methods for HTTP responses
type ResponseWriter struct {
	http.ResponseWriter
	written bool
}

// NewResponse wraps an http.ResponseWriter with helper methods
func NewResponse(w http.ResponseWriter) *ResponseWriter {
	return &ResponseWriter{ResponseWriter: w}
}

// JSON sends a JSON response with the given status code
func (r *ResponseWriter) JSON(status int, data interface{}) error {
	r.Header().Set("Content-Type", "application/json; charset=utf-8")
	r.WriteHeader(status)
	r.written = true

	if data == nil {
		return nil
	}

	encoder := json.NewEncoder(r)
	encoder.SetIndent("", "  ")
	return encoder.Encode(data)
}

// Success sends a success response
func (r *ResponseWriter) Success(data interface{}) error {
	return r.SuccessWithPath(data, "")
}

// SuccessWithPath sends a success response with a custom path
func (r *ResponseWriter) SuccessWithPath(data interface{}, path string) error {
	response := Response{
		Status: Status{
			Code:      http.StatusOK,
			Message:   "Success",
			Success:   true,
			Path:      path,
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
	}
	return r.JSON(http.StatusOK, response)
}

// Created sends a created response
func (r *ResponseWriter) Created(data interface{}) error {
	return r.CreatedWithPath(data, "")
}

// CreatedWithPath sends a created response with a custom path
func (r *ResponseWriter) CreatedWithPath(data interface{}, path string) error {
	response := Response{
		Status: Status{
			Code:      http.StatusCreated,
			Message:   "Created",
			Success:   true,
			Path:      path,
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
	}
	return r.JSON(http.StatusCreated, response)
}

// NoContent sends a no content response
func (r *ResponseWriter) NoContent() error {
	r.WriteHeader(http.StatusNoContent)
	r.written = true
	return nil
}

// Error sends an error response
func (r *ResponseWriter) Error(status int, message string) error {
	return r.ErrorWithPath(status, message, "")
}

// ErrorWithPath sends an error response with a custom path
func (r *ResponseWriter) ErrorWithPath(status int, message string, path string) error {
	response := Response{
		Status: Status{
			Code:      status,
			Message:   message,
			Success:   false,
			ErrorCode: getErrorCode(status),
			Path:      path,
			Timestamp: time.Now().Format(time.RFC3339),
		},
	}
	return r.JSON(status, response)
}

// ErrorWithDetails sends an error response with additional details
func (r *ResponseWriter) ErrorWithDetails(status int, message string, details interface{}) error {
	return r.ErrorWithDetailsAndPath(status, message, details, "")
}

// ErrorWithDetailsAndPath sends an error response with additional details and custom path
func (r *ResponseWriter) ErrorWithDetailsAndPath(status int, message string, details interface{}, path string) error {
	response := Response{
		Status: Status{
			Code:      status,
			Message:   message,
			Success:   false,
			ErrorCode: getErrorCode(status),
			Path:      path,
			Timestamp: time.Now().Format(time.RFC3339),
			Details:   details,
		},
	}
	return r.JSON(status, response)
}

// BadRequest sends a bad request error response
func (r *ResponseWriter) BadRequest(message string) error {
	return r.Error(http.StatusBadRequest, message)
}

// Unauthorized sends an unauthorized error response
func (r *ResponseWriter) Unauthorized(message string) error {
	if message == "" {
		message = "Unauthorized"
	}
	return r.Error(http.StatusUnauthorized, message)
}

// Forbidden sends a forbidden error response
func (r *ResponseWriter) Forbidden(message string) error {
	if message == "" {
		message = "Forbidden"
	}
	return r.Error(http.StatusForbidden, message)
}

// NotFound sends a not found error response
func (r *ResponseWriter) NotFound(message string) error {
	if message == "" {
		message = "Not Found"
	}
	return r.Error(http.StatusNotFound, message)
}

// InternalServerError sends an internal server error response
func (r *ResponseWriter) InternalServerError(message string) error {
	if message == "" {
		message = "Internal Server Error"
	}
	return r.Error(http.StatusInternalServerError, message)
}

// ValidationError sends a validation error response
func (r *ResponseWriter) ValidationError(errors map[string]string) error {
	// Convert map[string]string to []Detail format
	var details []Detail
	for field, message := range errors {
		details = append(details, Detail{
			Field:   field,
			Message: message,
		})
	}

	return r.ErrorWithDetails(
		http.StatusUnprocessableEntity,
		"Validation failed",
		details,
	)
}

// CursorPaginated sends a cursor-based paginated response
func (r *ResponseWriter) CursorPaginated(data interface{}, meta pagination.CursorResponse) error {
	response := ResponseMeta{
		Status: Status{
			Code:      http.StatusOK,
			Message:   "Success",
			Success:   true,
			Path:      "",
			Timestamp: time.Now().Format(time.RFC3339),
		},
		Data: data,
		Meta: &Meta{
			NextCursor: meta.NextCursor,
			HasMore:    meta.HasMore,
		},
	}
	return r.JSON(http.StatusOK, response)
}

// ============================================================================
// Standalone Helper Functions
// ============================================================================

// Standalone helper functions for direct use with http.ResponseWriter
func Success(w http.ResponseWriter, data interface{}) {
	NewResponse(w).Success(data)
}

func Created(w http.ResponseWriter, data interface{}) {
	NewResponse(w).Created(data)
}

func BadRequest(w http.ResponseWriter, message string, details ...string) {
	r := NewResponse(w)
	if len(details) > 0 {
		r.ErrorWithDetails(http.StatusBadRequest, message, details[0])
	} else {
		r.BadRequest(message)
	}
}

func Unauthorized(w http.ResponseWriter, message string) {
	NewResponse(w).Unauthorized(message)
}

func Forbidden(w http.ResponseWriter, message string) {
	NewResponse(w).Forbidden(message)
}

func NotFound(w http.ResponseWriter, message string) {
	NewResponse(w).NotFound(message)
}

func InternalServerError(w http.ResponseWriter, message string) {
	NewResponse(w).InternalServerError(message)
}

func InternalError(w http.ResponseWriter, message string, details ...string) {
	r := NewResponse(w)
	if len(details) > 0 {
		r.ErrorWithDetails(http.StatusInternalServerError, message, details[0])
	} else {
		r.InternalServerError(message)
	}
}

func TooManyRequests(w http.ResponseWriter, message string) {
	NewResponse(w).Error(http.StatusTooManyRequests, message)
}

func NoContent(w http.ResponseWriter) {
	NewResponse(w).NoContent()
}

func OK(w http.ResponseWriter, data interface{}) {
	NewResponse(w).Success(data)
}

func Conflict(w http.ResponseWriter, message string) {
	NewResponse(w).Error(http.StatusConflict, message)
}

func ValidationError(w http.ResponseWriter, errors interface{}) {
	r := NewResponse(w)
	switch v := errors.(type) {
	case map[string]string:
		r.ValidationError(v)
	case error:
		// Convert error to map format
		errorMap := map[string]string{"error": v.Error()}
		r.ValidationError(errorMap)
	default:
		// Fallback
		errorMap := map[string]string{"error": "Validation failed"}
		r.ValidationError(errorMap)
	}
}

// CursorPaginated sends a cursor-based paginated response
func CursorPaginated(w http.ResponseWriter, data interface{}, meta pagination.CursorResponse) {
	NewResponse(w).CursorPaginated(data, meta)
}

// ============================================================================
// Helper Functions
// ============================================================================

// getErrorCode returns an error code based on HTTP status
func getErrorCode(status int) string {
	switch status {
	case http.StatusBadRequest:
		return "BAD_REQUEST"
	case http.StatusUnauthorized:
		return "UNAUTHORIZED"
	case http.StatusForbidden:
		return "FORBIDDEN"
	case http.StatusNotFound:
		return "NOT_FOUND"
	case http.StatusMethodNotAllowed:
		return "METHOD_NOT_ALLOWED"
	case http.StatusConflict:
		return "CONFLICT"
	case http.StatusUnprocessableEntity:
		return "VALIDATION_ERROR"
	case http.StatusTooManyRequests:
		return "TOO_MANY_REQUESTS"
	case http.StatusInternalServerError:
		return "INTERNAL_ERROR"
	case http.StatusServiceUnavailable:
		return "SERVICE_UNAVAILABLE"
	default:
		return "ERROR"
	}
}
