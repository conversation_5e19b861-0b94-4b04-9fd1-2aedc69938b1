package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"golang.org/x/time/rate"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// AuthService interface for middleware - simplified for tenant-only server
type AuthService interface {
	ValidateToken(token string) (map[string]interface{}, error)
	GetUserByID(userID string) (map[string]interface{}, error)
}

// TokenClaims key for context
type contextKey string

const (
	tokenClaimsKey contextKey = "token_claims"
	userKey        contextKey = "user"
	requestIDKey   contextKey = "request_id"
	tenantIDKey    contextKey = "tenant_id"
)

// LoggingMiddleware logs HTTP requests
func LoggingMiddleware(logger utils.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()

			// Create a response writer wrapper to capture status code
			wrapped := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}

			next.ServeHTTP(wrapped, r)

			duration := time.Since(start)

			logger.WithFields(map[string]interface{}{
				"method":     r.Method,
				"path":       r.URL.Path,
				"status":     wrapped.statusCode,
				"duration":   duration.String(),
				"user_agent": r.UserAgent(),
				"remote_ip":  getClientIP(r),
			}).Info("HTTP request")
		})
	}
}

// RequestIDMiddleware adds a unique request ID to each request
func RequestIDMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			requestID := r.Header.Get("X-Request-ID")
			if requestID == "" {
				requestID = uuid.New().String()
			}

			w.Header().Set("X-Request-ID", requestID)

			ctx := context.WithValue(r.Context(), requestIDKey, requestID)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// CORSMiddleware handles CORS headers
func CORSMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Tenant-ID, X-Request-ID")
			w.Header().Set("Access-Control-Expose-Headers", "X-Request-ID")
			w.Header().Set("Access-Control-Max-Age", "86400")

			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// RateLimitMiddleware implements basic rate limiting
func RateLimitMiddleware() func(http.Handler) http.Handler {
	// Simple in-memory rate limiter (not production ready)
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// For demo purposes, just pass through
			// In production, use proper rate limiting like golang.org/x/time/rate
			next.ServeHTTP(w, r)
		})
	}
}

// AuthMiddleware validates authentication tokens
func AuthMiddleware(authService AuthService) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			authHeader := r.Header.Get("Authorization")
			if authHeader == "" {
				sendJSONError(w, http.StatusUnauthorized, "Authorization header required", "UNAUTHORIZED")
				return
			}

			// Extract token from "Bearer <token>"
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) != 2 || parts[0] != "Bearer" {
				sendJSONError(w, http.StatusUnauthorized, "Invalid authorization header format", "UNAUTHORIZED")
				return
			}

			token := parts[1]
			claims, err := authService.ValidateToken(token)
			if err != nil {
				sendJSONError(w, http.StatusUnauthorized, "Invalid token", "UNAUTHORIZED")
				return
			}

			// Add claims to context
			ctx := context.WithValue(r.Context(), tokenClaimsKey, claims)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// RequireRoleHTTPMiddleware requires specific roles for http.Handler
func RequireRoleHTTPMiddleware(allowedRoles ...string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			claims := GetTokenClaims(r)
			if claims == nil {
				sendJSONError(w, http.StatusUnauthorized, "Authentication required", "UNAUTHORIZED")
				return
			}

			// Check if user has any of the allowed roles
			hasRole := false
			if rolesInterface, ok := claims["roles"]; ok {
				if roles, ok := rolesInterface.([]interface{}); ok {
					for _, roleInterface := range roles {
						if userRole, ok := roleInterface.(string); ok {
							for _, allowedRole := range allowedRoles {
								if userRole == allowedRole {
									hasRole = true
									break
								}
							}
							if hasRole {
								break
							}
						}
					}
				}
			}

			if !hasRole {
				sendJSONError(w, http.StatusForbidden, "Insufficient permissions", "FORBIDDEN")
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// TenantMiddleware extracts tenant information
func TenantMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			tenantID := r.Header.Get("X-Tenant-ID")
			if tenantID == "" {
				tenantID = r.URL.Query().Get("tenant_id")
			}

			if tenantID != "" {
				ctx := context.WithValue(r.Context(), tenantIDKey, tenantID)
				next.ServeHTTP(w, r.WithContext(ctx))
			} else {
				next.ServeHTTP(w, r)
			}
		})
	}
}

// Helper functions

// GetTokenClaims retrieves token claims from request context
func GetTokenClaims(r *http.Request) map[string]interface{} {
	if claims, ok := r.Context().Value(tokenClaimsKey).(map[string]interface{}); ok {
		return claims
	}
	return nil
}

// GetUser retrieves user from request context
func GetUser(r *http.Request) map[string]interface{} {
	if user, ok := r.Context().Value(userKey).(map[string]interface{}); ok {
		return user
	}
	return nil
}

// GetRequestID retrieves request ID from context
func GetRequestID(r *http.Request) string {
	if id, ok := r.Context().Value(requestIDKey).(string); ok {
		return id
	}
	return ""
}

// GetTenantID retrieves tenant ID from context
func GetTenantID(r *http.Request) string {
	if id, ok := r.Context().Value(tenantIDKey).(string); ok {
		return id
	}
	return ""
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(statusCode int) {
	rw.statusCode = statusCode
	rw.ResponseWriter.WriteHeader(statusCode)
}

// getClientIP extracts client IP from request
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// Take the first IP in the list
		if ips := strings.Split(xff, ","); len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// Fallback to RemoteAddr
	if ip := r.RemoteAddr; ip != "" {
		// Remove port if present
		if idx := strings.LastIndex(ip, ":"); idx != -1 {
			return ip[:idx]
		}
		return ip
	}

	return "unknown"
}

// sendJSONError sends a standardized JSON error response
func sendJSONError(w http.ResponseWriter, statusCode int, message, errorCode string) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(statusCode)

	// Escape special characters in JSON
	escapedMessage := strings.ReplaceAll(message, `"`, `\"`)
	escapedMessage = strings.ReplaceAll(escapedMessage, `\`, `\\`)
	escapedCode := strings.ReplaceAll(errorCode, `"`, `\"`)
	escapedCode = strings.ReplaceAll(escapedCode, `\`, `\\`)

	jsonData := fmt.Sprintf(`{"success":false,"error":{"message":"%s","code":"%s"}}`, escapedMessage, escapedCode)
	w.Write([]byte(jsonData))
}

// ===============================================
// GIN MIDDLEWARE FUNCTIONS
// ===============================================

// GinLoggingMiddleware logs HTTP requests for Gin
func GinLoggingMiddleware(logger utils.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logger.WithFields(map[string]interface{}{
			"method":     param.Method,
			"path":       param.Path,
			"status":     param.StatusCode,
			"duration":   param.Latency.String(),
			"user_agent": param.Request.UserAgent(),
			"remote_ip":  param.ClientIP,
		}).Info("HTTP request")
		return ""
	})
}

// GinRequestIDMiddleware adds request ID to Gin context
func GinRequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := uuid.New().String()
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	}
}

// GinCORSMiddleware handles CORS for Gin
func GinCORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, PATCH, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-Request-ID")
		c.Header("Access-Control-Expose-Headers", "X-Request-ID")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		c.Next()
	}
}

// GinRateLimitMiddleware applies rate limiting for Gin
func GinRateLimitMiddleware() gin.HandlerFunc {
	limiter := rate.NewLimiter(rate.Limit(100), 200) // 100 requests per second, burst of 200
	
	return func(c *gin.Context) {
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Rate limit exceeded",
					"code":    "RATE_LIMIT_EXCEEDED",
				},
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// GinAuthMiddleware validates JWT tokens for Gin
func GinAuthMiddleware(authService AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Authorization header is required",
					"code":    "MISSING_AUTH_HEADER",
				},
			})
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Bearer token is required",
					"code":    "INVALID_AUTH_FORMAT",
				},
			})
			c.Abort()
			return
		}

		claims, err := authService.ValidateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Invalid token",
					"code":    "INVALID_TOKEN",
				},
			})
			c.Abort()
			return
		}

		c.Set("token_claims", claims)
		c.Next()
	}
}

// GinTenantIsolationMiddleware enforces tenant isolation for Gin
func GinTenantIsolationMiddleware(tenantService interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		tenantID := c.Param("tenantId")
		if tenantID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Tenant ID is required",
					"code":    "MISSING_TENANT_ID",
				},
			})
			c.Abort()
			return
		}

		c.Set("tenant_id", tenantID)
		c.Next()
	}
}

// Helper functions for Gin context

// GetTokenClaimsFromGin retrieves token claims from Gin context
func GetTokenClaimsFromGin(c *gin.Context) map[string]interface{} {
	if claims, exists := c.Get("token_claims"); exists {
		if claimsMap, ok := claims.(map[string]interface{}); ok {
			return claimsMap
		}
	}
	return nil
}

// GetUserFromGin retrieves user from Gin context
func GetUserFromGin(c *gin.Context) map[string]interface{} {
	if user, exists := c.Get("user"); exists {
		if userMap, ok := user.(map[string]interface{}); ok {
			return userMap
		}
	}
	return nil
}

// GetRequestIDFromGin retrieves request ID from Gin context
func GetRequestIDFromGin(c *gin.Context) string {
	if id, exists := c.Get("request_id"); exists {
		if idStr, ok := id.(string); ok {
			return idStr
		}
	}
	return ""
}

// GetTenantIDFromGin retrieves tenant ID from Gin context
func GetTenantIDFromGin(c *gin.Context) string {
	if id, exists := c.Get("tenant_id"); exists {
		if idStr, ok := id.(string); ok {
			return idStr
		}
	}
	return ""
}
