package middleware

import (
	"context"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"crypto/subtle"
	"encoding/base64"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SecurityConfig holds security middleware configuration
type SecurityConfig struct {
	EnableSQLInjectionProtection bool
	EnableXSSProtection          bool
	EnableCSRFProtection         bool
	EnablePathTraversalProtection bool
	MaxRequestSize               int64
	AllowedFileExtensions        []string
	BlockedUserAgents            []string
	EnableInputSanitization      bool
}

// DefaultSecurityConfig returns default security configuration
func DefaultSecurityConfig() *SecurityConfig {
	return &SecurityConfig{
		EnableSQLInjectionProtection:  true,
		EnableXSSProtection:           true,
		EnableCSRFProtection:          true,
		EnablePathTraversalProtection: true,
		MaxRequestSize:                10 * 1024 * 1024, // 10MB
		AllowedFileExtensions:         []string{".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"},
		BlockedUserAgents:            []string{"curl", "wget", "python-requests"},
		EnableInputSanitization:      true,
	}
}

// SecurityMiddleware provides comprehensive security protection
func SecurityMiddleware(config *SecurityConfig, logger utils.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check request size
		if c.Request.ContentLength > config.MaxRequestSize {
			logger.WithFields(map[string]interface{}{
				"content_length": c.Request.ContentLength,
				"max_size":       config.MaxRequestSize,
				"client_ip":      c.ClientIP(),
				"user_agent":     c.Request.UserAgent(),
			}).Warn("Request size exceeded limit")

			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Request too large",
					"code":    "REQUEST_TOO_LARGE",
				},
			})
			c.Abort()
			return
		}

		// Check for blocked user agents
		userAgent := strings.ToLower(c.Request.UserAgent())
		for _, blocked := range config.BlockedUserAgents {
			if strings.Contains(userAgent, strings.ToLower(blocked)) {
				logger.WithFields(map[string]interface{}{
					"user_agent": c.Request.UserAgent(),
					"client_ip":  c.ClientIP(),
				}).Warn("Blocked user agent detected")

				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error": gin.H{
						"message": "Access denied",
						"code":    "ACCESS_DENIED",
					},
				})
				c.Abort()
				return
			}
		}

		// SQL Injection Protection
		if config.EnableSQLInjectionProtection {
			if detectSQLInjection(c) {
				logger.WithFields(map[string]interface{}{
					"path":       c.Request.URL.Path,
					"query":      c.Request.URL.RawQuery,
					"client_ip":  c.ClientIP(),
					"user_agent": c.Request.UserAgent(),
				}).Warn("SQL injection attempt detected")

				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error": gin.H{
						"message": "Invalid input detected",
						"code":    "INVALID_INPUT",
					},
				})
				c.Abort()
				return
			}
		}

		// XSS Protection
		if config.EnableXSSProtection {
			if detectXSS(c) {
				logger.WithFields(map[string]interface{}{
					"path":       c.Request.URL.Path,
					"query":      c.Request.URL.RawQuery,
					"client_ip":  c.ClientIP(),
					"user_agent": c.Request.UserAgent(),
				}).Warn("XSS attempt detected")

				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error": gin.H{
						"message": "Invalid input detected",
						"code":    "INVALID_INPUT",
					},
				})
				c.Abort()
				return
			}
		}

		// Path Traversal Protection
		if config.EnablePathTraversalProtection {
			if detectPathTraversal(c) {
				logger.WithFields(map[string]interface{}{
					"path":       c.Request.URL.Path,
					"client_ip":  c.ClientIP(),
					"user_agent": c.Request.UserAgent(),
				}).Warn("Path traversal attempt detected")

				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error": gin.H{
						"message": "Invalid path",
						"code":    "INVALID_PATH",
					},
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// detectSQLInjection checks for SQL injection patterns
func detectSQLInjection(c *gin.Context) bool {
	sqlPatterns := []string{
		`(?i)(union\s+select)`,
		`(?i)(or\s+1=1)`,
		`(?i)(and\s+1=1)`,
		`(?i)(drop\s+table)`,
		`(?i)(exec\s*\()`,
		`(?i)(script\s*>)`,
		`(?i)(\'\s*or\s*\')`,
		`(?i)(\'\s*union\s*select)`,
		`(?i)(information_schema)`,
		`(?i)(show\s+tables)`,
		`(?i)(describe\s+\w+)`,
		`(?i)(insert\s+into)`,
		`(?i)(update\s+\w+\s+set)`,
		`(?i)(delete\s+from)`,
		`(?i)(having\s+\d+)`,
		`(?i)(group\s+by\s+\d+)`,
		`(?i)(order\s+by\s+\d+)`,
	}

	// Check URL path and query parameters
	fullURL := c.Request.URL.Path + "?" + c.Request.URL.RawQuery
	
	for _, pattern := range sqlPatterns {
		if matched, _ := regexp.MatchString(pattern, fullURL); matched {
			return true
		}
	}

	// Check form data and JSON body (if available)
	if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
		if err := c.Request.ParseForm(); err == nil {
			for _, values := range c.Request.PostForm {
				for _, value := range values {
					for _, pattern := range sqlPatterns {
						if matched, _ := regexp.MatchString(pattern, value); matched {
							return true
						}
					}
				}
			}
		}
	}

	return false
}

// detectXSS checks for XSS patterns
func detectXSS(c *gin.Context) bool {
	xssPatterns := []string{
		`(?i)(<script)`,
		`(?i)(javascript:)`,
		`(?i)(on\w+\s*=)`,
		`(?i)(<iframe)`,
		`(?i)(<object)`,
		`(?i)(<embed)`,
		`(?i)(<form)`,
		`(?i)(eval\s*\()`,
		`(?i)(expression\s*\()`,
		`(?i)(vbscript:)`,
		`(?i)(data:text/html)`,
		`(?i)(<svg)`,
		`(?i)(<meta)`,
		`(?i)(<link)`,
		`(?i)(document\.cookie)`,
		`(?i)(window\.location)`,
		`(?i)(alert\s*\()`,
		`(?i)(confirm\s*\()`,
		`(?i)(prompt\s*\()`,
	}

	// Check URL path and query parameters
	fullURL := c.Request.URL.Path + "?" + c.Request.URL.RawQuery
	
	for _, pattern := range xssPatterns {
		if matched, _ := regexp.MatchString(pattern, fullURL); matched {
			return true
		}
	}

	// Check headers
	for name, values := range c.Request.Header {
		for _, value := range values {
			for _, pattern := range xssPatterns {
				if matched, _ := regexp.MatchString(pattern, name+" "+value); matched {
					return true
				}
			}
		}
	}

	return false
}

// detectPathTraversal checks for path traversal patterns
func detectPathTraversal(c *gin.Context) bool {
	pathTraversalPatterns := []string{
		`\.\.\/`,
		`\.\.\\`,
		`%2e%2e%2f`,
		`%2e%2e%5c`,
		`%252e%252e%252f`,
		`%252e%252e%255c`,
		`..%2f`,
		`..%5c`,
		`%2e%2e/`,
		`%2e%2e\`,
	}

	path := c.Request.URL.Path
	query := c.Request.URL.RawQuery

	for _, pattern := range pathTraversalPatterns {
		if matched, _ := regexp.MatchString(pattern, path); matched {
			return true
		}
		if matched, _ := regexp.MatchString(pattern, query); matched {
			return true
		}
	}

	return false
}

// CSRFTokenGenerator generates CSRF tokens
type CSRFTokenGenerator struct {
	secret []byte
}

// NewCSRFTokenGenerator creates a new CSRF token generator
func NewCSRFTokenGenerator(secret string) *CSRFTokenGenerator {
	return &CSRFTokenGenerator{
		secret: []byte(secret),
	}
}

// GenerateToken generates a new CSRF token
func (g *CSRFTokenGenerator) GenerateToken(sessionID string) (string, error) {
	// Generate random bytes
	randomBytes := make([]byte, 32)
	if _, err := rand.Read(randomBytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Create token data: timestamp + sessionID + random bytes
	timestamp := time.Now().Unix()
	tokenData := fmt.Sprintf("%d:%s:%s", timestamp, sessionID, base64.URLEncoding.EncodeToString(randomBytes))

	// Create HMAC
	h := hmac.New(sha256.New, g.secret)
	h.Write([]byte(tokenData))
	signature := h.Sum(nil)

	// Return token as base64(tokenData) + "." + base64(signature)
	token := base64.URLEncoding.EncodeToString([]byte(tokenData)) + "." + base64.URLEncoding.EncodeToString(signature)
	return token, nil
}

// ValidateToken validates a CSRF token
func (g *CSRFTokenGenerator) ValidateToken(token, sessionID string) bool {
	// Split token into data and signature
	parts := strings.Split(token, ".")
	if len(parts) != 2 {
		return false
	}

	tokenData, err := base64.URLEncoding.DecodeString(parts[0])
	if err != nil {
		return false
	}

	signature, err := base64.URLEncoding.DecodeString(parts[1])
	if err != nil {
		return false
	}

	// Verify HMAC
	h := hmac.New(sha256.New, g.secret)
	h.Write(tokenData)
	expectedSignature := h.Sum(nil)

	if subtle.ConstantTimeCompare(signature, expectedSignature) != 1 {
		return false
	}

	// Parse token data
	tokenDataStr := string(tokenData)
	parts = strings.Split(tokenDataStr, ":")
	if len(parts) != 3 {
		return false
	}

	// Verify session ID matches
	if parts[1] != sessionID {
		return false
	}

	// Check if token is not too old (24 hours)
	var timestamp int64
	if _, err := fmt.Sscanf(parts[0], "%d", &timestamp); err != nil {
		return false
	}

	if time.Now().Unix()-timestamp > 24*3600 {
		return false
	}

	return true
}

// AntiCSRFMiddleware provides CSRF protection
func AntiCSRFMiddleware(secret string) gin.HandlerFunc {
	generator := NewCSRFTokenGenerator(secret)
	
	return func(c *gin.Context) {
		// Skip CSRF protection for safe methods
		if c.Request.Method == "GET" || c.Request.Method == "HEAD" || c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		// Get session ID from context (set by JWT middleware)
		sessionID := ""
		if claims, exists := c.Get(JWTClaimsKey); exists {
			if jwtClaims, ok := claims.(*models.JWTClaims); ok && jwtClaims.SessionID != nil {
				sessionID = fmt.Sprintf("%d", *jwtClaims.SessionID)
			}
		}

		// If no session ID, generate a default one based on IP and User-Agent
		if sessionID == "" {
			sessionID = fmt.Sprintf("%s:%s", c.ClientIP(), c.GetHeader("User-Agent"))
		}

		// Check for CSRF token in header
		token := c.GetHeader("X-CSRF-Token")
		if token == "" {
			// Check for token in form data
			token = c.PostForm("_csrf_token")
		}

		if token == "" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"message": "CSRF token missing",
					"code":    "CSRF_TOKEN_MISSING",
				},
			})
			c.Abort()
			return
		}

		// Validate CSRF token
		if !generator.ValidateToken(token, sessionID) {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Invalid CSRF token",
					"code":    "INVALID_CSRF_TOKEN",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CSRFTokenEndpoint provides an endpoint to generate CSRF tokens
func CSRFTokenEndpoint(secret string) gin.HandlerFunc {
	generator := NewCSRFTokenGenerator(secret)
	
	return func(c *gin.Context) {
		// Get session ID from context
		sessionID := ""
		if claims, exists := c.Get(JWTClaimsKey); exists {
			if jwtClaims, ok := claims.(*models.JWTClaims); ok && jwtClaims.SessionID != nil {
				sessionID = fmt.Sprintf("%d", *jwtClaims.SessionID)
			}
		}

		// If no session ID, generate a default one based on IP and User-Agent
		if sessionID == "" {
			sessionID = fmt.Sprintf("%s:%s", c.ClientIP(), c.GetHeader("User-Agent"))
		}

		// Generate new token
		token, err := generator.GenerateToken(sessionID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Failed to generate CSRF token",
					"code":    "CSRF_TOKEN_GENERATION_FAILED",
				},
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"csrf_token": token,
				"expires_in": 24 * 3600, // 24 hours
			},
		})
	}
}

// RequestTimeoutMiddleware adds request timeout
func RequestTimeoutMiddleware(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()

		// Replace request context
		c.Request = c.Request.WithContext(ctx)

		// Channel to signal completion
		done := make(chan struct{})
		
		go func() {
			c.Next()
			close(done)
		}()

		select {
		case <-done:
			// Request completed normally
			return
		case <-ctx.Done():
			// Request timed out
			c.JSON(http.StatusRequestTimeout, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Request timeout",
					"code":    "REQUEST_TIMEOUT",
				},
			})
			c.Abort()
			return
		}
	}
}

// InputSanitizationMiddleware sanitizes request inputs
func InputSanitizationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Sanitize query parameters
		query := c.Request.URL.Query()
		for key, values := range query {
			for i, value := range values {
				query[key][i] = sanitizeInput(value)
			}
		}
		c.Request.URL.RawQuery = query.Encode()

		// Sanitize form data
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			if err := c.Request.ParseForm(); err == nil {
				for key, values := range c.Request.PostForm {
					for i, value := range values {
						c.Request.PostForm[key][i] = sanitizeInput(value)
					}
				}
			}
		}

		c.Next()
	}
}

// sanitizeInput removes potentially dangerous characters from input
func sanitizeInput(input string) string {
	// Remove null bytes
	input = strings.ReplaceAll(input, "\x00", "")
	
	// Remove or escape dangerous characters
	input = strings.ReplaceAll(input, "<", "&lt;")
	input = strings.ReplaceAll(input, ">", "&gt;")
	input = strings.ReplaceAll(input, "\"", "&quot;")
	input = strings.ReplaceAll(input, "'", "&#39;")
	input = strings.ReplaceAll(input, "&", "&amp;")
	
	return input
}

// RequestMetricsMiddleware collects security-related metrics
func RequestMetricsMiddleware(logger utils.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		c.Next()
		
		duration := time.Since(start)
		
		// Log security metrics
		logger.WithFields(map[string]interface{}{
			"method":       c.Request.Method,
			"path":         c.Request.URL.Path,
			"status":       c.Writer.Status(),
			"duration":     duration.Milliseconds(),
			"client_ip":    c.ClientIP(),
			"user_agent":   c.Request.UserAgent(),
			"request_size": c.Request.ContentLength,
			"response_size": c.Writer.Size(),
		}).Info("Request metrics")
	}
}