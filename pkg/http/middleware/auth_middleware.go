package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/auth"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"golang.org/x/time/rate"
)

// AuthRateLimitMiddleware implements rate limiting specifically for auth endpoints
func AuthRateLimitMiddleware(maxAttempts int, windowDuration time.Duration) gin.HandlerFunc {
	// Simple in-memory rate limiter by IP
	type rateLimiter struct {
		limiter  *rate.Limiter
		lastSeen time.Time
	}

	var (
		mu      sync.RWMutex
		clients = make(map[string]*rateLimiter)
	)

	// Cleanup old entries periodically
	go func() {
		ticker := time.NewTicker(time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			mu.Lock()
			for ip, client := range clients {
				if time.Since(client.lastSeen) > windowDuration {
					delete(clients, ip)
				}
			}
			mu.Unlock()
		}
	}()

	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		mu.Lock()
		client, exists := clients[clientIP]
		if !exists {
			client = &rateLimiter{
				limiter:  rate.NewLimiter(rate.Every(windowDuration/time.Duration(maxAttempts)), maxAttempts),
				lastSeen: time.Now(),
			}
			clients[clientIP] = client
		}
		client.lastSeen = time.Now()
		mu.Unlock()

		if !client.limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Too many authentication attempts. Please try again later.",
					"code":    "AUTH_RATE_LIMIT_EXCEEDED",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// TenantContextMiddleware extracts tenant information from JWT claims
func TenantContextMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try to get tenant ID from JWT claims first
		// if claims, exists := c.Get(JWTClaimsKey); exists {
		// 	if jwtClaims, ok := claims.(*models.JWTClaims); ok {
		// 		c.Set("tenant_id", jwtClaims.CurrentTenantID)
		// 		c.Set("website_id", jwtClaims.WebsiteID)
		// 		c.Next()
		// 		return
		// 	}
		// }

		// Fallback to header or query parameter
		tenantID := c.GetHeader("X-Tenant-ID")
		if tenantID == "" {
			tenantID = c.Query("tenant_id")
		}

		if tenantID != "" {
			c.Set("tenant_id", tenantID)
		}

		c.Next()
	}
}

// SessionValidationMiddleware validates and manages user sessions
func SessionValidationMiddleware(sessionService auth.SessionService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get session ID from JWT claims
		if claims, exists := c.Get(JWTClaimsKey); exists {
			if jwtClaims, ok := claims.(*models.JWTClaims); ok && jwtClaims.SessionID != nil {
				ctx := context.Background()
				sessionID := *jwtClaims.SessionID

				// Validate session exists and is active
				sessionIDStr := fmt.Sprintf("%d", sessionID)
				session, err := sessionService.GetSession(ctx, sessionIDStr)
				if err != nil {
					// Session validation failed - return unauthorized
					c.JSON(http.StatusUnauthorized, gin.H{
						"error": "Session validation failed",
						"code":  "INVALID_SESSION",
					})
					c.Abort()
					return
				}

				// Check if session is still active
				if !session.IsActive {
					c.JSON(http.StatusUnauthorized, gin.H{
						"error": "Session is no longer active",
						"code":  "INACTIVE_SESSION",
					})
					c.Abort()
					return
				}

				// Check if session has expired
				if time.Now().After(session.ExpiresAt) {
					c.JSON(http.StatusUnauthorized, gin.H{
						"error": "Session has expired",
						"code":  "EXPIRED_SESSION",
					})
					c.Abort()
					return
				}

				// Update last activity
				session.LastActivity = time.Now()
				if err := sessionService.UpdateSession(ctx, session); err != nil {
					// Log error but don't fail the request
					// In production, you should log this properly
				}

				// Store session in context for downstream handlers
				c.Set("session", session)
			}
		}

		c.Next()
	}
}

// SessionCleanupMiddleware validates and cleans up expired sessions
func SessionCleanupMiddleware(sessionService auth.SessionService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// This middleware is for periodic cleanup, not per-request validation
		// The actual session validation is handled by SessionValidationMiddleware
		c.Next()
	}
}

// SecurityHeadersMiddleware adds security headers to responses
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Content Security Policy
		c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:; media-src 'self'; object-src 'none'; frame-src 'none'")

		// X-Frame-Options
		c.Header("X-Frame-Options", "DENY")

		// X-Content-Type-Options
		c.Header("X-Content-Type-Options", "nosniff")

		// X-XSS-Protection
		c.Header("X-XSS-Protection", "1; mode=block")

		// Referrer-Policy
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

		// Strict-Transport-Security (HSTS)
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload")

		// Permissions-Policy
		c.Header("Permissions-Policy", "camera=(), microphone=(), geolocation=(), interest-cohort=()")

		// X-Permitted-Cross-Domain-Policies
		c.Header("X-Permitted-Cross-Domain-Policies", "none")

		// Clear Server header
		c.Header("Server", "")

		c.Next()
	}
}

// EnhancedCORSMiddleware provides more comprehensive CORS handling for auth endpoints
func EnhancedCORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.GetHeader("Origin")

		// Allow specific origins or all origins for development
		// In production, this should be more restrictive
		if origin != "" {
			c.Header("Access-Control-Allow-Origin", origin)
			c.Header("Access-Control-Allow-Credentials", "true")
		} else {
			c.Header("Access-Control-Allow-Origin", "*")
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, PATCH, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-Request-ID, X-Tenant-ID, X-Website-ID")
		c.Header("Access-Control-Expose-Headers", "X-Request-ID, X-Rate-Limit-Remaining, X-Rate-Limit-Reset")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		c.Next()
	}
}

// RequireAuthentication ensures the request is authenticated
func RequireAuthentication() gin.HandlerFunc {
	return func(c *gin.Context) {
		_, exists := c.Get(JWTClaimsKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Authentication required",
					"code":    "AUTHENTICATION_REQUIRED",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePermissions validates user permissions
func RequirePermissions(requiredPermissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get(JWTClaimsKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Authentication required",
					"code":    "AUTHENTICATION_REQUIRED",
				},
			})
			c.Abort()
			return
		}

		jwtClaims, ok := claims.(*models.JWTClaims)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error": gin.H{
					"message": "Invalid authentication data",
					"code":    "INVALID_AUTH_DATA",
				},
			})
			c.Abort()
			return
		}

		// Check permissions
		for _, permission := range requiredPermissions {
			if !jwtClaims.HasScopeJWT(permission) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error": gin.H{
						"message":             "Insufficient permissions",
						"code":                "INSUFFICIENT_PERMISSIONS",
						"required_permission": permission,
					},
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// RequireAdminRole ensures the user has admin role
func RequireAdminRole() gin.HandlerFunc {
	return RequireRole(models.UserRoleAdmin)
}

// RequestLoggingMiddleware logs authentication-related requests
func RequestLoggingMiddleware(logger utils.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Get user info if available
		var userID uint
		var tenantID uint
		if claims, exists := c.Get(JWTClaimsKey); exists {
			if jwtClaims, ok := claims.(*models.JWTClaims); ok {
				userID = jwtClaims.UserID
				//tenantID = jwtClaims.CurrentTenantID
			}
		}

		c.Next()

		// Log the request
		duration := time.Since(start)
		logger.WithFields(map[string]interface{}{
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"status":     c.Writer.Status(),
			"duration":   duration.String(),
			"user_agent": c.Request.UserAgent(),
			"remote_ip":  c.ClientIP(),
			"user_id":    userID,
			"tenant_id":  tenantID,
			"request_id": c.GetString("request_id"),
		}).Info("Auth request")
	}
}

// ValidateJSONContentType ensures requests have correct content type
func ValidateJSONContentType() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")
			if contentType != "" && !strings.HasPrefix(contentType, "application/json") {
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"success": false,
					"error": gin.H{
						"message": "Content-Type must be application/json",
						"code":    "INVALID_CONTENT_TYPE",
					},
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// PreventBruteForceMiddleware implements brute force protection
func PreventBruteForceMiddleware(rateLimitService services.RateLimitingService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// For login attempts, check if the account is locked
		if c.Request.URL.Path == "/auth/login" && c.Request.Method == "POST" {
			// Get email from request body (this is a simplified approach)
			// In production, you might want to parse the body more carefully
			clientIP := c.ClientIP()

			// Check rate limiting
			locked, unlocksAt, err := rateLimitService.IsAccountLocked(c.Request.Context(), clientIP)
			if err != nil {
				// Log error but don't fail the request
				return
			}

			if locked {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"success": false,
					"error": gin.H{
						"message":    "Account temporarily locked due to too many failed attempts",
						"code":       "ACCOUNT_LOCKED",
						"unlocks_at": unlocksAt.Format(time.RFC3339),
					},
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}
