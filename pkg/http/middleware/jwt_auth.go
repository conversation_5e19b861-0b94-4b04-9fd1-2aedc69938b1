package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
)

// JWT context keys
const (
	JWTClaimsKey = "jwt_claims"
	UserIDKey    = "user_id"
	TenantIDKey  = "tenant_id"
	WebsiteIDKey = "website_id"
)

// JWTAuthMiddleware creates a JWT authentication middleware
func JWTAuthMiddleware(jwtService services.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Missing authorization header",
			})
			c.Abort()
			return
		}

		// Check Bearer scheme
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		tokenString := parts[1]

		// Validate token
		claims, err := jwtService.ValidateAccessToken(tokenString)
		if err != nil {
			var message string
			var statusCode int = http.StatusUnauthorized

			switch err {
			case services.ErrTokenExpired:
				message = "Token expired"
			case services.ErrTokenNotValidYet:
				message = "Token not valid yet"
			case services.ErrTokenBlacklisted:
				message = "Token has been revoked"
			case services.ErrInvalidTokenType:
				message = "Invalid token type"
			case services.ErrMissingTenantID:
				message = "Invalid token: missing tenant information"
			case services.ErrMissingUserID:
				message = "Invalid token: missing user information"
			default:
				message = "Invalid token"
			}

			c.JSON(statusCode, gin.H{
				"success": false,
				"error":   message,
			})
			c.Abort()
			return
		}

		// Set claims in context
		c.Set(JWTClaimsKey, claims)
		c.Set(UserIDKey, claims.UserID)
		//c.Set(TenantIDKey, claims.CurrentTenantID)
		//c.Set(WebsiteIDKey, claims.WebsiteID)

		c.Next()
	}
}

// OptionalJWTAuthMiddleware creates an optional JWT authentication middleware
// It validates the token if present but doesn't require it
func OptionalJWTAuthMiddleware(jwtService services.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// No token, continue without authentication
			c.Next()
			return
		}

		// Check Bearer scheme
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			// Invalid format, continue without authentication
			c.Next()
			return
		}

		tokenString := parts[1]

		// Validate token
		claims, err := jwtService.ValidateAccessToken(tokenString)
		if err == nil {
			// Valid token, set claims in context
			c.Set(JWTClaimsKey, claims)
			c.Set(UserIDKey, claims.UserID)
			//c.Set(TenantIDKey, claims.CurrentTenantID)
			//c.Set(WebsiteIDKey, claims.WebsiteID)
		}

		c.Next()
	}
}

// RequireScopes creates a middleware that requires specific JWT scopes
func RequireScopes(scopes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get(JWTClaimsKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Authentication required",
			})
			c.Abort()
			return
		}

		jwtClaims, ok := claims.(*models.JWTClaims)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Invalid claims format",
			})
			c.Abort()
			return
		}

		// Check if user has all required scopes
		for _, scope := range scopes {
			if !jwtClaims.HasScopeJWT(scope) {
				c.JSON(http.StatusForbidden, gin.H{
					"success":        false,
					"error":          "Insufficient permissions",
					"required_scope": scope,
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// RequireRole creates a middleware that requires a specific user role
// NOTE: In the new user-only JWT architecture, roles are determined by tenant membership
// and must be fetched from the database. This middleware is deprecated in favor of
// tenant-specific role checking.
func RequireRole(roles ...models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get(JWTClaimsKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Authentication required",
			})
			c.Abort()
			return
		}

		jwtClaims, ok := claims.(*models.JWTClaims)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Invalid claims format",
			})
			c.Abort()
			return
		}

		// TODO: Implement role checking via tenant membership lookup
		// For now, allow all authenticated users since roles are no longer in JWT
		// This should be replaced with proper tenant membership role checking
		_ = jwtClaims // Use the variable to avoid unused variable error
		
		c.Next()
	}
}

// GetJWTClaims extracts JWT claims from context
func GetJWTClaims(c *gin.Context) (*models.JWTClaims, bool) {
	claims, exists := c.Get(JWTClaimsKey)
	if !exists {
		return nil, false
	}

	jwtClaims, ok := claims.(*models.JWTClaims)
	return jwtClaims, ok
}

// GetUserID extracts user ID from context
func GetUserID(c *gin.Context) (uint, bool) {
	userID, exists := c.Get(UserIDKey)
	if !exists {
		return 0, false
	}

	id, ok := userID.(uint)
	return id, ok
}

// GetTenantIDFromJWT extracts tenant ID from JWT context
func GetTenantIDFromJWT(c *gin.Context) (uint, bool) {
	tenantID, exists := c.Get(TenantIDKey)
	if !exists {
		return 0, false
	}

	id, ok := tenantID.(uint)
	return id, ok
}

// GetWebsiteID extracts website ID from context
func GetWebsiteID(c *gin.Context) (uint, bool) {
	websiteID, exists := c.Get(WebsiteIDKey)
	if !exists {
		return 0, false
	}

	id, ok := websiteID.(uint)
	return id, ok
}

// RequireRoleMiddleware creates a middleware that requires specific roles (string-based)
// This is a temporary implementation that accepts string roles for compatibility
func RequireRoleMiddleware(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get(JWTClaimsKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Authentication required",
			})
			c.Abort()
			return
		}

		jwtClaims, ok := claims.(*models.JWTClaims)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Invalid claims format",
			})
			c.Abort()
			return
		}

		// TODO: Implement proper role checking via tenant membership lookup
		// For now, allow all authenticated users since roles are no longer in JWT
		// This should be replaced with proper tenant membership role checking
		_ = jwtClaims // Use the variable to avoid unused variable error
		_ = roles     // Use roles to avoid unused variable error
		
		// TEMPORARY: Allow all authenticated users
		// In a real implementation, you would:
		// 1. Get user's tenant memberships from database
		// 2. Check if any membership has the required role
		// 3. Return 403 if user doesn't have required role
		
		c.Next()
	}
}
