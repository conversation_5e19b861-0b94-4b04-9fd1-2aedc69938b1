package storage

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// minioStorage implements Storage and BucketStorage interfaces for MinIO
type minioStorage struct {
	client *minio.Client
	config *Config
}

// NewMinIOStorage creates a new MinIO storage instance
func NewMinIOStorage(config *Config) (BucketStorage, error) {
	if config.Endpoint == "" {
		return nil, fmt.Errorf("MinIO endpoint is required")
	}
	if config.AccessKey == "" {
		return nil, fmt.Errorf("MinIO access key is required")
	}
	if config.SecretKey == "" {
		return nil, fmt.Errorf("MinIO secret key is required")
	}
	if config.Bucket == "" {
		return nil, fmt.Errorf("MinIO bucket name is required")
	}

	// Initialize MinIO client
	client, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKey, config.SecretKey, ""),
		Secure: config.UseSSL,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO client: %w", err)
	}

	storage := &minioStorage{
		client: client,
		config: config,
	}

	return storage, nil
}

// Put stores a file in MinIO
func (m *minioStorage) Put(ctx context.Context, key string, reader io.Reader, options *PutOptions) error {
	key = m.sanitizeKey(key)
	
	// Convert options to MinIO options
	minioOptions := minio.PutObjectOptions{}
	if options != nil {
		if options.ContentType != "" {
			minioOptions.ContentType = options.ContentType
		}
		if options.ContentLength > 0 {
			minioOptions.UserMetadata = make(map[string]string)
			minioOptions.UserMetadata["content-length"] = fmt.Sprintf("%d", options.ContentLength)
		}
		if options.Metadata != nil {
			if minioOptions.UserMetadata == nil {
				minioOptions.UserMetadata = make(map[string]string)
			}
			for k, v := range options.Metadata {
				minioOptions.UserMetadata[k] = v
			}
		}
		if options.StorageClass != "" {
			minioOptions.StorageClass = options.StorageClass
		}
		if options.CacheControl != "" {
			minioOptions.CacheControl = options.CacheControl
		}
		if options.ContentEncoding != "" {
			minioOptions.ContentEncoding = options.ContentEncoding
		}
		if options.ServerSideEncryption != "" {
			// For MinIO, we'll handle SSE differently based on the encryption type
			// This is a placeholder - actual implementation would depend on specific SSE requirements
			minioOptions.UserMetadata["x-amz-server-side-encryption"] = options.ServerSideEncryption
		}
	}

	// Upload file
	_, err := m.client.PutObject(ctx, m.config.Bucket, key, reader, -1, minioOptions)
	if err != nil {
		return fmt.Errorf("failed to upload file to MinIO: %w", err)
	}

	return nil
}

// Get retrieves a file from MinIO
func (m *minioStorage) Get(ctx context.Context, key string) (io.ReadCloser, error) {
	key = m.sanitizeKey(key)
	
	obj, err := m.client.GetObject(ctx, m.config.Bucket, key, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get file from MinIO: %w", err)
	}
	
	return obj, nil
}

// Delete removes a file from MinIO
func (m *minioStorage) Delete(ctx context.Context, key string) error {
	key = m.sanitizeKey(key)
	
	err := m.client.RemoveObject(ctx, m.config.Bucket, key, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete file from MinIO: %w", err)
	}
	
	return nil
}

// Exists checks if a file exists in MinIO
func (m *minioStorage) Exists(ctx context.Context, key string) (bool, error) {
	key = m.sanitizeKey(key)
	
	_, err := m.client.StatObject(ctx, m.config.Bucket, key, minio.StatObjectOptions{})
	if err != nil {
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return false, nil
		}
		return false, fmt.Errorf("failed to check if file exists in MinIO: %w", err)
	}
	
	return true, nil
}

// GetInfo returns file information from MinIO
func (m *minioStorage) GetInfo(ctx context.Context, key string) (*FileInfo, error) {
	key = m.sanitizeKey(key)
	
	objInfo, err := m.client.StatObject(ctx, m.config.Bucket, key, minio.StatObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get file info from MinIO: %w", err)
	}
	
	return &FileInfo{
		Key:          key,
		Size:         objInfo.Size,
		ContentType:  objInfo.ContentType,
		ETag:         objInfo.ETag,
		LastModified: objInfo.LastModified,
		Metadata:     objInfo.UserMetadata,
		StorageClass: objInfo.StorageClass,
		IsPublic:     false, // MinIO doesn't have public/private concept like S3
		URL:          m.generateURL(key),
		PublicURL:    m.generatePublicURL(key),
	}, nil
}

// GetURL returns a presigned URL for the file
func (m *minioStorage) GetURL(ctx context.Context, key string, expiry time.Duration) (string, error) {
	key = m.sanitizeKey(key)
	
	reqParams := make(url.Values)
	presignedURL, err := m.client.PresignedGetObject(ctx, m.config.Bucket, key, expiry, reqParams)
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %w", err)
	}
	
	return presignedURL.String(), nil
}

// GetPublicURL returns a public URL for the file
func (m *minioStorage) GetPublicURL(ctx context.Context, key string) (string, error) {
	key = m.sanitizeKey(key)
	return m.generatePublicURL(key), nil
}

// List lists objects in MinIO
func (m *minioStorage) List(ctx context.Context, prefix string, options *ListOptions) (*ListResult, error) {
	prefix = m.sanitizeKey(prefix)
	
	listOptions := minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	}
	
	if options != nil {
		if options.MaxKeys > 0 {
			listOptions.MaxKeys = options.MaxKeys
		}
	}
	
	var files []*FileInfo
	var commonPrefixes []string
	
	objectCh := m.client.ListObjects(ctx, m.config.Bucket, listOptions)
	for object := range objectCh {
		if object.Err != nil {
			return nil, fmt.Errorf("failed to list objects: %w", object.Err)
		}
		
		files = append(files, &FileInfo{
			Key:          object.Key,
			Size:         object.Size,
			ContentType:  object.ContentType,
			ETag:         object.ETag,
			LastModified: object.LastModified,
			StorageClass: object.StorageClass,
			IsPublic:     false,
			URL:          m.generateURL(object.Key),
			PublicURL:    m.generatePublicURL(object.Key),
		})
	}
	
	return &ListResult{
		Files:          files,
		CommonPrefixes: commonPrefixes,
		IsTruncated:    false,
	}, nil
}

// ListAll lists all objects with the given prefix
func (m *minioStorage) ListAll(ctx context.Context, prefix string) ([]*FileInfo, error) {
	result, err := m.List(ctx, prefix, nil)
	if err != nil {
		return nil, err
	}
	return result.Files, nil
}

// PutMultiple stores multiple files in MinIO
func (m *minioStorage) PutMultiple(ctx context.Context, files []*FileUpload) error {
	for _, file := range files {
		if err := m.Put(ctx, file.Key, file.Reader, file.Options); err != nil {
			return fmt.Errorf("failed to upload file %s: %w", file.Key, err)
		}
	}
	return nil
}

// DeleteMultiple removes multiple files from MinIO
func (m *minioStorage) DeleteMultiple(ctx context.Context, keys []string) error {
	objectsCh := make(chan minio.ObjectInfo)
	
	// Send object names to channel
	go func() {
		defer close(objectsCh)
		for _, key := range keys {
			objectsCh <- minio.ObjectInfo{Key: m.sanitizeKey(key)}
		}
	}()
	
	// Remove objects
	errorCh := m.client.RemoveObjects(ctx, m.config.Bucket, objectsCh, minio.RemoveObjectsOptions{})
	for removeErr := range errorCh {
		if removeErr.Err != nil {
			return fmt.Errorf("failed to delete file %s: %w", removeErr.ObjectName, removeErr.Err)
		}
	}
	
	return nil
}

// Copy copies an object within MinIO
func (m *minioStorage) Copy(ctx context.Context, srcKey, dstKey string) error {
	srcKey = m.sanitizeKey(srcKey)
	dstKey = m.sanitizeKey(dstKey)
	
	// Copy source
	src := minio.CopySrcOptions{
		Bucket: m.config.Bucket,
		Object: srcKey,
	}
	
	// Copy destination
	dst := minio.CopyDestOptions{
		Bucket: m.config.Bucket,
		Object: dstKey,
	}
	
	_, err := m.client.CopyObject(ctx, dst, src)
	if err != nil {
		return fmt.Errorf("failed to copy object: %w", err)
	}
	
	return nil
}

// Move moves an object within MinIO
func (m *minioStorage) Move(ctx context.Context, srcKey, dstKey string) error {
	// Copy the object
	if err := m.Copy(ctx, srcKey, dstKey); err != nil {
		return err
	}
	
	// Delete the source
	if err := m.Delete(ctx, srcKey); err != nil {
		return fmt.Errorf("failed to delete source after move: %w", err)
	}
	
	return nil
}

// Connect initializes the MinIO connection
func (m *minioStorage) Connect() error {
	// Test connection by checking if bucket exists
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	exists, err := m.client.BucketExists(ctx, m.config.Bucket)
	if err != nil {
		return fmt.Errorf("failed to connect to MinIO: %w", err)
	}
	
	if !exists {
		// Create bucket if it doesn't exist
		err = m.client.MakeBucket(ctx, m.config.Bucket, minio.MakeBucketOptions{
			Region: m.config.Region,
		})
		if err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
	}
	
	return nil
}

// Disconnect closes the MinIO connection
func (m *minioStorage) Disconnect() error {
	// MinIO client doesn't require explicit disconnection
	return nil
}

// Ping tests the MinIO connection
func (m *minioStorage) Ping(ctx context.Context) error {
	_, err := m.client.BucketExists(ctx, m.config.Bucket)
	if err != nil {
		return fmt.Errorf("MinIO ping failed: %w", err)
	}
	return nil
}

// BucketStorage interface methods

// CreateBucket creates a new MinIO bucket
func (m *minioStorage) CreateBucket(ctx context.Context, bucket string) error {
	err := m.client.MakeBucket(ctx, bucket, minio.MakeBucketOptions{
		Region: m.config.Region,
	})
	if err != nil {
		return fmt.Errorf("failed to create bucket: %w", err)
	}
	return nil
}

// DeleteBucket deletes a MinIO bucket
func (m *minioStorage) DeleteBucket(ctx context.Context, bucket string) error {
	err := m.client.RemoveBucket(ctx, bucket)
	if err != nil {
		return fmt.Errorf("failed to delete bucket: %w", err)
	}
	return nil
}

// BucketExists checks if a bucket exists
func (m *minioStorage) BucketExists(ctx context.Context, bucket string) (bool, error) {
	exists, err := m.client.BucketExists(ctx, bucket)
	if err != nil {
		return false, fmt.Errorf("failed to check bucket existence: %w", err)
	}
	return exists, nil
}

// ListBuckets lists all buckets
func (m *minioStorage) ListBuckets(ctx context.Context) ([]string, error) {
	buckets, err := m.client.ListBuckets(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list buckets: %w", err)
	}
	
	var names []string
	for _, bucket := range buckets {
		names = append(names, bucket.Name)
	}
	
	return names, nil
}

// SetBucketPolicy sets bucket policy
func (m *minioStorage) SetBucketPolicy(ctx context.Context, bucket string, policy *BucketPolicy) error {
	// Convert BucketPolicy to JSON string
	policyJSON := ""
	// This would require proper policy JSON serialization
	
	err := m.client.SetBucketPolicy(ctx, bucket, policyJSON)
	if err != nil {
		return fmt.Errorf("failed to set bucket policy: %w", err)
	}
	return nil
}

// GetBucketPolicy gets bucket policy
func (m *minioStorage) GetBucketPolicy(ctx context.Context, bucket string) (*BucketPolicy, error) {
	policy, err := m.client.GetBucketPolicy(ctx, bucket)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket policy: %w", err)
	}
	
	// Parse policy JSON to BucketPolicy struct
	// This would require proper policy JSON deserialization
	_ = policy
	
	return &BucketPolicy{}, nil
}

// DeleteBucketPolicy deletes bucket policy
func (m *minioStorage) DeleteBucketPolicy(ctx context.Context, bucket string) error {
	err := m.client.SetBucketPolicy(ctx, bucket, "")
	if err != nil {
		return fmt.Errorf("failed to delete bucket policy: %w", err)
	}
	return nil
}

// SetBucketVersioning sets bucket versioning
func (m *minioStorage) SetBucketVersioning(ctx context.Context, bucket string, enabled bool) error {
	// MinIO versioning configuration
	versioningConfig := minio.BucketVersioningConfiguration{
		Status: "Enabled",
	}
	if !enabled {
		versioningConfig.Status = "Suspended"
	}
	
	err := m.client.SetBucketVersioning(ctx, bucket, versioningConfig)
	if err != nil {
		return fmt.Errorf("failed to set bucket versioning: %w", err)
	}
	return nil
}

// GetBucketVersioning gets bucket versioning status
func (m *minioStorage) GetBucketVersioning(ctx context.Context, bucket string) (bool, error) {
	versioningConfig, err := m.client.GetBucketVersioning(ctx, bucket)
	if err != nil {
		return false, fmt.Errorf("failed to get bucket versioning: %w", err)
	}
	
	return versioningConfig.Status == "Enabled", nil
}

// CreateMultipartUpload creates a multipart upload
func (m *minioStorage) CreateMultipartUpload(ctx context.Context, key string, options *PutOptions) (*MultipartUpload, error) {
	key = m.sanitizeKey(key)
	
	// MinIO handles multipart uploads automatically in PutObject
	// This is a placeholder implementation
	return &MultipartUpload{
		Key:      key,
		UploadID: fmt.Sprintf("minio-multipart-%d", time.Now().Unix()),
		Bucket:   m.config.Bucket,
	}, nil
}

// UploadPart uploads a part for multipart upload
func (m *minioStorage) UploadPart(ctx context.Context, upload *MultipartUpload, partNumber int, reader io.Reader) (*UploadPart, error) {
	// MinIO handles multipart uploads automatically
	// This would require implementing custom multipart logic
	return &UploadPart{
		PartNumber: partNumber,
		ETag:       fmt.Sprintf("etag-%d", partNumber),
		Size:       0, // Would need to calculate actual size
	}, nil
}

// CompleteMultipartUpload completes a multipart upload
func (m *minioStorage) CompleteMultipartUpload(ctx context.Context, upload *MultipartUpload, parts []*UploadPart) error {
	// MinIO handles multipart uploads automatically
	return nil
}

// AbortMultipartUpload aborts a multipart upload
func (m *minioStorage) AbortMultipartUpload(ctx context.Context, upload *MultipartUpload) error {
	// MinIO handles multipart uploads automatically
	return nil
}

// Helper methods

func (m *minioStorage) sanitizeKey(key string) string {
	// Remove leading slashes and clean the path
	key = strings.TrimPrefix(key, "/")
	key = filepath.Clean(key)
	
	// Add base path if configured
	if m.config.BasePath != "" {
		key = filepath.Join(m.config.BasePath, key)
	}
	
	return key
}

func (m *minioStorage) generateURL(key string) string {
	scheme := "http"
	if m.config.UseSSL {
		scheme = "https"
	}
	
	if m.config.PublicURL != "" {
		return fmt.Sprintf("%s/%s", strings.TrimSuffix(m.config.PublicURL, "/"), key)
	}
	
	return fmt.Sprintf("%s://%s/%s/%s", scheme, m.config.Endpoint, m.config.Bucket, key)
}

func (m *minioStorage) generatePublicURL(key string) string {
	if m.config.CDNEndpoint != "" {
		return fmt.Sprintf("%s/%s", strings.TrimSuffix(m.config.CDNEndpoint, "/"), key)
	}
	return m.generateURL(key)
}

// generateTenantPath creates a tenant-specific path for multi-tenancy
func (m *minioStorage) generateTenantPath(tenantID uint, websiteID uint, originalPath string) string {
	return fmt.Sprintf("tenant-%d/website-%d/%s", tenantID, websiteID, strings.TrimPrefix(originalPath, "/"))
}