# OpenTelemetry Dependencies Required for pkg/tracing

# Add these dependencies to the main go.mod file:

go get go.opentelemetry.io/otel@latest
go get go.opentelemetry.io/otel/trace@latest
go get go.opentelemetry.io/otel/sdk@latest
go get go.opentelemetry.io/otel/exporters/jaeger@latest
go get go.opentelemetry.io/otel/exporters/otlp/otlptrace@latest
go get go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp@latest
go get go.opentelemetry.io/otel/propagation@latest
go get go.opentelemetry.io/otel/semconv/v1.26.0@latest
go get go.opentelemetry.io/otel/attribute@latest
go get go.opentelemetry.io/otel/codes@latest
go get go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin@latest

# These dependencies provide:
# - Core OpenTelemetry SDK functionality
# - Trace creation and management
# - J<PERSON>ger exporter for distributed tracing
# - OTLP exporter for OpenTelemetry Protocol
# - Context propagation support
# - Semantic conventions for attributes
# - Gin framework integration (optional)

# After adding these dependencies, run:
# go mod tidy
# go mod verify