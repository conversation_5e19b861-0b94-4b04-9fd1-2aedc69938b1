package tracing

import (
	"context"
	"testing"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

func TestNewTracer(t *testing.T) {
	tests := []struct {
		name      string
		config    *Config
		wantError bool
	}{
		{
			name:      "valid config",
			config:    DefaultConfig(),
			wantError: false,
		},
		{
			name:      "nil config",
			config:    nil,
			wantError: false,
		},
		{
			name: "invalid config",
			config: &Config{
				ServiceName: "",
				Enabled:     true,
			},
			wantError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tracer, err := NewTracer(tt.config)
			
			if tt.wantError {
				if err == nil {
					t.Error("Expected error, got nil")
				}
			} else {
				if err != nil {
					t.<PERSON>("Expected no error, got %v", err)
				}
				if tracer == nil {
					t.Error("Expected tracer, got nil")
				}
			}
		})
	}
}

func TestTracerInitialize(t *testing.T) {
	tests := []struct {
		name      string
		config    *Config
		wantError bool
	}{
		{
			name:      "valid config",
			config:    TestingConfig(),
			wantError: false,
		},
		{
			name:      "disabled config",
			config:    &Config{ServiceName: "test", Enabled: false},
			wantError: false,
		},
		{
			name: "invalid config",
			config: &Config{
				ServiceName: "",
				Enabled:     true,
			},
			wantError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tracer, err := NewTracer(nil)
			if err != nil {
				t.Fatalf("Failed to create tracer: %v", err)
			}
			
			err = tracer.Initialize(tt.config)
			
			if tt.wantError {
				if err == nil {
					t.Error("Expected error, got nil")
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
				}
				
				// Test that tracer is enabled/disabled correctly
				if tt.config.Enabled != tracer.IsEnabled() {
					t.Errorf("Enabled = %v, want %v", tracer.IsEnabled(), tt.config.Enabled)
				}
			}
		})
	}
}

func TestTracerInitializeAlreadyInitialized(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	// Initialize once
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	
	// Try to initialize again
	err = tracer.Initialize(TestingConfig())
	if err != ErrTracerAlreadyInitialized {
		t.Errorf("Expected ErrTracerAlreadyInitialized, got %v", err)
	}
}

func TestTracerShutdown(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	// Initialize tracer
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	
	// Shutdown tracer
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	err = tracer.Shutdown(ctx)
	if err != nil {
		t.Errorf("Failed to shutdown tracer: %v", err)
	}
}

func TestTracerShutdownNotInitialized(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	// Try to shutdown without initializing
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	err = tracer.Shutdown(ctx)
	if err != ErrTracerNotInitialized {
		t.Errorf("Expected ErrTracerNotInitialized, got %v", err)
	}
}

func TestTracerStartSpan(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test enabled tracer
	ctx, span := tracer.StartSpan(context.Background(), "test-span")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	if !span.IsRecording() {
		t.Error("Expected span to be recording")
	}
	span.End()
	
	// Test with options
	ctx, span = tracer.StartSpan(ctx, "test-span-with-options",
		WithSpanKind(trace.SpanKindServer),
		WithAttributes(attribute.String("key", "value")),
	)
	if span == nil {
		t.Error("Expected span, got nil")
	}
	span.End()
}

func TestTracerStartSpanDisabled(t *testing.T) {
	config := TestingConfig()
	config.Enabled = false
	
	tracer, err := NewTracer(config)
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(config)
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	
	// Test disabled tracer
	ctx, span := tracer.StartSpan(context.Background(), "test-span")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	if span.IsRecording() {
		t.Error("Expected span not to be recording when tracer is disabled")
	}
	span.End()
}

func TestTracerGetSpanFromContext(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Create a span
	ctx, span := tracer.StartSpan(context.Background(), "test-span")
	defer span.End()
	
	// Get span from context
	retrievedSpan := tracer.GetSpanFromContext(ctx)
	if retrievedSpan == nil {
		t.Error("Expected span, got nil")
	}
	if !retrievedSpan.IsRecording() {
		t.Error("Expected span to be recording")
	}
}

func TestTracerGetSpanFromContextNoSpan(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Get span from context without creating one
	retrievedSpan := tracer.GetSpanFromContext(context.Background())
	if retrievedSpan == nil {
		t.Error("Expected noop span, got nil")
	}
	if retrievedSpan.IsRecording() {
		t.Error("Expected span not to be recording when no span in context")
	}
}

func TestTracerContextPropagation(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Create a span
	ctx, span := tracer.StartSpan(context.Background(), "test-span")
	defer span.End()
	
	// Create a carrier
	headers := make(map[string][]string)
	carrier := NewHTTPCarrier(headers)
	
	// Inject context
	err = tracer.InjectTraceContext(ctx, carrier)
	if err != nil {
		t.Errorf("Failed to inject trace context: %v", err)
	}
	
	// Extract context
	extractedCtx := tracer.ExtractTraceContext(context.Background(), carrier)
	if extractedCtx == nil {
		t.Error("Expected context, got nil")
	}
}

func TestOtelSpan(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Create a span
	ctx, span := tracer.StartSpan(context.Background(), "test-span")
	defer span.End()
	
	// Test span methods
	span.SetAttribute("string-key", "string-value")
	span.SetAttribute("int-key", 42)
	span.SetAttribute("bool-key", true)
	span.SetAttribute("float-key", 3.14)
	
	span.SetAttributes(
		attribute.String("attr1", "value1"),
		attribute.Int("attr2", 123),
	)
	
	span.SetStatus(codes.Ok, "success")
	
	testErr := WrapError(ErrTracerNotInitialized, "test error")
	span.SetError(testErr)
	
	span.AddEvent("test-event")
	span.AddEventWithAttributes("test-event-with-attrs", attribute.String("event-key", "event-value"))
	
	span.SetName("updated-span-name")
	
	// Test span context methods
	if !span.IsRecording() {
		t.Error("Expected span to be recording")
	}
	
	spanContext := span.SpanContext()
	if !spanContext.IsValid() {
		t.Error("Expected span context to be valid")
	}
	
	traceID := span.TraceID()
	if !traceID.IsValid() {
		t.Error("Expected trace ID to be valid")
	}
	
	spanID := span.SpanID()
	if !spanID.IsValid() {
		t.Error("Expected span ID to be valid")
	}
}

func TestNoopSpan(t *testing.T) {
	span := &noopSpan{}
	
	// Test that all methods can be called without panicking
	span.End()
	span.SetAttribute("key", "value")
	span.SetAttributes(attribute.String("attr", "value"))
	span.SetStatus(codes.Ok, "success")
	span.SetError(ErrTracerNotInitialized)
	span.AddEvent("event")
	span.AddEventWithAttributes("event", attribute.String("attr", "value"))
	span.SetName("name")
	
	// Test return values
	if span.IsRecording() {
		t.Error("Expected noop span not to be recording")
	}
	
	spanContext := span.SpanContext()
	if spanContext.IsValid() {
		t.Error("Expected noop span context to be invalid")
	}
	
	traceID := span.TraceID()
	if traceID.IsValid() {
		t.Error("Expected noop trace ID to be invalid")
	}
	
	spanID := span.SpanID()
	if spanID.IsValid() {
		t.Error("Expected noop span ID to be invalid")
	}
}

func TestCreateAttribute(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		value    interface{}
		expected attribute.KeyValue
	}{
		{
			name:     "string value",
			key:      "string-key",
			value:    "string-value",
			expected: attribute.String("string-key", "string-value"),
		},
		{
			name:     "int value",
			key:      "int-key",
			value:    42,
			expected: attribute.Int("int-key", 42),
		},
		{
			name:     "int64 value",
			key:      "int64-key",
			value:    int64(42),
			expected: attribute.Int64("int64-key", 42),
		},
		{
			name:     "float64 value",
			key:      "float64-key",
			value:    3.14,
			expected: attribute.Float64("float64-key", 3.14),
		},
		{
			name:     "bool value",
			key:      "bool-key",
			value:    true,
			expected: attribute.Bool("bool-key", true),
		},
		{
			name:     "string slice value",
			key:      "string-slice-key",
			value:    []string{"a", "b", "c"},
			expected: attribute.StringSlice("string-slice-key", []string{"a", "b", "c"}),
		},
		{
			name:     "int slice value",
			key:      "int-slice-key",
			value:    []int{1, 2, 3},
			expected: attribute.IntSlice("int-slice-key", []int{1, 2, 3}),
		},
		{
			name:     "other type",
			key:      "other-key",
			value:    struct{ Field string }{Field: "value"},
			expected: attribute.String("other-key", "{value}"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			attr := createAttribute(tt.key, tt.value)
			
			if attr.Key != tt.expected.Key {
				t.Errorf("Key = %v, want %v", attr.Key, tt.expected.Key)
			}
			
			// Compare values as strings since the underlying types might be different
			if attr.Value.AsString() != tt.expected.Value.AsString() {
				t.Errorf("Value = %v, want %v", attr.Value.AsString(), tt.expected.Value.AsString())
			}
		})
	}
}

func TestHTTPCarrier(t *testing.T) {
	headers := make(map[string][]string)
	carrier := NewHTTPCarrier(headers)
	
	// Test Set and Get
	carrier.Set("test-key", "test-value")
	value := carrier.Get("test-key")
	if value != "test-value" {
		t.Errorf("Get() = %v, want %v", value, "test-value")
	}
	
	// Test Get non-existent key
	value = carrier.Get("non-existent-key")
	if value != "" {
		t.Errorf("Get() = %v, want %v", value, "")
	}
	
	// Test Keys
	carrier.Set("key1", "value1")
	carrier.Set("key2", "value2")
	keys := carrier.Keys()
	
	if len(keys) != 2 {
		t.Errorf("Keys() returned %d keys, want 2", len(keys))
	}
	
	// Check that all keys are present
	keyMap := make(map[string]bool)
	for _, key := range keys {
		keyMap[key] = true
	}
	
	if !keyMap["key1"] || !keyMap["key2"] {
		t.Errorf("Keys() missing expected keys, got %v", keys)
	}
	
	// Test Set with multiple values
	headers["multi-value"] = []string{"value1", "value2"}
	value = carrier.Get("multi-value")
	if value != "value1" {
		t.Errorf("Get() for multi-value = %v, want %v", value, "value1")
	}
}

func TestSpanOptions(t *testing.T) {
	opts := &SpanOptions{}
	
	// Test WithSpanKind
	kindOpt := WithSpanKind(trace.SpanKindServer)
	kindOpt(opts)
	if opts.Kind != trace.SpanKindServer {
		t.Errorf("Kind = %v, want %v", opts.Kind, trace.SpanKindServer)
	}
	
	// Test WithAttributes
	attrs := []attribute.KeyValue{
		attribute.String("key1", "value1"),
		attribute.Int("key2", 42),
	}
	attrsOpt := WithAttributes(attrs...)
	attrsOpt(opts)
	if len(opts.Attributes) != 2 {
		t.Errorf("Attributes length = %d, want 2", len(opts.Attributes))
	}
	
	// Test WithStartTime
	startTime := time.Now()
	startTimeOpt := WithStartTime(startTime)
	startTimeOpt(opts)
	if !opts.StartTime.Equal(startTime) {
		t.Errorf("StartTime = %v, want %v", opts.StartTime, startTime)
	}
	
	// Test WithStackTrace
	stackTraceOpt := WithStackTrace()
	stackTraceOpt(opts)
	if !opts.StackTrace {
		t.Error("StackTrace = false, want true")
	}
	
	// Test WithComponentName
	componentOpt := WithComponentName("test-component")
	componentOpt(opts)
	if opts.ComponentName != "test-component" {
		t.Errorf("ComponentName = %v, want %v", opts.ComponentName, "test-component")
	}
	
	// Test WithOperationName
	operationOpt := WithOperationName("test-operation")
	operationOpt(opts)
	if opts.OperationName != "test-operation" {
		t.Errorf("OperationName = %v, want %v", opts.OperationName, "test-operation")
	}
}