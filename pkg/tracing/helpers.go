package tracing

import (
	"context"
	"fmt"
	"runtime"
	"strings"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// Attribute constants
const (
	AttributeDBSystem     = "db.system"
	AttributeDBOperation  = "db.operation"
	AttributeDBStatement  = "db.statement"
	AttributeCacheOperation = "cache.operation"
	AttributeCacheKey     = "cache.key"
	AttributeCacheHit     = "cache.hit"
	AttributeQueueName    = "queue.name"
	AttributeQueueOperation = "queue.operation"
	AttributeMessageType  = "message.type"
	AttributeTenantID     = "tenant.id"
	AttributeUserID       = "user.id"
	AttributeRequestID    = "request.id"
)

// noopSpan is a no-op implementation of Span
type noopSpan struct{}

func (s *noopSpan) End() {}
func (s *noopSpan) SetAttribute(key string, value interface{}) {}
func (s *noopSpan) SetAttributes(attrs ...attribute.KeyValue) {}
func (s *noopSpan) SetStatus(code codes.Code, description string) {}
func (s *noopSpan) SetError(err error) {}
func (s *noopSpan) AddEvent(name string, opts ...trace.EventOption) {}
func (s *noopSpan) AddEventWithAttributes(name string, attrs ...attribute.KeyValue) {}
func (s *noopSpan) SetName(name string) {}
func (s *noopSpan) IsRecording() bool { return false }
func (s *noopSpan) SpanContext() trace.SpanContext { return trace.SpanContext{} }
func (s *noopSpan) TraceID() trace.TraceID { return trace.TraceID{} }
func (s *noopSpan) SpanID() trace.SpanID { return trace.SpanID{} }

// TracerFromContext retrieves the tracer from the context
func TracerFromContext(ctx context.Context) Tracer {
	if v := ctx.Value("tracer"); v != nil {
		if tracer, ok := v.(Tracer); ok {
			return tracer
		}
	}
	return nil
}


// StartSpanWithCaller starts a span with the calling function name
func StartSpanWithCaller(ctx context.Context, opts ...SpanOption) (context.Context, Span) {
	name := getCallerName(1)
	tracer := TracerFromContext(ctx)
	if tracer == nil {
		return ctx, &noopSpan{}
	}
	return tracer.StartSpan(ctx, name, opts...)
}

// RecordError records an error on the current span in context
func RecordError(ctx context.Context, err error, opts ...trace.EventOption) {
	if span := SpanFromContext(ctx); span != nil && err != nil {
		span.RecordError(err, opts...)
		span.SetStatus(codes.Error, err.Error())
	}
}


// SetSpanStatus sets the status of the current span in context
func SetSpanStatus(ctx context.Context, code codes.Code, description string) {
	if span := SpanFromContext(ctx); span != nil {
		span.SetStatus(code, description)
	}
}


// WithSpan executes a function within a span
func WithSpan(ctx context.Context, name string, fn func(context.Context) error, opts ...SpanOption) error {
	tracer := TracerFromContext(ctx)
	if tracer == nil {
		return fn(ctx)
	}
	ctx, span := tracer.StartSpan(ctx, name, opts...)
	defer span.End()

	err := fn(ctx)
	if err != nil {
		RecordError(ctx, err)
	}
	return err
}

// WithSpanResult executes a function within a span and returns a result
func WithSpanResult[T any](ctx context.Context, name string, fn func(context.Context) (T, error), opts ...SpanOption) (T, error) {
	tracer := TracerFromContext(ctx)
	if tracer == nil {
		return fn(ctx)
	}
	ctx, span := tracer.StartSpan(ctx, name, opts...)
	defer span.End()

	result, err := fn(ctx)
	if err != nil {
		RecordError(ctx, err)
	}
	return result, err
}


// Helper to get caller function name
func getCallerName(skip int) string {
	pc, _, _, ok := runtime.Caller(skip + 1)
	if !ok {
		return "unknown"
	}
	
	fn := runtime.FuncForPC(pc)
	if fn == nil {
		return "unknown"
	}
	
	name := fn.Name()
	// Remove package path
	if idx := strings.LastIndex(name, "/"); idx >= 0 {
		name = name[idx+1:]
	}
	// Remove package name
	if idx := strings.Index(name, "."); idx >= 0 {
		name = name[idx+1:]
	}
	
	return name
}


// FormatSpanName creates a consistent span name
func FormatSpanName(service, operation string) string {
	return fmt.Sprintf("%s.%s", service, operation)
}

// ExtractTenantAndUserFromContext extracts tenant and user IDs from context
func ExtractTenantAndUserFromContext(ctx context.Context) (tenantID, userID uint) {
	// These would typically come from your auth middleware
	if v := ctx.Value("tenant_id"); v != nil {
		if id, ok := v.(uint); ok {
			tenantID = id
		}
	}
	if v := ctx.Value("user_id"); v != nil {
		if id, ok := v.(uint); ok {
			userID = id
		}
	}
	return
}

// SpanKindFromString converts a string to SpanKind
func SpanKindFromString(kind string) trace.SpanKind {
	switch strings.ToLower(kind) {
	case "client":
		return trace.SpanKindClient
	case "server":
		return trace.SpanKindServer
	case "producer":
		return trace.SpanKindProducer
	case "consumer":
		return trace.SpanKindConsumer
	default:
		return trace.SpanKindInternal
	}
}

// StatusFromHTTPCode converts HTTP status code to trace status
func StatusFromHTTPCode(code int) codes.Code {
	if code < 400 {
		return codes.Ok
	}
	if code < 500 {
		return codes.Error
	}
	return codes.Error
}

// DatabaseAttributes creates standard database attributes
func DatabaseAttributes(system, operation, statement string) []attribute.KeyValue {
	attrs := []attribute.KeyValue{
		attribute.String(AttributeDBSystem, system),
		attribute.String(AttributeDBOperation, operation),
	}
	if statement != "" {
		// Truncate long statements
		if len(statement) > 1000 {
			statement = statement[:1000] + "..."
		}
		attrs = append(attrs, attribute.String(AttributeDBStatement, statement))
	}
	return attrs
}

// CacheAttributes creates standard cache attributes
func CacheAttributes(operation, key string, hit bool) []attribute.KeyValue {
	return []attribute.KeyValue{
		attribute.String(AttributeCacheOperation, operation),
		attribute.String(AttributeCacheKey, key),
		attribute.Bool(AttributeCacheHit, hit),
	}
}

// QueueAttributes creates standard queue attributes
func QueueAttributes(queueName, operation, messageType string) []attribute.KeyValue {
	return []attribute.KeyValue{
		attribute.String(AttributeQueueName, queueName),
		attribute.String(AttributeQueueOperation, operation),
		attribute.String(AttributeMessageType, messageType),
	}
}