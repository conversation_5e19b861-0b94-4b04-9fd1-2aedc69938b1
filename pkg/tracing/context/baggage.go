package context

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"go.opentelemetry.io/otel/baggage"
	"go.opentelemetry.io/otel/propagation"
)

// BaggageManager handles baggage propagation for distributed tracing
type BaggageManager struct {
	propagator propagation.TextMapPropagator
}

// NewBaggageManager creates a new baggage manager
func NewBaggageManager() *BaggageManager {
	return &BaggageManager{
		propagator: propagation.Baggage{},
	}
}

// BaggageItem represents a key-value pair in baggage
type BaggageItem struct {
	Key   string
	Value string
}

// BaggageData represents baggage data with metadata
type BaggageData struct {
	Items    []BaggageItem
	Metadata map[string]string
}

// SetBaggage sets baggage in the context
func (b *BaggageManager) SetBaggage(ctx context.Context, key, value string) (context.Context, error) {
	// Create baggage member
	member, err := baggage.NewMember(key, value)
	if err != nil {
		return ctx, fmt.Errorf("failed to create baggage member: %w", err)
	}
	
	// Get existing baggage
	bag := baggage.FromContext(ctx)
	
	// Add new member to baggage
	bag, err = bag.SetMember(member)
	if err != nil {
		return ctx, fmt.Errorf("failed to set baggage member: %w", err)
	}
	
	// Return context with updated baggage
	return baggage.ContextWithBaggage(ctx, bag), nil
}

// GetBaggage gets baggage value from context
func (b *BaggageManager) GetBaggage(ctx context.Context, key string) (string, bool) {
	bag := baggage.FromContext(ctx)
	member := bag.Member(key)
	if member == (baggage.Member{}) {
		return "", false
	}
	return member.Value(), true
}

// GetAllBaggage gets all baggage from context
func (b *BaggageManager) GetAllBaggage(ctx context.Context) *BaggageData {
	bag := baggage.FromContext(ctx)
	
	data := &BaggageData{
		Items:    make([]BaggageItem, 0),
		Metadata: make(map[string]string),
	}
	
	// Iterate through all members
	for _, member := range bag.Members() {
		data.Items = append(data.Items, BaggageItem{
			Key:   member.Key(),
			Value: member.Value(),
		})
	}
	
	return data
}

// RemoveBaggage removes baggage from context
func (b *BaggageManager) RemoveBaggage(ctx context.Context, key string) context.Context {
	bag := baggage.FromContext(ctx)
	bag = bag.DeleteMember(key)
	return baggage.ContextWithBaggage(ctx, bag)
}

// ClearBaggage clears all baggage from context
func (b *BaggageManager) ClearBaggage(ctx context.Context) context.Context {
	emptyBag, _ := baggage.New()
	return baggage.ContextWithBaggage(ctx, emptyBag)
}

// InjectBaggageToHeaders injects baggage into HTTP headers
func (b *BaggageManager) InjectBaggageToHeaders(ctx context.Context, headers http.Header) {
	carrier := propagation.HeaderCarrier(headers)
	b.propagator.Inject(ctx, carrier)
}

// ExtractBaggageFromHeaders extracts baggage from HTTP headers
func (b *BaggageManager) ExtractBaggageFromHeaders(ctx context.Context, headers http.Header) context.Context {
	carrier := propagation.HeaderCarrier(headers)
	return b.propagator.Extract(ctx, carrier)
}

// InjectBaggageToMap injects baggage into map
func (b *BaggageManager) InjectBaggageToMap(ctx context.Context, headers map[string]string) {
	carrier := propagation.MapCarrier(headers)
	b.propagator.Inject(ctx, carrier)
}

// ExtractBaggageFromMap extracts baggage from map
func (b *BaggageManager) ExtractBaggageFromMap(ctx context.Context, headers map[string]string) context.Context {
	carrier := propagation.MapCarrier(headers)
	return b.propagator.Extract(ctx, carrier)
}

// SetMultipleBaggage sets multiple baggage items at once
func (b *BaggageManager) SetMultipleBaggage(ctx context.Context, items map[string]string) (context.Context, error) {
	newCtx := ctx
	var err error
	
	for key, value := range items {
		newCtx, err = b.SetBaggage(newCtx, key, value)
		if err != nil {
			return ctx, fmt.Errorf("failed to set baggage %s: %w", key, err)
		}
	}
	
	return newCtx, nil
}

// ValidateBaggageKey validates baggage key format
func (b *BaggageManager) ValidateBaggageKey(key string) error {
	if len(key) == 0 {
		return fmt.Errorf("baggage key cannot be empty")
	}
	
	if len(key) > 256 {
		return fmt.Errorf("baggage key too long: %d characters (max 256)", len(key))
	}
	
	// Check for invalid characters
	for _, char := range key {
		if !isValidBaggageChar(char) {
			return fmt.Errorf("invalid character in baggage key: %c", char)
		}
	}
	
	return nil
}

// ValidateBaggageValue validates baggage value format
func (b *BaggageManager) ValidateBaggageValue(value string) error {
	if len(value) > 8192 {
		return fmt.Errorf("baggage value too long: %d characters (max 8192)", len(value))
	}
	
	// Check for invalid characters
	for _, char := range value {
		if !isValidBaggageChar(char) {
			return fmt.Errorf("invalid character in baggage value: %c", char)
		}
	}
	
	return nil
}

// isValidBaggageChar checks if character is valid for baggage
func isValidBaggageChar(char rune) bool {
	// Allow printable ASCII characters except comma, semicolon, and equals
	return char >= 32 && char <= 126 && char != ',' && char != ';' && char != '='
}

// GetBaggageSize returns the size of baggage in bytes
func (b *BaggageManager) GetBaggageSize(ctx context.Context) int {
	bag := baggage.FromContext(ctx)
	
	size := 0
	for _, member := range bag.Members() {
		size += len(member.Key()) + len(member.Value()) + 2 // +2 for = and ,
	}
	
	return size
}

// IsBaggageWithinLimits checks if baggage is within size limits
func (b *BaggageManager) IsBaggageWithinLimits(ctx context.Context) bool {
	const maxBaggageSize = 8192 // 8KB limit
	return b.GetBaggageSize(ctx) <= maxBaggageSize
}

// TruncateBaggage truncates baggage to fit within limits
func (b *BaggageManager) TruncateBaggage(ctx context.Context) context.Context {
	if b.IsBaggageWithinLimits(ctx) {
		return ctx
	}
	
	bag := baggage.FromContext(ctx)
	members := bag.Members()
	
	// Create new baggage with truncated data
	newBag, _ := baggage.New()
	currentSize := 0
	const maxSize = 8192
	
	for _, member := range members {
		memberSize := len(member.Key()) + len(member.Value()) + 2
		if currentSize+memberSize > maxSize {
			break
		}
		
		newBag, _ = newBag.SetMember(member)
		currentSize += memberSize
	}
	
	return baggage.ContextWithBaggage(ctx, newBag)
}

// CopyBaggage copies baggage from source to destination context
func (b *BaggageManager) CopyBaggage(src, dst context.Context) context.Context {
	srcBag := baggage.FromContext(src)
	return baggage.ContextWithBaggage(dst, srcBag)
}

// MergeBaggage merges baggage from multiple contexts
func (b *BaggageManager) MergeBaggage(contexts ...context.Context) context.Context {
	if len(contexts) == 0 {
		return context.Background()
	}
	
	if len(contexts) == 1 {
		return contexts[0]
	}
	
	// Start with first context
	result := contexts[0]
	resultBag := baggage.FromContext(result)
	
	// Merge baggage from other contexts
	for _, ctx := range contexts[1:] {
		bag := baggage.FromContext(ctx)
		for _, member := range bag.Members() {
			resultBag, _ = resultBag.SetMember(member)
		}
	}
	
	return baggage.ContextWithBaggage(result, resultBag)
}

// Common baggage keys
const (
	BaggageKeyUserID      = "user.id"
	BaggageKeyTenantID    = "tenant.id"
	BaggageKeySessionID   = "session.id"
	BaggageKeyRequestID   = "request.id"
	BaggageKeyServiceName = "service.name"
	BaggageKeyVersion     = "service.version"
	BaggageKeyEnvironment = "environment"
	BaggageKeyRegion      = "region"
)

// SetUserBaggage sets user-related baggage
func (b *BaggageManager) SetUserBaggage(ctx context.Context, userID, tenantID, sessionID string) (context.Context, error) {
	items := make(map[string]string)
	
	if userID != "" {
		items[BaggageKeyUserID] = userID
	}
	if tenantID != "" {
		items[BaggageKeyTenantID] = tenantID
	}
	if sessionID != "" {
		items[BaggageKeySessionID] = sessionID
	}
	
	return b.SetMultipleBaggage(ctx, items)
}

// GetUserBaggage gets user-related baggage
func (b *BaggageManager) GetUserBaggage(ctx context.Context) (userID, tenantID, sessionID string) {
	userID, _ = b.GetBaggage(ctx, BaggageKeyUserID)
	tenantID, _ = b.GetBaggage(ctx, BaggageKeyTenantID)
	sessionID, _ = b.GetBaggage(ctx, BaggageKeySessionID)
	return
}

// SetServiceBaggage sets service-related baggage
func (b *BaggageManager) SetServiceBaggage(ctx context.Context, serviceName, version, environment string) (context.Context, error) {
	items := make(map[string]string)
	
	if serviceName != "" {
		items[BaggageKeyServiceName] = serviceName
	}
	if version != "" {
		items[BaggageKeyVersion] = version
	}
	if environment != "" {
		items[BaggageKeyEnvironment] = environment
	}
	
	return b.SetMultipleBaggage(ctx, items)
}

// GetServiceBaggage gets service-related baggage
func (b *BaggageManager) GetServiceBaggage(ctx context.Context) (serviceName, version, environment string) {
	serviceName, _ = b.GetBaggage(ctx, BaggageKeyServiceName)
	version, _ = b.GetBaggage(ctx, BaggageKeyVersion)
	environment, _ = b.GetBaggage(ctx, BaggageKeyEnvironment)
	return
}

// Global baggage manager instance
var globalBaggageManager *BaggageManager

// GetGlobalBaggageManager returns the global baggage manager instance
func GetGlobalBaggageManager() *BaggageManager {
	if globalBaggageManager == nil {
		globalBaggageManager = NewBaggageManager()
	}
	return globalBaggageManager
}

// SetGlobalBaggageManager sets the global baggage manager instance
func SetGlobalBaggageManager(bm *BaggageManager) {
	globalBaggageManager = bm
}