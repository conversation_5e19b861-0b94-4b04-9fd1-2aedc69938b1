package context

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"
)

// DebugManager provides debugging tools for distributed context propagation
type DebugManager struct {
	propagator     *Propagator
	headerManager  *HeaderManager
	baggageManager *BaggageManager
	validator      *ValidationManager
}

// NewDebugManager creates a new debug manager
func NewDebugManager(propagator *Propagator, headerManager *HeaderManager, baggageManager *BaggageManager, validator *ValidationManager) *DebugManager {
	return &DebugManager{
		propagator:     propagator,
		headerManager:  headerManager,
		baggageManager: baggageManager,
		validator:      validator,
	}
}

// DebugInfo represents debug information for context propagation
type DebugInfo struct {
	TraceContext   TraceContextInfo   `json:"trace_context"`
	BaggageInfo    BaggageDebugInfo   `json:"baggage"`
	HeadersInfo    HeadersDebugInfo   `json:"headers"`
	ValidationInfo ValidationDebugInfo `json:"validation"`
	Timestamp      time.Time          `json:"timestamp"`
}

// TraceContextInfo represents trace context debug information
type TraceContextInfo struct {
	TraceID      string `json:"trace_id"`
	SpanID       string `json:"span_id"`
	TraceFlags   string `json:"trace_flags"`
	TraceState   string `json:"trace_state"`
	IsValid      bool   `json:"is_valid"`
	IsRecording  bool   `json:"is_recording"`
	IsSampled    bool   `json:"is_sampled"`
	IsRemote     bool   `json:"is_remote"`
}

// BaggageDebugInfo represents baggage debug information
type BaggageDebugInfo struct {
	Items       []BaggageItem `json:"items"`
	Count       int           `json:"count"`
	Size        int           `json:"size_bytes"`
	WithinLimits bool         `json:"within_limits"`
}

// HeadersDebugInfo represents headers debug information
type HeadersDebugInfo struct {
	TraceHeaders    map[string]string `json:"trace_headers"`
	RequiredHeaders map[string]string `json:"required_headers"`
	OptionalHeaders map[string]string `json:"optional_headers"`
	AllHeaders      map[string]string `json:"all_headers"`
}

// ValidationDebugInfo represents validation debug information
type ValidationDebugInfo struct {
	IsValid      bool              `json:"is_valid"`
	Errors       []ValidationError `json:"errors"`
	SecurityOK   bool              `json:"security_ok"`
	Sanitized    bool              `json:"sanitized"`
}

// GetDebugInfo returns comprehensive debug information for context
func (d *DebugManager) GetDebugInfo(ctx context.Context, req *http.Request) *DebugInfo {
	info := &DebugInfo{
		Timestamp: time.Now(),
	}
	
	// Get trace context info
	info.TraceContext = d.getTraceContextInfo(ctx)
	
	// Get baggage info
	info.BaggageInfo = d.getBaggageInfo(ctx)
	
	// Get headers info
	if req != nil {
		info.HeadersInfo = d.getHeadersInfo(req)
	}
	
	// Get validation info
	info.ValidationInfo = d.getValidationInfo(ctx, req)
	
	return info
}

// getTraceContextInfo extracts trace context debug information
func (d *DebugManager) getTraceContextInfo(ctx context.Context) TraceContextInfo {
	span := trace.SpanFromContext(ctx)
	spanContext := span.SpanContext()
	
	return TraceContextInfo{
		TraceID:     spanContext.TraceID().String(),
		SpanID:      spanContext.SpanID().String(),
		TraceFlags:  fmt.Sprintf("0x%02x", spanContext.TraceFlags()),
		TraceState:  spanContext.TraceState().String(),
		IsValid:     spanContext.IsValid(),
		IsRecording: span.IsRecording(),
		IsSampled:   spanContext.IsSampled(),
		IsRemote:    spanContext.IsRemote(),
	}
}

// getBaggageInfo extracts baggage debug information
func (d *DebugManager) getBaggageInfo(ctx context.Context) BaggageDebugInfo {
	baggageData := d.baggageManager.GetAllBaggage(ctx)
	
	return BaggageDebugInfo{
		Items:        baggageData.Items,
		Count:        len(baggageData.Items),
		Size:         d.baggageManager.GetBaggageSize(ctx),
		WithinLimits: d.baggageManager.IsBaggageWithinLimits(ctx),
	}
}

// getHeadersInfo extracts headers debug information
func (d *DebugManager) getHeadersInfo(req *http.Request) HeadersDebugInfo {
	allHeaders := d.headerManager.HeadersToMap(req.Header)
	
	requiredHeaders := make(map[string]string)
	for _, header := range d.headerManager.GetRequiredHeaders() {
		if value := req.Header.Get(header); value != "" {
			requiredHeaders[header] = value
		}
	}
	
	optionalHeaders := make(map[string]string)
	for _, header := range d.headerManager.GetOptionalHeaders() {
		if value := req.Header.Get(header); value != "" {
			optionalHeaders[header] = value
		}
	}
	
	return HeadersDebugInfo{
		TraceHeaders:    allHeaders,
		RequiredHeaders: requiredHeaders,
		OptionalHeaders: optionalHeaders,
		AllHeaders:      allHeaders,
	}
}

// getValidationInfo extracts validation debug information
func (d *DebugManager) getValidationInfo(ctx context.Context, req *http.Request) ValidationDebugInfo {
	contextResult := d.validator.ValidateContext(ctx)
	
	var securityResult *ValidationResult
	if req != nil {
		securityResult = d.validator.SecurityValidation(ctx, req)
	}
	
	return ValidationDebugInfo{
		IsValid:    contextResult.Valid,
		Errors:     contextResult.Errors,
		SecurityOK: securityResult == nil || securityResult.Valid,
		Sanitized:  false, // This would be set by sanitization process
	}
}

// PrintDebugInfo prints debug information in a formatted way
func (d *DebugManager) PrintDebugInfo(ctx context.Context, req *http.Request) {
	info := d.GetDebugInfo(ctx, req)
	
	fmt.Println("=== Distributed Context Debug Information ===")
	fmt.Printf("Timestamp: %s\n", info.Timestamp.Format(time.RFC3339))
	fmt.Println()
	
	// Print trace context info
	d.printTraceContextInfo(info.TraceContext)
	
	// Print baggage info
	d.printBaggageInfo(info.BaggageInfo)
	
	// Print headers info
	if req != nil {
		d.printHeadersInfo(info.HeadersInfo)
	}
	
	// Print validation info
	d.printValidationInfo(info.ValidationInfo)
	
	fmt.Println("=== End Debug Information ===")
}

// printTraceContextInfo prints trace context information
func (d *DebugManager) printTraceContextInfo(info TraceContextInfo) {
	fmt.Println("--- Trace Context ---")
	fmt.Printf("  Trace ID: %s\n", info.TraceID)
	fmt.Printf("  Span ID: %s\n", info.SpanID)
	fmt.Printf("  Trace Flags: %s\n", info.TraceFlags)
	fmt.Printf("  Trace State: %s\n", info.TraceState)
	fmt.Printf("  Valid: %v\n", info.IsValid)
	fmt.Printf("  Recording: %v\n", info.IsRecording)
	fmt.Printf("  Sampled: %v\n", info.IsSampled)
	fmt.Printf("  Remote: %v\n", info.IsRemote)
	fmt.Println()
}

// printBaggageInfo prints baggage information
func (d *DebugManager) printBaggageInfo(info BaggageDebugInfo) {
	fmt.Println("--- Baggage Information ---")
	fmt.Printf("  Count: %d items\n", info.Count)
	fmt.Printf("  Size: %d bytes\n", info.Size)
	fmt.Printf("  Within Limits: %v\n", info.WithinLimits)
	
	if len(info.Items) > 0 {
		fmt.Println("  Items:")
		for _, item := range info.Items {
			fmt.Printf("    %s: %s\n", item.Key, item.Value)
		}
	}
	fmt.Println()
}

// printHeadersInfo prints headers information
func (d *DebugManager) printHeadersInfo(info HeadersDebugInfo) {
	fmt.Println("--- Headers Information ---")
	
	if len(info.RequiredHeaders) > 0 {
		fmt.Println("  Required Headers:")
		for key, value := range info.RequiredHeaders {
			fmt.Printf("    %s: %s\n", key, value)
		}
	}
	
	if len(info.OptionalHeaders) > 0 {
		fmt.Println("  Optional Headers:")
		for key, value := range info.OptionalHeaders {
			fmt.Printf("    %s: %s\n", key, value)
		}
	}
	
	fmt.Println()
}

// printValidationInfo prints validation information
func (d *DebugManager) printValidationInfo(info ValidationDebugInfo) {
	fmt.Println("--- Validation Information ---")
	fmt.Printf("  Valid: %v\n", info.IsValid)
	fmt.Printf("  Security OK: %v\n", info.SecurityOK)
	fmt.Printf("  Sanitized: %v\n", info.Sanitized)
	
	if len(info.Errors) > 0 {
		fmt.Println("  Errors:")
		for _, err := range info.Errors {
			fmt.Printf("    %s: %s (Code: %s)\n", err.Field, err.Message, err.Code)
		}
	}
	fmt.Println()
}

// GetDebugInfoJSON returns debug information as JSON
func (d *DebugManager) GetDebugInfoJSON(ctx context.Context, req *http.Request) (string, error) {
	info := d.GetDebugInfo(ctx, req)
	
	data, err := json.MarshalIndent(info, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal debug info: %w", err)
	}
	
	return string(data), nil
}

// TraceContextSummary returns a summary of trace context
func (d *DebugManager) TraceContextSummary(ctx context.Context) string {
	traceID := d.propagator.GetTraceID(ctx)
	spanID := d.propagator.GetSpanID(ctx)
	
	if traceID == "" || spanID == "" {
		return "No active trace context"
	}
	
	return fmt.Sprintf("Trace: %s, Span: %s", traceID[:8], spanID[:8])
}

// BaggageSummary returns a summary of baggage
func (d *DebugManager) BaggageSummary(ctx context.Context) string {
	baggageData := d.baggageManager.GetAllBaggage(ctx)
	
	if len(baggageData.Items) == 0 {
		return "No baggage"
	}
	
	size := d.baggageManager.GetBaggageSize(ctx)
	return fmt.Sprintf("%d items, %d bytes", len(baggageData.Items), size)
}

// HeadersSummary returns a summary of trace headers
func (d *DebugManager) HeadersSummary(req *http.Request) string {
	if req == nil {
		return "No request"
	}
	
	requiredCount := 0
	optionalCount := 0
	
	for _, header := range d.headerManager.GetRequiredHeaders() {
		if req.Header.Get(header) != "" {
			requiredCount++
		}
	}
	
	for _, header := range d.headerManager.GetOptionalHeaders() {
		if req.Header.Get(header) != "" {
			optionalCount++
		}
	}
	
	return fmt.Sprintf("Required: %d, Optional: %d", requiredCount, optionalCount)
}

// LogDebugSummary logs a debug summary
func (d *DebugManager) LogDebugSummary(ctx context.Context, req *http.Request, operation string) {
	traceSummary := d.TraceContextSummary(ctx)
	baggageSummary := d.BaggageSummary(ctx)
	headersSummary := d.HeadersSummary(req)
	
	fmt.Printf("[DEBUG] %s | %s | Baggage: %s | Headers: %s\n",
		operation, traceSummary, baggageSummary, headersSummary)
}

// DebugMiddleware returns a debug middleware for HTTP handlers
func (d *DebugManager) DebugMiddleware(enabled bool) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if enabled {
				d.LogDebugSummary(r.Context(), r, fmt.Sprintf("%s %s", r.Method, r.URL.Path))
			}
			
			next.ServeHTTP(w, r)
		})
	}
}

// DebugGinMiddleware returns a debug middleware for Gin handlers
func (d *DebugManager) DebugGinMiddleware(enabled bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		if enabled {
			d.LogDebugSummary(c.Request.Context(), c.Request, 
				fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path))
		}
		
		c.Next()
	}
}

// CompareContexts compares two contexts and returns differences
func (d *DebugManager) CompareContexts(ctx1, ctx2 context.Context) map[string]interface{} {
	diff := make(map[string]interface{})
	
	// Compare trace contexts
	trace1 := d.getTraceContextInfo(ctx1)
	trace2 := d.getTraceContextInfo(ctx2)
	
	if trace1.TraceID != trace2.TraceID {
		diff["trace_id"] = map[string]string{"ctx1": trace1.TraceID, "ctx2": trace2.TraceID}
	}
	if trace1.SpanID != trace2.SpanID {
		diff["span_id"] = map[string]string{"ctx1": trace1.SpanID, "ctx2": trace2.SpanID}
	}
	
	// Compare baggage
	baggage1 := d.getBaggageInfo(ctx1)
	baggage2 := d.getBaggageInfo(ctx2)
	
	if baggage1.Count != baggage2.Count {
		diff["baggage_count"] = map[string]int{"ctx1": baggage1.Count, "ctx2": baggage2.Count}
	}
	if baggage1.Size != baggage2.Size {
		diff["baggage_size"] = map[string]int{"ctx1": baggage1.Size, "ctx2": baggage2.Size}
	}
	
	return diff
}

// AnalyzePerformance analyzes performance impact of context propagation
func (d *DebugManager) AnalyzePerformance(ctx context.Context) map[string]interface{} {
	start := time.Now()
	
	// Measure baggage operations
	baggageStart := time.Now()
	_ = d.baggageManager.GetAllBaggage(ctx)
	baggageTime := time.Since(baggageStart)
	
	// Measure validation operations
	validationStart := time.Now()
	_ = d.validator.ValidateContext(ctx)
	validationTime := time.Since(validationStart)
	
	// Measure header operations
	headerStart := time.Now()
	headers := make(map[string]string)
	d.propagator.InjectHeaders(ctx, headers)
	headerTime := time.Since(headerStart)
	
	totalTime := time.Since(start)
	
	return map[string]interface{}{
		"total_time_ns":      totalTime.Nanoseconds(),
		"baggage_time_ns":    baggageTime.Nanoseconds(),
		"validation_time_ns": validationTime.Nanoseconds(),
		"header_time_ns":     headerTime.Nanoseconds(),
		"baggage_size":       d.baggageManager.GetBaggageSize(ctx),
		"performance_ok":     totalTime < time.Microsecond*100, // 100μs threshold
	}
}

// Global debug manager instance
var globalDebugManager *DebugManager

// GetGlobalDebugManager returns the global debug manager instance
func GetGlobalDebugManager() *DebugManager {
	if globalDebugManager == nil {
		globalDebugManager = NewDebugManager(
			GetGlobalPropagator(),
			GetGlobalHeaderManager(),
			GetGlobalBaggageManager(),
			GetGlobalValidationManager(),
		)
	}
	return globalDebugManager
}

// SetGlobalDebugManager sets the global debug manager instance
func SetGlobalDebugManager(dm *DebugManager) {
	globalDebugManager = dm
}