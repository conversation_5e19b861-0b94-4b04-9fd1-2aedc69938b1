package context

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// CorrelationManager handles cross-service correlation for distributed tracing
type CorrelationManager struct {
	propagator     *Propagator
	headerManager  *HeaderManager
	baggageManager *BaggageManager
}

// NewCorrelationManager creates a new correlation manager
func NewCorrelationManager(propagator *Propagator, headerManager *HeaderManager, baggageManager *BaggageManager) *CorrelationManager {
	return &CorrelationManager{
		propagator:     propagator,
		headerManager:  headerManager,
		baggageManager: baggageManager,
	}
}

// CorrelationData represents correlation data for cross-service communication
type CorrelationData struct {
	TraceID       string
	SpanID        string
	CorrelationID string
	RequestID     string
	ServiceName   string
	ServiceVersion string
	UserID        string
	TenantID      string
	SessionID     string
	Environment   string
	Timestamp     time.Time
}

// ServiceCall represents a service-to-service call
type ServiceCall struct {
	ServiceName    string
	ServiceVersion string
	Operation      string
	Endpoint       string
	Method         string
	StartTime      time.Time
	EndTime        time.Time
	Success        bool
	Error          error
	Metadata       map[string]string
}

// CreateCorrelationContext creates a new correlation context
func (c *CorrelationManager) CreateCorrelationContext(ctx context.Context, data *CorrelationData) (context.Context, error) {
	// Set baggage with correlation data
	correlationItems := make(map[string]string)
	
	if data.CorrelationID != "" {
		correlationItems[BaggageKeyRequestID] = data.CorrelationID
	}
	if data.UserID != "" {
		correlationItems[BaggageKeyUserID] = data.UserID
	}
	if data.TenantID != "" {
		correlationItems[BaggageKeyTenantID] = data.TenantID
	}
	if data.SessionID != "" {
		correlationItems[BaggageKeySessionID] = data.SessionID
	}
	if data.ServiceName != "" {
		correlationItems[BaggageKeyServiceName] = data.ServiceName
	}
	if data.ServiceVersion != "" {
		correlationItems[BaggageKeyVersion] = data.ServiceVersion
	}
	if data.Environment != "" {
		correlationItems[BaggageKeyEnvironment] = data.Environment
	}
	
	// Set baggage in context
	correlationCtx, err := c.baggageManager.SetMultipleBaggage(ctx, correlationItems)
	if err != nil {
		return ctx, fmt.Errorf("failed to set correlation baggage: %w", err)
	}
	
	return correlationCtx, nil
}

// ExtractCorrelationData extracts correlation data from context
func (c *CorrelationManager) ExtractCorrelationData(ctx context.Context) *CorrelationData {
	data := &CorrelationData{
		TraceID:   c.propagator.GetTraceID(ctx),
		SpanID:    c.propagator.GetSpanID(ctx),
		Timestamp: time.Now(),
	}
	
	// Extract from baggage
	data.CorrelationID, _ = c.baggageManager.GetBaggage(ctx, BaggageKeyRequestID)
	data.UserID, _ = c.baggageManager.GetBaggage(ctx, BaggageKeyUserID)
	data.TenantID, _ = c.baggageManager.GetBaggage(ctx, BaggageKeyTenantID)
	data.SessionID, _ = c.baggageManager.GetBaggage(ctx, BaggageKeySessionID)
	data.ServiceName, _ = c.baggageManager.GetBaggage(ctx, BaggageKeyServiceName)
	data.ServiceVersion, _ = c.baggageManager.GetBaggage(ctx, BaggageKeyVersion)
	data.Environment, _ = c.baggageManager.GetBaggage(ctx, BaggageKeyEnvironment)
	
	return data
}

// PropagateToServiceCall propagates correlation context to service call
func (c *CorrelationManager) PropagateToServiceCall(ctx context.Context, req *http.Request) error {
	// Inject trace headers
	c.headerManager.InjectTraceHeaders(ctx, req)
	
	// Inject baggage
	c.baggageManager.InjectBaggageToHeaders(ctx, req.Header)
	
	// Add correlation metadata
	correlationData := c.ExtractCorrelationData(ctx)
	c.addCorrelationMetadata(req, correlationData)
	
	return nil
}

// addCorrelationMetadata adds correlation metadata to request
func (c *CorrelationManager) addCorrelationMetadata(req *http.Request, data *CorrelationData) {
	if data.CorrelationID != "" {
		req.Header.Set(CorrelationIDHeader, data.CorrelationID)
	}
	if data.RequestID != "" {
		req.Header.Set(RequestIDHeader, data.RequestID)
	}
	if data.ServiceName != "" {
		req.Header.Set(ServiceNameHeader, data.ServiceName)
	}
	if data.ServiceVersion != "" {
		req.Header.Set(ServiceVersionHeader, data.ServiceVersion)
	}
}

// ExtractFromServiceCall extracts correlation context from service call
func (c *CorrelationManager) ExtractFromServiceCall(ctx context.Context, req *http.Request) context.Context {
	// Extract trace context
	traceCtx := c.headerManager.ExtractFromHeaders(c.headerManager.HeadersToMap(req.Header))
	
	// Extract baggage
	baggageCtx := c.baggageManager.ExtractBaggageFromHeaders(traceCtx, req.Header)
	
	return baggageCtx
}

// StartServiceCall starts tracking a service call
func (c *CorrelationManager) StartServiceCall(ctx context.Context, serviceName, operation string) (context.Context, trace.Span, *ServiceCall) {
	// Create child span
	spanCtx, span := c.propagator.CreateChildContext(ctx, fmt.Sprintf("%s.%s", serviceName, operation))
	
	// Set span attributes
	span.SetAttributes(
		attribute.String("service.name", serviceName),
		attribute.String("service.operation", operation),
		attribute.String("service.type", "internal"),
	)
	
	// Add correlation data as span attributes
	correlationData := c.ExtractCorrelationData(ctx)
	c.addCorrelationAttributes(span, correlationData)
	
	// Create service call record
	serviceCall := &ServiceCall{
		ServiceName: serviceName,
		Operation:   operation,
		StartTime:   time.Now(),
		Success:     false,
		Metadata:    make(map[string]string),
	}
	
	return spanCtx, span, serviceCall
}

// addCorrelationAttributes adds correlation data as span attributes
func (c *CorrelationManager) addCorrelationAttributes(span trace.Span, data *CorrelationData) {
	if data.CorrelationID != "" {
		span.SetAttributes(attribute.String("correlation.id", data.CorrelationID))
	}
	if data.UserID != "" {
		span.SetAttributes(attribute.String("user.id", data.UserID))
	}
	if data.TenantID != "" {
		span.SetAttributes(attribute.String("tenant.id", data.TenantID))
	}
	if data.SessionID != "" {
		span.SetAttributes(attribute.String("session.id", data.SessionID))
	}
	if data.Environment != "" {
		span.SetAttributes(attribute.String("environment", data.Environment))
	}
}

// FinishServiceCall finishes tracking a service call
func (c *CorrelationManager) FinishServiceCall(span trace.Span, serviceCall *ServiceCall, err error) {
	serviceCall.EndTime = time.Now()
	serviceCall.Success = err == nil
	serviceCall.Error = err
	
	// Set span status
	if err != nil {
		span.SetAttributes(attribute.String("error.message", err.Error()))
		span.SetStatus(codes.Error, err.Error())
	} else {
		span.SetStatus(codes.Ok, "")
	}
	
	// Add timing attributes
	duration := serviceCall.EndTime.Sub(serviceCall.StartTime)
	span.SetAttributes(
		attribute.Int64("service.duration_ms", duration.Milliseconds()),
		attribute.Bool("service.success", serviceCall.Success),
	)
	
	// Finish span
	span.End()
}

// CreateCorrelationID creates a new correlation ID
func (c *CorrelationManager) CreateCorrelationID(ctx context.Context) string {
	// Use trace ID if available, otherwise generate a new ID
	if traceID := c.propagator.GetTraceID(ctx); traceID != "" {
		return traceID
	}
	
	// Generate a new correlation ID using the same format as trace ID
	return generateCorrelationID()
}

// generateCorrelationID generates a new correlation ID
func generateCorrelationID() string {
	// Generate a random 32-character hex string
	id := make([]byte, 16)
	for i := range id {
		id[i] = byte(time.Now().UnixNano() % 256)
	}
	return fmt.Sprintf("%x", id)
}

// ValidateCorrelationChain validates correlation chain integrity
func (c *CorrelationManager) ValidateCorrelationChain(ctx context.Context) error {
	data := c.ExtractCorrelationData(ctx)
	
	// Check if trace ID exists
	if data.TraceID == "" {
		return fmt.Errorf("missing trace ID in correlation chain")
	}
	
	// Check if correlation ID exists
	if data.CorrelationID == "" {
		return fmt.Errorf("missing correlation ID in correlation chain")
	}
	
	// Validate trace ID format
	if len(data.TraceID) != 32 {
		return fmt.Errorf("invalid trace ID format: %s", data.TraceID)
	}
	
	return nil
}

// GetCorrelationChain gets the complete correlation chain
func (c *CorrelationManager) GetCorrelationChain(ctx context.Context) map[string]string {
	chain := make(map[string]string)
	
	data := c.ExtractCorrelationData(ctx)
	
	if data.TraceID != "" {
		chain["trace_id"] = data.TraceID
	}
	if data.SpanID != "" {
		chain["span_id"] = data.SpanID
	}
	if data.CorrelationID != "" {
		chain["correlation_id"] = data.CorrelationID
	}
	if data.RequestID != "" {
		chain["request_id"] = data.RequestID
	}
	if data.UserID != "" {
		chain["user_id"] = data.UserID
	}
	if data.TenantID != "" {
		chain["tenant_id"] = data.TenantID
	}
	if data.SessionID != "" {
		chain["session_id"] = data.SessionID
	}
	if data.ServiceName != "" {
		chain["service_name"] = data.ServiceName
	}
	if data.ServiceVersion != "" {
		chain["service_version"] = data.ServiceVersion
	}
	if data.Environment != "" {
		chain["environment"] = data.Environment
	}
	
	return chain
}

// LogCorrelationChain logs the correlation chain for debugging
func (c *CorrelationManager) LogCorrelationChain(ctx context.Context, logger interface{}) {
	chain := c.GetCorrelationChain(ctx)
	
	// Log the correlation chain
	// This would typically use a structured logger
	fmt.Printf("Correlation Chain: %+v\n", chain)
}

// CrossServiceHTTPClient wraps HTTP client with correlation propagation
type CrossServiceHTTPClient struct {
	client            *http.Client
	correlationManager *CorrelationManager
}

// NewCrossServiceHTTPClient creates a new cross-service HTTP client
func NewCrossServiceHTTPClient(client *http.Client, correlationManager *CorrelationManager) *CrossServiceHTTPClient {
	if client == nil {
		client = &http.Client{
			Timeout: 30 * time.Second,
		}
	}
	
	return &CrossServiceHTTPClient{
		client:            client,
		correlationManager: correlationManager,
	}
}

// Do performs an HTTP request with correlation propagation
func (c *CrossServiceHTTPClient) Do(ctx context.Context, req *http.Request) (*http.Response, error) {
	// Propagate correlation context
	err := c.correlationManager.PropagateToServiceCall(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to propagate correlation context: %w", err)
	}
	
	// Perform request
	resp, err := c.client.Do(req.WithContext(ctx))
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	
	return resp, nil
}

// Global correlation manager instance
var globalCorrelationManager *CorrelationManager

// GetGlobalCorrelationManager returns the global correlation manager instance
func GetGlobalCorrelationManager() *CorrelationManager {
	if globalCorrelationManager == nil {
		globalCorrelationManager = NewCorrelationManager(
			GetGlobalPropagator(),
			GetGlobalHeaderManager(),
			GetGlobalBaggageManager(),
		)
	}
	return globalCorrelationManager
}

// SetGlobalCorrelationManager sets the global correlation manager instance
func SetGlobalCorrelationManager(cm *CorrelationManager) {
	globalCorrelationManager = cm
}