package context

import (
	"context"
	"fmt"
	"net/http"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/pkg/tracing"
)

// ContextManager is the main manager for distributed context propagation
type ContextManager struct {
	jaegerClient       *tracing.JaegerClient
	propagator         *Propagator
	headerManager      *HeaderManager
	baggageManager     *BaggageManager
	correlationManager *CorrelationManager
	validationManager  *ValidationManager
	debugManager       *DebugManager
	config             *ContextConfig
	mutex              sync.RWMutex
}

// ContextConfig represents configuration for context propagation
type ContextConfig struct {
	// Jaeger configuration
	JaegerConfig *tracing.JaegerConfig `json:"jaeger"`
	
	// Validation settings
	EnableValidation     bool `json:"enable_validation"`
	EnableSanitization   bool `json:"enable_sanitization"`
	EnableSecurityChecks bool `json:"enable_security_checks"`
	
	// Debug settings
	EnableDebug      bool `json:"enable_debug"`
	DebugVerbose     bool `json:"debug_verbose"`
	LogDebugSummary  bool `json:"log_debug_summary"`
	
	// Performance settings
	MaxBaggageSize   int `json:"max_baggage_size"`
	MaxBaggageItems  int `json:"max_baggage_items"`
	MaxHeaderSize    int `json:"max_header_size"`
	
	// Rate limiting
	EnableRateLimit  bool `json:"enable_rate_limit"`
	RateLimitRPS     int  `json:"rate_limit_rps"`
}

// DefaultContextConfig returns default configuration
func DefaultContextConfig() *ContextConfig {
	return &ContextConfig{
		JaegerConfig:         tracing.DefaultConfig(),
		EnableValidation:     true,
		EnableSanitization:   true,
		EnableSecurityChecks: true,
		EnableDebug:          false,
		DebugVerbose:         false,
		LogDebugSummary:      false,
		MaxBaggageSize:       8192,  // 8KB
		MaxBaggageItems:      50,
		MaxHeaderSize:        8192,  // 8KB
		EnableRateLimit:      false,
		RateLimitRPS:         100,
	}
}

// NewContextManager creates a new context manager
func NewContextManager(config *ContextConfig) (*ContextManager, error) {
	if config == nil {
		config = DefaultContextConfig()
	}
	
	// Initialize Jaeger client
	jaegerClient, err := tracing.NewJaegerClient(config.JaegerConfig)
	if err != nil {
		return nil, err
	}
	
	// Initialize propagation components
	propagator := NewPropagator()
	headerManager := NewHeaderManager(propagator)
	baggageManager := NewBaggageManager()
	correlationManager := NewCorrelationManager(propagator, headerManager, baggageManager)
	validationManager := NewValidationManager(propagator, headerManager, baggageManager)
	debugManager := NewDebugManager(propagator, headerManager, baggageManager, validationManager)
	
	return &ContextManager{
		jaegerClient:       jaegerClient,
		propagator:         propagator,
		headerManager:      headerManager,
		baggageManager:     baggageManager,
		correlationManager: correlationManager,
		validationManager:  validationManager,
		debugManager:       debugManager,
		config:             config,
	}, nil
}

// GetJaegerClient returns the Jaeger client
func (m *ContextManager) GetJaegerClient() *tracing.JaegerClient {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.jaegerClient
}

// GetPropagator returns the propagator
func (m *ContextManager) GetPropagator() *Propagator {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.propagator
}

// GetHeaderManager returns the header manager
func (m *ContextManager) GetHeaderManager() *HeaderManager {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.headerManager
}

// GetBaggageManager returns the baggage manager
func (m *ContextManager) GetBaggageManager() *BaggageManager {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.baggageManager
}

// GetCorrelationManager returns the correlation manager
func (m *ContextManager) GetCorrelationManager() *CorrelationManager {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.correlationManager
}

// GetValidationManager returns the validation manager
func (m *ContextManager) GetValidationManager() *ValidationManager {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.validationManager
}

// GetDebugManager returns the debug manager
func (m *ContextManager) GetDebugManager() *DebugManager {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.debugManager
}

// GetConfig returns the configuration
func (m *ContextManager) GetConfig() *ContextConfig {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.config
}

// HTTPMiddleware returns comprehensive HTTP middleware for context propagation
func (m *ContextManager) HTTPMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Extract trace context from headers
			ctx := m.headerManager.ExtractFromHeaders(m.headerManager.HeadersToMap(r.Header))
			ctx = m.baggageManager.ExtractBaggageFromHeaders(ctx, r.Header)
			
			// Update request context
			r = r.WithContext(ctx)
			
			// Validation
			if m.config.EnableValidation {
				if result := m.validationManager.ValidateContext(ctx); !result.Valid {
					// Log validation errors but continue processing
					if m.config.EnableDebug {
						m.debugManager.LogDebugSummary(ctx, r, "VALIDATION_FAILED")
					}
				}
			}
			
			// Security checks
			if m.config.EnableSecurityChecks {
				if result := m.validationManager.SecurityValidation(ctx, r); !result.Valid {
					// Log security violations but continue processing
					if m.config.EnableDebug {
						m.debugManager.LogDebugSummary(ctx, r, "SECURITY_VIOLATION")
					}
				}
			}
			
			// Sanitization
			if m.config.EnableSanitization {
				ctx = m.validationManager.SanitizeContext(ctx)
				r = r.WithContext(ctx)
			}
			
			// Debug logging
			if m.config.EnableDebug && m.config.LogDebugSummary {
				m.debugManager.LogDebugSummary(ctx, r, "REQUEST_PROCESSED")
			}
			
			// Call next handler
			next.ServeHTTP(w, r)
		})
	}
}

// GinMiddleware returns comprehensive Gin middleware for context propagation
func (m *ContextManager) GinMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract trace context from headers
		ctx := m.headerManager.ExtractFromHeaders(m.headerManager.HeadersToMap(c.Request.Header))
		ctx = m.baggageManager.ExtractBaggageFromHeaders(ctx, c.Request.Header)
		
		// Update request context
		c.Request = c.Request.WithContext(ctx)
		
		// Validation
		if m.config.EnableValidation {
			if result := m.validationManager.ValidateContext(ctx); !result.Valid {
				// Log validation errors but continue processing
				if m.config.EnableDebug {
					m.debugManager.LogDebugSummary(ctx, c.Request, "VALIDATION_FAILED")
				}
			}
		}
		
		// Security checks
		if m.config.EnableSecurityChecks {
			if result := m.validationManager.SecurityValidation(ctx, c.Request); !result.Valid {
				// Log security violations but continue processing
				if m.config.EnableDebug {
					m.debugManager.LogDebugSummary(ctx, c.Request, "SECURITY_VIOLATION")
				}
			}
		}
		
		// Sanitization
		if m.config.EnableSanitization {
			ctx = m.validationManager.SanitizeContext(ctx)
			c.Request = c.Request.WithContext(ctx)
		}
		
		// Debug logging
		if m.config.EnableDebug && m.config.LogDebugSummary {
			m.debugManager.LogDebugSummary(ctx, c.Request, "REQUEST_PROCESSED")
		}
		
		// Continue processing
		c.Next()
	}
}

// StartSpan starts a new span with context propagation
func (m *ContextManager) StartSpan(ctx context.Context, operationName string, options ...interface{}) (context.Context, func()) {
	// Create span
	spanCtx, span := m.jaegerClient.StartSpan(ctx, operationName)
	
	// Return context and cleanup function
	return spanCtx, func() {
		span.End()
	}
}

// CreateCorrelationContext creates a correlation context for cross-service calls
func (m *ContextManager) CreateCorrelationContext(ctx context.Context, userID, tenantID, sessionID string) (context.Context, error) {
	// Set user baggage
	ctx, err := m.baggageManager.SetUserBaggage(ctx, userID, tenantID, sessionID)
	if err != nil {
		return ctx, err
	}
	
	// Set service baggage
	ctx, err = m.baggageManager.SetServiceBaggage(ctx, 
		m.config.JaegerConfig.ServiceName, 
		m.config.JaegerConfig.ServiceVersion, 
		m.config.JaegerConfig.Environment)
	if err != nil {
		return ctx, err
	}
	
	return ctx, nil
}

// PropagateToHTTPRequest propagates context to HTTP request
func (m *ContextManager) PropagateToHTTPRequest(ctx context.Context, req *http.Request) error {
	// Inject trace headers
	m.headerManager.InjectTraceHeaders(ctx, req)
	
	// Inject baggage
	m.baggageManager.InjectBaggageToHeaders(ctx, req.Header)
	
	// Debug logging
	if m.config.EnableDebug && m.config.LogDebugSummary {
		m.debugManager.LogDebugSummary(ctx, req, "CONTEXT_PROPAGATED")
	}
	
	return nil
}

// ValidateAndSanitize validates and sanitizes context
func (m *ContextManager) ValidateAndSanitize(ctx context.Context, req *http.Request) (context.Context, error) {
	// Validate context
	if m.config.EnableValidation {
		if result := m.validationManager.ValidateContext(ctx); !result.Valid {
			if m.config.EnableDebug {
				m.debugManager.LogDebugSummary(ctx, req, "VALIDATION_FAILED")
			}
		}
	}
	
	// Security validation
	if m.config.EnableSecurityChecks {
		if result := m.validationManager.SecurityValidation(ctx, req); !result.Valid {
			if m.config.EnableDebug {
				m.debugManager.LogDebugSummary(ctx, req, "SECURITY_VIOLATION")
			}
		}
	}
	
	// Sanitize context
	if m.config.EnableSanitization {
		ctx = m.validationManager.SanitizeContext(ctx)
	}
	
	return ctx, nil
}

// GetDebugInfo returns debug information
func (m *ContextManager) GetDebugInfo(ctx context.Context, req *http.Request) *DebugInfo {
	return m.debugManager.GetDebugInfo(ctx, req)
}

// GetCorrelationData extracts correlation data
func (m *ContextManager) GetCorrelationData(ctx context.Context) *CorrelationData {
	return m.correlationManager.ExtractCorrelationData(ctx)
}

// Shutdown gracefully shuts down the context manager
func (m *ContextManager) Shutdown(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	// Shutdown Jaeger client
	if m.jaegerClient != nil {
		return m.jaegerClient.Shutdown(ctx)
	}
	
	return nil
}

// UpdateConfig updates the configuration
func (m *ContextManager) UpdateConfig(config *ContextConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}
	
	m.config = config
	return nil
}

// GetHealthStatus returns health status of the context manager
func (m *ContextManager) GetHealthStatus() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	status := map[string]interface{}{
		"jaeger_enabled":       m.jaegerClient.IsEnabled(),
		"validation_enabled":   m.config.EnableValidation,
		"sanitization_enabled": m.config.EnableSanitization,
		"security_enabled":     m.config.EnableSecurityChecks,
		"debug_enabled":        m.config.EnableDebug,
		"service_name":         m.config.JaegerConfig.ServiceName,
		"service_version":      m.config.JaegerConfig.ServiceVersion,
		"environment":          m.config.JaegerConfig.Environment,
	}
	
	return status
}

// GetMetrics returns metrics for the context manager
func (m *ContextManager) GetMetrics() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	// This would typically integrate with a metrics system
	metrics := map[string]interface{}{
		"context_manager_healthy": true,
		"jaeger_connected":        m.jaegerClient.IsEnabled(),
		"config_loaded":           m.config != nil,
	}
	
	return metrics
}

// Global context manager instance
var globalContextManager *ContextManager

// InitializeGlobalContextManager initializes the global context manager
func InitializeGlobalContextManager(config *ContextConfig) error {
	manager, err := NewContextManager(config)
	if err != nil {
		return err
	}
	
	globalContextManager = manager
	
	// Set global instances
	SetGlobalPropagator(manager.propagator)
	SetGlobalHeaderManager(manager.headerManager)
	SetGlobalBaggageManager(manager.baggageManager)
	SetGlobalCorrelationManager(manager.correlationManager)
	SetGlobalValidationManager(manager.validationManager)
	SetGlobalDebugManager(manager.debugManager)
	
	return nil
}

// GetGlobalContextManager returns the global context manager
func GetGlobalContextManager() *ContextManager {
	return globalContextManager
}

// SetGlobalContextManager sets the global context manager
func SetGlobalContextManager(manager *ContextManager) {
	globalContextManager = manager
}

// Quick setup functions for common use cases

// SetupForDevelopment sets up context manager for development
func SetupForDevelopment(serviceName, serviceVersion string) (*ContextManager, error) {
	config := DefaultContextConfig()
	config.JaegerConfig.ServiceName = serviceName
	config.JaegerConfig.ServiceVersion = serviceVersion
	config.JaegerConfig.Environment = "development"
	config.JaegerConfig.SampleRate = 1.0 // Sample all traces in development
	config.EnableDebug = true
	config.LogDebugSummary = true
	config.DebugVerbose = true
	
	return NewContextManager(config)
}

// SetupForProduction sets up context manager for production
func SetupForProduction(serviceName, serviceVersion string) (*ContextManager, error) {
	config := DefaultContextConfig()
	config.JaegerConfig.ServiceName = serviceName
	config.JaegerConfig.ServiceVersion = serviceVersion
	config.JaegerConfig.Environment = "production"
	config.JaegerConfig.SampleRate = 0.1 // Sample 10% of traces in production
	config.EnableDebug = false
	config.LogDebugSummary = false
	config.DebugVerbose = false
	config.EnableSecurityChecks = true
	
	return NewContextManager(config)
}

// SetupForTesting sets up context manager for testing
func SetupForTesting(serviceName string) (*ContextManager, error) {
	config := DefaultContextConfig()
	config.JaegerConfig.ServiceName = serviceName
	config.JaegerConfig.ServiceVersion = "test"
	config.JaegerConfig.Environment = "test"
	config.JaegerConfig.Disabled = true // Disable Jaeger in tests
	config.EnableDebug = false
	config.LogDebugSummary = false
	config.EnableValidation = true
	config.EnableSanitization = true
	config.EnableSecurityChecks = false
	
	return NewContextManager(config)
}