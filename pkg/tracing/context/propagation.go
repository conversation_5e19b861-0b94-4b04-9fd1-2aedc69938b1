package context

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
)

// Propagator handles context propagation for distributed tracing
type Propagator struct {
	textMapPropagator propagation.TextMapPropagator
}

// NewPropagator creates a new context propagator
func NewPropagator() *Propagator {
	// Create a composite propagator that supports multiple formats
	propagator := propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	)

	// Set as global propagator
	otel.SetTextMapPropagator(propagator)

	return &Propagator{
		textMapPropagator: propagator,
	}
}

// InjectHTTPHeaders injects trace context into HTTP headers
func (p *Propagator) InjectHTTPHeaders(ctx context.Context, req *http.Request) {
	p.textMapPropagator.Inject(ctx, propagation.HeaderCarrier(req.<PERSON><PERSON>))
}

// ExtractHTTPHeaders extracts trace context from HTTP headers
func (p *Propagator) ExtractHTTPHeaders(ctx context.Context, req *http.Request) context.Context {
	return p.textMapPropagator.Extract(ctx, propagation.HeaderCarrier(req.Header))
}

// InjectHeaders injects trace context into generic headers map
func (p *Propagator) InjectHeaders(ctx context.Context, headers map[string]string) {
	carrier := propagation.MapCarrier(headers)
	p.textMapPropagator.Inject(ctx, carrier)
}

// ExtractHeaders extracts trace context from generic headers map
func (p *Propagator) ExtractHeaders(ctx context.Context, headers map[string]string) context.Context {
	carrier := propagation.MapCarrier(headers)
	return p.textMapPropagator.Extract(ctx, carrier)
}

// PropagateToContext propagates trace context to a child context
func (p *Propagator) PropagateToContext(parent context.Context, child context.Context) context.Context {
	// Get the span from parent context
	span := trace.SpanFromContext(parent)
	if !span.IsRecording() {
		return child
	}

	// Create new context with span
	return trace.ContextWithSpan(child, span)
}

// PropagateSpan propagates a span to a new context
func (p *Propagator) PropagateSpan(ctx context.Context, span trace.Span) context.Context {
	return trace.ContextWithSpan(ctx, span)
}

// GetTraceID returns the trace ID from context
func (p *Propagator) GetTraceID(ctx context.Context) string {
	spanContext := trace.SpanFromContext(ctx).SpanContext()
	if spanContext.IsValid() {
		return spanContext.TraceID().String()
	}
	return ""
}

// GetSpanID returns the span ID from context
func (p *Propagator) GetSpanID(ctx context.Context) string {
	spanContext := trace.SpanFromContext(ctx).SpanContext()
	if spanContext.IsValid() {
		return spanContext.SpanID().String()
	}
	return ""
}

// IsTraceActive checks if there's an active trace in the context
func (p *Propagator) IsTraceActive(ctx context.Context) bool {
	span := trace.SpanFromContext(ctx)
	return span.IsRecording()
}

// GetRemoteSpanContext returns the remote span context from headers
func (p *Propagator) GetRemoteSpanContext(headers map[string]string) (trace.SpanContext, bool) {
	carrier := propagation.MapCarrier(headers)
	ctx := p.textMapPropagator.Extract(context.Background(), carrier)
	
	spanContext := trace.SpanFromContext(ctx).SpanContext()
	return spanContext, spanContext.IsValid()
}

// CreateChildContext creates a child context with proper trace propagation
func (p *Propagator) CreateChildContext(parent context.Context, operationName string) (context.Context, trace.Span) {
	tracer := otel.Tracer("wn-api-v3")
	return tracer.Start(parent, operationName)
}

// MiddlewareFunc is a middleware function that handles context propagation
type MiddlewareFunc func(next http.Handler) http.Handler

// HTTPMiddleware returns an HTTP middleware that handles context propagation
func (p *Propagator) HTTPMiddleware() MiddlewareFunc {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Extract trace context from incoming request
			ctx := p.ExtractHTTPHeaders(r.Context(), r)
			
			// Create a new request with the updated context
			r = r.WithContext(ctx)
			
			// Call the next handler
			next.ServeHTTP(w, r)
		})
	}
}

// GinMiddleware returns a Gin middleware that handles context propagation
func (p *Propagator) GinMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract trace context from incoming request
		ctx := p.ExtractHTTPHeaders(c.Request.Context(), c.Request)
		
		// Set the updated context in Gin
		c.Request = c.Request.WithContext(ctx)
		
		// Continue with the request
		c.Next()
	}
}

// GetPropagator returns the underlying text map propagator
func (p *Propagator) GetPropagator() propagation.TextMapPropagator {
	return p.textMapPropagator
}

// Fields returns the fields that this propagator will read/write
func (p *Propagator) Fields() []string {
	return p.textMapPropagator.Fields()
}

// CloneContext creates a copy of the context with trace information
func (p *Propagator) CloneContext(ctx context.Context) context.Context {
	// Create a new background context
	newCtx := context.Background()
	
	// Propagate trace information
	span := trace.SpanFromContext(ctx)
	if span.IsRecording() {
		newCtx = trace.ContextWithSpan(newCtx, span)
	}
	
	return newCtx
}

// Global propagator instance
var globalPropagator *Propagator

// GetGlobalPropagator returns the global propagator instance
func GetGlobalPropagator() *Propagator {
	if globalPropagator == nil {
		globalPropagator = NewPropagator()
	}
	return globalPropagator
}

// SetGlobalPropagator sets the global propagator instance
func SetGlobalPropagator(p *Propagator) {
	globalPropagator = p
}