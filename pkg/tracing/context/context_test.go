package context

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/tranthanhloi/wn-api-v3/pkg/tracing"
)

func TestContextPropagation(t *testing.T) {
	// Setup test context manager
	config := &ContextConfig{
		JaegerConfig: &tracing.JaegerConfig{
			ServiceName:    "test-service",
			ServiceVersion: "1.0.0",
			Environment:    "test",
			Disabled:       true, // Disable Jaeger for tests
		},
		EnableValidation:   true,
		EnableSanitization: true,
		EnableDebug:        false,
	}

	manager, err := NewContextManager(config)
	require.NoError(t, err)
	require.NotNil(t, manager)

	t.Run("TestBasicPropagation", func(t *testing.T) {
		// Create context with trace
		ctx, cleanup := manager.StartSpan(context.Background(), "test-operation")
		defer cleanup()

		// Verify trace context exists
		traceID := manager.GetPropagator().GetTraceID(ctx)
		assert.NotEmpty(t, traceID)

		spanID := manager.GetPropagator().GetSpanID(ctx)
		assert.NotEmpty(t, spanID)
	})

	t.Run("TestBaggagePropagation", func(t *testing.T) {
		// Create context with trace
		ctx, cleanup := manager.StartSpan(context.Background(), "test-operation")
		defer cleanup()

		// Add baggage
		baggageManager := manager.GetBaggageManager()
		ctx, err := baggageManager.SetBaggage(ctx, "user_id", "user-123")
		require.NoError(t, err)

		// Verify baggage
		userID, found := baggageManager.GetBaggage(ctx, "user_id")
		assert.True(t, found)
		assert.Equal(t, "user-123", userID)
	})

	t.Run("TestHeaderPropagation", func(t *testing.T) {
		// Create context with trace
		ctx, cleanup := manager.StartSpan(context.Background(), "test-operation")
		defer cleanup()

		// Add baggage
		baggageManager := manager.GetBaggageManager()
		ctx, err := baggageManager.SetBaggage(ctx, "service_name", "test-service")
		require.NoError(t, err)

		// Create HTTP request
		req, err := http.NewRequestWithContext(ctx, "GET", "http://example.com", nil)
		require.NoError(t, err)

		// Inject headers
		err = manager.PropagateToHTTPRequest(ctx, req)
		require.NoError(t, err)

		// Verify headers were injected
		assert.NotEmpty(t, req.Header.Get("traceparent"))
		assert.NotEmpty(t, req.Header.Get("baggage"))
	})

	t.Run("TestValidation", func(t *testing.T) {
		// Create context with trace
		ctx, cleanup := manager.StartSpan(context.Background(), "test-operation")
		defer cleanup()

		// Add valid baggage
		baggageManager := manager.GetBaggageManager()
		ctx, err := baggageManager.SetBaggage(ctx, "valid_key", "valid_value")
		require.NoError(t, err)

		// Validate context
		validationManager := manager.GetValidationManager()
		result := validationManager.ValidateContext(ctx)
		assert.True(t, result.Valid)
		assert.Empty(t, result.Errors)
	})

	t.Run("TestCorrelation", func(t *testing.T) {
		// Create context with trace
		ctx, cleanup := manager.StartSpan(context.Background(), "test-operation")
		defer cleanup()

		// Create correlation context
		ctx, err := manager.CreateCorrelationContext(ctx, "user-123", "tenant-456", "session-789")
		require.NoError(t, err)

		// Get correlation data
		correlationData := manager.GetCorrelationData(ctx)
		assert.NotNil(t, correlationData)
		assert.Equal(t, "user-123", correlationData.UserID)
		assert.Equal(t, "tenant-456", correlationData.TenantID)
		assert.Equal(t, "session-789", correlationData.SessionID)
	})

	t.Run("TestDebugInfo", func(t *testing.T) {
		// Create context with trace
		ctx, cleanup := manager.StartSpan(context.Background(), "test-operation")
		defer cleanup()

		// Add baggage
		baggageManager := manager.GetBaggageManager()
		ctx, err := baggageManager.SetBaggage(ctx, "debug_key", "debug_value")
		require.NoError(t, err)

		// Create mock request
		req, err := http.NewRequestWithContext(ctx, "GET", "http://example.com", nil)
		require.NoError(t, err)

		// Get debug info
		debugInfo := manager.GetDebugInfo(ctx, req)
		assert.NotNil(t, debugInfo)
		assert.NotEmpty(t, debugInfo.TraceContext.TraceID)
		assert.Equal(t, 1, debugInfo.BaggageInfo.Count)
		assert.True(t, debugInfo.ValidationInfo.IsValid)
	})

	// Cleanup
	err = manager.Shutdown(context.Background())
	require.NoError(t, err)
}

func TestBaggageManager(t *testing.T) {
	manager := NewBaggageManager()
	ctx := context.Background()

	t.Run("TestSetGetBaggage", func(t *testing.T) {
		ctx, err := manager.SetBaggage(ctx, "key1", "value1")
		require.NoError(t, err)

		value, found := manager.GetBaggage(ctx, "key1")
		assert.True(t, found)
		assert.Equal(t, "value1", value)
	})

	t.Run("TestMultipleBaggage", func(t *testing.T) {
		items := map[string]string{
			"key1": "value1",
			"key2": "value2",
			"key3": "value3",
		}

		ctx, err := manager.SetMultipleBaggage(ctx, items)
		require.NoError(t, err)

		for key, expectedValue := range items {
			value, found := manager.GetBaggage(ctx, key)
			assert.True(t, found)
			assert.Equal(t, expectedValue, value)
		}
	})

	t.Run("TestBaggageValidation", func(t *testing.T) {
		// Test valid key
		err := manager.ValidateBaggageKey("valid_key")
		assert.NoError(t, err)

		// Test invalid key (empty)
		err = manager.ValidateBaggageKey("")
		assert.Error(t, err)

		// Test invalid key (too long)
		longKey := make([]byte, 300)
		for i := range longKey {
			longKey[i] = 'a'
		}
		err = manager.ValidateBaggageKey(string(longKey))
		assert.Error(t, err)

		// Test valid value
		err = manager.ValidateBaggageValue("valid_value")
		assert.NoError(t, err)

		// Test invalid value (too long)
		longValue := make([]byte, 9000)
		for i := range longValue {
			longValue[i] = 'a'
		}
		err = manager.ValidateBaggageValue(string(longValue))
		assert.Error(t, err)
	})
}

func TestHeaderManager(t *testing.T) {
	propagator := NewPropagator()
	manager := NewHeaderManager(propagator)

	t.Run("TestTraceHeaderValidation", func(t *testing.T) {
		// Test valid traceparent
		headers := &TraceHeaders{
			TraceParent: "00-12345678901234567890123456789012-1234567890123456-01",
		}
		err := manager.ValidateTraceHeaders(headers)
		assert.NoError(t, err)

		// Test invalid traceparent
		headers.TraceParent = "invalid-traceparent"
		err = manager.ValidateTraceHeaders(headers)
		assert.Error(t, err)

		// Test valid correlation ID
		headers.CorrelationID = "correlation-123"
		err = manager.ValidateTraceHeaders(headers)
		assert.NoError(t, err)

		// Test invalid correlation ID (too short)
		headers.CorrelationID = "short"
		err = manager.ValidateTraceHeaders(headers)
		assert.Error(t, err)
	})

	t.Run("TestHeaderExtraction", func(t *testing.T) {
		// Create mock request
		req, err := http.NewRequest("GET", "http://example.com", nil)
		require.NoError(t, err)

		// Set headers
		req.Header.Set("traceparent", "00-12345678901234567890123456789012-1234567890123456-01")
		req.Header.Set("x-correlation-id", "correlation-123")

		// Extract headers
		headers := manager.ExtractTraceHeaders(req)
		assert.Equal(t, "00-12345678901234567890123456789012-1234567890123456-01", headers.TraceParent)
		assert.Equal(t, "correlation-123", headers.CorrelationID)
	})
}

func TestValidationManager(t *testing.T) {
	propagator := NewPropagator()
	headerManager := NewHeaderManager(propagator)
	baggageManager := NewBaggageManager()
	manager := NewValidationManager(propagator, headerManager, baggageManager)

	t.Run("TestContextValidation", func(t *testing.T) {
		// Create context with trace
		ctx, span := propagator.CreateChildContext(context.Background(), "test-operation")
		defer span.End()

		// Add valid baggage
		ctx, err := baggageManager.SetBaggage(ctx, "valid_key", "valid_value")
		require.NoError(t, err)

		// Validate context
		result := manager.ValidateContext(ctx)
		assert.True(t, result.Valid)
		assert.Empty(t, result.Errors)
	})

	t.Run("TestContextSanitization", func(t *testing.T) {
		// Create context with trace
		ctx, span := propagator.CreateChildContext(context.Background(), "test-operation")
		defer span.End()

		// Add baggage with control characters
		ctx, err := baggageManager.SetBaggage(ctx, "test_key", "value_with\x00control\x1fchars")
		require.NoError(t, err)

		// Sanitize context
		sanitizedCtx := manager.SanitizeContext(ctx)

		// Verify sanitization
		value, found := baggageManager.GetBaggage(sanitizedCtx, "test_key")
		assert.True(t, found)
		assert.NotContains(t, value, "\x00")
		assert.NotContains(t, value, "\x1f")
	})
}

func TestGlobalInstances(t *testing.T) {
	// Initialize global context manager
	config := &ContextConfig{
		JaegerConfig: &tracing.JaegerConfig{
			ServiceName:    "test-global",
			ServiceVersion: "1.0.0",
			Environment:    "test",
			Disabled:       true,
		},
		EnableValidation: true,
		EnableDebug:      false,
	}

	err := InitializeGlobalContextManager(config)
	require.NoError(t, err)

	// Test global instances
	manager := GetGlobalContextManager()
	assert.NotNil(t, manager)

	propagator := GetGlobalPropagator()
	assert.NotNil(t, propagator)

	headerManager := GetGlobalHeaderManager()
	assert.NotNil(t, headerManager)

	baggageManager := GetGlobalBaggageManager()
	assert.NotNil(t, baggageManager)

	correlationManager := GetGlobalCorrelationManager()
	assert.NotNil(t, correlationManager)

	validationManager := GetGlobalValidationManager()
	assert.NotNil(t, validationManager)

	debugManager := GetGlobalDebugManager()
	assert.NotNil(t, debugManager)
}

func TestQuickSetup(t *testing.T) {
	t.Run("TestSetupForTesting", func(t *testing.T) {
		manager, err := SetupForTesting("test-service")
		require.NoError(t, err)
		assert.NotNil(t, manager)

		config := manager.GetConfig()
		assert.Equal(t, "test-service", config.JaegerConfig.ServiceName)
		assert.Equal(t, "test", config.JaegerConfig.Environment)
		assert.True(t, config.JaegerConfig.Disabled)
	})

	t.Run("TestSetupForDevelopment", func(t *testing.T) {
		manager, err := SetupForDevelopment("dev-service", "1.0.0")
		require.NoError(t, err)
		assert.NotNil(t, manager)

		config := manager.GetConfig()
		assert.Equal(t, "dev-service", config.JaegerConfig.ServiceName)
		assert.Equal(t, "1.0.0", config.JaegerConfig.ServiceVersion)
		assert.Equal(t, "development", config.JaegerConfig.Environment)
		assert.True(t, config.EnableDebug)
	})

	t.Run("TestSetupForProduction", func(t *testing.T) {
		manager, err := SetupForProduction("prod-service", "2.0.0")
		require.NoError(t, err)
		assert.NotNil(t, manager)

		config := manager.GetConfig()
		assert.Equal(t, "prod-service", config.JaegerConfig.ServiceName)
		assert.Equal(t, "2.0.0", config.JaegerConfig.ServiceVersion)
		assert.Equal(t, "production", config.JaegerConfig.Environment)
		assert.False(t, config.EnableDebug)
		assert.True(t, config.EnableSecurityChecks)
	})
}