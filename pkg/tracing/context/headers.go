package context

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
)

// Common trace header names
const (
	// W3C Trace Context headers
	TraceParentHeader = "traceparent"
	TraceStateHeader  = "tracestate"
	
	// Legacy headers for backward compatibility
	JaegerTraceHeader = "uber-trace-id"
	B3TraceIDHeader   = "x-b3-traceid"
	B3SpanIDHeader    = "x-b3-spanid"
	B3SampledHeader   = "x-b3-sampled"
	B3FlagsHeader     = "x-b3-flags"
	
	// Custom headers
	CorrelationIDHeader = "x-correlation-id"
	RequestIDHeader     = "x-request-id"
	
	// Service-to-service headers
	ServiceNameHeader    = "x-service-name"
	ServiceVersionHeader = "x-service-version"
	UserAgentHeader      = "user-agent"
)

// HeaderManager manages trace context headers
type HeaderManager struct {
	propagator *Propagator
}

// NewHeaderManager creates a new header manager
func NewHeaderManager(propagator *Propagator) *HeaderManager {
	return &HeaderManager{
		propagator: propagator,
	}
}

// TraceHeaders represents trace-related headers
type TraceHeaders struct {
	TraceParent   string
	TraceState    string
	CorrelationID string
	RequestID     string
	ServiceName   string
	ServiceVersion string
	UserAgent     string
}

// ExtractTraceHeaders extracts trace headers from HTTP request
func (h *HeaderManager) ExtractTraceHeaders(req *http.Request) *TraceHeaders {
	return &TraceHeaders{
		TraceParent:    req.Header.Get(TraceParentHeader),
		TraceState:     req.Header.Get(TraceStateHeader),
		CorrelationID:  req.Header.Get(CorrelationIDHeader),
		RequestID:      req.Header.Get(RequestIDHeader),
		ServiceName:    req.Header.Get(ServiceNameHeader),
		ServiceVersion: req.Header.Get(ServiceVersionHeader),
		UserAgent:      req.Header.Get(UserAgentHeader),
	}
}

// InjectTraceHeaders injects trace headers into HTTP request
func (h *HeaderManager) InjectTraceHeaders(ctx context.Context, req *http.Request) {
	// Use the propagator to inject standard headers
	h.propagator.InjectHTTPHeaders(ctx, req)
	
	// Add custom headers
	h.injectCustomHeaders(ctx, req)
}

// injectCustomHeaders injects custom service headers
func (h *HeaderManager) injectCustomHeaders(ctx context.Context, req *http.Request) {
	// Add service identification headers
	if req.Header.Get(ServiceNameHeader) == "" {
		req.Header.Set(ServiceNameHeader, "wn-api-v3")
	}
	if req.Header.Get(ServiceVersionHeader) == "" {
		req.Header.Set(ServiceVersionHeader, "1.0.0")
	}
	
	// Add correlation ID if not present
	if req.Header.Get(CorrelationIDHeader) == "" {
		if traceID := h.propagator.GetTraceID(ctx); traceID != "" {
			req.Header.Set(CorrelationIDHeader, traceID)
		}
	}
	
	// Add request ID if not present
	if req.Header.Get(RequestIDHeader) == "" {
		if spanID := h.propagator.GetSpanID(ctx); spanID != "" {
			req.Header.Set(RequestIDHeader, spanID)
		}
	}
}

// ExtractFromHeaders extracts trace context from headers map
func (h *HeaderManager) ExtractFromHeaders(headers map[string]string) context.Context {
	return h.propagator.ExtractHeaders(context.Background(), headers)
}

// InjectToHeaders injects trace context into headers map
func (h *HeaderManager) InjectToHeaders(ctx context.Context, headers map[string]string) {
	h.propagator.InjectHeaders(ctx, headers)
}

// GetTraceContextFromHeaders creates trace context from headers
func (h *HeaderManager) GetTraceContextFromHeaders(headers map[string]string) (trace.SpanContext, bool) {
	return h.propagator.GetRemoteSpanContext(headers)
}

// ValidateTraceHeaders validates trace headers
func (h *HeaderManager) ValidateTraceHeaders(headers *TraceHeaders) error {
	// Validate traceparent format if present
	if headers.TraceParent != "" {
		if !h.isValidTraceParent(headers.TraceParent) {
			return fmt.Errorf("invalid traceparent format: %s", headers.TraceParent)
		}
	}
	
	// Validate correlation ID format if present
	if headers.CorrelationID != "" {
		if !h.isValidCorrelationID(headers.CorrelationID) {
			return fmt.Errorf("invalid correlation ID format: %s", headers.CorrelationID)
		}
	}
	
	return nil
}

// isValidTraceParent validates traceparent header format
func (h *HeaderManager) isValidTraceParent(traceparent string) bool {
	// W3C Trace Context format: version-trace_id-parent_id-trace_flags
	parts := strings.Split(traceparent, "-")
	if len(parts) != 4 {
		return false
	}
	
	// Check version (2 hex digits)
	if len(parts[0]) != 2 {
		return false
	}
	
	// Check trace ID (32 hex digits)
	if len(parts[1]) != 32 {
		return false
	}
	
	// Check parent ID (16 hex digits)
	if len(parts[2]) != 16 {
		return false
	}
	
	// Check flags (2 hex digits)
	if len(parts[3]) != 2 {
		return false
	}
	
	return true
}

// isValidCorrelationID validates correlation ID format
func (h *HeaderManager) isValidCorrelationID(correlationID string) bool {
	// Allow alphanumeric characters, hyphens, and underscores
	// Length should be between 8 and 128 characters
	if len(correlationID) < 8 || len(correlationID) > 128 {
		return false
	}
	
	for _, char := range correlationID {
		if !((char >= 'a' && char <= 'z') || 
			 (char >= 'A' && char <= 'Z') || 
			 (char >= '0' && char <= '9') || 
			 char == '-' || char == '_') {
			return false
		}
	}
	
	return true
}

// GetRequiredHeaders returns list of required headers for trace propagation
func (h *HeaderManager) GetRequiredHeaders() []string {
	return []string{
		TraceParentHeader,
		TraceStateHeader,
	}
}

// GetOptionalHeaders returns list of optional headers for enhanced tracing
func (h *HeaderManager) GetOptionalHeaders() []string {
	return []string{
		CorrelationIDHeader,
		RequestIDHeader,
		ServiceNameHeader,
		ServiceVersionHeader,
		UserAgentHeader,
	}
}

// GetAllHeaders returns all headers managed by this manager
func (h *HeaderManager) GetAllHeaders() []string {
	required := h.GetRequiredHeaders()
	optional := h.GetOptionalHeaders()
	
	all := make([]string, len(required)+len(optional))
	copy(all, required)
	copy(all[len(required):], optional)
	
	return all
}

// CopyHeaders copies trace headers from source to destination
func (h *HeaderManager) CopyHeaders(src, dst http.Header) {
	for _, header := range h.GetAllHeaders() {
		if value := src.Get(header); value != "" {
			dst.Set(header, value)
		}
	}
}

// CleanHeaders removes invalid or expired headers
func (h *HeaderManager) CleanHeaders(headers http.Header) {
	for _, header := range h.GetAllHeaders() {
		value := headers.Get(header)
		if value != "" {
			// Validate header value
			if !h.isValidHeaderValue(header, value) {
				headers.Del(header)
			}
		}
	}
}

// isValidHeaderValue validates header value based on header type
func (h *HeaderManager) isValidHeaderValue(header, value string) bool {
	switch header {
	case TraceParentHeader:
		return h.isValidTraceParent(value)
	case CorrelationIDHeader:
		return h.isValidCorrelationID(value)
	case RequestIDHeader:
		return h.isValidCorrelationID(value) // Same validation as correlation ID
	default:
		return true // Allow other headers
	}
}

// HeadersToMap converts HTTP headers to map
func (h *HeaderManager) HeadersToMap(headers http.Header) map[string]string {
	result := make(map[string]string)
	for _, header := range h.GetAllHeaders() {
		if value := headers.Get(header); value != "" {
			result[header] = value
		}
	}
	return result
}

// MapToHeaders converts map to HTTP headers
func (h *HeaderManager) MapToHeaders(headerMap map[string]string) http.Header {
	headers := make(http.Header)
	for key, value := range headerMap {
		headers.Set(key, value)
	}
	return headers
}

// Global header manager instance
var globalHeaderManager *HeaderManager

// GetGlobalHeaderManager returns the global header manager instance
func GetGlobalHeaderManager() *HeaderManager {
	if globalHeaderManager == nil {
		globalHeaderManager = NewHeaderManager(GetGlobalPropagator())
	}
	return globalHeaderManager
}

// SetGlobalHeaderManager sets the global header manager instance
func SetGlobalHeaderManager(hm *HeaderManager) {
	globalHeaderManager = hm
}