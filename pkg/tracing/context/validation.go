package context

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"go.opentelemetry.io/otel/trace"
)

// ValidationManager handles context validation and sanitization
type ValidationManager struct {
	propagator     *Propagator
	headerManager  *HeaderManager
	baggageManager *BaggageManager
}

// NewValidationManager creates a new validation manager
func NewValidationManager(propagator *Propagator, headerManager *HeaderManager, baggageManager *BaggageManager) *ValidationManager {
	return &ValidationManager{
		propagator:     propagator,
		headerManager:  headerManager,
		baggageManager: baggageManager,
	}
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Value   string
	Message string
	Code    string
}

// Error returns the error message
func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation error for field %s: %s", e.Field, e.Message)
}

// ValidationResult represents the result of validation
type ValidationResult struct {
	Valid  bool
	Errors []ValidationError
}

// AddError adds a validation error
func (v *ValidationResult) AddError(field, value, message, code string) {
	v.Valid = false
	v.Errors = append(v.Errors, ValidationError{
		Field:   field,
		Value:   value,
		Message: message,
		Code:    code,
	})
}

// HasErrors returns true if there are validation errors
func (v *ValidationResult) HasErrors() bool {
	return len(v.Errors) > 0
}

// GetErrorMessages returns all error messages
func (v *ValidationResult) GetErrorMessages() []string {
	messages := make([]string, len(v.Errors))
	for i, err := range v.Errors {
		messages[i] = err.Message
	}
	return messages
}

// ValidateContext validates the trace context
func (v *ValidationManager) ValidateContext(ctx context.Context) *ValidationResult {
	result := &ValidationResult{Valid: true}
	
	// Validate trace context
	v.validateTraceContext(ctx, result)
	
	// Validate baggage
	v.validateBaggage(ctx, result)
	
	return result
}

// validateTraceContext validates the trace context
func (v *ValidationManager) validateTraceContext(ctx context.Context, result *ValidationResult) {
	span := trace.SpanFromContext(ctx)
	spanContext := span.SpanContext()
	
	// Check if span context is valid
	if !spanContext.IsValid() {
		result.AddError("trace_context", "", "invalid or missing trace context", "INVALID_TRACE_CONTEXT")
		return
	}
	
	// Validate trace ID
	traceID := spanContext.TraceID()
	if !traceID.IsValid() {
		result.AddError("trace_id", traceID.String(), "invalid trace ID", "INVALID_TRACE_ID")
	}
	
	// Validate span ID
	spanID := spanContext.SpanID()
	if !spanID.IsValid() {
		result.AddError("span_id", spanID.String(), "invalid span ID", "INVALID_SPAN_ID")
	}
	
	// Validate trace flags
	flags := spanContext.TraceFlags()
	if !v.isValidTraceFlags(flags) {
		result.AddError("trace_flags", fmt.Sprintf("%d", flags), "invalid trace flags", "INVALID_TRACE_FLAGS")
	}
}

// validateBaggage validates baggage data
func (v *ValidationManager) validateBaggage(ctx context.Context, result *ValidationResult) {
	// Check baggage size
	if !v.baggageManager.IsBaggageWithinLimits(ctx) {
		result.AddError("baggage_size", "", "baggage size exceeds limits", "BAGGAGE_SIZE_EXCEEDED")
	}
	
	// Validate baggage items
	baggageData := v.baggageManager.GetAllBaggage(ctx)
	for _, item := range baggageData.Items {
		// Validate key
		if err := v.baggageManager.ValidateBaggageKey(item.Key); err != nil {
			result.AddError("baggage_key", item.Key, err.Error(), "INVALID_BAGGAGE_KEY")
		}
		
		// Validate value
		if err := v.baggageManager.ValidateBaggageValue(item.Value); err != nil {
			result.AddError("baggage_value", item.Value, err.Error(), "INVALID_BAGGAGE_VALUE")
		}
	}
}

// isValidTraceFlags validates trace flags
func (v *ValidationManager) isValidTraceFlags(flags trace.TraceFlags) bool {
	// Valid trace flags are 0x00 (not sampled) or 0x01 (sampled)
	return flags == 0x00 || flags == 0x01
}

// ValidateHTTPHeaders validates HTTP headers for trace context
func (v *ValidationManager) ValidateHTTPHeaders(headers http.Header) *ValidationResult {
	result := &ValidationResult{Valid: true}
	
	// Extract trace headers
	traceHeaders := v.headerManager.ExtractTraceHeaders(&http.Request{Header: headers})
	
	// Validate trace headers
	if err := v.headerManager.ValidateTraceHeaders(traceHeaders); err != nil {
		result.AddError("trace_headers", "", err.Error(), "INVALID_TRACE_HEADERS")
	}
	
	return result
}

// SanitizeContext sanitizes the trace context
func (v *ValidationManager) SanitizeContext(ctx context.Context) context.Context {
	// Truncate baggage if it exceeds limits
	ctx = v.baggageManager.TruncateBaggage(ctx)
	
	// Remove invalid baggage items
	ctx = v.sanitizeBaggage(ctx)
	
	return ctx
}

// sanitizeBaggage sanitizes baggage data
func (v *ValidationManager) sanitizeBaggage(ctx context.Context) context.Context {
	baggageData := v.baggageManager.GetAllBaggage(ctx)
	
	// Clear all baggage first
	cleanCtx := v.baggageManager.ClearBaggage(ctx)
	
	// Add back valid baggage items
	for _, item := range baggageData.Items {
		// Validate key and value
		if err := v.baggageManager.ValidateBaggageKey(item.Key); err != nil {
			continue // Skip invalid key
		}
		if err := v.baggageManager.ValidateBaggageValue(item.Value); err != nil {
			continue // Skip invalid value
		}
		
		// Sanitize value
		sanitizedValue := v.sanitizeBaggageValue(item.Value)
		
		// Add sanitized baggage item
		cleanCtx, _ = v.baggageManager.SetBaggage(cleanCtx, item.Key, sanitizedValue)
	}
	
	return cleanCtx
}

// sanitizeBaggageValue sanitizes baggage value
func (v *ValidationManager) sanitizeBaggageValue(value string) string {
	// Remove control characters
	sanitized := strings.Map(func(r rune) rune {
		if r < 32 || r == 127 {
			return -1 // Remove control characters
		}
		return r
	}, value)
	
	// Trim whitespace
	sanitized = strings.TrimSpace(sanitized)
	
	// Limit length
	if len(sanitized) > 8192 {
		sanitized = sanitized[:8192]
	}
	
	return sanitized
}

// SanitizeHTTPHeaders sanitizes HTTP headers
func (v *ValidationManager) SanitizeHTTPHeaders(headers http.Header) http.Header {
	// Clean headers using header manager
	v.headerManager.CleanHeaders(headers)
	
	// Additional sanitization
	v.sanitizeTraceHeaders(headers)
	
	return headers
}

// sanitizeTraceHeaders sanitizes trace-specific headers
func (v *ValidationManager) sanitizeTraceHeaders(headers http.Header) {
	// Sanitize traceparent header
	if traceparent := headers.Get(TraceParentHeader); traceparent != "" {
		if sanitized := v.sanitizeTraceParent(traceparent); sanitized != traceparent {
			if sanitized == "" {
				headers.Del(TraceParentHeader)
			} else {
				headers.Set(TraceParentHeader, sanitized)
			}
		}
	}
	
	// Sanitize correlation ID
	if correlationID := headers.Get(CorrelationIDHeader); correlationID != "" {
		if sanitized := v.sanitizeCorrelationID(correlationID); sanitized != correlationID {
			if sanitized == "" {
				headers.Del(CorrelationIDHeader)
			} else {
				headers.Set(CorrelationIDHeader, sanitized)
			}
		}
	}
}

// sanitizeTraceParent sanitizes traceparent header
func (v *ValidationManager) sanitizeTraceParent(traceparent string) string {
	// W3C Trace Context format: version-trace_id-parent_id-trace_flags
	parts := strings.Split(traceparent, "-")
	if len(parts) != 4 {
		return "" // Invalid format
	}
	
	// Validate each part
	if !v.isValidHex(parts[0], 2) || // version
		!v.isValidHex(parts[1], 32) || // trace_id
		!v.isValidHex(parts[2], 16) || // parent_id
		!v.isValidHex(parts[3], 2) { // trace_flags
		return "" // Invalid format
	}
	
	return traceparent
}

// sanitizeCorrelationID sanitizes correlation ID
func (v *ValidationManager) sanitizeCorrelationID(correlationID string) string {
	// Remove invalid characters
	sanitized := regexp.MustCompile(`[^a-zA-Z0-9_-]`).ReplaceAllString(correlationID, "")
	
	// Limit length
	if len(sanitized) > 128 {
		sanitized = sanitized[:128]
	}
	
	// Minimum length check
	if len(sanitized) < 8 {
		return ""
	}
	
	return sanitized
}

// isValidHex validates hex string format
func (v *ValidationManager) isValidHex(value string, expectedLength int) bool {
	if len(value) != expectedLength {
		return false
	}
	
	for _, char := range value {
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f') || (char >= 'A' && char <= 'F')) {
			return false
		}
	}
	
	return true
}

// SecurityValidation performs security validation
func (v *ValidationManager) SecurityValidation(ctx context.Context, req *http.Request) *ValidationResult {
	result := &ValidationResult{Valid: true}
	
	// Check for suspicious patterns
	v.checkSuspiciousPatterns(ctx, req, result)
	
	// Check for injection attempts
	v.checkInjectionAttempts(ctx, req, result)
	
	// Check for size limits
	v.checkSizeLimits(ctx, req, result)
	
	return result
}

// checkSuspiciousPatterns checks for suspicious patterns in context
func (v *ValidationManager) checkSuspiciousPatterns(ctx context.Context, req *http.Request, result *ValidationResult) {
	// Check baggage for suspicious patterns
	baggageData := v.baggageManager.GetAllBaggage(ctx)
	for _, item := range baggageData.Items {
		if v.containsSuspiciousPattern(item.Value) {
			result.AddError("baggage_security", item.Key, "suspicious pattern detected in baggage", "SUSPICIOUS_PATTERN")
		}
	}
	
	// Check headers for suspicious patterns
	for name, values := range req.Header {
		for _, value := range values {
			if v.containsSuspiciousPattern(value) {
				result.AddError("header_security", name, "suspicious pattern detected in header", "SUSPICIOUS_PATTERN")
			}
		}
	}
}

// checkInjectionAttempts checks for injection attempts
func (v *ValidationManager) checkInjectionAttempts(ctx context.Context, req *http.Request, result *ValidationResult) {
	// Check for SQL injection patterns
	sqlInjectionPatterns := []string{
		"'", "\"", ";", "--", "/*", "*/", "xp_", "sp_", "union", "select", "insert", "update", "delete", "drop",
	}
	
	// Check baggage
	baggageData := v.baggageManager.GetAllBaggage(ctx)
	for _, item := range baggageData.Items {
		if v.containsInjectionPattern(item.Value, sqlInjectionPatterns) {
			result.AddError("baggage_injection", item.Key, "potential injection attempt detected", "INJECTION_ATTEMPT")
		}
	}
	
	// Check headers
	for name, values := range req.Header {
		for _, value := range values {
			if v.containsInjectionPattern(value, sqlInjectionPatterns) {
				result.AddError("header_injection", name, "potential injection attempt detected", "INJECTION_ATTEMPT")
			}
		}
	}
}

// checkSizeLimits checks for size limits
func (v *ValidationManager) checkSizeLimits(ctx context.Context, req *http.Request, result *ValidationResult) {
	// Check baggage size
	if !v.baggageManager.IsBaggageWithinLimits(ctx) {
		result.AddError("baggage_size", "", "baggage size exceeds security limits", "SIZE_LIMIT_EXCEEDED")
	}
	
	// Check header sizes
	for name, values := range req.Header {
		for _, value := range values {
			if len(value) > 8192 {
				result.AddError("header_size", name, "header value exceeds size limit", "SIZE_LIMIT_EXCEEDED")
			}
		}
	}
}

// containsSuspiciousPattern checks if value contains suspicious patterns
func (v *ValidationManager) containsSuspiciousPattern(value string) bool {
	lowerValue := strings.ToLower(value)
	
	suspiciousPatterns := []string{
		"<script", "</script>", "javascript:", "vbscript:", "onload=", "onerror=", "eval(",
		"alert(", "document.cookie", "document.write", "window.location", "base64",
	}
	
	for _, pattern := range suspiciousPatterns {
		if strings.Contains(lowerValue, pattern) {
			return true
		}
	}
	
	return false
}

// containsInjectionPattern checks if value contains injection patterns
func (v *ValidationManager) containsInjectionPattern(value string, patterns []string) bool {
	lowerValue := strings.ToLower(value)
	
	for _, pattern := range patterns {
		if strings.Contains(lowerValue, pattern) {
			return true
		}
	}
	
	return false
}

// RateLimitValidation validates rate limiting for trace context
func (v *ValidationManager) RateLimitValidation(ctx context.Context, clientIP string) *ValidationResult {
	result := &ValidationResult{Valid: true}
	
	// This would typically integrate with a rate limiter
	// For now, we'll just validate the context
	
	// Check if trace context is being abused
	if v.isTraceContextAbused(ctx) {
		result.AddError("trace_abuse", "", "trace context abuse detected", "TRACE_ABUSE")
	}
	
	return result
}

// isTraceContextAbused checks if trace context is being abused
func (v *ValidationManager) isTraceContextAbused(ctx context.Context) bool {
	// Check baggage size (potential abuse)
	baggageSize := v.baggageManager.GetBaggageSize(ctx)
	if baggageSize > 4096 { // 4KB threshold
		return true
	}
	
	// Check number of baggage items
	baggageData := v.baggageManager.GetAllBaggage(ctx)
	if len(baggageData.Items) > 50 { // 50 items threshold
		return true
	}
	
	return false
}

// ContextMetrics represents metrics for context validation
type ContextMetrics struct {
	ValidContexts    int64
	InvalidContexts  int64
	SanitizedContexts int64
	SecurityViolations int64
	LastValidation    time.Time
}

// GetContextMetrics returns validation metrics
func (v *ValidationManager) GetContextMetrics() *ContextMetrics {
	// This would typically be implemented with a metrics system
	return &ContextMetrics{
		LastValidation: time.Now(),
	}
}

// Global validation manager instance
var globalValidationManager *ValidationManager

// GetGlobalValidationManager returns the global validation manager instance
func GetGlobalValidationManager() *ValidationManager {
	if globalValidationManager == nil {
		globalValidationManager = NewValidationManager(
			GetGlobalPropagator(),
			GetGlobalHeaderManager(),
			GetGlobalBaggageManager(),
		)
	}
	return globalValidationManager
}

// SetGlobalValidationManager sets the global validation manager instance
func SetGlobalValidationManager(vm *ValidationManager) {
	globalValidationManager = vm
}