package tracing

import (
	"os"
	"testing"
	"time"
)

func TestLoadConfig(t *testing.T) {
	tests := []struct {
		name     string
		envVars  map[string]string
		expected *Config
	}{
		{
			name:    "default config",
			envVars: map[string]string{},
			expected: &Config{
				ServiceName:        "wn-api-v3",
				ServiceVersion:     "1.0.0",
				Environment:        "development",
				Enabled:            false,
				Exporter:           JaegerExporter,
				Sampling:           TraceIDRatio,
				SampleRate:         0.1,
				JaegerEndpoint:     "http://localhost:14268/api/traces",
				OTLPEndpoint:       "http://localhost:4318/v1/traces",
				OTLPInsecure:       true,
				BatchTimeout:       1000 * time.Millisecond,
				BatchSize:          100,
				MaxExportBatchSize: 512,
				MaxQueueSize:       2048,
				Propagators:        []string{"tracecontext", "baggage"},
				EnableGRPCTrace:    true,
				EnableHTTPTrace:    true,
				EnableDBTrace:      true,
				EnableCacheTrace:   true,
				ShutdownTimeout:    30 * time.Second,
			},
		},
		{
			name: "custom config",
			envVars: map[string]string{
				"TRACING_SERVICE_NAME":    "test-service",
				"TRACING_SERVICE_VERSION": "2.0.0",
				"TRACING_ENVIRONMENT":     "production",
				"TRACING_ENABLED":         "true",
				"TRACING_EXPORTER":        "otlp",
				"TRACING_SAMPLING":        "always",
				"TRACING_SAMPLE_RATE":     "1.0",
				"JAEGER_ENDPOINT":         "http://jaeger:14268/api/traces",
				"OTLP_ENDPOINT":           "http://otlp:4318/v1/traces",
				"TRACING_BATCH_SIZE":      "200",
				"TRACING_BATCH_TIMEOUT":   "2000",
			},
			expected: &Config{
				ServiceName:        "test-service",
				ServiceVersion:     "2.0.0",
				Environment:        "production",
				Enabled:            true,
				Exporter:           OTLPExporter,
				Sampling:           AlwaysSample,
				SampleRate:         1.0,
				JaegerEndpoint:     "http://jaeger:14268/api/traces",
				OTLPEndpoint:       "http://otlp:4318/v1/traces",
				OTLPInsecure:       true,
				BatchTimeout:       2000 * time.Millisecond,
				BatchSize:          200,
				MaxExportBatchSize: 512,
				MaxQueueSize:       2048,
				Propagators:        []string{"tracecontext", "baggage"},
				EnableGRPCTrace:    true,
				EnableHTTPTrace:    true,
				EnableDBTrace:      true,
				EnableCacheTrace:   true,
				ShutdownTimeout:    30 * time.Second,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables
			for key, value := range tt.envVars {
				os.Setenv(key, value)
			}

			// Load config
			cfg := LoadConfig()

			// Verify basic fields
			if cfg.ServiceName != tt.expected.ServiceName {
				t.Errorf("ServiceName = %v, want %v", cfg.ServiceName, tt.expected.ServiceName)
			}
			if cfg.ServiceVersion != tt.expected.ServiceVersion {
				t.Errorf("ServiceVersion = %v, want %v", cfg.ServiceVersion, tt.expected.ServiceVersion)
			}
			if cfg.Environment != tt.expected.Environment {
				t.Errorf("Environment = %v, want %v", cfg.Environment, tt.expected.Environment)
			}
			if cfg.Enabled != tt.expected.Enabled {
				t.Errorf("Enabled = %v, want %v", cfg.Enabled, tt.expected.Enabled)
			}
			if cfg.Exporter != tt.expected.Exporter {
				t.Errorf("Exporter = %v, want %v", cfg.Exporter, tt.expected.Exporter)
			}
			if cfg.Sampling != tt.expected.Sampling {
				t.Errorf("Sampling = %v, want %v", cfg.Sampling, tt.expected.Sampling)
			}
			if cfg.SampleRate != tt.expected.SampleRate {
				t.Errorf("SampleRate = %v, want %v", cfg.SampleRate, tt.expected.SampleRate)
			}
			if cfg.BatchSize != tt.expected.BatchSize {
				t.Errorf("BatchSize = %v, want %v", cfg.BatchSize, tt.expected.BatchSize)
			}
			if cfg.BatchTimeout != tt.expected.BatchTimeout {
				t.Errorf("BatchTimeout = %v, want %v", cfg.BatchTimeout, tt.expected.BatchTimeout)
			}

			// Clean up environment variables
			for key := range tt.envVars {
				os.Unsetenv(key)
			}
		})
	}
}

func TestConfigValidate(t *testing.T) {
	tests := []struct {
		name      string
		config    *Config
		wantError bool
		errorType error
	}{
		{
			name: "valid config",
			config: &Config{
				ServiceName:        "test-service",
				Enabled:            true,
				SampleRate:         0.5,
				BatchSize:          100,
				MaxExportBatchSize: 500,
				MaxQueueSize:       1000,
				BatchTimeout:       time.Second,
				ShutdownTimeout:    30 * time.Second,
			},
			wantError: false,
		},
		{
			name: "missing service name",
			config: &Config{
				ServiceName: "",
				Enabled:     true,
			},
			wantError: true,
			errorType: ErrMissingServiceName,
		},
		{
			name: "invalid sample rate - too low",
			config: &Config{
				ServiceName: "test-service",
				Enabled:     true,
				SampleRate:  -0.1,
			},
			wantError: true,
			errorType: ErrInvalidSampleRate,
		},
		{
			name: "invalid sample rate - too high",
			config: &Config{
				ServiceName: "test-service",
				Enabled:     true,
				SampleRate:  1.1,
			},
			wantError: true,
			errorType: ErrInvalidSampleRate,
		},
		{
			name: "invalid batch size",
			config: &Config{
				ServiceName: "test-service",
				Enabled:     true,
				SampleRate:  0.5,
				BatchSize:   0,
			},
			wantError: true,
			errorType: ErrInvalidBatchSize,
		},
		{
			name: "invalid max export batch size",
			config: &Config{
				ServiceName:        "test-service",
				Enabled:            true,
				SampleRate:         0.5,
				BatchSize:          100,
				MaxExportBatchSize: 0,
			},
			wantError: true,
			errorType: ErrInvalidMaxExportBatchSize,
		},
		{
			name: "invalid max queue size",
			config: &Config{
				ServiceName:        "test-service",
				Enabled:            true,
				SampleRate:         0.5,
				BatchSize:          100,
				MaxExportBatchSize: 500,
				MaxQueueSize:       0,
			},
			wantError: true,
			errorType: ErrInvalidMaxQueueSize,
		},
		{
			name: "invalid batch timeout",
			config: &Config{
				ServiceName:        "test-service",
				Enabled:            true,
				SampleRate:         0.5,
				BatchSize:          100,
				MaxExportBatchSize: 500,
				MaxQueueSize:       1000,
				BatchTimeout:       0,
			},
			wantError: true,
			errorType: ErrInvalidBatchTimeout,
		},
		{
			name: "invalid shutdown timeout",
			config: &Config{
				ServiceName:        "test-service",
				Enabled:            true,
				SampleRate:         0.5,
				BatchSize:          100,
				MaxExportBatchSize: 500,
				MaxQueueSize:       1000,
				BatchTimeout:       time.Second,
				ShutdownTimeout:    0,
			},
			wantError: true,
			errorType: ErrInvalidShutdownTimeout,
		},
		{
			name: "disabled config - should be valid",
			config: &Config{
				ServiceName: "test-service",
				Enabled:     false,
				SampleRate:  -1, // Invalid but should be ignored when disabled
			},
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			
			if tt.wantError {
				if err == nil {
					t.Error("Expected error, got nil")
				} else if tt.errorType != nil && err != tt.errorType {
					t.Errorf("Expected error %v, got %v", tt.errorType, err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
				}
			}
		})
	}
}

func TestDefaultConfig(t *testing.T) {
	cfg := DefaultConfig()
	
	if cfg.ServiceName != "wn-api-v3" {
		t.Errorf("ServiceName = %v, want %v", cfg.ServiceName, "wn-api-v3")
	}
	if cfg.ServiceVersion != "1.0.0" {
		t.Errorf("ServiceVersion = %v, want %v", cfg.ServiceVersion, "1.0.0")
	}
	if cfg.Environment != "development" {
		t.Errorf("Environment = %v, want %v", cfg.Environment, "development")
	}
	if cfg.Enabled != false {
		t.Errorf("Enabled = %v, want %v", cfg.Enabled, false)
	}
	if cfg.Exporter != JaegerExporter {
		t.Errorf("Exporter = %v, want %v", cfg.Exporter, JaegerExporter)
	}
	if cfg.Sampling != TraceIDRatio {
		t.Errorf("Sampling = %v, want %v", cfg.Sampling, TraceIDRatio)
	}
	if cfg.SampleRate != 0.1 {
		t.Errorf("SampleRate = %v, want %v", cfg.SampleRate, 0.1)
	}
	
	// Should be valid
	if err := cfg.Validate(); err != nil {
		t.Errorf("Default config should be valid, got error: %v", err)
	}
}

func TestDevelopmentConfig(t *testing.T) {
	cfg := DevelopmentConfig()
	
	if cfg.Enabled != true {
		t.Errorf("Enabled = %v, want %v", cfg.Enabled, true)
	}
	if cfg.Debug != true {
		t.Errorf("Debug = %v, want %v", cfg.Debug, true)
	}
	if cfg.Sampling != AlwaysSample {
		t.Errorf("Sampling = %v, want %v", cfg.Sampling, AlwaysSample)
	}
	if cfg.SampleRate != 1.0 {
		t.Errorf("SampleRate = %v, want %v", cfg.SampleRate, 1.0)
	}
	if cfg.Environment != "development" {
		t.Errorf("Environment = %v, want %v", cfg.Environment, "development")
	}
	
	// Should be valid
	if err := cfg.Validate(); err != nil {
		t.Errorf("Development config should be valid, got error: %v", err)
	}
}

func TestProductionConfig(t *testing.T) {
	cfg := ProductionConfig()
	
	if cfg.Enabled != true {
		t.Errorf("Enabled = %v, want %v", cfg.Enabled, true)
	}
	if cfg.Debug != false {
		t.Errorf("Debug = %v, want %v", cfg.Debug, false)
	}
	if cfg.Sampling != TraceIDRatio {
		t.Errorf("Sampling = %v, want %v", cfg.Sampling, TraceIDRatio)
	}
	if cfg.SampleRate != 0.1 {
		t.Errorf("SampleRate = %v, want %v", cfg.SampleRate, 0.1)
	}
	if cfg.Environment != "production" {
		t.Errorf("Environment = %v, want %v", cfg.Environment, "production")
	}
	
	// Should be valid
	if err := cfg.Validate(); err != nil {
		t.Errorf("Production config should be valid, got error: %v", err)
	}
}

func TestTestingConfig(t *testing.T) {
	cfg := TestingConfig()
	
	if cfg.Enabled != true {
		t.Errorf("Enabled = %v, want %v", cfg.Enabled, true)
	}
	if cfg.Debug != true {
		t.Errorf("Debug = %v, want %v", cfg.Debug, true)
	}
	if cfg.Exporter != ConsoleExporter {
		t.Errorf("Exporter = %v, want %v", cfg.Exporter, ConsoleExporter)
	}
	if cfg.Sampling != AlwaysSample {
		t.Errorf("Sampling = %v, want %v", cfg.Sampling, AlwaysSample)
	}
	if cfg.SampleRate != 1.0 {
		t.Errorf("SampleRate = %v, want %v", cfg.SampleRate, 1.0)
	}
	if cfg.Environment != "testing" {
		t.Errorf("Environment = %v, want %v", cfg.Environment, "testing")
	}
	
	// Should be valid
	if err := cfg.Validate(); err != nil {
		t.Errorf("Testing config should be valid, got error: %v", err)
	}
}

func TestConfigExporterChecks(t *testing.T) {
	tests := []struct {
		name     string
		exporter ExporterType
		checks   map[string]bool
	}{
		{
			name:     "jaeger exporter",
			exporter: JaegerExporter,
			checks: map[string]bool{
				"IsJaegerExporter":  true,
				"IsOTLPExporter":    false,
				"IsConsoleExporter": false,
				"IsNoOpExporter":    false,
			},
		},
		{
			name:     "otlp exporter",
			exporter: OTLPExporter,
			checks: map[string]bool{
				"IsJaegerExporter":  false,
				"IsOTLPExporter":    true,
				"IsConsoleExporter": false,
				"IsNoOpExporter":    false,
			},
		},
		{
			name:     "console exporter",
			exporter: ConsoleExporter,
			checks: map[string]bool{
				"IsJaegerExporter":  false,
				"IsOTLPExporter":    false,
				"IsConsoleExporter": true,
				"IsNoOpExporter":    false,
			},
		},
		{
			name:     "noop exporter",
			exporter: NoOpExporter,
			checks: map[string]bool{
				"IsJaegerExporter":  false,
				"IsOTLPExporter":    false,
				"IsConsoleExporter": false,
				"IsNoOpExporter":    true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := &Config{Exporter: tt.exporter}
			
			if cfg.IsJaegerExporter() != tt.checks["IsJaegerExporter"] {
				t.Errorf("IsJaegerExporter() = %v, want %v", cfg.IsJaegerExporter(), tt.checks["IsJaegerExporter"])
			}
			if cfg.IsOTLPExporter() != tt.checks["IsOTLPExporter"] {
				t.Errorf("IsOTLPExporter() = %v, want %v", cfg.IsOTLPExporter(), tt.checks["IsOTLPExporter"])
			}
			if cfg.IsConsoleExporter() != tt.checks["IsConsoleExporter"] {
				t.Errorf("IsConsoleExporter() = %v, want %v", cfg.IsConsoleExporter(), tt.checks["IsConsoleExporter"])
			}
			if cfg.IsNoOpExporter() != tt.checks["IsNoOpExporter"] {
				t.Errorf("IsNoOpExporter() = %v, want %v", cfg.IsNoOpExporter(), tt.checks["IsNoOpExporter"])
			}
		})
	}
}

func TestParseHeaders(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected map[string]string
	}{
		{
			name:     "empty string",
			input:    "",
			expected: map[string]string{},
		},
		{
			name:  "single header",
			input: "authorization=Bearer token",
			expected: map[string]string{
				"authorization": "Bearer token",
			},
		},
		{
			name:  "multiple headers",
			input: "authorization=Bearer token,content-type=application/json",
			expected: map[string]string{
				"authorization": "Bearer token",
				"content-type":  "application/json",
			},
		},
		{
			name:  "headers with spaces",
			input: " authorization = Bearer token , content-type = application/json ",
			expected: map[string]string{
				"authorization": "Bearer token",
				"content-type":  "application/json",
			},
		},
		{
			name:     "invalid format",
			input:    "invalid-header-without-equals",
			expected: map[string]string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseHeaders(tt.input)
			
			if len(result) != len(tt.expected) {
				t.Errorf("parseHeaders() returned %d headers, want %d", len(result), len(tt.expected))
			}
			
			for key, expectedValue := range tt.expected {
				if actualValue, exists := result[key]; !exists {
					t.Errorf("parseHeaders() missing key %s", key)
				} else if actualValue != expectedValue {
					t.Errorf("parseHeaders() key %s = %s, want %s", key, actualValue, expectedValue)
				}
			}
		})
	}
}

func TestParseSlice(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "empty string",
			input:    "",
			expected: []string{},
		},
		{
			name:     "single item",
			input:    "item1",
			expected: []string{"item1"},
		},
		{
			name:     "multiple items",
			input:    "item1,item2,item3",
			expected: []string{"item1", "item2", "item3"},
		},
		{
			name:     "items with spaces",
			input:    " item1 , item2 , item3 ",
			expected: []string{"item1", "item2", "item3"},
		},
		{
			name:     "empty items",
			input:    "item1,,item3",
			expected: []string{"item1", "item3"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseSlice(tt.input)
			
			if len(result) != len(tt.expected) {
				t.Errorf("parseSlice() returned %d items, want %d", len(result), len(tt.expected))
			}
			
			for i, expectedItem := range tt.expected {
				if i >= len(result) {
					t.Errorf("parseSlice() missing item at index %d", i)
				} else if result[i] != expectedItem {
					t.Errorf("parseSlice() item at index %d = %s, want %s", i, result[i], expectedItem)
				}
			}
		})
	}
}