package tracing

import (
	"context"
	"errors"
	"testing"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

func TestSpanBuilder(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test basic span builder
	builder := NewSpanBuilder(tracer, "test-span")
	if builder == nil {
		t.<PERSON><PERSON>("Expected span builder, got nil")
	}
	
	// Test with options
	startTime := time.Now()
	ctx := context.Background()
	
	builder = builder.
		WithContext(ctx).
		WithStartTime(startTime).
		WithSpanKind(trace.SpanKindServer).
		WithAttribute("key1", "value1").
		WithAttributes(map[string]interface{}{
			"key2": 42,
			"key3": true,
		}).
		WithStackTrace().
		WithComponentName("test-component").
		WithOperationName("test-operation")
	
	// Start the span
	newCtx, span := builder.Start()
	if span == nil {
		t.Error("Expected span, got nil")
	}
	if newCtx == nil {
		t.Error("Expected context, got nil")
	}
	
	span.End()
}

func TestSpanBuilderWithCallback(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test successful callback
	builder := NewSpanBuilder(tracer, "test-span")
	err = builder.StartWithCallback(func(ctx context.Context, span Span) error {
		span.SetAttribute("callback", "success")
		return nil
	})
	
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	
	// Test callback with error
	testErr := errors.New("test error")
	err = builder.StartWithCallback(func(ctx context.Context, span Span) error {
		span.SetAttribute("callback", "error")
		return testErr
	})
	
	if err != testErr {
		t.Errorf("Expected %v, got %v", testErr, err)
	}
}

func TestStartSpanVariants(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	ctx := context.Background()
	
	// Test StartSpan
	ctx, span := StartSpan(ctx, tracer, "test-span")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	span.End()
	
	// Test StartSpanWithKind
	ctx, span = StartSpanWithKind(ctx, tracer, "test-span-kind", trace.SpanKindServer)
	if span == nil {
		t.Error("Expected span, got nil")
	}
	span.End()
	
	// Test StartServerSpan
	ctx, span = StartServerSpan(ctx, tracer, "server-span")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	span.End()
	
	// Test StartClientSpan
	ctx, span = StartClientSpan(ctx, tracer, "client-span")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	span.End()
	
	// Test StartInternalSpan
	ctx, span = StartInternalSpan(ctx, tracer, "internal-span")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	span.End()
	
	// Test StartProducerSpan
	ctx, span = StartProducerSpan(ctx, tracer, "producer-span")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	span.End()
	
	// Test StartConsumerSpan
	ctx, span = StartConsumerSpan(ctx, tracer, "consumer-span")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	span.End()
}

func TestSpanHelperFunctions(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	ctx, span := tracer.StartSpan(context.Background(), "test-span")
	defer span.End()
	
	// Test SetSpanError
	testErr := errors.New("test error")
	SetSpanError(span, testErr)
	
	// Test SetSpanSuccess
	SetSpanSuccess(span)
	
	// Test SetSpanAttributes
	attrs := map[string]interface{}{
		"string-attr": "value",
		"int-attr":    42,
		"bool-attr":   true,
		"float-attr":  3.14,
	}
	SetSpanAttributes(span, attrs)
	
	// Test AddSpanEvent
	AddSpanEvent(span, "test-event", nil)
	AddSpanEvent(span, "test-event-with-attrs", map[string]interface{}{
		"event-key": "event-value",
	})
	
	// Test AddSpanTags (alias for SetSpanAttributes)
	tags := map[string]interface{}{
		"tag1": "value1",
		"tag2": "value2",
	}
	AddSpanTags(span, tags)
	
	// Test SetSpanOperationName
	SetSpanOperationName(span, "updated-operation")
	
	// Test SetSpanComponent
	SetSpanComponent(span, "test-component")
	
	// Test SetSpanLayer
	SetSpanLayer(span, "service")
}

func TestSpanHelperFunctionsWithNilSpan(t *testing.T) {
	// Test that all helper functions handle nil spans gracefully
	SetSpanError(nil, errors.New("test error"))
	SetSpanSuccess(nil)
	SetSpanAttributes(nil, map[string]interface{}{"key": "value"})
	AddSpanEvent(nil, "event", nil)
	AddSpanTags(nil, map[string]interface{}{"key": "value"})
	SetSpanOperationName(nil, "operation")
	SetSpanComponent(nil, "component")
	SetSpanLayer(nil, "layer")
	
	// No assertions needed - just ensure no panics occur
}

func TestHTTPSpanUtilities(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test StartHTTPSpan
	ctx, span := StartHTTPSpan(context.Background(), tracer, "GET", "https://example.com/api/users")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	defer span.End()
	
	// Test SetHTTPSpanAttributes
	SetHTTPSpanAttributes(span, "GET", "https://example.com/api/users", 200, "Mozilla/5.0")
	
	// Test with error status code
	SetHTTPSpanAttributes(span, "GET", "https://example.com/api/users", 404, "Mozilla/5.0")
}

func TestDatabaseSpanUtilities(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test StartDBSpan
	ctx, span := StartDBSpan(context.Background(), tracer, "SELECT", "users")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	defer span.End()
	
	// Test SetDBSpanAttributes
	SetDBSpanAttributes(span, "mysql", "mydb", "SELECT", "users", "SELECT * FROM users WHERE id = ?")
}

func TestCacheSpanUtilities(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test StartCacheSpan
	ctx, span := StartCacheSpan(context.Background(), tracer, "GET", "user:123")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	defer span.End()
	
	// Test SetCacheSpanAttributes
	SetCacheSpanAttributes(span, "GET", "user:123", true, time.Hour)
	SetCacheSpanAttributes(span, "GET", "user:456", false, 0)
}

func TestQueueSpanUtilities(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test StartQueueSpan
	ctx, span := StartQueueSpan(context.Background(), tracer, "PUBLISH", "user-events")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	defer span.End()
	
	// Test SetQueueSpanAttributes
	SetQueueSpanAttributes(span, "PUBLISH", "user-events", "msg-123", 1024)
}

func TestTraceFunction(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test TraceFunction
	ctx, span := TraceFunction(context.Background(), tracer, "myFunction")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	defer span.End()
	
	// Test TraceMethod
	ctx, span = TraceMethod(ctx, tracer, "MyStruct", "MyMethod")
	if span == nil {
		t.Error("Expected span, got nil")
	}
	defer span.End()
}

func TestWithSpanDecorator(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test successful decorator
	decorator := WithSpanDecorator(tracer, "test-operation", func(ctx context.Context, span Span) error {
		span.SetAttribute("decorator", "success")
		return nil
	})
	
	err = decorator(context.Background())
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	
	// Test decorator with error
	testErr := errors.New("test error")
	decorator = WithSpanDecorator(tracer, "test-operation", func(ctx context.Context, span Span) error {
		span.SetAttribute("decorator", "error")
		return testErr
	})
	
	err = decorator(context.Background())
	if err != testErr {
		t.Errorf("Expected %v, got %v", testErr, err)
	}
}

func TestWithSpanDecoratorWithResult(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test successful decorator
	decorator := WithSpanDecoratorWithResult(tracer, "test-operation", func(ctx context.Context, span Span) (string, error) {
		span.SetAttribute("decorator", "success")
		return "result", nil
	})
	
	result, err := decorator(context.Background())
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if result != "result" {
		t.Errorf("Expected 'result', got %v", result)
	}
	
	// Test decorator with error
	testErr := errors.New("test error")
	decorator = WithSpanDecoratorWithResult(tracer, "test-operation", func(ctx context.Context, span Span) (string, error) {
		span.SetAttribute("decorator", "error")
		return "", testErr
	})
	
	result, err = decorator(context.Background())
	if err != testErr {
		t.Errorf("Expected %v, got %v", testErr, err)
	}
	if result != "" {
		t.Errorf("Expected empty string, got %v", result)
	}
}

func TestMeasureTime(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	_, span := tracer.StartSpan(context.Background(), "test-span")
	defer span.End()
	
	// Test successful measurement
	err = MeasureTime(span, "operation", func() error {
		time.Sleep(10 * time.Millisecond)
		return nil
	})
	
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	
	// Test measurement with error
	testErr := errors.New("test error")
	err = MeasureTime(span, "operation", func() error {
		time.Sleep(10 * time.Millisecond)
		return testErr
	})
	
	if err != testErr {
		t.Errorf("Expected %v, got %v", testErr, err)
	}
}

func TestSetSpanUserInfo(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	_, span := tracer.StartSpan(context.Background(), "test-span")
	defer span.End()
	
	// Test SetSpanUserInfo
	SetSpanUserInfo(span, "123", "john.doe", "<EMAIL>")
	
	// Test with empty values
	SetSpanUserInfo(span, "", "", "")
	
	// Test with nil span
	SetSpanUserInfo(nil, "123", "john.doe", "<EMAIL>")
}

func TestSetSpanTenantInfo(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	_, span := tracer.StartSpan(context.Background(), "test-span")
	defer span.End()
	
	// Test SetSpanTenantInfo
	SetSpanTenantInfo(span, "tenant-123", "Acme Corp")
	
	// Test with empty values
	SetSpanTenantInfo(span, "", "")
	
	// Test with nil span
	SetSpanTenantInfo(nil, "tenant-123", "Acme Corp")
}

func TestSetSpanBusinessContext(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	_, span := tracer.StartSpan(context.Background(), "test-span")
	defer span.End()
	
	// Test SetSpanBusinessContext
	context := map[string]string{
		"workflow": "user-registration",
		"step":     "validation",
		"version":  "v2",
	}
	SetSpanBusinessContext(span, context)
	
	// Test with empty context
	SetSpanBusinessContext(span, map[string]string{})
	
	// Test with nil span
	SetSpanBusinessContext(nil, context)
	
	// Test with nil context
	SetSpanBusinessContext(span, nil)
}

func TestGetStackTrace(t *testing.T) {
	stackTrace := getStackTrace()
	if stackTrace == "" {
		t.Error("Expected non-empty stack trace")
	}
	
	// Stack trace should contain function names and line numbers
	if !contains(stackTrace, "TestGetStackTrace") {
		t.Error("Expected stack trace to contain test function name")
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr || 
		   (len(s) > len(substr) && contains(s[1:], substr))
}

func TestSpanBuilderChaining(t *testing.T) {
	tracer, err := NewTracer(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	err = tracer.Initialize(TestingConfig())
	if err != nil {
		t.Fatalf("Failed to initialize tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test that all methods return the builder for chaining
	builder := NewSpanBuilder(tracer, "test-span")
	
	// Chain all methods
	result := builder.
		WithContext(context.Background()).
		WithStartTime(time.Now()).
		WithSpanKind(trace.SpanKindServer).
		WithAttribute("key", "value").
		WithAttributes(map[string]interface{}{"attr": "value"}).
		WithStackTrace().
		WithComponentName("component").
		WithOperationName("operation")
	
	if result != builder {
		t.Error("Expected builder methods to return the same builder instance")
	}
}