package tracing

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/codes"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
)

// consoleExporter exports spans to the console
type consoleExporter struct {
	debug bool
}

// ExportSpans exports spans to the console
func (e *consoleExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	for _, span := range spans {
		e.printSpan(span)
	}
	return nil
}

// Shutdown shuts down the console exporter
func (e *consoleExporter) Shutdown(ctx context.Context) error {
	return nil
}

// printSpan prints a span to the console
func (e *consoleExporter) printSpan(span sdktrace.ReadOnlySpan) {
	if e.debug {
		fmt.Printf("[TRACE] Span: %s\n", span.Name())
		fmt.Printf("  TraceID: %s\n", span.SpanContext().TraceID().String())
		fmt.Printf("  SpanID: %s\n", span.SpanContext().SpanID().String())
		fmt.Printf("  ParentSpanID: %s\n", span.Parent().SpanID().String())
		fmt.Printf("  StartTime: %s\n", span.StartTime().Format(time.RFC3339))
		fmt.Printf("  EndTime: %s\n", span.EndTime().Format(time.RFC3339))
		fmt.Printf("  Duration: %v\n", span.EndTime().Sub(span.StartTime()))
		fmt.Printf("  Status: %s\n", span.Status().Description)
		fmt.Printf("  Kind: %s\n", span.SpanKind().String())
		
		// Print attributes
		if len(span.Attributes()) > 0 {
			fmt.Printf("  Attributes:\n")
			for _, attr := range span.Attributes() {
				fmt.Printf("    %s: %v\n", string(attr.Key), attr.Value.AsInterface())
			}
		}
		
		// Print events
		if len(span.Events()) > 0 {
			fmt.Printf("  Events:\n")
			for _, event := range span.Events() {
				fmt.Printf("    %s at %s\n", event.Name, event.Time.Format(time.RFC3339))
				if len(event.Attributes) > 0 {
					for _, attr := range event.Attributes {
						fmt.Printf("      %s: %v\n", string(attr.Key), attr.Value.AsInterface())
					}
				}
			}
		}
		
		// Print links
		if len(span.Links()) > 0 {
			fmt.Printf("  Links:\n")
			for _, link := range span.Links() {
				fmt.Printf("    TraceID: %s, SpanID: %s\n", 
					link.SpanContext.TraceID().String(), 
					link.SpanContext.SpanID().String())
			}
		}
		
		fmt.Printf("---\n")
	} else {
		fmt.Printf("[TRACE] %s - %s (Duration: %v)\n", 
			span.Name(), 
			span.SpanContext().TraceID().String(), 
			span.EndTime().Sub(span.StartTime()))
	}
}

// noopExporter is a no-operation exporter
type noopExporter struct{}

// ExportSpans does nothing
func (e *noopExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	return nil
}

// Shutdown does nothing
func (e *noopExporter) Shutdown(ctx context.Context) error {
	return nil
}

// fileExporter exports spans to a file
type fileExporter struct {
	filename string
	debug    bool
}

// NewFileExporter creates a new file exporter
func NewFileExporter(filename string, debug bool) *fileExporter {
	return &fileExporter{
		filename: filename,
		debug:    debug,
	}
}

// ExportSpans exports spans to a file
func (e *fileExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	// Implementation would write to file
	// For now, just print to console
	for _, span := range spans {
		e.writeSpan(span)
	}
	return nil
}

// Shutdown shuts down the file exporter
func (e *fileExporter) Shutdown(ctx context.Context) error {
	return nil
}

// writeSpan writes a span to the file
func (e *fileExporter) writeSpan(span sdktrace.ReadOnlySpan) {
	// For now, just print to console
	// In a real implementation, this would write to a file
	fmt.Printf("[FILE] Span: %s - %s\n", 
		span.Name(), 
		span.SpanContext().TraceID().String())
}

// multiExporter exports spans to multiple destinations
type multiExporter struct {
	exporters []sdktrace.SpanExporter
}

// NewMultiExporter creates a new multi-exporter
func NewMultiExporter(exporters ...sdktrace.SpanExporter) *multiExporter {
	return &multiExporter{
		exporters: exporters,
	}
}

// ExportSpans exports spans to all configured exporters
func (e *multiExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	for _, exporter := range e.exporters {
		if err := exporter.ExportSpans(ctx, spans); err != nil {
			// Log error but continue with other exporters
			fmt.Printf("[ERROR] Failed to export spans: %v\n", err)
		}
	}
	return nil
}

// Shutdown shuts down all exporters
func (e *multiExporter) Shutdown(ctx context.Context) error {
	for _, exporter := range e.exporters {
		if err := exporter.Shutdown(ctx); err != nil {
			// Log error but continue with other exporters
			fmt.Printf("[ERROR] Failed to shutdown exporter: %v\n", err)
		}
	}
	return nil
}

// bufferedExporter buffers spans and exports them in batches
type bufferedExporter struct {
	exporter   sdktrace.SpanExporter
	bufferSize int
	buffer     []sdktrace.ReadOnlySpan
}

// NewBufferedExporter creates a new buffered exporter
func NewBufferedExporter(exporter sdktrace.SpanExporter, bufferSize int) *bufferedExporter {
	return &bufferedExporter{
		exporter:   exporter,
		bufferSize: bufferSize,
		buffer:     make([]sdktrace.ReadOnlySpan, 0, bufferSize),
	}
}

// ExportSpans buffers spans and exports them in batches
func (e *bufferedExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	e.buffer = append(e.buffer, spans...)
	
	if len(e.buffer) >= e.bufferSize {
		return e.flush(ctx)
	}
	
	return nil
}

// Shutdown flushes any remaining spans and shuts down the exporter
func (e *bufferedExporter) Shutdown(ctx context.Context) error {
	if len(e.buffer) > 0 {
		if err := e.flush(ctx); err != nil {
			return err
		}
	}
	return e.exporter.Shutdown(ctx)
}

// flush exports all buffered spans
func (e *bufferedExporter) flush(ctx context.Context) error {
	if len(e.buffer) == 0 {
		return nil
	}
	
	err := e.exporter.ExportSpans(ctx, e.buffer)
	e.buffer = e.buffer[:0] // Clear buffer
	
	return err
}

// metricExporter exports spans and collects metrics
type metricExporter struct {
	exporter sdktrace.SpanExporter
	metrics  TracingMetrics
}

// NewMetricExporter creates a new metric exporter
func NewMetricExporter(exporter sdktrace.SpanExporter, metrics TracingMetrics) *metricExporter {
	return &metricExporter{
		exporter: exporter,
		metrics:  metrics,
	}
}

// ExportSpans exports spans and records metrics
func (e *metricExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	if e.metrics != nil {
		e.metrics.RecordExportedSpans(len(spans))
		
		for _, span := range spans {
			duration := span.EndTime().Sub(span.StartTime())
			e.metrics.RecordSpanDuration(span.Name(), duration)
			
			// Record error if span has error status
			if span.Status().Code == codes.Error {
				e.metrics.RecordSpanError(span.Name(), span.Status().Description)
			}
		}
	}
	
	return e.exporter.ExportSpans(ctx, spans)
}

// Shutdown shuts down the metric exporter
func (e *metricExporter) Shutdown(ctx context.Context) error {
	return e.exporter.Shutdown(ctx)
}

// retryExporter retries failed exports
type retryExporter struct {
	exporter   sdktrace.SpanExporter
	maxRetries int
	retryDelay time.Duration
}

// NewRetryExporter creates a new retry exporter
func NewRetryExporter(exporter sdktrace.SpanExporter, maxRetries int, retryDelay time.Duration) *retryExporter {
	return &retryExporter{
		exporter:   exporter,
		maxRetries: maxRetries,
		retryDelay: retryDelay,
	}
}

// ExportSpans exports spans with retry logic
func (e *retryExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	var lastErr error
	
	for i := 0; i <= e.maxRetries; i++ {
		err := e.exporter.ExportSpans(ctx, spans)
		if err == nil {
			return nil
		}
		
		lastErr = err
		
		if i < e.maxRetries {
			// Wait before retry
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(e.retryDelay):
				// Continue to next retry
			}
		}
	}
	
	return fmt.Errorf("failed to export spans after %d retries: %w", e.maxRetries, lastErr)
}

// Shutdown shuts down the retry exporter
func (e *retryExporter) Shutdown(ctx context.Context) error {
	return e.exporter.Shutdown(ctx)
}