package tracing

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	"go.opentelemetry.io/otel/trace"
)

// otelTracer implements the Tracer interface using OpenTelemetry
type otelTracer struct {
	config     *Config
	provider   *sdktrace.TracerProvider
	tracer     trace.Tracer
	propagator propagation.TextMapPropagator
	exporter   sdktrace.SpanExporter
	shutdown   context.CancelFunc
	mu         sync.RWMutex
	enabled    bool
}

// NewTracer creates a new OpenTelemetry tracer
func NewTracer(cfg *Config) (Tracer, error) {
	if cfg == nil {
		cfg = DefaultConfig()
	}
	
	if err := cfg.Validate(); err != nil {
		return nil, WrapError(err, "invalid configuration")
	}
	
	return &otelTracer{
		config:  cfg,
		enabled: cfg.Enabled,
	}, nil
}

// Initialize initializes the tracer with the given configuration
func (t *otelTracer) Initialize(cfg *Config) error {
	t.mu.Lock()
	defer t.mu.Unlock()
	
	if t.provider != nil {
		return ErrTracerAlreadyInitialized
	}
	
	if cfg != nil {
		t.config = cfg
		t.enabled = cfg.Enabled
	}
	
	if !t.enabled {
		// Set up no-op tracer
		t.tracer = otel.Tracer(t.config.ServiceName)
		return nil
	}
	
	// Create resource
	res, err := t.createResource()
	if err != nil {
		return WrapError(err, "failed to create resource")
	}
	
	// Create exporter
	exporter, err := t.createExporter()
	if err != nil {
		return WrapError(err, "failed to create exporter")
	}
	
	// Create sampler
	sampler := t.createSampler()
	
	// Create tracer provider
	tp := sdktrace.NewTracerProvider(
		sdktrace.WithResource(res),
		sdktrace.WithSpanProcessor(sdktrace.NewBatchSpanProcessor(
			exporter,
			sdktrace.WithBatchTimeout(t.config.BatchTimeout),
			sdktrace.WithMaxExportBatchSize(t.config.MaxExportBatchSize),
			sdktrace.WithMaxQueueSize(t.config.MaxQueueSize),
		)),
		sdktrace.WithSampler(sampler),
	)
	
	// Set global tracer provider
	otel.SetTracerProvider(tp)
	
	// Create propagator
	propagator := t.createPropagator()
	otel.SetTextMapPropagator(propagator)
	
	// Create context with cancel for shutdown
	ctx, cancel := context.WithCancel(context.Background())
	
	t.provider = tp
	t.exporter = exporter
	t.tracer = tp.Tracer(t.config.ServiceName)
	t.propagator = propagator
	t.shutdown = cancel
	
	// Start background monitoring if debug is enabled
	if t.config.Debug {
		go t.monitorTracer(ctx)
	}
	
	return nil
}

// Shutdown gracefully shuts down the tracer
func (t *otelTracer) Shutdown(ctx context.Context) error {
	t.mu.Lock()
	defer t.mu.Unlock()
	
	if t.provider == nil {
		return ErrTracerNotInitialized
	}
	
	if t.shutdown != nil {
		t.shutdown()
	}
	
	// Create shutdown context with timeout
	shutdownCtx, cancel := context.WithTimeout(ctx, t.config.ShutdownTimeout)
	defer cancel()
	
	// Shutdown tracer provider
	if err := t.provider.Shutdown(shutdownCtx); err != nil {
		return WrapError(err, "failed to shutdown tracer provider")
	}
	
	t.provider = nil
	t.tracer = nil
	t.exporter = nil
	t.propagator = nil
	
	return nil
}

// StartSpan creates a new span with the given name
func (t *otelTracer) StartSpan(ctx context.Context, name string, opts ...SpanOption) (context.Context, Span) {
	if !t.enabled || t.tracer == nil {
		return ctx, &noopSpan{}
	}
	
	// Apply options
	options := &SpanOptions{}
	for _, opt := range opts {
		opt(options)
	}
	
	// Create OpenTelemetry span options
	spanOpts := []trace.SpanStartOption{
		trace.WithSpanKind(options.Kind),
		trace.WithAttributes(options.Attributes...),
	}
	
	if !options.StartTime.IsZero() {
		spanOpts = append(spanOpts, trace.WithTimestamp(options.StartTime))
	}
	
	if len(options.Links) > 0 {
		spanOpts = append(spanOpts, trace.WithLinks(options.Links...))
	}
	
	// Start span
	ctx, span := t.tracer.Start(ctx, name, spanOpts...)
	
	// Create wrapped span
	wrappedSpan := &otelSpan{
		span: span,
	}
	
	return ctx, wrappedSpan
}

// StartSpanFromContext creates a new span from the current context
func (t *otelTracer) StartSpanFromContext(ctx context.Context, name string, opts ...SpanOption) (context.Context, Span) {
	return t.StartSpan(ctx, name, opts...)
}

// GetSpanFromContext retrieves the current span from context
func (t *otelTracer) GetSpanFromContext(ctx context.Context) Span {
	if !t.enabled {
		return &noopSpan{}
	}
	
	span := trace.SpanFromContext(ctx)
	if span == nil || !span.IsRecording() {
		return &noopSpan{}
	}
	
	return &otelSpan{
		span: span,
	}
}

// InjectTraceContext injects trace context into headers
func (t *otelTracer) InjectTraceContext(ctx context.Context, carrier TraceCarrier) error {
	if !t.enabled || t.propagator == nil {
		return nil
	}
	
	t.propagator.Inject(ctx, carrier)
	return nil
}

// ExtractTraceContext extracts trace context from headers
func (t *otelTracer) ExtractTraceContext(ctx context.Context, carrier TraceCarrier) context.Context {
	if !t.enabled || t.propagator == nil {
		return ctx
	}
	
	return t.propagator.Extract(ctx, carrier)
}

// IsEnabled returns true if tracing is enabled
func (t *otelTracer) IsEnabled() bool {
	return t.enabled
}

// createResource creates an OpenTelemetry resource
func (t *otelTracer) createResource() (*resource.Resource, error) {
	attrs := []attribute.KeyValue{
		semconv.ServiceNameKey.String(t.config.ServiceName),
		semconv.ServiceVersionKey.String(t.config.ServiceVersion),
		semconv.DeploymentEnvironmentKey.String(t.config.Environment),
	}
	
	if t.config.ServiceInstance != "" {
		attrs = append(attrs, semconv.ServiceInstanceIDKey.String(t.config.ServiceInstance))
	}
	
	// Add custom resource attributes
	for key, value := range t.config.ResourceAttributes {
		attrs = append(attrs, attribute.String(key, value))
	}
	
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		attrs...,
	), nil
}

// createExporter creates an OpenTelemetry exporter
func (t *otelTracer) createExporter() (sdktrace.SpanExporter, error) {
	switch t.config.Exporter {
	case JaegerExporter:
		return t.createJaegerExporter()
	case OTLPExporter:
		return t.createOTLPExporter()
	case ConsoleExporter:
		return t.createConsoleExporter()
	case NoOpExporter:
		return t.createNoOpExporter()
	default:
		return nil, ErrUnsupportedExporter
	}
}

// createJaegerExporter creates a Jaeger exporter
func (t *otelTracer) createJaegerExporter() (sdktrace.SpanExporter, error) {
	opts := []jaeger.CollectorEndpointOption{
		jaeger.WithEndpoint(t.config.JaegerEndpoint),
	}
	
	if t.config.JaegerUser != "" && t.config.JaegerPassword != "" {
		opts = append(opts, 
			jaeger.WithUsername(t.config.JaegerUser),
			jaeger.WithPassword(t.config.JaegerPassword),
		)
	}
	
	return jaeger.New(jaeger.WithCollectorEndpoint(opts...))
}

// createOTLPExporter creates an OTLP exporter
func (t *otelTracer) createOTLPExporter() (sdktrace.SpanExporter, error) {
	opts := []otlptracehttp.Option{
		otlptracehttp.WithEndpoint(t.config.OTLPEndpoint),
	}
	
	if t.config.OTLPInsecure {
		opts = append(opts, otlptracehttp.WithInsecure())
	}
	
	if len(t.config.OTLPHeaders) > 0 {
		opts = append(opts, otlptracehttp.WithHeaders(t.config.OTLPHeaders))
	}
	
	client := otlptracehttp.NewClient(opts...)
	return otlptrace.New(context.Background(), client)
}

// createConsoleExporter creates a console exporter
func (t *otelTracer) createConsoleExporter() (sdktrace.SpanExporter, error) {
	// Return a custom console exporter
	return &consoleExporter{
		debug: t.config.Debug,
	}, nil
}

// createNoOpExporter creates a no-op exporter
func (t *otelTracer) createNoOpExporter() (sdktrace.SpanExporter, error) {
	return &noopExporter{}, nil
}

// createSampler creates a sampler based on configuration
func (t *otelTracer) createSampler() sdktrace.Sampler {
	switch t.config.Sampling {
	case AlwaysSample:
		return sdktrace.AlwaysSample()
	case NeverSample:
		return sdktrace.NeverSample()
	case TraceIDRatio:
		return sdktrace.TraceIDRatioBased(t.config.SampleRate)
	case RateBased:
		return sdktrace.TraceIDRatioBased(t.config.SampleRate)
	default:
		return sdktrace.TraceIDRatioBased(t.config.SampleRate)
	}
}

// createPropagator creates a propagator based on configuration
func (t *otelTracer) createPropagator() propagation.TextMapPropagator {
	var propagators []propagation.TextMapPropagator
	
	for _, p := range t.config.Propagators {
		switch p {
		case "tracecontext":
			propagators = append(propagators, propagation.TraceContext{})
		case "baggage":
			propagators = append(propagators, propagation.Baggage{})
		case "b3":
			// B3 propagator would be added here if available
			// propagators = append(propagators, b3.New())
		case "jaeger":
			// Jaeger propagator would be added here if available
			// propagators = append(propagators, jaeger.Jaeger{})
		}
	}
	
	if len(propagators) == 0 {
		// Default to TraceContext and Baggage
		propagators = []propagation.TextMapPropagator{
			propagation.TraceContext{},
			propagation.Baggage{},
		}
	}
	
	return propagation.NewCompositeTextMapPropagator(propagators...)
}

// monitorTracer monitors the tracer in debug mode
func (t *otelTracer) monitorTracer(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if t.config.Debug {
				fmt.Printf("[TRACING] Tracer is running - Service: %s, Environment: %s, Exporter: %s\n",
					t.config.ServiceName, t.config.Environment, t.config.Exporter)
			}
		}
	}
}



// Helper functions