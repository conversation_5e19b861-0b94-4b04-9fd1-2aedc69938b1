package tracing

import (
	"context"
	"fmt"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
	"go.opentelemetry.io/otel/trace"
)

// OTelTracer implements Tracer using OpenTelemetry
type OTelTracer struct {
	tracer   trace.Tracer
	provider *sdktrace.TracerProvider
	config   *Config
}

// otelSpan wraps an OpenTelemetry span to implement our Span interface
type otelSpan struct {
	span trace.Span
}

// NewOTelTracer creates a new OpenTelemetry-based tracer
func NewOTelTracer(config *Config) (*OTelTracer, error) {
	if !config.Enabled {
		// Return a no-op tracer if tracing is disabled
		return &OTelTracer{
			tracer: trace.NewNoopTracerProvider().Tracer(config.ServiceName),
			config: config,
		}, nil
	}

	// Create resource
	res, err := resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(config.ServiceName),
			semconv.ServiceVersion(config.ServiceVersion),
			semconv.DeploymentEnvironment(config.Environment),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create resource: %w", err)
	}

	// Add custom resource attributes
	for k, v := range config.ResourceAttributes {
		res, _ = resource.Merge(res, resource.NewWithAttributes(
			semconv.SchemaURL,
			attribute.String(k, v),
		))
	}

	// Create exporter
	exporter, err := createExporter(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create exporter: %w", err)
	}

	// Create span processor
	var spanProcessor sdktrace.SpanProcessor
	if config.Exporter == ConsoleExporter {
		spanProcessor = sdktrace.NewSimpleSpanProcessor(exporter)
	} else {
		opts := []sdktrace.BatchSpanProcessorOption{}
		if config.BatchTimeout > 0 {
			opts = append(opts, sdktrace.WithBatchTimeout(config.BatchTimeout))
		}
		if config.MaxExportBatchSize > 0 {
			opts = append(opts, sdktrace.WithMaxExportBatchSize(config.MaxExportBatchSize))
		}
		if config.MaxQueueSize > 0 {
			opts = append(opts, sdktrace.WithMaxQueueSize(config.MaxQueueSize))
		}
		spanProcessor = sdktrace.NewBatchSpanProcessor(exporter, opts...)
	}

	// Create tracer provider
	provider := sdktrace.NewTracerProvider(
		sdktrace.WithResource(res),
		sdktrace.WithSpanProcessor(spanProcessor),
		sdktrace.WithSampler(sdktrace.TraceIDRatioBased(config.SampleRate)),
	)

	// Set global tracer provider
	otel.SetTracerProvider(provider)

	// Set up propagators
	setupPropagators(config)

	return &OTelTracer{
		tracer:   provider.Tracer(config.ServiceName),
		provider: provider,
		config:   config,
	}, nil
}

func createExporter(config *Config) (sdktrace.SpanExporter, error) {
	switch config.Exporter {
	case JaegerExporter:
		return jaeger.New(
			jaeger.WithCollectorEndpoint(
				jaeger.WithEndpoint(config.Endpoint),
			),
		)

	case ConsoleExporter:
		return stdouttrace.New(stdouttrace.WithPrettyPrint())

	case OTLPExporter:
		return otlptracegrpc.New(
			context.Background(),
			otlptracegrpc.WithEndpoint(config.Endpoint),
			otlptracegrpc.WithInsecure(),
		)


	case NoOpExporter:
		return &noopExporter{}, nil

	default:
		return nil, fmt.Errorf("unsupported exporter type: %s", config.Exporter)
	}
}

func setupPropagators(config *Config) {
	propagators := []propagation.TextMapPropagator{}

	// Default to W3C Trace Context if no propagators specified
	if len(config.Propagators) == 0 {
		config.Propagators = []string{"tracecontext", "baggage"}
	}

	for _, p := range config.Propagators {
		switch p {
		case "tracecontext":
			propagators = append(propagators, propagation.TraceContext{})
		case "baggage":
			propagators = append(propagators, propagation.Baggage{})
		case "b3":
			// B3 propagator would need to be implemented or imported
			// For now, we'll skip it
		}
	}

	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(propagators...))
}

// Start creates a new span
func (t *OTelTracer) Start(ctx context.Context, spanName string, opts ...SpanOption) (context.Context, Span) {
	options := &SpanOptions{
		Kind: trace.SpanKindInternal,
	}
	for _, opt := range opts {
		opt(options)
	}

	spanOpts := []trace.SpanStartOption{
		trace.WithSpanKind(options.Kind),
	}

	if len(options.Attributes) > 0 {
		spanOpts = append(spanOpts, trace.WithAttributes(options.Attributes...))
	}

	if !options.StartTime.IsZero() {
		spanOpts = append(spanOpts, trace.WithTimestamp(options.StartTime))
	}

	if len(options.Links) > 0 {
		spanOpts = append(spanOpts, trace.WithLinks(options.Links...))
	}

	ctx, span := t.tracer.Start(ctx, spanName, spanOpts...)
	
	// Store span in context
	ctx = context.WithValue(ctx, ContextKeySpan, &otelSpan{span: span})
	ctx = context.WithValue(ctx, ContextKeyTracer, t)
	
	return ctx, &otelSpan{span: span}
}

// StartWithParent creates a new span with explicit parent
func (t *OTelTracer) StartWithParent(ctx context.Context, parent trace.SpanContext, spanName string, opts ...SpanOption) (context.Context, Span) {
	// Create a new context with the parent span context
	ctx = trace.ContextWithSpanContext(ctx, parent)
	return t.Start(ctx, spanName, opts...)
}

// Extract extracts span context from carrier
func (t *OTelTracer) Extract(ctx context.Context, carrier interface{}) (context.Context, error) {
	propagator := otel.GetTextMapPropagator()
	
	switch c := carrier.(type) {
	case propagation.TextMapCarrier:
		return propagator.Extract(ctx, c), nil
	default:
		return ctx, fmt.Errorf("unsupported carrier type: %T", carrier)
	}
}

// Inject injects span context into carrier
func (t *OTelTracer) Inject(ctx context.Context, carrier interface{}) error {
	propagator := otel.GetTextMapPropagator()
	
	switch c := carrier.(type) {
	case propagation.TextMapCarrier:
		propagator.Inject(ctx, c)
		return nil
	default:
		return fmt.Errorf("unsupported carrier type: %T", carrier)
	}
}

// Shutdown gracefully shuts down the tracer
func (t *OTelTracer) Shutdown(ctx context.Context) error {
	if t.provider != nil {
		return t.provider.Shutdown(ctx)
	}
	return nil
}

// otelSpan implementation

func (s *otelSpan) End() {
	s.span.End()
}

func (s *otelSpan) SetAttributes(kv ...attribute.KeyValue) {
	s.span.SetAttributes(kv...)
}

func (s *otelSpan) SetStatus(code codes.Code, description string) {
	s.span.SetStatus(code, description)
}

func (s *otelSpan) RecordError(err error, options ...trace.EventOption) {
	if err != nil {
		s.span.RecordError(err, options...)
	}
}

func (s *otelSpan) AddEvent(name string, options ...trace.EventOption) {
	s.span.AddEvent(name, options...)
}

func (s *otelSpan) AddEventWithAttributes(name string, attrs ...attribute.KeyValue) {
	s.span.AddEvent(name, trace.WithAttributes(attrs...))
}

func (s *otelSpan) SpanContext() trace.SpanContext {
	return s.span.SpanContext()
}

func (s *otelSpan) IsRecording() bool {
	return s.span.IsRecording()
}

func (s *otelSpan) SetName(name string) {
	s.span.SetName(name)
}

func (s *otelSpan) SetAttribute(key string, value interface{}) {
	s.span.SetAttributes(createAttribute(key, value))
}

func (s *otelSpan) SetError(err error) {
	if err != nil {
		s.span.RecordError(err)
		s.span.SetStatus(codes.Error, err.Error())
	}
}

func (s *otelSpan) TraceID() trace.TraceID {
	return s.span.SpanContext().TraceID()
}

func (s *otelSpan) SpanID() trace.SpanID {
	return s.span.SpanContext().SpanID()
}


// Helper function to create common attributes
func CommonAttributes(tenantID, userID uint, requestID string) []attribute.KeyValue {
	attrs := []attribute.KeyValue{}
	
	if tenantID > 0 {
		attrs = append(attrs, attribute.Int64(AttributeTenantID, int64(tenantID)))
	}
	if userID > 0 {
		attrs = append(attrs, attribute.Int64(AttributeUserID, int64(userID)))
	}
	if requestID != "" {
		attrs = append(attrs, attribute.String(AttributeRequestID, requestID))
	}
	
	return attrs
}

// createAttribute creates an OpenTelemetry attribute from a key-value pair
func createAttribute(key string, value interface{}) attribute.KeyValue {
	switch v := value.(type) {
	case string:
		return attribute.String(key, v)
	case int:
		return attribute.Int(key, v)
	case int64:
		return attribute.Int64(key, v)
	case float64:
		return attribute.Float64(key, v)
	case bool:
		return attribute.Bool(key, v)
	case []string:
		return attribute.StringSlice(key, v)
	case []int:
		return attribute.IntSlice(key, v)
	case []int64:
		return attribute.Int64Slice(key, v)
	case []float64:
		return attribute.Float64Slice(key, v)
	case []bool:
		return attribute.BoolSlice(key, v)
	default:
		return attribute.String(key, fmt.Sprintf("%v", v))
	}
}