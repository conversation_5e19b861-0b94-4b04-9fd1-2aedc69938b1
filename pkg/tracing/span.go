package tracing

import (
	"context"
	"fmt"
	"runtime"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// SpanBuilder provides a fluent interface for creating spans
type SpanBuilder struct {
	tracer    Tracer
	name      string
	options   []SpanOption
	ctx       context.Context
	startTime time.Time
	attrs     []attribute.KeyValue
}

// NewSpanBuilder creates a new span builder
func NewSpanBuilder(tracer Tracer, name string) *SpanBuilder {
	return &SpanBuilder{
		tracer:  tracer,
		name:    name,
		ctx:     context.Background(),
		options: make([]SpanOption, 0),
		attrs:   make([]attribute.KeyValue, 0),
	}
}

// WithContext sets the context for the span
func (sb *SpanBuilder) WithContext(ctx context.Context) *SpanBuilder {
	sb.ctx = ctx
	return sb
}

// WithStartTime sets the start time for the span
func (sb *SpanBuilder) WithStartTime(startTime time.Time) *SpanBuilder {
	sb.startTime = startTime
	return sb
}

// WithSpanKind sets the span kind
func (sb *SpanBuilder) WithSpanKind(kind trace.SpanKind) *SpanBuilder {
	sb.options = append(sb.options, WithSpanKind(kind))
	return sb
}

// WithAttribute adds an attribute to the span
func (sb *SpanBuilder) WithAttribute(key string, value interface{}) *SpanBuilder {
	sb.attrs = append(sb.attrs, createAttribute(key, value))
	return sb
}

// WithAttributes adds multiple attributes to the span
func (sb *SpanBuilder) WithAttributes(attrs map[string]interface{}) *SpanBuilder {
	for key, value := range attrs {
		sb.attrs = append(sb.attrs, createAttribute(key, value))
	}
	return sb
}

// WithStackTrace enables stack trace capture
func (sb *SpanBuilder) WithStackTrace() *SpanBuilder {
	sb.options = append(sb.options, WithStackTrace())
	return sb
}

// WithComponentName sets the component name
func (sb *SpanBuilder) WithComponentName(name string) *SpanBuilder {
	sb.options = append(sb.options, WithComponentName(name))
	return sb
}

// WithOperationName sets the operation name
func (sb *SpanBuilder) WithOperationName(name string) *SpanBuilder {
	sb.options = append(sb.options, WithOperationName(name))
	return sb
}

// Start creates and starts the span
func (sb *SpanBuilder) Start() (context.Context, Span) {
	if len(sb.attrs) > 0 {
		sb.options = append(sb.options, WithAttributes(sb.attrs...))
	}
	
	if !sb.startTime.IsZero() {
		sb.options = append(sb.options, WithStartTime(sb.startTime))
	}
	
	return sb.tracer.StartSpan(sb.ctx, sb.name, sb.options...)
}

// StartWithCallback creates and starts a span, executes the callback, and ends the span
func (sb *SpanBuilder) StartWithCallback(callback func(ctx context.Context, span Span) error) error {
	ctx, span := sb.Start()
	defer span.End()
	
	if err := callback(ctx, span); err != nil {
		span.SetError(err)
		return err
	}
	
	return nil
}

// Span utilities

// StartSpanWithTracer is a convenience function to start a new span with a specific tracer
func StartSpanWithTracer(ctx context.Context, tracer Tracer, name string, opts ...SpanOption) (context.Context, Span) {
	return tracer.StartSpan(ctx, name, opts...)
}

// StartSpanWithKind starts a span with a specific kind
func StartSpanWithKind(ctx context.Context, tracer Tracer, name string, kind trace.SpanKind, opts ...SpanOption) (context.Context, Span) {
	allOpts := make([]SpanOption, 0, len(opts)+1)
	allOpts = append(allOpts, WithSpanKind(kind))
	allOpts = append(allOpts, opts...)
	return StartSpanWithTracer(ctx, tracer, name, allOpts...)
}

// StartServerSpan starts a server span
func StartServerSpan(ctx context.Context, tracer Tracer, name string, opts ...SpanOption) (context.Context, Span) {
	return StartSpanWithKind(ctx, tracer, name, trace.SpanKindServer, opts...)
}

// StartClientSpan starts a client span
func StartClientSpan(ctx context.Context, tracer Tracer, name string, opts ...SpanOption) (context.Context, Span) {
	return StartSpanWithKind(ctx, tracer, name, trace.SpanKindClient, opts...)
}

// StartInternalSpan starts an internal span
func StartInternalSpan(ctx context.Context, tracer Tracer, name string, opts ...SpanOption) (context.Context, Span) {
	return StartSpanWithKind(ctx, tracer, name, trace.SpanKindInternal, opts...)
}

// StartProducerSpan starts a producer span
func StartProducerSpan(ctx context.Context, tracer Tracer, name string, opts ...SpanOption) (context.Context, Span) {
	return StartSpanWithKind(ctx, tracer, name, trace.SpanKindProducer, opts...)
}

// StartConsumerSpan starts a consumer span
func StartConsumerSpan(ctx context.Context, tracer Tracer, name string, opts ...SpanOption) (context.Context, Span) {
	return StartSpanWithKind(ctx, tracer, name, trace.SpanKindConsumer, opts...)
}

// Span helper functions

// SetSpanError sets an error on the span with detailed information
func SetSpanError(span Span, err error) {
	if span == nil || err == nil {
		return
	}
	
	span.SetStatus(codes.Error, err.Error())
	span.SetAttribute("error", true)
	span.SetAttribute("error.type", fmt.Sprintf("%T", err))
	span.SetAttribute("error.message", err.Error())
	
	// Add stack trace if available
	if stackTrace := getStackTrace(); stackTrace != "" {
		span.SetAttribute("error.stack", stackTrace)
	}
}

// SetSpanSuccess marks the span as successful
func SetSpanSuccess(span Span) {
	if span == nil {
		return
	}
	
	span.SetStatus(codes.Ok, "")
	span.SetAttribute("error", false)
}

// SetSpanAttributes sets multiple attributes on the span
func SetSpanAttributes(span Span, attrs map[string]interface{}) {
	if span == nil || len(attrs) == 0 {
		return
	}
	
	for key, value := range attrs {
		span.SetAttribute(key, value)
	}
}

// AddSpanEvent adds an event to the span
func AddSpanEvent(span Span, name string, attrs map[string]interface{}) {
	if span == nil {
		return
	}
	
	if len(attrs) == 0 {
		span.AddEvent(name)
		return
	}
	
	otelAttrs := make([]attribute.KeyValue, 0, len(attrs))
	for key, value := range attrs {
		otelAttrs = append(otelAttrs, createAttribute(key, value))
	}
	
	span.AddEventWithAttributes(name, otelAttrs...)
}

// AddSpanTags adds tags to the span (alias for attributes)
func AddSpanTags(span Span, tags map[string]interface{}) {
	SetSpanAttributes(span, tags)
}

// SetSpanOperationName sets the operation name on the span
func SetSpanOperationName(span Span, operationName string) {
	if span == nil {
		return
	}
	
	span.SetName(operationName)
	span.SetAttribute("operation.name", operationName)
}

// SetSpanComponent sets the component name on the span
func SetSpanComponent(span Span, component string) {
	if span == nil {
		return
	}
	
	span.SetAttribute("component", component)
}

// SetSpanLayer sets the layer (service layer) on the span
func SetSpanLayer(span Span, layer string) {
	if span == nil {
		return
	}
	
	span.SetAttribute("layer", layer)
}

// HTTP span utilities

// StartHTTPSpan starts an HTTP span
func StartHTTPSpan(ctx context.Context, tracer Tracer, method, url string, opts ...SpanOption) (context.Context, Span) {
	spanName := fmt.Sprintf("%s %s", method, url)
	
	allOpts := make([]SpanOption, 0, len(opts)+1)
	allOpts = append(allOpts, WithSpanKind(trace.SpanKindClient))
	allOpts = append(allOpts, WithAttributes(
		attribute.String("http.method", method),
		attribute.String("http.url", url),
		attribute.String("component", "http"),
	))
	allOpts = append(allOpts, opts...)
	
	return tracer.StartSpan(ctx, spanName, allOpts...)
}

// SetHTTPSpanAttributes sets HTTP-specific attributes on the span
func SetHTTPSpanAttributes(span Span, method, url string, statusCode int, userAgent string) {
	if span == nil {
		return
	}
	
	span.SetAttribute("http.method", method)
	span.SetAttribute("http.url", url)
	span.SetAttribute("http.status_code", statusCode)
	
	if userAgent != "" {
		span.SetAttribute("http.user_agent", userAgent)
	}
	
	// Set status based on HTTP status code
	if statusCode >= 400 {
		span.SetStatus(codes.Error, fmt.Sprintf("HTTP %d", statusCode))
	} else {
		span.SetStatus(codes.Ok, "")
	}
}

// Database span utilities

// StartDBSpan starts a database span
func StartDBSpan(ctx context.Context, tracer Tracer, operation, table string, opts ...SpanOption) (context.Context, Span) {
	spanName := fmt.Sprintf("db.%s %s", operation, table)
	
	allOpts := make([]SpanOption, 0, len(opts)+1)
	allOpts = append(allOpts, WithSpanKind(trace.SpanKindClient))
	allOpts = append(allOpts, WithAttributes(
		attribute.String("db.operation", operation),
		attribute.String("db.table", table),
		attribute.String("component", "database"),
	))
	allOpts = append(allOpts, opts...)
	
	return tracer.StartSpan(ctx, spanName, allOpts...)
}

// SetDBSpanAttributes sets database-specific attributes on the span
func SetDBSpanAttributes(span Span, dbType, dbName, operation, table, query string) {
	if span == nil {
		return
	}
	
	span.SetAttribute("db.type", dbType)
	span.SetAttribute("db.name", dbName)
	span.SetAttribute("db.operation", operation)
	span.SetAttribute("db.table", table)
	
	if query != "" {
		span.SetAttribute("db.statement", query)
	}
}

// Cache span utilities

// StartCacheSpan starts a cache span
func StartCacheSpan(ctx context.Context, tracer Tracer, operation, key string, opts ...SpanOption) (context.Context, Span) {
	spanName := fmt.Sprintf("cache.%s %s", operation, key)
	
	allOpts := make([]SpanOption, 0, len(opts)+1)
	allOpts = append(allOpts, WithSpanKind(trace.SpanKindClient))
	allOpts = append(allOpts, WithAttributes(
		attribute.String("cache.operation", operation),
		attribute.String("cache.key", key),
		attribute.String("component", "cache"),
	))
	allOpts = append(allOpts, opts...)
	
	return tracer.StartSpan(ctx, spanName, allOpts...)
}

// SetCacheSpanAttributes sets cache-specific attributes on the span
func SetCacheSpanAttributes(span Span, operation, key string, hit bool, ttl time.Duration) {
	if span == nil {
		return
	}
	
	span.SetAttribute("cache.operation", operation)
	span.SetAttribute("cache.key", key)
	span.SetAttribute("cache.hit", hit)
	
	if ttl > 0 {
		span.SetAttribute("cache.ttl", ttl.Seconds())
	}
}

// Queue span utilities

// StartQueueSpan starts a queue span
func StartQueueSpan(ctx context.Context, tracer Tracer, operation, queue string, opts ...SpanOption) (context.Context, Span) {
	spanName := fmt.Sprintf("queue.%s %s", operation, queue)
	
	allOpts := make([]SpanOption, 0, len(opts)+1)
	allOpts = append(allOpts, WithSpanKind(trace.SpanKindProducer))
	allOpts = append(allOpts, WithAttributes(
		attribute.String("queue.operation", operation),
		attribute.String("queue.name", queue),
		attribute.String("component", "queue"),
	))
	allOpts = append(allOpts, opts...)
	
	return tracer.StartSpan(ctx, spanName, allOpts...)
}

// SetQueueSpanAttributes sets queue-specific attributes on the span
func SetQueueSpanAttributes(span Span, operation, queue, messageID string, messageSize int) {
	if span == nil {
		return
	}
	
	span.SetAttribute("queue.operation", operation)
	span.SetAttribute("queue.name", queue)
	span.SetAttribute("queue.message_id", messageID)
	span.SetAttribute("queue.message_size", messageSize)
}

// Utility functions

// getStackTrace returns the current stack trace
func getStackTrace() string {
	const depth = 32
	var pcs [depth]uintptr
	n := runtime.Callers(3, pcs[:])
	frames := runtime.CallersFrames(pcs[:n])
	
	var sb strings.Builder
	for {
		frame, more := frames.Next()
		if !more {
			break
		}
		
		fmt.Fprintf(&sb, "%s:%d %s\n", frame.File, frame.Line, frame.Function)
	}
	
	return sb.String()
}

// TraceFunction traces a function execution
func TraceFunction(ctx context.Context, tracer Tracer, functionName string) (context.Context, Span) {
	return StartInternalSpan(ctx, tracer, functionName, WithAttributes(
		attribute.String("function.name", functionName),
		attribute.String("component", "function"),
	))
}

// TraceMethod traces a method execution
func TraceMethod(ctx context.Context, tracer Tracer, receiver, method string) (context.Context, Span) {
	spanName := fmt.Sprintf("%s.%s", receiver, method)
	return StartInternalSpan(ctx, tracer, spanName, WithAttributes(
		attribute.String("method.receiver", receiver),
		attribute.String("method.name", method),
		attribute.String("component", "method"),
	))
}

// WithSpanDecorator wraps a function with span creation and management
func WithSpanDecorator(tracer Tracer, spanName string, fn func(ctx context.Context, span Span) error) func(ctx context.Context) error {
	return func(ctx context.Context) error {
		ctx, span := tracer.StartSpan(ctx, spanName)
		defer span.End()
		
		if err := fn(ctx, span); err != nil {
			SetSpanError(span, err)
			return err
		}
		
		SetSpanSuccess(span)
		return nil
	}
}

// WithSpanDecoratorWithResult wraps a function with span creation and returns both result and error
func WithSpanDecoratorWithResult[T any](tracer Tracer, spanName string, fn func(ctx context.Context, span Span) (T, error)) func(ctx context.Context) (T, error) {
	return func(ctx context.Context) (T, error) {
		ctx, span := tracer.StartSpan(ctx, spanName)
		defer span.End()
		
		result, err := fn(ctx, span)
		if err != nil {
			SetSpanError(span, err)
			var zero T
			return zero, err
		}
		
		SetSpanSuccess(span)
		return result, nil
	}
}

// MeasureTime measures execution time and adds it as span attribute
func MeasureTime(span Span, name string, fn func() error) error {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		span.SetAttribute(name+".duration", duration.Nanoseconds())
		span.SetAttribute(name+".duration_ms", duration.Milliseconds())
	}()
	
	return fn()
}

// SetSpanUserInfo sets user information on the span
func SetSpanUserInfo(span Span, userID, username, email string) {
	if span == nil {
		return
	}
	
	if userID != "" {
		span.SetAttribute("user.id", userID)
	}
	if username != "" {
		span.SetAttribute("user.username", username)
	}
	if email != "" {
		span.SetAttribute("user.email", email)
	}
}

// SetSpanTenantInfo sets tenant information on the span
func SetSpanTenantInfo(span Span, tenantID, tenantName string) {
	if span == nil {
		return
	}
	
	if tenantID != "" {
		span.SetAttribute("tenant.id", tenantID)
	}
	if tenantName != "" {
		span.SetAttribute("tenant.name", tenantName)
	}
}

// SetSpanBusinessContext sets business context on the span
func SetSpanBusinessContext(span Span, context map[string]string) {
	if span == nil || len(context) == 0 {
		return
	}
	
	for key, value := range context {
		span.SetAttribute("business."+key, value)
	}
}