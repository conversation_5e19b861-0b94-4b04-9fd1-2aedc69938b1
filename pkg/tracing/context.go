package tracing

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"go.opentelemetry.io/otel/trace"
)

// ContextKey is a custom type for context keys to avoid collisions
type ContextK<PERSON> string

const (
	// TracingContextKey is the key for tracing context
	TracingContextKey ContextKey = "tracing"
	
	// TraceIDKey is the key for trace ID
	TraceIDKey ContextKey = "trace_id"
	
	// SpanIDKey is the key for span ID
	SpanIDKey ContextKey = "span_id"
	
	// ParentSpanIDKey is the key for parent span ID
	ParentSpanIDKey ContextKey = "parent_span_id"
	
	// TracingEnabledKey is the key for tracing enabled flag
	TracingEnabledKey ContextKey = "tracing_enabled"
	
	// ContextKeySpan is the key for storing span in context
	ContextKeySpan ContextKey = "otel_span"
	
	// ContextKeyTracer is the key for storing tracer in context  
	ContextKeyTracer ContextKey = "otel_tracer"
)

// Common HTTP headers for trace context
const (
	// TraceParentHeader is the W3C Trace Context header
	TraceParentHeader = "traceparent"
	
	// TraceStateHeader is the W3C Trace State header
	TraceStateHeader = "tracestate"
	
	// B3TraceIDHeader is the B3 Trace ID header
	B3TraceIDHeader = "X-B3-TraceId"
	
	// B3SpanIDHeader is the B3 Span ID header
	B3SpanIDHeader = "X-B3-SpanId"
	
	// B3ParentSpanIDHeader is the B3 Parent Span ID header
	B3ParentSpanIDHeader = "X-B3-ParentSpanId"
	
	// B3SampledHeader is the B3 Sampled header
	B3SampledHeader = "X-B3-Sampled"
	
	// B3FlagsHeader is the B3 Flags header
	B3FlagsHeader = "X-B3-Flags"
	
	// JaegerTraceIDHeader is the Jaeger Trace ID header
	JaegerTraceIDHeader = "uber-trace-id"
	
	// XTraceIDHeader is a custom trace ID header
	XTraceIDHeader = "X-Trace-Id"
	
	// XSpanIDHeader is a custom span ID header
	XSpanIDHeader = "X-Span-Id"
	
	// XRequestIDHeader is a custom request ID header
	XRequestIDHeader = "X-Request-Id"
)

// TracingContext holds tracing-related context information
type TracingContext struct {
	TraceID       string
	SpanID        string
	ParentSpanID  string
	Sampled       bool
	Flags         string
	TraceState    string
	RequestID     string
	ServiceName   string
	OperationName string
	Attributes    map[string]string
}

// NewTracingContext creates a new tracing context
func NewTracingContext() *TracingContext {
	return &TracingContext{
		Attributes: make(map[string]string),
	}
}

// WithTraceID sets the trace ID
func (tc *TracingContext) WithTraceID(traceID string) *TracingContext {
	tc.TraceID = traceID
	return tc
}

// WithSpanID sets the span ID
func (tc *TracingContext) WithSpanID(spanID string) *TracingContext {
	tc.SpanID = spanID
	return tc
}

// WithParentSpanID sets the parent span ID
func (tc *TracingContext) WithParentSpanID(parentSpanID string) *TracingContext {
	tc.ParentSpanID = parentSpanID
	return tc
}

// WithSampled sets the sampled flag
func (tc *TracingContext) WithSampled(sampled bool) *TracingContext {
	tc.Sampled = sampled
	return tc
}

// WithRequestID sets the request ID
func (tc *TracingContext) WithRequestID(requestID string) *TracingContext {
	tc.RequestID = requestID
	return tc
}

// WithServiceName sets the service name
func (tc *TracingContext) WithServiceName(serviceName string) *TracingContext {
	tc.ServiceName = serviceName
	return tc
}

// WithOperationName sets the operation name
func (tc *TracingContext) WithOperationName(operationName string) *TracingContext {
	tc.OperationName = operationName
	return tc
}

// WithAttribute adds an attribute
func (tc *TracingContext) WithAttribute(key, value string) *TracingContext {
	tc.Attributes[key] = value
	return tc
}

// WithAttributes adds multiple attributes
func (tc *TracingContext) WithAttributes(attributes map[string]string) *TracingContext {
	for k, v := range attributes {
		tc.Attributes[k] = v
	}
	return tc
}

// IsValid returns true if the tracing context is valid
func (tc *TracingContext) IsValid() bool {
	return tc.TraceID != "" && tc.SpanID != ""
}

// String returns a string representation of the tracing context
func (tc *TracingContext) String() string {
	if !tc.IsValid() {
		return "invalid-trace-context"
	}
	return fmt.Sprintf("%s-%s", tc.TraceID, tc.SpanID)
}

// ToTraceParent converts the tracing context to a W3C trace parent header
func (tc *TracingContext) ToTraceParent() string {
	if !tc.IsValid() {
		return ""
	}
	
	version := "00"
	flags := "01"
	if !tc.Sampled {
		flags = "00"
	}
	
	return fmt.Sprintf("%s-%s-%s-%s", version, tc.TraceID, tc.SpanID, flags)
}

// FromTraceParent parses a W3C trace parent header
func (tc *TracingContext) FromTraceParent(traceParent string) error {
	parts := strings.Split(traceParent, "-")
	if len(parts) != 4 {
		return fmt.Errorf("invalid trace parent format: %s", traceParent)
	}
	
	tc.TraceID = parts[1]
	tc.SpanID = parts[2]
	tc.Flags = parts[3]
	tc.Sampled = parts[3] == "01"
	
	return nil
}

// Context management functions

// WithTracingContext adds tracing context to the context
func WithTracingContext(ctx context.Context, tc *TracingContext) context.Context {
	return context.WithValue(ctx, TracingContextKey, tc)
}

// TracingContextFromContext retrieves tracing context from the context
func TracingContextFromContext(ctx context.Context) (*TracingContext, bool) {
	tc, ok := ctx.Value(TracingContextKey).(*TracingContext)
	return tc, ok
}

// WithTraceID adds trace ID to the context
func WithTraceID(ctx context.Context, traceID string) context.Context {
	return context.WithValue(ctx, TraceIDKey, traceID)
}

// TraceIDFromContext retrieves trace ID from the context
func TraceIDFromContext(ctx context.Context) (string, bool) {
	traceID, ok := ctx.Value(TraceIDKey).(string)
	return traceID, ok
}

// WithSpanID adds span ID to the context
func WithSpanID(ctx context.Context, spanID string) context.Context {
	return context.WithValue(ctx, SpanIDKey, spanID)
}

// SpanIDFromContext retrieves span ID from the context
func SpanIDFromContext(ctx context.Context) (string, bool) {
	spanID, ok := ctx.Value(SpanIDKey).(string)
	return spanID, ok
}

// WithTracingEnabled adds tracing enabled flag to the context
func WithTracingEnabled(ctx context.Context, enabled bool) context.Context {
	return context.WithValue(ctx, TracingEnabledKey, enabled)
}

// IsTracingEnabled checks if tracing is enabled in the context
func IsTracingEnabled(ctx context.Context) bool {
	enabled, ok := ctx.Value(TracingEnabledKey).(bool)
	return ok && enabled
}

// HTTP context management

// ExtractTracingContextFromHTTPHeaders extracts tracing context from HTTP headers
func ExtractTracingContextFromHTTPHeaders(headers http.Header) *TracingContext {
	tc := NewTracingContext()
	
	// Try W3C Trace Context first
	if traceParent := headers.Get(TraceParentHeader); traceParent != "" {
		if err := tc.FromTraceParent(traceParent); err == nil {
			if traceState := headers.Get(TraceStateHeader); traceState != "" {
				tc.TraceState = traceState
			}
			return tc
		}
	}
	
	// Try B3 headers
	if traceID := headers.Get(B3TraceIDHeader); traceID != "" {
		tc.TraceID = traceID
		if spanID := headers.Get(B3SpanIDHeader); spanID != "" {
			tc.SpanID = spanID
		}
		if parentSpanID := headers.Get(B3ParentSpanIDHeader); parentSpanID != "" {
			tc.ParentSpanID = parentSpanID
		}
		if sampled := headers.Get(B3SampledHeader); sampled == "1" {
			tc.Sampled = true
		}
		if flags := headers.Get(B3FlagsHeader); flags != "" {
			tc.Flags = flags
		}
		return tc
	}
	
	// Try Jaeger headers
	if uberTraceID := headers.Get(JaegerTraceIDHeader); uberTraceID != "" {
		// Parse uber-trace-id format: {trace-id}:{span-id}:{parent-span-id}:{flags}
		parts := strings.Split(uberTraceID, ":")
		if len(parts) >= 2 {
			tc.TraceID = parts[0]
			tc.SpanID = parts[1]
			if len(parts) >= 3 && parts[2] != "0" {
				tc.ParentSpanID = parts[2]
			}
			if len(parts) >= 4 {
				tc.Flags = parts[3]
				tc.Sampled = parts[3] == "1"
			}
		}
		return tc
	}
	
	// Try custom headers
	if traceID := headers.Get(XTraceIDHeader); traceID != "" {
		tc.TraceID = traceID
		if spanID := headers.Get(XSpanIDHeader); spanID != "" {
			tc.SpanID = spanID
		}
		return tc
	}
	
	// Try request ID as trace ID
	if requestID := headers.Get(XRequestIDHeader); requestID != "" {
		tc.RequestID = requestID
		tc.TraceID = requestID
		return tc
	}
	
	return tc
}

// InjectTracingContextToHTTPHeaders injects tracing context into HTTP headers
func InjectTracingContextToHTTPHeaders(tc *TracingContext, headers http.Header) {
	if tc == nil || !tc.IsValid() {
		return
	}
	
	// Set W3C Trace Context headers
	if traceParent := tc.ToTraceParent(); traceParent != "" {
		headers.Set(TraceParentHeader, traceParent)
	}
	
	if tc.TraceState != "" {
		headers.Set(TraceStateHeader, tc.TraceState)
	}
	
	// Set custom headers
	headers.Set(XTraceIDHeader, tc.TraceID)
	headers.Set(XSpanIDHeader, tc.SpanID)
	
	if tc.RequestID != "" {
		headers.Set(XRequestIDHeader, tc.RequestID)
	}
}

// Gin context management

// ExtractTracingContextFromGinContext extracts tracing context from Gin context
func ExtractTracingContextFromGinContext(c interface{}) *TracingContext {
	// This would be implemented if we had gin.Context imported
	// For now, just return empty context
	return NewTracingContext()
}

// InjectTracingContextToGinContext injects tracing context into Gin context
func InjectTracingContextToGinContext(c interface{}, tc *TracingContext) {
	// This would be implemented if we had gin.Context imported
	// For now, do nothing
}

// Utility functions

// GenerateTraceID generates a new trace ID
func GenerateTraceID() string {
	traceID := trace.TraceID{}
	// This would use a proper random generator
	// For now, return a placeholder
	return traceID.String()
}

// GenerateSpanID generates a new span ID
func GenerateSpanID() string {
	spanID := trace.SpanID{}
	// This would use a proper random generator
	// For now, return a placeholder
	return spanID.String()
}

// IsValidTraceID checks if a trace ID is valid
func IsValidTraceID(traceID string) bool {
	return len(traceID) == 32 && isHexString(traceID)
}

// IsValidSpanID checks if a span ID is valid
func IsValidSpanID(spanID string) bool {
	return len(spanID) == 16 && isHexString(spanID)
}

// isHexString checks if a string is a valid hex string
func isHexString(s string) bool {
	for _, c := range s {
		if !((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F')) {
			return false
		}
	}
	return true
}

// GetTraceContext gets the current trace context from an OpenTelemetry span
func GetTraceContext(span trace.Span) *TracingContext {
	if span == nil {
		return NewTracingContext()
	}
	
	spanContext := span.SpanContext()
	if !spanContext.IsValid() {
		return NewTracingContext()
	}
	
	tc := NewTracingContext()
	tc.TraceID = spanContext.TraceID().String()
	tc.SpanID = spanContext.SpanID().String()
	tc.Sampled = spanContext.IsSampled()
	tc.Flags = spanContext.TraceFlags().String()
	tc.TraceState = spanContext.TraceState().String()
	
	return tc
}

// SetTraceContext sets trace context on an OpenTelemetry span
func SetTraceContext(span trace.Span, tc *TracingContext) {
	if span == nil || tc == nil || !tc.IsValid() {
		return
	}
	
	// OpenTelemetry spans are immutable, so we can't modify the context
	// This would be used for setting attributes instead
	if span.IsRecording() {
		span.SetAttributes(
			createAttribute("trace.id", tc.TraceID),
			createAttribute("span.id", tc.SpanID),
			createAttribute("trace.sampled", tc.Sampled),
		)
		
		if tc.ParentSpanID != "" {
			span.SetAttributes(createAttribute("span.parent_id", tc.ParentSpanID))
		}
		
		if tc.RequestID != "" {
			span.SetAttributes(createAttribute("request.id", tc.RequestID))
		}
		
		if tc.ServiceName != "" {
			span.SetAttributes(createAttribute("service.name", tc.ServiceName))
		}
		
		if tc.OperationName != "" {
			span.SetAttributes(createAttribute("operation.name", tc.OperationName))
		}
		
		// Set custom attributes
		for key, value := range tc.Attributes {
			span.SetAttributes(createAttribute(key, value))
		}
	}
}

// ContextWithSpan creates a new context with the given span
func ContextWithSpan(ctx context.Context, span trace.Span) context.Context {
	return trace.ContextWithSpan(ctx, span)
}

// SpanFromContext retrieves a span from the context
func SpanFromContext(ctx context.Context) trace.Span {
	return trace.SpanFromContext(ctx)
}

// ActiveSpan returns the active span from the context
func ActiveSpan(ctx context.Context) trace.Span {
	return trace.SpanFromContext(ctx)
}

// IsActiveSpan checks if there's an active span in the context
func IsActiveSpan(ctx context.Context) bool {
	span := trace.SpanFromContext(ctx)
	return span != nil && span.IsRecording()
}