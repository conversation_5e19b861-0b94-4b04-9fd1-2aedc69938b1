package tracing

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// HTTPMiddleware provides HTTP middleware for automatic tracing
type HTTPMiddleware struct {
	tracer    Tracer
	config    *MiddlewareConfig
	skipPaths map[string]bool
}

// MiddlewareConfig holds configuration for the tracing middleware
type MiddlewareConfig struct {
	// Service name to use for spans
	ServiceName string
	
	// Skip paths that should not be traced
	SkipPaths []string
	
	// Include request/response bodies in traces
	IncludeRequestBody  bool
	IncludeResponseBody bool
	
	// Maximum body size to include in traces
	MaxBodySize int64
	
	// Include query parameters in traces
	IncludeQueryParams bool
	
	// Include headers in traces
	IncludeHeaders bool
	
	// Header names to include (empty means all)
	HeadersToInclude []string
	
	// Header names to exclude
	HeadersToExclude []string
	
	// Custom span name function
	SpanNameFunc func(r *http.Request) string
	
	// Custom attributes function
	AttributesFunc func(r *http.Request) []attribute.KeyValue
	
	// Skip function to determine if a request should be skipped
	SkipFunc func(r *http.Request) bool
}

// DefaultMiddlewareConfig returns default middleware configuration
func DefaultMiddlewareConfig() *MiddlewareConfig {
	return &MiddlewareConfig{
		ServiceName:         "http-server",
		SkipPaths:           []string{"/health", "/metrics", "/ping"},
		IncludeRequestBody:  false,
		IncludeResponseBody: false,
		MaxBodySize:         1024 * 1024, // 1MB
		IncludeQueryParams:  true,
		IncludeHeaders:      true,
		HeadersToInclude:    []string{},
		HeadersToExclude:    []string{"authorization", "cookie", "x-api-key"},
		SpanNameFunc:        defaultSpanNameFunc,
		AttributesFunc:      defaultAttributesFunc,
	}
}

// NewHTTPMiddleware creates a new HTTP middleware
func NewHTTPMiddleware(tracer Tracer, config *MiddlewareConfig) *HTTPMiddleware {
	if config == nil {
		config = DefaultMiddlewareConfig()
	}
	
	// Create skip paths map for fast lookup
	skipPaths := make(map[string]bool)
	for _, path := range config.SkipPaths {
		skipPaths[path] = true
	}
	
	return &HTTPMiddleware{
		tracer:    tracer,
		config:    config,
		skipPaths: skipPaths,
	}
}

// Handler returns an HTTP handler that wraps the given handler with tracing
func (m *HTTPMiddleware) Handler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip if tracing is disabled
		if !m.tracer.IsEnabled() {
			next.ServeHTTP(w, r)
			return
		}
		
		// Skip if path should be skipped
		if m.shouldSkipRequest(r) {
			next.ServeHTTP(w, r)
			return
		}
		
		// Extract trace context from headers
		ctx := m.tracer.ExtractTraceContext(r.Context(), NewHTTPCarrier(r.Header))
		
		// Create span
		spanName := m.config.SpanNameFunc(r)
		ctx, span := m.tracer.StartSpan(ctx, spanName, WithSpanKind(trace.SpanKindServer))
		defer span.End()
		
		// Set basic HTTP attributes
		m.setHTTPAttributes(span, r)
		
		// Set custom attributes
		if m.config.AttributesFunc != nil {
			customAttrs := m.config.AttributesFunc(r)
			span.SetAttributes(customAttrs...)
		}
		
		// Create response writer wrapper
		wrapped := &responseWriterWrapper{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
			bodySize:       0,
		}
		
		// Add span context to request context
		r = r.WithContext(ctx)
		
		// Add trace headers to response
		m.injectTraceHeaders(ctx, wrapped)
		
		// Record request start time
		startTime := time.Now()
		
		// Call next handler
		next.ServeHTTP(wrapped, r)
		
		// Record request completion
		duration := time.Since(startTime)
		m.recordRequestCompletion(span, r, wrapped, duration)
	})
}

// shouldSkipRequest determines if a request should be skipped
func (m *HTTPMiddleware) shouldSkipRequest(r *http.Request) bool {
	// Use custom skip function if provided
	if m.config.SkipFunc != nil {
		return m.config.SkipFunc(r)
	}
	
	// Check skip paths
	return m.skipPaths[r.URL.Path]
}

// setHTTPAttributes sets HTTP-specific attributes on the span
func (m *HTTPMiddleware) setHTTPAttributes(span Span, r *http.Request) {
	// Basic HTTP attributes
	span.SetAttribute("http.method", r.Method)
	span.SetAttribute("http.url", r.URL.String())
	span.SetAttribute("http.scheme", r.URL.Scheme)
	span.SetAttribute("http.host", r.Host)
	span.SetAttribute("http.target", r.URL.Path)
	span.SetAttribute("http.user_agent", r.UserAgent())
	span.SetAttribute("http.request_content_length", r.ContentLength)
	span.SetAttribute("http.flavor", r.Proto)
	
	// Network attributes
	span.SetAttribute("net.host.name", r.Host)
	span.SetAttribute("net.host.port", getPortFromHost(r.Host))
	span.SetAttribute("net.peer.ip", getClientIP(r))
	
	// Query parameters
	if m.config.IncludeQueryParams && len(r.URL.Query()) > 0 {
		for key, values := range r.URL.Query() {
			span.SetAttribute("http.query."+key, strings.Join(values, ","))
		}
	}
	
	// Headers
	if m.config.IncludeHeaders {
		m.setHeaderAttributes(span, r.Header)
	}
	
	// Request body
	if m.config.IncludeRequestBody && r.ContentLength > 0 && r.ContentLength <= m.config.MaxBodySize {
		// Note: Reading the body would require buffering, which is not implemented here
		// In a real implementation, you would need to read and restore the body
		span.SetAttribute("http.request.body_available", true)
	}
}

// setHeaderAttributes sets header attributes on the span
func (m *HTTPMiddleware) setHeaderAttributes(span Span, headers http.Header) {
	for name, values := range headers {
		// Skip excluded headers
		if m.isHeaderExcluded(name) {
			continue
		}
		
		// Include only specified headers if list is not empty
		if len(m.config.HeadersToInclude) > 0 && !m.isHeaderIncluded(name) {
			continue
		}
		
		// Set attribute
		headerKey := "http.header." + strings.ToLower(name)
		if len(values) == 1 {
			span.SetAttribute(headerKey, values[0])
		} else {
			span.SetAttribute(headerKey, strings.Join(values, ","))
		}
	}
}

// isHeaderExcluded checks if a header should be excluded
func (m *HTTPMiddleware) isHeaderExcluded(name string) bool {
	lowerName := strings.ToLower(name)
	for _, excluded := range m.config.HeadersToExclude {
		if strings.ToLower(excluded) == lowerName {
			return true
		}
	}
	return false
}

// isHeaderIncluded checks if a header should be included
func (m *HTTPMiddleware) isHeaderIncluded(name string) bool {
	if len(m.config.HeadersToInclude) == 0 {
		return true
	}
	
	lowerName := strings.ToLower(name)
	for _, included := range m.config.HeadersToInclude {
		if strings.ToLower(included) == lowerName {
			return true
		}
	}
	return false
}

// injectTraceHeaders injects trace headers into the response
func (m *HTTPMiddleware) injectTraceHeaders(ctx context.Context, w http.ResponseWriter) {
	// Get span from context
	span := m.tracer.GetSpanFromContext(ctx)
	if span == nil || !span.IsRecording() {
		return
	}
	
	// Get trace context
	traceCtx := GetTraceContext(SpanFromContext(ctx))
	if traceCtx == nil || !traceCtx.IsValid() {
		return
	}
	
	// Inject trace headers
	headers := w.Header()
	InjectTracingContextToHTTPHeaders(traceCtx, headers)
}

// recordRequestCompletion records request completion attributes
func (m *HTTPMiddleware) recordRequestCompletion(span Span, r *http.Request, w *responseWriterWrapper, duration time.Duration) {
	// Response attributes
	span.SetAttribute("http.status_code", w.statusCode)
	span.SetAttribute("http.response_content_length", w.bodySize)
	span.SetAttribute("http.response_time", duration.Milliseconds())
	
	// Set span status based on HTTP status code
	if w.statusCode >= 400 {
		span.SetStatus(codes.Error, http.StatusText(w.statusCode))
		span.SetAttribute("error", true)
	} else {
		span.SetStatus(codes.Ok, "")
	}
	
	// Add timing event
	span.AddEvent("request_completed", trace.WithAttributes(
		attribute.Int("status_code", w.statusCode),
		attribute.Int64("duration_ms", duration.Milliseconds()),
	))
	
	// Response body
	if m.config.IncludeResponseBody && w.bodySize > 0 && w.bodySize <= m.config.MaxBodySize {
		// Note: Capturing response body would require buffering
		span.SetAttribute("http.response.body_available", true)
	}
}

// responseWriterWrapper wraps http.ResponseWriter to capture response details
type responseWriterWrapper struct {
	http.ResponseWriter
	statusCode int
	bodySize   int64
}

// WriteHeader captures the status code
func (w *responseWriterWrapper) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

// Write captures the response body size
func (w *responseWriterWrapper) Write(data []byte) (int, error) {
	n, err := w.ResponseWriter.Write(data)
	w.bodySize += int64(n)
	return n, err
}

// Default functions

// defaultSpanNameFunc returns the default span name
func defaultSpanNameFunc(r *http.Request) string {
	return fmt.Sprintf("%s %s", r.Method, r.URL.Path)
}

// defaultAttributesFunc returns default attributes
func defaultAttributesFunc(r *http.Request) []attribute.KeyValue {
	return []attribute.KeyValue{
		attribute.String("component", "http-server"),
		attribute.String("http.route", r.URL.Path),
	}
}

// Utility functions

// getPortFromHost extracts port from host
func getPortFromHost(host string) int {
	if strings.Contains(host, ":") {
		parts := strings.Split(host, ":")
		if len(parts) > 1 {
			if port, err := strconv.Atoi(parts[1]); err == nil {
				return port
			}
		}
	}
	return 80 // default HTTP port
}

// getClientIP extracts client IP from request
func getClientIP(r *http.Request) string {
	// Try X-Real-IP header first
	if ip := r.Header.Get("X-Real-IP"); ip != "" {
		return ip
	}
	
	// Try X-Forwarded-For header
	if ip := r.Header.Get("X-Forwarded-For"); ip != "" {
		// X-Forwarded-For can contain multiple IPs, take the first one
		if ips := strings.Split(ip, ","); len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}
	
	// Fall back to remote address
	if ip := r.RemoteAddr; ip != "" {
		// Remove port if present
		if strings.Contains(ip, ":") {
			if host, _, ok := strings.Cut(ip, ":"); ok {
				return host
			}
		}
		return ip
	}
	
	return ""
}

// Gin middleware (placeholder for Gin integration)
func GinMiddleware(tracer Tracer, config *MiddlewareConfig) interface{} {
	// This would return gin.HandlerFunc in a real implementation
	// For now, return a placeholder
	return func() {
		// Gin middleware implementation would go here
	}
}

// Echo middleware (placeholder for Echo integration)
func EchoMiddleware(tracer Tracer, config *MiddlewareConfig) interface{} {
	// This would return echo.MiddlewareFunc in a real implementation
	// For now, return a placeholder
	return func() {
		// Echo middleware implementation would go here
	}
}

// Fiber middleware (placeholder for Fiber integration)
func FiberMiddleware(tracer Tracer, config *MiddlewareConfig) interface{} {
	// This would return fiber.Handler in a real implementation
	// For now, return a placeholder
	return func() {
		// Fiber middleware implementation would go here
	}
}

// Database middleware

// DBMiddleware provides database middleware for automatic tracing
type DBMiddleware struct {
	tracer Tracer
	config *DBMiddlewareConfig
}

// DBMiddlewareConfig holds configuration for database tracing middleware
type DBMiddlewareConfig struct {
	ServiceName      string
	IncludeQuery     bool
	IncludeParams    bool
	MaxQueryLength   int
	SkipOperations   []string
	AttributesFunc   func(operation, table string) []attribute.KeyValue
}

// NewDBMiddleware creates a new database middleware
func NewDBMiddleware(tracer Tracer, config *DBMiddlewareConfig) *DBMiddleware {
	if config == nil {
		config = &DBMiddlewareConfig{
			ServiceName:    "database",
			IncludeQuery:   true,
			IncludeParams:  false,
			MaxQueryLength: 1000,
			SkipOperations: []string{},
		}
	}
	
	return &DBMiddleware{
		tracer: tracer,
		config: config,
	}
}

// TraceQuery traces a database query
func (m *DBMiddleware) TraceQuery(ctx context.Context, operation, table, query string, params []interface{}) (context.Context, Span) {
	if !m.tracer.IsEnabled() {
		return ctx, &noopSpan{}
	}
	
	spanName := fmt.Sprintf("db.%s %s", operation, table)
	ctx, span := m.tracer.StartSpan(ctx, spanName, WithSpanKind(trace.SpanKindClient))
	
	// Set database attributes
	span.SetAttribute("db.operation", operation)
	span.SetAttribute("db.table", table)
	span.SetAttribute("component", "database")
	
	// Include query if enabled
	if m.config.IncludeQuery && query != "" {
		if len(query) > m.config.MaxQueryLength {
			query = query[:m.config.MaxQueryLength] + "..."
		}
		span.SetAttribute("db.statement", query)
	}
	
	// Include params if enabled
	if m.config.IncludeParams && len(params) > 0 {
		span.SetAttribute("db.params_count", len(params))
		// Note: Be careful about logging sensitive data
	}
	
	// Set custom attributes
	if m.config.AttributesFunc != nil {
		customAttrs := m.config.AttributesFunc(operation, table)
		span.SetAttributes(customAttrs...)
	}
	
	return ctx, span
}

// Cache middleware

// CacheMiddleware provides cache middleware for automatic tracing
type CacheMiddleware struct {
	tracer Tracer
	config *CacheMiddlewareConfig
}

// CacheMiddlewareConfig holds configuration for cache tracing middleware
type CacheMiddlewareConfig struct {
	ServiceName    string
	IncludeKeys    bool
	IncludeValues  bool
	MaxValueLength int
	SkipOperations []string
	AttributesFunc func(operation, key string) []attribute.KeyValue
}

// NewCacheMiddleware creates a new cache middleware
func NewCacheMiddleware(tracer Tracer, config *CacheMiddlewareConfig) *CacheMiddleware {
	if config == nil {
		config = &CacheMiddlewareConfig{
			ServiceName:    "cache",
			IncludeKeys:    true,
			IncludeValues:  false,
			MaxValueLength: 100,
			SkipOperations: []string{},
		}
	}
	
	return &CacheMiddleware{
		tracer: tracer,
		config: config,
	}
}

// TraceOperation traces a cache operation
func (m *CacheMiddleware) TraceOperation(ctx context.Context, operation, key string) (context.Context, Span) {
	if !m.tracer.IsEnabled() {
		return ctx, &noopSpan{}
	}
	
	spanName := fmt.Sprintf("cache.%s %s", operation, key)
	ctx, span := m.tracer.StartSpan(ctx, spanName, WithSpanKind(trace.SpanKindClient))
	
	// Set cache attributes
	span.SetAttribute("cache.operation", operation)
	span.SetAttribute("component", "cache")
	
	// Include key if enabled
	if m.config.IncludeKeys && key != "" {
		span.SetAttribute("cache.key", key)
	}
	
	// Set custom attributes
	if m.config.AttributesFunc != nil {
		customAttrs := m.config.AttributesFunc(operation, key)
		span.SetAttributes(customAttrs...)
	}
	
	return ctx, span
}

// RecordCacheHit records a cache hit/miss
func (m *CacheMiddleware) RecordCacheHit(span Span, hit bool, ttl time.Duration) {
	if span == nil {
		return
	}
	
	span.SetAttribute("cache.hit", hit)
	if ttl > 0 {
		span.SetAttribute("cache.ttl", ttl.Seconds())
	}
	
	// Add event
	eventName := "cache_miss"
	if hit {
		eventName = "cache_hit"
	}
	span.AddEvent(eventName)
}