package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/tranthanhloi/wn-api-v3/pkg/tracing/http"
)

func TestHTTPTracingMiddleware(t *testing.T) {
	// Create config
	config := &http.Config{
		ServiceName:    "test-service",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		ExporterType:   "stdout",
		SampleRate:     1.0,
	}

	// Initialize tracing
	err := http.InitializeGlobalTracing(config)
	assert.NoError(t, err)

	// Create middleware
	middleware := NewHTTPTracingMiddleware(config)

	// Create test handler
	handler := middleware.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Add custom attribute
		AddAttributeToRequest(r, "test.attribute", "test_value")
		
		// Add event
		AddEventToRequest(r, "test.event", map[string]interface{}{
			"event.type": "test",
		})
		
		// Get trace ID
		traceID := GetTraceIDFromRequest(r)
		assert.NotEmpty(t, traceID)
		
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("X-Request-ID", "test-request-id")
	req.Header.Set("X-Tenant-ID", "test-tenant")

	// Create response recorder
	rr := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(rr, req)

	// Verify response
	assert.Equal(t, http.StatusOK, rr.Code)
	assert.Equal(t, "OK", rr.Body.String())
	assert.NotEmpty(t, rr.Header().Get("X-Request-ID"))
}

func TestGinTracingMiddleware(t *testing.T) {
	// Create config
	config := &http.Config{
		ServiceName:    "test-gin-service",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		ExporterType:   "stdout",
		SampleRate:     1.0,
	}

	// Initialize tracing
	err := http.InitializeGlobalTracing(config)
	assert.NoError(t, err)

	// Create Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add tracing middleware
	router.Use(GinTracingHandler(config))

	// Add test route
	router.GET("/test/:id", func(c *gin.Context) {
		// Add custom attribute
		AddAttributeToGinContext(c, "test.attribute", "gin_test_value")
		
		// Add event
		AddEventToGinContext(c, "test.event", map[string]interface{}{
			"event.type": "gin_test",
		})
		
		// Get trace ID
		traceID := GetTraceIDFromGinContext(c)
		assert.NotEmpty(t, traceID)
		
		c.JSON(http.StatusOK, gin.H{
			"message":  "OK",
			"trace_id": traceID,
			"param_id": c.Param("id"),
		})
	})

	// Create test request
	req := httptest.NewRequest("GET", "/test/123", nil)
	req.Header.Set("X-Request-ID", "gin-test-request-id")
	req.Header.Set("X-Tenant-ID", "gin-test-tenant")

	// Create response recorder
	rr := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(rr, req)

	// Verify response
	assert.Equal(t, http.StatusOK, rr.Code)
	assert.Contains(t, rr.Body.String(), "OK")
	assert.Contains(t, rr.Body.String(), "trace_id")
	assert.Contains(t, rr.Body.String(), "123")
}

func TestTracingMiddlewareSkipPaths(t *testing.T) {
	// Create config
	config := &http.Config{
		ServiceName:    "test-skip-service",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		ExporterType:   "stdout",
		SampleRate:     1.0,
	}

	// Initialize tracing
	err := http.InitializeGlobalTracing(config)
	assert.NoError(t, err)

	// Create middleware
	middleware := NewHTTPTracingMiddleware(config)

	// Create test handler
	handler := middleware.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// This should not have tracing for health check paths
		span := GetSpanFromRequest(r)
		if r.URL.Path == "/health" {
			assert.Nil(t, span)
		} else {
			assert.NotNil(t, span)
		}
		
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))

	// Test health check path (should be skipped)
	req := httptest.NewRequest("GET", "/health", nil)
	rr := httptest.NewRecorder()
	handler.ServeHTTP(rr, req)
	assert.Equal(t, http.StatusOK, rr.Code)

	// Test regular path (should have tracing)
	req = httptest.NewRequest("GET", "/api/test", nil)
	rr = httptest.NewRecorder()
	handler.ServeHTTP(rr, req)
	assert.Equal(t, http.StatusOK, rr.Code)
}

func TestMiddlewareIntegration(t *testing.T) {
	// This test verifies that tracing middleware works with existing middleware
	
	// Create config
	config := &http.Config{
		ServiceName:    "test-integration-service",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		ExporterType:   "stdout",
		SampleRate:     1.0,
	}

	// Initialize tracing
	err := http.InitializeGlobalTracing(config)
	assert.NoError(t, err)

	// Create Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add existing middleware
	router.Use(gin.Recovery())
	router.Use(gin.Logger())

	// Add tracing middleware
	router.Use(GinTracingHandler(config))

	// Add custom middleware that uses tracing
	router.Use(func(c *gin.Context) {
		// Add custom attributes from middleware
		AddAttributeToGinContext(c, "middleware.custom", "integration_test")
		c.Next()
		
		// Add post-processing attributes
		AddAttributeToGinContext(c, "response.status", c.Writer.Status())
	})

	// Add test route
	router.GET("/integration", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Integration test successful",
			"trace_id": GetTraceIDFromGinContext(c),
		})
	})

	// Create test request
	req := httptest.NewRequest("GET", "/integration", nil)
	rr := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(rr, req)

	// Verify response
	assert.Equal(t, http.StatusOK, rr.Code)
	assert.Contains(t, rr.Body.String(), "Integration test successful")
	assert.Contains(t, rr.Body.String(), "trace_id")
}