package middleware

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/pkg/tracing/http"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

// GinTracingMiddleware provides Gin-specific HTTP request tracing middleware
type GinTracingMiddleware struct {
	instrumentor *http.HTTPInstrumentor
	config       *http.Config
}

// NewGinTracingMiddleware creates a new Gin tracing middleware
func NewGinTracingMiddleware(config *http.Config) *GinTracingMiddleware {
	if config == nil {
		config = http.DefaultConfig()
	}
	
	return &GinTracingMiddleware{
		instrumentor: http.NewHTTPInstrumentor(config),
		config:       config,
	}
}

// Handler returns the Gin middleware handler
func (m *GinTracingMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip tracing for health check endpoints
		if m.shouldSkipTracing(c.Request.URL.Path) {
			c.Next()
			return
		}
		
		// Start timing
		startTime := time.Now()
		
		// Instrument request
		ctx, span := m.instrumentor.InstrumentRequest(c.Request)
		
		// Update request context
		c.Request = c.Request.WithContext(ctx)
		
		// Set span in Gin context for easy access
		c.Set("trace_span", span)
		c.Set("trace_context", ctx)
		
		// Add Gin-specific attributes
		if route := c.FullPath(); route != "" {
			m.instrumentor.AddSpanAttribute(span, "gin.route", route)
		}
		
		// Add handler name if available
		if handlerName := c.HandlerName(); handlerName != "" {
			m.instrumentor.AddSpanAttribute(span, "gin.handler", handlerName)
		}
		
		// Add route parameters
		for _, param := range c.Params {
			m.instrumentor.AddSpanAttribute(span, fmt.Sprintf("gin.param.%s", param.Key), param.Value)
		}
		
		// Defer span finishing
		defer func() {
			// Calculate duration
			duration := time.Since(startTime)
			
			// Add timing attributes
			m.instrumentor.AddSpanAttribute(span, "http.request.duration", duration.Nanoseconds())
			m.instrumentor.AddSpanAttribute(span, "http.request.duration_ms", float64(duration.Nanoseconds())/1e6)
			
			// Add response size if available
			responseSize := int64(c.Writer.Size())
			if responseSize > 0 {
				m.instrumentor.AddSpanAttribute(span, "http.response.size", responseSize)
			}
			
			// Instrument response
			m.instrumentor.InstrumentResponse(span, c.Writer.Status(), responseSize, c.Writer.Header())
			
			// Add errors if any
			if len(c.Errors) > 0 {
				m.instrumentor.AddSpanAttribute(span, "error", true)
				for i, err := range c.Errors {
					m.instrumentor.AddSpanAttribute(span, fmt.Sprintf("gin.error.%d", i), err.Error())
				}
			}
			
			// Finish span
			m.instrumentor.FinishSpan(span, time.Now())
		}()
		
		// Handle panics
		defer func() {
			if r := recover(); r != nil {
				// Record panic as error
				m.instrumentor.AddSpanAttribute(span, "error", true)
				m.instrumentor.AddSpanAttribute(span, "panic.message", r)
				m.instrumentor.AddSpanEvent(span, "panic")
				
				// Re-panic after recording
				panic(r)
			}
		}()
		
		// Call next handler
		c.Next()
	}
}

// shouldSkipTracing determines if tracing should be skipped for a given path
func (m *GinTracingMiddleware) shouldSkipTracing(path string) bool {
	skipPaths := []string{
		"/health",
		"/healthz", 
		"/ready",
		"/readiness",
		"/liveness",
		"/metrics",
		"/ping",
		"/favicon.ico",
	}
	
	for _, skipPath := range skipPaths {
		if path == skipPath {
			return true
		}
	}
	
	return false
}

// GetSpanFromGinContext extracts the span from the Gin context
func GetSpanFromGinContext(c *gin.Context) trace.Span {
	if span, exists := c.Get("trace_span"); exists {
		if s, ok := span.(trace.Span); ok {
			return s
		}
	}
	return nil
}

// GetTraceContextFromGinContext extracts the trace context from the Gin context
func GetTraceContextFromGinContext(c *gin.Context) context.Context {
	if ctx, exists := c.Get("trace_context"); exists {
		if context, ok := ctx.(context.Context); ok {
			return context
		}
	}
	return c.Request.Context()
}

// GetTraceIDFromGinContext extracts the trace ID from the Gin context
func GetTraceIDFromGinContext(c *gin.Context) string {
	if span := GetSpanFromGinContext(c); span != nil {
		return span.SpanContext().TraceID().String()
	}
	return ""
}

// GetSpanIDFromGinContext extracts the span ID from the Gin context
func GetSpanIDFromGinContext(c *gin.Context) string {
	if span := GetSpanFromGinContext(c); span != nil {
		return span.SpanContext().SpanID().String()
	}
	return ""
}

// AddAttributeToGinContext adds an attribute to the span associated with the Gin context
func AddAttributeToGinContext(c *gin.Context, key string, value interface{}) {
	if span := GetSpanFromGinContext(c); span != nil {
		instrumentor := http.NewHTTPInstrumentor(nil)
		instrumentor.AddSpanAttribute(span, key, value)
	}
}

// AddEventToGinContext adds an event to the span associated with the Gin context
func AddEventToGinContext(c *gin.Context, name string, attributes map[string]interface{}) {
	if span := GetSpanFromGinContext(c); span != nil {
		instrumentor := http.NewHTTPInstrumentor(nil)
		
		// Convert map to attributes
		attrs := make([]attribute.KeyValue, 0, len(attributes))
		for k, v := range attributes {
			switch val := v.(type) {
			case string:
				attrs = append(attrs, attribute.String(k, val))
			case int:
				attrs = append(attrs, attribute.Int(k, val))
			case int64:
				attrs = append(attrs, attribute.Int64(k, val))
			case float64:
				attrs = append(attrs, attribute.Float64(k, val))
			case bool:
				attrs = append(attrs, attribute.Bool(k, val))
			default:
				attrs = append(attrs, attribute.String(k, fmt.Sprintf("%v", val)))
			}
		}
		
		instrumentor.AddSpanEvent(span, name, attrs...)
	}
}

// RecordErrorInGinContext records an error in the span associated with the Gin context
func RecordErrorInGinContext(c *gin.Context, err error) {
	if span := GetSpanFromGinContext(c); span != nil {
		instrumentor := http.NewHTTPInstrumentor(nil)
		instrumentor.InstrumentError(span, err)
	}
}

// StartChildSpanFromGinContext starts a child span from the Gin context
func StartChildSpanFromGinContext(c *gin.Context, operationName string) (context.Context, trace.Span) {
	ctx := GetTraceContextFromGinContext(c)
	tracer := http.GetGlobalTracer()
	return tracer.Start(ctx, operationName)
}

// SetGinContextWithSpan sets a new span in the Gin context
func SetGinContextWithSpan(c *gin.Context, span trace.Span) {
	c.Set("trace_span", span)
	c.Set("trace_context", trace.ContextWithSpan(c.Request.Context(), span))
}

// Global middleware instance for easy access
var globalGinMiddleware *GinTracingMiddleware

// InitializeGlobalGinMiddleware initializes the global Gin middleware
func InitializeGlobalGinMiddleware(config *http.Config) {
	globalGinMiddleware = NewGinTracingMiddleware(config)
}

// GetGlobalGinMiddleware returns the global Gin middleware instance
func GetGlobalGinMiddleware() *GinTracingMiddleware {
	if globalGinMiddleware == nil {
		globalGinMiddleware = NewGinTracingMiddleware(nil)
	}
	return globalGinMiddleware
}

// GinTracingHandler is a convenience function that returns a Gin tracing handler
func GinTracingHandler(config *http.Config) gin.HandlerFunc {
	middleware := NewGinTracingMiddleware(config)
	return middleware.Handler()
}

// DefaultGinTracingHandler returns a Gin tracing handler with default configuration
func DefaultGinTracingHandler() gin.HandlerFunc {
	return GinTracingHandler(nil)
}