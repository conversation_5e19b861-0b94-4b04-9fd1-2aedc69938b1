package middleware

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/tracing/http"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

// HTTPTracingMiddleware provides HTTP request tracing middleware
type HTTPTracingMiddleware struct {
	instrumentor *http.HTTPInstrumentor
	config       *http.Config
}

// NewHTTPTracingMiddleware creates a new HTTP tracing middleware
func NewHTTPTracingMiddleware(config *http.Config) *HTTPTracingMiddleware {
	if config == nil {
		config = http.DefaultConfig()
	}
	
	return &HTTPTracingMiddleware{
		instrumentor: http.NewHTTPInstrumentor(config),
		config:       config,
	}
}

// <PERSON><PERSON> returns the HTTP middleware handler
func (m *HTTPTracingMiddleware) Handler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip tracing for health check endpoints
		if m.shouldSkipTracing(r.URL.Path) {
			next.ServeHTTP(w, r)
			return
		}
		
		// Start timing
		startTime := time.Now()
		
		// Create tracing response writer
		tracingWriter := http.NewTracingResponseWriter(w)
		
		// Instrument request
		ctx, span := m.instrumentor.InstrumentRequest(r)
		
		// Update request context
		r = r.WithContext(ctx)
		
		// Add span to context for easy access
		ctx = context.WithValue(ctx, "trace_span", span)
		r = r.WithContext(ctx)
		
		// Defer span finishing
		defer func() {
			// Calculate duration
			duration := time.Since(startTime)
			
			// Add timing attributes
			m.instrumentor.AddSpanAttribute(span, "http.request.duration", duration.Nanoseconds())
			m.instrumentor.AddSpanAttribute(span, "http.request.duration_ms", float64(duration.Nanoseconds())/1e6)
			
			// Instrument response
			m.instrumentor.InstrumentResponse(span, tracingWriter.StatusCode(), tracingWriter.ResponseSize(), tracingWriter.Headers())
			
			// Finish span
			m.instrumentor.FinishSpan(span, time.Now())
		}()
		
		// Handle panics
		defer func() {
			if r := recover(); r != nil {
				// Record panic as error
				m.instrumentor.AddSpanAttribute(span, "error", true)
				m.instrumentor.AddSpanAttribute(span, "panic.message", r)
				m.instrumentor.AddSpanEvent(span, "panic")
				
				// Re-panic after recording
				panic(r)
			}
		}()
		
		// Call next handler
		next.ServeHTTP(tracingWriter, r)
	})
}

// Middleware returns a middleware function compatible with standard middleware chains
func (m *HTTPTracingMiddleware) Middleware() func(http.Handler) http.Handler {
	return m.Handler
}

// shouldSkipTracing determines if tracing should be skipped for a given path
func (m *HTTPTracingMiddleware) shouldSkipTracing(path string) bool {
	skipPaths := []string{
		"/health",
		"/healthz", 
		"/ready",
		"/readiness",
		"/liveness",
		"/metrics",
		"/ping",
		"/favicon.ico",
	}
	
	for _, skipPath := range skipPaths {
		if path == skipPath {
			return true
		}
	}
	
	return false
}

// GetSpanFromRequest extracts the span from the request context
func GetSpanFromRequest(r *http.Request) trace.Span {
	if span := r.Context().Value("trace_span"); span != nil {
		if s, ok := span.(trace.Span); ok {
			return s
		}
	}
	return nil
}

// GetTraceIDFromRequest extracts the trace ID from the request context
func GetTraceIDFromRequest(r *http.Request) string {
	if span := GetSpanFromRequest(r); span != nil {
		return span.SpanContext().TraceID().String()
	}
	return ""
}

// AddAttributeToRequest adds an attribute to the span associated with the request
func AddAttributeToRequest(r *http.Request, key string, value interface{}) {
	if span := GetSpanFromRequest(r); span != nil {
		instrumentor := http.NewHTTPInstrumentor(nil)
		instrumentor.AddSpanAttribute(span, key, value)
	}
}

// AddEventToRequest adds an event to the span associated with the request
func AddEventToRequest(r *http.Request, name string, attributes map[string]interface{}) {
	if span := GetSpanFromRequest(r); span != nil {
		instrumentor := http.NewHTTPInstrumentor(nil)
		// Convert map to attributes
		attrs := make([]attribute.KeyValue, 0, len(attributes))
		for k, v := range attributes {
			switch val := v.(type) {
			case string:
				attrs = append(attrs, attribute.String(k, val))
			case int:
				attrs = append(attrs, attribute.Int(k, val))
			case int64:
				attrs = append(attrs, attribute.Int64(k, val))
			case float64:
				attrs = append(attrs, attribute.Float64(k, val))
			case bool:
				attrs = append(attrs, attribute.Bool(k, val))
			default:
				attrs = append(attrs, attribute.String(k, fmt.Sprintf("%v", val)))
			}
		}
		instrumentor.AddSpanEvent(span, name, attrs...)
	}
}

// Global middleware instance for easy access
var globalHTTPMiddleware *HTTPTracingMiddleware

// InitializeGlobalHTTPMiddleware initializes the global HTTP middleware
func InitializeGlobalHTTPMiddleware(config *http.Config) {
	globalHTTPMiddleware = NewHTTPTracingMiddleware(config)
}

// GetGlobalHTTPMiddleware returns the global HTTP middleware instance
func GetGlobalHTTPMiddleware() *HTTPTracingMiddleware {
	if globalHTTPMiddleware == nil {
		globalHTTPMiddleware = NewHTTPTracingMiddleware(nil)
	}
	return globalHTTPMiddleware
}

// HTTPTracingHandler is a convenience function that returns a tracing handler
func HTTPTracingHandler(config *http.Config) func(http.Handler) http.Handler {
	middleware := NewHTTPTracingMiddleware(config)
	return middleware.Handler
}

// DefaultHTTPTracingHandler returns a tracing handler with default configuration
func DefaultHTTPTracingHandler() func(http.Handler) http.Handler {
	return HTTPTracingHandler(nil)
}