package tracing

import "errors"

// Configuration errors
var (
	ErrMissingServiceName          = errors.New("tracing: service name is required")
	ErrInvalidSampleRate           = errors.New("tracing: sample rate must be between 0.0 and 1.0")
	ErrInvalidBatchSize            = errors.New("tracing: batch size must be greater than 0")
	ErrInvalidMaxExportBatchSize   = errors.New("tracing: max export batch size must be greater than 0")
	ErrInvalidMaxQueueSize         = errors.New("tracing: max queue size must be greater than 0")
	ErrInvalidBatchTimeout         = errors.New("tracing: batch timeout must be greater than 0")
	ErrInvalidShutdownTimeout      = errors.New("tracing: shutdown timeout must be greater than 0")
)

// Tracer errors
var (
	ErrTracerNotInitialized        = errors.New("tracing: tracer not initialized")
	ErrTracerAlreadyInitialized    = errors.New("tracing: tracer already initialized")
	ErrTracerShutdown              = errors.New("tracing: tracer is shut down")
	ErrUnsupportedExporter         = errors.New("tracing: unsupported exporter type")
	ErrUnsupportedSampling         = errors.New("tracing: unsupported sampling strategy")
)

// Span errors
var (
	ErrSpanNotRecording            = errors.New("tracing: span is not recording")
	ErrInvalidSpanContext          = errors.New("tracing: invalid span context")
	ErrSpanAlreadyEnded            = errors.New("tracing: span already ended")
)

// Export errors
var (
	ErrExportFailed                = errors.New("tracing: export failed")
	ErrExporterNotAvailable        = errors.New("tracing: exporter not available")
	ErrExportTimeout               = errors.New("tracing: export timeout")
	ErrExportBatchTooLarge         = errors.New("tracing: export batch too large")
)

// Context errors
var (
	ErrInvalidTraceContext         = errors.New("tracing: invalid trace context")
	ErrTraceContextNotFound        = errors.New("tracing: trace context not found")
	ErrTraceContextCorrupted       = errors.New("tracing: trace context corrupted")
)

// Propagation errors
var (
	ErrPropagationFailed           = errors.New("tracing: propagation failed")
	ErrUnsupportedPropagator       = errors.New("tracing: unsupported propagator")
	ErrCarrierNotSupported         = errors.New("tracing: carrier not supported")
)

// Metrics errors
var (
	ErrMetricsNotEnabled           = errors.New("tracing: metrics not enabled")
	ErrMetricsCollectionFailed     = errors.New("tracing: metrics collection failed")
)

// Utility functions for error handling

// IsConfigurationError returns true if the error is a configuration error
func IsConfigurationError(err error) bool {
	return errors.Is(err, ErrMissingServiceName) ||
		errors.Is(err, ErrInvalidSampleRate) ||
		errors.Is(err, ErrInvalidBatchSize) ||
		errors.Is(err, ErrInvalidMaxExportBatchSize) ||
		errors.Is(err, ErrInvalidMaxQueueSize) ||
		errors.Is(err, ErrInvalidBatchTimeout) ||
		errors.Is(err, ErrInvalidShutdownTimeout)
}

// IsTracerError returns true if the error is a tracer error
func IsTracerError(err error) bool {
	return errors.Is(err, ErrTracerNotInitialized) ||
		errors.Is(err, ErrTracerAlreadyInitialized) ||
		errors.Is(err, ErrTracerShutdown) ||
		errors.Is(err, ErrUnsupportedExporter) ||
		errors.Is(err, ErrUnsupportedSampling)
}

// IsSpanError returns true if the error is a span error
func IsSpanError(err error) bool {
	return errors.Is(err, ErrSpanNotRecording) ||
		errors.Is(err, ErrInvalidSpanContext) ||
		errors.Is(err, ErrSpanAlreadyEnded)
}

// IsExportError returns true if the error is an export error
func IsExportError(err error) bool {
	return errors.Is(err, ErrExportFailed) ||
		errors.Is(err, ErrExporterNotAvailable) ||
		errors.Is(err, ErrExportTimeout) ||
		errors.Is(err, ErrExportBatchTooLarge)
}

// IsContextError returns true if the error is a context error
func IsContextError(err error) bool {
	return errors.Is(err, ErrInvalidTraceContext) ||
		errors.Is(err, ErrTraceContextNotFound) ||
		errors.Is(err, ErrTraceContextCorrupted)
}

// IsPropagationError returns true if the error is a propagation error
func IsPropagationError(err error) bool {
	return errors.Is(err, ErrPropagationFailed) ||
		errors.Is(err, ErrUnsupportedPropagator) ||
		errors.Is(err, ErrCarrierNotSupported)
}

// IsMetricsError returns true if the error is a metrics error
func IsMetricsError(err error) bool {
	return errors.Is(err, ErrMetricsNotEnabled) ||
		errors.Is(err, ErrMetricsCollectionFailed)
}

// WrapError wraps an error with additional context
func WrapError(err error, message string) error {
	if err == nil {
		return nil
	}
	return &wrappedError{
		message: message,
		cause:   err,
	}
}

// wrappedError implements error wrapping
type wrappedError struct {
	message string
	cause   error
}

func (e *wrappedError) Error() string {
	return e.message + ": " + e.cause.Error()
}

func (e *wrappedError) Unwrap() error {
	return e.cause
}

func (e *wrappedError) Is(target error) bool {
	return errors.Is(e.cause, target)
}

func (e *wrappedError) As(target interface{}) bool {
	return errors.As(e.cause, target)
}