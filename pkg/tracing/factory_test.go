package tracing

import (
	"context"
	"testing"
	"time"
)

func TestNewFactory(t *testing.T) {
	factory := NewFactory()
	if factory == nil {
		t.<PERSON>r("Expected factory, got nil")
	}
}

func TestFactoryCreate(t *testing.T) {
	factory := NewFactory()
	
	// Test creating a tracer
	cfg := TestingConfig()
	tracer, err := factory.Create(cfg)
	if err != nil {
		t.<PERSON>rrorf("Failed to create tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	
	// Clean up
	tracer.Shutdown(context.Background())
}

func TestFactoryCreateWithName(t *testing.T) {
	factory := NewFactory()
	
	// Test creating a tracer with name
	cfg := TestingConfig()
	tracer, err := factory.CreateWithName("test-tracer", cfg)
	if err != nil {
		t.Errorf("Failed to create tracer: %v", err)
	}
	if tracer == nil {
		t.<PERSON>rror("Expected tracer, got nil")
	}
	
	// Test creating another tracer with the same name (should return existing)
	tracer2, err := factory.CreateWithName("test-tracer", cfg)
	if err != nil {
		t.Errorf("Failed to create tracer: %v", err)
	}
	if tracer2 != tracer {
		t.Error("Expected same tracer instance for duplicate name")
	}
	
	// Clean up
	tracer.Shutdown(context.Background())
}

func TestFactoryGet(t *testing.T) {
	factory := NewFactory()
	
	// Test getting non-existent tracer
	tracer, exists := factory.Get("non-existent")
	if exists {
		t.Error("Expected tracer not to exist")
	}
	if tracer != nil {
		t.Error("Expected nil tracer")
	}
	
	// Create a tracer
	cfg := TestingConfig()
	createdTracer, err := factory.CreateWithName("test-tracer", cfg)
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	defer createdTracer.Shutdown(context.Background())
	
	// Test getting existing tracer
	tracer, exists = factory.Get("test-tracer")
	if !exists {
		t.Error("Expected tracer to exist")
	}
	if tracer != createdTracer {
		t.Error("Expected same tracer instance")
	}
}

func TestFactoryGetOrCreate(t *testing.T) {
	factory := NewFactory()
	cfg := TestingConfig()
	
	// Test getting or creating non-existent tracer
	tracer1, err := factory.GetOrCreate("test-tracer", cfg)
	if err != nil {
		t.Errorf("Failed to get or create tracer: %v", err)
	}
	if tracer1 == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer1.Shutdown(context.Background())
	
	// Test getting existing tracer
	tracer2, err := factory.GetOrCreate("test-tracer", cfg)
	if err != nil {
		t.Errorf("Failed to get or create tracer: %v", err)
	}
	if tracer2 != tracer1 {
		t.Error("Expected same tracer instance")
	}
}

func TestFactoryShutdown(t *testing.T) {
	factory := NewFactory()
	cfg := TestingConfig()
	
	// Create multiple tracers
	tracer1, err := factory.CreateWithName("tracer1", cfg)
	if err != nil {
		t.Fatalf("Failed to create tracer1: %v", err)
	}
	
	tracer2, err := factory.CreateWithName("tracer2", cfg)
	if err != nil {
		t.Fatalf("Failed to create tracer2: %v", err)
	}
	
	// Shutdown all tracers
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	err = factory.Shutdown(ctx)
	if err != nil {
		t.Errorf("Failed to shutdown factory: %v", err)
	}
	
	// Check that tracers are removed
	names := factory.List()
	if len(names) != 0 {
		t.Errorf("Expected 0 tracers after shutdown, got %d", len(names))
	}
}

func TestFactoryList(t *testing.T) {
	factory := NewFactory()
	cfg := TestingConfig()
	
	// Test empty list
	names := factory.List()
	if len(names) != 0 {
		t.Errorf("Expected 0 tracers, got %d", len(names))
	}
	
	// Create tracers
	tracer1, err := factory.CreateWithName("tracer1", cfg)
	if err != nil {
		t.Fatalf("Failed to create tracer1: %v", err)
	}
	defer tracer1.Shutdown(context.Background())
	
	tracer2, err := factory.CreateWithName("tracer2", cfg)
	if err != nil {
		t.Fatalf("Failed to create tracer2: %v", err)
	}
	defer tracer2.Shutdown(context.Background())
	
	// Test list
	names = factory.List()
	if len(names) != 2 {
		t.Errorf("Expected 2 tracers, got %d", len(names))
	}
	
	// Check names
	nameMap := make(map[string]bool)
	for _, name := range names {
		nameMap[name] = true
	}
	
	if !nameMap["tracer1"] || !nameMap["tracer2"] {
		t.Errorf("Expected tracer1 and tracer2 in list, got %v", names)
	}
}

func TestFactoryRemove(t *testing.T) {
	factory := NewFactory()
	cfg := TestingConfig()
	
	// Create a tracer
	tracer, err := factory.CreateWithName("test-tracer", cfg)
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	
	// Remove the tracer
	err = factory.Remove("test-tracer")
	if err != nil {
		t.Errorf("Failed to remove tracer: %v", err)
	}
	
	// Check that tracer is removed
	_, exists := factory.Get("test-tracer")
	if exists {
		t.Error("Expected tracer to be removed")
	}
	
	// Test removing non-existent tracer
	err = factory.Remove("non-existent")
	if err != nil {
		t.Errorf("Expected no error when removing non-existent tracer, got %v", err)
	}
}

func TestGlobalFactory(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Test setting and getting global factory
	newFactory := NewFactory()
	SetGlobalFactory(newFactory)
	
	retrievedFactory := GetGlobalFactory()
	if retrievedFactory != newFactory {
		t.Error("Expected same factory instance")
	}
}

func TestGlobalConvenienceFunctions(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	cfg := TestingConfig()
	
	// Test CreateTracer
	tracer, err := CreateTracer(cfg)
	if err != nil {
		t.Errorf("Failed to create tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test CreateTracerWithName
	tracer2, err := CreateTracerWithName("test-tracer", cfg)
	if err != nil {
		t.Errorf("Failed to create tracer with name: %v", err)
	}
	if tracer2 == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer2.Shutdown(context.Background())
	
	// Test GetTracer
	retrievedTracer, exists := GetTracer("test-tracer")
	if !exists {
		t.Error("Expected tracer to exist")
	}
	if retrievedTracer != tracer2 {
		t.Error("Expected same tracer instance")
	}
	
	// Test GetOrCreateTracer
	tracer3, err := GetOrCreateTracer("test-tracer", cfg)
	if err != nil {
		t.Errorf("Failed to get or create tracer: %v", err)
	}
	if tracer3 != tracer2 {
		t.Error("Expected same tracer instance")
	}
	
	// Test ShutdownAllTracers
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	err = ShutdownAllTracers(ctx)
	if err != nil {
		t.Errorf("Failed to shutdown all tracers: %v", err)
	}
}

func TestPredefinedTracers(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Test NewDevelopmentTracer
	tracer, err := NewDevelopmentTracer("dev-service")
	if err != nil {
		t.Errorf("Failed to create development tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test NewProductionTracer
	tracer, err = NewProductionTracer("prod-service")
	if err != nil {
		t.Errorf("Failed to create production tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test NewTestingTracer
	tracer, err = NewTestingTracer("test-service")
	if err != nil {
		t.Errorf("Failed to create testing tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test NewJaegerTracer
	tracer, err = NewJaegerTracer("jaeger-service", "http://localhost:14268/api/traces")
	if err != nil {
		t.Errorf("Failed to create Jaeger tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test NewOTLPTracer
	tracer, err = NewOTLPTracer("otlp-service", "http://localhost:4318/v1/traces")
	if err != nil {
		t.Errorf("Failed to create OTLP tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test NewConsoleTracer
	tracer, err = NewConsoleTracer("console-service")
	if err != nil {
		t.Errorf("Failed to create console tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test NewNoOpTracer
	tracer, err = NewNoOpTracer("noop-service")
	if err != nil {
		t.Errorf("Failed to create no-op tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
}

func TestTracerBuilder(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Test builder pattern
	tracer, err := NewTracerBuilder("builder-service").
		WithServiceVersion("2.0.0").
		WithEnvironment("testing").
		WithEnabled(true).
		WithDebug(true).
		WithExporter(ConsoleExporter).
		WithSampling(AlwaysSample).
		WithSampleRate(1.0).
		WithJaegerEndpoint("http://localhost:14268/api/traces").
		WithOTLPEndpoint("http://localhost:4318/v1/traces").
		WithResourceAttributes(map[string]string{
			"custom.attr": "value",
		}).
		WithPropagators("tracecontext", "baggage").
		Build()
	
	if err != nil {
		t.Errorf("Failed to build tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test that tracer is enabled
	if !tracer.IsEnabled() {
		t.Error("Expected tracer to be enabled")
	}
}

func TestTracerBuilderDefaults(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Test builder with minimal configuration
	tracer, err := NewTracerBuilder("minimal-service").Build()
	if err != nil {
		t.Errorf("Failed to build tracer: %v", err)
	}
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
}

func TestMustCreateTracer(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Test successful creation
	cfg := TestingConfig()
	tracer := MustCreateTracer(cfg)
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test panic on invalid config
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected panic for invalid config")
		}
	}()
	
	invalidCfg := &Config{
		ServiceName: "",
		Enabled:     true,
	}
	MustCreateTracer(invalidCfg)
}

func TestMustCreateTracerWithName(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Test successful creation
	cfg := TestingConfig()
	tracer := MustCreateTracerWithName("must-test", cfg)
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
}

func TestMustGetTracer(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Create a tracer
	cfg := TestingConfig()
	tracer, err := CreateTracerWithName("must-get-test", cfg)
	if err != nil {
		t.Fatalf("Failed to create tracer: %v", err)
	}
	defer tracer.Shutdown(context.Background())
	
	// Test successful get
	retrievedTracer := MustGetTracer("must-get-test")
	if retrievedTracer != tracer {
		t.Error("Expected same tracer instance")
	}
	
	// Test panic on non-existent tracer
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected panic for non-existent tracer")
		}
	}()
	
	MustGetTracer("non-existent")
}

func TestDefaultTracer(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Test getting default tracer when none exists
	tracer := DefaultTracer()
	if tracer == nil {
		t.Error("Expected tracer, got nil")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test setting default tracer
	cfg := TestingConfig()
	customTracer, err := CreateTracerWithName("custom-default", cfg)
	if err != nil {
		t.Fatalf("Failed to create custom tracer: %v", err)
	}
	defer customTracer.Shutdown(context.Background())
	
	SetDefaultTracer(customTracer)
	
	retrievedTracer := DefaultTracer()
	if retrievedTracer != customTracer {
		t.Error("Expected same tracer instance")
	}
}

func TestInitialize(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Test initialization
	cfg := TestingConfig()
	err := Initialize(cfg)
	if err != nil {
		t.Errorf("Failed to initialize: %v", err)
	}
	
	// Test that default tracer is set
	tracer := DefaultTracer()
	if tracer == nil {
		t.Error("Expected default tracer to be set")
	}
	defer tracer.Shutdown(context.Background())
	
	// Test initialization with nil config
	err = Initialize(nil)
	if err != nil {
		t.Errorf("Failed to initialize with nil config: %v", err)
	}
}

func TestInitializeFromEnv(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Test initialization from environment
	err := InitializeFromEnv()
	if err != nil {
		t.Errorf("Failed to initialize from env: %v", err)
	}
	
	// Test that default tracer is set
	tracer := DefaultTracer()
	if tracer == nil {
		t.Error("Expected default tracer to be set")
	}
	defer tracer.Shutdown(context.Background())
}

func TestShutdown(t *testing.T) {
	// Save original factory
	originalFactory := globalFactory
	defer func() {
		globalFactory = originalFactory
	}()
	
	// Set up new factory
	factory := NewFactory()
	SetGlobalFactory(factory)
	
	// Create some tracers
	cfg := TestingConfig()
	tracer1, err := CreateTracerWithName("shutdown-test-1", cfg)
	if err != nil {
		t.Fatalf("Failed to create tracer1: %v", err)
	}
	
	tracer2, err := CreateTracerWithName("shutdown-test-2", cfg)
	if err != nil {
		t.Fatalf("Failed to create tracer2: %v", err)
	}
	
	// Test shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	err = Shutdown(ctx)
	if err != nil {
		t.Errorf("Failed to shutdown: %v", err)
	}
}