package external

import (
	"context"
	"fmt"
	"math"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

type RetryConfig struct {
	MaxAttempts      int
	InitialBackoff   time.Duration
	MaxBackoff       time.Duration
	BackoffStrategy  BackoffStrategy
	RetryCondition   func(error) bool
	TracerName       string
	Attributes       []attribute.KeyValue
	TimeoutPerRetry  time.Duration
	JitterEnabled    bool
	JitterMaxPercent float64
}

type BackoffStrategy string

const (
	BackoffStrategyFixed       BackoffStrategy = "fixed"
	BackoffStrategyExponential BackoffStrategy = "exponential"
	BackoffStrategyLinear      BackoffStrategy = "linear"
)

type RetryHandler struct {
	tracer trace.Tracer
	config RetryConfig
}

type RetryResult struct {
	Result       interface{}
	Attempts     int
	TotalTime    time.Duration
	LastError    error
	Success      bool
	Backoffs     []time.Duration
	AttemptTimes []time.Duration
}

func NewRetryHandler(cfg RetryConfig) *RetryHandler {
	if cfg.TracerName == "" {
		cfg.TracerName = "external-retry-handler"
	}

	if cfg.MaxAttempts == 0 {
		cfg.MaxAttempts = 3
	}

	if cfg.InitialBackoff == 0 {
		cfg.InitialBackoff = 100 * time.Millisecond
	}

	if cfg.MaxBackoff == 0 {
		cfg.MaxBackoff = 10 * time.Second
	}

	if cfg.BackoffStrategy == "" {
		cfg.BackoffStrategy = BackoffStrategyExponential
	}

	if cfg.RetryCondition == nil {
		cfg.RetryCondition = func(err error) bool {
			return err != nil
		}
	}

	if cfg.TimeoutPerRetry == 0 {
		cfg.TimeoutPerRetry = 30 * time.Second
	}

	if cfg.JitterMaxPercent == 0 {
		cfg.JitterMaxPercent = 0.1
	}

	return &RetryHandler{
		tracer: otel.Tracer(cfg.TracerName),
		config: cfg,
	}
}

func (r *RetryHandler) Execute(ctx context.Context, operation string, fn func(ctx context.Context) (interface{}, error)) (*RetryResult, error) {
	spanName := fmt.Sprintf("retry.%s", operation)
	ctx, span := r.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
		trace.WithAttributes(r.config.Attributes...),
	)
	defer span.End()

	result := &RetryResult{
		Backoffs:     make([]time.Duration, 0, r.config.MaxAttempts),
		AttemptTimes: make([]time.Duration, 0, r.config.MaxAttempts),
	}

	span.SetAttributes(
		attribute.String("retry.operation", operation),
		attribute.Int("retry.max_attempts", r.config.MaxAttempts),
		attribute.String("retry.backoff_strategy", string(r.config.BackoffStrategy)),
	)

	totalStart := time.Now()

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		attemptCtx := ctx
		if r.config.TimeoutPerRetry > 0 {
			var cancel context.CancelFunc
			attemptCtx, cancel = context.WithTimeout(ctx, r.config.TimeoutPerRetry)
			defer cancel()
		}

		attemptStart := time.Now()
		attemptResult, err := r.executeAttempt(attemptCtx, operation, attempt, fn)
		attemptDuration := time.Since(attemptStart)

		result.AttemptTimes = append(result.AttemptTimes, attemptDuration)

		if err == nil {
			result.Result = attemptResult
			result.Success = true
			result.Attempts = attempt
			result.TotalTime = time.Since(totalStart)
			span.SetStatus(codes.Ok, fmt.Sprintf("Operation succeeded on attempt %d", attempt))
			span.SetAttributes(
				attribute.Int("retry.attempts", attempt),
				attribute.Bool("retry.success", true),
				attribute.Float64("retry.total_time_ms", float64(result.TotalTime.Nanoseconds())/1e6),
			)
			return result, nil
		}

		result.LastError = err

		if !r.config.RetryCondition(err) {
			span.SetStatus(codes.Error, "Operation failed with non-retryable error")
			span.SetAttributes(
				attribute.String("error.type", "non_retryable_error"),
				attribute.String("error.message", err.Error()),
			)
			break
		}

		if attempt == r.config.MaxAttempts {
			break
		}

		backoffDuration := r.calculateBackoff(attempt)
		result.Backoffs = append(result.Backoffs, backoffDuration)

		span.SetAttributes(
			attribute.Int("retry.current_attempt", attempt),
			attribute.Float64("retry.backoff_ms", float64(backoffDuration.Nanoseconds())/1e6),
		)

		select {
		case <-time.After(backoffDuration):
		case <-ctx.Done():
			result.TotalTime = time.Since(totalStart)
			span.SetStatus(codes.Error, "Operation cancelled")
			return result, ctx.Err()
		}
	}

	result.Attempts = r.config.MaxAttempts
	result.TotalTime = time.Since(totalStart)
	span.SetStatus(codes.Error, "Operation failed after all retry attempts")
	span.SetAttributes(
		attribute.Int("retry.attempts", result.Attempts),
		attribute.Bool("retry.success", false),
		attribute.Float64("retry.total_time_ms", float64(result.TotalTime.Nanoseconds())/1e6),
		attribute.String("retry.final_error", result.LastError.Error()),
	)

	return result, fmt.Errorf("operation failed after %d attempts: %w", r.config.MaxAttempts, result.LastError)
}

func (r *RetryHandler) executeAttempt(ctx context.Context, operation string, attempt int, fn func(ctx context.Context) (interface{}, error)) (interface{}, error) {
	spanName := fmt.Sprintf("retry.attempt.%s.%d", operation, attempt)
	ctx, span := r.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
		trace.WithAttributes(r.config.Attributes...),
	)
	defer span.End()

	span.SetAttributes(
		attribute.String("retry.operation", operation),
		attribute.Int("retry.attempt", attempt),
	)

	result, err := fn(ctx)

	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.SetAttributes(
			attribute.String("error.type", "attempt_error"),
			attribute.String("error.message", err.Error()),
		)
	} else {
		span.SetStatus(codes.Ok, "Attempt succeeded")
	}

	return result, err
}

func (r *RetryHandler) calculateBackoff(attempt int) time.Duration {
	var backoff time.Duration

	switch r.config.BackoffStrategy {
	case BackoffStrategyFixed:
		backoff = r.config.InitialBackoff
	case BackoffStrategyLinear:
		backoff = time.Duration(attempt) * r.config.InitialBackoff
	case BackoffStrategyExponential:
		backoff = time.Duration(math.Pow(2, float64(attempt-1))) * r.config.InitialBackoff
	default:
		backoff = r.config.InitialBackoff
	}

	if backoff > r.config.MaxBackoff {
		backoff = r.config.MaxBackoff
	}

	if r.config.JitterEnabled && r.config.JitterMaxPercent > 0 {
		jitter := time.Duration(float64(backoff) * r.config.JitterMaxPercent * (2*math.Sin(float64(time.Now().UnixNano()))) / 2)
		backoff += jitter
	}

	return backoff
}

func (r *RetryHandler) ExecuteWithTimeout(ctx context.Context, operation string, timeout time.Duration, fn func(ctx context.Context) (interface{}, error)) (*RetryResult, error) {
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	return r.Execute(timeoutCtx, operation, fn)
}

func (r *RetryHandler) ExecuteWithCircuitBreaker(ctx context.Context, operation string, circuitBreaker CircuitBreaker, fn func(ctx context.Context) (interface{}, error)) (*RetryResult, error) {
	spanName := fmt.Sprintf("retry.circuit_breaker.%s", operation)
	ctx, span := r.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
		trace.WithAttributes(r.config.Attributes...),
	)
	defer span.End()

	if !circuitBreaker.AllowRequest() {
		span.SetStatus(codes.Error, "Circuit breaker is open")
		span.SetAttributes(
			attribute.String("circuit_breaker.state", "open"),
			attribute.String("error.type", "circuit_breaker_open"),
		)
		return &RetryResult{
			Success:   false,
			Attempts:  0,
			LastError: fmt.Errorf("circuit breaker is open for operation: %s", operation),
		}, fmt.Errorf("circuit breaker is open for operation: %s", operation)
	}

	result, err := r.Execute(ctx, operation, fn)

	if err != nil {
		circuitBreaker.RecordFailure()
		span.SetAttributes(
			attribute.String("circuit_breaker.result", "failure"),
		)
	} else {
		circuitBreaker.RecordSuccess()
		span.SetAttributes(
			attribute.String("circuit_breaker.result", "success"),
		)
	}

	return result, err
}

func (r *RetryHandler) GetConfig() RetryConfig {
	return r.config
}

func (r *RetryHandler) GetTracer() trace.Tracer {
	return r.tracer
}

type CircuitBreaker interface {
	AllowRequest() bool
	RecordSuccess()
	RecordFailure()
	GetState() string
}

type SimpleCircuitBreaker struct {
	failureCount    int
	successCount    int
	failureThreshold int
	resetTimeout    time.Duration
	lastFailureTime time.Time
	state           string
}

func NewSimpleCircuitBreaker(failureThreshold int, resetTimeout time.Duration) *SimpleCircuitBreaker {
	return &SimpleCircuitBreaker{
		failureThreshold: failureThreshold,
		resetTimeout:     resetTimeout,
		state:           "closed",
	}
}

func (cb *SimpleCircuitBreaker) AllowRequest() bool {
	if cb.state == "open" {
		if time.Since(cb.lastFailureTime) > cb.resetTimeout {
			cb.state = "half-open"
			return true
		}
		return false
	}
	return true
}

func (cb *SimpleCircuitBreaker) RecordSuccess() {
	cb.successCount++
	cb.failureCount = 0
	if cb.state == "half-open" {
		cb.state = "closed"
	}
}

func (cb *SimpleCircuitBreaker) RecordFailure() {
	cb.failureCount++
	cb.lastFailureTime = time.Now()
	if cb.failureCount >= cb.failureThreshold {
		cb.state = "open"
	}
}

func (cb *SimpleCircuitBreaker) GetState() string {
	return cb.state
}