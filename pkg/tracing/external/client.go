package external

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

const (
	ExternalServiceSpanName = "external.service.call"
	HTTPClientSpanName      = "external.http.client"
)

type TracedHTTPClient struct {
	client     *http.Client
	tracer     trace.Tracer
	serviceName string
	attributes  []attribute.KeyValue
}

type ClientConfig struct {
	ServiceName    string
	TracerName     string
	DefaultTimeout time.Duration
	UserAgent      string
	Attributes     []attribute.KeyValue
	HTTPClient     *http.Client
}

func NewTracedHTTPClient(cfg ClientConfig) *TracedHTTPClient {
	if cfg.TracerName == "" {
		cfg.TracerName = "external-service-tracer"
	}

	if cfg.ServiceName == "" {
		cfg.ServiceName = "external-service"
	}

	if cfg.DefaultTimeout == 0 {
		cfg.DefaultTimeout = 30 * time.Second
	}

	client := cfg.HTTPClient
	if client == nil {
		client = &http.Client{
			Timeout: cfg.DefaultTimeout,
		}
	}

	defaultAttrs := []attribute.KeyValue{
		attribute.String("service.name", cfg.ServiceName),
		attribute.String("service.type", "external"),
	}

	if cfg.UserAgent != "" {
		defaultAttrs = append(defaultAttrs, attribute.String("http.user_agent", cfg.UserAgent))
	}

	if cfg.Attributes != nil {
		defaultAttrs = append(defaultAttrs, cfg.Attributes...)
	}

	return &TracedHTTPClient{
		client:      client,
		tracer:      otel.Tracer(cfg.TracerName),
		serviceName: cfg.ServiceName,
		attributes:  defaultAttrs,
	}
}

func (t *TracedHTTPClient) Do(ctx context.Context, req *http.Request) (*http.Response, error) {
	spanName := fmt.Sprintf("%s %s", req.Method, req.URL.Host)
	ctx, span := t.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindClient),
		trace.WithAttributes(t.attributes...),
	)
	defer span.End()

	t.setHTTPAttributes(span, req)

	start := time.Now()
	resp, err := t.client.Do(req.WithContext(ctx))
	duration := time.Since(start)

	t.recordMetrics(req, resp, err, duration)

	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.SetAttributes(
			attribute.String("error.type", "http_request_error"),
			attribute.String("error.message", err.Error()),
		)
		return nil, fmt.Errorf("external service call failed: %w", err)
	}

	if resp != nil {
		span.SetAttributes(
			attribute.Int("http.status_code", resp.StatusCode),
			attribute.String("http.status_text", resp.Status),
		)

		if resp.StatusCode >= 400 {
			span.SetStatus(codes.Error, fmt.Sprintf("HTTP %d", resp.StatusCode))
			span.SetAttributes(
				attribute.String("error.type", "http_status_error"),
				attribute.String("error.message", fmt.Sprintf("HTTP %d %s", resp.StatusCode, resp.Status)),
			)
		}
	}

	return resp, nil
}

func (t *TracedHTTPClient) Get(ctx context.Context, url string) (*http.Response, error) {
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create GET request: %w", err)
	}
	return t.Do(ctx, req)
}

func (t *TracedHTTPClient) Post(ctx context.Context, url, contentType string, body interface{}) (*http.Response, error) {
	req, err := createRequestWithBody(http.MethodPost, url, contentType, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create POST request: %w", err)
	}
	return t.Do(ctx, req)
}

func (t *TracedHTTPClient) Put(ctx context.Context, url, contentType string, body interface{}) (*http.Response, error) {
	req, err := createRequestWithBody(http.MethodPut, url, contentType, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create PUT request: %w", err)
	}
	return t.Do(ctx, req)
}

func (t *TracedHTTPClient) Delete(ctx context.Context, url string) (*http.Response, error) {
	req, err := http.NewRequest(http.MethodDelete, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create DELETE request: %w", err)
	}
	return t.Do(ctx, req)
}

func (t *TracedHTTPClient) setHTTPAttributes(span trace.Span, req *http.Request) {
	span.SetAttributes(
		attribute.String("http.method", req.Method),
		attribute.String("http.url", req.URL.String()),
		attribute.String("http.scheme", req.URL.Scheme),
		attribute.String("http.host", req.URL.Host),
		attribute.String("http.target", req.URL.Path),
		attribute.String("net.peer.name", req.URL.Hostname()),
	)

	if req.URL.Port() != "" {
		span.SetAttributes(attribute.String("net.peer.port", req.URL.Port()))
	}

	if req.ContentLength > 0 {
		span.SetAttributes(attribute.Int64("http.request_content_length", req.ContentLength))
	}

	if userAgent := req.Header.Get("User-Agent"); userAgent != "" {
		span.SetAttributes(attribute.String("http.user_agent", userAgent))
	}
}

func (t *TracedHTTPClient) recordMetrics(req *http.Request, resp *http.Response, err error, duration time.Duration) {
	labels := []attribute.KeyValue{
		attribute.String("service.name", t.serviceName),
		attribute.String("http.method", req.Method),
		attribute.String("http.host", req.URL.Host),
	}

	if resp != nil {
		labels = append(labels, attribute.Int("http.status_code", resp.StatusCode))
	}

	if err != nil {
		labels = append(labels, attribute.String("error", "true"))
	} else {
		labels = append(labels, attribute.String("error", "false"))
	}

	labels = append(labels, attribute.Float64("duration_ms", float64(duration.Nanoseconds())/1e6))
}

func (t *TracedHTTPClient) GetHTTPClient() *http.Client {
	return t.client
}

func (t *TracedHTTPClient) GetServiceName() string {
	return t.serviceName
}

func (t *TracedHTTPClient) GetTracer() trace.Tracer {
	return t.tracer
}

func (t *TracedHTTPClient) SetAttributes(attrs ...attribute.KeyValue) {
	t.attributes = append(t.attributes, attrs...)
}

func (t *TracedHTTPClient) Clone() *TracedHTTPClient {
	return &TracedHTTPClient{
		client:      t.client,
		tracer:      t.tracer,
		serviceName: t.serviceName,
		attributes:  append([]attribute.KeyValue{}, t.attributes...),
	}
}