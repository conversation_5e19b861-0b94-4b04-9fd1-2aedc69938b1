package external

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/trace"
)

type MetricsCollector struct {
	tracer          trace.Tracer
	meter           metric.Meter
	requestCounter  metric.Int64Counter
	durationHisto   metric.Float64Histogram
	errorCounter    metric.Int64Counter
	activeRequests  metric.Int64UpDownCounter
	attributes      []attribute.KeyValue
	customMetrics   map[string]metric.Instrument
	metricsLock     sync.RWMutex
}

type MetricsConfig struct {
	MeterName  string
	TracerName string
	Attributes []attribute.KeyValue
	Enabled    bool
}

type ServiceMetrics struct {
	ServiceName     string
	TotalRequests   int64
	SuccessRequests int64
	ErrorRequests   int64
	AvgDuration     time.Duration
	MinDuration     time.Duration
	MaxDuration     time.Duration
	P95Duration     time.Duration
	P99Duration     time.Duration
	ErrorRate       float64
	Throughput      float64
	ActiveRequests  int64
	Attributes      []attribute.KeyValue
	LastUpdated     time.Time
}

type MetricsSnapshot struct {
	Services  map[string]*ServiceMetrics
	Timestamp time.Time
	Duration  time.Duration
}

func NewMetricsCollector(cfg MetricsConfig) (*MetricsCollector, error) {
	if !cfg.Enabled {
		return &MetricsCollector{
			tracer:        otel.Tracer("disabled-metrics-tracer"),
			customMetrics: make(map[string]metric.Instrument),
		}, nil
	}

	if cfg.MeterName == "" {
		cfg.MeterName = "external-service-metrics"
	}

	if cfg.TracerName == "" {
		cfg.TracerName = "external-service-metrics-tracer"
	}

	meter := otel.Meter(cfg.MeterName)
	tracer := otel.Tracer(cfg.TracerName)

	requestCounter, err := meter.Int64Counter(
		"external_service_requests_total",
		metric.WithDescription("Total number of external service requests"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request counter: %w", err)
	}

	durationHisto, err := meter.Float64Histogram(
		"external_service_request_duration_ms",
		metric.WithDescription("Duration of external service requests in milliseconds"),
		metric.WithUnit("ms"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create duration histogram: %w", err)
	}

	errorCounter, err := meter.Int64Counter(
		"external_service_errors_total",
		metric.WithDescription("Total number of external service errors"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create error counter: %w", err)
	}

	activeRequests, err := meter.Int64UpDownCounter(
		"external_service_active_requests",
		metric.WithDescription("Number of active external service requests"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create active requests counter: %w", err)
	}

	return &MetricsCollector{
		tracer:         tracer,
		meter:          meter,
		requestCounter: requestCounter,
		durationHisto:  durationHisto,
		errorCounter:   errorCounter,
		activeRequests: activeRequests,
		attributes:     cfg.Attributes,
		customMetrics:  make(map[string]metric.Instrument),
	}, nil
}

func (m *MetricsCollector) RecordRequest(ctx context.Context, serviceName, operation string, duration time.Duration, err error, attrs ...attribute.KeyValue) {
	if m.requestCounter == nil {
		return
	}

	spanName := fmt.Sprintf("metrics.record_request.%s", serviceName)
	ctx, span := m.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
		trace.WithAttributes(m.attributes...),
	)
	defer span.End()

	labels := []attribute.KeyValue{
		attribute.String("service.name", serviceName),
		attribute.String("operation", operation),
	}

	if err != nil {
		labels = append(labels, attribute.String("status", "error"))
		labels = append(labels, attribute.String("error.type", "request_error"))
		m.errorCounter.Add(ctx, 1, metric.WithAttributes(labels...))
	} else {
		labels = append(labels, attribute.String("status", "success"))
	}

	labels = append(labels, attrs...)
	labels = append(labels, m.attributes...)

	m.requestCounter.Add(ctx, 1, metric.WithAttributes(labels...))
	m.durationHisto.Record(ctx, float64(duration.Nanoseconds())/1e6, metric.WithAttributes(labels...))

	span.SetAttributes(
		attribute.String("service.name", serviceName),
		attribute.String("operation", operation),
		attribute.Float64("duration_ms", float64(duration.Nanoseconds())/1e6),
		attribute.Bool("success", err == nil),
	)
}

func (m *MetricsCollector) IncrementActiveRequests(ctx context.Context, serviceName string, attrs ...attribute.KeyValue) {
	if m.activeRequests == nil {
		return
	}

	labels := []attribute.KeyValue{
		attribute.String("service.name", serviceName),
	}
	labels = append(labels, attrs...)
	labels = append(labels, m.attributes...)

	m.activeRequests.Add(ctx, 1, metric.WithAttributes(labels...))
}

func (m *MetricsCollector) DecrementActiveRequests(ctx context.Context, serviceName string, attrs ...attribute.KeyValue) {
	if m.activeRequests == nil {
		return
	}

	labels := []attribute.KeyValue{
		attribute.String("service.name", serviceName),
	}
	labels = append(labels, attrs...)
	labels = append(labels, m.attributes...)

	m.activeRequests.Add(ctx, -1, metric.WithAttributes(labels...))
}

func (m *MetricsCollector) RecordCustomMetric(ctx context.Context, metricName string, value float64, attrs ...attribute.KeyValue) error {
	spanName := fmt.Sprintf("metrics.record_custom.%s", metricName)
	ctx, span := m.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
		trace.WithAttributes(m.attributes...),
	)
	defer span.End()

	m.metricsLock.RLock()
	instrument, exists := m.customMetrics[metricName]
	m.metricsLock.RUnlock()

	if !exists {
		m.metricsLock.Lock()
		gauge, err := m.meter.Float64Gauge(
			metricName,
			metric.WithDescription(fmt.Sprintf("Custom metric: %s", metricName)),
		)
		if err != nil {
			m.metricsLock.Unlock()
			span.SetStatus(codes.Error, err.Error())
			return fmt.Errorf("failed to create custom metric %s: %w", metricName, err)
		}
		m.customMetrics[metricName] = gauge
		instrument = gauge
		m.metricsLock.Unlock()
	}

	if gauge, ok := instrument.(metric.Float64Gauge); ok {
		labels := append([]attribute.KeyValue{}, attrs...)
		labels = append(labels, m.attributes...)
		gauge.Record(ctx, value, metric.WithAttributes(labels...))
	}

	span.SetAttributes(
		attribute.String("metric.name", metricName),
		attribute.Float64("metric.value", value),
	)

	return nil
}

func (m *MetricsCollector) CreateCustomCounter(name, description string) (metric.Int64Counter, error) {
	return m.meter.Int64Counter(
		name,
		metric.WithDescription(description),
		metric.WithUnit("1"),
	)
}

func (m *MetricsCollector) CreateCustomHistogram(name, description string) (metric.Float64Histogram, error) {
	return m.meter.Float64Histogram(
		name,
		metric.WithDescription(description),
		metric.WithUnit("ms"),
	)
}

func (m *MetricsCollector) CreateCustomGauge(name, description string) (metric.Float64Gauge, error) {
	return m.meter.Float64Gauge(
		name,
		metric.WithDescription(description),
	)
}

func (m *MetricsCollector) RecordBatch(ctx context.Context, records []MetricRecord) error {
	spanName := fmt.Sprintf("metrics.record_batch_%d", len(records))
	ctx, span := m.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
		trace.WithAttributes(m.attributes...),
	)
	defer span.End()

	var errors []error
	for _, record := range records {
		switch record.Type {
		case MetricTypeCounter:
			m.requestCounter.Add(ctx, int64(record.Value), metric.WithAttributes(record.Attributes...))
		case MetricTypeHistogram:
			m.durationHisto.Record(ctx, record.Value, metric.WithAttributes(record.Attributes...))
		case MetricTypeGauge:
			err := m.RecordCustomMetric(ctx, record.Name, record.Value, record.Attributes...)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}

	span.SetAttributes(
		attribute.Int("batch.size", len(records)),
		attribute.Int("batch.errors", len(errors)),
	)

	if len(errors) > 0 {
		return fmt.Errorf("batch recording failed with %d errors", len(errors))
	}

	return nil
}

func (m *MetricsCollector) GetMeter() metric.Meter {
	return m.meter
}

func (m *MetricsCollector) GetTracer() trace.Tracer {
	return m.tracer
}

func (m *MetricsCollector) SetAttributes(attrs ...attribute.KeyValue) {
	m.attributes = append(m.attributes, attrs...)
}

type MetricRecord struct {
	Name       string
	Type       MetricType
	Value      float64
	Attributes []attribute.KeyValue
}

type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeGauge     MetricType = "gauge"
)

func (m *MetricsCollector) StartRequest(ctx context.Context, serviceName, operation string) (context.Context, func(error)) {
	m.IncrementActiveRequests(ctx, serviceName)
	start := time.Now()

	return ctx, func(err error) {
		duration := time.Since(start)
		m.DecrementActiveRequests(ctx, serviceName)
		m.RecordRequest(ctx, serviceName, operation, duration, err)
	}
}

func (m *MetricsCollector) MeasureLatency(ctx context.Context, serviceName, operation string, fn func() error) error {
	ctx, finishFn := m.StartRequest(ctx, serviceName, operation)
	err := fn()
	finishFn(err)
	return err
}

func (m *MetricsCollector) MeasureLatencyWithResult(ctx context.Context, serviceName, operation string, fn func() (interface{}, error)) (interface{}, error) {
	ctx, finishFn := m.StartRequest(ctx, serviceName, operation)
	result, err := fn()
	finishFn(err)
	return result, err
}