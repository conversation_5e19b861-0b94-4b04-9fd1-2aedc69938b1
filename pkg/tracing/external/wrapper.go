package external

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

type ServiceWrapper struct {
	tracer      trace.Tracer
	serviceName string
	attributes  []attribute.KeyValue
	client      *TracedHTTPClient
}

type WrapperConfig struct {
	ServiceName string
	TracerName  string
	Attributes  []attribute.KeyValue
	HTTPClient  *http.Client
}

type ServiceCall struct {
	ServiceName string
	Operation   string
	Attributes  []attribute.KeyValue
	Timeout     time.Duration
}

type ServiceResponse struct {
	Data       interface{}
	StatusCode int
	Headers    http.Header
	Duration   time.Duration
	Error      error
}

func NewServiceWrapper(cfg WrapperConfig) *ServiceWrapper {
	if cfg.TracerName == "" {
		cfg.TracerName = fmt.Sprintf("%s-wrapper", cfg.ServiceName)
	}

	clientCfg := ClientConfig{
		ServiceName:    cfg.ServiceName,
		TracerName:     cfg.TracerName,
		DefaultTimeout: 30 * time.Second,
		Attributes:     cfg.Attributes,
		HTTPClient:     cfg.HTTPClient,
	}

	return &ServiceWrapper{
		tracer:      otel.Tracer(cfg.TracerName),
		serviceName: cfg.ServiceName,
		attributes:  cfg.Attributes,
		client:      NewTracedHTTPClient(clientCfg),
	}
}

func (w *ServiceWrapper) CallService(ctx context.Context, call ServiceCall, fn func(ctx context.Context) (interface{}, error)) (interface{}, error) {
	spanName := fmt.Sprintf("%s.%s", call.ServiceName, call.Operation)
	ctx, span := w.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindClient),
		trace.WithAttributes(w.attributes...),
	)
	defer span.End()

	if call.Attributes != nil {
		span.SetAttributes(call.Attributes...)
	}

	span.SetAttributes(
		attribute.String("service.name", call.ServiceName),
		attribute.String("service.operation", call.Operation),
	)

	if call.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, call.Timeout)
		defer cancel()
	}

	start := time.Now()
	result, err := fn(ctx)
	duration := time.Since(start)

	span.SetAttributes(
		attribute.Float64("duration_ms", float64(duration.Nanoseconds())/1e6),
	)

	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.SetAttributes(
			attribute.String("error.type", "service_call_error"),
			attribute.String("error.message", err.Error()),
		)
		return nil, fmt.Errorf("service call failed: %w", err)
	}

	span.SetStatus(codes.Ok, "Service call completed successfully")
	return result, nil
}

func (w *ServiceWrapper) HTTPCall(ctx context.Context, method, url string, body interface{}, headers map[string]string) (*ServiceResponse, error) {
	spanName := fmt.Sprintf("HTTP %s %s", method, extractHostFromURL(url))
	ctx, span := w.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindClient),
		trace.WithAttributes(w.attributes...),
	)
	defer span.End()

	req, err := w.createHTTPRequest(method, url, body)
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	start := time.Now()
	resp, err := w.client.Do(ctx, req)
	duration := time.Since(start)

	serviceResp := &ServiceResponse{
		Duration: duration,
		Error:    err,
	}

	if resp != nil {
		serviceResp.StatusCode = resp.StatusCode
		serviceResp.Headers = resp.Header
		defer resp.Body.Close()

		responseBody, readErr := io.ReadAll(resp.Body)
		if readErr != nil {
			span.SetAttributes(
				attribute.String("error.type", "response_read_error"),
				attribute.String("error.message", readErr.Error()),
			)
		} else {
			serviceResp.Data = responseBody
		}
	}

	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		return serviceResp, fmt.Errorf("HTTP call failed: %w", err)
	}

	return serviceResp, nil
}

func (w *ServiceWrapper) BatchCall(ctx context.Context, calls []ServiceCall, fn func(ctx context.Context, call ServiceCall) (interface{}, error)) ([]interface{}, error) {
	spanName := fmt.Sprintf("batch_call_%d_operations", len(calls))
	ctx, span := w.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindClient),
		trace.WithAttributes(w.attributes...),
	)
	defer span.End()

	span.SetAttributes(
		attribute.Int("batch.size", len(calls)),
	)

	results := make([]interface{}, len(calls))
	errors := make([]error, len(calls))
	successCount := 0

	start := time.Now()
	for i, call := range calls {
		result, err := fn(ctx, call)
		results[i] = result
		errors[i] = err
		if err == nil {
			successCount++
		}
	}
	duration := time.Since(start)

	span.SetAttributes(
		attribute.Int("batch.success_count", successCount),
		attribute.Int("batch.error_count", len(calls)-successCount),
		attribute.Float64("batch.duration_ms", float64(duration.Nanoseconds())/1e6),
	)

	if successCount == 0 {
		span.SetStatus(codes.Error, "All batch operations failed")
		return results, fmt.Errorf("all batch operations failed")
	}

	if successCount < len(calls) {
		span.SetStatus(codes.Error, fmt.Sprintf("Partial batch failure: %d/%d operations failed", len(calls)-successCount, len(calls)))
	}

	return results, nil
}

func (w *ServiceWrapper) createHTTPRequest(method, url string, body interface{}) (*http.Request, error) {
	var reqBody io.Reader

	if body != nil {
		switch v := body.(type) {
		case string:
			reqBody = strings.NewReader(v)
		case []byte:
			reqBody = bytes.NewReader(v)
		case io.Reader:
			reqBody = v
		default:
			jsonBody, err := json.Marshal(body)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal request body: %w", err)
			}
			reqBody = bytes.NewReader(jsonBody)
		}
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return req, nil
}

func (w *ServiceWrapper) GetClient() *TracedHTTPClient {
	return w.client
}

func (w *ServiceWrapper) GetServiceName() string {
	return w.serviceName
}

func (w *ServiceWrapper) GetTracer() trace.Tracer {
	return w.tracer
}

func (w *ServiceWrapper) SetAttributes(attrs ...attribute.KeyValue) {
	w.attributes = append(w.attributes, attrs...)
}

func extractHostFromURL(url string) string {
	if strings.HasPrefix(url, "http://") {
		url = url[7:]
	} else if strings.HasPrefix(url, "https://") {
		url = url[8:]
	}

	if idx := strings.Index(url, "/"); idx != -1 {
		url = url[:idx]
	}

	return url
}

func createRequestWithBody(method, url, contentType string, body interface{}) (*http.Request, error) {
	var reqBody io.Reader

	if body != nil {
		switch v := body.(type) {
		case string:
			reqBody = strings.NewReader(v)
		case []byte:
			reqBody = bytes.NewReader(v)
		case io.Reader:
			reqBody = v
		default:
			jsonBody, err := json.Marshal(body)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal request body: %w", err)
			}
			reqBody = bytes.NewReader(jsonBody)
		}
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}

	return req, nil
}