package external

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

type ServiceMonitor struct {
	tracer          trace.Tracer
	dependencies    map[string]*ServiceDependency
	dependencyMutex sync.RWMutex
	healthChecks    map[string]HealthCheck
	healthMutex     sync.RWMutex
	attributes      []attribute.KeyValue
}

type ServiceDependency struct {
	Name         string
	Type         string
	Endpoint     string
	Version      string
	Critical     bool
	Attributes   []attribute.KeyValue
	LastSeen     time.Time
	CallCount    int64
	ErrorCount   int64
	AvgDuration  time.Duration
	mutex        sync.RWMutex
}

type HealthCheck struct {
	ServiceName string
	Status      HealthStatus
	LastCheck   time.Time
	LastError   error
	CheckFn     func(ctx context.Context) error
	Interval    time.Duration
	Timeout     time.Duration
	Attributes  []attribute.KeyValue
}

type HealthStatus string

const (
	HealthStatusUnknown     HealthStatus = "unknown"
	HealthStatusHealthy     HealthStatus = "healthy"
	HealthStatusUnhealthy   HealthStatus = "unhealthy"
	HealthStatusDegraded    HealthStatus = "degraded"
	HealthStatusMaintenance HealthStatus = "maintenance"
)

type MonitorConfig struct {
	TracerName string
	Attributes []attribute.KeyValue
}

type DependencyMap struct {
	Services    map[string]*ServiceDependency
	Connections []ServiceConnection
	UpdatedAt   time.Time
}

type ServiceConnection struct {
	From        string
	To          string
	CallCount   int64
	ErrorRate   float64
	AvgDuration time.Duration
	Attributes  []attribute.KeyValue
}

func NewServiceMonitor(cfg MonitorConfig) *ServiceMonitor {
	if cfg.TracerName == "" {
		cfg.TracerName = "external-service-monitor"
	}

	return &ServiceMonitor{
		tracer:       otel.Tracer(cfg.TracerName),
		dependencies: make(map[string]*ServiceDependency),
		healthChecks: make(map[string]HealthCheck),
		attributes:   cfg.Attributes,
	}
}

func (m *ServiceMonitor) RegisterDependency(name, serviceType, endpoint, version string, critical bool, attrs ...attribute.KeyValue) {
	m.dependencyMutex.Lock()
	defer m.dependencyMutex.Unlock()

	dependency := &ServiceDependency{
		Name:        name,
		Type:        serviceType,
		Endpoint:    endpoint,
		Version:     version,
		Critical:    critical,
		Attributes:  attrs,
		LastSeen:    time.Now(),
		CallCount:   0,
		ErrorCount:  0,
		AvgDuration: 0,
	}

	m.dependencies[name] = dependency
}

func (m *ServiceMonitor) RecordCall(ctx context.Context, serviceName string, duration time.Duration, err error) {
	spanName := fmt.Sprintf("monitor.record_call.%s", serviceName)
	ctx, span := m.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
		trace.WithAttributes(m.attributes...),
	)
	defer span.End()

	m.dependencyMutex.Lock()
	defer m.dependencyMutex.Unlock()

	dependency, exists := m.dependencies[serviceName]
	if !exists {
		dependency = &ServiceDependency{
			Name:        serviceName,
			Type:        "unknown",
			LastSeen:    time.Now(),
			CallCount:   0,
			ErrorCount:  0,
			AvgDuration: 0,
		}
		m.dependencies[serviceName] = dependency
	}

	dependency.mutex.Lock()
	dependency.CallCount++
	dependency.LastSeen = time.Now()

	if err != nil {
		dependency.ErrorCount++
		span.SetStatus(codes.Error, err.Error())
		span.SetAttributes(
			attribute.String("error.type", "service_call_error"),
			attribute.String("error.message", err.Error()),
		)
	}

	if dependency.CallCount == 1 {
		dependency.AvgDuration = duration
	} else {
		dependency.AvgDuration = time.Duration(
			(int64(dependency.AvgDuration)*dependency.CallCount + int64(duration)) / (dependency.CallCount + 1),
		)
	}

	dependency.mutex.Unlock()

	span.SetAttributes(
		attribute.String("service.name", serviceName),
		attribute.Int64("service.call_count", dependency.CallCount),
		attribute.Int64("service.error_count", dependency.ErrorCount),
		attribute.Float64("service.error_rate", float64(dependency.ErrorCount)/float64(dependency.CallCount)),
		attribute.Float64("service.avg_duration_ms", float64(dependency.AvgDuration.Nanoseconds())/1e6),
		attribute.Float64("call.duration_ms", float64(duration.Nanoseconds())/1e6),
	)
}

func (m *ServiceMonitor) RegisterHealthCheck(serviceName string, checkFn func(ctx context.Context) error, interval, timeout time.Duration, attrs ...attribute.KeyValue) {
	m.healthMutex.Lock()
	defer m.healthMutex.Unlock()

	m.healthChecks[serviceName] = HealthCheck{
		ServiceName: serviceName,
		Status:      HealthStatusUnknown,
		CheckFn:     checkFn,
		Interval:    interval,
		Timeout:     timeout,
		Attributes:  attrs,
	}
}

func (m *ServiceMonitor) RunHealthCheck(ctx context.Context, serviceName string) error {
	spanName := fmt.Sprintf("monitor.health_check.%s", serviceName)
	ctx, span := m.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
		trace.WithAttributes(m.attributes...),
	)
	defer span.End()

	m.healthMutex.RLock()
	check, exists := m.healthChecks[serviceName]
	m.healthMutex.RUnlock()

	if !exists {
		err := fmt.Errorf("health check not found for service: %s", serviceName)
		span.SetStatus(codes.Error, err.Error())
		return err
	}

	if check.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, check.Timeout)
		defer cancel()
	}

	start := time.Now()
	err := check.CheckFn(ctx)
	duration := time.Since(start)

	m.healthMutex.Lock()
	updatedCheck := m.healthChecks[serviceName]
	updatedCheck.LastCheck = time.Now()
	updatedCheck.LastError = err

	if err != nil {
		updatedCheck.Status = HealthStatusUnhealthy
		span.SetStatus(codes.Error, err.Error())
		span.SetAttributes(
			attribute.String("health.status", string(HealthStatusUnhealthy)),
			attribute.String("error.message", err.Error()),
		)
	} else {
		updatedCheck.Status = HealthStatusHealthy
		span.SetStatus(codes.Ok, "Health check passed")
		span.SetAttributes(
			attribute.String("health.status", string(HealthStatusHealthy)),
		)
	}

	m.healthChecks[serviceName] = updatedCheck
	m.healthMutex.Unlock()

	span.SetAttributes(
		attribute.String("service.name", serviceName),
		attribute.Float64("health_check.duration_ms", float64(duration.Nanoseconds())/1e6),
	)

	return err
}

func (m *ServiceMonitor) StartHealthCheckScheduler(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.runScheduledHealthChecks(ctx)
		}
	}
}

func (m *ServiceMonitor) runScheduledHealthChecks(ctx context.Context) {
	m.healthMutex.RLock()
	checks := make([]HealthCheck, 0, len(m.healthChecks))
	for _, check := range m.healthChecks {
		if time.Since(check.LastCheck) >= check.Interval {
			checks = append(checks, check)
		}
	}
	m.healthMutex.RUnlock()

	for _, check := range checks {
		go func(check HealthCheck) {
			checkCtx, cancel := context.WithTimeout(ctx, check.Timeout)
			defer cancel()
			m.RunHealthCheck(checkCtx, check.ServiceName)
		}(check)
	}
}

func (m *ServiceMonitor) GetDependencyMap(ctx context.Context) (*DependencyMap, error) {
	spanName := "monitor.get_dependency_map"
	ctx, span := m.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
		trace.WithAttributes(m.attributes...),
	)
	defer span.End()

	m.dependencyMutex.RLock()
	defer m.dependencyMutex.RUnlock()

	services := make(map[string]*ServiceDependency)
	connections := make([]ServiceConnection, 0)

	for name, dep := range m.dependencies {
		depCopy := *dep
		services[name] = &depCopy

		if dep.CallCount > 0 {
			connection := ServiceConnection{
				From:        "wn-api-v3",
				To:          name,
				CallCount:   dep.CallCount,
				ErrorRate:   float64(dep.ErrorCount) / float64(dep.CallCount),
				AvgDuration: dep.AvgDuration,
				Attributes:  dep.Attributes,
			}
			connections = append(connections, connection)
		}
	}

	dependencyMap := &DependencyMap{
		Services:    services,
		Connections: connections,
		UpdatedAt:   time.Now(),
	}

	span.SetAttributes(
		attribute.Int("dependency_map.service_count", len(services)),
		attribute.Int("dependency_map.connection_count", len(connections)),
	)

	return dependencyMap, nil
}

func (m *ServiceMonitor) GetServiceHealth(serviceName string) (HealthStatus, error) {
	m.healthMutex.RLock()
	defer m.healthMutex.RUnlock()

	check, exists := m.healthChecks[serviceName]
	if !exists {
		return HealthStatusUnknown, fmt.Errorf("health check not found for service: %s", serviceName)
	}

	return check.Status, nil
}

func (m *ServiceMonitor) GetAllServiceHealth() map[string]HealthStatus {
	m.healthMutex.RLock()
	defer m.healthMutex.RUnlock()

	status := make(map[string]HealthStatus)
	for name, check := range m.healthChecks {
		status[name] = check.Status
	}

	return status
}

func (m *ServiceMonitor) GetServiceStats(serviceName string) (*ServiceDependency, error) {
	m.dependencyMutex.RLock()
	defer m.dependencyMutex.RUnlock()

	dep, exists := m.dependencies[serviceName]
	if !exists {
		return nil, fmt.Errorf("service dependency not found: %s", serviceName)
	}

	depCopy := *dep
	return &depCopy, nil
}

func (m *ServiceMonitor) GetTracer() trace.Tracer {
	return m.tracer
}

func (m *ServiceMonitor) SetAttributes(attrs ...attribute.KeyValue) {
	m.attributes = append(m.attributes, attrs...)
}