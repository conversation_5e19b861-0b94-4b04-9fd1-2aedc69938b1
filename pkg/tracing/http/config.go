package http

import (
	"context"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	"go.opentelemetry.io/otel/trace"
)

// Config holds configuration for HTTP tracing
type Config struct {
	// ServiceName is the name of the service
	ServiceName string
	
	// ServiceVersion is the version of the service
	ServiceVersion string
	
	// Environment is the deployment environment (dev, staging, production)
	Environment string
	
	// ExporterType defines the exporter to use (jaeger, otlp, stdout)
	ExporterType string
	
	// JaegerEndpoint is the Jaeger collector endpoint
	JaegerEndpoint string
	
	// OTLPEndpoint is the OTLP collector endpoint
	OTLPEndpoint string
	
	// SampleRate is the sampling rate for traces (0.0 to 1.0)
	SampleRate float64
	
	// Timeout for exporter operations
	Timeout time.Duration
	
	// Headers to exclude from tracing
	ExcludeHeaders []string
	
	// Enable request/response body capture
	CaptureRequestBody  bool
	CaptureResponseBody bool
	
	// Maximum body size to capture (in bytes)
	MaxBodySize int64
	
	// Custom attributes to add to all spans
	CustomAttributes []attribute.KeyValue
}

// DefaultConfig returns a default configuration
func DefaultConfig() *Config {
	return &Config{
		ServiceName:         "wn-api-v3",
		ServiceVersion:      "1.0.0",
		Environment:         "development",
		ExporterType:        "stdout",
		JaegerEndpoint:      "http://localhost:14268/api/traces",
		OTLPEndpoint:        "http://localhost:4318/v1/traces",
		SampleRate:          1.0,
		Timeout:             10 * time.Second,
		ExcludeHeaders:      []string{"authorization", "cookie", "x-api-key"},
		CaptureRequestBody:  false,
		CaptureResponseBody: false,
		MaxBodySize:         1024 * 1024, // 1MB
		CustomAttributes:    []attribute.KeyValue{},
	}
}

// TracingManager manages OpenTelemetry tracing setup
type TracingManager struct {
	config       *Config
	tracer       trace.Tracer
	provider     *sdktrace.TracerProvider
	shutdown     func(context.Context) error
}

// NewTracingManager creates a new tracing manager
func NewTracingManager(config *Config) (*TracingManager, error) {
	if config == nil {
		config = DefaultConfig()
	}
	
	// Create resource
	res, err := resource.New(context.Background(),
		resource.WithAttributes(
			semconv.ServiceName(config.ServiceName),
			semconv.ServiceVersion(config.ServiceVersion),
			semconv.DeploymentEnvironment(config.Environment),
		),
		resource.WithAttributes(config.CustomAttributes...),
	)
	if err != nil {
		return nil, err
	}
	
	// Create exporter
	var exporter sdktrace.SpanExporter
	switch config.ExporterType {
	case "jaeger":
		exporter, err = jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(config.JaegerEndpoint)))
	case "otlp":
		exporter, err = otlptrace.New(context.Background(),
			otlptracehttp.NewClient(
				otlptracehttp.WithEndpoint(config.OTLPEndpoint),
				otlptracehttp.WithTimeout(config.Timeout),
			),
		)
	case "stdout":
		exporter, err = stdouttrace.New(stdouttrace.WithPrettyPrint())
	default:
		exporter, err = stdouttrace.New(stdouttrace.WithPrettyPrint())
	}
	
	if err != nil {
		return nil, err
	}
	
	// Create tracer provider
	tp := sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
		sdktrace.WithSampler(sdktrace.TraceIDRatioBased(config.SampleRate)),
	)
	
	// Set global tracer provider
	otel.SetTracerProvider(tp)
	
	// Create tracer
	tracer := tp.Tracer("wn-api-v3/http")
	
	return &TracingManager{
		config:   config,
		tracer:   tracer,
		provider: tp,
		shutdown: func(ctx context.Context) error {
			return tp.Shutdown(ctx)
		},
	}, nil
}

// GetTracer returns the tracer instance
func (tm *TracingManager) GetTracer() trace.Tracer {
	return tm.tracer
}

// GetConfig returns the configuration
func (tm *TracingManager) GetConfig() *Config {
	return tm.config
}

// Shutdown gracefully shuts down the tracing manager
func (tm *TracingManager) Shutdown(ctx context.Context) error {
	if tm.shutdown != nil {
		return tm.shutdown(ctx)
	}
	return nil
}

// Global instance for easy access
var globalManager *TracingManager

// InitializeGlobalTracing initializes global tracing with the provided config
func InitializeGlobalTracing(config *Config) error {
	manager, err := NewTracingManager(config)
	if err != nil {
		return err
	}
	
	globalManager = manager
	return nil
}

// GetGlobalTracer returns the global tracer instance
func GetGlobalTracer() trace.Tracer {
	if globalManager == nil {
		// Initialize with default config if not initialized
		_ = InitializeGlobalTracing(DefaultConfig())
	}
	return globalManager.GetTracer()
}

// GetGlobalManager returns the global tracing manager
func GetGlobalManager() *TracingManager {
	return globalManager
}

// ShutdownGlobalTracing shuts down the global tracing
func ShutdownGlobalTracing(ctx context.Context) error {
	if globalManager != nil {
		return globalManager.Shutdown(ctx)
	}
	return nil
}