package http

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/propagation"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	"go.opentelemetry.io/otel/trace"
)

// HTTPInstrumentor provides HTTP request/response instrumentation
type HTTPInstrumentor struct {
	tracer trace.Tracer
	config *Config
}

// NewHTTPInstrumentor creates a new HTTP instrumentor
func NewHTTPInstrumentor(config *Config) *HTTPInstrumentor {
	if config == nil {
		config = DefaultConfig()
	}
	
	return &HTTPInstrumentor{
		tracer: GetGlobalTracer(),
		config: config,
	}
}

// InstrumentRequest instruments an HTTP request and returns the context and span
func (i *HTTPInstrumentor) InstrumentRequest(r *http.Request) (context.Context, trace.Span) {
	// Extract trace context from headers
	ctx := otel.GetTextMapPropagator().Extract(r.Context(), propagation.HeaderCarrier(r.Header))
	
	// Create span name
	spanName := fmt.Sprintf("%s %s", r.Method, r.URL.Path)
	
	// Start new span
	ctx, span := i.tracer.Start(ctx, spanName,
		trace.WithSpanKind(trace.SpanKindServer),
		trace.WithTimestamp(time.Now()),
	)
	
	// Set standard HTTP attributes
	span.SetAttributes(
		semconv.HTTPMethod(r.Method),
		semconv.HTTPURL(r.URL.String()),
		semconv.HTTPScheme(r.URL.Scheme),
		semconv.HTTPHost(r.Host),
		semconv.HTTPTarget(r.URL.Path),
		semconv.HTTPUserAgent(r.UserAgent()),
		semconv.HTTPRoute(r.URL.Path),
		semconv.NetHostName(r.Host),
		semconv.NetHostPort(getPortFromHost(r.Host)),
	)
	
	// Add custom attributes
	span.SetAttributes(i.config.CustomAttributes...)
	
	// Add request headers (excluding sensitive ones)
	for name, values := range r.Header {
		if !i.isHeaderExcluded(name) {
			if len(values) == 1 {
				span.SetAttributes(attribute.String(fmt.Sprintf("http.request.header.%s", strings.ToLower(name)), values[0]))
			} else if len(values) > 1 {
				span.SetAttributes(attribute.StringSlice(fmt.Sprintf("http.request.header.%s", strings.ToLower(name)), values))
			}
		}
	}
	
	// Add query parameters
	if r.URL.RawQuery != "" {
		span.SetAttributes(attribute.String("http.request.query", r.URL.RawQuery))
	}
	
	// Capture request body if enabled
	if i.config.CaptureRequestBody && r.Body != nil {
		if body, err := i.captureRequestBody(r); err == nil {
			span.SetAttributes(attribute.String("http.request.body", body))
		}
	}
	
	// Add tenant ID if available
	if tenantID := r.Header.Get("X-Tenant-ID"); tenantID != "" {
		span.SetAttributes(attribute.String("tenant.id", tenantID))
	}
	
	// Add request ID if available
	if requestID := r.Header.Get("X-Request-ID"); requestID != "" {
		span.SetAttributes(attribute.String("request.id", requestID))
	}
	
	return ctx, span
}

// InstrumentResponse instruments an HTTP response
func (i *HTTPInstrumentor) InstrumentResponse(span trace.Span, statusCode int, responseSize int64, headers http.Header) {
	// Set response attributes
	span.SetAttributes(
		semconv.HTTPStatusCode(statusCode),
		attribute.Int64("http.response.size", responseSize),
	)
	
	// Add response headers (excluding sensitive ones)
	for name, values := range headers {
		if !i.isHeaderExcluded(name) {
			if len(values) == 1 {
				span.SetAttributes(attribute.String(fmt.Sprintf("http.response.header.%s", strings.ToLower(name)), values[0]))
			} else if len(values) > 1 {
				span.SetAttributes(attribute.StringSlice(fmt.Sprintf("http.response.header.%s", strings.ToLower(name)), values))
			}
		}
	}
	
	// Set span status based on HTTP status code
	if statusCode >= 400 {
		span.SetStatus(codes.Error, fmt.Sprintf("HTTP %d", statusCode))
	} else {
		span.SetStatus(codes.Ok, "")
	}
}

// InstrumentError instruments an error
func (i *HTTPInstrumentor) InstrumentError(span trace.Span, err error) {
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
	}
}

// FinishSpan finishes the span with the given timestamp
func (i *HTTPInstrumentor) FinishSpan(span trace.Span, timestamp time.Time) {
	span.End(trace.WithTimestamp(timestamp))
}

// InjectTraceContext injects trace context into HTTP headers
func (i *HTTPInstrumentor) InjectTraceContext(ctx context.Context, headers http.Header) {
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(headers))
}

// ExtractTraceContext extracts trace context from HTTP headers
func (i *HTTPInstrumentor) ExtractTraceContext(ctx context.Context, headers http.Header) context.Context {
	return otel.GetTextMapPropagator().Extract(ctx, propagation.HeaderCarrier(headers))
}

// GetSpanFromContext gets the span from context
func (i *HTTPInstrumentor) GetSpanFromContext(ctx context.Context) trace.Span {
	return trace.SpanFromContext(ctx)
}

// AddSpanAttribute adds an attribute to the span
func (i *HTTPInstrumentor) AddSpanAttribute(span trace.Span, key string, value interface{}) {
	switch v := value.(type) {
	case string:
		span.SetAttributes(attribute.String(key, v))
	case int:
		span.SetAttributes(attribute.Int(key, v))
	case int64:
		span.SetAttributes(attribute.Int64(key, v))
	case float64:
		span.SetAttributes(attribute.Float64(key, v))
	case bool:
		span.SetAttributes(attribute.Bool(key, v))
	case []string:
		span.SetAttributes(attribute.StringSlice(key, v))
	default:
		span.SetAttributes(attribute.String(key, fmt.Sprintf("%v", v)))
	}
}

// AddSpanEvent adds an event to the span
func (i *HTTPInstrumentor) AddSpanEvent(span trace.Span, name string, attrs ...attribute.KeyValue) {
	span.AddEvent(name, trace.WithAttributes(attrs...))
}

// Helper functions

// isHeaderExcluded checks if a header should be excluded from tracing
func (i *HTTPInstrumentor) isHeaderExcluded(headerName string) bool {
	lowerName := strings.ToLower(headerName)
	for _, excluded := range i.config.ExcludeHeaders {
		if strings.ToLower(excluded) == lowerName {
			return true
		}
	}
	return false
}

// captureRequestBody captures the request body if enabled
func (i *HTTPInstrumentor) captureRequestBody(r *http.Request) (string, error) {
	if r.Body == nil {
		return "", nil
	}
	
	// Read body
	body, err := io.ReadAll(io.LimitReader(r.Body, i.config.MaxBodySize))
	if err != nil {
		return "", err
	}
	
	// Replace body with a new reader
	r.Body = io.NopCloser(bytes.NewBuffer(body))
	
	return string(body), nil
}

// getPortFromHost extracts port from host string
func getPortFromHost(host string) int {
	parts := strings.Split(host, ":")
	if len(parts) == 2 {
		if port, err := strconv.Atoi(parts[1]); err == nil {
			return port
		}
	}
	return 80 // default HTTP port
}

// ResponseWriter wrapper to capture response data
type TracingResponseWriter struct {
	http.ResponseWriter
	statusCode   int
	responseSize int64
	headers      http.Header
}

// NewTracingResponseWriter creates a new tracing response writer
func NewTracingResponseWriter(w http.ResponseWriter) *TracingResponseWriter {
	return &TracingResponseWriter{
		ResponseWriter: w,
		statusCode:     200,
		responseSize:   0,
		headers:        make(http.Header),
	}
}

// WriteHeader captures the status code
func (w *TracingResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	// Copy headers before writing
	for k, v := range w.ResponseWriter.Header() {
		w.headers[k] = v
	}
	w.ResponseWriter.WriteHeader(statusCode)
}

// Write captures the response size
func (w *TracingResponseWriter) Write(data []byte) (int, error) {
	size, err := w.ResponseWriter.Write(data)
	w.responseSize += int64(size)
	return size, err
}

// StatusCode returns the captured status code
func (w *TracingResponseWriter) StatusCode() int {
	return w.statusCode
}

// ResponseSize returns the captured response size
func (w *TracingResponseWriter) ResponseSize() int64 {
	return w.responseSize
}

// Headers returns the captured headers
func (w *TracingResponseWriter) Headers() http.Header {
	return w.headers
}