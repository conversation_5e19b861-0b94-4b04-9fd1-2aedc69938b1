package tracing

import (
	"context"
	"sync"
)

// Factory provides a factory for creating tracer instances
type Factory struct {
	mu      sync.RWMutex
	tracers map[string]Tracer
}

// NewFactory creates a new tracer factory
func NewFactory() TracerFactory {
	return &Factory{
		tracers: make(map[string]Tracer),
	}
}

// Create creates a new tracer instance
func (f *Factory) Create(cfg *Config) (Tracer, error) {
	return f.CreateWithName(cfg.ServiceName, cfg)
}

// CreateWithName creates a new tracer instance with a specific name
func (f *Factory) CreateWithName(name string, cfg *Config) (Tracer, error) {
	f.mu.Lock()
	defer f.mu.Unlock()
	
	// Check if tracer already exists
	if tracer, exists := f.tracers[name]; exists {
		return tracer, nil
	}
	
	// Create new tracer
	tracer, err := NewTracer(cfg)
	if err != nil {
		return nil, err
	}
	
	// Initialize tracer
	if err := tracer.Initialize(cfg); err != nil {
		return nil, err
	}
	
	// Store tracer
	f.tracers[name] = tracer
	
	return tracer, nil
}

// Get retrieves an existing tracer by name
func (f *Factory) Get(name string) (Tracer, bool) {
	f.mu.RLock()
	defer f.mu.RUnlock()
	
	tracer, exists := f.tracers[name]
	return tracer, exists
}

// GetOrCreate retrieves an existing tracer or creates a new one
func (f *Factory) GetOrCreate(name string, cfg *Config) (Tracer, error) {
	// Try to get existing tracer first
	if tracer, exists := f.Get(name); exists {
		return tracer, nil
	}
	
	// Create new tracer
	return f.CreateWithName(name, cfg)
}

// Shutdown shuts down all tracers
func (f *Factory) Shutdown(ctx context.Context) error {
	f.mu.Lock()
	defer f.mu.Unlock()
	
	var lastErr error
	for name, tracer := range f.tracers {
		if err := tracer.Shutdown(ctx); err != nil {
			lastErr = err
			// Continue shutting down other tracers
		}
		delete(f.tracers, name)
	}
	
	return lastErr
}

// List returns all tracer names
func (f *Factory) List() []string {
	f.mu.RLock()
	defer f.mu.RUnlock()
	
	names := make([]string, 0, len(f.tracers))
	for name := range f.tracers {
		names = append(names, name)
	}
	
	return names
}

// Remove removes a tracer by name
func (f *Factory) Remove(name string) error {
	f.mu.Lock()
	defer f.mu.Unlock()
	
	if tracer, exists := f.tracers[name]; exists {
		if err := tracer.Shutdown(context.Background()); err != nil {
			return err
		}
		delete(f.tracers, name)
	}
	
	return nil
}

// Global factory instance
var globalFactory TracerFactory = NewFactory()

// SetGlobalFactory sets the global factory instance
func SetGlobalFactory(factory TracerFactory) {
	globalFactory = factory
}

// GetGlobalFactory returns the global factory instance
func GetGlobalFactory() TracerFactory {
	return globalFactory
}

// Global convenience functions

// CreateTracer creates a new tracer using the global factory
func CreateTracer(cfg *Config) (Tracer, error) {
	return globalFactory.Create(cfg)
}

// CreateTracerWithName creates a new tracer with a specific name using the global factory
func CreateTracerWithName(name string, cfg *Config) (Tracer, error) {
	return globalFactory.CreateWithName(name, cfg)
}

// GetTracer retrieves an existing tracer by name using the global factory
func GetTracer(name string) (Tracer, bool) {
	if factory, ok := globalFactory.(*Factory); ok {
		return factory.Get(name)
	}
	return nil, false
}

// GetOrCreateTracer retrieves an existing tracer or creates a new one using the global factory
func GetOrCreateTracer(name string, cfg *Config) (Tracer, error) {
	if factory, ok := globalFactory.(*Factory); ok {
		return factory.GetOrCreate(name, cfg)
	}
	return globalFactory.CreateWithName(name, cfg)
}

// ShutdownAllTracers shuts down all tracers using the global factory
func ShutdownAllTracers(ctx context.Context) error {
	if factory, ok := globalFactory.(*Factory); ok {
		return factory.Shutdown(ctx)
	}
	return nil
}

// Predefined configurations

// NewDevelopmentTracer creates a new tracer with development configuration
func NewDevelopmentTracer(serviceName string) (Tracer, error) {
	cfg := DevelopmentConfig()
	cfg.ServiceName = serviceName
	return CreateTracerWithName(serviceName, cfg)
}

// NewProductionTracer creates a new tracer with production configuration
func NewProductionTracer(serviceName string) (Tracer, error) {
	cfg := ProductionConfig()
	cfg.ServiceName = serviceName
	return CreateTracerWithName(serviceName, cfg)
}

// NewTestingTracer creates a new tracer with testing configuration
func NewTestingTracer(serviceName string) (Tracer, error) {
	cfg := TestingConfig()
	cfg.ServiceName = serviceName
	return CreateTracerWithName(serviceName, cfg)
}

// NewJaegerTracer creates a new tracer with Jaeger configuration
func NewJaegerTracer(serviceName, jaegerEndpoint string) (Tracer, error) {
	cfg := DefaultConfig()
	cfg.ServiceName = serviceName
	cfg.Enabled = true
	cfg.Exporter = JaegerExporter
	cfg.JaegerEndpoint = jaegerEndpoint
	return CreateTracerWithName(serviceName, cfg)
}

// NewOTLPTracer creates a new tracer with OTLP configuration
func NewOTLPTracer(serviceName, otlpEndpoint string) (Tracer, error) {
	cfg := DefaultConfig()
	cfg.ServiceName = serviceName
	cfg.Enabled = true
	cfg.Exporter = OTLPExporter
	cfg.OTLPEndpoint = otlpEndpoint
	return CreateTracerWithName(serviceName, cfg)
}

// NewConsoleTracer creates a new tracer with console output
func NewConsoleTracer(serviceName string) (Tracer, error) {
	cfg := DefaultConfig()
	cfg.ServiceName = serviceName
	cfg.Enabled = true
	cfg.Exporter = ConsoleExporter
	cfg.Debug = true
	return CreateTracerWithName(serviceName, cfg)
}

// NewNoOpTracer creates a new no-op tracer
func NewNoOpTracer(serviceName string) (Tracer, error) {
	cfg := DefaultConfig()
	cfg.ServiceName = serviceName
	cfg.Enabled = false
	cfg.Exporter = NoOpExporter
	return CreateTracerWithName(serviceName, cfg)
}

// Builder pattern for creating tracers

// TracerBuilder provides a builder pattern for creating tracers
type TracerBuilder struct {
	config *Config
}

// NewTracerBuilder creates a new tracer builder
func NewTracerBuilder(serviceName string) *TracerBuilder {
	return &TracerBuilder{
		config: &Config{
			ServiceName: serviceName,
		},
	}
}

// WithServiceVersion sets the service version
func (b *TracerBuilder) WithServiceVersion(version string) *TracerBuilder {
	b.config.ServiceVersion = version
	return b
}

// WithEnvironment sets the environment
func (b *TracerBuilder) WithEnvironment(env string) *TracerBuilder {
	b.config.Environment = env
	return b
}

// WithEnabled sets the enabled flag
func (b *TracerBuilder) WithEnabled(enabled bool) *TracerBuilder {
	b.config.Enabled = enabled
	return b
}

// WithDebug sets the debug flag
func (b *TracerBuilder) WithDebug(debug bool) *TracerBuilder {
	b.config.Debug = debug
	return b
}

// WithExporter sets the exporter type
func (b *TracerBuilder) WithExporter(exporter ExporterType) *TracerBuilder {
	b.config.Exporter = exporter
	return b
}

// WithSampling sets the sampling strategy
func (b *TracerBuilder) WithSampling(sampling SamplingStrategy) *TracerBuilder {
	b.config.Sampling = sampling
	return b
}

// WithSampleRate sets the sample rate
func (b *TracerBuilder) WithSampleRate(rate float64) *TracerBuilder {
	b.config.SampleRate = rate
	return b
}

// WithJaegerEndpoint sets the Jaeger endpoint
func (b *TracerBuilder) WithJaegerEndpoint(endpoint string) *TracerBuilder {
	b.config.JaegerEndpoint = endpoint
	return b
}

// WithOTLPEndpoint sets the OTLP endpoint
func (b *TracerBuilder) WithOTLPEndpoint(endpoint string) *TracerBuilder {
	b.config.OTLPEndpoint = endpoint
	return b
}

// WithResourceAttributes sets resource attributes
func (b *TracerBuilder) WithResourceAttributes(attrs map[string]string) *TracerBuilder {
	if b.config.ResourceAttributes == nil {
		b.config.ResourceAttributes = make(map[string]string)
	}
	for k, v := range attrs {
		b.config.ResourceAttributes[k] = v
	}
	return b
}

// WithPropagators sets propagators
func (b *TracerBuilder) WithPropagators(propagators ...string) *TracerBuilder {
	b.config.Propagators = propagators
	return b
}

// Build creates the tracer
func (b *TracerBuilder) Build() (Tracer, error) {
	// Set defaults if not specified
	if b.config.ServiceVersion == "" {
		b.config.ServiceVersion = "1.0.0"
	}
	if b.config.Environment == "" {
		b.config.Environment = "development"
	}
	if b.config.Exporter == "" {
		b.config.Exporter = JaegerExporter
	}
	if b.config.Sampling == "" {
		b.config.Sampling = TraceIDRatio
	}
	if b.config.SampleRate == 0 {
		b.config.SampleRate = 0.1
	}
	if len(b.config.Propagators) == 0 {
		b.config.Propagators = []string{"tracecontext", "baggage"}
	}
	
	// Validate configuration
	if err := b.config.Validate(); err != nil {
		return nil, err
	}
	
	// Create tracer
	return CreateTracerWithName(b.config.ServiceName, b.config)
}

// Convenience functions for common patterns

// MustCreateTracer creates a tracer and panics if it fails
func MustCreateTracer(cfg *Config) Tracer {
	tracer, err := CreateTracer(cfg)
	if err != nil {
		panic(err)
	}
	return tracer
}

// MustCreateTracerWithName creates a tracer with a name and panics if it fails
func MustCreateTracerWithName(name string, cfg *Config) Tracer {
	tracer, err := CreateTracerWithName(name, cfg)
	if err != nil {
		panic(err)
	}
	return tracer
}

// MustGetTracer gets a tracer and panics if it doesn't exist
func MustGetTracer(name string) Tracer {
	tracer, exists := GetTracer(name)
	if !exists {
		panic("tracer not found: " + name)
	}
	return tracer
}

// DefaultTracer returns the default tracer instance
func DefaultTracer() Tracer {
	tracer, exists := GetTracer("default")
	if !exists {
		// Create default tracer
		cfg := DefaultConfig()
		cfg.ServiceName = "default"
		tracer, err := CreateTracerWithName("default", cfg)
		if err != nil {
			// Return no-op tracer if creation fails
			return &otelTracer{enabled: false}
		}
		return tracer
	}
	return tracer
}

// SetDefaultTracer sets the default tracer
func SetDefaultTracer(tracer Tracer) {
	if factory, ok := globalFactory.(*Factory); ok {
		factory.mu.Lock()
		defer factory.mu.Unlock()
		factory.tracers["default"] = tracer
	}
}

// Initialize initializes the global tracing system
func Initialize(cfg *Config) error {
	if cfg == nil {
		cfg = LoadConfig()
	}
	
	// Create default tracer
	tracer, err := CreateTracerWithName("default", cfg)
	if err != nil {
		return err
	}
	
	// Set as default
	SetDefaultTracer(tracer)
	
	return nil
}

// InitializeFromEnv initializes tracing from environment variables
func InitializeFromEnv() error {
	cfg := LoadConfig()
	return Initialize(cfg)
}

// Shutdown shuts down the global tracing system
func Shutdown(ctx context.Context) error {
	return ShutdownAllTracers(ctx)
}