package metrics

import (
	"context"
	"testing"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/database"
)

// TestMetricsIntegration tests the integration of all metrics components
func TestMetricsIntegration(t *testing.T) {
	// Setup
	ctx := context.Background()
	
	// Create a mock database metrics
	dbMetrics := &database.ConnectionMetrics{}
	dbMetrics.IncrementConnectionsOpened()
	dbMetrics.IncrementQueriesExecuted()
	
	// Create metrics collector
	collectorConfig := DefaultCollectorConfig()
	collector := NewDefaultCollector(dbMetrics, collectorConfig)
	
	// Create business metrics
	businessConfig := DefaultBusinessMetricsConfig()
	businessMetrics := NewBusinessMetrics(collector, businessConfig)
	
	// Create performance metrics
	performanceConfig := DefaultPerformanceConfig()
	performanceMetrics := NewPerformanceMetrics(collector, performanceConfig)
	
	// Create alert manager
	alertConfig := DefaultAlertManagerConfig()
	alertManager := NewAlertManager(collector, alertConfig)
	
	// Create dashboard manager
	dashboardConfig := DefaultDashboardConfig()
	dashboardManager := NewDashboardManager(collector, businessMetrics, performanceMetrics, alertManager, dashboardConfig)
	
	// Test metrics collection
	t.Run("TestMetricsCollection", func(t *testing.T) {
		// Test counter
		collector.IncrementCounter("test_counter", map[string]string{"env": "test"}, 1)
		value := collector.GetCounter("test_counter", map[string]string{"env": "test"})
		if value != 1 {
			t.Errorf("Expected counter value 1, got %f", value)
		}
		
		// Test gauge
		collector.SetGauge("test_gauge", map[string]string{"env": "test"}, 42.5)
		gaugeValue := collector.GetGauge("test_gauge", map[string]string{"env": "test"})
		if gaugeValue != 42.5 {
			t.Errorf("Expected gauge value 42.5, got %f", gaugeValue)
		}
		
		// Test histogram
		collector.RecordHistogram("test_histogram", map[string]string{"env": "test"}, 100.0)
		histogramValues := collector.GetHistogram("test_histogram", map[string]string{"env": "test"})
		if len(histogramValues) != 1 || histogramValues[0] != 100.0 {
			t.Errorf("Expected histogram value [100.0], got %v", histogramValues)
		}
		
		// Test metrics collection
		metrics, err := collector.CollectMetrics(ctx)
		if err != nil {
			t.Errorf("Error collecting metrics: %v", err)
		}
		
		if len(metrics) == 0 {
			t.Error("Expected metrics to be collected, got none")
		}
	})
	
	// Test business metrics
	t.Run("TestBusinessMetrics", func(t *testing.T) {
		// Track user registration
		businessMetrics.TrackUserRegistration(ctx, "user123", "tenant456", "email")
		
		// Track API request
		businessMetrics.TrackAPIRequest(ctx, "/api/users", "GET", 200, 150*time.Millisecond, "tenant456")
		
		// Get KPIs
		kpis := businessMetrics.GetBusinessKPIs()
		if kpis.UserRegistrations != 1 {
			t.Errorf("Expected 1 user registration, got %d", kpis.UserRegistrations)
		}
		
		if kpis.APIRequests != 1 {
			t.Errorf("Expected 1 API request, got %d", kpis.APIRequests)
		}
	})
	
	// Test performance metrics
	t.Run("TestPerformanceMetrics", func(t *testing.T) {
		// Track system metrics
		performanceMetrics.TrackSystemMetrics(ctx)
		
		// Track request performance
		performanceMetrics.TrackRequestPerformance(ctx, "GET", "/api/test", 200, 100*time.Millisecond, 1024)
		
		// Track database performance
		performanceMetrics.TrackDatabasePerformance(ctx, "SELECT", "users", 50*time.Millisecond, 5)
		
		// Get performance stats
		stats := performanceMetrics.GetPerformanceStats()
		if stats.AverageRequestDuration == 0 {
			t.Error("Expected average request duration to be recorded")
		}
	})
	
	// Test alert manager
	t.Run("TestAlertManager", func(t *testing.T) {
		// Add a test alert rule
		rule := &AlertRule{
			ID:          "test_rule",
			Name:        "Test Rule",
			Description: "Test alert rule",
			MetricName:  "test_gauge",
			Condition:   "gt",
			Threshold:   40.0,
			Duration:    1 * time.Second,
			Severity:    SeverityWarning,
			Labels:      map[string]string{"env": "test"},
			Enabled:     true,
		}
		
		err := alertManager.AddRule(rule)
		if err != nil {
			t.Errorf("Error adding alert rule: %v", err)
		}
		
		// Evaluate rules
		err = alertManager.EvaluateRules(ctx)
		if err != nil {
			t.Errorf("Error evaluating rules: %v", err)
		}
		
		// Since our test gauge is 42.5 and threshold is 40.0, we should have an active alert
		activeAlerts := alertManager.GetActiveAlerts()
		if len(activeAlerts) != 1 {
			t.Errorf("Expected 1 active alert, got %d", len(activeAlerts))
		}
	})
	
	// Test dashboard manager
	t.Run("TestDashboardManager", func(t *testing.T) {
		// Create default dashboards
		err := dashboardManager.CreateDefaultDashboards()
		if err != nil {
			t.Errorf("Error creating default dashboards: %v", err)
		}
		
		// Get dashboard list
		dashboards := dashboardManager.GetDashboardList()
		if len(dashboards) != 2 {
			t.Errorf("Expected 2 default dashboards, got %d", len(dashboards))
		}
		
		// Get dashboard data
		timeRange := TimeRange{
			From: time.Now().Add(-1 * time.Hour),
			To:   time.Now(),
		}
		
		data, err := dashboardManager.GetDashboardData(ctx, "system-overview", timeRange)
		if err != nil {
			t.Errorf("Error getting dashboard data: %v", err)
		}
		
		if data.DashboardID != "system-overview" {
			t.Errorf("Expected dashboard ID 'system-overview', got '%s'", data.DashboardID)
		}
	})
	
	// Test collector health
	t.Run("TestCollectorHealth", func(t *testing.T) {
		if !collector.IsHealthy() {
			t.Error("Expected collector to be healthy")
		}
		
		stats := collector.GetStats()
		if stats.MetricsCollected == 0 {
			t.Error("Expected metrics to be collected")
		}
	})
}

// TestMetricsExport tests the export functionality
func TestMetricsExport(t *testing.T) {
	ctx := context.Background()
	
	// Create collector
	dbMetrics := &database.ConnectionMetrics{}
	collectorConfig := DefaultCollectorConfig()
	collector := NewDefaultCollector(dbMetrics, collectorConfig)
	
	// Add some test data
	collector.IncrementCounter("export_test_counter", map[string]string{"type": "test"}, 5)
	collector.SetGauge("export_test_gauge", map[string]string{"type": "test"}, 95.5)
	
	// Collect metrics
	metrics, err := collector.CollectMetrics(ctx)
	if err != nil {
		t.Errorf("Error collecting metrics: %v", err)
	}
	
	// Test export
	err = collector.ExportMetrics(ctx, metrics)
	if err != nil {
		t.Errorf("Error exporting metrics: %v", err)
	}
	
	// Verify export stats
	stats := collector.GetStats()
	if stats.MetricsExported == 0 {
		t.Error("Expected metrics to be exported")
	}
}

// TestBusinessMetricsExport tests business metrics export
func TestBusinessMetricsExport(t *testing.T) {
	ctx := context.Background()
	
	// Create business metrics
	collectorConfig := DefaultCollectorConfig()
	collector := NewDefaultCollector(nil, collectorConfig)
	
	businessConfig := DefaultBusinessMetricsConfig()
	businessMetrics := NewBusinessMetrics(collector, businessConfig)
	
	// Add some test data
	businessMetrics.TrackUserRegistration(ctx, "user1", "tenant1", "email")
	businessMetrics.TrackTenantCreation(ctx, "tenant1", "basic", "example.com")
	
	// Export business metrics
	metrics, err := businessMetrics.ExportBusinessMetrics(ctx)
	if err != nil {
		t.Errorf("Error exporting business metrics: %v", err)
	}
	
	if len(metrics) == 0 {
		t.Error("Expected business metrics to be exported")
	}
	
	// Verify specific metrics
	found := false
	for _, metric := range metrics {
		if metric.Name == "business_user_registrations" {
			found = true
			if metric.Value != 1 {
				t.Errorf("Expected user registration value 1, got %f", metric.Value)
			}
		}
	}
	
	if !found {
		t.Error("Expected to find business_user_registrations metric")
	}
}

// TestPerformanceMetricsExport tests performance metrics export
func TestPerformanceMetricsExport(t *testing.T) {
	ctx := context.Background()
	
	// Create performance metrics
	collectorConfig := DefaultCollectorConfig()
	collector := NewDefaultCollector(nil, collectorConfig)
	
	performanceConfig := DefaultPerformanceConfig()
	performanceMetrics := NewPerformanceMetrics(collector, performanceConfig)
	
	// Track some performance data
	performanceMetrics.TrackRequestPerformance(ctx, "GET", "/api/test", 200, 100*time.Millisecond, 1024)
	performanceMetrics.TrackSystemMetrics(ctx)
	
	// Export performance metrics
	metrics, err := performanceMetrics.ExportPerformanceMetrics(ctx)
	if err != nil {
		t.Errorf("Error exporting performance metrics: %v", err)
	}
	
	if len(metrics) == 0 {
		t.Error("Expected performance metrics to be exported")
	}
	
	// Verify specific metrics
	found := false
	for _, metric := range metrics {
		if metric.Name == "performance_throughput" {
			found = true
			break
		}
	}
	
	if !found {
		t.Error("Expected to find performance_throughput metric")
	}
}