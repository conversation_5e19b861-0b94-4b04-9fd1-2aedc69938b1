package metrics

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"
)

// DashboardManager manages dashboard data and visualization
type DashboardManager struct {
	mu                sync.RWMutex
	collector         MetricCollector
	businessMetrics   *BusinessMetrics
	performanceMetrics *PerformanceMetrics
	alertManager      *AlertManager
	
	// Dashboard configurations
	dashboards map[string]*Dashboard
	
	// Data aggregation
	aggregatedData map[string][]DataPoint
	
	// Configuration
	config DashboardConfig
}

// DashboardConfig holds configuration for the dashboard manager
type DashboardConfig struct {
	RefreshInterval    time.Duration `json:"refresh_interval"`
	DataRetention      time.Duration `json:"data_retention"`
	MaxDataPoints      int           `json:"max_data_points"`
	EnableRealTime     bool          `json:"enable_real_time"`
	EnableExport       bool          `json:"enable_export"`
	DefaultTimeRange   time.Duration `json:"default_time_range"`
}

// Dashboard represents a monitoring dashboard
type Dashboard struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Panels      []Panel     `json:"panels"`
	Layout      Layout      `json:"layout"`
	Filters     []Filter    `json:"filters"`
	TimeRange   TimeRange   `json:"time_range"`
	RefreshRate time.Duration `json:"refresh_rate"`
	CreatedAt   time.Time   `json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

// Panel represents a single dashboard panel
type Panel struct {
	ID          string            `json:"id"`
	Title       string            `json:"title"`
	Type        PanelType         `json:"type"`
	MetricQuery string            `json:"metric_query"`
	Visualization Visualization   `json:"visualization"`
	Position    Position          `json:"position"`
	Size        Size              `json:"size"`
	Options     map[string]interface{} `json:"options"`
}

// PanelType represents the type of panel
type PanelType string

const (
	PanelTypeGraph     PanelType = "graph"
	PanelTypeGauge     PanelType = "gauge"
	PanelTypeTable     PanelType = "table"
	PanelTypeCounter   PanelType = "counter"
	PanelTypeHeatmap   PanelType = "heatmap"
	PanelTypeText      PanelType = "text"
	PanelTypeAlert     PanelType = "alert"
)

// Visualization represents visualization settings
type Visualization struct {
	Type        string                 `json:"type"`
	Properties  map[string]interface{} `json:"properties"`
	Colors      []string               `json:"colors"`
	Thresholds  []Threshold            `json:"thresholds"`
}

// Threshold represents a threshold configuration
type Threshold struct {
	Value float64 `json:"value"`
	Color string  `json:"color"`
	Label string  `json:"label"`
}

// Position represents panel position
type Position struct {
	X int `json:"x"`
	Y int `json:"y"`
}

// Size represents panel size
type Size struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

// Layout represents dashboard layout
type Layout struct {
	Type    string `json:"type"`
	Columns int    `json:"columns"`
	Rows    int    `json:"rows"`
}

// Filter represents dashboard filter
type Filter struct {
	Name     string   `json:"name"`
	Type     string   `json:"type"`
	Values   []string `json:"values"`
	Selected []string `json:"selected"`
}

// TimeRange represents time range for dashboard
type TimeRange struct {
	From time.Time `json:"from"`
	To   time.Time `json:"to"`
}

// DataPoint represents a single data point
type DataPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Labels    map[string]string `json:"labels"`
}

// DashboardData represents data for a dashboard
type DashboardData struct {
	DashboardID string                    `json:"dashboard_id"`
	Panels      map[string][]DataPoint    `json:"panels"`
	Metadata    DashboardMetadata         `json:"metadata"`
	GeneratedAt time.Time                 `json:"generated_at"`
}

// DashboardMetadata represents metadata for dashboard
type DashboardMetadata struct {
	TimeRange      TimeRange         `json:"time_range"`
	DataPointCount int               `json:"data_point_count"`
	LastUpdate     time.Time         `json:"last_update"`
	Filters        map[string]string `json:"filters"`
}

// DefaultDashboardConfig returns default configuration
func DefaultDashboardConfig() DashboardConfig {
	return DashboardConfig{
		RefreshInterval:  30 * time.Second,
		DataRetention:    24 * time.Hour,
		MaxDataPoints:    1000,
		EnableRealTime:   true,
		EnableExport:     true,
		DefaultTimeRange: 1 * time.Hour,
	}
}

// NewDashboardManager creates a new dashboard manager
func NewDashboardManager(
	collector MetricCollector,
	businessMetrics *BusinessMetrics,
	performanceMetrics *PerformanceMetrics,
	alertManager *AlertManager,
	config DashboardConfig,
) *DashboardManager {
	return &DashboardManager{
		collector:          collector,
		businessMetrics:    businessMetrics,
		performanceMetrics: performanceMetrics,
		alertManager:       alertManager,
		config:            config,
		dashboards:        make(map[string]*Dashboard),
		aggregatedData:    make(map[string][]DataPoint),
	}
}

// CreateDashboard creates a new dashboard
func (dm *DashboardManager) CreateDashboard(dashboard *Dashboard) error {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	dashboard.CreatedAt = time.Now()
	dashboard.UpdatedAt = time.Now()
	
	dm.dashboards[dashboard.ID] = dashboard
	return nil
}

// GetDashboard retrieves a dashboard by ID
func (dm *DashboardManager) GetDashboard(dashboardID string) (*Dashboard, error) {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	
	dashboard, exists := dm.dashboards[dashboardID]
	if !exists {
		return nil, fmt.Errorf("dashboard with ID %s not found", dashboardID)
	}
	
	return dashboard, nil
}

// UpdateDashboard updates an existing dashboard
func (dm *DashboardManager) UpdateDashboard(dashboardID string, updates *Dashboard) error {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	dashboard, exists := dm.dashboards[dashboardID]
	if !exists {
		return fmt.Errorf("dashboard with ID %s not found", dashboardID)
	}
	
	// Update fields
	if updates.Name != "" {
		dashboard.Name = updates.Name
	}
	if updates.Description != "" {
		dashboard.Description = updates.Description
	}
	if updates.Panels != nil {
		dashboard.Panels = updates.Panels
	}
	if updates.Layout.Type != "" {
		dashboard.Layout = updates.Layout
	}
	if updates.Filters != nil {
		dashboard.Filters = updates.Filters
	}
	
	dashboard.UpdatedAt = time.Now()
	
	return nil
}

// DeleteDashboard deletes a dashboard
func (dm *DashboardManager) DeleteDashboard(dashboardID string) error {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	delete(dm.dashboards, dashboardID)
	delete(dm.aggregatedData, dashboardID)
	
	return nil
}

// GetDashboardData retrieves data for a dashboard
func (dm *DashboardManager) GetDashboardData(ctx context.Context, dashboardID string, timeRange TimeRange) (*DashboardData, error) {
	dashboard, err := dm.GetDashboard(dashboardID)
	if err != nil {
		return nil, err
	}
	
	data := &DashboardData{
		DashboardID: dashboardID,
		Panels:      make(map[string][]DataPoint),
		Metadata: DashboardMetadata{
			TimeRange:  timeRange,
			LastUpdate: time.Now(),
			Filters:    make(map[string]string),
		},
		GeneratedAt: time.Now(),
	}
	
	// Collect data for each panel
	for _, panel := range dashboard.Panels {
		panelData, err := dm.getPanelData(ctx, panel, timeRange)
		if err != nil {
			continue // Skip panel on error
		}
		data.Panels[panel.ID] = panelData
		data.Metadata.DataPointCount += len(panelData)
	}
	
	return data, nil
}

// getPanelData retrieves data for a specific panel
func (dm *DashboardManager) getPanelData(ctx context.Context, panel Panel, timeRange TimeRange) ([]DataPoint, error) {
	switch panel.Type {
	case PanelTypeGraph:
		return dm.getGraphData(panel.MetricQuery, timeRange)
	case PanelTypeGauge:
		return dm.getGaugeData(panel.MetricQuery, timeRange)
	case PanelTypeTable:
		return dm.getTableData(panel.MetricQuery, timeRange)
	case PanelTypeCounter:
		return dm.getCounterData(panel.MetricQuery, timeRange)
	case PanelTypeAlert:
		return dm.getAlertData(timeRange)
	default:
		return nil, fmt.Errorf("unsupported panel type: %s", panel.Type)
	}
}

// getGraphData retrieves data for graph panels
func (dm *DashboardManager) getGraphData(metricQuery string, timeRange TimeRange) ([]DataPoint, error) {
	// This would typically execute the metric query
	// For now, return sample data
	var dataPoints []DataPoint
	
	duration := timeRange.To.Sub(timeRange.From)
	interval := duration / 100 // 100 data points
	
	for i := 0; i < 100; i++ {
		timestamp := timeRange.From.Add(time.Duration(i) * interval)
		
		// Get metric value (placeholder implementation)
		value := dm.collector.GetGauge(metricQuery, map[string]string{})
		
		dataPoints = append(dataPoints, DataPoint{
			Timestamp: timestamp,
			Value:     value,
			Labels:    map[string]string{"metric": metricQuery},
		})
	}
	
	return dataPoints, nil
}

// getGaugeData retrieves data for gauge panels
func (dm *DashboardManager) getGaugeData(metricQuery string, timeRange TimeRange) ([]DataPoint, error) {
	// Return current value
	value := dm.collector.GetGauge(metricQuery, map[string]string{})
	
	return []DataPoint{
		{
			Timestamp: time.Now(),
			Value:     value,
			Labels:    map[string]string{"metric": metricQuery},
		},
	}, nil
}

// getTableData retrieves data for table panels
func (dm *DashboardManager) getTableData(metricQuery string, timeRange TimeRange) ([]DataPoint, error) {
	// This would typically return tabular data
	// For now, return sample data
	return []DataPoint{
		{
			Timestamp: time.Now(),
			Value:     100,
			Labels:    map[string]string{"metric": metricQuery, "status": "active"},
		},
	}, nil
}

// getCounterData retrieves data for counter panels
func (dm *DashboardManager) getCounterData(metricQuery string, timeRange TimeRange) ([]DataPoint, error) {
	value := dm.collector.GetCounter(metricQuery, map[string]string{})
	
	return []DataPoint{
		{
			Timestamp: time.Now(),
			Value:     value,
			Labels:    map[string]string{"metric": metricQuery},
		},
	}, nil
}

// getAlertData retrieves data for alert panels
func (dm *DashboardManager) getAlertData(timeRange TimeRange) ([]DataPoint, error) {
	if dm.alertManager == nil {
		return nil, fmt.Errorf("alert manager not configured")
	}
	
	alerts := dm.alertManager.GetActiveAlerts()
	
	var dataPoints []DataPoint
	for _, alert := range alerts {
		dataPoints = append(dataPoints, DataPoint{
			Timestamp: alert.StartTime,
			Value:     float64(len(alerts)),
			Labels: map[string]string{
				"alert_id":   alert.ID,
				"severity":   string(alert.Severity),
				"rule_name":  alert.RuleName,
			},
		})
	}
	
	return dataPoints, nil
}

// GetDashboardList returns a list of all dashboards
func (dm *DashboardManager) GetDashboardList() []Dashboard {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	
	dashboards := make([]Dashboard, 0, len(dm.dashboards))
	for _, dashboard := range dm.dashboards {
		dashboards = append(dashboards, *dashboard)
	}
	
	// Sort by creation time
	sort.Slice(dashboards, func(i, j int) bool {
		return dashboards[i].CreatedAt.After(dashboards[j].CreatedAt)
	})
	
	return dashboards
}

// ExportDashboard exports dashboard configuration
func (dm *DashboardManager) ExportDashboard(dashboardID string) ([]byte, error) {
	if !dm.config.EnableExport {
		return nil, fmt.Errorf("dashboard export is disabled")
	}
	
	dashboard, err := dm.GetDashboard(dashboardID)
	if err != nil {
		return nil, err
	}
	
	return json.MarshalIndent(dashboard, "", "  ")
}

// ImportDashboard imports dashboard configuration
func (dm *DashboardManager) ImportDashboard(data []byte) error {
	var dashboard Dashboard
	if err := json.Unmarshal(data, &dashboard); err != nil {
		return err
	}
	
	return dm.CreateDashboard(&dashboard)
}

// StartDataCollection starts continuous data collection for dashboards
func (dm *DashboardManager) StartDataCollection(ctx context.Context) {
	if !dm.config.EnableRealTime {
		return
	}
	
	ticker := time.NewTicker(dm.config.RefreshInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			dm.collectDashboardData(ctx)
			dm.cleanupOldData()
		}
	}
}

// collectDashboardData collects data for all dashboards
func (dm *DashboardManager) collectDashboardData(ctx context.Context) {
	dm.mu.RLock()
	dashboards := make(map[string]*Dashboard)
	for id, dashboard := range dm.dashboards {
		dashboards[id] = dashboard
	}
	dm.mu.RUnlock()
	
	for dashboardID := range dashboards {
		timeRange := TimeRange{
			From: time.Now().Add(-dm.config.DefaultTimeRange),
			To:   time.Now(),
		}
		
		data, err := dm.GetDashboardData(ctx, dashboardID, timeRange)
		if err != nil {
			continue
		}
		
		// Store aggregated data
		dm.mu.Lock()
		for panelID, panelData := range data.Panels {
			key := fmt.Sprintf("%s_%s", dashboardID, panelID)
			dm.aggregatedData[key] = append(dm.aggregatedData[key], panelData...)
		}
		dm.mu.Unlock()
	}
}

// cleanupOldData removes old data points
func (dm *DashboardManager) cleanupOldData() {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	cutoff := time.Now().Add(-dm.config.DataRetention)
	
	for key, dataPoints := range dm.aggregatedData {
		filtered := make([]DataPoint, 0)
		
		for _, dp := range dataPoints {
			if dp.Timestamp.After(cutoff) {
				filtered = append(filtered, dp)
			}
		}
		
		// Also limit to max data points
		if len(filtered) > dm.config.MaxDataPoints {
			filtered = filtered[len(filtered)-dm.config.MaxDataPoints:]
		}
		
		dm.aggregatedData[key] = filtered
	}
}

// CreateDefaultDashboards creates a set of default dashboards
func (dm *DashboardManager) CreateDefaultDashboards() error {
	// System Overview Dashboard
	systemDashboard := &Dashboard{
		ID:          "system-overview",
		Name:        "System Overview",
		Description: "Overview of system performance and health",
		Panels: []Panel{
			{
				ID:          "cpu-usage",
				Title:       "CPU Usage",
				Type:        PanelTypeGauge,
				MetricQuery: "system_cpu_usage_percent",
				Visualization: Visualization{
					Type: "gauge",
					Properties: map[string]interface{}{
						"min": 0,
						"max": 100,
						"unit": "percent",
					},
					Thresholds: []Threshold{
						{Value: 50, Color: "green", Label: "Normal"},
						{Value: 80, Color: "yellow", Label: "Warning"},
						{Value: 95, Color: "red", Label: "Critical"},
					},
				},
				Position: Position{X: 0, Y: 0},
				Size:     Size{Width: 6, Height: 4},
			},
			{
				ID:          "memory-usage",
				Title:       "Memory Usage",
				Type:        PanelTypeGauge,
				MetricQuery: "system_memory_usage_percent",
				Visualization: Visualization{
					Type: "gauge",
					Properties: map[string]interface{}{
						"min": 0,
						"max": 100,
						"unit": "percent",
					},
					Thresholds: []Threshold{
						{Value: 50, Color: "green", Label: "Normal"},
						{Value: 80, Color: "yellow", Label: "Warning"},
						{Value: 95, Color: "red", Label: "Critical"},
					},
				},
				Position: Position{X: 6, Y: 0},
				Size:     Size{Width: 6, Height: 4},
			},
			{
				ID:          "request-rate",
				Title:       "Request Rate",
				Type:        PanelTypeGraph,
				MetricQuery: "http_throughput_requests_per_second",
				Visualization: Visualization{
					Type: "line",
					Properties: map[string]interface{}{
						"unit": "requests/sec",
					},
				},
				Position: Position{X: 0, Y: 4},
				Size:     Size{Width: 12, Height: 4},
			},
		},
		Layout: Layout{
			Type:    "grid",
			Columns: 12,
			Rows:    8,
		},
		TimeRange: TimeRange{
			From: time.Now().Add(-1 * time.Hour),
			To:   time.Now(),
		},
		RefreshRate: 30 * time.Second,
	}
	
	// Business Metrics Dashboard
	businessDashboard := &Dashboard{
		ID:          "business-metrics",
		Name:        "Business Metrics",
		Description: "Key business metrics and KPIs",
		Panels: []Panel{
			{
				ID:          "user-registrations",
				Title:       "User Registrations",
				Type:        PanelTypeCounter,
				MetricQuery: "business_user_registrations",
				Visualization: Visualization{
					Type: "counter",
					Properties: map[string]interface{}{
						"unit": "users",
					},
				},
				Position: Position{X: 0, Y: 0},
				Size:     Size{Width: 3, Height: 2},
			},
			{
				ID:          "tenant-creations",
				Title:       "Tenant Creations",
				Type:        PanelTypeCounter,
				MetricQuery: "business_tenant_creations",
				Visualization: Visualization{
					Type: "counter",
					Properties: map[string]interface{}{
						"unit": "tenants",
					},
				},
				Position: Position{X: 3, Y: 0},
				Size:     Size{Width: 3, Height: 2},
			},
			{
				ID:          "revenue",
				Title:       "Revenue Generated",
				Type:        PanelTypeCounter,
				MetricQuery: "business_revenue_generated",
				Visualization: Visualization{
					Type: "counter",
					Properties: map[string]interface{}{
						"unit": "currency",
					},
				},
				Position: Position{X: 6, Y: 0},
				Size:     Size{Width: 3, Height: 2},
			},
			{
				ID:          "active-users",
				Title:       "Active Users",
				Type:        PanelTypeCounter,
				MetricQuery: "business_active_users",
				Visualization: Visualization{
					Type: "counter",
					Properties: map[string]interface{}{
						"unit": "users",
					},
				},
				Position: Position{X: 9, Y: 0},
				Size:     Size{Width: 3, Height: 2},
			},
		},
		Layout: Layout{
			Type:    "grid",
			Columns: 12,
			Rows:    8,
		},
		TimeRange: TimeRange{
			From: time.Now().Add(-24 * time.Hour),
			To:   time.Now(),
		},
		RefreshRate: 60 * time.Second,
	}
	
	// Create dashboards
	if err := dm.CreateDashboard(systemDashboard); err != nil {
		return err
	}
	
	if err := dm.CreateDashboard(businessDashboard); err != nil {
		return err
	}
	
	return nil
}