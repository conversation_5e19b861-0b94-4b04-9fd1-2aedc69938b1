package metrics

import (
	"context"
	"runtime"
	"sync"
	"time"
)

// PerformanceMetrics tracks system and application performance metrics
type PerformanceMetrics struct {
	collector MetricCollector
	mu        sync.RWMutex
	
	// System metrics
	cpuUsage    float64
	memoryUsage float64
	goroutines  int64
	
	// Application metrics
	requestDuration     []time.Duration
	responseSize        []int64
	errorRates          map[string]float64
	throughput          float64
	
	// Database performance
	dbConnections       int64
	dbQueryDuration     []time.Duration
	dbSlowQueries       int64
	
	// Configuration
	config PerformanceConfig
}

// PerformanceConfig holds configuration for performance monitoring
type PerformanceConfig struct {
	EnableSystemMetrics    bool          `json:"enable_system_metrics"`
	EnableDatabaseMetrics  bool          `json:"enable_database_metrics"`
	EnableDetailedMetrics  bool          `json:"enable_detailed_metrics"`
	CollectionInterval     time.Duration `json:"collection_interval"`
	SlowQueryThreshold     time.Duration `json:"slow_query_threshold"`
	MemoryAlertThreshold   float64       `json:"memory_alert_threshold"`
	CPUAlertThreshold      float64       `json:"cpu_alert_threshold"`
}

// DefaultPerformanceConfig returns default configuration
func DefaultPerformanceConfig() PerformanceConfig {
	return PerformanceConfig{
		EnableSystemMetrics:    true,
		EnableDatabaseMetrics:  true,
		EnableDetailedMetrics:  true,
		CollectionInterval:     30 * time.Second,
		SlowQueryThreshold:     1 * time.Second,
		MemoryAlertThreshold:   80.0, // 80% memory usage
		CPUAlertThreshold:      80.0, // 80% CPU usage
	}
}

// NewPerformanceMetrics creates a new performance metrics tracker
func NewPerformanceMetrics(collector MetricCollector, config PerformanceConfig) *PerformanceMetrics {
	return &PerformanceMetrics{
		collector:        collector,
		config:          config,
		requestDuration: make([]time.Duration, 0),
		responseSize:    make([]int64, 0),
		errorRates:      make(map[string]float64),
		dbQueryDuration: make([]time.Duration, 0),
	}
}

// TrackSystemMetrics collects system performance metrics
func (pm *PerformanceMetrics) TrackSystemMetrics(ctx context.Context) {
	if !pm.config.EnableSystemMetrics {
		return
	}
	
	// Memory statistics
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	
	pm.mu.Lock()
	pm.memoryUsage = float64(memStats.Alloc) / float64(memStats.Sys) * 100
	pm.goroutines = int64(runtime.NumGoroutine())
	pm.mu.Unlock()
	
	labels := map[string]string{"component": "system"}
	
	// Memory metrics
	pm.collector.SetGauge("system_memory_usage_percent", labels, pm.memoryUsage)
	pm.collector.SetGauge("system_memory_alloc_bytes", labels, float64(memStats.Alloc))
	pm.collector.SetGauge("system_memory_sys_bytes", labels, float64(memStats.Sys))
	pm.collector.SetGauge("system_memory_heap_alloc_bytes", labels, float64(memStats.HeapAlloc))
	pm.collector.SetGauge("system_memory_heap_sys_bytes", labels, float64(memStats.HeapSys))
	
	// Goroutine metrics
	pm.collector.SetGauge("system_goroutines_count", labels, float64(pm.goroutines))
	
	// GC metrics
	pm.collector.SetGauge("system_gc_cycles_total", labels, float64(memStats.NumGC))
	pm.collector.SetGauge("system_gc_pause_total_ns", labels, float64(memStats.PauseTotalNs))
	
	// CPU metrics (placeholder - would require more sophisticated implementation)
	pm.collector.SetGauge("system_cpu_usage_percent", labels, pm.cpuUsage)
	
	// Alert on high resource usage
	if pm.memoryUsage > pm.config.MemoryAlertThreshold {
		pm.collector.IncrementCounter("system_memory_alerts_total", labels, 1)
	}
	
	if pm.cpuUsage > pm.config.CPUAlertThreshold {
		pm.collector.IncrementCounter("system_cpu_alerts_total", labels, 1)
	}
}

// TrackRequestPerformance tracks HTTP request performance
func (pm *PerformanceMetrics) TrackRequestPerformance(ctx context.Context, method string, endpoint string, statusCode int, duration time.Duration, responseSize int64) {
	pm.mu.Lock()
	pm.requestDuration = append(pm.requestDuration, duration)
	pm.responseSize = append(pm.responseSize, responseSize)
	pm.mu.Unlock()
	
	labels := map[string]string{
		"method":      method,
		"endpoint":    endpoint,
		"status_code": string(rune(statusCode)),
	}
	
	// Request duration metrics
	pm.collector.RecordHistogram("http_request_duration_ms", labels, float64(duration.Milliseconds()))
	pm.collector.RecordHistogram("http_response_size_bytes", labels, float64(responseSize))
	
	// Throughput calculation
	pm.calculateThroughput()
	pm.collector.SetGauge("http_throughput_requests_per_second", map[string]string{"component": "http"}, pm.throughput)
	
	// Error rate tracking
	if statusCode >= 400 {
		errorKey := endpoint + "_" + method
		pm.mu.Lock()
		pm.errorRates[errorKey] = pm.errorRates[errorKey] + 1
		pm.mu.Unlock()
		
		errorLabels := map[string]string{
			"method":   method,
			"endpoint": endpoint,
		}
		pm.collector.IncrementCounter("http_errors_total", errorLabels, 1)
	}
}

// TrackDatabasePerformance tracks database operation performance
func (pm *PerformanceMetrics) TrackDatabasePerformance(ctx context.Context, operation string, table string, duration time.Duration, recordsAffected int64) {
	if !pm.config.EnableDatabaseMetrics {
		return
	}
	
	pm.mu.Lock()
	pm.dbQueryDuration = append(pm.dbQueryDuration, duration)
	
	// Track slow queries
	if duration > pm.config.SlowQueryThreshold {
		pm.dbSlowQueries++
	}
	pm.mu.Unlock()
	
	labels := map[string]string{
		"operation": operation,
		"table":     table,
	}
	
	pm.collector.RecordHistogram("database_query_duration_ms", labels, float64(duration.Milliseconds()))
	pm.collector.SetGauge("database_records_affected", labels, float64(recordsAffected))
	
	// Slow query tracking
	if duration > pm.config.SlowQueryThreshold {
		slowQueryLabels := map[string]string{
			"operation": operation,
			"table":     table,
			"threshold": pm.config.SlowQueryThreshold.String(),
		}
		pm.collector.IncrementCounter("database_slow_queries_total", slowQueryLabels, 1)
	}
}

// TrackCachePerformance tracks cache operation performance
func (pm *PerformanceMetrics) TrackCachePerformance(ctx context.Context, operation string, cacheType string, hitRate float64, duration time.Duration) {
	labels := map[string]string{
		"operation":  operation,
		"cache_type": cacheType,
	}
	
	pm.collector.RecordHistogram("cache_operation_duration_ms", labels, float64(duration.Milliseconds()))
	pm.collector.SetGauge("cache_hit_rate_percent", labels, hitRate)
	
	// Track cache effectiveness
	if hitRate < 50.0 { // Less than 50% hit rate
		pm.collector.IncrementCounter("cache_low_hit_rate_alerts_total", labels, 1)
	}
}

// TrackQueuePerformance tracks queue operation performance
func (pm *PerformanceMetrics) TrackQueuePerformance(ctx context.Context, queueName string, operation string, duration time.Duration, queueSize int64) {
	labels := map[string]string{
		"queue_name": queueName,
		"operation":  operation,
	}
	
	pm.collector.RecordHistogram("queue_operation_duration_ms", labels, float64(duration.Milliseconds()))
	pm.collector.SetGauge("queue_size", labels, float64(queueSize))
	
	// Alert on queue backup
	if queueSize > 1000 { // Threshold for queue backup
		pm.collector.IncrementCounter("queue_backup_alerts_total", labels, 1)
	}
}

// calculateThroughput calculates current throughput
func (pm *PerformanceMetrics) calculateThroughput() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	if len(pm.requestDuration) == 0 {
		pm.throughput = 0
		return
	}
	
	// Simple throughput calculation based on recent requests
	// In a real implementation, this would be more sophisticated
	recentRequests := len(pm.requestDuration)
	if recentRequests > 100 {
		recentRequests = 100 // Only consider last 100 requests
	}
	
	pm.throughput = float64(recentRequests) / pm.config.CollectionInterval.Seconds()
}

// GetPerformanceStats returns current performance statistics
func (pm *PerformanceMetrics) GetPerformanceStats() PerformanceStats {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	stats := PerformanceStats{
		CPUUsage:         pm.cpuUsage,
		MemoryUsage:      pm.memoryUsage,
		Goroutines:       pm.goroutines,
		Throughput:       pm.throughput,
		DatabaseConnections: pm.dbConnections,
		SlowQueries:      pm.dbSlowQueries,
		Timestamp:        time.Now(),
	}
	
	// Calculate average request duration
	if len(pm.requestDuration) > 0 {
		var totalDuration time.Duration
		for _, duration := range pm.requestDuration {
			totalDuration += duration
		}
		stats.AverageRequestDuration = totalDuration / time.Duration(len(pm.requestDuration))
	}
	
	// Calculate average response size
	if len(pm.responseSize) > 0 {
		var totalSize int64
		for _, size := range pm.responseSize {
			totalSize += size
		}
		stats.AverageResponseSize = totalSize / int64(len(pm.responseSize))
	}
	
	// Calculate database query average
	if len(pm.dbQueryDuration) > 0 {
		var totalDuration time.Duration
		for _, duration := range pm.dbQueryDuration {
			totalDuration += duration
		}
		stats.AverageDBQueryDuration = totalDuration / time.Duration(len(pm.dbQueryDuration))
	}
	
	return stats
}

// PerformanceStats represents performance statistics
type PerformanceStats struct {
	CPUUsage                float64       `json:"cpu_usage"`
	MemoryUsage             float64       `json:"memory_usage"`
	Goroutines              int64         `json:"goroutines"`
	Throughput              float64       `json:"throughput"`
	AverageRequestDuration  time.Duration `json:"average_request_duration"`
	AverageResponseSize     int64         `json:"average_response_size"`
	DatabaseConnections     int64         `json:"database_connections"`
	AverageDBQueryDuration  time.Duration `json:"average_db_query_duration"`
	SlowQueries             int64         `json:"slow_queries"`
	Timestamp               time.Time     `json:"timestamp"`
}

// StartPerformanceMonitoring starts continuous performance monitoring
func (pm *PerformanceMetrics) StartPerformanceMonitoring(ctx context.Context) {
	ticker := time.NewTicker(pm.config.CollectionInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			pm.TrackSystemMetrics(ctx)
			pm.cleanupOldData()
		}
	}
}

// cleanupOldData removes old performance data to prevent memory leaks
func (pm *PerformanceMetrics) cleanupOldData() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	// Keep only last 1000 entries for each metric
	maxEntries := 1000
	
	if len(pm.requestDuration) > maxEntries {
		pm.requestDuration = pm.requestDuration[len(pm.requestDuration)-maxEntries:]
	}
	
	if len(pm.responseSize) > maxEntries {
		pm.responseSize = pm.responseSize[len(pm.responseSize)-maxEntries:]
	}
	
	if len(pm.dbQueryDuration) > maxEntries {
		pm.dbQueryDuration = pm.dbQueryDuration[len(pm.dbQueryDuration)-maxEntries:]
	}
}

// GetPerformanceAlerts returns current performance alerts
func (pm *PerformanceMetrics) GetPerformanceAlerts() []PerformanceAlert {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	var alerts []PerformanceAlert
	
	// Memory usage alert
	if pm.memoryUsage > pm.config.MemoryAlertThreshold {
		alerts = append(alerts, PerformanceAlert{
			Type:        "memory_usage",
			Severity:    "warning",
			Message:     "High memory usage detected",
			CurrentValue: pm.memoryUsage,
			Threshold:   pm.config.MemoryAlertThreshold,
			Timestamp:   time.Now(),
		})
	}
	
	// CPU usage alert
	if pm.cpuUsage > pm.config.CPUAlertThreshold {
		alerts = append(alerts, PerformanceAlert{
			Type:        "cpu_usage",
			Severity:    "warning",
			Message:     "High CPU usage detected",
			CurrentValue: pm.cpuUsage,
			Threshold:   pm.config.CPUAlertThreshold,
			Timestamp:   time.Now(),
		})
	}
	
	// Slow queries alert
	if pm.dbSlowQueries > 10 { // More than 10 slow queries
		alerts = append(alerts, PerformanceAlert{
			Type:        "slow_queries",
			Severity:    "warning",
			Message:     "High number of slow database queries",
			CurrentValue: float64(pm.dbSlowQueries),
			Threshold:   10.0,
			Timestamp:   time.Now(),
		})
	}
	
	return alerts
}

// PerformanceAlert represents a performance alert
type PerformanceAlert struct {
	Type         string    `json:"type"`
	Severity     string    `json:"severity"`
	Message      string    `json:"message"`
	CurrentValue float64   `json:"current_value"`
	Threshold    float64   `json:"threshold"`
	Timestamp    time.Time `json:"timestamp"`
}

// ExportPerformanceMetrics exports performance metrics for external systems
func (pm *PerformanceMetrics) ExportPerformanceMetrics(ctx context.Context) ([]MetricData, error) {
	stats := pm.GetPerformanceStats()
	timestamp := time.Now()
	
	metrics := []MetricData{
		{
			Name:        "performance_cpu_usage",
			Type:        MetricTypeGauge,
			Value:       stats.CPUUsage,
			Labels:      map[string]string{"category": "performance"},
			Timestamp:   timestamp,
			Unit:        "percent",
			Description: "CPU usage percentage",
		},
		{
			Name:        "performance_memory_usage",
			Type:        MetricTypeGauge,
			Value:       stats.MemoryUsage,
			Labels:      map[string]string{"category": "performance"},
			Timestamp:   timestamp,
			Unit:        "percent",
			Description: "Memory usage percentage",
		},
		{
			Name:        "performance_goroutines",
			Type:        MetricTypeGauge,
			Value:       float64(stats.Goroutines),
			Labels:      map[string]string{"category": "performance"},
			Timestamp:   timestamp,
			Unit:        "count",
			Description: "Number of goroutines",
		},
		{
			Name:        "performance_throughput",
			Type:        MetricTypeGauge,
			Value:       stats.Throughput,
			Labels:      map[string]string{"category": "performance"},
			Timestamp:   timestamp,
			Unit:        "requests_per_second",
			Description: "HTTP requests per second",
		},
		{
			Name:        "performance_average_request_duration",
			Type:        MetricTypeGauge,
			Value:       float64(stats.AverageRequestDuration.Milliseconds()),
			Labels:      map[string]string{"category": "performance"},
			Timestamp:   timestamp,
			Unit:        "milliseconds",
			Description: "Average HTTP request duration",
		},
		{
			Name:        "performance_slow_queries",
			Type:        MetricTypeGauge,
			Value:       float64(stats.SlowQueries),
			Labels:      map[string]string{"category": "performance"},
			Timestamp:   timestamp,
			Unit:        "count",
			Description: "Number of slow database queries",
		},
	}
	
	return metrics, nil
}