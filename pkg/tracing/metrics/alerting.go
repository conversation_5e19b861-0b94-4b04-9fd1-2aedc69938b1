package metrics

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// AlertSeverity represents the severity level of an alert
type AlertSeverity string

const (
	SeverityInfo     AlertSeverity = "info"
	SeverityWarning  AlertSeverity = "warning"
	SeverityError    AlertSeverity = "error"
	SeverityCritical AlertSeverity = "critical"
)

// AlertStatus represents the status of an alert
type AlertStatus string

const (
	StatusActive    AlertStatus = "active"
	StatusResolved  AlertStatus = "resolved"
	StatusSuppressed AlertStatus = "suppressed"
)

// AlertRule defines the conditions for triggering an alert
type AlertRule struct {
	ID          string        `json:"id"`
	Name        string        `json:"name"`
	Description string        `json:"description"`
	MetricName  string        `json:"metric_name"`
	Condition   string        `json:"condition"` // "gt", "lt", "eq", "gte", "lte"
	Threshold   float64       `json:"threshold"`
	Duration    time.Duration `json:"duration"`
	Severity    AlertSeverity `json:"severity"`
	Labels      map[string]string `json:"labels"`
	Enabled     bool          `json:"enabled"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
}

// Alert represents an active or resolved alert
type Alert struct {
	ID           string            `json:"id"`
	RuleID       string            `json:"rule_id"`
	RuleName     string            `json:"rule_name"`
	MetricName   string            `json:"metric_name"`
	CurrentValue float64           `json:"current_value"`
	Threshold    float64           `json:"threshold"`
	Severity     AlertSeverity     `json:"severity"`
	Status       AlertStatus       `json:"status"`
	Labels       map[string]string `json:"labels"`
	Message      string            `json:"message"`
	StartTime    time.Time         `json:"start_time"`
	EndTime      *time.Time        `json:"end_time,omitempty"`
	Duration     time.Duration     `json:"duration"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// AlertManager manages alert rules and active alerts
type AlertManager struct {
	mu           sync.RWMutex
	rules        map[string]*AlertRule
	activeAlerts map[string]*Alert
	collector    MetricCollector
	
	// Alert history for SLA tracking
	alertHistory []Alert
	
	// Notification handlers
	notificationHandlers []NotificationHandler
	
	// Configuration
	config AlertManagerConfig
}

// AlertManagerConfig holds configuration for the alert manager
type AlertManagerConfig struct {
	EvaluationInterval time.Duration `json:"evaluation_interval"`
	MaxAlertHistory    int           `json:"max_alert_history"`
	DefaultSeverity    AlertSeverity `json:"default_severity"`
	EnableNotifications bool          `json:"enable_notifications"`
	AlertRetention     time.Duration `json:"alert_retention"`
}

// NotificationHandler interface for sending alert notifications
type NotificationHandler interface {
	SendNotification(ctx context.Context, alert *Alert) error
	GetHandlerType() string
}

// DefaultAlertManagerConfig returns default configuration
func DefaultAlertManagerConfig() AlertManagerConfig {
	return AlertManagerConfig{
		EvaluationInterval:  30 * time.Second,
		MaxAlertHistory:     1000,
		DefaultSeverity:     SeverityWarning,
		EnableNotifications: true,
		AlertRetention:      7 * 24 * time.Hour, // 7 days
	}
}

// NewAlertManager creates a new alert manager
func NewAlertManager(collector MetricCollector, config AlertManagerConfig) *AlertManager {
	return &AlertManager{
		rules:        make(map[string]*AlertRule),
		activeAlerts: make(map[string]*Alert),
		collector:    collector,
		config:       config,
		alertHistory: make([]Alert, 0),
		notificationHandlers: make([]NotificationHandler, 0),
	}
}

// AddRule adds a new alert rule
func (am *AlertManager) AddRule(rule *AlertRule) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	rule.CreatedAt = time.Now()
	rule.UpdatedAt = time.Now()
	
	am.rules[rule.ID] = rule
	return nil
}

// UpdateRule updates an existing alert rule
func (am *AlertManager) UpdateRule(ruleID string, updates *AlertRule) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	rule, exists := am.rules[ruleID]
	if !exists {
		return fmt.Errorf("rule with ID %s not found", ruleID)
	}
	
	// Update fields
	if updates.Name != "" {
		rule.Name = updates.Name
	}
	if updates.Description != "" {
		rule.Description = updates.Description
	}
	if updates.MetricName != "" {
		rule.MetricName = updates.MetricName
	}
	if updates.Condition != "" {
		rule.Condition = updates.Condition
	}
	if updates.Threshold != 0 {
		rule.Threshold = updates.Threshold
	}
	if updates.Duration != 0 {
		rule.Duration = updates.Duration
	}
	if updates.Severity != "" {
		rule.Severity = updates.Severity
	}
	if updates.Labels != nil {
		rule.Labels = updates.Labels
	}
	
	rule.UpdatedAt = time.Now()
	
	return nil
}

// DeleteRule removes an alert rule
func (am *AlertManager) DeleteRule(ruleID string) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	delete(am.rules, ruleID)
	return nil
}

// GetRule retrieves an alert rule
func (am *AlertManager) GetRule(ruleID string) (*AlertRule, error) {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	rule, exists := am.rules[ruleID]
	if !exists {
		return nil, fmt.Errorf("rule with ID %s not found", ruleID)
	}
	
	return rule, nil
}

// GetAllRules returns all alert rules
func (am *AlertManager) GetAllRules() []*AlertRule {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	rules := make([]*AlertRule, 0, len(am.rules))
	for _, rule := range am.rules {
		rules = append(rules, rule)
	}
	
	return rules
}

// EvaluateRules evaluates all alert rules against current metrics
func (am *AlertManager) EvaluateRules(ctx context.Context) error {
	am.mu.RLock()
	rules := make([]*AlertRule, 0, len(am.rules))
	for _, rule := range am.rules {
		if rule.Enabled {
			rules = append(rules, rule)
		}
	}
	am.mu.RUnlock()
	
	for _, rule := range rules {
		if err := am.evaluateRule(ctx, rule); err != nil {
			// Log error but continue with other rules
			continue
		}
	}
	
	return nil
}

// evaluateRule evaluates a single alert rule
func (am *AlertManager) evaluateRule(ctx context.Context, rule *AlertRule) error {
	// Get current metric value
	currentValue := am.getCurrentMetricValue(rule.MetricName, rule.Labels)
	
	// Check if condition is met
	conditionMet := am.evaluateCondition(rule.Condition, currentValue, rule.Threshold)
	
	alertID := fmt.Sprintf("%s_%s", rule.ID, am.generateAlertKey(rule.Labels))
	
	am.mu.Lock()
	defer am.mu.Unlock()
	
	existingAlert, exists := am.activeAlerts[alertID]
	
	if conditionMet {
		if !exists {
			// Create new alert
			alert := &Alert{
				ID:           alertID,
				RuleID:       rule.ID,
				RuleName:     rule.Name,
				MetricName:   rule.MetricName,
				CurrentValue: currentValue,
				Threshold:    rule.Threshold,
				Severity:     rule.Severity,
				Status:       StatusActive,
				Labels:       rule.Labels,
				Message:      fmt.Sprintf("Alert %s: %s is %s %.2f (threshold: %.2f)", rule.Name, rule.MetricName, rule.Condition, currentValue, rule.Threshold),
				StartTime:    time.Now(),
				UpdatedAt:    time.Now(),
			}
			
			am.activeAlerts[alertID] = alert
			am.sendNotification(ctx, alert)
			
		} else {
			// Update existing alert
			existingAlert.CurrentValue = currentValue
			existingAlert.UpdatedAt = time.Now()
			existingAlert.Duration = time.Since(existingAlert.StartTime)
		}
	} else {
		if exists {
			// Resolve alert
			existingAlert.Status = StatusResolved
			endTime := time.Now()
			existingAlert.EndTime = &endTime
			existingAlert.Duration = endTime.Sub(existingAlert.StartTime)
			existingAlert.UpdatedAt = endTime
			
			// Move to history
			am.alertHistory = append(am.alertHistory, *existingAlert)
			delete(am.activeAlerts, alertID)
			
			// Send resolution notification
			am.sendNotification(ctx, existingAlert)
		}
	}
	
	return nil
}

// getCurrentMetricValue retrieves the current value of a metric
func (am *AlertManager) getCurrentMetricValue(metricName string, labels map[string]string) float64 {
	// This would typically query the metrics collector
	// For now, return a sample value
	return am.collector.GetGauge(metricName, labels)
}

// evaluateCondition evaluates whether a condition is met
func (am *AlertManager) evaluateCondition(condition string, currentValue, threshold float64) bool {
	switch condition {
	case "gt":
		return currentValue > threshold
	case "gte":
		return currentValue >= threshold
	case "lt":
		return currentValue < threshold
	case "lte":
		return currentValue <= threshold
	case "eq":
		return currentValue == threshold
	default:
		return false
	}
}

// generateAlertKey generates a unique key for an alert based on labels
func (am *AlertManager) generateAlertKey(labels map[string]string) string {
	key := ""
	for k, v := range labels {
		key += fmt.Sprintf("%s:%s,", k, v)
	}
	return key
}

// sendNotification sends alert notifications to configured handlers
func (am *AlertManager) sendNotification(ctx context.Context, alert *Alert) {
	if !am.config.EnableNotifications {
		return
	}
	
	for _, handler := range am.notificationHandlers {
		go func(h NotificationHandler) {
			if err := h.SendNotification(ctx, alert); err != nil {
				// Log error but don't fail the alert
			}
		}(handler)
	}
}

// AddNotificationHandler adds a notification handler
func (am *AlertManager) AddNotificationHandler(handler NotificationHandler) {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	am.notificationHandlers = append(am.notificationHandlers, handler)
}

// GetActiveAlerts returns all active alerts
func (am *AlertManager) GetActiveAlerts() []*Alert {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	alerts := make([]*Alert, 0, len(am.activeAlerts))
	for _, alert := range am.activeAlerts {
		alerts = append(alerts, alert)
	}
	
	return alerts
}

// GetAlertHistory returns alert history
func (am *AlertManager) GetAlertHistory(limit int) []Alert {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	if limit <= 0 || limit > len(am.alertHistory) {
		limit = len(am.alertHistory)
	}
	
	// Return most recent alerts
	start := len(am.alertHistory) - limit
	return am.alertHistory[start:]
}

// StartAlertEvaluation starts continuous alert evaluation
func (am *AlertManager) StartAlertEvaluation(ctx context.Context) {
	ticker := time.NewTicker(am.config.EvaluationInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			am.EvaluateRules(ctx)
			am.cleanupHistory()
		}
	}
}

// cleanupHistory removes old alerts from history
func (am *AlertManager) cleanupHistory() {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	if len(am.alertHistory) > am.config.MaxAlertHistory {
		// Keep only the most recent alerts
		am.alertHistory = am.alertHistory[len(am.alertHistory)-am.config.MaxAlertHistory:]
	}
	
	// Remove alerts older than retention period
	cutoff := time.Now().Add(-am.config.AlertRetention)
	filteredHistory := make([]Alert, 0)
	
	for _, alert := range am.alertHistory {
		if alert.StartTime.After(cutoff) {
			filteredHistory = append(filteredHistory, alert)
		}
	}
	
	am.alertHistory = filteredHistory
}

// GetAlertingStats returns statistics about the alerting system
func (am *AlertManager) GetAlertingStats() AlertingStats {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	stats := AlertingStats{
		TotalRules:        len(am.rules),
		ActiveAlerts:      len(am.activeAlerts),
		TotalAlertHistory: len(am.alertHistory),
		Timestamp:         time.Now(),
	}
	
	// Count enabled rules
	for _, rule := range am.rules {
		if rule.Enabled {
			stats.EnabledRules++
		}
	}
	
	// Count alerts by severity
	stats.AlertsBySeverity = make(map[AlertSeverity]int)
	for _, alert := range am.activeAlerts {
		stats.AlertsBySeverity[alert.Severity]++
	}
	
	return stats
}

// AlertingStats represents statistics about the alerting system
type AlertingStats struct {
	TotalRules         int                      `json:"total_rules"`
	EnabledRules       int                      `json:"enabled_rules"`
	ActiveAlerts       int                      `json:"active_alerts"`
	TotalAlertHistory  int                      `json:"total_alert_history"`
	AlertsBySeverity   map[AlertSeverity]int    `json:"alerts_by_severity"`
	Timestamp          time.Time                `json:"timestamp"`
}

// EmailNotificationHandler sends email notifications
type EmailNotificationHandler struct {
	SMTPConfig SMTPConfig `json:"smtp_config"`
}

// SMTPConfig holds SMTP configuration
type SMTPConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	To       []string `json:"to"`
}

// SendNotification sends an email notification
func (e *EmailNotificationHandler) SendNotification(ctx context.Context, alert *Alert) error {
	// TODO: Implement actual email sending
	// For now, this is a placeholder
	return nil
}

// GetHandlerType returns the handler type
func (e *EmailNotificationHandler) GetHandlerType() string {
	return "email"
}

// WebhookNotificationHandler sends webhook notifications
type WebhookNotificationHandler struct {
	WebhookURL string            `json:"webhook_url"`
	Headers    map[string]string `json:"headers"`
}

// SendNotification sends a webhook notification
func (w *WebhookNotificationHandler) SendNotification(ctx context.Context, alert *Alert) error {
	// TODO: Implement actual webhook sending
	// For now, this is a placeholder
	return nil
}

// GetHandlerType returns the handler type
func (w *WebhookNotificationHandler) GetHandlerType() string {
	return "webhook"
}

// CreateDefaultAlertRules creates a set of default alert rules
func CreateDefaultAlertRules() []*AlertRule {
	return []*AlertRule{
		{
			ID:          "high_memory_usage",
			Name:        "High Memory Usage",
			Description: "Alert when memory usage exceeds 80%",
			MetricName:  "system_memory_usage_percent",
			Condition:   "gt",
			Threshold:   80.0,
			Duration:    2 * time.Minute,
			Severity:    SeverityWarning,
			Labels:      map[string]string{"component": "system"},
			Enabled:     true,
		},
		{
			ID:          "high_cpu_usage",
			Name:        "High CPU Usage",
			Description: "Alert when CPU usage exceeds 80%",
			MetricName:  "system_cpu_usage_percent",
			Condition:   "gt",
			Threshold:   80.0,
			Duration:    2 * time.Minute,
			Severity:    SeverityWarning,
			Labels:      map[string]string{"component": "system"},
			Enabled:     true,
		},
		{
			ID:          "high_error_rate",
			Name:        "High Error Rate",
			Description: "Alert when error rate exceeds 5%",
			MetricName:  "http_error_rate_percent",
			Condition:   "gt",
			Threshold:   5.0,
			Duration:    1 * time.Minute,
			Severity:    SeverityError,
			Labels:      map[string]string{"component": "http"},
			Enabled:     true,
		},
		{
			ID:          "slow_response_time",
			Name:        "Slow Response Time",
			Description: "Alert when average response time exceeds 1 second",
			MetricName:  "http_request_duration_ms",
			Condition:   "gt",
			Threshold:   1000.0,
			Duration:    5 * time.Minute,
			Severity:    SeverityWarning,
			Labels:      map[string]string{"component": "http"},
			Enabled:     true,
		},
		{
			ID:          "database_connection_failure",
			Name:        "Database Connection Failure",
			Description: "Alert when database connection fails",
			MetricName:  "database_connection_failures_total",
			Condition:   "gt",
			Threshold:   0.0,
			Duration:    30 * time.Second,
			Severity:    SeverityCritical,
			Labels:      map[string]string{"component": "database"},
			Enabled:     true,
		},
	}
}