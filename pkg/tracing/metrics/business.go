package metrics

import (
	"context"
	"sync"
	"time"
)

// BusinessMetrics tracks key business KPIs and metrics
type BusinessMetrics struct {
	collector MetricCollector
	mu        sync.RWMutex
	
	// Business metric counters
	userRegistrations   int64
	tenantCreations     int64
	websiteCreations    int64
	apiRequests         int64
	subscriptionChanges int64
	
	// Revenue metrics
	revenueGenerated  float64
	subscriptionValue float64
	
	// Engagement metrics
	activeUsers      int64
	sessionDuration  float64
	pageViews        int64
	
	// Configuration
	config BusinessMetricsConfig
}

// BusinessMetricsConfig holds configuration for business metrics
type BusinessMetricsConfig struct {
	TrackUserActivity    bool          `json:"track_user_activity"`
	TrackRevenue        bool          `json:"track_revenue"`
	TrackEngagement     bool          `json:"track_engagement"`
	AggregationInterval time.Duration `json:"aggregation_interval"`
	EnableTrending      bool          `json:"enable_trending"`
}

// DefaultBusinessMetricsConfig returns default configuration
func DefaultBusinessMetricsConfig() BusinessMetricsConfig {
	return BusinessMetricsConfig{
		TrackUserActivity:    true,
		TrackRevenue:        true,
		TrackEngagement:     true,
		AggregationInterval: 5 * time.Minute,
		EnableTrending:      true,
	}
}

// NewBusinessMetrics creates a new business metrics tracker
func NewBusinessMetrics(collector MetricCollector, config BusinessMetricsConfig) *BusinessMetrics {
	return &BusinessMetrics{
		collector: collector,
		config:    config,
	}
}

// User Registration Metrics
func (bm *BusinessMetrics) TrackUserRegistration(ctx context.Context, userID string, tenantID string, registrationMethod string) {
	bm.mu.Lock()
	bm.userRegistrations++
	bm.mu.Unlock()
	
	labels := map[string]string{
		"tenant_id":           tenantID,
		"registration_method": registrationMethod,
	}
	
	bm.collector.IncrementCounter("user_registrations_total", labels, 1)
	bm.collector.SetGauge("user_registrations_current", labels, float64(bm.userRegistrations))
	
	// Track daily active users
	if bm.config.TrackUserActivity {
		dailyLabels := map[string]string{
			"date":      time.Now().Format("2006-01-02"),
			"tenant_id": tenantID,
		}
		bm.collector.IncrementCounter("daily_active_users", dailyLabels, 1)
	}
}

// Tenant Creation Metrics
func (bm *BusinessMetrics) TrackTenantCreation(ctx context.Context, tenantID string, planType string, domain string) {
	bm.mu.Lock()
	bm.tenantCreations++
	bm.mu.Unlock()
	
	labels := map[string]string{
		"plan_type": planType,
		"domain":    domain,
	}
	
	bm.collector.IncrementCounter("tenant_creations_total", labels, 1)
	bm.collector.SetGauge("tenant_creations_current", labels, float64(bm.tenantCreations))
	
	// Track tenant growth rate
	if bm.config.EnableTrending {
		hourlyLabels := map[string]string{
			"hour":      time.Now().Format("2006-01-02-15"),
			"plan_type": planType,
		}
		bm.collector.IncrementCounter("hourly_tenant_growth", hourlyLabels, 1)
	}
}

// Website Creation Metrics
func (bm *BusinessMetrics) TrackWebsiteCreation(ctx context.Context, websiteID string, tenantID string, templateType string) {
	bm.mu.Lock()
	bm.websiteCreations++
	bm.mu.Unlock()
	
	labels := map[string]string{
		"tenant_id":     tenantID,
		"template_type": templateType,
	}
	
	bm.collector.IncrementCounter("website_creations_total", labels, 1)
	bm.collector.SetGauge("website_creations_current", labels, float64(bm.websiteCreations))
}

// API Request Metrics
func (bm *BusinessMetrics) TrackAPIRequest(ctx context.Context, endpoint string, method string, statusCode int, duration time.Duration, tenantID string) {
	bm.mu.Lock()
	bm.apiRequests++
	bm.mu.Unlock()
	
	labels := map[string]string{
		"endpoint":    endpoint,
		"method":      method,
		"status_code": string(rune(statusCode)),
		"tenant_id":   tenantID,
	}
	
	bm.collector.IncrementCounter("api_requests_total", labels, 1)
	bm.collector.RecordHistogram("api_request_duration_ms", labels, float64(duration.Milliseconds()))
	
	// Track error rates
	if statusCode >= 400 {
		errorLabels := map[string]string{
			"endpoint":  endpoint,
			"method":    method,
			"tenant_id": tenantID,
		}
		bm.collector.IncrementCounter("api_errors_total", errorLabels, 1)
	}
}

// Subscription Metrics
func (bm *BusinessMetrics) TrackSubscriptionChange(ctx context.Context, tenantID string, fromPlan string, toPlan string, amount float64) {
	bm.mu.Lock()
	bm.subscriptionChanges++
	if bm.config.TrackRevenue {
		bm.revenueGenerated += amount
	}
	bm.mu.Unlock()
	
	labels := map[string]string{
		"tenant_id": tenantID,
		"from_plan": fromPlan,
		"to_plan":   toPlan,
	}
	
	bm.collector.IncrementCounter("subscription_changes_total", labels, 1)
	
	if bm.config.TrackRevenue {
		revenueLabels := map[string]string{
			"tenant_id": tenantID,
			"plan":      toPlan,
		}
		bm.collector.IncrementCounter("revenue_generated_total", revenueLabels, amount)
		bm.collector.SetGauge("revenue_generated_current", revenueLabels, bm.revenueGenerated)
	}
}

// User Engagement Metrics
func (bm *BusinessMetrics) TrackUserSession(ctx context.Context, userID string, tenantID string, sessionDuration time.Duration) {
	if !bm.config.TrackEngagement {
		return
	}
	
	bm.mu.Lock()
	bm.activeUsers++
	bm.sessionDuration += sessionDuration.Seconds()
	bm.mu.Unlock()
	
	labels := map[string]string{
		"user_id":   userID,
		"tenant_id": tenantID,
	}
	
	bm.collector.RecordHistogram("user_session_duration_seconds", labels, sessionDuration.Seconds())
	bm.collector.IncrementCounter("user_sessions_total", labels, 1)
}

// Page View Metrics
func (bm *BusinessMetrics) TrackPageView(ctx context.Context, websiteID string, tenantID string, pagePath string, userAgent string) {
	if !bm.config.TrackEngagement {
		return
	}
	
	bm.mu.Lock()
	bm.pageViews++
	bm.mu.Unlock()
	
	labels := map[string]string{
		"website_id": websiteID,
		"tenant_id":  tenantID,
		"page_path":  pagePath,
	}
	
	bm.collector.IncrementCounter("page_views_total", labels, 1)
	bm.collector.SetGauge("page_views_current", labels, float64(bm.pageViews))
}

// Feature Usage Metrics
func (bm *BusinessMetrics) TrackFeatureUsage(ctx context.Context, featureName string, tenantID string, userID string, usage int) {
	labels := map[string]string{
		"feature":   featureName,
		"tenant_id": tenantID,
		"user_id":   userID,
	}
	
	bm.collector.IncrementCounter("feature_usage_total", labels, float64(usage))
}

// Content Metrics
func (bm *BusinessMetrics) TrackContentCreation(ctx context.Context, contentType string, tenantID string, websiteID string) {
	labels := map[string]string{
		"content_type": contentType,
		"tenant_id":    tenantID,
		"website_id":   websiteID,
	}
	
	bm.collector.IncrementCounter("content_created_total", labels, 1)
}

// GetBusinessKPIs returns current business KPIs
func (bm *BusinessMetrics) GetBusinessKPIs() BusinessKPIs {
	bm.mu.RLock()
	defer bm.mu.RUnlock()
	
	return BusinessKPIs{
		UserRegistrations:   bm.userRegistrations,
		TenantCreations:     bm.tenantCreations,
		WebsiteCreations:    bm.websiteCreations,
		APIRequests:         bm.apiRequests,
		SubscriptionChanges: bm.subscriptionChanges,
		RevenueGenerated:    bm.revenueGenerated,
		ActiveUsers:         bm.activeUsers,
		AverageSessionDuration: bm.sessionDuration / float64(bm.activeUsers),
		PageViews:           bm.pageViews,
		Timestamp:           time.Now(),
	}
}

// BusinessKPIs represents key business metrics
type BusinessKPIs struct {
	UserRegistrations      int64     `json:"user_registrations"`
	TenantCreations        int64     `json:"tenant_creations"`
	WebsiteCreations       int64     `json:"website_creations"`
	APIRequests            int64     `json:"api_requests"`
	SubscriptionChanges    int64     `json:"subscription_changes"`
	RevenueGenerated       float64   `json:"revenue_generated"`
	ActiveUsers            int64     `json:"active_users"`
	AverageSessionDuration float64   `json:"average_session_duration"`
	PageViews              int64     `json:"page_views"`
	Timestamp              time.Time `json:"timestamp"`
}

// GetTrendingMetrics returns trending analysis for business metrics
func (bm *BusinessMetrics) GetTrendingMetrics(ctx context.Context, period time.Duration) (*TrendingMetrics, error) {
	if !bm.config.EnableTrending {
		return nil, nil
	}
	
	// This would typically query historical data
	// For now, return current snapshot
	kpis := bm.GetBusinessKPIs()
	
	return &TrendingMetrics{
		Period:    period,
		KPIs:      kpis,
		Trends:    calculateTrends(kpis), // Placeholder for trend calculation
		Timestamp: time.Now(),
	}, nil
}

// TrendingMetrics represents trending analysis
type TrendingMetrics struct {
	Period    time.Duration          `json:"period"`
	KPIs      BusinessKPIs           `json:"kpis"`
	Trends    map[string]TrendData   `json:"trends"`
	Timestamp time.Time              `json:"timestamp"`
}

// TrendData represents trend information for a metric
type TrendData struct {
	CurrentValue  float64 `json:"current_value"`
	PreviousValue float64 `json:"previous_value"`
	ChangePercent float64 `json:"change_percent"`
	Direction     string  `json:"direction"` // "up", "down", "stable"
}

// calculateTrends is a placeholder for trend calculation logic
func calculateTrends(kpis BusinessKPIs) map[string]TrendData {
	// This would implement actual trend calculation
	// For now, return empty trends
	return map[string]TrendData{}
}

// ExportBusinessMetrics exports business metrics for external systems
func (bm *BusinessMetrics) ExportBusinessMetrics(ctx context.Context) ([]MetricData, error) {
	kpis := bm.GetBusinessKPIs()
	timestamp := time.Now()
	
	metrics := []MetricData{
		{
			Name:        "business_user_registrations",
			Type:        MetricTypeGauge,
			Value:       float64(kpis.UserRegistrations),
			Labels:      map[string]string{"category": "business"},
			Timestamp:   timestamp,
			Unit:        "count",
			Description: "Total number of user registrations",
		},
		{
			Name:        "business_tenant_creations",
			Type:        MetricTypeGauge,
			Value:       float64(kpis.TenantCreations),
			Labels:      map[string]string{"category": "business"},
			Timestamp:   timestamp,
			Unit:        "count",
			Description: "Total number of tenant creations",
		},
		{
			Name:        "business_website_creations",
			Type:        MetricTypeGauge,
			Value:       float64(kpis.WebsiteCreations),
			Labels:      map[string]string{"category": "business"},
			Timestamp:   timestamp,
			Unit:        "count",
			Description: "Total number of website creations",
		},
		{
			Name:        "business_api_requests",
			Type:        MetricTypeGauge,
			Value:       float64(kpis.APIRequests),
			Labels:      map[string]string{"category": "business"},
			Timestamp:   timestamp,
			Unit:        "count",
			Description: "Total number of API requests",
		},
		{
			Name:        "business_revenue_generated",
			Type:        MetricTypeGauge,
			Value:       kpis.RevenueGenerated,
			Labels:      map[string]string{"category": "business"},
			Timestamp:   timestamp,
			Unit:        "currency",
			Description: "Total revenue generated",
		},
		{
			Name:        "business_active_users",
			Type:        MetricTypeGauge,
			Value:       float64(kpis.ActiveUsers),
			Labels:      map[string]string{"category": "business"},
			Timestamp:   timestamp,
			Unit:        "count",
			Description: "Number of active users",
		},
		{
			Name:        "business_average_session_duration",
			Type:        MetricTypeGauge,
			Value:       kpis.AverageSessionDuration,
			Labels:      map[string]string{"category": "business"},
			Timestamp:   timestamp,
			Unit:        "seconds",
			Description: "Average user session duration",
		},
		{
			Name:        "business_page_views",
			Type:        MetricTypeGauge,
			Value:       float64(kpis.PageViews),
			Labels:      map[string]string{"category": "business"},
			Timestamp:   timestamp,
			Unit:        "count",
			Description: "Total number of page views",
		},
	}
	
	return metrics, nil
}