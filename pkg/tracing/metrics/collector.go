package metrics

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/database"
)

// MetricType represents different types of metrics
type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeHistogram MetricType = "histogram"
)

// MetricData represents a single metric measurement
type MetricData struct {
	Name        string            `json:"name"`
	Type        MetricType        `json:"type"`
	Value       float64           `json:"value"`
	Labels      map[string]string `json:"labels"`
	Timestamp   time.Time         `json:"timestamp"`
	Unit        string            `json:"unit"`
	Description string            `json:"description"`
}

// MetricCollector interface defines the contract for metric collection
type MetricCollector interface {
	// Counter metrics
	IncrementCounter(name string, labels map[string]string, value float64)
	GetCounter(name string, labels map[string]string) float64

	// Gauge metrics
	SetGauge(name string, labels map[string]string, value float64)
	GetGauge(name string, labels map[string]string) float64

	// Histogram metrics
	RecordHistogram(name string, labels map[string]string, value float64)
	GetHistogram(name string, labels map[string]string) []float64

	// Collection and export
	CollectMetrics(ctx context.Context) ([]MetricData, error)
	ExportMetrics(ctx context.Context, metrics []MetricData) error
	
	// Health and status
	IsHealthy() bool
	GetStats() CollectorStats
}

// CollectorStats represents statistics about the collector itself
type CollectorStats struct {
	MetricsCollected   int64     `json:"metrics_collected"`
	MetricsExported    int64     `json:"metrics_exported"`
	MetricsErrors      int64     `json:"metrics_errors"`
	LastCollection     time.Time `json:"last_collection"`
	LastExport         time.Time `json:"last_export"`
	LastError          error     `json:"last_error,omitempty"`
	CollectionDuration time.Duration `json:"collection_duration"`
}

// DefaultCollector is the default implementation of MetricCollector
type DefaultCollector struct {
	mu        sync.RWMutex
	counters  map[string]float64
	gauges    map[string]float64
	histograms map[string][]float64
	stats     CollectorStats
	
	// Database metrics integration
	dbMetrics *database.ConnectionMetrics
	
	// Configuration
	config CollectorConfig
}

// CollectorConfig holds configuration for the metric collector
type CollectorConfig struct {
	CollectionInterval time.Duration `json:"collection_interval"`
	BatchSize         int           `json:"batch_size"`
	RetentionPeriod   time.Duration `json:"retention_period"`
	EnableAutoExport  bool          `json:"enable_auto_export"`
	MetricsPrefix     string        `json:"metrics_prefix"`
}

// DefaultCollectorConfig returns a default configuration
func DefaultCollectorConfig() CollectorConfig {
	return CollectorConfig{
		CollectionInterval: 60 * time.Second,
		BatchSize:         100,
		RetentionPeriod:   24 * time.Hour,
		EnableAutoExport:  true,
		MetricsPrefix:     "wn_api_v3",
	}
}

// NewDefaultCollector creates a new default metric collector
func NewDefaultCollector(dbMetrics *database.ConnectionMetrics, config CollectorConfig) *DefaultCollector {
	return &DefaultCollector{
		counters:   make(map[string]float64),
		gauges:     make(map[string]float64),
		histograms: make(map[string][]float64),
		dbMetrics:  dbMetrics,
		config:     config,
		stats: CollectorStats{
			LastCollection: time.Now(),
		},
	}
}

// IncrementCounter increments a counter metric
func (c *DefaultCollector) IncrementCounter(name string, labels map[string]string, value float64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	key := c.buildMetricKey(name, labels)
	c.counters[key] += value
	c.stats.MetricsCollected++
}

// GetCounter returns the current value of a counter metric
func (c *DefaultCollector) GetCounter(name string, labels map[string]string) float64 {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	key := c.buildMetricKey(name, labels)
	return c.counters[key]
}

// SetGauge sets a gauge metric value
func (c *DefaultCollector) SetGauge(name string, labels map[string]string, value float64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	key := c.buildMetricKey(name, labels)
	c.gauges[key] = value
	c.stats.MetricsCollected++
}

// GetGauge returns the current value of a gauge metric
func (c *DefaultCollector) GetGauge(name string, labels map[string]string) float64 {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	key := c.buildMetricKey(name, labels)
	return c.gauges[key]
}

// RecordHistogram records a value in a histogram metric
func (c *DefaultCollector) RecordHistogram(name string, labels map[string]string, value float64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	key := c.buildMetricKey(name, labels)
	if _, exists := c.histograms[key]; !exists {
		c.histograms[key] = make([]float64, 0)
	}
	c.histograms[key] = append(c.histograms[key], value)
	c.stats.MetricsCollected++
}

// GetHistogram returns the histogram values for a metric
func (c *DefaultCollector) GetHistogram(name string, labels map[string]string) []float64 {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	key := c.buildMetricKey(name, labels)
	if values, exists := c.histograms[key]; exists {
		// Return a copy to avoid race conditions
		result := make([]float64, len(values))
		copy(result, values)
		return result
	}
	return nil
}

// CollectMetrics gathers all current metrics
func (c *DefaultCollector) CollectMetrics(ctx context.Context) ([]MetricData, error) {
	start := time.Now()
	defer func() {
		c.mu.Lock()
		c.stats.LastCollection = time.Now()
		c.stats.CollectionDuration = time.Since(start)
		c.mu.Unlock()
	}()
	
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	var metrics []MetricData
	timestamp := time.Now()
	
	// Collect counters
	for key, value := range c.counters {
		name, labels := c.parseMetricKey(key)
		metrics = append(metrics, MetricData{
			Name:      name,
			Type:      MetricTypeCounter,
			Value:     value,
			Labels:    labels,
			Timestamp: timestamp,
			Unit:      "count",
		})
	}
	
	// Collect gauges
	for key, value := range c.gauges {
		name, labels := c.parseMetricKey(key)
		metrics = append(metrics, MetricData{
			Name:      name,
			Type:      MetricTypeGauge,
			Value:     value,
			Labels:    labels,
			Timestamp: timestamp,
			Unit:      "value",
		})
	}
	
	// Collect histograms (use average for now, can be extended for percentiles)
	for key, values := range c.histograms {
		if len(values) > 0 {
			name, labels := c.parseMetricKey(key)
			sum := 0.0
			for _, v := range values {
				sum += v
			}
			avg := sum / float64(len(values))
			
			metrics = append(metrics, MetricData{
				Name:      name,
				Type:      MetricTypeHistogram,
				Value:     avg,
				Labels:    labels,
				Timestamp: timestamp,
				Unit:      "duration_ms",
			})
		}
	}
	
	// Add database metrics if available
	if c.dbMetrics != nil {
		dbStats := c.dbMetrics.GetStats()
		baseLabels := map[string]string{"component": "database"}
		
		metrics = append(metrics, []MetricData{
			{
				Name:      c.config.MetricsPrefix + "_db_connections_opened",
				Type:      MetricTypeCounter,
				Value:     float64(dbStats.ConnectionsOpened),
				Labels:    baseLabels,
				Timestamp: timestamp,
				Unit:      "count",
			},
			{
				Name:      c.config.MetricsPrefix + "_db_connections_closed",
				Type:      MetricTypeCounter,
				Value:     float64(dbStats.ConnectionsClosed),
				Labels:    baseLabels,
				Timestamp: timestamp,
				Unit:      "count",
			},
			{
				Name:      c.config.MetricsPrefix + "_db_queries_executed",
				Type:      MetricTypeCounter,
				Value:     float64(dbStats.QueriesExecuted),
				Labels:    baseLabels,
				Timestamp: timestamp,
				Unit:      "count",
			},
			{
				Name:      c.config.MetricsPrefix + "_db_queries_failed",
				Type:      MetricTypeCounter,
				Value:     float64(dbStats.QueriesFailed),
				Labels:    baseLabels,
				Timestamp: timestamp,
				Unit:      "count",
			},
		}...)
	}
	
	return metrics, nil
}

// ExportMetrics exports collected metrics (placeholder for actual implementation)
func (c *DefaultCollector) ExportMetrics(ctx context.Context, metrics []MetricData) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.stats.MetricsExported += int64(len(metrics))
	c.stats.LastExport = time.Now()
	
	// TODO: Implement actual export logic (e.g., to Jaeger, Prometheus, etc.)
	// For now, this is a placeholder that could log or send to monitoring systems
	
	return nil
}

// IsHealthy returns whether the collector is healthy
func (c *DefaultCollector) IsHealthy() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	// Consider healthy if we've collected metrics recently
	return time.Since(c.stats.LastCollection) < 5*c.config.CollectionInterval
}

// GetStats returns collector statistics
func (c *DefaultCollector) GetStats() CollectorStats {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	return c.stats
}

// buildMetricKey creates a unique key for a metric with labels
func (c *DefaultCollector) buildMetricKey(name string, labels map[string]string) string {
	key := fmt.Sprintf("%s_%s", c.config.MetricsPrefix, name)
	for k, v := range labels {
		key += fmt.Sprintf("_%s_%s", k, v)
	}
	return key
}

// parseMetricKey extracts name and labels from a metric key
func (c *DefaultCollector) parseMetricKey(key string) (string, map[string]string) {
	// This is a simplified implementation
	// In a real scenario, you'd want more sophisticated parsing
	return key, map[string]string{}
}

// StartCollection starts the automatic metric collection process
func (c *DefaultCollector) StartCollection(ctx context.Context) {
	if !c.config.EnableAutoExport {
		return
	}
	
	ticker := time.NewTicker(c.config.CollectionInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			metrics, err := c.CollectMetrics(ctx)
			if err != nil {
				c.mu.Lock()
				c.stats.MetricsErrors++
				c.stats.LastError = err
				c.mu.Unlock()
				continue
			}
			
			if err := c.ExportMetrics(ctx, metrics); err != nil {
				c.mu.Lock()
				c.stats.MetricsErrors++
				c.stats.LastError = err
				c.mu.Unlock()
			}
		}
	}
}