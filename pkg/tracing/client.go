package tracing

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	"go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
	otelTrace "go.opentelemetry.io/otel/trace"
)

const (
	DefaultServiceName = "wn-api-v3"
	DefaultTracerName  = "wn-api-v3/tracer"
)

type JaegerClient struct {
	tracer      otelTrace.Tracer
	tracerName  string
	serviceName string
	provider    *trace.TracerProvider
}

type ClientConfig struct {
	ServiceName     string
	ServiceVersion  string
	Environment     string
	JaegerEndpoint  string
	SampleRate      float64
	BatchTimeout    time.Duration
	MaxBatchSize    int
	MaxQueueSize    int
	Enabled         bool
}

func NewJaegerClient(cfg ClientConfig) (*JaegerClient, error) {
	if !cfg.Enabled {
		return &JaegerClient{
			tracer:      otel.Tracer(DefaultTracerName),
			tracerName:  DefaultTracerName,
			serviceName: cfg.ServiceName,
		}, nil
	}

	if cfg.ServiceName == "" {
		cfg.ServiceName = DefaultServiceName
	}

	if cfg.JaegerEndpoint == "" {
		cfg.JaegerEndpoint = os.Getenv("JAEGER_ENDPOINT")
		if cfg.JaegerEndpoint == "" {
			cfg.JaegerEndpoint = "http://localhost:14268/api/traces"
		}
	}

	if cfg.SampleRate == 0 {
		cfg.SampleRate = 1.0
	}

	if cfg.BatchTimeout == 0 {
		cfg.BatchTimeout = 5 * time.Second
	}

	if cfg.MaxBatchSize == 0 {
		cfg.MaxBatchSize = 512
	}

	if cfg.MaxQueueSize == 0 {
		cfg.MaxQueueSize = 2048
	}

	exporter, err := jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(cfg.JaegerEndpoint)))
	if err != nil {
		return nil, fmt.Errorf("failed to create jaeger exporter: %w", err)
	}

	resource := resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(cfg.ServiceName),
		semconv.ServiceVersionKey.String(cfg.ServiceVersion),
		semconv.DeploymentEnvironmentKey.String(cfg.Environment),
	)

	provider := trace.NewTracerProvider(
		trace.WithBatcher(exporter,
			trace.WithBatchTimeout(cfg.BatchTimeout),
			trace.WithMaxExportBatchSize(cfg.MaxBatchSize),
			trace.WithMaxQueueSize(cfg.MaxQueueSize),
		),
		trace.WithResource(resource),
		trace.WithSampler(trace.TraceIDRatioBased(cfg.SampleRate)),
	)

	otel.SetTracerProvider(provider)

	propagator := propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	)
	otel.SetTextMapPropagator(propagator)

	tracerName := fmt.Sprintf("%s/tracer", cfg.ServiceName)

	client := &JaegerClient{
		tracer:      otel.Tracer(tracerName),
		tracerName:  tracerName,
		serviceName: cfg.ServiceName,
		provider:    provider,
	}

	log.Printf("Jaeger client initialized for service: %s", cfg.ServiceName)
	return client, nil
}

func (j *JaegerClient) StartSpan(ctx context.Context, spanName string, opts ...otelTrace.SpanStartOption) (context.Context, otelTrace.Span) {
	return j.tracer.Start(ctx, spanName, opts...)
}

func (j *JaegerClient) GetTracer() otelTrace.Tracer {
	return j.tracer
}

func (j *JaegerClient) GetTracerName() string {
	return j.tracerName
}

func (j *JaegerClient) GetServiceName() string {
	return j.serviceName
}

func (j *JaegerClient) Shutdown(ctx context.Context) error {
	if j.provider != nil {
		return j.provider.Shutdown(ctx)
	}
	return nil
}

func (j *JaegerClient) ForceFlush(ctx context.Context) error {
	if j.provider != nil {
		return j.provider.ForceFlush(ctx)
	}
	return nil
}

var globalClient *JaegerClient

func InitGlobalTracer(cfg ClientConfig) error {
	client, err := NewJaegerClient(cfg)
	if err != nil {
		return err
	}
	globalClient = client
	return nil
}

func GetGlobalTracer() otelTrace.Tracer {
	if globalClient == nil {
		return otel.Tracer(DefaultTracerName)
	}
	return globalClient.GetTracer()
}

func GetGlobalClient() *JaegerClient {
	return globalClient
}

func StartSpan(ctx context.Context, spanName string, opts ...otelTrace.SpanStartOption) (context.Context, otelTrace.Span) {
	if globalClient == nil {
		return otel.Tracer(DefaultTracerName).Start(ctx, spanName, opts...)
	}
	return globalClient.StartSpan(ctx, spanName, opts...)
}

func ShutdownGlobalTracer(ctx context.Context) error {
	if globalClient != nil {
		return globalClient.Shutdown(ctx)
	}
	return nil
}