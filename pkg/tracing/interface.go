package tracing

import (
	"context"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
)

// Tracer provides distributed tracing capabilities
type Tracer interface {
	// Initialize initializes the tracer with the given configuration
	Initialize(cfg *Config) error
	
	// Shutdown gracefully shuts down the tracer
	Shutdown(ctx context.Context) error
	
	// StartSpan creates a new span with the given name
	StartSpan(ctx context.Context, name string, opts ...SpanOption) (context.Context, Span)
	
	// StartSpanFromContext creates a new span from the current context
	StartSpanFromContext(ctx context.Context, name string, opts ...SpanOption) (context.Context, Span)
	
	// GetSpanFromContext retrieves the current span from context
	GetSpanFromContext(ctx context.Context) Span
	
	// InjectTraceContext injects trace context into headers
	InjectTraceContext(ctx context.Context, carrier TraceCarrier) error
	
	// ExtractTraceContext extracts trace context from headers
	ExtractTraceContext(ctx context.Context, carrier TraceCarrier) context.Context
	
	// IsEnabled returns true if tracing is enabled
	IsEnabled() bool
}

// Span represents a tracing span
type Span interface {
	// End ends the span
	End()
	
	// SetAttribute sets an attribute on the span
	SetAttribute(key string, value interface{})
	
	// SetAttributes sets multiple attributes on the span
	SetAttributes(attrs ...attribute.KeyValue)
	
	// SetStatus sets the status of the span
	SetStatus(code codes.Code, description string)
	
	// SetError marks the span as having an error
	SetError(err error)
	
	// AddEvent adds an event to the span
	AddEvent(name string, opts ...trace.EventOption)
	
	// AddEventWithAttributes adds an event with attributes to the span
	AddEventWithAttributes(name string, attrs ...attribute.KeyValue)
	
	// SetName sets the name of the span
	SetName(name string)
	
	// IsRecording returns true if the span is recording
	IsRecording() bool
	
	// SpanContext returns the span context
	SpanContext() trace.SpanContext
	
	// TraceID returns the trace ID
	TraceID() trace.TraceID
	
	// SpanID returns the span ID
	SpanID() trace.SpanID
}

// TraceCarrier defines the interface for trace context carriers
type TraceCarrier interface {
	// Get retrieves a value for a key
	Get(key string) string
	
	// Set sets a value for a key
	Set(key string, value string)
	
	// Keys returns all keys in the carrier
	Keys() []string
}

// SpanOption defines options for creating spans
type SpanOption func(*SpanOptions)

// SpanOptions holds configuration for creating spans
type SpanOptions struct {
	Kind           trace.SpanKind
	Attributes     []attribute.KeyValue
	StartTime      time.Time
	Links          []trace.Link
	StackTrace     bool
	ComponentName  string
	OperationName  string
	ParentSpanID   trace.SpanID
	TraceID        trace.TraceID
}

// HTTPCarrier implements TraceCarrier for HTTP headers
type HTTPCarrier struct {
	headers map[string][]string
}

// NewHTTPCarrier creates a new HTTP carrier
func NewHTTPCarrier(headers map[string][]string) *HTTPCarrier {
	return &HTTPCarrier{
		headers: headers,
	}
}

// Get retrieves a value for a key from HTTP headers
func (h *HTTPCarrier) Get(key string) string {
	if values, exists := h.headers[key]; exists && len(values) > 0 {
		return values[0]
	}
	return ""
}

// Set sets a value for a key in HTTP headers
func (h *HTTPCarrier) Set(key string, value string) {
	h.headers[key] = []string{value}
}

// Keys returns all keys in the HTTP headers
func (h *HTTPCarrier) Keys() []string {
	keys := make([]string, 0, len(h.headers))
	for k := range h.headers {
		keys = append(keys, k)
	}
	return keys
}

// Common span options

// WithSpanKind sets the span kind
func WithSpanKind(kind trace.SpanKind) SpanOption {
	return func(opts *SpanOptions) {
		opts.Kind = kind
	}
}

// WithAttributes sets attributes for the span
func WithAttributes(attrs ...attribute.KeyValue) SpanOption {
	return func(opts *SpanOptions) {
		opts.Attributes = append(opts.Attributes, attrs...)
	}
}

// WithStartTime sets the start time for the span
func WithStartTime(startTime time.Time) SpanOption {
	return func(opts *SpanOptions) {
		opts.StartTime = startTime
	}
}

// WithLinks sets links for the span
func WithLinks(links ...trace.Link) SpanOption {
	return func(opts *SpanOptions) {
		opts.Links = append(opts.Links, links...)
	}
}

// WithStackTrace enables stack trace capture
func WithStackTrace() SpanOption {
	return func(opts *SpanOptions) {
		opts.StackTrace = true
	}
}

// WithComponentName sets the component name
func WithComponentName(name string) SpanOption {
	return func(opts *SpanOptions) {
		opts.ComponentName = name
	}
}

// WithOperationName sets the operation name
func WithOperationName(name string) SpanOption {
	return func(opts *SpanOptions) {
		opts.OperationName = name
	}
}

// TracerFactory creates tracer instances
type TracerFactory interface {
	Create(cfg *Config) (Tracer, error)
	CreateWithName(name string, cfg *Config) (Tracer, error)
}

// TraceExporter defines the interface for trace exporters
type TraceExporter interface {
	// Export exports traces to the configured destination
	Export(ctx context.Context, spans []sdktrace.ReadOnlySpan) error
	
	// Shutdown shuts down the exporter
	Shutdown(ctx context.Context) error
}

// SamplingStrategy defines different sampling strategies
type SamplingStrategy string

const (
	// AlwaysSample always samples traces
	AlwaysSample SamplingStrategy = "always"
	
	// NeverSample never samples traces
	NeverSample SamplingStrategy = "never"
	
	// RateBased uses rate-based sampling
	RateBased SamplingStrategy = "rate"
	
	// TraceIDRatio uses trace ID ratio sampling
	TraceIDRatio SamplingStrategy = "trace_id_ratio"
)

// ExporterType defines different exporter types
type ExporterType string

const (
	// JaegerExporter uses Jaeger as the exporter
	JaegerExporter ExporterType = "jaeger"
	
	// OTLPExporter uses OTLP as the exporter
	OTLPExporter ExporterType = "otlp"
	
	// ConsoleExporter uses console output as the exporter
	ConsoleExporter ExporterType = "console"
	
	// NoOpExporter disables exporting
	NoOpExporter ExporterType = "noop"
)

// TraceContext holds trace context information
type TraceContext struct {
	TraceID    trace.TraceID
	SpanID     trace.SpanID
	TraceFlags trace.TraceFlags
	TraceState trace.TraceState
	Remote     bool
}

// NewTraceContext creates a new trace context
func NewTraceContext(traceID trace.TraceID, spanID trace.SpanID, traceFlags trace.TraceFlags, traceState trace.TraceState) *TraceContext {
	return &TraceContext{
		TraceID:    traceID,
		SpanID:     spanID,
		TraceFlags: traceFlags,
		TraceState: traceState,
		Remote:     false,
	}
}

// IsValid returns true if the trace context is valid
func (tc *TraceContext) IsValid() bool {
	return tc.TraceID.IsValid() && tc.SpanID.IsValid()
}

// String returns a string representation of the trace context
func (tc *TraceContext) String() string {
	return tc.TraceID.String() + "-" + tc.SpanID.String()
}

// TracingMetrics provides metrics for tracing operations
type TracingMetrics interface {
	// RecordSpanCreated records a span creation event
	RecordSpanCreated(spanName string, serviceName string)
	
	// RecordSpanDuration records span duration
	RecordSpanDuration(spanName string, duration time.Duration)
	
	// RecordSpanError records span error
	RecordSpanError(spanName string, errorType string)
	
	// RecordExportedSpans records exported spans count
	RecordExportedSpans(count int)
	
	// RecordSampledSpans records sampled spans count
	RecordSampledSpans(count int)
}