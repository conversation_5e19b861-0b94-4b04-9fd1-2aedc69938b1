package tracing

import (
	"os"
	"strconv"
	"time"
)

// Config holds the configuration for distributed tracing
type Config struct {
	// Service configuration
	ServiceName     string `json:"service_name"`
	ServiceVersion  string `json:"service_version"`
	ServiceInstance string `json:"service_instance"`
	Environment     string `json:"environment"`
	
	// Tracing configuration
	Enabled     bool             `json:"enabled"`
	Debug       bool             `json:"debug"`
	Endpoint    string           `json:"endpoint"`
	Exporter    ExporterType     `json:"exporter"`
	Sampling    SamplingStrategy `json:"sampling"`
	SampleRate  float64          `json:"sample_rate"`
	
	// Jaeger configuration
	JaegerEndpoint string `json:"jaeger_endpoint"`
	JaegerUser     string `json:"jaeger_user"`
	JaegerPassword string `json:"jaeger_password"`
	
	// OTLP configuration
	OTLPEndpoint string            `json:"otlp_endpoint"`
	OTLPHeaders  map[string]string `json:"otlp_headers"`
	OTLPInsecure bool              `json:"otlp_insecure"`
	
	// Batch configuration
	BatchTimeout       time.Duration `json:"batch_timeout"`
	BatchSize          int           `json:"batch_size"`
	MaxExportBatchSize int           `json:"max_export_batch_size"`
	MaxQueueSize       int           `json:"max_queue_size"`
	
	// Resource configuration
	ResourceAttributes map[string]string `json:"resource_attributes"`
	
	// Propagation configuration
	Propagators []string `json:"propagators"`
	
	// Advanced configuration
	EnableGRPCTrace  bool          `json:"enable_grpc_trace"`
	EnableHTTPTrace  bool          `json:"enable_http_trace"`
	EnableDBTrace    bool          `json:"enable_db_trace"`
	EnableCacheTrace bool          `json:"enable_cache_trace"`
	ShutdownTimeout  time.Duration `json:"shutdown_timeout"`
}

// LoadConfig loads tracing configuration from environment variables
func LoadConfig() *Config {
	return &Config{
		ServiceName:     getEnv("TRACING_SERVICE_NAME", "wn-api-v3"),
		ServiceVersion:  getEnv("TRACING_SERVICE_VERSION", "1.0.0"),
		ServiceInstance: getEnv("TRACING_SERVICE_INSTANCE", ""),
		Environment:     getEnv("TRACING_ENVIRONMENT", "development"),
		
		Enabled:     getEnvAsBool("TRACING_ENABLED", false),
		Debug:       getEnvAsBool("TRACING_DEBUG", false),
		Endpoint:    getEnv("TRACING_ENDPOINT", ""),
		Exporter:    ExporterType(getEnv("TRACING_EXPORTER", string(JaegerExporter))),
		Sampling:    SamplingStrategy(getEnv("TRACING_SAMPLING", string(TraceIDRatio))),
		SampleRate:  getEnvAsFloat64("TRACING_SAMPLE_RATE", 0.1),
		
		JaegerEndpoint: getEnv("JAEGER_ENDPOINT", "http://localhost:14268/api/traces"),
		JaegerUser:     getEnv("JAEGER_USER", ""),
		JaegerPassword: getEnv("JAEGER_PASSWORD", ""),
		
		OTLPEndpoint: getEnv("OTLP_ENDPOINT", "http://localhost:4318/v1/traces"),
		OTLPHeaders:  parseHeaders(getEnv("OTLP_HEADERS", "")),
		OTLPInsecure: getEnvAsBool("OTLP_INSECURE", true),
		
		BatchTimeout:       time.Duration(getEnvAsInt("TRACING_BATCH_TIMEOUT", 1000)) * time.Millisecond,
		BatchSize:          getEnvAsInt("TRACING_BATCH_SIZE", 100),
		MaxExportBatchSize: getEnvAsInt("TRACING_MAX_EXPORT_BATCH_SIZE", 512),
		MaxQueueSize:       getEnvAsInt("TRACING_MAX_QUEUE_SIZE", 2048),
		
		ResourceAttributes: parseAttributes(getEnv("TRACING_RESOURCE_ATTRIBUTES", "")),
		
		Propagators: parseSlice(getEnv("TRACING_PROPAGATORS", "tracecontext,baggage")),
		
		EnableGRPCTrace:  getEnvAsBool("TRACING_ENABLE_GRPC", true),
		EnableHTTPTrace:  getEnvAsBool("TRACING_ENABLE_HTTP", true),
		EnableDBTrace:    getEnvAsBool("TRACING_ENABLE_DB", true),
		EnableCacheTrace: getEnvAsBool("TRACING_ENABLE_CACHE", true),
		ShutdownTimeout:  time.Duration(getEnvAsInt("TRACING_SHUTDOWN_TIMEOUT", 30)) * time.Second,
	}
}

// DefaultConfig returns a default configuration
func DefaultConfig() *Config {
	return &Config{
		ServiceName:     "wn-api-v3",
		ServiceVersion:  "1.0.0",
		ServiceInstance: "",
		Environment:     "development",
		
		Enabled:     false,
		Debug:       false,
		Endpoint:    "",
		Exporter:    JaegerExporter,
		Sampling:    TraceIDRatio,
		SampleRate:  0.1,
		
		JaegerEndpoint: "http://localhost:14268/api/traces",
		JaegerUser:     "",
		JaegerPassword: "",
		
		OTLPEndpoint: "http://localhost:4318/v1/traces",
		OTLPHeaders:  make(map[string]string),
		OTLPInsecure: true,
		
		BatchTimeout:       1000 * time.Millisecond,
		BatchSize:          100,
		MaxExportBatchSize: 512,
		MaxQueueSize:       2048,
		
		ResourceAttributes: make(map[string]string),
		
		Propagators: []string{"tracecontext", "baggage"},
		
		EnableGRPCTrace:  true,
		EnableHTTPTrace:  true,
		EnableDBTrace:    true,
		EnableCacheTrace: true,
		ShutdownTimeout:  30 * time.Second,
	}
}

// DevelopmentConfig returns a development configuration
func DevelopmentConfig() *Config {
	cfg := DefaultConfig()
	cfg.Enabled = true
	cfg.Debug = true
	cfg.Sampling = AlwaysSample
	cfg.SampleRate = 1.0
	cfg.Environment = "development"
	return cfg
}

// ProductionConfig returns a production configuration
func ProductionConfig() *Config {
	cfg := DefaultConfig()
	cfg.Enabled = true
	cfg.Debug = false
	cfg.Sampling = TraceIDRatio
	cfg.SampleRate = 0.1
	cfg.Environment = "production"
	cfg.BatchTimeout = 500 * time.Millisecond
	cfg.BatchSize = 200
	cfg.MaxExportBatchSize = 1024
	cfg.MaxQueueSize = 4096
	return cfg
}

// TestingConfig returns a testing configuration
func TestingConfig() *Config {
	cfg := DefaultConfig()
	cfg.Enabled = true
	cfg.Debug = true
	cfg.Exporter = ConsoleExporter
	cfg.Sampling = AlwaysSample
	cfg.SampleRate = 1.0
	cfg.Environment = "testing"
	return cfg
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.ServiceName == "" {
		return ErrMissingServiceName
	}
	
	if c.Enabled {
		if c.SampleRate < 0.0 || c.SampleRate > 1.0 {
			return ErrInvalidSampleRate
		}
		
		if c.BatchSize <= 0 {
			return ErrInvalidBatchSize
		}
		
		if c.MaxExportBatchSize <= 0 {
			return ErrInvalidMaxExportBatchSize
		}
		
		if c.MaxQueueSize <= 0 {
			return ErrInvalidMaxQueueSize
		}
		
		if c.BatchTimeout <= 0 {
			return ErrInvalidBatchTimeout
		}
		
		if c.ShutdownTimeout <= 0 {
			return ErrInvalidShutdownTimeout
		}
	}
	
	return nil
}

// IsJaegerExporter returns true if the exporter is Jaeger
func (c *Config) IsJaegerExporter() bool {
	return c.Exporter == JaegerExporter
}

// IsOTLPExporter returns true if the exporter is OTLP
func (c *Config) IsOTLPExporter() bool {
	return c.Exporter == OTLPExporter
}

// IsConsoleExporter returns true if the exporter is console
func (c *Config) IsConsoleExporter() bool {
	return c.Exporter == ConsoleExporter
}

// IsNoOpExporter returns true if the exporter is no-op
func (c *Config) IsNoOpExporter() bool {
	return c.Exporter == NoOpExporter
}

// Helper functions

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	valueStr := getEnv(key, "")
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultValue
}

func getEnvAsFloat64(key string, defaultValue float64) float64 {
	valueStr := getEnv(key, "")
	if value, err := strconv.ParseFloat(valueStr, 64); err == nil {
		return value
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	valueStr := getEnv(key, "")
	if value, err := strconv.ParseBool(valueStr); err == nil {
		return value
	}
	return defaultValue
}

func parseHeaders(headersStr string) map[string]string {
	headers := make(map[string]string)
	if headersStr == "" {
		return headers
	}
	
	// Parse headers in format "key1=value1,key2=value2"
	pairs := parseSlice(headersStr)
	for _, pair := range pairs {
		if parts := parseKeyValue(pair); len(parts) == 2 {
			headers[parts[0]] = parts[1]
		}
	}
	
	return headers
}

func parseAttributes(attributesStr string) map[string]string {
	attributes := make(map[string]string)
	if attributesStr == "" {
		return attributes
	}
	
	// Parse attributes in format "key1=value1,key2=value2"
	pairs := parseSlice(attributesStr)
	for _, pair := range pairs {
		if parts := parseKeyValue(pair); len(parts) == 2 {
			attributes[parts[0]] = parts[1]
		}
	}
	
	return attributes
}

func parseSlice(str string) []string {
	if str == "" {
		return []string{}
	}
	
	parts := make([]string, 0)
	for _, part := range splitByComma(str) {
		if trimmed := trimSpace(part); trimmed != "" {
			parts = append(parts, trimmed)
		}
	}
	
	return parts
}

func parseKeyValue(pair string) []string {
	parts := make([]string, 0, 2)
	for _, part := range splitByEquals(pair) {
		if trimmed := trimSpace(part); trimmed != "" {
			parts = append(parts, trimmed)
		}
	}
	return parts
}

func splitByComma(str string) []string {
	result := make([]string, 0)
	current := ""
	
	for _, char := range str {
		if char == ',' {
			result = append(result, current)
			current = ""
		} else {
			current += string(char)
		}
	}
	
	if current != "" {
		result = append(result, current)
	}
	
	return result
}

func splitByEquals(str string) []string {
	result := make([]string, 0)
	current := ""
	
	for _, char := range str {
		if char == '=' {
			result = append(result, current)
			current = ""
		} else {
			current += string(char)
		}
	}
	
	if current != "" {
		result = append(result, current)
	}
	
	return result
}

func trimSpace(str string) string {
	// Simple trim implementation
	start := 0
	end := len(str)
	
	for start < end && isSpace(str[start]) {
		start++
	}
	
	for end > start && isSpace(str[end-1]) {
		end--
	}
	
	return str[start:end]
}

func isSpace(char byte) bool {
	return char == ' ' || char == '\t' || char == '\n' || char == '\r'
}