package monitoring

import (
	"context"
	"sync"
	"time"
)

// SimpleMonitor provides a basic monitoring implementation without external dependencies
type SimpleMonitor struct {
	healthStatus *HealthStatus
	metrics      map[string]interface{}
	startTime    time.Time
	mu           sync.RWMutex
}

// NewSimpleMonitor creates a new simple monitor
func NewSimpleMonitor() *SimpleMonitor {
	return &SimpleMonitor{
		healthStatus: &HealthStatus{
			Status:   "healthy",
			Database: make(map[string]bool),
			Cache:    make(map[string]bool),
			Queue:    make(map[string]bool),
			External: make(map[string]bool),
			Details:  make(map[string]interface{}),
		},
		metrics:   make(map[string]interface{}),
		startTime: time.Now(),
	}
}

// IsHealthy checks if the system is healthy
func (sm *SimpleMonitor) IsHealthy() bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	return sm.healthStatus.Status == "healthy"
}

// GetHealthStatus returns the current health status
func (sm *SimpleMonitor) GetHealthStatus() *HealthStatus {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	// Update uptime
	sm.healthStatus.Uptime = time.Since(sm.startTime)
	
	// Create a copy to avoid race conditions
	status := &HealthStatus{
		Status:   sm.healthStatus.Status,
		Database: make(map[string]bool),
		Cache:    make(map[string]bool),
		Queue:    make(map[string]bool),
		External: make(map[string]bool),
		Uptime:   sm.healthStatus.Uptime,
		Details:  make(map[string]interface{}),
	}
	
	// Copy maps
	for k, v := range sm.healthStatus.Database {
		status.Database[k] = v
	}
	for k, v := range sm.healthStatus.Cache {
		status.Cache[k] = v
	}
	for k, v := range sm.healthStatus.Queue {
		status.Queue[k] = v
	}
	for k, v := range sm.healthStatus.External {
		status.External[k] = v
	}
	for k, v := range sm.healthStatus.Details {
		status.Details[k] = v
	}
	
	return status
}

// IncrementCounter increments a counter metric
func (sm *SimpleMonitor) IncrementCounter(name string, labels map[string]string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	key := sm.buildMetricKey(name, labels)
	if val, exists := sm.metrics[key]; exists {
		if count, ok := val.(int64); ok {
			sm.metrics[key] = count + 1
		}
	} else {
		sm.metrics[key] = int64(1)
	}
}

// RecordDuration records a duration metric
func (sm *SimpleMonitor) RecordDuration(name string, duration time.Duration, labels map[string]string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	key := sm.buildMetricKey(name, labels)
	sm.metrics[key] = duration
}

// RecordGauge records a gauge metric
func (sm *SimpleMonitor) RecordGauge(name string, value float64, labels map[string]string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	key := sm.buildMetricKey(name, labels)
	sm.metrics[key] = value
}

// RecordHistogram records a histogram metric
func (sm *SimpleMonitor) RecordHistogram(name string, value float64, labels map[string]string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	key := sm.buildMetricKey(name, labels)
	// For simple implementation, just store the value
	sm.metrics[key] = value
}

// StartSpan starts a new tracing span
func (sm *SimpleMonitor) StartSpan(ctx context.Context, name string) (context.Context, Span) {
	span := &simpleSpan{
		name:      name,
		startTime: time.Now(),
		monitor:   sm,
	}
	
	return ctx, span
}

// RecordError records an error
func (sm *SimpleMonitor) RecordError(err error, labels map[string]string) {
	errorLabels := make(map[string]string)
	for k, v := range labels {
		errorLabels[k] = v
	}
	errorLabels["error"] = err.Error()
	
	sm.IncrementCounter("errors_total", errorLabels)
}

// RecordDBQuery records a database query
func (sm *SimpleMonitor) RecordDBQuery(query string, duration time.Duration, err error) {
	labels := map[string]string{
		"database":   "mysql",
		"query_type": "select",
	}
	
	if err != nil {
		labels["status"] = "error"
		sm.RecordError(err, labels)
	} else {
		labels["status"] = "success"
	}
	
	sm.IncrementCounter("database_queries_total", labels)
	sm.RecordDuration("database_query_duration_seconds", duration, map[string]string{
		"database":   "mysql",
		"query_type": "select",
	})
}

// GetDBStats returns database statistics
func (sm *SimpleMonitor) GetDBStats() *DBStats {
	return &DBStats{
		ConnectionsActive: 5,
		ConnectionsIdle:   10,
		ConnectionsTotal:  15,
		QueriesTotal:      1000,
		QueriesActive:     2,
		AverageQueryTime:  50 * time.Millisecond,
		SlowQueries:       5,
	}
}

// GetSystemMetrics returns system metrics
func (sm *SimpleMonitor) GetSystemMetrics() *SystemMetrics {
	return &SystemMetrics{
		CPUUsage:     0.0,
		MemoryUsage:  0.0,
		DiskUsage:    0.0,
		Goroutines:   0,
		HTTPRequests: 0,
	}
}

// Close closes the monitor
func (sm *SimpleMonitor) Close() error {
	return nil
}

// buildMetricKey builds a metric key from name and labels
func (sm *SimpleMonitor) buildMetricKey(name string, labels map[string]string) string {
	key := name
	for k, v := range labels {
		key += ":" + k + "=" + v
	}
	return key
}

// GetMetrics returns all collected metrics
func (sm *SimpleMonitor) GetMetrics() map[string]interface{} {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	metrics := make(map[string]interface{})
	for k, v := range sm.metrics {
		metrics[k] = v
	}
	
	return metrics
}

// simpleSpan implements the Span interface
type simpleSpan struct {
	name      string
	startTime time.Time
	monitor   *SimpleMonitor
}

func (s *simpleSpan) SetAttribute(key, value string) {
	// Could store attributes for later use
}

func (s *simpleSpan) SetStatus(status string) {
	// Could record status
}

func (s *simpleSpan) RecordError(err error) {
	s.monitor.RecordError(err, map[string]string{
		"span": s.name,
	})
}

func (s *simpleSpan) End() {
	duration := time.Since(s.startTime)
	s.monitor.RecordDuration("span_duration_seconds", duration, map[string]string{
		"span": s.name,
	})
}