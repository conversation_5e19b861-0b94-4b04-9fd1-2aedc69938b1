package monitoring

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// HTTPMiddleware provides HTTP monitoring middleware
type HTTPMiddleware struct {
	monitor Monitor
}

// NewHTTPMiddleware creates a new HTTP middleware
func NewHTTPMiddleware(monitor Monitor) *HTTPMiddleware {
	return &HTTPMiddleware{
		monitor: monitor,
	}
}

// GinMiddleware returns a Gin middleware for monitoring
func (hm *HTTPMiddleware) GinMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		// Start span
		ctx, span := hm.monitor.StartSpan(c.Request.Context(), "http_request")
		span.SetAttribute("method", c.Request.Method)
		span.SetAttribute("path", c.Request.URL.Path)
		span.SetAttribute("user_agent", c.Request.UserAgent())
		
		// Update request context
		c.Request = c.Request.WithContext(ctx)
		
		// Process request
		c.Next()
		
		// Calculate duration
		duration := time.Since(start)
		
		// Record metrics
		statusCode := c.Writer.Status()
		labels := map[string]string{
			"method":      c.Request.Method,
			"path":        c.Request.URL.Path,
			"status_code": strconv.Itoa(statusCode),
		}
		
		hm.monitor.IncrementCounter("http_requests_total", labels)
		hm.monitor.RecordDuration("http_request_duration_seconds", duration, map[string]string{
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
		})
		
		// Set span attributes
		span.SetAttribute("status_code", strconv.Itoa(statusCode))
		span.SetAttribute("duration_ms", strconv.FormatInt(duration.Nanoseconds()/1000000, 10))
		
		// Handle errors
		if len(c.Errors) > 0 {
			span.SetStatus("error")
			for _, err := range c.Errors {
				span.RecordError(err.Err)
				hm.monitor.RecordError(err.Err, labels)
			}
		} else if statusCode >= 400 {
			span.SetStatus("error")
		} else {
			span.SetStatus("success")
		}
		
		// End span
		span.End()
	}
}

// HTTPHandlerMiddleware returns a standard HTTP middleware
func (hm *HTTPMiddleware) HTTPHandlerMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		
		// Start span
		ctx, span := hm.monitor.StartSpan(r.Context(), "http_request")
		span.SetAttribute("method", r.Method)
		span.SetAttribute("path", r.URL.Path)
		span.SetAttribute("user_agent", r.UserAgent())
		
		// Update request context
		r = r.WithContext(ctx)
		
		// Wrap ResponseWriter to capture status code
		wrappedWriter := &responseWriterWrapper{
			ResponseWriter: w,
			statusCode:     200,
		}
		
		// Process request
		next.ServeHTTP(wrappedWriter, r)
		
		// Calculate duration
		duration := time.Since(start)
		
		// Record metrics
		statusCode := wrappedWriter.statusCode
		labels := map[string]string{
			"method":      r.Method,
			"path":        r.URL.Path,
			"status_code": strconv.Itoa(statusCode),
		}
		
		hm.monitor.IncrementCounter("http_requests_total", labels)
		hm.monitor.RecordDuration("http_request_duration_seconds", duration, map[string]string{
			"method": r.Method,
			"path":   r.URL.Path,
		})
		
		// Set span attributes
		span.SetAttribute("status_code", strconv.Itoa(statusCode))
		span.SetAttribute("duration_ms", strconv.FormatInt(duration.Nanoseconds()/1000000, 10))
		
		// Handle errors
		if statusCode >= 400 {
			span.SetStatus("error")
		} else {
			span.SetStatus("success")
		}
		
		// End span
		span.End()
	})
}

// responseWriterWrapper wraps http.ResponseWriter to capture status code
type responseWriterWrapper struct {
	http.ResponseWriter
	statusCode int
}

func (rww *responseWriterWrapper) WriteHeader(code int) {
	rww.statusCode = code
	rww.ResponseWriter.WriteHeader(code)
}

func (rww *responseWriterWrapper) Write(b []byte) (int, error) {
	if rww.statusCode == 0 {
		rww.statusCode = 200
	}
	return rww.ResponseWriter.Write(b)
}

// DatabaseMiddleware provides database monitoring middleware
type DatabaseMiddleware struct {
	monitor Monitor
}

// NewDatabaseMiddleware creates a new database middleware
func NewDatabaseMiddleware(monitor Monitor) *DatabaseMiddleware {
	return &DatabaseMiddleware{
		monitor: monitor,
	}
}

// WrapQuery wraps a database query with monitoring
func (dm *DatabaseMiddleware) WrapQuery(ctx context.Context, query string, fn func() error) error {
	start := time.Now()
	
	// Start span
	ctx, span := dm.monitor.StartSpan(ctx, "database_query")
	span.SetAttribute("query", query)
	
	// Execute query
	err := fn()
	
	// Calculate duration
	duration := time.Since(start)
	
	// Record metrics
	dm.monitor.RecordDBQuery(query, duration, err)
	
	// Set span attributes
	span.SetAttribute("duration_ms", strconv.FormatInt(duration.Nanoseconds()/1000000, 10))
	
	// Handle errors
	if err != nil {
		span.SetStatus("error")
		span.RecordError(err)
	} else {
		span.SetStatus("success")
	}
	
	// End span
	span.End()
	
	return err
}

// CacheMiddleware provides cache monitoring middleware
type CacheMiddleware struct {
	monitor Monitor
}

// NewCacheMiddleware creates a new cache middleware
func NewCacheMiddleware(monitor Monitor) *CacheMiddleware {
	return &CacheMiddleware{
		monitor: monitor,
	}
}

// WrapCacheOperation wraps a cache operation with monitoring
func (cm *CacheMiddleware) WrapCacheOperation(ctx context.Context, operation, key string, fn func() error) error {
	start := time.Now()
	
	// Start span
	ctx, span := cm.monitor.StartSpan(ctx, "cache_operation")
	span.SetAttribute("operation", operation)
	span.SetAttribute("key", key)
	
	// Execute operation
	err := fn()
	
	// Calculate duration
	duration := time.Since(start)
	
	// Record metrics
	labels := map[string]string{
		"operation": operation,
		"status":    "success",
	}
	
	if err != nil {
		labels["status"] = "error"
	}
	
	cm.monitor.IncrementCounter("cache_operations_total", labels)
	cm.monitor.RecordDuration("cache_operation_duration_seconds", duration, map[string]string{
		"operation": operation,
	})
	
	// Set span attributes
	span.SetAttribute("duration_ms", strconv.FormatInt(duration.Nanoseconds()/1000000, 10))
	
	// Handle errors
	if err != nil {
		span.SetStatus("error")
		span.RecordError(err)
	} else {
		span.SetStatus("success")
	}
	
	// End span
	span.End()
	
	return err
}

// QueueMiddleware provides queue monitoring middleware
type QueueMiddleware struct {
	monitor Monitor
}

// NewQueueMiddleware creates a new queue middleware
func NewQueueMiddleware(monitor Monitor) *QueueMiddleware {
	return &QueueMiddleware{
		monitor: monitor,
	}
}

// WrapQueueOperation wraps a queue operation with monitoring
func (qm *QueueMiddleware) WrapQueueOperation(ctx context.Context, operation, queueName string, fn func() error) error {
	start := time.Now()
	
	// Start span
	ctx, span := qm.monitor.StartSpan(ctx, "queue_operation")
	span.SetAttribute("operation", operation)
	span.SetAttribute("queue", queueName)
	
	// Execute operation
	err := fn()
	
	// Calculate duration
	duration := time.Since(start)
	
	// Record metrics
	labels := map[string]string{
		"operation": operation,
		"queue":     queueName,
		"status":    "success",
	}
	
	if err != nil {
		labels["status"] = "error"
	}
	
	qm.monitor.IncrementCounter("queue_operations_total", labels)
	qm.monitor.RecordDuration("queue_operation_duration_seconds", duration, map[string]string{
		"operation": operation,
		"queue":     queueName,
	})
	
	// Set span attributes
	span.SetAttribute("duration_ms", strconv.FormatInt(duration.Nanoseconds()/1000000, 10))
	
	// Handle errors
	if err != nil {
		span.SetStatus("error")
		span.RecordError(err)
	} else {
		span.SetStatus("success")
	}
	
	// End span
	span.End()
	
	return err
}