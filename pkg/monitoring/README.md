# Monitoring Package

This package provides comprehensive monitoring and observability capabilities for the blog API, including health checks, metrics collection, performance monitoring, and error tracking.

## Features

- **Health Checks**: Monitor database, external services, system resources
- **Metrics Collection**: Prometheus-compatible metrics for HTTP requests, database queries, cache operations
- **Performance Monitoring**: Request/response times, database query performance
- **Error Tracking**: Error rates, error details with context
- **Middleware Integration**: Easy integration with Gin, standard HTTP handlers
- **System Monitoring**: CPU, memory, disk usage, goroutines

## Quick Start

```go
package main

import (
    "database/sql"
    "log"
    "net/http"
    
    "github.com/gin-gonic/gin"
    "github.com/tranthanhloi/wn-api-v3/pkg/monitoring"
)

func main() {
    // Create monitoring service
    config := monitoring.DefaultConfig()
    db, _ := sql.Open("mysql", "connection_string")
    
    monitoringService, err := monitoring.NewMonitoringService(config, db)
    if err != nil {
        log.Fatal(err)
    }
    
    // Start monitoring
    if err := monitoringService.Start(); err != nil {
        log.Fatal(err)
    }
    defer monitoringService.Stop()
    
    // Setup Gin with monitoring middleware
    r := gin.Default()
    r.Use(monitoringService.HTTPMiddleware.GinMiddleware())
    
    // Health check endpoint
    r.GET("/health", func(c *gin.Context) {
        results := monitoringService.HealthService.CheckAll(c.Request.Context())
        c.JSON(http.StatusOK, results)
    })
    
    // Metrics endpoint
    r.GET("/metrics", func(c *gin.Context) {
        if prometheusMonitor, ok := monitoringService.Monitor.(monitoring.PrometheusMetrics); ok {
            metrics, err := prometheusMonitor.GetPrometheusMetrics()
            if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
            }
            c.Data(http.StatusOK, "text/plain", metrics)
        }
    })
    
    r.Run(":8080")
}
```

## Configuration

```yaml
type: prometheus
prometheus:
  enabled: true
  path: /metrics
  port: 9090
  namespace: blog_api
  subsystem: v3
health:
  enabled: true
  path: /health
  port: 8080
  timeout: 5s
  interval: 30s
  database: true
  memory: true
  disk: true
  external:
    - https://api.example.com/health
  max_memory_mb: 1024
  max_disk_pct: 90.0
```

## Health Checks

### Built-in Health Checkers

1. **Database Health Checker**: Tests database connectivity and query execution
2. **HTTP Health Checker**: Tests external HTTP endpoints
3. **Memory Health Checker**: Monitors memory usage
4. **Disk Health Checker**: Monitors disk usage
5. **Custom Health Checker**: Allows custom health check functions

### Usage

```go
// Create health service
healthService := monitoring.NewHealthService()

// Register database health checker
healthService.RegisterChecker(monitoring.NewDatabaseHealthChecker(db, 5*time.Second))

// Register HTTP health checker
healthService.RegisterChecker(monitoring.NewHTTPHealthChecker("https://api.example.com/health", 10*time.Second))

// Register custom health checker
healthService.RegisterChecker(monitoring.NewCustomHealthChecker("custom", func(ctx context.Context) error {
    // Custom health check logic
    return nil
}, 5*time.Second))

// Check all health checks
results := healthService.CheckAll(context.Background())
```

## Metrics

### HTTP Metrics

- `http_requests_total`: Total HTTP requests by method, path, status code
- `http_request_duration_seconds`: HTTP request duration histogram

### Database Metrics

- `database_queries_total`: Total database queries by type and status
- `database_query_duration_seconds`: Database query duration histogram
- `database_connections`: Current database connections by status

### System Metrics

- `system_cpu_usage`: CPU usage percentage
- `system_memory_usage`: Memory usage in MB
- `system_goroutines`: Number of goroutines

### Cache Metrics

- `cache_operations_total`: Total cache operations by type and status
- `cache_operation_duration_seconds`: Cache operation duration histogram

### Queue Metrics

- `queue_operations_total`: Total queue operations by type and status
- `queue_operation_duration_seconds`: Queue operation duration histogram

## Middleware

### HTTP Middleware

```go
// Gin middleware
r.Use(monitoringService.HTTPMiddleware.GinMiddleware())

// Standard HTTP middleware
handler := monitoringService.HTTPMiddleware.HTTPHandlerMiddleware(yourHandler)
```

### Database Middleware

```go
// Wrap database queries
err := monitoringService.DBMiddleware.WrapQuery(ctx, "SELECT * FROM users", func() error {
    return db.QueryContext(ctx, "SELECT * FROM users")
})
```

### Cache Middleware

```go
// Wrap cache operations
err := monitoringService.CacheMiddleware.WrapCacheOperation(ctx, "get", "user:123", func() error {
    return cache.Get("user:123")
})
```

### Queue Middleware

```go
// Wrap queue operations
err := monitoringService.QueueMiddleware.WrapQueueOperation(ctx, "enqueue", "email_queue", func() error {
    return queue.Enqueue("email_queue", message)
})
```

## Custom Metrics

```go
// Increment counter
monitor.IncrementCounter("custom_counter", map[string]string{
    "type": "email",
    "status": "sent",
})

// Record gauge
monitor.RecordGauge("queue_size", 100, map[string]string{
    "queue": "email_queue",
})

// Record histogram
monitor.RecordHistogram("processing_time", 0.5, map[string]string{
    "operation": "image_resize",
})

// Record duration
monitor.RecordDuration("task_duration", duration, map[string]string{
    "task": "backup",
})
```

## Error Tracking

```go
// Record error
monitor.RecordError(err, map[string]string{
    "component": "user_service",
    "operation": "create_user",
})

// Record error with span
ctx, span := monitor.StartSpan(ctx, "user_creation")
span.SetAttribute("user_id", "123")
if err != nil {
    span.RecordError(err)
    span.SetStatus("error")
}
span.End()
```

## Tracing

```go
// Start span
ctx, span := monitor.StartSpan(ctx, "process_order")
span.SetAttribute("order_id", "12345")
span.SetAttribute("user_id", "67890")

// Process order...

if err != nil {
    span.RecordError(err)
    span.SetStatus("error")
} else {
    span.SetStatus("success")
}

span.End()
```

## Prometheus Integration

The monitoring package provides native Prometheus integration:

```go
// Get Prometheus metrics
if prometheusMonitor, ok := monitor.(monitoring.PrometheusMetrics); ok {
    metrics, err := prometheusMonitor.GetPrometheusMetrics()
    if err != nil {
        // Handle error
    }
    // Serve metrics
}

// Register custom metric
prometheusMonitor.RegisterCustomMetric(
    "custom_metric_total",
    "Help text for custom metric",
    []string{"label1", "label2"},
)
```

## Best Practices

1. **Use Appropriate Metric Types**:
   - Counters for things that increase (requests, errors)
   - Gauges for things that go up and down (memory, connections)
   - Histograms for durations and sizes

2. **Keep Label Cardinality Low**:
   - Avoid high-cardinality labels (user IDs, timestamps)
   - Use consistent label names across metrics

3. **Monitor What Matters**:
   - Focus on user-facing metrics
   - Monitor critical business processes
   - Track error rates and response times

4. **Set Appropriate Timeouts**:
   - Health checks should have reasonable timeouts
   - Don't let monitoring affect application performance

5. **Use Middleware Consistently**:
   - Apply monitoring middleware to all HTTP handlers
   - Wrap database and cache operations
   - Monitor queue operations

## Integration with Existing Services

The monitoring package integrates with existing services:

- **Database**: Uses existing database connections for health checks
- **Cache**: Compatible with Redis and in-memory cache
- **Queue**: Works with Redis Queue and memory queue
- **HTTP**: Integrates with Gin and standard HTTP handlers
- **Tracing**: Compatible with OpenTelemetry (future enhancement)

## Performance Considerations

- Metrics collection has minimal overhead
- Health checks run in separate goroutines
- Prometheus metrics are collected efficiently
- No blocking operations in middleware
- Configurable collection intervals for system metrics