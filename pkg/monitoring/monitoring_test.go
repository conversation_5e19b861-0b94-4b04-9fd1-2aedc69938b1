package monitoring

import (
	"context"
	"testing"
	"time"
)

func TestSimpleMonitor(t *testing.T) {
	monitor := NewSimpleMonitor()

	// Test health status
	if !monitor.IsHealthy() {
		t.<PERSON>rror("Monitor should be healthy by default")
	}

	health := monitor.GetHealthStatus()
	if health.Status != "healthy" {
		t.<PERSON><PERSON>("Expected status 'healthy', got '%s'", health.Status)
	}

	// Test metrics
	monitor.IncrementCounter("test_counter", map[string]string{
		"type": "test",
	})

	monitor.RecordGauge("test_gauge", 42.0, map[string]string{
		"type": "test",
	})

	monitor.RecordDuration("test_duration", time.Second, map[string]string{
		"type": "test",
	})

	// Test spans
	ctx := context.Background()
	ctx, span := monitor.StartSpan(ctx, "test_span")
	span.SetAttribute("key", "value")
	span.SetStatus("success")
	span.End()

	// Test error recording
	monitor.RecordError(context.DeadlineExceeded, map[string]string{
		"component": "test",
	})

	// Test database query recording
	monitor.RecordDBQuery("SELECT 1", time.Millisecond*10, nil)

	// Get metrics
	metrics := monitor.GetMetrics()
	if len(metrics) == 0 {
		t.Error("Expected metrics to be recorded")
	}

	// Test system metrics
	sysMetrics := monitor.GetSystemMetrics()
	if sysMetrics == nil {
		t.Error("Expected system metrics to be returned")
	}

	// Test database stats
	dbStats := monitor.GetDBStats()
	if dbStats == nil {
		t.Error("Expected database stats to be returned")
	}
}

func TestHealthService(t *testing.T) {
	healthService := NewHealthService()

	// Test custom health checker
	healthService.RegisterChecker(NewCustomHealthChecker("test", func(ctx context.Context) error {
		return nil
	}, time.Second))

	// Test memory health checker
	healthService.RegisterChecker(NewMemoryHealthChecker(1024, time.Second))

	// Run health checks
	results := healthService.CheckAll(context.Background())
	
	if len(results) != 2 {
		t.Errorf("Expected 2 health check results, got %d", len(results))
	}

	for name, result := range results {
		if result.Status != "healthy" {
			t.Errorf("Health check '%s' should be healthy, got '%s'", name, result.Status)
		}
	}

	// Test overall health
	if !healthService.IsHealthy() {
		t.Error("Health service should be healthy")
	}
}

func TestMonitoringFactory(t *testing.T) {
	config := DefaultConfig()
	factory := NewMonitoringFactory(config)

	// Test monitor creation
	monitor, err := factory.CreateMonitor()
	if err != nil {
		t.Errorf("Failed to create monitor: %v", err)
	}

	if monitor == nil {
		t.Error("Monitor should not be nil")
	}

	// Test health service creation
	healthService := factory.CreateHealthService(nil)
	if healthService == nil {
		t.Error("Health service should not be nil")
	}

	// Test middleware creation
	httpMiddleware := factory.CreateHTTPMiddleware(monitor)
	if httpMiddleware == nil {
		t.Error("HTTP middleware should not be nil")
	}

	dbMiddleware := factory.CreateDatabaseMiddleware(monitor)
	if dbMiddleware == nil {
		t.Error("Database middleware should not be nil")
	}

	cacheMiddleware := factory.CreateCacheMiddleware(monitor)
	if cacheMiddleware == nil {
		t.Error("Cache middleware should not be nil")
	}

	queueMiddleware := factory.CreateQueueMiddleware(monitor)
	if queueMiddleware == nil {
		t.Error("Queue middleware should not be nil")
	}
}

func TestMonitoringService(t *testing.T) {
	config := DefaultConfig()
	
	// Test service creation
	service, err := NewMonitoringService(config, nil)
	if err != nil {
		t.Errorf("Failed to create monitoring service: %v", err)
	}

	if service == nil {
		t.Error("Monitoring service should not be nil")
	}

	// Test start/stop
	err = service.Start()
	if err != nil {
		t.Errorf("Failed to start monitoring service: %v", err)
	}

	err = service.Stop()
	if err != nil {
		t.Errorf("Failed to stop monitoring service: %v", err)
	}
}

func TestMiddleware(t *testing.T) {
	monitor := NewSimpleMonitor()
	
	// Test database middleware
	dbMiddleware := NewDatabaseMiddleware(monitor)
	err := dbMiddleware.WrapQuery(context.Background(), "SELECT 1", func() error {
		return nil
	})
	if err != nil {
		t.Errorf("Database middleware failed: %v", err)
	}

	// Test cache middleware
	cacheMiddleware := NewCacheMiddleware(monitor)
	err = cacheMiddleware.WrapCacheOperation(context.Background(), "get", "test_key", func() error {
		return nil
	})
	if err != nil {
		t.Errorf("Cache middleware failed: %v", err)
	}

	// Test queue middleware
	queueMiddleware := NewQueueMiddleware(monitor)
	err = queueMiddleware.WrapQueueOperation(context.Background(), "enqueue", "test_queue", func() error {
		return nil
	})
	if err != nil {
		t.Errorf("Queue middleware failed: %v", err)
	}
}

func TestNoOpMonitor(t *testing.T) {
	monitor := NewNoOpMonitor()

	// Test that all methods work without errors
	if !monitor.IsHealthy() {
		t.Error("NoOp monitor should always be healthy")
	}

	health := monitor.GetHealthStatus()
	if health.Status != "healthy" {
		t.Errorf("Expected status 'healthy', got '%s'", health.Status)
	}

	// Test all metric operations
	monitor.IncrementCounter("test", map[string]string{})
	monitor.RecordGauge("test", 1.0, map[string]string{})
	monitor.RecordDuration("test", time.Second, map[string]string{})
	monitor.RecordHistogram("test", 1.0, map[string]string{})
	monitor.RecordError(context.DeadlineExceeded, map[string]string{})
	monitor.RecordDBQuery("SELECT 1", time.Millisecond, nil)

	// Test span operations
	_, span := monitor.StartSpan(context.Background(), "test")
	span.SetAttribute("key", "value")
	span.SetStatus("success")
	span.RecordError(context.DeadlineExceeded)
	span.End()

	// Test stats
	dbStats := monitor.GetDBStats()
	if dbStats == nil {
		t.Error("Expected database stats to be returned")
	}

	sysMetrics := monitor.GetSystemMetrics()
	if sysMetrics == nil {
		t.Error("Expected system metrics to be returned")
	}

	// Test close
	err := monitor.Close()
	if err != nil {
		t.Errorf("NoOp monitor close failed: %v", err)
	}
}