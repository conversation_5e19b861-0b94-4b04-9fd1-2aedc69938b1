package monitoring

import (
	"context"
	"time"
)

// Monitor defines the interface for monitoring services
type Monitor interface {
	// Health checks
	IsHealthy() bool
	GetHealthStatus() *HealthStatus
	
	// Metrics collection
	IncrementCounter(name string, labels map[string]string)
	RecordDuration(name string, duration time.Duration, labels map[string]string)
	RecordGauge(name string, value float64, labels map[string]string)
	RecordHistogram(name string, value float64, labels map[string]string)
	
	// Performance monitoring
	StartSpan(ctx context.Context, name string) (context.Context, Span)
	RecordError(err error, labels map[string]string)
	
	// Database monitoring
	RecordDBQuery(query string, duration time.Duration, err error)
	GetDBStats() *DBStats
	
	// System monitoring
	GetSystemMetrics() *SystemMetrics
	
	// Cleanup
	Close() error
}

// HealthStatus represents the health status of the system
type HealthStatus struct {
	Status   string                 `json:"status"`
	Database map[string]bool        `json:"database"`
	Cache    map[string]bool        `json:"cache"`
	Queue    map[string]bool        `json:"queue"`
	External map[string]bool        `json:"external"`
	Uptime   time.Duration          `json:"uptime"`
	Details  map[string]interface{} `json:"details"`
}

// Span represents a tracing span
type Span interface {
	SetAttribute(key, value string)
	SetStatus(status string)
	RecordError(err error)
	End()
}

// DBStats represents database statistics
type DBStats struct {
	ConnectionsActive int           `json:"connections_active"`
	ConnectionsIdle   int           `json:"connections_idle"`
	ConnectionsTotal  int           `json:"connections_total"`
	QueriesTotal      int64         `json:"queries_total"`
	QueriesActive     int           `json:"queries_active"`
	AverageQueryTime  time.Duration `json:"average_query_time"`
	SlowQueries       int64         `json:"slow_queries"`
}

// SystemMetrics represents system-level metrics
type SystemMetrics struct {
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	DiskUsage   float64 `json:"disk_usage"`
	Goroutines  int     `json:"goroutines"`
	HTTPRequests int64  `json:"http_requests"`
}

// PrometheusMetrics interface for Prometheus-specific metrics
type PrometheusMetrics interface {
	GetPrometheusMetrics() ([]byte, error)
	RegisterCustomMetric(name, help string, labels []string) error
}

// AlertManager interface for alerting
type AlertManager interface {
	SendAlert(alert *Alert) error
	GetAlerts() ([]*Alert, error)
}

// Alert represents an alert
type Alert struct {
	Name        string            `json:"name"`
	Level       string            `json:"level"`
	Message     string            `json:"message"`
	Labels      map[string]string `json:"labels"`
	Timestamp   time.Time         `json:"timestamp"`
	Resolved    bool              `json:"resolved"`
	ResolvedAt  *time.Time        `json:"resolved_at,omitempty"`
}