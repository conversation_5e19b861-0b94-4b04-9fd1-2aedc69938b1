package monitoring

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/prometheus/common/expfmt"
)

// PrometheusMonitor implements the Monitor interface using Prometheus
type PrometheusMonitor struct {
	registry *prometheus.Registry
	
	// Standard metrics
	counters   map[string]*prometheus.CounterVec
	histograms map[string]*prometheus.HistogramVec
	gauges     map[string]*prometheus.GaugeVec
	
	// System metrics
	cpuUsage    prometheus.Gauge
	memoryUsage prometheus.Gauge
	goroutines  prometheus.Gauge
	
	// Database metrics
	dbConnections *prometheus.GaugeVec
	dbQueries     *prometheus.CounterVec
	dbDuration    *prometheus.HistogramVec
	
	// HTTP metrics
	httpRequests *prometheus.CounterVec
	httpDuration *prometheus.HistogramVec
	
	// Health status
	healthStatus *HealthStatus
	startTime    time.Time
	
	mu sync.RWMutex
}

// NewPrometheusMonitor creates a new Prometheus monitor
func NewPrometheusMonitor() *PrometheusMonitor {
	registry := prometheus.NewRegistry()
	
	pm := &PrometheusMonitor{
		registry:   registry,
		counters:   make(map[string]*prometheus.CounterVec),
		histograms: make(map[string]*prometheus.HistogramVec),
		gauges:     make(map[string]*prometheus.GaugeVec),
		startTime:  time.Now(),
		healthStatus: &HealthStatus{
			Status:   "healthy",
			Database: make(map[string]bool),
			Cache:    make(map[string]bool),
			Queue:    make(map[string]bool),
			External: make(map[string]bool),
			Details:  make(map[string]interface{}),
		},
	}
	
	// Initialize standard metrics
	pm.initializeMetrics()
	
	// Start system metrics collection
	go pm.collectSystemMetrics()
	
	return pm
}

// initializeMetrics initializes all Prometheus metrics
func (pm *PrometheusMonitor) initializeMetrics() {
	// System metrics
	pm.cpuUsage = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "system_cpu_usage",
		Help: "Current CPU usage percentage",
	})
	
	pm.memoryUsage = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "system_memory_usage",
		Help: "Current memory usage percentage",
	})
	
	pm.goroutines = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "system_goroutines",
		Help: "Current number of goroutines",
	})
	
	// Database metrics
	pm.dbConnections = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "database_connections",
			Help: "Current number of database connections",
		},
		[]string{"database", "status"},
	)
	
	pm.dbQueries = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "database_queries_total",
			Help: "Total number of database queries",
		},
		[]string{"database", "query_type", "status"},
	)
	
	pm.dbDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "database_query_duration_seconds",
			Help: "Database query duration in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0},
		},
		[]string{"database", "query_type"},
	)
	
	// HTTP metrics
	pm.httpRequests = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "path", "status_code"},
	)
	
	pm.httpDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "http_request_duration_seconds",
			Help: "HTTP request duration in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0},
		},
		[]string{"method", "path"},
	)
	
	// Register metrics
	pm.registry.MustRegister(
		pm.cpuUsage,
		pm.memoryUsage,
		pm.goroutines,
		pm.dbConnections,
		pm.dbQueries,
		pm.dbDuration,
		pm.httpRequests,
		pm.httpDuration,
	)
}

// IsHealthy checks if the system is healthy
func (pm *PrometheusMonitor) IsHealthy() bool {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	return pm.healthStatus.Status == "healthy"
}

// GetHealthStatus returns the current health status
func (pm *PrometheusMonitor) GetHealthStatus() *HealthStatus {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	// Update uptime
	pm.healthStatus.Uptime = time.Since(pm.startTime)
	
	// Create a copy to avoid race conditions
	status := &HealthStatus{
		Status:   pm.healthStatus.Status,
		Database: make(map[string]bool),
		Cache:    make(map[string]bool),
		Queue:    make(map[string]bool),
		External: make(map[string]bool),
		Uptime:   pm.healthStatus.Uptime,
		Details:  make(map[string]interface{}),
	}
	
	// Copy maps
	for k, v := range pm.healthStatus.Database {
		status.Database[k] = v
	}
	for k, v := range pm.healthStatus.Cache {
		status.Cache[k] = v
	}
	for k, v := range pm.healthStatus.Queue {
		status.Queue[k] = v
	}
	for k, v := range pm.healthStatus.External {
		status.External[k] = v
	}
	for k, v := range pm.healthStatus.Details {
		status.Details[k] = v
	}
	
	return status
}

// IncrementCounter increments a counter metric
func (pm *PrometheusMonitor) IncrementCounter(name string, labels map[string]string) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	counter, exists := pm.counters[name]
	if !exists {
		// Create new counter
		labelKeys := make([]string, 0, len(labels))
		for k := range labels {
			labelKeys = append(labelKeys, k)
		}
		
		counter = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: name,
				Help: fmt.Sprintf("Counter metric for %s", name),
			},
			labelKeys,
		)
		
		pm.counters[name] = counter
		pm.registry.MustRegister(counter)
	}
	
	counter.With(labels).Inc()
}

// RecordDuration records a duration metric
func (pm *PrometheusMonitor) RecordDuration(name string, duration time.Duration, labels map[string]string) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	histogram, exists := pm.histograms[name]
	if !exists {
		// Create new histogram
		labelKeys := make([]string, 0, len(labels))
		for k := range labels {
			labelKeys = append(labelKeys, k)
		}
		
		histogram = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name: name,
				Help: fmt.Sprintf("Duration metric for %s", name),
				Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0},
			},
			labelKeys,
		)
		
		pm.histograms[name] = histogram
		pm.registry.MustRegister(histogram)
	}
	
	histogram.With(labels).Observe(duration.Seconds())
}

// RecordGauge records a gauge metric
func (pm *PrometheusMonitor) RecordGauge(name string, value float64, labels map[string]string) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	gauge, exists := pm.gauges[name]
	if !exists {
		// Create new gauge
		labelKeys := make([]string, 0, len(labels))
		for k := range labels {
			labelKeys = append(labelKeys, k)
		}
		
		gauge = prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: name,
				Help: fmt.Sprintf("Gauge metric for %s", name),
			},
			labelKeys,
		)
		
		pm.gauges[name] = gauge
		pm.registry.MustRegister(gauge)
	}
	
	gauge.With(labels).Set(value)
}

// RecordHistogram records a histogram metric
func (pm *PrometheusMonitor) RecordHistogram(name string, value float64, labels map[string]string) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	histogram, exists := pm.histograms[name]
	if !exists {
		// Create new histogram
		labelKeys := make([]string, 0, len(labels))
		for k := range labels {
			labelKeys = append(labelKeys, k)
		}
		
		histogram = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name: name,
				Help: fmt.Sprintf("Histogram metric for %s", name),
				Buckets: prometheus.DefBuckets,
			},
			labelKeys,
		)
		
		pm.histograms[name] = histogram
		pm.registry.MustRegister(histogram)
	}
	
	histogram.With(labels).Observe(value)
}

// StartSpan starts a new tracing span
func (pm *PrometheusMonitor) StartSpan(ctx context.Context, name string) (context.Context, Span) {
	// Simple implementation - could be enhanced with OpenTelemetry
	span := &prometheusSpan{
		name:      name,
		startTime: time.Now(),
		monitor:   pm,
	}
	
	return ctx, span
}

// RecordError records an error
func (pm *PrometheusMonitor) RecordError(err error, labels map[string]string) {
	errorLabels := make(map[string]string)
	for k, v := range labels {
		errorLabels[k] = v
	}
	errorLabels["error"] = err.Error()
	
	pm.IncrementCounter("errors_total", errorLabels)
}

// RecordDBQuery records a database query
func (pm *PrometheusMonitor) RecordDBQuery(query string, duration time.Duration, err error) {
	labels := map[string]string{
		"database":   "mysql",
		"query_type": "select", // Could be enhanced to detect query type
	}
	
	if err != nil {
		labels["status"] = "error"
	} else {
		labels["status"] = "success"
	}
	
	pm.dbQueries.With(labels).Inc()
	pm.dbDuration.With(map[string]string{
		"database":   "mysql",
		"query_type": "select",
	}).Observe(duration.Seconds())
}

// GetDBStats returns database statistics
func (pm *PrometheusMonitor) GetDBStats() *DBStats {
	// This would be implemented with actual database connection pool stats
	return &DBStats{
		ConnectionsActive: 5,
		ConnectionsIdle:   10,
		ConnectionsTotal:  15,
		QueriesTotal:      1000,
		QueriesActive:     2,
		AverageQueryTime:  50 * time.Millisecond,
		SlowQueries:       5,
	}
}

// GetSystemMetrics returns system metrics
func (pm *PrometheusMonitor) GetSystemMetrics() *SystemMetrics {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	return &SystemMetrics{
		CPUUsage:     0.0, // Would need actual CPU monitoring
		MemoryUsage:  float64(m.Alloc) / 1024 / 1024, // MB
		DiskUsage:    0.0, // Would need disk monitoring
		Goroutines:   runtime.NumGoroutine(),
		HTTPRequests: 0, // Would track actual HTTP requests
	}
}

// GetPrometheusMetrics returns Prometheus metrics in text format
func (pm *PrometheusMonitor) GetPrometheusMetrics() ([]byte, error) {
	gathering, err := pm.registry.Gather()
	if err != nil {
		return nil, err
	}
	
	var buf []byte
	for _, mf := range gathering {
		if buf == nil {
			buf = make([]byte, 0, 1024)
		}
		
		// Use simpler approach - convert to string and append
		content := mf.String()
		buf = append(buf, []byte(content)...)
	}
	
	return buf, nil
}

// RegisterCustomMetric registers a custom metric
func (pm *PrometheusMonitor) RegisterCustomMetric(name, help string, labels []string) error {
	counter := prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: name,
			Help: help,
		},
		labels,
	)
	
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.counters[name] = counter
	return pm.registry.Register(counter)
}

// collectSystemMetrics collects system metrics periodically
func (pm *PrometheusMonitor) collectSystemMetrics() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	
	for range ticker.C {
		// Update goroutines
		pm.goroutines.Set(float64(runtime.NumGoroutine()))
		
		// Update memory usage
		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		pm.memoryUsage.Set(float64(m.Alloc) / 1024 / 1024) // MB
	}
}

// Close closes the monitor
func (pm *PrometheusMonitor) Close() error {
	// Cleanup resources
	return nil
}

// prometheusSpan implements the Span interface
type prometheusSpan struct {
	name      string
	startTime time.Time
	monitor   *PrometheusMonitor
}

func (s *prometheusSpan) SetAttribute(key, value string) {
	// Could store attributes for later use
}

func (s *prometheusSpan) SetStatus(status string) {
	// Could record status
}

func (s *prometheusSpan) RecordError(err error) {
	s.monitor.RecordError(err, map[string]string{
		"span": s.name,
	})
}

func (s *prometheusSpan) End() {
	duration := time.Since(s.startTime)
	s.monitor.RecordDuration("span_duration_seconds", duration, map[string]string{
		"span": s.name,
	})
}