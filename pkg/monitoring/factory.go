package monitoring

import (
	"context"
	"database/sql"
	"fmt"
	"time"
)

// MonitoringConfig holds configuration for monitoring
type MonitoringConfig struct {
	Type       string                 `json:"type" yaml:"type"`
	Prometheus PrometheusConfig       `json:"prometheus" yaml:"prometheus"`
	Health     HealthConfig           `json:"health" yaml:"health"`
	Metrics    map[string]interface{} `json:"metrics" yaml:"metrics"`
}

// PrometheusConfig holds Prometheus-specific configuration
type PrometheusConfig struct {
	Enabled    bool   `json:"enabled" yaml:"enabled"`
	Path       string `json:"path" yaml:"path"`
	Port       int    `json:"port" yaml:"port"`
	Namespace  string `json:"namespace" yaml:"namespace"`
	Subsystem  string `json:"subsystem" yaml:"subsystem"`
}

// HealthConfig holds health check configuration
type HealthConfig struct {
	Enabled     bool          `json:"enabled" yaml:"enabled"`
	Path        string        `json:"path" yaml:"path"`
	Port        int           `json:"port" yaml:"port"`
	Timeout     time.Duration `json:"timeout" yaml:"timeout"`
	Interval    time.Duration `json:"interval" yaml:"interval"`
	Database    bool          `json:"database" yaml:"database"`
	Memory      bool          `json:"memory" yaml:"memory"`
	Disk        bool          `json:"disk" yaml:"disk"`
	External    []string      `json:"external" yaml:"external"`
	MaxMemoryMB int64         `json:"max_memory_mb" yaml:"max_memory_mb"`
	MaxDiskPct  float64       `json:"max_disk_pct" yaml:"max_disk_pct"`
}

// MonitoringFactory creates monitoring instances
type MonitoringFactory struct {
	config *MonitoringConfig
}

// NewMonitoringFactory creates a new monitoring factory
func NewMonitoringFactory(config *MonitoringConfig) *MonitoringFactory {
	return &MonitoringFactory{
		config: config,
	}
}

// CreateMonitor creates a monitor instance based on configuration
func (mf *MonitoringFactory) CreateMonitor() (Monitor, error) {
	switch mf.config.Type {
	case "prometheus":
		return nil, fmt.Errorf("prometheus monitor is disabled - use 'simple' or 'noop' instead")
	case "simple":
		return NewSimpleMonitor(), nil
	case "noop":
		return NewNoOpMonitor(), nil
	default:
		return nil, fmt.Errorf("unsupported monitor type: %s", mf.config.Type)
	}
}

// CreateHealthService creates a health service with configured health checks
func (mf *MonitoringFactory) CreateHealthService(db *sql.DB) *HealthService {
	healthService := NewHealthService()
	
	if !mf.config.Health.Enabled {
		return healthService
	}
	
	timeout := mf.config.Health.Timeout
	if timeout == 0 {
		timeout = 5 * time.Second
	}
	
	// Database health check
	if mf.config.Health.Database && db != nil {
		healthService.RegisterChecker(NewDatabaseHealthChecker(db, timeout))
	}
	
	// Memory health check
	if mf.config.Health.Memory {
		maxMemory := mf.config.Health.MaxMemoryMB
		if maxMemory == 0 {
			maxMemory = 1024 // Default 1GB
		}
		healthService.RegisterChecker(NewMemoryHealthChecker(maxMemory, timeout))
	}
	
	// Disk health check
	if mf.config.Health.Disk {
		maxDiskPct := mf.config.Health.MaxDiskPct
		if maxDiskPct == 0 {
			maxDiskPct = 90.0 // Default 90%
		}
		healthService.RegisterChecker(NewDiskHealthChecker("/", maxDiskPct, timeout))
	}
	
	// External service health checks
	for _, url := range mf.config.Health.External {
		healthService.RegisterChecker(NewHTTPHealthChecker(url, timeout))
	}
	
	return healthService
}

// CreateHTTPMiddleware creates HTTP monitoring middleware
func (mf *MonitoringFactory) CreateHTTPMiddleware(monitor Monitor) *HTTPMiddleware {
	return NewHTTPMiddleware(monitor)
}

// CreateDatabaseMiddleware creates database monitoring middleware
func (mf *MonitoringFactory) CreateDatabaseMiddleware(monitor Monitor) *DatabaseMiddleware {
	return NewDatabaseMiddleware(monitor)
}

// CreateCacheMiddleware creates cache monitoring middleware
func (mf *MonitoringFactory) CreateCacheMiddleware(monitor Monitor) *CacheMiddleware {
	return NewCacheMiddleware(monitor)
}

// CreateQueueMiddleware creates queue monitoring middleware
func (mf *MonitoringFactory) CreateQueueMiddleware(monitor Monitor) *QueueMiddleware {
	return NewQueueMiddleware(monitor)
}

// DefaultConfig returns default monitoring configuration
func DefaultConfig() *MonitoringConfig {
	return &MonitoringConfig{
		Type: "simple",
		Prometheus: PrometheusConfig{
			Enabled:   true,
			Path:      "/metrics",
			Port:      9090,
			Namespace: "blog_api",
			Subsystem: "v3",
		},
		Health: HealthConfig{
			Enabled:     true,
			Path:        "/health",
			Port:        8080,
			Timeout:     5 * time.Second,
			Interval:    30 * time.Second,
			Database:    true,
			Memory:      true,
			Disk:        true,
			External:    []string{},
			MaxMemoryMB: 1024,
			MaxDiskPct:  90.0,
		},
		Metrics: map[string]interface{}{
			"collect_system_metrics": true,
			"collect_runtime_metrics": true,
			"collect_custom_metrics": true,
		},
	}
}

// NoOpMonitor implements Monitor interface with no-op operations
type NoOpMonitor struct{}

// NewNoOpMonitor creates a no-op monitor
func NewNoOpMonitor() *NoOpMonitor {
	return &NoOpMonitor{}
}

func (n *NoOpMonitor) IsHealthy() bool {
	return true
}

func (n *NoOpMonitor) GetHealthStatus() *HealthStatus {
	return &HealthStatus{
		Status:   "healthy",
		Database: make(map[string]bool),
		Cache:    make(map[string]bool),
		Queue:    make(map[string]bool),
		External: make(map[string]bool),
		Uptime:   time.Hour,
		Details:  make(map[string]interface{}),
	}
}

func (n *NoOpMonitor) IncrementCounter(name string, labels map[string]string) {}

func (n *NoOpMonitor) RecordDuration(name string, duration time.Duration, labels map[string]string) {}

func (n *NoOpMonitor) RecordGauge(name string, value float64, labels map[string]string) {}

func (n *NoOpMonitor) RecordHistogram(name string, value float64, labels map[string]string) {}

func (n *NoOpMonitor) StartSpan(ctx context.Context, name string) (context.Context, Span) {
	return ctx, &noOpSpan{}
}

func (n *NoOpMonitor) RecordError(err error, labels map[string]string) {}

func (n *NoOpMonitor) RecordDBQuery(query string, duration time.Duration, err error) {}

func (n *NoOpMonitor) GetDBStats() *DBStats {
	return &DBStats{}
}

func (n *NoOpMonitor) GetSystemMetrics() *SystemMetrics {
	return &SystemMetrics{}
}

func (n *NoOpMonitor) Close() error {
	return nil
}

// noOpSpan implements Span interface with no-op operations
type noOpSpan struct{}

func (n *noOpSpan) SetAttribute(key, value string) {}

func (n *noOpSpan) SetStatus(status string) {}

func (n *noOpSpan) RecordError(err error) {}

func (n *noOpSpan) End() {}

// MonitoringService combines all monitoring components
type MonitoringService struct {
	Monitor         Monitor
	HealthService   *HealthService
	HTTPMiddleware  *HTTPMiddleware
	DBMiddleware    *DatabaseMiddleware
	CacheMiddleware *CacheMiddleware
	QueueMiddleware *QueueMiddleware
	Config          *MonitoringConfig
}

// NewMonitoringService creates a complete monitoring service
func NewMonitoringService(config *MonitoringConfig, db *sql.DB) (*MonitoringService, error) {
	factory := NewMonitoringFactory(config)
	
	// Create monitor
	monitor, err := factory.CreateMonitor()
	if err != nil {
		return nil, fmt.Errorf("failed to create monitor: %w", err)
	}
	
	// Create health service
	healthService := factory.CreateHealthService(db)
	
	// Create middleware
	httpMiddleware := factory.CreateHTTPMiddleware(monitor)
	dbMiddleware := factory.CreateDatabaseMiddleware(monitor)
	cacheMiddleware := factory.CreateCacheMiddleware(monitor)
	queueMiddleware := factory.CreateQueueMiddleware(monitor)
	
	return &MonitoringService{
		Monitor:         monitor,
		HealthService:   healthService,
		HTTPMiddleware:  httpMiddleware,
		DBMiddleware:    dbMiddleware,
		CacheMiddleware: cacheMiddleware,
		QueueMiddleware: queueMiddleware,
		Config:          config,
	}, nil
}

// Start starts the monitoring service
func (ms *MonitoringService) Start() error {
	// Could start background tasks here
	return nil
}

// Stop stops the monitoring service
func (ms *MonitoringService) Stop() error {
	return ms.Monitor.Close()
}