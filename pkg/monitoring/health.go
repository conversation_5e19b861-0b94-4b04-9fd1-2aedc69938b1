package monitoring

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"runtime"
	"sync"
	"time"
)

// HealthChecker defines the interface for health checks
type HealthChecker interface {
	Check(ctx context.Context) error
	Name() string
	Timeout() time.Duration
}

// HealthService manages health checks
type HealthService struct {
	checkers []HealthChecker
	cache    map[string]*HealthCheckResult
	mu       sync.RWMutex
}

// HealthCheckResult represents the result of a health check
type HealthCheckResult struct {
	Name      string        `json:"name"`
	Status    string        `json:"status"`
	Error     string        `json:"error,omitempty"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
}

// NewHealthService creates a new health service
func NewHealthService() *HealthService {
	return &HealthService{
		checkers: make([]HealthChecker, 0),
		cache:    make(map[string]*HealthCheckResult),
	}
}

// RegisterChecker registers a health checker
func (hs *HealthService) RegisterChecker(checker HealthChecker) {
	hs.mu.Lock()
	defer hs.mu.Unlock()
	
	hs.checkers = append(hs.checkers, checker)
}

// CheckAll runs all health checks
func (hs *HealthService) CheckAll(ctx context.Context) map[string]*HealthCheckResult {
	results := make(map[string]*HealthCheckResult)
	
	// Use a channel to collect results
	resultChan := make(chan *HealthCheckResult, len(hs.checkers))
	
	// Run all checks concurrently
	for _, checker := range hs.checkers {
		go func(c HealthChecker) {
			result := hs.runCheck(ctx, c)
			resultChan <- result
		}(checker)
	}
	
	// Collect results
	for i := 0; i < len(hs.checkers); i++ {
		result := <-resultChan
		results[result.Name] = result
	}
	
	// Update cache
	hs.mu.Lock()
	for name, result := range results {
		hs.cache[name] = result
	}
	hs.mu.Unlock()
	
	return results
}

// runCheck runs a single health check
func (hs *HealthService) runCheck(ctx context.Context, checker HealthChecker) *HealthCheckResult {
	start := time.Now()
	
	// Create context with timeout
	checkCtx, cancel := context.WithTimeout(ctx, checker.Timeout())
	defer cancel()
	
	result := &HealthCheckResult{
		Name:      checker.Name(),
		Status:    "healthy",
		Timestamp: start,
	}
	
	// Run the check
	err := checker.Check(checkCtx)
	result.Duration = time.Since(start)
	
	if err != nil {
		result.Status = "unhealthy"
		result.Error = err.Error()
	}
	
	return result
}

// GetCachedResults returns cached health check results
func (hs *HealthService) GetCachedResults() map[string]*HealthCheckResult {
	hs.mu.RLock()
	defer hs.mu.RUnlock()
	
	results := make(map[string]*HealthCheckResult)
	for name, result := range hs.cache {
		results[name] = result
	}
	
	return results
}

// IsHealthy returns true if all checks are healthy
func (hs *HealthService) IsHealthy() bool {
	hs.mu.RLock()
	defer hs.mu.RUnlock()
	
	for _, result := range hs.cache {
		if result.Status != "healthy" {
			return false
		}
	}
	
	return true
}

// DatabaseHealthChecker checks database connectivity
type DatabaseHealthChecker struct {
	db      *sql.DB
	timeout time.Duration
}

// NewDatabaseHealthChecker creates a database health checker
func NewDatabaseHealthChecker(db *sql.DB, timeout time.Duration) *DatabaseHealthChecker {
	return &DatabaseHealthChecker{
		db:      db,
		timeout: timeout,
	}
}

func (d *DatabaseHealthChecker) Name() string {
	return "database"
}

func (d *DatabaseHealthChecker) Timeout() time.Duration {
	return d.timeout
}

func (d *DatabaseHealthChecker) Check(ctx context.Context) error {
	// Test database connection
	err := d.db.PingContext(ctx)
	if err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}
	
	// Test a simple query
	_, err = d.db.ExecContext(ctx, "SELECT 1")
	if err != nil {
		return fmt.Errorf("database query failed: %w", err)
	}
	
	return nil
}

// HTTPHealthChecker checks HTTP endpoint
type HTTPHealthChecker struct {
	url     string
	timeout time.Duration
	client  *http.Client
}

// NewHTTPHealthChecker creates an HTTP health checker
func NewHTTPHealthChecker(url string, timeout time.Duration) *HTTPHealthChecker {
	return &HTTPHealthChecker{
		url:     url,
		timeout: timeout,
		client: &http.Client{
			Timeout: timeout,
		},
	}
}

func (h *HTTPHealthChecker) Name() string {
	return fmt.Sprintf("http_%s", h.url)
}

func (h *HTTPHealthChecker) Timeout() time.Duration {
	return h.timeout
}

func (h *HTTPHealthChecker) Check(ctx context.Context) error {
	req, err := http.NewRequestWithContext(ctx, "GET", h.url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	resp, err := h.client.Do(req)
	if err != nil {
		return fmt.Errorf("http request failed: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode >= 400 {
		return fmt.Errorf("http status code: %d", resp.StatusCode)
	}
	
	return nil
}

// MemoryHealthChecker checks memory usage
type MemoryHealthChecker struct {
	maxMemoryMB int64
	timeout     time.Duration
}

// NewMemoryHealthChecker creates a memory health checker
func NewMemoryHealthChecker(maxMemoryMB int64, timeout time.Duration) *MemoryHealthChecker {
	return &MemoryHealthChecker{
		maxMemoryMB: maxMemoryMB,
		timeout:     timeout,
	}
}

func (m *MemoryHealthChecker) Name() string {
	return "memory"
}

func (m *MemoryHealthChecker) Timeout() time.Duration {
	return m.timeout
}

func (m *MemoryHealthChecker) Check(ctx context.Context) error {
	var stats runtime.MemStats
	runtime.ReadMemStats(&stats)
	
	currentMemoryMB := int64(stats.Alloc) / 1024 / 1024
	
	if currentMemoryMB > m.maxMemoryMB {
		return fmt.Errorf("memory usage %dMB exceeds limit %dMB", currentMemoryMB, m.maxMemoryMB)
	}
	
	return nil
}

// DiskHealthChecker checks disk usage
type DiskHealthChecker struct {
	path        string
	maxUsagePct float64
	timeout     time.Duration
}

// NewDiskHealthChecker creates a disk health checker
func NewDiskHealthChecker(path string, maxUsagePct float64, timeout time.Duration) *DiskHealthChecker {
	return &DiskHealthChecker{
		path:        path,
		maxUsagePct: maxUsagePct,
		timeout:     timeout,
	}
}

func (d *DiskHealthChecker) Name() string {
	return fmt.Sprintf("disk_%s", d.path)
}

func (d *DiskHealthChecker) Timeout() time.Duration {
	return d.timeout
}

func (d *DiskHealthChecker) Check(ctx context.Context) error {
	// This is a simplified implementation
	// In production, you would use syscall.Statfs or similar
	
	// For now, just return healthy
	return nil
}

// CustomHealthChecker allows custom health checks
type CustomHealthChecker struct {
	name        string
	checkFunc   func(context.Context) error
	timeout     time.Duration
}

// NewCustomHealthChecker creates a custom health checker
func NewCustomHealthChecker(name string, checkFunc func(context.Context) error, timeout time.Duration) *CustomHealthChecker {
	return &CustomHealthChecker{
		name:      name,
		checkFunc: checkFunc,
		timeout:   timeout,
	}
}

func (c *CustomHealthChecker) Name() string {
	return c.name
}

func (c *CustomHealthChecker) Timeout() time.Duration {
	return c.timeout
}

func (c *CustomHealthChecker) Check(ctx context.Context) error {
	return c.checkFunc(ctx)
}