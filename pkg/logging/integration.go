package logging

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/gin-gonic/gin"
)

// ApplicationLogger represents the main application logger
type ApplicationLogger struct {
	main     Logger
	access   AccessLogger
	error    ErrorLogger
	audit    AuditLogger
	security SecurityLogger
	debug    *DebugLogger
	tracer   *RequestTracer
	config   *LogConfig
}

// NewApplicationLogger creates a new application logger
func NewApplicationLogger(config *LogConfig) (*ApplicationLogger, error) {
	if config == nil {
		config = LoadConfigFromEnv()
	}

	// Create base logger
	baseLogger, err := NewZapLogger(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create base logger: %w", err)
	}

	// Apply production features if needed
	var mainLogger Logger = baseLogger
	
	// Add sanitization in production
	if config.Mode == ModeProduction && config.Sanitization.Enabled {
		mainLogger = NewSanitizingLogger(mainLogger, config.Sanitization)
	}

	// Add sampling in production
	if config.Mode == ModeProduction && config.Production != nil {
		if config.Production.SamplingRate < 1.0 {
			mainLogger = NewSamplingLogger(mainLogger, config.Production.SamplingRate, config.Production.ErrorSamplingRate)
		}
	}

	// Add async logging in production
	if config.Mode == ModeProduction && config.Production != nil && config.Production.AsyncLogging {
		mainLogger = NewAsyncLogger(mainLogger, config.Production.BatchSize, config.Production.FlushInterval)
	}

	// Create specialized loggers
	accessLogger := NewAccessLogger(mainLogger)
	errorLogger := NewErrorLogger(mainLogger)
	auditLogger := NewAuditLogger(mainLogger)
	securityLogger := NewSecurityLogger(mainLogger)

	// Create debug logger if enabled
	var debugLogger *DebugLogger
	var tracer *RequestTracer
	if config.Debug != nil && config.Debug.Enabled {
		debugLogger = NewDebugLogger(mainLogger, config.Debug)
		tracer = NewRequestTracer(mainLogger, config.Debug)
	}

	return &ApplicationLogger{
		main:     mainLogger,
		access:   accessLogger,
		error:    errorLogger,
		audit:    auditLogger,
		security: securityLogger,
		debug:    debugLogger,
		tracer:   tracer,
		config:   config,
	}, nil
}

// GetLogger returns the main logger
func (a *ApplicationLogger) GetLogger() Logger {
	return a.main
}

// GetAccessLogger returns the access logger
func (a *ApplicationLogger) GetAccessLogger() AccessLogger {
	return a.access
}

// GetErrorLogger returns the error logger
func (a *ApplicationLogger) GetErrorLogger() ErrorLogger {
	return a.error
}

// GetAuditLogger returns the audit logger
func (a *ApplicationLogger) GetAuditLogger() AuditLogger {
	return a.audit
}

// GetSecurityLogger returns the security logger
func (a *ApplicationLogger) GetSecurityLogger() SecurityLogger {
	return a.security
}

// GetDebugLogger returns the debug logger
func (a *ApplicationLogger) GetDebugLogger() *DebugLogger {
	return a.debug
}

// GetTracer returns the request tracer
func (a *ApplicationLogger) GetTracer() *RequestTracer {
	return a.tracer
}

// GetConfig returns the logging configuration
func (a *ApplicationLogger) GetConfig() *LogConfig {
	return a.config
}

// SetupMiddleware sets up all logging middleware
func (a *ApplicationLogger) SetupMiddleware(router *gin.Engine) {
	// Create middleware configuration
	middlewareConfig := &LoggingConfig{
		AccessLog:     true,
		ErrorLog:      true,
		PanicRecovery: true,
		RequestBody:   a.config.Debug != nil && a.config.Debug.RequestBodyLogging,
		Trace:         a.config.Debug != nil && a.config.Debug.Enabled,
		Security:      true,
		ExcludePaths:  a.config.Production.ExcludePaths,
	}

	// Setup middleware
	SetupLoggingMiddleware(router, a.main, middlewareConfig)

	// Setup debug endpoints if enabled
	if a.debug != nil && a.debug.IsEnabled() {
		SetupDebugMode(router, a.main, a.config.Debug)
	}
}

// LogStartup logs application startup information
func (a *ApplicationLogger) LogStartup(ctx context.Context, appName, version string, port int) {
	a.main.WithContext(ctx).Info("application starting",
		Field{Key: "app_name", Value: appName},
		Field{Key: "version", Value: version},
		Field{Key: "port", Value: port},
		Field{Key: "mode", Value: a.config.Mode},
		Field{Key: "log_level", Value: a.config.Level.String()},
		Field{Key: "environment", Value: os.Getenv("ENVIRONMENT")},
	)
}

// LogShutdown logs application shutdown information
func (a *ApplicationLogger) LogShutdown(ctx context.Context, reason string) {
	a.main.WithContext(ctx).Info("application shutting down",
		Field{Key: "reason", Value: reason},
	)
}

// Close closes all loggers and flushes pending logs
func (a *ApplicationLogger) Close() error {
	// Close async logger if present
	if asyncLogger, ok := a.main.(*AsyncLogger); ok {
		if err := asyncLogger.Close(); err != nil {
			return fmt.Errorf("failed to close async logger: %w", err)
		}
	}

	// Sync Zap logger
	if zapLogger, ok := a.main.(*ZapLogger); ok {
		if err := zapLogger.Sync(); err != nil {
			return fmt.Errorf("failed to sync zap logger: %w", err)
		}
	}

	return nil
}

// LoggerSetup provides a simple setup function for the application
func LoggerSetup(appName, version string) (*ApplicationLogger, error) {
	// Load configuration
	config := LoadConfigFromEnv()

	// Create application logger
	appLogger, err := NewApplicationLogger(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create application logger: %w", err)
	}

	// Log startup
	appLogger.LogStartup(context.Background(), appName, version, 0)

	return appLogger, nil
}

// MustLoggerSetup creates logger or panics
func MustLoggerSetup(appName, version string) *ApplicationLogger {
	appLogger, err := LoggerSetup(appName, version)
	if err != nil {
		panic(fmt.Sprintf("failed to setup logger: %v", err))
	}
	return appLogger
}

// IntegrateWithGin integrates logging with Gin router
func IntegrateWithGin(router *gin.Engine, appLogger *ApplicationLogger) {
	// Set Gin mode based on logging mode
	switch appLogger.config.Mode {
	case ModeDevelopment, ModeDebug:
		gin.SetMode(gin.DebugMode)
	case ModeStaging:
		gin.SetMode(gin.TestMode)
	case ModeProduction:
		gin.SetMode(gin.ReleaseMode)
	}

	// Setup middleware
	appLogger.SetupMiddleware(router)

	// Add request ID middleware
	router.Use(func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
			c.Header("X-Request-ID", requestID)
		}
		
		// Add to context
		ctx := context.WithValue(c.Request.Context(), "request_id", requestID)
		c.Request = c.Request.WithContext(ctx)
		
		c.Next()
	})

	// Add tenant context middleware if needed
	router.Use(func(c *gin.Context) {
		tenantID := c.GetHeader("X-Tenant-ID")
		if tenantID != "" {
			ctx := context.WithValue(c.Request.Context(), "tenant_id", tenantID)
			c.Request = c.Request.WithContext(ctx)
		}
		c.Next()
	})
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// IntegrateWithDatabase integrates logging with database operations
func IntegrateWithDatabase(appLogger *ApplicationLogger, db interface{}) {
	// This would integrate with GORM or other database libraries
	// For now, just log the integration
	appLogger.main.Info("database logging integration configured")
}

// IntegrateWithCache integrates logging with cache operations
func IntegrateWithCache(appLogger *ApplicationLogger, cache interface{}) {
	// This would integrate with Redis or other cache libraries
	appLogger.main.Info("cache logging integration configured")
}

// LoggingContext provides context-aware logging helpers
type LoggingContext struct {
	logger Logger
	ctx    context.Context
}

// NewLoggingContext creates a new logging context
func NewLoggingContext(logger Logger, ctx context.Context) *LoggingContext {
	return &LoggingContext{
		logger: logger.WithContext(ctx),
		ctx:    ctx,
	}
}

// WithField adds a field to the logging context
func (l *LoggingContext) WithField(key string, value interface{}) *LoggingContext {
	return &LoggingContext{
		logger: l.logger.With(key, value),
		ctx:    l.ctx,
	}
}

// WithFields adds multiple fields to the logging context
func (l *LoggingContext) WithFields(fields ...Field) *LoggingContext {
	return &LoggingContext{
		logger: l.logger.WithFields(fields...),
		ctx:    l.ctx,
	}
}

// WithError adds an error to the logging context
func (l *LoggingContext) WithError(err error) *LoggingContext {
	return &LoggingContext{
		logger: l.logger.WithError(err),
		ctx:    l.ctx,
	}
}

// Logger returns the underlying logger
func (l *LoggingContext) Logger() Logger {
	return l.logger
}

// Context returns the context
func (l *LoggingContext) Context() context.Context {
	return l.ctx
}

// LogOperation logs an operation with timing
func (l *LoggingContext) LogOperation(operation string, fn func() error) error {
	timer := l.logger.WithTimer()
	defer timer.Stop()

	l.logger.Debug("operation started",
		Field{Key: "operation", Value: operation},
	)

	err := fn()
	
	if err != nil {
		l.logger.Error("operation failed",
			Field{Key: "operation", Value: operation},
			Field{Key: "error", Value: err.Error()},
		)
	} else {
		l.logger.Info("operation completed",
			Field{Key: "operation", Value: operation},
		)
	}

	return err
}

// Package-level convenience functions for global application logger
var globalAppLogger *ApplicationLogger

// InitGlobalLogger initializes the global application logger
func InitGlobalLogger(config *LogConfig) error {
	appLogger, err := NewApplicationLogger(config)
	if err != nil {
		return err
	}
	
	globalAppLogger = appLogger
	return nil
}

// GetGlobalLogger returns the global application logger
func GetGlobalLogger() *ApplicationLogger {
	return globalAppLogger
}

// GetGlobalMainLogger returns the global main logger
func GetGlobalMainLogger() Logger {
	if globalAppLogger == nil {
		return MustNewLogger()
	}
	return globalAppLogger.main
}

// WithContext creates a logging context with the global logger
func WithContext(ctx context.Context) *LoggingContext {
	return NewLoggingContext(GetGlobalMainLogger(), ctx)
}

// LogInfo logs an info message with the global logger
func LogInfo(msg string, fields ...Field) {
	GetGlobalMainLogger().Info(msg, fields...)
}

// LogError logs an error message with the global logger
func LogError(msg string, fields ...Field) {
	GetGlobalMainLogger().Error(msg, fields...)
}

// LogWarn logs a warning message with the global logger
func LogWarn(msg string, fields ...Field) {
	GetGlobalMainLogger().Warn(msg, fields...)
}

// LogDebug logs a debug message with the global logger
func LogDebug(msg string, fields ...Field) {
	GetGlobalMainLogger().Debug(msg, fields...)
}