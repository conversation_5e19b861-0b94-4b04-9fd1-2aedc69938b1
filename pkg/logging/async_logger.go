package logging

import (
	"context"
	"sync"
	"time"
)

// AsyncLogger implements async logging for high-performance scenarios
type AsyncLogger struct {
	logger    Logger
	buffer    chan *LogEntry
	batchSize int
	interval  time.Duration
	done      chan struct{}
	wg        sync.WaitGroup
	overflow  int64 // Counter for dropped logs
	mu        sync.RWMutex
}

// NewAsyncLogger creates a new async logger
func NewAsyncLogger(logger Logger, batchSize int, interval time.Duration) *AsyncLogger {
	a := &AsyncLogger{
		logger:    logger,
		buffer:    make(chan *LogEntry, batchSize*10), // Buffer 10x batch size
		batchSize: batchSize,
		interval:  interval,
		done:      make(chan struct{}),
	}

	a.wg.Add(1)
	go a.processor()
	return a
}

// processor processes log entries in batches
func (a *AsyncLogger) processor() {
	defer a.wg.Done()
	
	batch := make([]*LogEntry, 0, a.batchSize)
	ticker := time.NewTicker(a.interval)
	defer ticker.Stop()

	for {
		select {
		case entry := <-a.buffer:
			batch = append(batch, entry)
			if len(batch) >= a.batchSize {
				a.flush(batch)
				batch = batch[:0]
			}

		case <-ticker.C:
			if len(batch) > 0 {
				a.flush(batch)
				batch = batch[:0]
			}

		case <-a.done:
			// Process remaining entries
			for len(a.buffer) > 0 {
				batch = append(batch, <-a.buffer)
				if len(batch) >= a.batchSize {
					a.flush(batch)
					batch = batch[:0]
				}
			}
			// Flush remaining
			if len(batch) > 0 {
				a.flush(batch)
			}
			return
		}
	}
}

// flush processes a batch of log entries
func (a *AsyncLogger) flush(batch []*LogEntry) {
	for _, entry := range batch {
		switch entry.Level {
		case TRACE:
			a.logger.Trace(entry.Message, entry.Fields...)
		case DEBUG:
			a.logger.Debug(entry.Message, entry.Fields...)
		case INFO:
			a.logger.Info(entry.Message, entry.Fields...)
		case WARN:
			a.logger.Warn(entry.Message, entry.Fields...)
		case ERROR:
			a.logger.Error(entry.Message, entry.Fields...)
		case FATAL:
			a.logger.Fatal(entry.Message, entry.Fields...)
		case PANIC:
			a.logger.Panic(entry.Message, entry.Fields...)
		}
	}
}

// handleOverflow handles buffer overflow
func (a *AsyncLogger) handleOverflow() {
	a.mu.Lock()
	a.overflow++
	a.mu.Unlock()
	
	// Log overflow periodically (every 1000 drops)
	if a.overflow%1000 == 0 {
		a.logger.Warn("log buffer overflow",
			Field{Key: "dropped_logs", Value: a.overflow},
		)
	}
}

// GetOverflowCount returns the number of dropped logs
func (a *AsyncLogger) GetOverflowCount() int64 {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.overflow
}

// Implementation of Logger interface

func (a *AsyncLogger) Trace(msg string, fields ...Field) {
	a.logAsync(TRACE, msg, fields...)
}

func (a *AsyncLogger) Debug(msg string, fields ...Field) {
	a.logAsync(DEBUG, msg, fields...)
}

func (a *AsyncLogger) Info(msg string, fields ...Field) {
	a.logAsync(INFO, msg, fields...)
}

func (a *AsyncLogger) Warn(msg string, fields ...Field) {
	a.logAsync(WARN, msg, fields...)
}

func (a *AsyncLogger) Error(msg string, fields ...Field) {
	a.logAsync(ERROR, msg, fields...)
}

func (a *AsyncLogger) Fatal(msg string, fields ...Field) {
	// Fatal logs should be synchronous
	a.logger.Fatal(msg, fields...)
}

func (a *AsyncLogger) Panic(msg string, fields ...Field) {
	// Panic logs should be synchronous
	a.logger.Panic(msg, fields...)
}

// logAsync queues a log entry for async processing
func (a *AsyncLogger) logAsync(level LogLevel, msg string, fields ...Field) {
	entry := &LogEntry{
		Level:   level,
		Message: msg,
		Fields:  fields,
		Time:    time.Now(),
	}

	select {
	case a.buffer <- entry:
		// Successfully queued
	default:
		// Buffer full, handle overflow
		a.handleOverflow()
	}
}

func (a *AsyncLogger) WithContext(ctx context.Context) Logger {
	// Create a new async logger with context-aware underlying logger
	return &AsyncLogger{
		logger:    a.logger.WithContext(ctx),
		buffer:    a.buffer,
		batchSize: a.batchSize,
		interval:  a.interval,
		done:      a.done,
		overflow:  a.overflow,
	}
}

func (a *AsyncLogger) WithFields(fields ...Field) Logger {
	return &AsyncLogger{
		logger:    a.logger.WithFields(fields...),
		buffer:    a.buffer,
		batchSize: a.batchSize,
		interval:  a.interval,
		done:      a.done,
		overflow:  a.overflow,
	}
}

func (a *AsyncLogger) WithError(err error) Logger {
	return &AsyncLogger{
		logger:    a.logger.WithError(err),
		buffer:    a.buffer,
		batchSize: a.batchSize,
		interval:  a.interval,
		done:      a.done,
		overflow:  a.overflow,
	}
}

func (a *AsyncLogger) WithTimer() TimerLogger {
	start := time.Now()
	return &asyncTimerLogger{
		AsyncLogger: a,
		start:       start,
	}
}

func (a *AsyncLogger) With(key string, value interface{}) Logger {
	return &AsyncLogger{
		logger:    a.logger.With(key, value),
		buffer:    a.buffer,
		batchSize: a.batchSize,
		interval:  a.interval,
		done:      a.done,
		overflow:  a.overflow,
	}
}

// Close gracefully shuts down the async logger
func (a *AsyncLogger) Close() error {
	close(a.done)
	a.wg.Wait()
	return nil
}

// Flush forces immediate processing of all queued entries
func (a *AsyncLogger) Flush() {
	// Signal flush by sending on done channel (non-blocking)
	select {
	case a.done <- struct{}{}:
	default:
	}
}

// asyncTimerLogger implements TimerLogger for async logger
type asyncTimerLogger struct {
	*AsyncLogger
	start time.Time
}

func (t *asyncTimerLogger) Stop() {
	duration := time.Since(t.start)
	t.Info("operation completed",
		Field{Key: "duration", Value: duration},
		Field{Key: "duration_ms", Value: duration.Milliseconds()},
	)
}

// Stats returns async logger statistics
func (a *AsyncLogger) Stats() map[string]interface{} {
	a.mu.RLock()
	defer a.mu.RUnlock()
	
	return map[string]interface{}{
		"buffer_size":    cap(a.buffer),
		"buffer_used":    len(a.buffer),
		"batch_size":     a.batchSize,
		"flush_interval": a.interval,
		"overflow_count": a.overflow,
	}
}