package logging

import (
	"bytes"
	"context"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AccessLogMiddleware creates middleware for access logging
func AccessLogMiddleware(logger Logger) gin.HandlerFunc {
	accessLogger := NewAccessLogger(logger)
	
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery
		
		// Generate request ID if not present
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
			c.Header("X-Request-ID", requestID)
		}
		
		// Set request ID in context
		ctx := context.WithValue(c.Request.Context(), "request_id", requestID)
		c.Request = c.Request.WithContext(ctx)
		
		// Process request
		c.Next()
		
		// Log after processing
		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()
		bodySize := c.Writer.Size()
		
		// Get error message if any
		var errorMessage string
		if len(c.Errors) > 0 {
			errorMessage = c.Errors.ByType(gin.ErrorTypePrivate).String()
		}
		
		// Get user context if authenticated
		var userID, tenantID *uint
		if uid, exists := c.Get("user_id"); exists {
			if id, ok := uid.(uint); ok {
				userID = &id
			}
		}
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}
		
		// Create access log request
		req := &AccessLogRequest{
			Method:       method,
			Path:         path,
			Query:        raw,
			Status:       statusCode,
			ClientIP:     clientIP,
			UserAgent:    c.Request.UserAgent(),
			Latency:      latency,
			BodySize:     bodySize,
			UserID:       userID,
			TenantID:     tenantID,
			RequestID:    requestID,
			ErrorMessage: errorMessage,
		}
		
		accessLogger.LogRequest(ctx, req)
	}
}

// ErrorLogMiddleware creates middleware for error logging
func ErrorLogMiddleware(logger Logger) gin.HandlerFunc {
	errorLogger := NewErrorLogger(logger)
	
	return func(c *gin.Context) {
		c.Next()
		
		// Log errors
		for _, err := range c.Errors {
			errorLogger.LogError(c.Request.Context(), err.Err, err.Meta.(string))
		}
	}
}

// PanicRecoveryMiddleware creates middleware for panic recovery with logging
func PanicRecoveryMiddleware(logger Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				logger.WithContext(c.Request.Context()).Panic("panic recovered",
					Field{Key: "error", Value: err},
					Field{Key: "path", Value: c.Request.URL.Path},
					Field{Key: "method", Value: c.Request.Method},
					Field{Key: "ip", Value: c.ClientIP()},
				)
				
				c.AbortWithStatusJSON(500, gin.H{
					"error": "Internal server error",
				})
			}
		}()
		
		c.Next()
	}
}

// RequestBodyLogMiddleware creates middleware for request body logging (debug mode)
func RequestBodyLogMiddleware(logger Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Only log if content type is JSON and content length is reasonable
		contentType := c.GetHeader("Content-Type")
		if contentType != "application/json" {
			c.Next()
			return
		}
		
		// Read body
		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			logger.WithContext(c.Request.Context()).Error("failed to read request body",
				Field{Key: "error", Value: err.Error()},
			)
			c.Next()
			return
		}
		
		// Restore body
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		
		// Log body (be careful with sensitive data)
		if len(body) > 0 && len(body) < 10000 { // Limit size
			logger.WithContext(c.Request.Context()).Debug("request body",
				Field{Key: "body", Value: string(body)},
				Field{Key: "size", Value: len(body)},
			)
		}
		
		c.Next()
	}
}

// TraceMiddleware creates middleware for request tracing
func TraceMiddleware(logger Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		traceID := c.GetHeader("X-Trace-ID")
		if traceID == "" {
			traceID = uuid.New().String()
			c.Header("X-Trace-ID", traceID)
		}
		
		// Set trace ID in context
		ctx := context.WithValue(c.Request.Context(), "trace_id", traceID)
		c.Request = c.Request.WithContext(ctx)
		
		// Create trace logger
		traceLogger := logger.WithContext(ctx)
		
		// Log request start
		traceLogger.Debug("request started",
			Field{Key: "method", Value: c.Request.Method},
			Field{Key: "path", Value: c.Request.URL.Path},
			Field{Key: "headers", Value: c.Request.Header},
			Field{Key: "ip", Value: c.ClientIP()},
			Field{Key: "user_agent", Value: c.Request.UserAgent()},
		)
		
		start := time.Now()
		c.Next()
		
		// Log request completion
		duration := time.Since(start)
		traceLogger.Debug("request completed",
			Field{Key: "status", Value: c.Writer.Status()},
			Field{Key: "size", Value: c.Writer.Size()},
			Field{Key: "duration", Value: duration},
			Field{Key: "duration_ms", Value: duration.Milliseconds()},
		)
	}
}

// SecurityLogMiddleware creates middleware for security logging
func SecurityLogMiddleware(logger Logger) gin.HandlerFunc {
	securityLogger := NewSecurityLogger(logger)
	
	return func(c *gin.Context) {
		// Log potential security events
		userAgent := c.Request.UserAgent()
		
		// Detect suspicious patterns
		if isSuspiciousUserAgent(userAgent) {
			securityLogger.LogSecurityEvent(&SecurityEvent{
				Type:      "suspicious_user_agent",
				Severity:  "medium",
				SourceIP:  c.ClientIP(),
				Details: map[string]interface{}{
					"user_agent": userAgent,
					"path":       c.Request.URL.Path,
				},
				Timestamp: time.Now(),
			})
		}
		
		c.Next()
		
		// Log failed authentication attempts
		if c.Writer.Status() == 401 {
			securityLogger.LogAuthEvent(c.Request.Context(), "authentication_failed", false, map[string]interface{}{
				"path":       c.Request.URL.Path,
				"method":     c.Request.Method,
				"user_agent": userAgent,
			})
		}
	}
}

// isSuspiciousUserAgent checks if user agent is suspicious
func isSuspiciousUserAgent(userAgent string) bool {
	suspicious := []string{
		"sqlmap",
		"nmap",
		"nikto",
		"dirb",
		"gobuster",
		"masscan",
	}
	
	for _, pattern := range suspicious {
		if contains([]string{userAgent}, pattern) {
			return true
		}
	}
	
	return false
}

// LoggingConfig represents middleware configuration
type LoggingConfig struct {
	AccessLog     bool
	ErrorLog      bool
	PanicRecovery bool
	RequestBody   bool
	Trace         bool
	Security      bool
	ExcludePaths  []string
}

// DefaultLoggingConfig returns default middleware configuration
func DefaultLoggingConfig() *LoggingConfig {
	return &LoggingConfig{
		AccessLog:     true,
		ErrorLog:      true,
		PanicRecovery: true,
		RequestBody:   false,
		Trace:         false,
		Security:      true,
		ExcludePaths:  []string{"/health", "/metrics", "/ping"},
	}
}

// SetupLoggingMiddleware sets up all logging middleware
func SetupLoggingMiddleware(router *gin.Engine, logger Logger, config *LoggingConfig) {
	if config == nil {
		config = DefaultLoggingConfig()
	}
	
	// Skip logging for excluded paths
	skipPaths := config.ExcludePaths
	
	if config.PanicRecovery {
		router.Use(PanicRecoveryMiddleware(logger))
	}
	
	if config.Trace {
		router.Use(skipPathsMiddleware(TraceMiddleware(logger), skipPaths))
	}
	
	if config.AccessLog {
		router.Use(skipPathsMiddleware(AccessLogMiddleware(logger), skipPaths))
	}
	
	if config.ErrorLog {
		router.Use(skipPathsMiddleware(ErrorLogMiddleware(logger), skipPaths))
	}
	
	if config.RequestBody {
		router.Use(skipPathsMiddleware(RequestBodyLogMiddleware(logger), skipPaths))
	}
	
	if config.Security {
		router.Use(skipPathsMiddleware(SecurityLogMiddleware(logger), skipPaths))
	}
}

// skipPathsMiddleware creates middleware that skips certain paths
func skipPathsMiddleware(middleware gin.HandlerFunc, skipPaths []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		for _, skipPath := range skipPaths {
			if path == skipPath {
				c.Next()
				return
			}
		}
		middleware(c)
	}
}