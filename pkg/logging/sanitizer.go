package logging

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"
)

// Sanitizer handles sensitive data sanitization in logs
type Sanitizer struct {
	patterns        map[string]*regexp.Regexp
	sensitiveFields []string
	enabled         bool
}

// NewSanitizer creates a new sanitizer
func NewSanitizer(config *SanitizationConfig) *Sanitizer {
	if config == nil {
		return &Sanitizer{enabled: false}
	}

	s := &Sanitizer{
		patterns:        make(map[string]*regexp.Regexp),
		sensitiveFields: config.SensitiveFields,
		enabled:         config.Enabled,
	}

	if !s.enabled {
		return s
	}

	// Compile default patterns
	s.patterns["email"] = regexp.MustCompile(`[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,4}`)
	s.patterns["credit_card"] = regexp.MustCompile(`\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}`)
	s.patterns["ssn"] = regexp.MustCompile(`\d{3}-\d{2}-\d{4}`)
	s.patterns["api_key"] = regexp.MustCompile(`[A-Za-z0-9]{32,}`)
	s.patterns["phone"] = regexp.MustCompile(`\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}`)
	s.patterns["jwt"] = regexp.MustCompile(`eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*`)
	s.patterns["uuid"] = regexp.MustCompile(`[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}`)
	s.patterns["ip"] = regexp.MustCompile(`\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b`)

	// Compile custom patterns
	for _, pattern := range config.MaskPatterns {
		compiled, err := regexp.Compile(pattern)
		if err == nil {
			s.patterns[fmt.Sprintf("custom_%d", len(s.patterns))] = compiled
		}
	}

	return s
}

// SanitizeFields sanitizes a slice of fields
func (s *Sanitizer) SanitizeFields(fields []Field) []Field {
	if !s.enabled {
		return fields
	}

	sanitized := make([]Field, len(fields))
	for i, field := range fields {
		sanitized[i] = s.sanitizeField(field)
	}
	return sanitized
}

// sanitizeField sanitizes a single field
func (s *Sanitizer) sanitizeField(field Field) Field {
	// Check if field should be completely masked
	if s.shouldMaskField(field.Key) {
		return Field{
			Key:   field.Key,
			Value: "[REDACTED]",
		}
	}

	// Convert value to string for pattern matching
	strValue := fmt.Sprintf("%v", field.Value)
	sanitizedValue := s.sanitizeString(strValue)

	// Return original value if no changes were made
	if sanitizedValue == strValue {
		return field
	}

	return Field{
		Key:   field.Key,
		Value: sanitizedValue,
	}
}

// sanitizeString applies pattern-based sanitization to a string
func (s *Sanitizer) sanitizeString(value string) string {
	result := value

	for patternName, pattern := range s.patterns {
		mask := s.getMask(patternName)
		result = pattern.ReplaceAllString(result, mask)
	}

	return result
}

// shouldMaskField checks if a field should be completely masked
func (s *Sanitizer) shouldMaskField(key string) bool {
	keyLower := strings.ToLower(key)
	for _, sensitiveField := range s.sensitiveFields {
		if strings.Contains(keyLower, strings.ToLower(sensitiveField)) {
			return true
		}
	}
	return false
}

// getMask returns appropriate mask for a pattern
func (s *Sanitizer) getMask(patternName string) string {
	switch patternName {
	case "email":
		return "***@***.***"
	case "credit_card":
		return "****-****-****-****"
	case "ssn":
		return "***-**-****"
	case "api_key":
		return "[API_KEY_REDACTED]"
	case "phone":
		return "***-***-****"
	case "jwt":
		return "[JWT_REDACTED]"
	case "uuid":
		return "[UUID_REDACTED]"
	case "ip":
		return "***.***.***.***"
	default:
		return "[REDACTED]"
	}
}

// SanitizeMessage sanitizes a log message
func (s *Sanitizer) SanitizeMessage(message string) string {
	if !s.enabled {
		return message
	}
	return s.sanitizeString(message)
}

// SanitizeMap sanitizes a map of values
func (s *Sanitizer) SanitizeMap(data map[string]interface{}) map[string]interface{} {
	if !s.enabled {
		return data
	}

	sanitized := make(map[string]interface{})
	for key, value := range data {
		if s.shouldMaskField(key) {
			sanitized[key] = "[REDACTED]"
		} else {
			strValue := fmt.Sprintf("%v", value)
			sanitized[key] = s.sanitizeString(strValue)
		}
	}
	return sanitized
}

// SanitizingLogger wraps a logger with sanitization
type SanitizingLogger struct {
	logger    Logger
	sanitizer *Sanitizer
}

// NewSanitizingLogger creates a logger with sanitization
func NewSanitizingLogger(logger Logger, config *SanitizationConfig) *SanitizingLogger {
	return &SanitizingLogger{
		logger:    logger,
		sanitizer: NewSanitizer(config),
	}
}

// Implementation of Logger interface with sanitization

func (l *SanitizingLogger) Trace(msg string, fields ...Field) {
	msg = l.sanitizer.SanitizeMessage(msg)
	fields = l.sanitizer.SanitizeFields(fields)
	l.logger.Trace(msg, fields...)
}

func (l *SanitizingLogger) Debug(msg string, fields ...Field) {
	msg = l.sanitizer.SanitizeMessage(msg)
	fields = l.sanitizer.SanitizeFields(fields)
	l.logger.Debug(msg, fields...)
}

func (l *SanitizingLogger) Info(msg string, fields ...Field) {
	msg = l.sanitizer.SanitizeMessage(msg)
	fields = l.sanitizer.SanitizeFields(fields)
	l.logger.Info(msg, fields...)
}

func (l *SanitizingLogger) Warn(msg string, fields ...Field) {
	msg = l.sanitizer.SanitizeMessage(msg)
	fields = l.sanitizer.SanitizeFields(fields)
	l.logger.Warn(msg, fields...)
}

func (l *SanitizingLogger) Error(msg string, fields ...Field) {
	msg = l.sanitizer.SanitizeMessage(msg)
	fields = l.sanitizer.SanitizeFields(fields)
	l.logger.Error(msg, fields...)
}

func (l *SanitizingLogger) Fatal(msg string, fields ...Field) {
	msg = l.sanitizer.SanitizeMessage(msg)
	fields = l.sanitizer.SanitizeFields(fields)
	l.logger.Fatal(msg, fields...)
}

func (l *SanitizingLogger) Panic(msg string, fields ...Field) {
	msg = l.sanitizer.SanitizeMessage(msg)
	fields = l.sanitizer.SanitizeFields(fields)
	l.logger.Panic(msg, fields...)
}

func (l *SanitizingLogger) WithContext(ctx context.Context) Logger {
	return &SanitizingLogger{
		logger:    l.logger.WithContext(ctx),
		sanitizer: l.sanitizer,
	}
}

func (l *SanitizingLogger) WithFields(fields ...Field) Logger {
	fields = l.sanitizer.SanitizeFields(fields)
	return &SanitizingLogger{
		logger:    l.logger.WithFields(fields...),
		sanitizer: l.sanitizer,
	}
}

func (l *SanitizingLogger) WithError(err error) Logger {
	return &SanitizingLogger{
		logger:    l.logger.WithError(err),
		sanitizer: l.sanitizer,
	}
}

func (l *SanitizingLogger) WithTimer() TimerLogger {
	start := time.Now()
	return &sanitizingTimerLogger{
		SanitizingLogger: l,
		start:            start,
	}
}

func (l *SanitizingLogger) With(key string, value interface{}) Logger {
	// Sanitize the value before adding
	field := l.sanitizer.sanitizeField(Field{Key: key, Value: value})
	return &SanitizingLogger{
		logger:    l.logger.With(field.Key, field.Value),
		sanitizer: l.sanitizer,
	}
}

// sanitizingTimerLogger implements TimerLogger for sanitizing logger
type sanitizingTimerLogger struct {
	*SanitizingLogger
	start time.Time
}

func (t *sanitizingTimerLogger) Stop() {
	duration := time.Since(t.start)
	t.Info("operation completed",
		Field{Key: "duration", Value: duration},
		Field{Key: "duration_ms", Value: duration.Milliseconds()},
	)
}

// AddPattern adds a custom sanitization pattern
func (s *Sanitizer) AddPattern(name, pattern string) error {
	if !s.enabled {
		return nil
	}

	compiled, err := regexp.Compile(pattern)
	if err != nil {
		return fmt.Errorf("invalid pattern %s: %w", pattern, err)
	}

	s.patterns[name] = compiled
	return nil
}

// RemovePattern removes a sanitization pattern
func (s *Sanitizer) RemovePattern(name string) {
	delete(s.patterns, name)
}

// GetPatterns returns all registered patterns
func (s *Sanitizer) GetPatterns() map[string]string {
	patterns := make(map[string]string)
	for name, pattern := range s.patterns {
		patterns[name] = pattern.String()
	}
	return patterns
}

// IsEnabled returns whether sanitization is enabled
func (s *Sanitizer) IsEnabled() bool {
	return s.enabled
}

// SetEnabled enables or disables sanitization
func (s *Sanitizer) SetEnabled(enabled bool) {
	s.enabled = enabled
}