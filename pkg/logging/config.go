package logging

import (
	"os"
	"strconv"
	"strings"
	"time"
)

// DefaultConfig returns default logging configuration
func DefaultConfig() *LogConfig {
	return &LogConfig{
		Mode:  ModeDevelopment,
		Level: INFO,
		Output: []string{"console"},
		Console: &ConsoleConfig{
			Format:             "text",
			Colorize:           true,
			IncludeCaller:      true,
			IncludeStackTrace:  false,
			Enabled:            true,
		},
		File: &FileConfig{
			Path:       "./logs/app.log",
			MaxSize:    "100MB",
			MaxAge:     7,
			MaxBackups: 3,
			Compress:   true,
		},
		Remote: &RemoteConfig{
			Enabled:       false,
			BatchSize:     1000,
			FlushInterval: 5 * time.Second,
		},
		Debug: &DebugConfig{
			Enabled:             false,
			VerboseSQL:          false,
			RequestBodyLogging:  false,
			ResponseBodyLogging: false,
			SlowQueryThreshold:  100 * time.Millisecond,
			MemoryProfiling:     false,
			CPUProfiling:        false,
			TraceSampling:       1.0,
		},
		Production: &ProductionConfig{
			SamplingRate:      1.0,
			ErrorSamplingRate: 1.0,
			MinLevel:          WARN,
			ExcludePaths:      []string{"/health", "/metrics", "/ping"},
			AsyncLogging:      false,
			BatchSize:         1000,
			FlushInterval:     5 * time.Second,
		},
		Sanitization: &SanitizationConfig{
			SensitiveFields: []string{
				"password", "api_key", "secret", "token", "credit_card",
				"ssn", "private_key", "authorization", "x-api-key",
			},
			MaskPatterns: []string{
				`[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,4}`,           // email
				`\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}`,     // credit card
				`\d{3}-\d{2}-\d{4}`,                          // SSN
				`[A-Za-z0-9]{32,}`,                           // API keys
			},
			Enabled: true,
		},
	}
}

// LoadConfigFromEnv loads configuration from environment variables
func LoadConfigFromEnv() *LogConfig {
	config := DefaultConfig()

	// Load mode
	if mode := os.Getenv("LOG_MODE"); mode != "" {
		config.Mode = LogMode(mode)
	}

	// Load level
	if level := os.Getenv("LOG_LEVEL"); level != "" {
		config.Level = parseLogLevel(level)
	}

	// Load output
	if output := os.Getenv("LOG_OUTPUT"); output != "" {
		config.Output = strings.Split(output, ",")
	}

	// Console configuration
	if format := os.Getenv("LOG_CONSOLE_FORMAT"); format != "" {
		config.Console.Format = format
	}
	if colorize := os.Getenv("LOG_CONSOLE_COLORIZE"); colorize != "" {
		config.Console.Colorize = parseBool(colorize)
	}
	if caller := os.Getenv("LOG_CONSOLE_CALLER"); caller != "" {
		config.Console.IncludeCaller = parseBool(caller)
	}
	if enabled := os.Getenv("LOG_CONSOLE_ENABLED"); enabled != "" {
		config.Console.Enabled = parseBool(enabled)
	}

	// File configuration
	if path := os.Getenv("LOG_FILE_PATH"); path != "" {
		config.File.Path = path
	}
	if maxSize := os.Getenv("LOG_FILE_MAX_SIZE"); maxSize != "" {
		config.File.MaxSize = maxSize
	}
	if maxAge := os.Getenv("LOG_FILE_MAX_AGE"); maxAge != "" {
		if age, err := strconv.Atoi(maxAge); err == nil {
			config.File.MaxAge = age
		}
	}
	if maxBackups := os.Getenv("LOG_FILE_MAX_BACKUPS"); maxBackups != "" {
		if backups, err := strconv.Atoi(maxBackups); err == nil {
			config.File.MaxBackups = backups
		}
	}
	if compress := os.Getenv("LOG_FILE_COMPRESS"); compress != "" {
		config.File.Compress = parseBool(compress)
	}

	// Remote configuration
	if enabled := os.Getenv("LOG_REMOTE_ENABLED"); enabled != "" {
		config.Remote.Enabled = parseBool(enabled)
	}
	if endpoint := os.Getenv("LOG_REMOTE_ENDPOINT"); endpoint != "" {
		config.Remote.Endpoint = endpoint
	}
	if batchSize := os.Getenv("LOG_REMOTE_BATCH_SIZE"); batchSize != "" {
		if size, err := strconv.Atoi(batchSize); err == nil {
			config.Remote.BatchSize = size
		}
	}
	if flushInterval := os.Getenv("LOG_REMOTE_FLUSH_INTERVAL"); flushInterval != "" {
		if interval, err := time.ParseDuration(flushInterval); err == nil {
			config.Remote.FlushInterval = interval
		}
	}

	// Debug configuration
	if enabled := os.Getenv("LOG_DEBUG_ENABLED"); enabled != "" {
		config.Debug.Enabled = parseBool(enabled)
	}
	if verboseSQL := os.Getenv("LOG_DEBUG_VERBOSE_SQL"); verboseSQL != "" {
		config.Debug.VerboseSQL = parseBool(verboseSQL)
	}
	if requestBody := os.Getenv("LOG_DEBUG_REQUEST_BODY"); requestBody != "" {
		config.Debug.RequestBodyLogging = parseBool(requestBody)
	}
	if responseBody := os.Getenv("LOG_DEBUG_RESPONSE_BODY"); responseBody != "" {
		config.Debug.ResponseBodyLogging = parseBool(responseBody)
	}
	if slowQuery := os.Getenv("LOG_DEBUG_SLOW_QUERY_THRESHOLD"); slowQuery != "" {
		if threshold, err := time.ParseDuration(slowQuery); err == nil {
			config.Debug.SlowQueryThreshold = threshold
		}
	}

	// Production configuration
	if samplingRate := os.Getenv("LOG_PRODUCTION_SAMPLING_RATE"); samplingRate != "" {
		if rate, err := strconv.ParseFloat(samplingRate, 64); err == nil {
			config.Production.SamplingRate = rate
		}
	}
	if errorSamplingRate := os.Getenv("LOG_PRODUCTION_ERROR_SAMPLING_RATE"); errorSamplingRate != "" {
		if rate, err := strconv.ParseFloat(errorSamplingRate, 64); err == nil {
			config.Production.ErrorSamplingRate = rate
		}
	}
	if minLevel := os.Getenv("LOG_PRODUCTION_MIN_LEVEL"); minLevel != "" {
		config.Production.MinLevel = parseLogLevel(minLevel)
	}
	if excludePaths := os.Getenv("LOG_PRODUCTION_EXCLUDE_PATHS"); excludePaths != "" {
		config.Production.ExcludePaths = strings.Split(excludePaths, ",")
	}
	if asyncLogging := os.Getenv("LOG_PRODUCTION_ASYNC"); asyncLogging != "" {
		config.Production.AsyncLogging = parseBool(asyncLogging)
	}

	// Sanitization configuration
	if enabled := os.Getenv("LOG_SANITIZATION_ENABLED"); enabled != "" {
		config.Sanitization.Enabled = parseBool(enabled)
	}
	if sensitiveFields := os.Getenv("LOG_SANITIZATION_SENSITIVE_FIELDS"); sensitiveFields != "" {
		config.Sanitization.SensitiveFields = strings.Split(sensitiveFields, ",")
	}

	// Apply mode-specific configurations
	applyModeSpecificConfig(config)

	return config
}

// applyModeSpecificConfig applies mode-specific configurations
func applyModeSpecificConfig(config *LogConfig) {
	switch config.Mode {
	case ModeDevelopment:
		config.Level = TRACE
		config.Console.Format = "text"
		config.Console.Colorize = true
		config.Console.IncludeCaller = true
		config.File.MaxSize = "100MB"
		config.File.MaxAge = 7
		config.File.Compress = false
		config.Debug.Enabled = true
		config.Sanitization.Enabled = false

	case ModeDebug:
		config.Level = DEBUG
		config.Console.Format = "text"
		config.Console.Colorize = true
		config.Console.IncludeCaller = true
		config.Console.IncludeStackTrace = true
		config.File.MaxSize = "500MB"
		config.File.MaxAge = 30
		config.File.Compress = true
		config.Debug.Enabled = true
		config.Debug.VerboseSQL = true
		config.Debug.RequestBodyLogging = true
		config.Remote.Enabled = true
		config.Sanitization.Enabled = false

	case ModeStaging:
		config.Level = INFO
		config.Console.Format = "json"
		config.Console.Colorize = false
		config.Console.IncludeCaller = false
		config.File.MaxSize = "1GB"
		config.File.MaxAge = 30
		config.File.Compress = true
		config.Remote.Enabled = true
		config.Sanitization.Enabled = true

	case ModeProduction:
		config.Level = WARN
		config.Output = []string{"file", "remote"}
		config.Console.Enabled = false
		config.Console.Format = "json"
		config.File.Path = "/var/log/blog-api/production.log"
		config.File.MaxSize = "1GB"
		config.File.MaxAge = 90
		config.File.MaxBackups = 10
		config.File.Compress = true
		config.Remote.Enabled = true
		config.Remote.BatchSize = 1000
		config.Remote.FlushInterval = 5 * time.Second
		config.Production.AsyncLogging = true
		config.Production.SamplingRate = 0.1
		config.Production.ErrorSamplingRate = 1.0
		config.Sanitization.Enabled = true
		config.Debug.Enabled = false
	}
}

// parseLogLevel parses log level from string
func parseLogLevel(level string) LogLevel {
	switch strings.ToLower(level) {
	case "trace":
		return TRACE
	case "debug":
		return DEBUG
	case "info":
		return INFO
	case "warn", "warning":
		return WARN
	case "error":
		return ERROR
	case "fatal":
		return FATAL
	case "panic":
		return PANIC
	default:
		return INFO
	}
}

// parseBool parses boolean from string
func parseBool(value string) bool {
	switch strings.ToLower(value) {
	case "true", "1", "yes", "on":
		return true
	case "false", "0", "no", "off":
		return false
	default:
		return false
	}
}

// GetModeFromEnv returns log mode from environment
func GetModeFromEnv() LogMode {
	env := strings.ToLower(os.Getenv("ENVIRONMENT"))
	switch env {
	case "development", "dev":
		return ModeDevelopment
	case "debug":
		return ModeDebug
	case "staging", "stage":
		return ModeStaging
	case "production", "prod":
		return ModeProduction
	default:
		if mode := os.Getenv("LOG_MODE"); mode != "" {
			return LogMode(mode)
		}
		return ModeDevelopment
	}
}

// ValidateConfig validates logging configuration
func ValidateConfig(config *LogConfig) error {
	if config == nil {
		return nil
	}

	// Validate output formats
	for _, output := range config.Output {
		if output != "console" && output != "file" && output != "remote" {
			return &InvalidConfigError{Field: "output", Value: output}
		}
	}

	// Validate console format
	if config.Console != nil {
		if config.Console.Format != "text" && config.Console.Format != "json" {
			return &InvalidConfigError{Field: "console.format", Value: config.Console.Format}
		}
	}

	return nil
}

// InvalidConfigError represents invalid configuration error
type InvalidConfigError struct {
	Field string
	Value interface{}
}

func (e *InvalidConfigError) Error() string {
	return "invalid logging configuration: " + e.Field + " = " + toString(e.Value)
}

func toString(value interface{}) string {
	if s, ok := value.(string); ok {
		return s
	}
	return ""
}