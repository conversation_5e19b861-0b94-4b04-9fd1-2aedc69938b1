package logging

import (
	"context"
	"time"
)

// LogLevel represents logging level
type LogLevel int

const (
	TRACE LogLevel = iota // Most verbose - development only
	DEBUG                 // Detailed debug information
	INFO                  // General information
	WARN                  // Warning conditions
	ERROR                 // Error conditions
	FATAL                 // Fatal errors causing shutdown
	PANIC                 // Panic conditions
)

// LogMode represents logging mode
type LogMode string

const (
	ModeDevelopment LogMode = "development"
	ModeDebug       LogMode = "debug"
	ModeStaging     LogMode = "staging"
	ModeProduction  LogMode = "production"
)

// Log level configuration by mode
var LogLevelByMode = map[LogMode]LogLevel{
	ModeDevelopment: TRACE,
	ModeDebug:       DEBUG,
	ModeStaging:     INFO,
	ModeProduction:  WARN,
}

// Field represents a structured log field
type Field struct {
	Key   string
	Value interface{}
}

// Logger interface defines logging methods
type Logger interface {
	// Basic logging methods
	Trace(msg string, fields ...Field)
	Debug(msg string, fields ...Field)
	Info(msg string, fields ...Field)
	Warn(msg string, fields ...Field)
	Error(msg string, fields ...Field)
	Fatal(msg string, fields ...Field)
	Panic(msg string, fields ...Field)

	// Contextual logging
	WithContext(ctx context.Context) Logger
	WithFields(fields ...Field) Logger
	WithError(err error) Logger

	// Performance logging
	WithTimer() TimerLogger

	// Structured fields
	With(key string, value interface{}) Logger
}

// TimerLogger interface for performance logging
type TimerLogger interface {
	Logger
	Stop() // Logs duration when stopped
}

// AccessLogger interface for HTTP access logging
type AccessLogger interface {
	LogRequest(ctx context.Context, req *AccessLogRequest)
}

// ErrorLogger interface for error logging
type ErrorLogger interface {
	LogError(ctx context.Context, err error, msg string)
}

// AuditLogger interface for audit logging
type AuditLogger interface {
	LogDataChange(ctx context.Context, event *AuditEvent)
}

// SecurityLogger interface for security logging
type SecurityLogger interface {
	LogSecurityEvent(event *SecurityEvent)
	LogAuthEvent(ctx context.Context, event string, success bool, details map[string]interface{})
}

// AccessLogRequest represents an HTTP request for access logging
type AccessLogRequest struct {
	Method       string
	Path         string
	Query        string
	Status       int
	ClientIP     string
	UserAgent    string
	Latency      time.Duration
	BodySize     int
	UserID       *uint
	TenantID     *uint
	RequestID    string
	ErrorMessage string
}

// AuditEvent represents an audit log event
type AuditEvent struct {
	ID         string                 `json:"id"`
	Timestamp  time.Time              `json:"timestamp"`
	UserID     uint                   `json:"user_id"`
	TenantID   uint                   `json:"tenant_id"`
	Action     string                 `json:"action"`
	Resource   string                 `json:"resource"`
	ResourceID string                 `json:"resource_id"`
	OldValue   map[string]interface{} `json:"old_value,omitempty"`
	NewValue   map[string]interface{} `json:"new_value,omitempty"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	Result     string                 `json:"result"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// SecurityEvent represents a security log event
type SecurityEvent struct {
	Type      string                 `json:"type"`
	Severity  string                 `json:"severity"`
	SourceIP  string                 `json:"source_ip"`
	UserID    uint                   `json:"user_id"`
	Details   map[string]interface{} `json:"details"`
	Timestamp time.Time              `json:"timestamp"`
}

// LogConfig represents logging configuration
type LogConfig struct {
	Mode         LogMode
	Level        LogLevel
	Output       []string // console, file, remote
	Console      *ConsoleConfig
	File         *FileConfig
	Remote       *RemoteConfig
	Debug        *DebugConfig
	Production   *ProductionConfig
	Sanitization *SanitizationConfig
}

// ConsoleConfig represents console output configuration
type ConsoleConfig struct {
	Format             string // text, json
	Colorize           bool
	IncludeCaller      bool
	IncludeStackTrace  bool
	Enabled            bool
}

// FileConfig represents file output configuration
type FileConfig struct {
	Path       string
	MaxSize    string // e.g., "100MB"
	MaxAge     int    // days
	MaxBackups int
	Compress   bool
}

// RemoteConfig represents remote logging configuration
type RemoteConfig struct {
	Enabled       bool
	Endpoint      string
	BatchSize     int
	FlushInterval time.Duration
	Headers       map[string]string
}

// DebugConfig represents debug mode configuration
type DebugConfig struct {
	Enabled             bool
	VerboseSQL          bool
	RequestBodyLogging  bool
	ResponseBodyLogging bool
	SlowQueryThreshold  time.Duration
	MemoryProfiling     bool
	CPUProfiling        bool
	TraceSampling       float64
}

// ProductionConfig represents production mode configuration
type ProductionConfig struct {
	SamplingRate      float64 // 0.1 = 10% of logs
	ErrorSamplingRate float64 // 1.0 = 100% of errors
	MinLevel          LogLevel
	ExcludePaths      []string // Health checks, metrics
	AsyncLogging      bool
	BatchSize         int
	FlushInterval     time.Duration
}

// SanitizationConfig represents data sanitization configuration
type SanitizationConfig struct {
	SensitiveFields []string
	MaskPatterns    []string
	Enabled         bool
}

// LogEntry represents a log entry for async processing
type LogEntry struct {
	Level     LogLevel
	Message   string
	Fields    []Field
	Time      time.Time
	Caller    string
	RequestID string
}

// String returns the string representation of LogLevel
func (l LogLevel) String() string {
	switch l {
	case TRACE:
		return "trace"
	case DEBUG:
		return "debug"
	case INFO:
		return "info"
	case WARN:
		return "warn"
	case ERROR:
		return "error"
	case FATAL:
		return "fatal"
	case PANIC:
		return "panic"
	default:
		return "unknown"
	}
}

// String returns the string representation of LogMode
func (m LogMode) String() string {
	return string(m)
}