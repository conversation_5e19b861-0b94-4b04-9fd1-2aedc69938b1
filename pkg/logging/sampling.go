package logging

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// SamplingLogger implements log sampling for production environments
type SamplingLogger struct {
	logger            Logger
	samplingRate      float64
	errorSamplingRate float64
	rand              *rand.Rand
	mu                sync.Mutex
}

// NewSamplingLogger creates a new sampling logger
func NewSamplingLogger(logger Logger, samplingRate, errorSamplingRate float64) *SamplingLogger {
	return &SamplingLogger{
		logger:            logger,
		samplingRate:      samplingRate,
		errorSamplingRate: errorSamplingRate,
		rand:              rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// shouldSample determines if a log entry should be sampled
func (s *SamplingLogger) shouldSample(level LogLevel) bool {
	s.mu.Lock()
	defer s.mu.Unlock()

	var rate float64
	switch level {
	case ERROR, FATAL, PANIC:
		rate = s.errorSamplingRate
	default:
		rate = s.samplingRate
	}

	return s.rand.Float64() < rate
}

// Implementation of Logger interface with sampling

func (s *SamplingLogger) Trace(msg string, fields ...Field) {
	if s.shouldSample(TRACE) {
		s.logger.Trace(msg, fields...)
	}
}

func (s *SamplingLogger) Debug(msg string, fields ...Field) {
	if s.shouldSample(DEBUG) {
		s.logger.Debug(msg, fields...)
	}
}

func (s *SamplingLogger) Info(msg string, fields ...Field) {
	if s.shouldSample(INFO) {
		s.logger.Info(msg, fields...)
	}
}

func (s *SamplingLogger) Warn(msg string, fields ...Field) {
	if s.shouldSample(WARN) {
		s.logger.Warn(msg, fields...)
	}
}

func (s *SamplingLogger) Error(msg string, fields ...Field) {
	if s.shouldSample(ERROR) {
		s.logger.Error(msg, fields...)
	}
}

func (s *SamplingLogger) Fatal(msg string, fields ...Field) {
	// Fatal logs are always logged
	s.logger.Fatal(msg, fields...)
}

func (s *SamplingLogger) Panic(msg string, fields ...Field) {
	// Panic logs are always logged
	s.logger.Panic(msg, fields...)
}

func (s *SamplingLogger) WithContext(ctx context.Context) Logger {
	return &SamplingLogger{
		logger:            s.logger.WithContext(ctx),
		samplingRate:      s.samplingRate,
		errorSamplingRate: s.errorSamplingRate,
		rand:              s.rand,
	}
}

func (s *SamplingLogger) WithFields(fields ...Field) Logger {
	return &SamplingLogger{
		logger:            s.logger.WithFields(fields...),
		samplingRate:      s.samplingRate,
		errorSamplingRate: s.errorSamplingRate,
		rand:              s.rand,
	}
}

func (s *SamplingLogger) WithError(err error) Logger {
	return &SamplingLogger{
		logger:            s.logger.WithError(err),
		samplingRate:      s.samplingRate,
		errorSamplingRate: s.errorSamplingRate,
		rand:              s.rand,
	}
}

func (s *SamplingLogger) WithTimer() TimerLogger {
	start := time.Now()
	return &samplingTimerLogger{
		SamplingLogger: s,
		start:          start,
	}
}

func (s *SamplingLogger) With(key string, value interface{}) Logger {
	return &SamplingLogger{
		logger:            s.logger.With(key, value),
		samplingRate:      s.samplingRate,
		errorSamplingRate: s.errorSamplingRate,
		rand:              s.rand,
	}
}

// samplingTimerLogger implements TimerLogger for sampling logger
type samplingTimerLogger struct {
	*SamplingLogger
	start time.Time
}

func (t *samplingTimerLogger) Stop() {
	duration := time.Since(t.start)
	if t.shouldSample(INFO) {
		t.logger.Info("operation completed",
			Field{Key: "duration", Value: duration},
			Field{Key: "duration_ms", Value: duration.Milliseconds()},
		)
	}
}

// SetSamplingRate updates the sampling rate
func (s *SamplingLogger) SetSamplingRate(rate float64) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.samplingRate = rate
}

// SetErrorSamplingRate updates the error sampling rate
func (s *SamplingLogger) SetErrorSamplingRate(rate float64) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.errorSamplingRate = rate
}

// GetSamplingRates returns current sampling rates
func (s *SamplingLogger) GetSamplingRates() (float64, float64) {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.samplingRate, s.errorSamplingRate
}

// AdaptiveSamplingLogger implements adaptive sampling based on log volume
type AdaptiveSamplingLogger struct {
	logger            Logger
	baseSamplingRate  float64
	errorSamplingRate float64
	maxRate           float64
	minRate           float64
	windowSize        time.Duration
	logCounts         map[LogLevel]int
	windowStart       time.Time
	mu                sync.RWMutex
}

// NewAdaptiveSamplingLogger creates an adaptive sampling logger
func NewAdaptiveSamplingLogger(logger Logger, baseSamplingRate, errorSamplingRate float64) *AdaptiveSamplingLogger {
	return &AdaptiveSamplingLogger{
		logger:            logger,
		baseSamplingRate:  baseSamplingRate,
		errorSamplingRate: errorSamplingRate,
		maxRate:           1.0,
		minRate:           0.01,
		windowSize:        time.Minute,
		logCounts:         make(map[LogLevel]int),
		windowStart:       time.Now(),
	}
}

// updateCounts updates log counts and adjusts sampling rate
func (a *AdaptiveSamplingLogger) updateCounts(level LogLevel) {
	a.mu.Lock()
	defer a.mu.Unlock()

	now := time.Now()
	if now.Sub(a.windowStart) > a.windowSize {
		// Reset window
		a.logCounts = make(map[LogLevel]int)
		a.windowStart = now
	}

	a.logCounts[level]++
}

// getSamplingRate calculates adaptive sampling rate
func (a *AdaptiveSamplingLogger) getSamplingRate(level LogLevel) float64 {
	a.mu.RLock()
	defer a.mu.RUnlock()

	if level == ERROR || level == FATAL || level == PANIC {
		return a.errorSamplingRate
	}

	// Calculate total logs in window
	totalLogs := 0
	for _, count := range a.logCounts {
		totalLogs += count
	}

	// Adaptive rate based on volume
	if totalLogs > 10000 { // High volume
		return a.minRate
	} else if totalLogs > 1000 { // Medium volume
		return a.baseSamplingRate * 0.5
	} else { // Low volume
		return a.baseSamplingRate
	}
}

// shouldSample determines if a log should be sampled adaptively
func (a *AdaptiveSamplingLogger) shouldSample(level LogLevel) bool {
	a.updateCounts(level)
	rate := a.getSamplingRate(level)
	return rand.Float64() < rate
}

// Implementation of Logger interface with adaptive sampling

func (a *AdaptiveSamplingLogger) Trace(msg string, fields ...Field) {
	if a.shouldSample(TRACE) {
		a.logger.Trace(msg, fields...)
	}
}

func (a *AdaptiveSamplingLogger) Debug(msg string, fields ...Field) {
	if a.shouldSample(DEBUG) {
		a.logger.Debug(msg, fields...)
	}
}

func (a *AdaptiveSamplingLogger) Info(msg string, fields ...Field) {
	if a.shouldSample(INFO) {
		a.logger.Info(msg, fields...)
	}
}

func (a *AdaptiveSamplingLogger) Warn(msg string, fields ...Field) {
	if a.shouldSample(WARN) {
		a.logger.Warn(msg, fields...)
	}
}

func (a *AdaptiveSamplingLogger) Error(msg string, fields ...Field) {
	if a.shouldSample(ERROR) {
		a.logger.Error(msg, fields...)
	}
}

func (a *AdaptiveSamplingLogger) Fatal(msg string, fields ...Field) {
	a.logger.Fatal(msg, fields...)
}

func (a *AdaptiveSamplingLogger) Panic(msg string, fields ...Field) {
	a.logger.Panic(msg, fields...)
}

func (a *AdaptiveSamplingLogger) WithContext(ctx context.Context) Logger {
	return &AdaptiveSamplingLogger{
		logger:            a.logger.WithContext(ctx),
		baseSamplingRate:  a.baseSamplingRate,
		errorSamplingRate: a.errorSamplingRate,
		maxRate:           a.maxRate,
		minRate:           a.minRate,
		windowSize:        a.windowSize,
		logCounts:         a.logCounts,
		windowStart:       a.windowStart,
	}
}

func (a *AdaptiveSamplingLogger) WithFields(fields ...Field) Logger {
	return &AdaptiveSamplingLogger{
		logger:            a.logger.WithFields(fields...),
		baseSamplingRate:  a.baseSamplingRate,
		errorSamplingRate: a.errorSamplingRate,
		maxRate:           a.maxRate,
		minRate:           a.minRate,
		windowSize:        a.windowSize,
		logCounts:         a.logCounts,
		windowStart:       a.windowStart,
	}
}

func (a *AdaptiveSamplingLogger) WithError(err error) Logger {
	return &AdaptiveSamplingLogger{
		logger:            a.logger.WithError(err),
		baseSamplingRate:  a.baseSamplingRate,
		errorSamplingRate: a.errorSamplingRate,
		maxRate:           a.maxRate,
		minRate:           a.minRate,
		windowSize:        a.windowSize,
		logCounts:         a.logCounts,
		windowStart:       a.windowStart,
	}
}

func (a *AdaptiveSamplingLogger) WithTimer() TimerLogger {
	start := time.Now()
	return &adaptiveTimerLogger{
		AdaptiveSamplingLogger: a,
		start:                  start,
	}
}

func (a *AdaptiveSamplingLogger) With(key string, value interface{}) Logger {
	return &AdaptiveSamplingLogger{
		logger:            a.logger.With(key, value),
		baseSamplingRate:  a.baseSamplingRate,
		errorSamplingRate: a.errorSamplingRate,
		maxRate:           a.maxRate,
		minRate:           a.minRate,
		windowSize:        a.windowSize,
		logCounts:         a.logCounts,
		windowStart:       a.windowStart,
	}
}

// adaptiveTimerLogger implements TimerLogger for adaptive sampling logger
type adaptiveTimerLogger struct {
	*AdaptiveSamplingLogger
	start time.Time
}

func (t *adaptiveTimerLogger) Stop() {
	duration := time.Since(t.start)
	if t.shouldSample(INFO) {
		t.logger.Info("operation completed",
			Field{Key: "duration", Value: duration},
			Field{Key: "duration_ms", Value: duration.Milliseconds()},
		)
	}
}

// GetStats returns adaptive sampling statistics
func (a *AdaptiveSamplingLogger) GetStats() map[string]interface{} {
	a.mu.RLock()
	defer a.mu.RUnlock()

	stats := map[string]interface{}{
		"base_sampling_rate":  a.baseSamplingRate,
		"error_sampling_rate": a.errorSamplingRate,
		"window_size":         a.windowSize,
		"window_start":        a.windowStart,
		"log_counts":          a.logCounts,
	}

	// Calculate current rates
	for level := TRACE; level <= PANIC; level++ {
		stats[fmt.Sprintf("current_%s_rate", level.String())] = a.getSamplingRate(level)
	}

	return stats
}