package logging

import (
	"fmt"
	"os"
)

// Factory creates logger instances
type Factory struct {
	defaultConfig *LogConfig
}

// NewFactory creates a new logger factory
func NewFactory(config *LogConfig) *Factory {
	if config == nil {
		config = LoadConfigFromEnv()
	}
	return &Factory{
		defaultConfig: config,
	}
}

// CreateLogger creates a new logger instance
func (f *Factory) CreateLogger() (Logger, error) {
	return f.CreateLoggerWithConfig(f.defaultConfig)
}

// CreateLoggerWithConfig creates a logger with specific configuration
func (f *Factory) CreateLoggerWithConfig(config *LogConfig) (Logger, error) {
	if config == nil {
		config = f.defaultConfig
	}

	// Validate configuration
	if err := ValidateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid logging configuration: %w", err)
	}

	// Create Zap logger
	logger, err := NewZapLogger(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create zap logger: %w", err)
	}

	return logger, nil
}

// CreateAccessLogger creates an access logger
func (f *Factory) CreateAccessLogger() (AccessLogger, error) {
	logger, err := f.CreateLogger()
	if err != nil {
		return nil, err
	}
	return NewAccessLogger(logger), nil
}

// CreateErrorLogger creates an error logger
func (f *Factory) CreateErrorLogger() (ErrorLogger, error) {
	logger, err := f.CreateLogger()
	if err != nil {
		return nil, err
	}
	return NewErrorLogger(logger), nil
}

// CreateAuditLogger creates an audit logger
func (f *Factory) CreateAuditLogger() (AuditLogger, error) {
	logger, err := f.CreateLogger()
	if err != nil {
		return nil, err
	}
	return NewAuditLogger(logger), nil
}

// CreateSecurityLogger creates a security logger
func (f *Factory) CreateSecurityLogger() (SecurityLogger, error) {
	logger, err := f.CreateLogger()
	if err != nil {
		return nil, err
	}
	return NewSecurityLogger(logger), nil
}

// Global factory instance
var globalFactory *Factory

// InitGlobalFactory initializes the global factory
func InitGlobalFactory(config *LogConfig) error {
	globalFactory = NewFactory(config)
	return nil
}

// GetGlobalFactory returns the global factory
func GetGlobalFactory() *Factory {
	if globalFactory == nil {
		// Initialize with default config
		globalFactory = NewFactory(LoadConfigFromEnv())
	}
	return globalFactory
}

// Package-level convenience functions

// NewLogger creates a new logger with default configuration
func NewLogger() (Logger, error) {
	return GetGlobalFactory().CreateLogger()
}

// NewLoggerWithMode creates a logger with specific mode
func NewLoggerWithMode(mode LogMode) (Logger, error) {
	config := LoadConfigFromEnv()
	config.Mode = mode
	applyModeSpecificConfig(config)
	return GetGlobalFactory().CreateLoggerWithConfig(config)
}

// MustNewLogger creates a new logger or panics
func MustNewLogger() Logger {
	logger, err := NewLogger()
	if err != nil {
		panic(fmt.Sprintf("failed to create logger: %v", err))
	}
	return logger
}

// MustNewLoggerWithMode creates a logger with mode or panics
func MustNewLoggerWithMode(mode LogMode) Logger {
	logger, err := NewLoggerWithMode(mode)
	if err != nil {
		panic(fmt.Sprintf("failed to create logger with mode %s: %v", mode, err))
	}
	return logger
}

// SetupLogging initializes logging for the application
func SetupLogging() (Logger, error) {
	// Load configuration from environment
	config := LoadConfigFromEnv()

	// Create log directory if using file output
	if contains(config.Output, "file") && config.File != nil {
		logDir := getLogDir(config.File.Path)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			return nil, fmt.Errorf("failed to create log directory %s: %w", logDir, err)
		}
	}

	// Initialize global factory
	if err := InitGlobalFactory(config); err != nil {
		return nil, fmt.Errorf("failed to initialize global factory: %w", err)
	}

	// Create main logger
	logger, err := GetGlobalFactory().CreateLogger()
	if err != nil {
		return nil, fmt.Errorf("failed to create main logger: %w", err)
	}

	// Log startup information
	logger.Info("logging system initialized",
		Field{Key: "mode", Value: config.Mode},
		Field{Key: "level", Value: config.Level.String()},
		Field{Key: "output", Value: config.Output},
	)

	return logger, nil
}

// contains checks if slice contains string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// getLogDir extracts directory from log file path
func getLogDir(path string) string {
	for i := len(path) - 1; i >= 0; i-- {
		if path[i] == '/' || path[i] == '\\' {
			return path[:i]
		}
	}
	return "."
}

// GetDefaultLogger returns a default logger (for backwards compatibility)
func GetDefaultLogger() Logger {
	return MustNewLogger()
}