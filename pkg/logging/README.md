# Logging Package

A comprehensive logging system for Blog API v3 with structured logging, multi-environment support, and production-ready features.

## Features

- **Structured Logging**: JSON and text formats with rich metadata
- **Multi-Environment Support**: Development, debug, staging, and production modes
- **Performance Optimized**: Async logging, sampling, and efficient processing
- **Security**: Data sanitization and sensitive information protection
- **Debug Features**: Request tracing, memory profiling, and debug endpoints
- **Specialized Loggers**: Access, error, audit, and security logging
- **Gin Integration**: Comprehensive middleware for HTTP logging

## Quick Start

```go
package main

import (
    "context"
    "github.com/gin-gonic/gin"
    "github.com/tranthanhloi/wn-api-v3/pkg/logging"
)

func main() {
    // Setup logging
    appLogger, err := logging.LoggerSetup("blog-api", "3.0.0")
    if err != nil {
        panic(err)
    }
    defer appLogger.Close()

    // Create Gin router
    router := gin.New()
    
    // Integrate logging with Gin
    logging.IntegrateWithGin(router, appLogger)
    
    // Use logger in handlers
    router.GET("/hello", func(c *gin.Context) {
        logger := appLogger.GetLogger().WithContext(c.Request.Context())
        logger.Info("hello endpoint called")
        c.JSON(200, gin.H{"message": "Hello World"})
    })
    
    router.Run(":8080")
}
```

## Configuration

### Environment Variables

```bash
# Basic configuration
LOG_MODE=development          # development, debug, staging, production
LOG_LEVEL=info               # trace, debug, info, warn, error, fatal, panic
LOG_OUTPUT=console,file      # console, file, remote

# Console output
LOG_CONSOLE_FORMAT=text      # text, json
LOG_CONSOLE_COLORIZE=true
LOG_CONSOLE_CALLER=true

# File output
LOG_FILE_PATH=./logs/app.log
LOG_FILE_MAX_SIZE=100MB
LOG_FILE_MAX_AGE=7
LOG_FILE_COMPRESS=true

# Remote logging
LOG_REMOTE_ENABLED=false
LOG_REMOTE_ENDPOINT=https://logs.example.com
LOG_REMOTE_BATCH_SIZE=1000

# Debug mode
LOG_DEBUG_ENABLED=true
LOG_DEBUG_VERBOSE_SQL=true
LOG_DEBUG_REQUEST_BODY=true
LOG_DEBUG_RESPONSE_BODY=true

# Production optimizations
LOG_PRODUCTION_SAMPLING_RATE=0.1
LOG_PRODUCTION_ERROR_SAMPLING_RATE=1.0
LOG_PRODUCTION_ASYNC=true

# Data sanitization
LOG_SANITIZATION_ENABLED=true
LOG_SANITIZATION_SENSITIVE_FIELDS=password,api_key,token
```

### Configuration Modes

#### Development Mode
- **Level**: TRACE
- **Output**: Console + File
- **Format**: Human-readable text with colors
- **Features**: All debug features enabled
- **Sanitization**: Disabled

#### Debug Mode
- **Level**: DEBUG
- **Output**: Console + File + Remote
- **Format**: Text with stack traces
- **Features**: SQL logging, request tracing, profiling
- **Sanitization**: Disabled

#### Staging Mode
- **Level**: INFO
- **Output**: File + Remote
- **Format**: JSON
- **Features**: Limited debug features
- **Sanitization**: Enabled

#### Production Mode
- **Level**: WARN
- **Output**: File + Remote only
- **Format**: JSON
- **Features**: Async logging, sampling, sanitization
- **Sanitization**: Enabled

## Usage Examples

### Basic Logging

```go
logger := logging.MustNewLogger()

// Simple logging
logger.Info("User logged in", 
    logging.Field{Key: "user_id", Value: 123},
    logging.Field{Key: "ip", Value: "***********"},
)

// With context
ctx := context.WithValue(context.Background(), "request_id", "req_123")
logger.WithContext(ctx).Info("Processing request")

// With error
err := errors.New("database connection failed")
logger.WithError(err).Error("Failed to connect to database")
```

### Structured Logging

```go
// Using fields
logger.Info("User action",
    logging.Field{Key: "user_id", Value: 123},
    logging.Field{Key: "action", Value: "create_post"},
    logging.Field{Key: "post_id", Value: 456},
    logging.Field{Key: "duration", Value: time.Duration(250 * time.Millisecond)},
)

// Using With methods
logger.With("user_id", 123).
    With("action", "create_post").
    Info("User created post")
```

### Performance Logging

```go
// Timer logging
timer := logger.WithTimer()
defer timer.Stop()

// Your operation here
performExpensiveOperation()
// Timer will automatically log duration when Stop() is called
```

### Specialized Loggers

#### Access Logger
```go
accessLogger := appLogger.GetAccessLogger()

req := &logging.AccessLogRequest{
    Method:    "GET",
    Path:      "/api/posts",
    Status:    200,
    ClientIP:  "***********",
    Latency:   50 * time.Millisecond,
    UserID:    &userID,
    TenantID:  &tenantID,
}

accessLogger.LogRequest(ctx, req)
```

#### Error Logger
```go
errorLogger := appLogger.GetErrorLogger()
errorLogger.LogError(ctx, err, "Failed to process request")
```

#### Audit Logger
```go
auditLogger := appLogger.GetAuditLogger()

event := &logging.AuditEvent{
    ID:         "audit_123",
    UserID:     123,
    TenantID:   456,
    Action:     "user.update",
    Resource:   "user_profile",
    ResourceID: "123",
    Result:     "success",
    Timestamp:  time.Now(),
}

auditLogger.LogDataChange(ctx, event)
```

#### Security Logger
```go
securityLogger := appLogger.GetSecurityLogger()

event := &logging.SecurityEvent{
    Type:     "failed_login",
    Severity: "high",
    SourceIP: "*************",
    UserID:   123,
    Details: map[string]interface{}{
        "attempts": 5,
        "reason":   "invalid_password",
    },
    Timestamp: time.Now(),
}

securityLogger.LogSecurityEvent(event)
```

### Debug Features

#### Request Tracing
```go
tracer := appLogger.GetTracer()

// Start trace
ctx = tracer.StartTrace(ctx, "create_user")

// Add events
tracer.AddEvent(ctx, "validate_input", map[string]interface{}{
    "validation_time": "5ms",
})

tracer.AddEvent(ctx, "save_to_database", map[string]interface{}{
    "db_time": "50ms",
})

// End trace
tracer.EndTrace(ctx)
```

#### Memory Profiling
```go
debugLogger := appLogger.GetDebugLogger()

// Log memory stats
debugLogger.LogMemoryStats(ctx)

// Log goroutine profile
debugLogger.LogGoroutineProfile(ctx)
```

### Gin Middleware Integration

```go
router := gin.New()

// Setup all logging middleware
appLogger.SetupMiddleware(router)

// Or setup individual middleware
router.Use(logging.AccessLogMiddleware(appLogger.GetLogger()))
router.Use(logging.ErrorLogMiddleware(appLogger.GetLogger()))
router.Use(logging.PanicRecoveryMiddleware(appLogger.GetLogger()))
```

### Context-Aware Logging

```go
// Create logging context
logCtx := logging.WithContext(ctx).
    WithField("user_id", 123).
    WithField("tenant_id", 456)

// Log with context
logCtx.Logger().Info("User operation completed")

// Log operation with timing
err := logCtx.LogOperation("create_post", func() error {
    return createPost(postData)
})
```

### Production Features

#### Async Logging
```go
config := &logging.LogConfig{
    Mode: logging.ModeProduction,
    Production: &logging.ProductionConfig{
        AsyncLogging:  true,
        BatchSize:     1000,
        FlushInterval: 5 * time.Second,
    },
}

logger, err := logging.NewZapLogger(config)
asyncLogger := logging.NewAsyncLogger(logger, 1000, 5*time.Second)
```

#### Sampling
```go
// Fixed sampling (10% of logs, 100% of errors)
samplingLogger := logging.NewSamplingLogger(logger, 0.1, 1.0)

// Adaptive sampling
adaptiveLogger := logging.NewAdaptiveSamplingLogger(logger, 0.1, 1.0)
```

#### Data Sanitization
```go
config := &logging.SanitizationConfig{
    Enabled: true,
    SensitiveFields: []string{"password", "api_key", "token"},
    MaskPatterns: []string{
        `[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,4}`, // emails
        `\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}`, // credit cards
    },
}

sanitizingLogger := logging.NewSanitizingLogger(logger, config)
```

## Debug Endpoints

When debug mode is enabled, the following endpoints are available:

- `GET /debug/memory` - Memory statistics
- `GET /debug/goroutines` - Goroutine information
- `GET /debug/goroutines?stack=true` - Goroutine stack traces
- `GET /debug/config` - Current configuration
- `GET /debug/logger/stats` - Logger statistics
- `POST /debug/gc` - Force garbage collection
- `GET /debug/health` - Health check

## Best Practices

### 1. Use Structured Logging
```go
// Good
logger.Info("User login successful",
    logging.Field{Key: "user_id", Value: userID},
    logging.Field{Key: "ip", Value: clientIP},
    logging.Field{Key: "duration", Value: authDuration},
)

// Bad
logger.Info(fmt.Sprintf("User %d logged in from %s", userID, clientIP))
```

### 2. Include Context Information
```go
// Always use context-aware logging
logger := appLogger.GetLogger().WithContext(ctx)
logger.Info("Processing request")
```

### 3. Log at Appropriate Levels
```go
// TRACE: Very detailed debugging (development only)
logger.Trace("Entering function", logging.Field{Key: "params", Value: params})

// DEBUG: Detailed debugging information
logger.Debug("Cache miss", logging.Field{Key: "key", Value: cacheKey})

// INFO: General information about program execution
logger.Info("User registered", logging.Field{Key: "user_id", Value: userID})

// WARN: Warning conditions
logger.Warn("API rate limit approached", logging.Field{Key: "usage", Value: "90%"})

// ERROR: Error conditions
logger.Error("Database connection failed", logging.Field{Key: "error", Value: err})

// FATAL: Fatal errors causing shutdown
logger.Fatal("Failed to initialize database", logging.Field{Key: "error", Value: err})
```

### 4. Performance Considerations
```go
// Use conditional logging for expensive operations
if logger.Core().Enabled(zapcore.DebugLevel) {
    logger.Debug("Expensive debug info",
        logging.Field{Key: "data", Value: expensiveToCompute()},
    )
}

// Use timers for performance monitoring
timer := logger.WithTimer()
defer timer.Stop()
```

### 5. Error Handling
```go
// Include error context
logger.WithError(err).Error("Failed to process request",
    logging.Field{Key: "user_id", Value: userID},
    logging.Field{Key: "request_id", Value: requestID},
)
```

## Integration with Other Components

### Database Integration
```go
// Log slow queries
debugLogger.LogSQLQuery(ctx, query, args, duration, err)
```

### Cache Integration
```go
// Log cache operations
logging.LogCacheOperation(logger, ctx, "get", key, hit, duration)
```

### Service Calls
```go
// Log service interactions
logging.LogServiceCall(logger, ctx, "user-service", "GetProfile", duration, err)
```

## Troubleshooting

### Common Issues

1. **Logs not appearing**: Check log level and output configuration
2. **Performance issues**: Enable async logging and sampling in production
3. **Missing context**: Ensure proper context propagation
4. **Sensitive data in logs**: Enable sanitization
5. **High memory usage**: Check for log retention and rotation settings

### Debug Commands

```bash
# Check memory usage
curl http://localhost:8080/debug/memory

# Check goroutines
curl http://localhost:8080/debug/goroutines

# Force garbage collection
curl -X POST http://localhost:8080/debug/gc

# Check configuration
curl http://localhost:8080/debug/config
```

## Dependencies

- `go.uber.org/zap` - High-performance logging
- `gopkg.in/natefinch/lumberjack.v2` - Log rotation
- `github.com/gin-gonic/gin` - HTTP framework integration
- `github.com/google/uuid` - UUID generation

## License

This package is part of the Blog API v3 project and follows the same license terms.