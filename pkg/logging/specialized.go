package logging

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// accessLogger implements AccessLogger interface
type accessLogger struct {
	logger Logger
}

// NewAccessLogger creates a new access logger
func NewAccessLogger(logger Logger) AccessLogger {
	return &accessLogger{
		logger: logger,
	}
}

func (a *accessLogger) LogRequest(ctx context.Context, req *AccessLogRequest) {
	logger := a.logger.WithContext(ctx)

	fields := []Field{
		{Key: "method", Value: req.Method},
		{Key: "path", Value: req.Path},
		{Key: "query", Value: req.Query},
		{Key: "status", Value: req.Status},
		{Key: "ip", Value: req.ClientIP},
		{Key: "latency", Value: req.Latency},
		{Key: "latency_ms", Value: req.Latency.Milliseconds()},
		{Key: "user_agent", Value: req.UserAgent},
		{Key: "body_size", Value: req.BodySize},
		{Key: "request_id", Value: req.RequestID},
	}

	// Add user context if available
	if req.UserID != nil {
		fields = append(fields, Field{Key: "user_id", Value: *req.UserID})
	}

	if req.TenantID != nil {
		fields = append(fields, Field{Key: "tenant_id", Value: *req.TenantID})
	}

	// Log based on status code
	switch {
	case req.Status >= 500:
		if req.ErrorMessage != "" {
			fields = append(fields, Field{Key: "error", Value: req.ErrorMessage})
		}
		logger.Error("server error", fields...)
	case req.Status >= 400:
		logger.Warn("client error", fields...)
	case req.Status >= 300:
		logger.Info("redirection", fields...)
	default:
		logger.Info("request completed", fields...)
	}
}

// errorLogger implements ErrorLogger interface
type errorLogger struct {
	logger Logger
}

// NewErrorLogger creates a new error logger
func NewErrorLogger(logger Logger) ErrorLogger {
	return &errorLogger{
		logger: logger,
	}
}

func (e *errorLogger) LogError(ctx context.Context, err error, msg string) {
	logger := e.logger.WithContext(ctx)

	fields := []Field{
		{Key: "error", Value: err.Error()},
		{Key: "error_type", Value: fmt.Sprintf("%T", err)},
	}

	// Add stack trace for internal errors
	if stackErr, ok := err.(interface{ StackTrace() string }); ok {
		fields = append(fields, Field{Key: "stack_trace", Value: stackErr.StackTrace()})
	}

	// Add context from error
	if ctxErr, ok := err.(interface{ Context() map[string]interface{} }); ok {
		fields = append(fields, Field{Key: "error_context", Value: ctxErr.Context()})
	}

	// Log based on error type
	switch err.(type) {
	case *ValidationError:
		logger.Warn(msg, fields...)
	case *NotFoundError:
		logger.Info(msg, fields...)
	case *AuthError:
		logger.Warn(msg, fields...)
	default:
		logger.Error(msg, fields...)
	}
}

// auditLogger implements AuditLogger interface
type auditLogger struct {
	logger Logger
}

// NewAuditLogger creates a new audit logger
func NewAuditLogger(logger Logger) AuditLogger {
	return &auditLogger{
		logger: logger,
	}
}

func (a *auditLogger) LogDataChange(ctx context.Context, event *AuditEvent) {
	logger := a.logger.WithContext(ctx)

	fields := []Field{
		{Key: "audit_id", Value: event.ID},
		{Key: "action", Value: event.Action},
		{Key: "resource", Value: event.Resource},
		{Key: "resource_id", Value: event.ResourceID},
		{Key: "user_id", Value: event.UserID},
		{Key: "tenant_id", Value: event.TenantID},
		{Key: "result", Value: event.Result},
		{Key: "ip_address", Value: event.IPAddress},
		{Key: "user_agent", Value: event.UserAgent},
		{Key: "timestamp", Value: event.Timestamp},
	}

	if event.OldValue != nil {
		fields = append(fields, Field{Key: "old_value", Value: event.OldValue})
	}

	if event.NewValue != nil {
		fields = append(fields, Field{Key: "new_value", Value: event.NewValue})
	}

	if event.Metadata != nil {
		fields = append(fields, Field{Key: "metadata", Value: event.Metadata})
	}

	logger.Info("audit event", fields...)
}

// securityLogger implements SecurityLogger interface
type securityLogger struct {
	logger Logger
}

// NewSecurityLogger creates a new security logger
func NewSecurityLogger(logger Logger) SecurityLogger {
	return &securityLogger{
		logger: logger,
	}
}

func (s *securityLogger) LogSecurityEvent(event *SecurityEvent) {
	fields := []Field{
		{Key: "event_type", Value: event.Type},
		{Key: "severity", Value: event.Severity},
		{Key: "source_ip", Value: event.SourceIP},
		{Key: "user_id", Value: event.UserID},
		{Key: "details", Value: event.Details},
		{Key: "timestamp", Value: event.Timestamp},
	}

	switch event.Severity {
	case "critical":
		s.logger.Error("critical security event", fields...)
	case "high":
		s.logger.Warn("high severity security event", fields...)
	case "medium":
		s.logger.Info("medium severity security event", fields...)
	default:
		s.logger.Info("security event", fields...)
	}
}

func (s *securityLogger) LogAuthEvent(ctx context.Context, event string, success bool, details map[string]interface{}) {
	logger := s.logger.WithContext(ctx)

	fields := []Field{
		{Key: "auth_event", Value: event},
		{Key: "success", Value: success},
		{Key: "details", Value: details},
		{Key: "timestamp", Value: time.Now()},
	}

	// Add IP address from context
	if ip, ok := ctx.Value("client_ip").(string); ok {
		fields = append(fields, Field{Key: "ip_address", Value: ip})
	}

	if success {
		logger.Info("authentication event", fields...)
	} else {
		logger.Warn("authentication failure", fields...)
	}
}

// Custom error types for better error handling
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation error on field %s: %s", e.Field, e.Message)
}

type NotFoundError struct {
	Resource string
	ID       string
}

func (e *NotFoundError) Error() string {
	return fmt.Sprintf("resource %s with id %s not found", e.Resource, e.ID)
}

type AuthError struct {
	Type    string
	Message string
}

func (e *AuthError) Error() string {
	return fmt.Sprintf("authentication error [%s]: %s", e.Type, e.Message)
}

// Helper functions for creating structured log entries

// LogRequestStart logs the start of a request
func LogRequestStart(logger Logger, ctx context.Context, method, path string) {
	logger.WithContext(ctx).Info("request started",
		Field{Key: "method", Value: method},
		Field{Key: "path", Value: path},
		Field{Key: "timestamp", Value: time.Now()},
	)
}

// LogRequestEnd logs the end of a request
func LogRequestEnd(logger Logger, ctx context.Context, method, path string, status int, duration time.Duration) {
	logger.WithContext(ctx).Info("request completed",
		Field{Key: "method", Value: method},
		Field{Key: "path", Value: path},
		Field{Key: "status", Value: status},
		Field{Key: "duration", Value: duration},
		Field{Key: "duration_ms", Value: duration.Milliseconds()},
	)
}

// LogDatabaseQuery logs a database query
func LogDatabaseQuery(logger Logger, ctx context.Context, query string, duration time.Duration, err error) {
	fields := []Field{
		{Key: "query", Value: query},
		{Key: "duration", Value: duration},
		{Key: "duration_ms", Value: duration.Milliseconds()},
	}

	if err != nil {
		fields = append(fields, Field{Key: "error", Value: err.Error()})
		logger.WithContext(ctx).Error("database query failed", fields...)
	} else {
		logger.WithContext(ctx).Debug("database query executed", fields...)
	}
}

// LogServiceCall logs a service call
func LogServiceCall(logger Logger, ctx context.Context, service, method string, duration time.Duration, err error) {
	fields := []Field{
		{Key: "service", Value: service},
		{Key: "method", Value: method},
		{Key: "duration", Value: duration},
		{Key: "duration_ms", Value: duration.Milliseconds()},
	}

	if err != nil {
		fields = append(fields, Field{Key: "error", Value: err.Error()})
		logger.WithContext(ctx).Error("service call failed", fields...)
	} else {
		logger.WithContext(ctx).Info("service call completed", fields...)
	}
}

// LogCacheOperation logs a cache operation
func LogCacheOperation(logger Logger, ctx context.Context, operation, key string, hit bool, duration time.Duration) {
	fields := []Field{
		{Key: "operation", Value: operation},
		{Key: "key", Value: key},
		{Key: "hit", Value: hit},
		{Key: "duration", Value: duration},
		{Key: "duration_ms", Value: duration.Milliseconds()},
	}

	logger.WithContext(ctx).Debug("cache operation", fields...)
}

// LogUserAction logs a user action for audit purposes
func LogUserAction(logger Logger, ctx context.Context, action, resource string, userID, tenantID uint) {
	fields := []Field{
		{Key: "action", Value: action},
		{Key: "resource", Value: resource},
		{Key: "user_id", Value: userID},
		{Key: "tenant_id", Value: tenantID},
		{Key: "timestamp", Value: time.Now()},
	}

	logger.WithContext(ctx).Info("user action", fields...)
}

// LogSystemEvent logs a system event
func LogSystemEvent(logger Logger, event, component string, details map[string]interface{}) {
	fields := []Field{
		{Key: "event", Value: event},
		{Key: "component", Value: component},
		{Key: "details", Value: details},
		{Key: "timestamp", Value: time.Now()},
	}

	logger.Info("system event", fields...)
}