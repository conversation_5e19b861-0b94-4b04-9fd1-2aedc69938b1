package logging_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/logging"
)

func TestBasicLogging(t *testing.T) {
	// Create a logger with development configuration
	config := &logging.LogConfig{
		Mode:  logging.ModeDevelopment,
		Level: logging.DEBUG,
		Output: []string{"console"},
		Console: &logging.ConsoleConfig{
			Format:        "text",
			Colorize:      true,
			IncludeCaller: true,
			Enabled:       true,
		},
	}

	logger, err := logging.NewZapLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test basic logging
	logger.Info("Test message",
		logging.Field{Key: "test_key", Value: "test_value"},
		logging.Field{Key: "number", Value: 42},
	)

	// Test with context
	ctx := context.WithValue(context.Background(), "request_id", "test_123")
	logger.WithContext(ctx).Info("Message with context")

	// Test with error
	err = errors.New("test error")
	logger.WithError(err).Error("Error message")

	// Test with timer
	timer := logger.WithTimer()
	time.Sleep(10 * time.Millisecond)
	timer.Stop()
}

func TestApplicationLogger(t *testing.T) {
	// Setup application logger
	appLogger, err := logging.LoggerSetup("test-app", "1.0.0")
	if err != nil {
		t.Fatalf("Failed to setup application logger: %v", err)
	}
	defer appLogger.Close()

	// Test main logger
	mainLogger := appLogger.GetLogger()
	mainLogger.Info("Application logger test")

	// Test access logger
	accessLogger := appLogger.GetAccessLogger()
	req := &logging.AccessLogRequest{
		Method:    "GET",
		Path:      "/test",
		Status:    200,
		ClientIP:  "127.0.0.1",
		Latency:   50 * time.Millisecond,
		BodySize:  100,
		RequestID: "test_request",
	}
	accessLogger.LogRequest(context.Background(), req)

	// Test error logger
	errorLogger := appLogger.GetErrorLogger()
	testErr := errors.New("test error")
	errorLogger.LogError(context.Background(), testErr, "Test error message")

	// Test audit logger
	auditLogger := appLogger.GetAuditLogger()
	event := &logging.AuditEvent{
		ID:         "audit_test",
		UserID:     123,
		TenantID:   456,
		Action:     "test.action",
		Resource:   "test_resource",
		ResourceID: "test_123",
		Result:     "success",
		Timestamp:  time.Now(),
	}
	auditLogger.LogDataChange(context.Background(), event)

	// Test security logger
	securityLogger := appLogger.GetSecurityLogger()
	secEvent := &logging.SecurityEvent{
		Type:     "test_event",
		Severity: "medium",
		SourceIP: "127.0.0.1",
		UserID:   123,
		Details: map[string]interface{}{
			"test_detail": "test_value",
		},
		Timestamp: time.Now(),
	}
	securityLogger.LogSecurityEvent(secEvent)
}

func TestDebugFeatures(t *testing.T) {
	// Create debug configuration
	config := &logging.LogConfig{
		Mode:  logging.ModeDebug,
		Level: logging.DEBUG,
		Output: []string{"console"},
		Console: &logging.ConsoleConfig{
			Format:        "text",
			Colorize:      true,
			IncludeCaller: true,
			Enabled:       true,
		},
		Debug: &logging.DebugConfig{
			Enabled:             true,
			VerboseSQL:          true,
			RequestBodyLogging:  true,
			ResponseBodyLogging: true,
			SlowQueryThreshold:  100 * time.Millisecond,
		},
	}

	logger, err := logging.NewZapLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test debug logger
	debugLogger := logging.NewDebugLogger(logger, config.Debug)
	ctx := context.Background()

	// Test SQL query logging
	debugLogger.LogSQLQuery(ctx, "SELECT * FROM users WHERE id = ?", []interface{}{123}, 50*time.Millisecond, nil)

	// Test memory stats
	debugLogger.LogMemoryStats(ctx)

	// Test goroutine profile
	debugLogger.LogGoroutineProfile(ctx)

	// Test request tracer
	tracer := logging.NewRequestTracer(logger, config.Debug)
	ctx = tracer.StartTrace(ctx, "test_operation")
	tracer.AddEvent(ctx, "step_1", map[string]interface{}{"step": "validation"})
	tracer.AddEvent(ctx, "step_2", map[string]interface{}{"step": "processing"})
	tracer.EndTrace(ctx)
}

func TestProductionFeatures(t *testing.T) {
	// Create production configuration
	config := &logging.LogConfig{
		Mode:  logging.ModeProduction,
		Level: logging.WARN,
		Output: []string{"console"},
		Console: &logging.ConsoleConfig{
			Format:  "json",
			Enabled: true,
		},
		Production: &logging.ProductionConfig{
			SamplingRate:      0.1,
			ErrorSamplingRate: 1.0,
			AsyncLogging:      true,
			BatchSize:         100,
			FlushInterval:     1 * time.Second,
		},
		Sanitization: &logging.SanitizationConfig{
			Enabled: true,
			SensitiveFields: []string{"password", "api_key"},
			MaskPatterns: []string{
				`[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,4}`, // emails
			},
		},
	}

	logger, err := logging.NewZapLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test sampling logger
	samplingLogger := logging.NewSamplingLogger(logger, 0.1, 1.0)
	for i := 0; i < 10; i++ {
		samplingLogger.Info("Sampled message", logging.Field{Key: "iteration", Value: i})
	}

	// Test sanitization
	sanitizingLogger := logging.NewSanitizingLogger(logger, config.Sanitization)
	sanitizingLogger.Info("Message with sensitive data",
		logging.Field{Key: "password", Value: "secret123"},
		logging.Field{Key: "email", Value: "<EMAIL>"},
		logging.Field{Key: "normal_field", Value: "normal_value"},
	)

	// Test async logger
	asyncLogger := logging.NewAsyncLogger(logger, 10, 100*time.Millisecond)
	for i := 0; i < 5; i++ {
		asyncLogger.Info("Async message", logging.Field{Key: "iteration", Value: i})
	}
	
	// Wait for async processing
	time.Sleep(200 * time.Millisecond)
	asyncLogger.Close()
}

func TestLoggingContext(t *testing.T) {
	logger, err := logging.NewLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	ctx := context.Background()
	
	// Test logging context
	logCtx := logging.NewLoggingContext(logger, ctx).
		WithField("user_id", 123).
		WithField("tenant_id", 456)

	// Test operation logging
	err = logCtx.LogOperation("test_operation", func() error {
		time.Sleep(10 * time.Millisecond)
		return nil
	})
	if err != nil {
		t.Errorf("Operation failed: %v", err)
	}

	// Test operation with error
	err = logCtx.LogOperation("failing_operation", func() error {
		time.Sleep(5 * time.Millisecond)
		return errors.New("operation failed")
	})
	if err == nil {
		t.Error("Expected operation to fail")
	}
}

func TestHelperFunctions(t *testing.T) {
	logger, err := logging.NewLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	ctx := context.Background()

	// Test helper functions
	logging.LogRequestStart(logger, ctx, "GET", "/api/test")
	logging.LogRequestEnd(logger, ctx, "GET", "/api/test", 200, 50*time.Millisecond)

	logging.LogDatabaseQuery(logger, ctx, "SELECT * FROM users", 25*time.Millisecond, nil)
	logging.LogDatabaseQuery(logger, ctx, "SELECT * FROM posts", 150*time.Millisecond, errors.New("connection timeout"))

	logging.LogServiceCall(logger, ctx, "user-service", "GetProfile", 75*time.Millisecond, nil)
	logging.LogCacheOperation(logger, ctx, "get", "user:123", true, 5*time.Millisecond)

	logging.LogUserAction(logger, ctx, "create_post", "post", 123, 456)
	logging.LogSystemEvent(logger, "startup", "api-server", map[string]interface{}{
		"version": "3.0.0",
		"port":    8080,
	})
}

func BenchmarkLogging(b *testing.B) {
	logger, err := logging.NewLogger()
	if err != nil {
		b.Fatalf("Failed to create logger: %v", err)
	}

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			logger.WithContext(ctx).Info("Benchmark message",
				logging.Field{Key: "iteration", Value: b.N},
				logging.Field{Key: "timestamp", Value: time.Now()},
			)
		}
	})
}

func BenchmarkAsyncLogging(b *testing.B) {
	logger, err := logging.NewLogger()
	if err != nil {
		b.Fatalf("Failed to create logger: %v", err)
	}

	asyncLogger := logging.NewAsyncLogger(logger, 1000, 100*time.Millisecond)
	defer asyncLogger.Close()

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			asyncLogger.WithContext(ctx).Info("Async benchmark message",
				logging.Field{Key: "iteration", Value: b.N},
				logging.Field{Key: "timestamp", Value: time.Now()},
			)
		}
	})
}

func BenchmarkSamplingLogging(b *testing.B) {
	logger, err := logging.NewLogger()
	if err != nil {
		b.Fatalf("Failed to create logger: %v", err)
	}

	samplingLogger := logging.NewSamplingLogger(logger, 0.1, 1.0)
	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			samplingLogger.WithContext(ctx).Info("Sampling benchmark message",
				logging.Field{Key: "iteration", Value: b.N},
				logging.Field{Key: "timestamp", Value: time.Now()},
			)
		}
	})
}