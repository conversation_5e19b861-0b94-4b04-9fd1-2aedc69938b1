package logging

import (
	"context"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// ZapLogger wraps zap logger with our interface
type ZapLogger struct {
	logger *zap.Logger
	sugar  *zap.SugaredLogger
	config *LogConfig
}

// NewZapLogger creates a new Zap logger instance
func NewZapLogger(config *LogConfig) (*ZapLogger, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// Build zap configuration
	zapConfig := buildZapConfig(config)

	// Create logger
	logger, err := zapConfig.Build(
		zap.AddCaller(),
		zap.AddCallerSkip(1),
		zap.AddStacktrace(zapcore.ErrorLevel),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create zap logger: %w", err)
	}

	// Add hooks based on mode
	if config.Mode == ModeProduction {
		logger = logger.WithOptions(
			zap.Hooks(sanitizeHook(config.Sanitization)),
		)
	}

	return &ZapLogger{
		logger: logger,
		sugar:  logger.Sugar(),
		config: config,
	}, nil
}

// buildZapConfig builds zap configuration
func buildZapConfig(config *LogConfig) zap.Config {
	var zapConfig zap.Config

	// Base configuration based on mode
	switch config.Mode {
	case ModeDevelopment, ModeDebug:
		zapConfig = zap.NewDevelopmentConfig()
	case ModeStaging, ModeProduction:
		zapConfig = zap.NewProductionConfig()
	default:
		zapConfig = zap.NewDevelopmentConfig()
	}

	// Set level
	zapConfig.Level = zap.NewAtomicLevelAt(convertLogLevel(config.Level))

	// Configure output
	zapConfig.OutputPaths = []string{}
	zapConfig.ErrorOutputPaths = []string{}

	for _, output := range config.Output {
		switch output {
		case "console":
			if config.Console != nil && config.Console.Enabled {
				zapConfig.OutputPaths = append(zapConfig.OutputPaths, "stdout")
				zapConfig.ErrorOutputPaths = append(zapConfig.ErrorOutputPaths, "stderr")
			}
		case "file":
			if config.File != nil {
				zapConfig.OutputPaths = append(zapConfig.OutputPaths, config.File.Path)
				zapConfig.ErrorOutputPaths = append(zapConfig.ErrorOutputPaths, config.File.Path)
			}
		}
	}

	// Set encoding
	if config.Console != nil && config.Console.Format == "json" {
		zapConfig.Encoding = "json"
	} else {
		zapConfig.Encoding = "console"
	}

	// Configure encoder
	zapConfig.EncoderConfig = buildEncoderConfig(config)

	// Configure sampling for production
	if config.Mode == ModeProduction && config.Production != nil {
		zapConfig.Sampling = &zap.SamplingConfig{
			Initial:    100,
			Thereafter: int(1 / config.Production.SamplingRate),
		}
	}

	return zapConfig
}

// buildEncoderConfig builds encoder configuration
func buildEncoderConfig(config *LogConfig) zapcore.EncoderConfig {
	var encoderConfig zapcore.EncoderConfig

	switch config.Mode {
	case ModeDevelopment, ModeDebug:
		encoderConfig = zap.NewDevelopmentEncoderConfig()
		encoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05")
		if config.Console != nil && config.Console.Colorize {
			encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
		}
	case ModeStaging, ModeProduction:
		encoderConfig = zap.NewProductionEncoderConfig()
		encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	default:
		encoderConfig = zap.NewDevelopmentEncoderConfig()
	}

	return encoderConfig
}

// convertLogLevel converts our LogLevel to zap level
func convertLogLevel(level LogLevel) zapcore.Level {
	switch level {
	case TRACE:
		return zapcore.DebugLevel // Zap doesn't have trace, use debug
	case DEBUG:
		return zapcore.DebugLevel
	case INFO:
		return zapcore.InfoLevel
	case WARN:
		return zapcore.WarnLevel
	case ERROR:
		return zapcore.ErrorLevel
	case FATAL:
		return zapcore.FatalLevel
	case PANIC:
		return zapcore.PanicLevel
	default:
		return zapcore.InfoLevel
	}
}

// convertFields converts our fields to zap fields
func convertFields(fields []Field) []zap.Field {
	zapFields := make([]zap.Field, len(fields))
	for i, field := range fields {
		zapFields[i] = zap.Any(field.Key, field.Value)
	}
	return zapFields
}

// sanitizeHook creates a sanitization hook
func sanitizeHook(config *SanitizationConfig) func(zapcore.Entry) error {
	return func(entry zapcore.Entry) error {
		// Sanitization logic would go here
		// For now, just return nil
		return nil
	}
}

// Implementation of Logger interface

func (l *ZapLogger) Trace(msg string, fields ...Field) {
	// Zap doesn't have trace, use debug
	l.logger.Debug(msg, convertFields(fields)...)
}

func (l *ZapLogger) Debug(msg string, fields ...Field) {
	l.logger.Debug(msg, convertFields(fields)...)
}

func (l *ZapLogger) Info(msg string, fields ...Field) {
	l.logger.Info(msg, convertFields(fields)...)
}

func (l *ZapLogger) Warn(msg string, fields ...Field) {
	l.logger.Warn(msg, convertFields(fields)...)
}

func (l *ZapLogger) Error(msg string, fields ...Field) {
	l.logger.Error(msg, convertFields(fields)...)
}

func (l *ZapLogger) Fatal(msg string, fields ...Field) {
	l.logger.Fatal(msg, convertFields(fields)...)
}

func (l *ZapLogger) Panic(msg string, fields ...Field) {
	l.logger.Panic(msg, convertFields(fields)...)
}

func (l *ZapLogger) WithContext(ctx context.Context) Logger {
	fields := []zap.Field{}

	// Extract context values
	if requestID, ok := ctx.Value("request_id").(string); ok {
		fields = append(fields, zap.String("request_id", requestID))
	}

	if userID, ok := ctx.Value("user_id").(uint); ok {
		fields = append(fields, zap.Uint("user_id", userID))
	}

	if tenantID, ok := ctx.Value("tenant_id").(uint); ok {
		fields = append(fields, zap.Uint("tenant_id", tenantID))
	}

	if traceID, ok := ctx.Value("trace_id").(string); ok {
		fields = append(fields, zap.String("trace_id", traceID))
	}

	return &ZapLogger{
		logger: l.logger.With(fields...),
		sugar:  l.sugar,
		config: l.config,
	}
}

func (l *ZapLogger) WithFields(fields ...Field) Logger {
	zapFields := convertFields(fields)
	return &ZapLogger{
		logger: l.logger.With(zapFields...),
		sugar:  l.sugar,
		config: l.config,
	}
}

func (l *ZapLogger) WithError(err error) Logger {
	return &ZapLogger{
		logger: l.logger.With(zap.Error(err)),
		sugar:  l.sugar,
		config: l.config,
	}
}

func (l *ZapLogger) WithTimer() TimerLogger {
	start := time.Now()
	return &zapTimerLogger{
		ZapLogger: l,
		start:     start,
	}
}

func (l *ZapLogger) With(key string, value interface{}) Logger {
	return &ZapLogger{
		logger: l.logger.With(zap.Any(key, value)),
		sugar:  l.sugar,
		config: l.config,
	}
}

// zapTimerLogger implements TimerLogger
type zapTimerLogger struct {
	*ZapLogger
	start time.Time
}

func (t *zapTimerLogger) Stop() {
	duration := time.Since(t.start)
	t.logger.Info("operation completed",
		zap.Duration("duration", duration),
		zap.Int64("duration_ms", duration.Milliseconds()),
	)
}

// NewFileLogger creates a logger that writes to a file with rotation
func NewFileLogger(config *LogConfig) (*ZapLogger, error) {
	if config == nil || config.File == nil {
		return nil, fmt.Errorf("file config is required")
	}

	// Create directory if it doesn't exist
	if err := os.MkdirAll(config.File.Path[:len(config.File.Path)-len("/app.log")], 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	// Setup file rotation
	writer := &lumberjack.Logger{
		Filename:   config.File.Path,
		MaxSize:    parseMaxSize(config.File.MaxSize),
		MaxAge:     config.File.MaxAge,
		MaxBackups: config.File.MaxBackups,
		Compress:   config.File.Compress,
	}

	// Create core
	encoderConfig := buildEncoderConfig(config)
	encoder := zapcore.NewJSONEncoder(encoderConfig)
	core := zapcore.NewCore(encoder, zapcore.AddSync(writer), convertLogLevel(config.Level))

	// Create logger
	logger := zap.New(core,
		zap.AddCaller(),
		zap.AddStacktrace(zapcore.ErrorLevel),
	)

	return &ZapLogger{
		logger: logger,
		sugar:  logger.Sugar(),
		config: config,
	}, nil
}

// parseMaxSize parses max size string to megabytes
func parseMaxSize(maxSize string) int {
	// Simple parsing - just extract number and assume MB
	// In real implementation, you'd want proper parsing
	switch maxSize {
	case "100MB":
		return 100
	case "500MB":
		return 500
	case "1GB":
		return 1024
	default:
		return 100
	}
}

// Sync flushes any buffered log entries
func (l *ZapLogger) Sync() error {
	return l.logger.Sync()
}

// Close closes the logger
func (l *ZapLogger) Close() error {
	return l.logger.Sync()
}