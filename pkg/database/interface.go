package database

import (
	"context"
	"database/sql"
	"time"
)

// Database defines the interface for database operations
type Database interface {
	// Connection management
	Connect(ctx context.Context) error
	Close() error
	Ping(ctx context.Context) error
	
	// Get underlying connection
	DB() *sql.DB
	
	// Transaction support
	BeginTx(ctx context.Context, opts *sql.TxOptions) (Transaction, error)
	
	// Migration support
	Migrate(ctx context.Context) error
	MigrateDown(ctx context.Context, steps int) error
	
	// Health check
	Health(ctx context.Context) error
	
	// Metrics and monitoring
	GetMetrics() *ConnectionMetrics
	GetHealthStatus(ctx context.Context) *HealthStatus
}

// Transaction defines the interface for database transactions
type Transaction interface {
	Commit() error
	Rollback() error
	
	// Query execution within transaction
	Exec(query string, args ...interface{}) (sql.Result, error)
	Query(query string, args ...interface{}) (*sql.Rows, error)
	QueryRow(query string, args ...interface{}) *sql.Row
}

// Config holds database configuration
type Config struct {
	// Connection settings
	Host     string
	Port     int
	Database string
	Username string
	Password string
	Charset  string
	
	// Connection pool settings
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
	ConnMaxIdleTime time.Duration
	
	// Additional options
	ParseTime bool
	Loc       string
	Timeout   time.Duration
	
	// Migration settings
	MigrationsPath string
	
	// SSL/TLS settings
	SSLMode string
	
	// Retry settings
	RetryConfig *RetryConfig
	
	// Circuit breaker settings
	CircuitBreakerConfig *CircuitBreakerConfig
	
	// Enable metrics collection
	EnableMetrics bool
	
	// Enable SQL debug logging
	DebugSQL bool
}