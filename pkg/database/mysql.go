package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/golang-migrate/migrate/v4"
	migratemysql "github.com/golang-migrate/migrate/v4/database/mysql"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// MySQLDatabase implements the Database interface for MySQL
type MySQLDatabase struct {
	config   *Config
	db       *sql.DB
	gormDB   *gorm.DB
	migrate  *migrate.Migrate
	metrics  *ConnectionMetrics
}

// NewMySQLDatabase creates a new MySQL database instance
func NewMySQLDatabase(config *Config) *MySQLDatabase {
	mysql := &MySQLDatabase{
		config: config,
	}
	
	// Initialize metrics if enabled
	if config.EnableMetrics {
		mysql.metrics = &ConnectionMetrics{}
	}
	
	return mysql
}

// Connect establishes connection to MySQL database
func (m *MySQLDatabase) Connect(ctx context.Context) error {
	// Use retry mechanism if configured
	if m.config.RetryConfig != nil {
		return RetryWithMetrics(ctx, m.config.RetryConfig, m.metrics, m.doConnect)
	}
	
	return m.doConnect()
}

// doConnect performs the actual connection
func (m *MySQLDatabase) doConnect() error {
	// Build DSN
	dsn := m.buildDSN()
	
	// Open database connection
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		if m.metrics != nil {
			m.metrics.IncrementConnectionsFailed(err)
		}
		return fmt.Errorf("failed to open database connection: %w", err)
	}
	
	// Configure connection pool
	if m.config.MaxOpenConns > 0 {
		db.SetMaxOpenConns(m.config.MaxOpenConns)
	}
	if m.config.MaxIdleConns > 0 {
		db.SetMaxIdleConns(m.config.MaxIdleConns)
	}
	if m.config.ConnMaxLifetime > 0 {
		db.SetConnMaxLifetime(m.config.ConnMaxLifetime)
	}
	if m.config.ConnMaxIdleTime > 0 {
		db.SetConnMaxIdleTime(m.config.ConnMaxIdleTime)
	}
	
	// Test connection
	pingCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := db.PingContext(pingCtx); err != nil {
		db.Close()
		if m.metrics != nil {
			m.metrics.IncrementConnectionsFailed(err)
		}
		return fmt.Errorf("failed to ping database: %w", err)
	}
	
	m.db = db
	
	// Update metrics on successful connection
	if m.metrics != nil {
		m.metrics.IncrementConnectionsOpened()
	}
	
	// Initialize GORM
	logLevel := logger.Silent
	if m.config.DebugSQL {
		logLevel = logger.Info
	}
	
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	}
	
	gormDB, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		m.db.Close()
		return fmt.Errorf("failed to initialize GORM: %w", err)
	}
	
	m.gormDB = gormDB
	
	// Initialize migration if path is provided
	if m.config.MigrationsPath != "" {
		if err := m.initMigration(); err != nil {
			// Log error but don't fail connection
			fmt.Printf("Warning: failed to initialize migrations: %v\n", err)
		}
	}
	
	return nil
}

// Close closes the database connection
func (m *MySQLDatabase) Close() error {
	if m.migrate != nil {
		sourceErr, dbErr := m.migrate.Close()
		if sourceErr != nil {
			return fmt.Errorf("failed to close migration source: %w", sourceErr)
		}
		if dbErr != nil {
			return fmt.Errorf("failed to close migration database: %w", dbErr)
		}
	}
	
	if m.db != nil {
		return m.db.Close()
	}
	
	return nil
}

// Ping checks if the database connection is alive
func (m *MySQLDatabase) Ping(ctx context.Context) error {
	if m.db == nil {
		return fmt.Errorf("database not connected")
	}
	
	return m.db.PingContext(ctx)
}

// DB returns the underlying sql.DB instance
func (m *MySQLDatabase) DB() *sql.DB {
	return m.db
}

// GORM returns the underlying GORM instance
func (m *MySQLDatabase) GORM() *gorm.DB {
	return m.gormDB
}

// BeginTx starts a new transaction
func (m *MySQLDatabase) BeginTx(ctx context.Context, opts *sql.TxOptions) (Transaction, error) {
	if m.db == nil {
		return nil, fmt.Errorf("database not connected")
	}
	
	tx, err := m.db.BeginTx(ctx, opts)
	if err != nil {
		if m.metrics != nil {
			m.metrics.IncrementTransactionsFailed()
		}
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	
	if m.metrics != nil {
		m.metrics.IncrementTransactionsOpened()
	}
	
	return &mysqlTransaction{tx: tx}, nil
}

// Migrate runs all pending migrations
func (m *MySQLDatabase) Migrate(ctx context.Context) error {
	if m.migrate == nil {
		return fmt.Errorf("migrations not initialized")
	}
	
	if err := m.migrate.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("failed to run migrations: %w", err)
	}
	
	return nil
}

// MigrateDown rolls back migrations
func (m *MySQLDatabase) MigrateDown(ctx context.Context, steps int) error {
	if m.migrate == nil {
		return fmt.Errorf("migrations not initialized")
	}
	
	if err := m.migrate.Steps(-steps); err != nil {
		return fmt.Errorf("failed to rollback migrations: %w", err)
	}
	
	return nil
}

// Health performs a health check on the database
func (m *MySQLDatabase) Health(ctx context.Context) error {
	if err := m.Ping(ctx); err != nil {
		return err
	}
	
	// Run a simple query to verify database is functional
	var result int
	err := m.db.QueryRowContext(ctx, "SELECT 1").Scan(&result)
	if err != nil {
		return fmt.Errorf("health check query failed: %w", err)
	}
	
	return nil
}

// buildDSN builds the MySQL DSN from config
func (m *MySQLDatabase) buildDSN() string {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		m.config.Username,
		m.config.Password,
		m.config.Host,
		m.config.Port,
		m.config.Database,
	)
	
	params := make([]string, 0)
	
	if m.config.Charset != "" {
		params = append(params, fmt.Sprintf("charset=%s", m.config.Charset))
	}
	
	if m.config.ParseTime {
		params = append(params, "parseTime=1")
	}
	
	if m.config.Loc != "" {
		params = append(params, fmt.Sprintf("loc=%s", m.config.Loc))
	}
	
	if m.config.Timeout > 0 {
		params = append(params, fmt.Sprintf("timeout=%s", m.config.Timeout))
	}
	
	if len(params) > 0 {
		dsn += "?" + params[0]
		for i := 1; i < len(params); i++ {
			dsn += "&" + params[i]
		}
	}
	
	return dsn
}

// initMigration initializes the migration instance
func (m *MySQLDatabase) initMigration() error {
	driver, err := migratemysql.WithInstance(m.db, &migratemysql.Config{})
	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}
	
	migrate, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", m.config.MigrationsPath),
		"mysql",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migration instance: %w", err)
	}
	
	m.migrate = migrate
	return nil
}

// mysqlTransaction implements the Transaction interface
type mysqlTransaction struct {
	tx *sql.Tx
}

func (t *mysqlTransaction) Commit() error {
	return t.tx.Commit()
}

func (t *mysqlTransaction) Rollback() error {
	return t.tx.Rollback()
}

func (t *mysqlTransaction) Exec(query string, args ...interface{}) (sql.Result, error) {
	return t.tx.Exec(query, args...)
}

func (t *mysqlTransaction) Query(query string, args ...interface{}) (*sql.Rows, error) {
	return t.tx.Query(query, args...)
}

func (t *mysqlTransaction) QueryRow(query string, args ...interface{}) *sql.Row {
	return t.tx.QueryRow(query, args...)
}

// GetMetrics returns connection metrics
func (m *MySQLDatabase) GetMetrics() *ConnectionMetrics {
	return m.metrics
}

// GetHealthStatus returns comprehensive health status
func (m *MySQLDatabase) GetHealthStatus(ctx context.Context) *HealthStatus {
	return PerformHealthCheck(ctx, m, m.metrics)
}