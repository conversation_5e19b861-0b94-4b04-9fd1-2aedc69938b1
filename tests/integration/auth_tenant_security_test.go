package integration

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"github.com/tranthanhloi/wn-api-v3/tests/integration/helpers"
)

var _ = Describe("Auth-Tenant Security Tests", func() {
	var (
		db     *gorm.DB
		router *gin.Engine
		logger utils.Logger
		helper *helpers.AuthTenantTestHelper
		
		testData *helpers.TestScenarioData
		cleanup  func()
	)

	BeforeEach(func() {
		// Setup test database
		var err error
		
		// Connect to test database
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			utils.GetEnv("TEST_DB_USER", "root"),
			utils.GetEnv("TEST_DB_PASSWORD", "root"),
			utils.GetEnv("TEST_DB_HOST", "localhost"),
			utils.GetEnv("TEST_DB_PORT", "3306"),
			utils.GetEnv("TEST_DB_NAME", "blogapi_test"),
		)
		
		db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
		Expect(err).ToNot(HaveOccurred())
		
		// Setup logger
		logger = utils.NewLogger("security-test")
		
		// Setup validator
		v := validator.New()
		
		// Setup Gin router
		gin.SetMode(gin.TestMode)
		router = gin.New()
		
		// Register modules
		apiV1 := router.Group("/api/cms/v1")
		auth.RegisterRoutes(apiV1, db, v, logger)
		tenant.RegisterRoutes(apiV1, db, v, logger)
		user.RegisterRoutes(apiV1, db, v, logger)
		
		// Setup test helper
		helper = helpers.NewAuthTenantTestHelper(db, router, logger)
		
		// Create test scenario
		testData, err = helper.CreateTestScenario()
		Expect(err).ToNot(HaveOccurred())
		
		// Setup cleanup
		cleanup = func() {
			helper.CleanupTestData()
		}
	})

	AfterEach(func() {
		cleanup()
	})

	Context("Token Security", func() {
		It("should prevent token replay attacks", func() {
			// Use a token multiple times rapidly
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + token,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
			}
			
			// Make multiple concurrent requests
			const numRequests = 5
			results := make(chan int, numRequests)
			
			for i := 0; i < numRequests; i++ {
				go func() {
					resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
					results <- resp.Code
				}()
			}
			
			// All requests should succeed (token replay is allowed for legitimate use)
			for i := 0; i < numRequests; i++ {
				code := <-results
				Expect(code).To(Equal(http.StatusOK))
			}
		})

		It("should prevent token manipulation", func() {
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			// Try to manipulate the token
			manipulatedToken := token[:len(token)-5] + "XXXXX"
			
			headers := map[string]string{
				"Authorization": "Bearer " + manipulatedToken,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
			}
			
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			
			Expect(resp.Code).To(Equal(http.StatusUnauthorized))
		})

		It("should prevent token without proper signature", func() {
			// Create a fake token with proper structure but invalid signature
			fakeToken := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
			tenantID := testData.Tenants["corp"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + fakeToken,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
			}
			
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			
			Expect(resp.Code).To(Equal(http.StatusUnauthorized))
		})
	})

	Context("Tenant Isolation Security", func() {
		It("should prevent lateral movement between tenants", func() {
			// Admin tries to access startup tenant with corp token
			corpToken := testData.Tokens["admin"]["corp"]
			startupTenantID := testData.Tenants["startup"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + corpToken,
				"X-Tenant-ID":   strconv.Itoa(int(startupTenantID)),
			}
			
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			
			// Should return corp context, not startup context
			Expect(resp.Code).To(Equal(http.StatusOK))
			
			apiResp, err := helper.ParseResponse(resp)
			Expect(err).ToNot(HaveOccurred())
			
			// Verify it returns corp tenant context
			if tenantData, exists := apiResp.Data["tenant"]; exists {
				tenant := tenantData.(map[string]interface{})
				Expect(tenant["slug"]).To(Equal("test-corp"))
				Expect(tenant["slug"]).ToNot(Equal("test-startup"))
			}
		})

		It("should prevent unauthorized tenant access", func() {
			// Guest tries to access startup tenant (no access)
			guestToken := testData.Tokens["guest"]["corp"]
			startupTenantID := testData.Tenants["startup"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + guestToken,
				"X-Tenant-ID":   strconv.Itoa(int(startupTenantID)),
			}
			
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			
			// Should return corp context (guest's only tenant)
			Expect(resp.Code).To(Equal(http.StatusOK))
			
			apiResp, err := helper.ParseResponse(resp)
			Expect(err).ToNot(HaveOccurred())
			
			// Verify it returns corp tenant context
			if tenantData, exists := apiResp.Data["tenant"]; exists {
				tenant := tenantData.(map[string]interface{})
				Expect(tenant["slug"]).To(Equal("test-corp"))
			}
		})

		It("should prevent tenant ID injection", func() {
			// Try to inject tenant ID through various methods
			corpToken := testData.Tokens["admin"]["corp"]
			startupTenantID := testData.Tenants["startup"].ID
			
			// Try injection through header
			headers := map[string]string{
				"Authorization": "Bearer " + corpToken,
				"X-Tenant-ID":   fmt.Sprintf("%d; DROP TABLE tenants; --", startupTenantID),
			}
			
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			
			// Should handle invalid tenant ID gracefully
			Expect(resp.Code).To(Equal(http.StatusOK))
		})
	})

	Context("Rate Limiting and Brute Force Protection", func() {
		It("should rate limit authentication attempts", func() {
			// Try multiple failed login attempts
			const maxAttempts = 10
			results := make(chan int, maxAttempts)
			
			for i := 0; i < maxAttempts; i++ {
				go func() {
					body := map[string]interface{}{
						"email":    "<EMAIL>",
						"password": "wrongpassword",
					}
					
					resp := helper.MakeRequest("POST", "/api/cms/v1/auth/login", body, nil)
					results <- resp.Code
				}()
			}
			
			// Collect results
			unauthorizedCount := 0
			rateLimitedCount := 0
			
			for i := 0; i < maxAttempts; i++ {
				code := <-results
				if code == http.StatusUnauthorized {
					unauthorizedCount++
				} else if code == http.StatusTooManyRequests {
					rateLimitedCount++
				}
			}
			
			// Should have some rate limited responses
			Expect(unauthorizedCount + rateLimitedCount).To(Equal(maxAttempts))
		})

		It("should enforce session rate limits", func() {
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + token,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
			}
			
			// Make many requests rapidly
			const numRequests = 20
			results := make(chan int, numRequests)
			
			for i := 0; i < numRequests; i++ {
				go func() {
					resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
					results <- resp.Code
				}()
			}
			
			// Should handle the load gracefully
			for i := 0; i < numRequests; i++ {
				code := <-results
				Expect(code).To(BeNumerically(">=", 200))
				Expect(code).To(BeNumerically("<", 500))
			}
		})
	})

	Context("Authorization Bypass Prevention", func() {
		It("should prevent privilege escalation", func() {
			// Member tries to access admin-only resources
			memberToken := testData.Tokens["member"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + memberToken,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
			}
			
			// Try to access admin endpoint (if it exists)
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			
			// Should succeed for profile but verify role
			Expect(resp.Code).To(Equal(http.StatusOK))
			
			apiResp, err := helper.ParseResponse(resp)
			Expect(err).ToNot(HaveOccurred())
			
			userData := apiResp.Data["user"].(map[string]interface{})
			Expect(userData["email"]).To(Equal("<EMAIL>"))
		})

		It("should prevent role manipulation", func() {
			// Try to manipulate role in request
			memberToken := testData.Tokens["member"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + memberToken,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
				"X-User-Role":   "admin", // Try to inject admin role
			}
			
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			
			Expect(resp.Code).To(Equal(http.StatusOK))
			
			// Role should be determined by JWT/database, not headers
			apiResp, err := helper.ParseResponse(resp)
			Expect(err).ToNot(HaveOccurred())
			
			userData := apiResp.Data["user"].(map[string]interface{})
			Expect(userData["email"]).To(Equal("<EMAIL>"))
		})
	})

	Context("Session Security", func() {
		It("should prevent session hijacking", func() {
			// Get original session info
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + token,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
			}
			
			// Get sessions
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/sessions", nil, headers)
			
			Expect(resp.Code).To(Equal(http.StatusOK))
			
			// Verify session security attributes
			apiResp, err := helper.ParseResponse(resp)
			Expect(err).ToNot(HaveOccurred())
			
			sessions := apiResp.Data["sessions"].([]interface{})
			if len(sessions) > 0 {
				session := sessions[0].(map[string]interface{})
				Expect(session).To(HaveKey("id"))
				Expect(session).To(HaveKey("ip_address"))
				Expect(session).To(HaveKey("user_agent"))
				Expect(session).To(HaveKey("last_activity"))
			}
		})

		It("should detect suspicious session activity", func() {
			// Use token from different IP/User-Agent
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + token,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
				"User-Agent":    "SuspiciousBot/1.0",
				"X-Forwarded-For": "********",
			}
			
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			
			// Should still work but be logged
			Expect(resp.Code).To(Equal(http.StatusOK))
		})
	})

	Context("Timing Attack Prevention", func() {
		It("should have consistent response times", func() {
			// Test with valid and invalid tokens
			validToken := testData.Tokens["admin"]["corp"]
			invalidToken := "invalid-token"
			tenantID := testData.Tenants["corp"].ID
			
			// Measure response times
			timings := make([]time.Duration, 10)
			
			for i := 0; i < 10; i++ {
				var token string
				if i%2 == 0 {
					token = validToken
				} else {
					token = invalidToken
				}
				
				headers := map[string]string{
					"Authorization": "Bearer " + token,
					"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
				}
				
				start := time.Now()
				helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
				timings[i] = time.Since(start)
			}
			
			// Calculate timing statistics
			var validTotal, invalidTotal time.Duration
			validCount, invalidCount := 0, 0
			
			for i, timing := range timings {
				if i%2 == 0 {
					validTotal += timing
					validCount++
				} else {
					invalidTotal += timing
					invalidCount++
				}
			}
			
			validAvg := validTotal / time.Duration(validCount)
			invalidAvg := invalidTotal / time.Duration(invalidCount)
			
			// Timing difference should be reasonable
			diff := validAvg - invalidAvg
			if diff < 0 {
				diff = -diff
			}
			
			// Allow up to 50ms difference
			Expect(diff).To(BeNumerically("<", 50*time.Millisecond))
		})
	})

	Context("Input Validation Security", func() {
		It("should prevent XSS in user input", func() {
			// Try to register with XSS payload
			xssPayload := "<script>alert('xss')</script>"
			
			body := map[string]interface{}{
				"email":      "<EMAIL>",
				"password":   "Password123",
				"first_name": xssPayload,
				"last_name":  "User",
			}
			
			resp := helper.MakeRequest("POST", "/api/cms/v1/auth/register", body, nil)
			
			// Should either reject or sanitize the input
			if resp.Code == http.StatusCreated {
				apiResp, err := helper.ParseResponse(resp)
				Expect(err).ToNot(HaveOccurred())
				
				userData := apiResp.Data["user"].(map[string]interface{})
				firstName := userData["first_name"].(string)
				
				// Should not contain script tags
				Expect(firstName).ToNot(ContainSubstring("<script>"))
				Expect(firstName).ToNot(ContainSubstring("</script>"))
			}
		})

		It("should prevent SQL injection in auth parameters", func() {
			// Try SQL injection in login
			sqlPayload := "admin'; DROP TABLE users; --"
			
			body := map[string]interface{}{
				"email":    sqlPayload,
				"password": "password",
			}
			
			resp := helper.MakeRequest("POST", "/api/cms/v1/auth/login", body, nil)
			
			// Should reject invalid email format
			Expect(resp.Code).To(Equal(http.StatusUnauthorized))
		})
	})

	Context("Race Condition Prevention", func() {
		It("should handle concurrent tenant switching", func() {
			token := testData.Tokens["admin"]["corp"]
			corpTenantID := testData.Tenants["corp"].ID
			startupTenantID := testData.Tenants["startup"].ID
			
			const numConcurrent = 10
			results := make(chan string, numConcurrent)
			
			// Switch between tenants concurrently
			for i := 0; i < numConcurrent; i++ {
				go func(index int) {
					var targetTenantID uint
					if index%2 == 0 {
						targetTenantID = corpTenantID
					} else {
						targetTenantID = startupTenantID
					}
					
					newTokens, err := helper.SwitchTenant(token, targetTenantID)
					if err != nil {
						results <- fmt.Sprintf("error-%d", index)
						return
					}
					
					results <- newTokens.AccessToken
				}(i)
			}
			
			// Collect results
			successCount := 0
			for i := 0; i < numConcurrent; i++ {
				result := <-results
				if !strings.HasPrefix(result, "error-") {
					successCount++
				}
			}
			
			// Most switches should succeed
			Expect(successCount).To(BeNumerically(">=", numConcurrent/2))
		})
	})

	Context("Memory and Resource Security", func() {
		It("should handle memory exhaustion attacks", func() {
			// Try to create many sessions
			const numRequests = 50
			var wg sync.WaitGroup
			
			for i := 0; i < numRequests; i++ {
				wg.Add(1)
				go func(index int) {
					defer wg.Done()
					
					// Try to login (which creates sessions)
					body := map[string]interface{}{
						"email":    fmt.Sprintf("<EMAIL>", index),
						"password": "password",
					}
					
					helper.MakeRequest("POST", "/api/cms/v1/auth/login", body, nil)
				}(i)
			}
			
			wg.Wait()
			
			// Server should remain responsive
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + token,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
			}
			
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusOK))
		})
	})
})