# Integration Test Makefile
# Run comprehensive integration tests for auth-tenant functionality

# Test environment variables
TEST_DB_HOST ?= localhost
TEST_DB_PORT ?= 3306
TEST_DB_USER ?= root
TEST_DB_PASSWORD ?= root
TEST_DB_NAME ?= blogapi_test

# Test configuration
TEST_TIMEOUT ?= 30m
TEST_VERBOSE ?= false
TEST_RACE ?= true
TEST_COVERAGE ?= true

# Colors for output
GREEN := \033[0;32m
YELLOW := \033[0;33m
RED := \033[0;31m
NC := \033[0m # No Color

.PHONY: help setup test test-auth-tenant test-security test-performance test-all clean coverage

help: ## Display this help message
	@echo "$(GREEN)Integration Test Commands$(NC)"
	@echo
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

setup: ## Setup test environment and database
	@echo "$(YELLOW)Setting up test environment...$(NC)"
	@echo "Creating test database if not exists..."
	@mysql -h$(TEST_DB_HOST) -P$(TEST_DB_PORT) -u$(TEST_DB_USER) -p$(TEST_DB_PASSWORD) -e "CREATE DATABASE IF NOT EXISTS $(TEST_DB_NAME);"
	@echo "Running migrations..."
	@cd ../.. && TEST_DB_HOST=$(TEST_DB_HOST) TEST_DB_PORT=$(TEST_DB_PORT) TEST_DB_USER=$(TEST_DB_USER) TEST_DB_PASSWORD=$(TEST_DB_PASSWORD) TEST_DB_NAME=$(TEST_DB_NAME) make migrate-up
	@echo "$(GREEN)Test environment setup complete$(NC)"

test: setup ## Run all integration tests
	@echo "$(YELLOW)Running all integration tests...$(NC)"
	@$(MAKE) test-all

test-auth-tenant: setup ## Run auth-tenant integration tests
	@echo "$(YELLOW)Running auth-tenant integration tests...$(NC)"
	@TEST_DB_HOST=$(TEST_DB_HOST) \
	 TEST_DB_PORT=$(TEST_DB_PORT) \
	 TEST_DB_USER=$(TEST_DB_USER) \
	 TEST_DB_PASSWORD=$(TEST_DB_PASSWORD) \
	 TEST_DB_NAME=$(TEST_DB_NAME) \
	 ginkgo $(if $(TEST_RACE),--race,) \
	        $(if $(TEST_VERBOSE),-v,) \
	        --timeout=$(TEST_TIMEOUT) \
	        --focus="Auth-Tenant Integration Tests" \
	        --junit-report=test-results-auth-tenant.xml \
	        --json-report=test-results-auth-tenant.json \
	        $(if $(TEST_COVERAGE),--cover --coverprofile=coverage-auth-tenant.out,) \
	        .

test-security: setup ## Run security integration tests
	@echo "$(YELLOW)Running security integration tests...$(NC)"
	@TEST_DB_HOST=$(TEST_DB_HOST) \
	 TEST_DB_PORT=$(TEST_DB_PORT) \
	 TEST_DB_USER=$(TEST_DB_USER) \
	 TEST_DB_PASSWORD=$(TEST_DB_PASSWORD) \
	 TEST_DB_NAME=$(TEST_DB_NAME) \
	 ginkgo $(if $(TEST_RACE),--race,) \
	        $(if $(TEST_VERBOSE),-v,) \
	        --timeout=$(TEST_TIMEOUT) \
	        --focus="Auth-Tenant Security Tests" \
	        --junit-report=test-results-security.xml \
	        --json-report=test-results-security.json \
	        $(if $(TEST_COVERAGE),--cover --coverprofile=coverage-security.out,) \
	        .

test-performance: setup ## Run performance integration tests
	@echo "$(YELLOW)Running performance integration tests...$(NC)"
	@TEST_DB_HOST=$(TEST_DB_HOST) \
	 TEST_DB_PORT=$(TEST_DB_PORT) \
	 TEST_DB_USER=$(TEST_DB_USER) \
	 TEST_DB_PASSWORD=$(TEST_DB_PASSWORD) \
	 TEST_DB_NAME=$(TEST_DB_NAME) \
	 ginkgo $(if $(TEST_RACE),--race,) \
	        $(if $(TEST_VERBOSE),-v,) \
	        --timeout=$(TEST_TIMEOUT) \
	        --focus="Auth-Tenant Performance Tests" \
	        --junit-report=test-results-performance.xml \
	        --json-report=test-results-performance.json \
	        $(if $(TEST_COVERAGE),--cover --coverprofile=coverage-performance.out,) \
	        .

test-all: setup ## Run all auth-tenant integration tests
	@echo "$(YELLOW)Running all auth-tenant integration tests...$(NC)"
	@TEST_DB_HOST=$(TEST_DB_HOST) \
	 TEST_DB_PORT=$(TEST_DB_PORT) \
	 TEST_DB_USER=$(TEST_DB_USER) \
	 TEST_DB_PASSWORD=$(TEST_DB_PASSWORD) \
	 TEST_DB_NAME=$(TEST_DB_NAME) \
	 ginkgo $(if $(TEST_RACE),--race,) \
	        $(if $(TEST_VERBOSE),-v,) \
	        --timeout=$(TEST_TIMEOUT) \
	        --junit-report=test-results-all.xml \
	        --json-report=test-results-all.json \
	        $(if $(TEST_COVERAGE),--cover --coverprofile=coverage-all.out,) \
	        .

test-ci: ## Run tests for CI environment
	@echo "$(YELLOW)Running tests for CI environment...$(NC)"
	@$(MAKE) test-all TEST_VERBOSE=true TEST_RACE=true TEST_COVERAGE=true

test-quick: setup ## Run quick tests (no race detection, no coverage)
	@echo "$(YELLOW)Running quick integration tests...$(NC)"
	@$(MAKE) test-all TEST_RACE=false TEST_COVERAGE=false

test-focus: setup ## Run focused tests (use FOCUS variable)
	@echo "$(YELLOW)Running focused tests: $(FOCUS)$(NC)"
	@TEST_DB_HOST=$(TEST_DB_HOST) \
	 TEST_DB_PORT=$(TEST_DB_PORT) \
	 TEST_DB_USER=$(TEST_DB_USER) \
	 TEST_DB_PASSWORD=$(TEST_DB_PASSWORD) \
	 TEST_DB_NAME=$(TEST_DB_NAME) \
	 ginkgo $(if $(TEST_RACE),--race,) \
	        $(if $(TEST_VERBOSE),-v,) \
	        --timeout=$(TEST_TIMEOUT) \
	        --focus="$(FOCUS)" \
	        .

benchmark: setup ## Run performance benchmarks
	@echo "$(YELLOW)Running performance benchmarks...$(NC)"
	@TEST_DB_HOST=$(TEST_DB_HOST) \
	 TEST_DB_PORT=$(TEST_DB_PORT) \
	 TEST_DB_USER=$(TEST_DB_USER) \
	 TEST_DB_PASSWORD=$(TEST_DB_PASSWORD) \
	 TEST_DB_NAME=$(TEST_DB_NAME) \
	 go test -bench=. -benchmem -timeout=$(TEST_TIMEOUT) ./...

coverage: test-all ## Generate coverage report
	@echo "$(YELLOW)Generating coverage report...$(NC)"
	@if [ -f coverage-all.out ]; then \
		go tool cover -html=coverage-all.out -o coverage-all.html; \
		go tool cover -func=coverage-all.out; \
		echo "$(GREEN)Coverage report generated: coverage-all.html$(NC)"; \
	else \
		echo "$(RED)Coverage file not found. Run 'make test-all' first.$(NC)"; \
	fi

clean: ## Clean test artifacts
	@echo "$(YELLOW)Cleaning test artifacts...$(NC)"
	@rm -f test-results-*.xml test-results-*.json
	@rm -f coverage-*.out coverage-*.html
	@rm -f integration.test
	@echo "$(GREEN)Clean complete$(NC)"

reset-db: ## Reset test database
	@echo "$(YELLOW)Resetting test database...$(NC)"
	@mysql -h$(TEST_DB_HOST) -P$(TEST_DB_PORT) -u$(TEST_DB_USER) -p$(TEST_DB_PASSWORD) -e "DROP DATABASE IF EXISTS $(TEST_DB_NAME);"
	@mysql -h$(TEST_DB_HOST) -P$(TEST_DB_PORT) -u$(TEST_DB_USER) -p$(TEST_DB_PASSWORD) -e "CREATE DATABASE $(TEST_DB_NAME);"
	@echo "$(GREEN)Test database reset complete$(NC)"

check-deps: ## Check test dependencies
	@echo "$(YELLOW)Checking test dependencies...$(NC)"
	@command -v ginkgo >/dev/null 2>&1 || { echo "$(RED)ginkgo is not installed. Install with: go install github.com/onsi/ginkgo/v2/ginkgo@latest$(NC)"; exit 1; }
	@command -v mysql >/dev/null 2>&1 || { echo "$(RED)mysql client is not installed$(NC)"; exit 1; }
	@echo "$(GREEN)All dependencies available$(NC)"

status: ## Show test environment status
	@echo "$(GREEN)Test Environment Status$(NC)"
	@echo "DB Host: $(TEST_DB_HOST)"
	@echo "DB Port: $(TEST_DB_PORT)"
	@echo "DB User: $(TEST_DB_USER)"
	@echo "DB Name: $(TEST_DB_NAME)"
	@echo "Test Timeout: $(TEST_TIMEOUT)"
	@echo "Race Detection: $(TEST_RACE)"
	@echo "Coverage: $(TEST_COVERAGE)"
	@echo "Verbose: $(TEST_VERBOSE)"

# Example usage commands
examples: ## Show example usage
	@echo "$(GREEN)Example Usage:$(NC)"
	@echo
	@echo "# Run all tests:"
	@echo "make test"
	@echo
	@echo "# Run specific test suite:"
	@echo "make test-auth-tenant"
	@echo "make test-security"
	@echo "make test-performance"
	@echo
	@echo "# Run with custom database:"
	@echo "make test TEST_DB_HOST=localhost TEST_DB_USER=testuser TEST_DB_PASSWORD=testpass"
	@echo
	@echo "# Run focused tests:"
	@echo "make test-focus FOCUS='User Authentication'"
	@echo
	@echo "# Run quick tests (no race detection):"
	@echo "make test-quick"
	@echo
	@echo "# Generate coverage report:"
	@echo "make coverage"
	@echo
	@echo "# Clean up artifacts:"
	@echo "make clean"

# Development workflow
dev: ## Development workflow: reset, setup, test
	@echo "$(YELLOW)Running development workflow...$(NC)"
	@$(MAKE) reset-db
	@$(MAKE) setup
	@$(MAKE) test-auth-tenant TEST_VERBOSE=true

# CI workflow
ci: check-deps ## CI workflow: setup, test, coverage
	@echo "$(YELLOW)Running CI workflow...$(NC)"
	@$(MAKE) setup
	@$(MAKE) test-ci
	@$(MAKE) coverage