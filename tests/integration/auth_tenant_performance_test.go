package integration

import (
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"github.com/tranthanhloi/wn-api-v3/tests/integration/helpers"
)

var _ = Describe("Auth-Tenant Performance Tests", func() {
	var (
		db     *gorm.DB
		router *gin.Engine
		logger utils.Logger
		helper *helpers.AuthTenantTestHelper
		
		testData *helpers.TestScenarioData
		cleanup  func()
	)

	BeforeEach(func() {
		// Setup test database
		var err error
		
		// Connect to test database
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			utils.GetEnv("TEST_DB_USER", "root"),
			utils.GetEnv("TEST_DB_PASSWORD", "root"),
			utils.GetEnv("TEST_DB_HOST", "localhost"),
			utils.GetEnv("TEST_DB_PORT", "3306"),
			utils.GetEnv("TEST_DB_NAME", "blogapi_test"),
		)
		
		db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
		Expect(err).ToNot(HaveOccurred())
		
		// Setup logger
		logger = utils.NewLogger("performance-test")
		
		// Setup validator
		v := validator.New()
		
		// Setup Gin router
		gin.SetMode(gin.TestMode)
		router = gin.New()
		
		// Register modules
		apiV1 := router.Group("/api/cms/v1")
		auth.RegisterRoutes(apiV1, db, v, logger)
		tenant.RegisterRoutes(apiV1, db, v, logger)
		user.RegisterRoutes(apiV1, db, v, logger)
		
		// Setup test helper
		helper = helpers.NewAuthTenantTestHelper(db, router, logger)
		
		// Create test scenario
		testData, err = helper.CreateTestScenario()
		Expect(err).ToNot(HaveOccurred())
		
		// Setup cleanup
		cleanup = func() {
			helper.CleanupTestData()
		}
	})

	AfterEach(func() {
		cleanup()
	})

	Context("Authentication Performance", func() {
		It("should handle concurrent login requests", func() {
			const numConcurrent = 20
			const timeout = 30 * time.Second
			
			results := make(chan time.Duration, numConcurrent)
			var wg sync.WaitGroup
			
			for i := 0; i < numConcurrent; i++ {
				wg.Add(1)
				go func(index int) {
					defer wg.Done()
					
					start := time.Now()
					
					// Each goroutine logs in with admin credentials
					body := map[string]interface{}{
						"email":    "<EMAIL>",
						"password": "AdminPass123",
					}
					
					resp := helper.MakeRequest("POST", "/api/cms/v1/auth/login", body, nil)
					duration := time.Since(start)
					
					// Should succeed
					Expect(resp.Code).To(Equal(http.StatusOK))
					
					results <- duration
				}(i)
			}
			
			// Wait for all goroutines to complete
			done := make(chan bool)
			go func() {
				wg.Wait()
				done <- true
			}()
			
			select {
			case <-done:
				// All completed successfully
			case <-time.After(timeout):
				Fail("Login performance test timed out")
			}
			
			// Collect and analyze results
			var totalDuration time.Duration
			var maxDuration time.Duration
			
			for i := 0; i < numConcurrent; i++ {
				duration := <-results
				totalDuration += duration
				if duration > maxDuration {
					maxDuration = duration
				}
			}
			
			avgDuration := totalDuration / time.Duration(numConcurrent)
			
			// Performance assertions
			Expect(avgDuration).To(BeNumerically("<", 500*time.Millisecond))
			Expect(maxDuration).To(BeNumerically("<", 2*time.Second))
			
			fmt.Printf("Login Performance: Avg=%v, Max=%v\n", avgDuration, maxDuration)
		})

		It("should handle concurrent token validation", func() {
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			const numConcurrent = 50
			results := make(chan time.Duration, numConcurrent)
			var wg sync.WaitGroup
			
			for i := 0; i < numConcurrent; i++ {
				wg.Add(1)
				go func() {
					defer wg.Done()
					
					start := time.Now()
					
					headers := map[string]string{
						"Authorization": "Bearer " + token,
						"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
					}
					
					resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
					duration := time.Since(start)
					
					Expect(resp.Code).To(Equal(http.StatusOK))
					results <- duration
				}()
			}
			
			wg.Wait()
			
			// Analyze results
			var totalDuration time.Duration
			var maxDuration time.Duration
			
			for i := 0; i < numConcurrent; i++ {
				duration := <-results
				totalDuration += duration
				if duration > maxDuration {
					maxDuration = duration
				}
			}
			
			avgDuration := totalDuration / time.Duration(numConcurrent)
			
			// Token validation should be fast
			Expect(avgDuration).To(BeNumerically("<", 100*time.Millisecond))
			Expect(maxDuration).To(BeNumerically("<", 500*time.Millisecond))
			
			fmt.Printf("Token Validation Performance: Avg=%v, Max=%v\n", avgDuration, maxDuration)
		})
	})

	Context("Tenant Switching Performance", func() {
		It("should handle concurrent tenant switching", func() {
			token := testData.Tokens["admin"]["corp"]
			corpTenantID := testData.Tenants["corp"].ID
			startupTenantID := testData.Tenants["startup"].ID
			
			const numConcurrent = 10
			results := make(chan time.Duration, numConcurrent)
			var wg sync.WaitGroup
			
			for i := 0; i < numConcurrent; i++ {
				wg.Add(1)
				go func(index int) {
					defer wg.Done()
					
					start := time.Now()
					
					// Alternate between tenants
					var targetTenantID uint
					if index%2 == 0 {
						targetTenantID = corpTenantID
					} else {
						targetTenantID = startupTenantID
					}
					
					_, err := helper.SwitchTenant(token, targetTenantID)
					duration := time.Since(start)
					
					Expect(err).ToNot(HaveOccurred())
					results <- duration
				}(i)
			}
			
			wg.Wait()
			
			// Analyze results
			var totalDuration time.Duration
			var maxDuration time.Duration
			
			for i := 0; i < numConcurrent; i++ {
				duration := <-results
				totalDuration += duration
				if duration > maxDuration {
					maxDuration = duration
				}
			}
			
			avgDuration := totalDuration / time.Duration(numConcurrent)
			
			// Tenant switching should be reasonably fast
			Expect(avgDuration).To(BeNumerically("<", 1*time.Second))
			Expect(maxDuration).To(BeNumerically("<", 3*time.Second))
			
			fmt.Printf("Tenant Switching Performance: Avg=%v, Max=%v\n", avgDuration, maxDuration)
		})
	})

	Context("Session Management Performance", func() {
		It("should handle concurrent session queries", func() {
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			const numConcurrent = 20
			results := make(chan time.Duration, numConcurrent)
			var wg sync.WaitGroup
			
			for i := 0; i < numConcurrent; i++ {
				wg.Add(1)
				go func() {
					defer wg.Done()
					
					start := time.Now()
					
					headers := map[string]string{
						"Authorization": "Bearer " + token,
						"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
					}
					
					resp := helper.MakeRequest("GET", "/api/cms/v1/auth/sessions", nil, headers)
					duration := time.Since(start)
					
					Expect(resp.Code).To(Equal(http.StatusOK))
					results <- duration
				}()
			}
			
			wg.Wait()
			
			// Analyze results
			var totalDuration time.Duration
			var maxDuration time.Duration
			
			for i := 0; i < numConcurrent; i++ {
				duration := <-results
				totalDuration += duration
				if duration > maxDuration {
					maxDuration = duration
				}
			}
			
			avgDuration := totalDuration / time.Duration(numConcurrent)
			
			// Session queries should be fast
			Expect(avgDuration).To(BeNumerically("<", 200*time.Millisecond))
			Expect(maxDuration).To(BeNumerically("<", 1*time.Second))
			
			fmt.Printf("Session Query Performance: Avg=%v, Max=%v\n", avgDuration, maxDuration)
		})
	})

	Context("Database Performance", func() {
		It("should handle multiple users with many tenants", func() {
			// Create additional users and tenants for load testing
			const numUsers = 10
			const numTenants = 5
			
			// Create test users
			var testUsers []*helpers.UserContext
			for i := 0; i < numUsers; i++ {
				email := fmt.Sprintf("<EMAIL>", i)
				user, err := helper.RegisterUser(email, "LoadTest123", "Load", fmt.Sprintf("Test%d", i))
				Expect(err).ToNot(HaveOccurred())
				
				userCtx := &helpers.UserContext{
					ID:    user.ID,
					Email: user.Email,
				}
				testUsers = append(testUsers, userCtx)
			}
			
			// Create test tenants
			var testTenants []*helpers.TenantContext
			for i := 0; i < numTenants; i++ {
				name := fmt.Sprintf("Load Test Tenant %d", i)
				slug := fmt.Sprintf("load-test-tenant-%d", i)
				tenant, err := helper.CreateTenant(name, slug)
				Expect(err).ToNot(HaveOccurred())
				
				tenantCtx := &helpers.TenantContext{
					ID:   tenant.ID,
					Name: tenant.Name,
					Slug: tenant.Slug,
				}
				testTenants = append(testTenants, tenantCtx)
			}
			
			// Add all users to all tenants
			for _, user := range testUsers {
				for _, tenant := range testTenants {
					err := helper.AddUserToTenant(user.ID, tenant.ID, "member", false)
					Expect(err).ToNot(HaveOccurred())
				}
			}
			
			// Test authentication performance with many tenant memberships
			start := time.Now()
			
			// Login first user
			tokens, err := helper.LoginUser(testUsers[0].Email, "LoadTest123")
			Expect(err).ToNot(HaveOccurred())
			
			loginDuration := time.Since(start)
			
			// Test tenant switching performance
			start = time.Now()
			
			_, err = helper.SwitchTenant(tokens.AccessToken, testTenants[1].ID)
			Expect(err).ToNot(HaveOccurred())
			
			switchDuration := time.Since(start)
			
			// Performance should remain reasonable even with many relationships
			Expect(loginDuration).To(BeNumerically("<", 2*time.Second))
			Expect(switchDuration).To(BeNumerically("<", 2*time.Second))
			
			fmt.Printf("Load Test Performance: Login=%v, Switch=%v\n", loginDuration, switchDuration)
		})
	})

	Context("Memory Usage", func() {
		It("should not leak memory during concurrent operations", func() {
			// This test ensures that concurrent operations don't cause memory leaks
			// Run multiple cycles of operations
			const numCycles = 5
			const numConcurrentPerCycle = 10
			
			for cycle := 0; cycle < numCycles; cycle++ {
				var wg sync.WaitGroup
				
				for i := 0; i < numConcurrentPerCycle; i++ {
					wg.Add(1)
					go func(index int) {
						defer wg.Done()
						
						// Perform various operations
						email := fmt.Sprintf("<EMAIL>", cycle, index)
						
						// Register user
						user, err := helper.RegisterUser(email, "MemTest123", "Mem", "Test")
						if err != nil {
							return
						}
						
						// Login
						tokens, err := helper.LoginUser(email, "MemTest123")
						if err != nil {
							return
						}
						
						// Access profile
						headers := map[string]string{
							"Authorization": "Bearer " + tokens.AccessToken,
						}
						
						helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
						
						// Logout
						helper.MakeRequest("POST", "/api/cms/v1/auth/logout", nil, headers)
					}(i)
				}
				
				wg.Wait()
				
				// Small delay between cycles
				time.Sleep(100 * time.Millisecond)
			}
			
			// Test that the system is still responsive
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			headers := map[string]string{
				"Authorization": "Bearer " + token,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
			}
			
			resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
			Expect(resp.Code).To(Equal(http.StatusOK))
		})
	})

	Context("Scalability", func() {
		It("should scale with number of tenants", func() {
			// Test performance with increasing number of tenants
			numTenants := []int{1, 5, 10, 20}
			results := make(map[int]time.Duration)
			
			for _, count := range numTenants {
				// Create user for this test
				email := fmt.Sprintf("<EMAIL>", count)
				user, err := helper.RegisterUser(email, "ScaleTest123", "Scale", "Test")
				Expect(err).ToNot(HaveOccurred())
				
				// Create tenants and add user to them
				for i := 0; i < count; i++ {
					tenantName := fmt.Sprintf("Scale Test Tenant %d-%d", count, i)
					tenantSlug := fmt.Sprintf("scale-test-tenant-%d-%d", count, i)
					
					tenant, err := helper.CreateTenant(tenantName, tenantSlug)
					Expect(err).ToNot(HaveOccurred())
					
					err = helper.AddUserToTenant(user.ID, tenant.ID, "member", i == 0)
					Expect(err).ToNot(HaveOccurred())
				}
				
				// Measure login performance
				start := time.Now()
				tokens, err := helper.LoginUser(email, "ScaleTest123")
				Expect(err).ToNot(HaveOccurred())
				loginDuration := time.Since(start)
				
				// Measure profile access performance
				start = time.Now()
				headers := map[string]string{
					"Authorization": "Bearer " + tokens.AccessToken,
				}
				resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
				profileDuration := time.Since(start)
				
				Expect(resp.Code).To(Equal(http.StatusOK))
				
				// Record total time
				results[count] = loginDuration + profileDuration
				
				fmt.Printf("Scale Test %d tenants: Login=%v, Profile=%v, Total=%v\n", 
					count, loginDuration, profileDuration, results[count])
			}
			
			// Performance should not degrade significantly with more tenants
			for i := 1; i < len(numTenants); i++ {
				prevCount := numTenants[i-1]
				currCount := numTenants[i]
				
				prevTime := results[prevCount]
				currTime := results[currCount]
				
				// Performance should not degrade more than 3x
				Expect(currTime).To(BeNumerically("<", 3*prevTime))
			}
		})
	})

	Context("Response Time Consistency", func() {
		It("should have consistent response times", func() {
			token := testData.Tokens["admin"]["corp"]
			tenantID := testData.Tenants["corp"].ID
			
			const numSamples = 20
			timings := make([]time.Duration, numSamples)
			
			headers := map[string]string{
				"Authorization": "Bearer " + token,
				"X-Tenant-ID":   strconv.Itoa(int(tenantID)),
			}
			
			// Collect timing samples
			for i := 0; i < numSamples; i++ {
				start := time.Now()
				resp := helper.MakeRequest("GET", "/api/cms/v1/auth/profile", nil, headers)
				timings[i] = time.Since(start)
				
				Expect(resp.Code).To(Equal(http.StatusOK))
				
				// Small delay between requests
				time.Sleep(10 * time.Millisecond)
			}
			
			// Calculate statistics
			var total time.Duration
			var min, max time.Duration = timings[0], timings[0]
			
			for _, timing := range timings {
				total += timing
				if timing < min {
					min = timing
				}
				if timing > max {
					max = timing
				}
			}
			
			avg := total / time.Duration(numSamples)
			
			// Calculate standard deviation
			var variance float64
			for _, timing := range timings {
				diff := float64(timing - avg)
				variance += diff * diff
			}
			variance /= float64(numSamples)
			stdDev := time.Duration(variance)
			
			// Consistency checks
			Expect(avg).To(BeNumerically("<", 200*time.Millisecond))
			Expect(max-min).To(BeNumerically("<", 500*time.Millisecond)) // Range should be reasonable
			Expect(stdDev).To(BeNumerically("<", 100*time.Millisecond))  // Low variance
			
			fmt.Printf("Response Time Consistency: Avg=%v, Min=%v, Max=%v, StdDev=%v\n", 
				avg, min, max, stdDev)
		})
	})
})