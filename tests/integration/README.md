# Integration Tests

Comprehensive integration tests for Blog API v3, using Ginkgo and Gomega framework.

## Test Suites

### 1. Auth-Tenant Integration Tests
Comprehensive tests for authentication and tenant management integration.

**Files:**
- `auth_tenant_integration_test.go` - Core auth-tenant integration tests
- `auth_tenant_security_test.go` - Security-focused integration tests  
- `auth_tenant_performance_test.go` - Performance and scalability tests
- `helpers/auth_tenant_helpers.go` - Test helper functions

### 2. Onboarding Flow Tests
End-to-end tests for the complete onboarding process.

**Files:**
- `onboarding_flow_test.go` - Onboarding workflow tests

### Test Structure

```
tests/integration/
├── integration_suite_test.go           # Main test suite
├── auth_tenant_integration_test.go     # Auth-tenant integration tests
├── auth_tenant_security_test.go        # Security tests
├── auth_tenant_performance_test.go     # Performance tests
├── onboarding_flow_test.go             # Onboarding flow tests
├── helpers/
│   └── auth_tenant_helpers.go          # Test helpers
├── Makefile                            # Test automation
└── README.md                           # This documentation
```

## Auth-Tenant Integration Tests

### Core Integration Tests
Tests the fundamental integration between authentication and tenant management systems.

**Test Categories:**
- **User Authentication with Tenant Context** - JWT tokens with tenant information
- **Tenant Isolation** - Cross-tenant access prevention
- **JWT Token Validation** - Token validation with tenant context
- **Tenant Switching** - Multi-tenant context switching
- **Role-Based Access Control** - Tenant-specific role enforcement
- **Session Management** - Tenant-scoped session handling
- **Integration Workflow** - End-to-end auth-tenant workflows

### Security Tests
Comprehensive security testing for auth-tenant integration.

**Security Categories:**
- **Token Security** - Token manipulation prevention, replay attacks
- **Tenant Isolation Security** - Lateral movement prevention, unauthorized access
- **Rate Limiting** - Brute force protection, session limits
- **Authorization Bypass Prevention** - Privilege escalation, role manipulation
- **Session Security** - Session hijacking prevention, suspicious activity detection
- **Timing Attack Prevention** - Consistent response times
- **Input Validation** - XSS prevention, SQL injection prevention
- **Race Condition Prevention** - Concurrent operation safety

### Performance Tests
Performance and scalability testing for auth-tenant operations.

**Performance Categories:**
- **Authentication Performance** - Concurrent login handling
- **Token Validation Performance** - High-throughput token validation
- **Tenant Switching Performance** - Multi-tenant context switching speed
- **Session Management Performance** - Session query optimization
- **Database Performance** - Multi-tenant relationship scaling
- **Memory Usage** - Memory leak prevention
- **Scalability** - Performance with increasing tenants/users
- **Response Time Consistency** - Reliable response times

### Test Features

#### Comprehensive Coverage
- ✅ **Authentication Flows** - Login, logout, token refresh
- ✅ **Tenant Management** - Creation, switching, isolation
- ✅ **Multi-User Scenarios** - Admin, member, guest roles
- ✅ **Security Validation** - Token security, access control
- ✅ **Performance Metrics** - Response times, throughput
- ✅ **Error Handling** - Graceful failure handling
- ✅ **Concurrent Operations** - Race condition prevention

#### Test Data Management
- **Automated Setup** - Test database initialization
- **Data Isolation** - Each test uses isolated data
- **Cleanup** - Automatic cleanup after each test
- **Seed Data** - Predefined test scenarios
- **Dynamic Data** - Generated test users/tenants

#### Helper Functions
- **Request Helpers** - HTTP request abstraction
- **Auth Helpers** - User registration, login, token management
- **Tenant Helpers** - Tenant creation, membership management
- **Assertion Helpers** - Database state validation
- **Performance Helpers** - Timing and metrics collection

### Test Scenarios

#### 1. Basic Auth-Tenant Integration
- User registers and gets assigned to tenant
- User logs in with tenant context
- User accesses tenant-scoped resources
- User logs out and token is invalidated

#### 2. Multi-Tenant User Management
- User has access to multiple tenants
- User switches between tenant contexts
- User's access is properly isolated per tenant
- User's role varies by tenant

#### 3. Security Validation
- Unauthorized access attempts are blocked
- Token manipulation is detected
- Cross-tenant access is prevented
- Rate limiting is enforced

#### 4. Performance Testing
- Concurrent authentication requests
- High-throughput token validation
- Scalability with many tenants/users
- Memory usage under load

#### 5. Error Scenarios
- Invalid token handling
- Expired token scenarios
- Database connection failures
- Network timeout handling

## Running Tests

### Prerequisites

1. **Database**: MySQL 8.0+ running
2. **Dependencies**: 
   ```bash
   go install github.com/onsi/ginkgo/v2/ginkgo@latest
   ```
3. **Environment Variables**:
   ```bash
   export TEST_DB_HOST=localhost
   export TEST_DB_PORT=3306
   export TEST_DB_USER=root
   export TEST_DB_PASSWORD=root
   export TEST_DB_NAME=blogapi_test
   ```

### Quick Start

```bash
# Check dependencies and setup
make check-deps
make setup

# Run all auth-tenant integration tests
make test

# Run specific test suites
make test-auth-tenant    # Core integration tests
make test-security       # Security tests
make test-performance    # Performance tests
```

### Test Commands

```bash
# Basic test execution
make test                # Run all tests with setup
make test-all           # Run all tests (requires setup)
make test-quick         # Quick tests (no race detection)

# Specific test suites
make test-auth-tenant   # Auth-tenant integration tests
make test-security      # Security-focused tests
make test-performance   # Performance and scalability tests

# Development and debugging
make test-focus FOCUS="User Authentication"  # Run focused tests
make dev                # Development workflow: reset, setup, test
make benchmark          # Run performance benchmarks

# CI/CD workflow
make test-ci            # CI workflow with coverage
make ci                 # Complete CI workflow
```

### Test Configuration

```bash
# Custom database configuration
make test TEST_DB_HOST=localhost TEST_DB_USER=testuser TEST_DB_PASSWORD=testpass

# Test behavior configuration
make test TEST_VERBOSE=true TEST_RACE=true TEST_COVERAGE=true TEST_TIMEOUT=45m

# Environment-specific tests
make test-ci            # CI environment
make test-quick         # Quick development tests
```

### Manual Commands

```bash
# Với script
./scripts/run-onboarding-tests.sh

# Với options
./scripts/run-onboarding-tests.sh --coverage --verbose

# Với Ginkgo (nếu có)
ginkgo -v -p ./tests/integration/

# Với go test
go test -v -race ./tests/integration/...
```

## Test Framework

### Ginkgo + Gomega

```go
import (
    . "github.com/onsi/ginkgo/v2"
    . "github.com/onsi/gomega"
)

var _ = Describe("Onboarding Flow", func() {
    Context("When a new user registers", func() {
        It("Should complete the full registration flow", func() {
            // Test implementation
            Expect(result).To(BeTrue())
        })
    })
})
```

### Test Structure

1. **BeforeEach**: Setup test database, seed data, initialize services
2. **Test Execution**: Run actual test scenarios
3. **Assertions**: Verify expected outcomes với Gomega matchers
4. **AfterEach**: Cleanup test data

### Helper Functions

```go
// Request helpers
makeAuthRequest(method, path, data)
makeTenantRequest(method, path, data, token)
makeWebsiteRequest(method, path, data, token, tenantID)
makeOnboardingRequest(method, path, data, token)

// Data helpers
generateStepCompletionData(stepType)
createTestUser(email, password, tenantID)
setupServices()
setupRouter()
```

## Test Data Management

### Database Lifecycle

1. **Setup**: Create test database
2. **Migration**: Run all migrations
3. **Seeding**: Insert seed data
4. **Test Execution**: Run tests với isolated data
5. **Cleanup**: Clean database sau mỗi test

### Tenant Isolation

- Mỗi test sử dụng unique tenant
- Cross-tenant access bị block
- Data được scoped theo tenant

### User Management

- Dynamic user creation với unique emails
- Password hashing qua services
- Session management cho authentication

## Assertions & Validations

### Database Assertions

```go
// User assertions
testHelper.AssertUserExists(userID)
testHelper.AssertUserHasStatus(userID, "active")

// Tenant assertions  
testHelper.AssertTenantExists(tenantID)
testHelper.AssertTenantHasStatus(tenantID, "active")

// Onboarding assertions
testHelper.AssertOnboardingProgressExists(userID)
testHelper.AssertOnboardingProgressCompleted(userID)
```

### API Response Assertions

```go
Expect(response.Code).To(Equal(http.StatusCreated))

var result map[string]interface{}
err := json.Unmarshal(response.Body.Bytes(), &result)
Expect(err).NotTo(HaveOccurred())
Expect(result["success"]).To(BeTrue())
```

### State Validations

```go
// Verify user state
Expect(createdUser.Status).To(Equal(models.UserStatusActive))
Expect(createdUser.EmailVerified).To(BeTrue())

// Verify onboarding state
Expect(progress.Status).To(Equal("completed"))
Expect(progress.CompletedAt).NotTo(BeNil())

// Verify tenant state
Expect(tenant.Status).To(Equal(models.TenantStatusActive))
```

## Performance Considerations

### Test Timeout

- Default: 10 minutes cho full suite
- Individual test: 5 minutes
- Database operations: 30 seconds

### Parallel Execution

- Tests có thể chạy parallel với Ginkgo
- Database isolation đảm bảo no conflicts
- Race detection enabled

### Memory Management

- Database connections được manage properly
- Test data được cleanup sau mỗi test
- Coverage reports generated on demand

## Troubleshooting

### Common Issues

1. **Database Connection**:
   ```bash
   # Check MySQL service
   sudo service mysql status
   
   # Check port
   netstat -tulpn | grep :3306
   ```

2. **Permission Issues**:
   ```bash
   # Check database permissions
   mysql -u root -p -e "SHOW GRANTS FOR 'root'@'localhost';"
   ```

3. **Migration Failures**:
   ```bash
   # Reset migrations
   make migrate-down
   make migrate-up
   ```

### Debug Mode

```bash
# Verbose output
./scripts/run-onboarding-tests.sh --verbose

# với debug logs
TEST_DEBUG=true make test-onboarding

# Single test focus
ginkgo -v --focus="Complete Onboarding Flow" ./tests/integration/
```

### Log Analysis

```bash
# Test logs
tail -f /tmp/onboarding-test.log

# Database logs
sudo tail -f /var/log/mysql/error.log

# Application logs
tail -f logs/test.log
```

## CI/CD Integration

### GitHub Actions

```yaml
- name: Run Onboarding Tests
  run: make test-onboarding-ci
  env:
    TEST_DB_HOST: localhost
    TEST_DB_USER: root
    TEST_DB_PASSWORD: root
```

### Docker Testing

```bash
# Run tests trong Docker
docker-compose -f docker-compose.test.yml up --build test-onboarding

# Cleanup
docker-compose -f docker-compose.test.yml down -v
```

## Contributing

### Adding New Tests

1. Create test file trong `tests/integration/`
2. Follow Ginkgo/Gomega patterns
3. Add appropriate setup/cleanup
4. Update documentation

### Test Guidelines

- Descriptive test names
- Proper error handling
- Comprehensive assertions
- Cleanup sau test
- Performance considerations

### Review Checklist

- [ ] Tests pass locally
- [ ] Coverage adequate (>80%)
- [ ] No database leaks
- [ ] Proper tenant isolation
- [ ] Documentation updated