#!/bin/bash

# Test script for J<PERSON>ger tracing setup
set -e

echo "🔍 Testing Jaeger Tracing Setup..."

# Check if <PERSON><PERSON><PERSON> is running
echo "1. Checking if <PERSON><PERSON><PERSON> is running..."
if curl -s http://localhost:16686/api/services > /dev/null; then
    echo "   ✅ Jaeger UI is accessible at http://localhost:16686"
else
    echo "   ❌ Jaeger UI is not accessible"
    exit 1
fi

# Check <PERSON><PERSON><PERSON> health
echo "2. Checking <PERSON><PERSON><PERSON> health..."
if curl -s http://localhost:14269/health > /dev/null; then
    echo "   ✅ Jaeger health endpoint is responding"
else
    echo "   ❌ <PERSON>aeger health endpoint is not responding"
    exit 1
fi

# Check collector endpoint
echo "3. Checking <PERSON>aeger collector endpoint..."
if curl -s http://localhost:14268/api/traces > /dev/null; then
    echo "   ✅ Jaeger collector endpoint is accessible"
else
    echo "   ❌ Jaeger collector endpoint is not accessible"
    exit 1
fi

# Test environment variables
echo "4. Testing environment variables..."
export TRACING_ENABLED=true
export TRACING_SERVICE_NAME=blog-api-v3
export JAEGER_ENDPOINT=http://localhost:14268/api/traces
export TRACING_DEBUG=true
export TRACING_SAMPLING=always
export TRACING_SAMPLE_RATE=1.0

echo "   ✅ Environment variables set:"
echo "      - TRACING_ENABLED: $TRACING_ENABLED"
echo "      - TRACING_SERVICE_NAME: $TRACING_SERVICE_NAME"
echo "      - JAEGER_ENDPOINT: $JAEGER_ENDPOINT"
echo "      - TRACING_DEBUG: $TRACING_DEBUG"
echo "      - TRACING_SAMPLING: $TRACING_SAMPLING"
echo "      - TRACING_SAMPLE_RATE: $TRACING_SAMPLE_RATE"

# Test Go module compilation
echo "5. Testing Go module compilation..."
cd "$(dirname "$0")/.."
if go build -o ./bin/test-tracing ./cmd/server/tracing_integration.go; then
    echo "   ✅ Tracing integration compiles successfully"
    rm -f ./bin/test-tracing
else
    echo "   ❌ Tracing integration compilation failed"
    exit 1
fi

# Test configuration validation
echo "6. Testing configuration..."
if [ -f ".env.tracing" ]; then
    echo "   ✅ Tracing configuration file exists"
    source .env.tracing
    echo "   ✅ Configuration loaded successfully"
else
    echo "   ❌ Tracing configuration file not found"
    exit 1
fi

echo ""
echo "🎉 All Jaeger tracing tests passed!"
echo ""
echo "📊 Access Points:"
echo "   - Jaeger UI: http://localhost:16686"
echo "   - Jaeger Health: http://localhost:14269/health"
echo "   - Jaeger Collector: http://localhost:14268/api/traces"
echo ""
echo "🚀 Next Steps:"
echo "   1. Start your application with tracing enabled"
echo "   2. Make some API requests to generate traces"
echo "   3. Visit the Jaeger UI to view traces"
echo "   4. Select 'blog-api-v3' service to see traces"
echo ""
echo "💡 Example API calls to generate traces:"
echo "   curl http://localhost:8080/health"
echo "   curl http://localhost:8080/api/v1/users"
echo "   curl http://localhost:8080/tracing/info"
echo ""
echo "📖 Documentation:"
echo "   - Setup Guide: docs/tracing/jaeger-setup.md"
echo "   - Integration Example: examples/tracing/integration_example.go"
echo "   - Configuration: .env.tracing"
echo ""