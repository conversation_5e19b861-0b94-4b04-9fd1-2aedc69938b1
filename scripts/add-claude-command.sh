#!/bin/bash

# Add Claude Command Script
# This script helps create new Claude Code commands quickly
# Usage: ./scripts/add-claude-command.sh <command-name> <description>

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CLAUDE_COMMANDS_DIR="${PROJECT_ROOT}/.claude/commands"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Show usage
show_usage() {
    echo "Usage: $0 <command-name> <description> [template]"
    echo ""
    echo "Arguments:"
    echo "  command-name    Name of the command (e.g., 'analyze-performance')"
    echo "  description     Brief description of what the command does"
    echo "  template        Optional template to use (default, debug, test, deploy, monitor)"
    echo ""
    echo "Examples:"
    echo "  $0 analyze-performance 'Analyze application performance metrics'"
    echo "  $0 fix-security-issue 'Fix security vulnerability' debug"
    echo "  $0 deploy-staging 'Deploy to staging environment' deploy"
    echo ""
    echo "Available templates:"
    echo "  default  - Generic command template"
    echo "  debug    - Debug and troubleshooting template"
    echo "  test     - Testing and validation template"
    echo "  deploy   - Deployment and infrastructure template"
    echo "  monitor  - Monitoring and analysis template"
    echo "  docs     - Documentation template"
}

# Validate inputs
validate_inputs() {
    if [ $# -lt 2 ]; then
        print_error "Missing required arguments"
        show_usage
        exit 1
    fi
    
    COMMAND_NAME="$1"
    DESCRIPTION="$2"
    TEMPLATE="${3:-default}"
    
    # Validate command name
    if [[ ! "$COMMAND_NAME" =~ ^[a-z0-9-]+$ ]]; then
        print_error "Command name must contain only lowercase letters, numbers, and hyphens"
        exit 1
    fi
    
    # Create commands directory if it doesn't exist
    if [ ! -d "$CLAUDE_COMMANDS_DIR" ]; then
        mkdir -p "$CLAUDE_COMMANDS_DIR"
        print_status "Created .claude/commands directory"
    fi
    
    COMMAND_FILE="${CLAUDE_COMMANDS_DIR}/${COMMAND_NAME}.md"
    
    # Check if command already exists
    if [ -f "$COMMAND_FILE" ]; then
        print_warning "Command '$COMMAND_NAME' already exists"
        read -p "Do you want to overwrite it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Command creation cancelled"
            exit 0
        fi
    fi
}

# Create default template
create_default_template() {
    cat > "$COMMAND_FILE" << EOF
# ${DESCRIPTION}

Please ${DESCRIPTION,,}: \$ARGUMENTS.

Follow these steps:
1. Analyze the current state
2. Identify requirements and constraints
3. Plan the approach
4. Implement the solution
5. Test and validate
6. Document the changes

Focus on:
- Code quality and maintainability
- Error handling and logging
- Performance considerations
- Security best practices
- Test coverage
- Documentation updates
EOF
}

# Create debug template
create_debug_template() {
    cat > "$COMMAND_FILE" << EOF
# ${DESCRIPTION}

Please debug and resolve: \$ARGUMENTS.

Follow these steps:
1. Reproduce the issue
2. Analyze logs and error messages
3. Identify the root cause
4. Review related code sections
5. Implement a fix
6. Test the solution thoroughly
7. Add preventive measures (tests, monitoring)
8. Document the resolution

Commands to use:
- Review application logs: \`tail -f server.log\`
- Check system status: \`curl http://localhost:9077/health/status\`
- Run specific tests: \`go test -v ./path/to/test\`

Focus on:
- Root cause analysis
- Comprehensive testing
- Error prevention
- Monitoring improvements
- Documentation of fix
EOF
}

# Create test template
create_test_template() {
    cat > "$COMMAND_FILE" << EOF
# ${DESCRIPTION}

Please test and validate: \$ARGUMENTS.

Follow these steps:
1. Understand the testing requirements
2. Create or update test cases
3. Run the test suite
4. Analyze test results and coverage
5. Fix any failing tests
6. Optimize test performance
7. Update test documentation

Commands to use:
- Run all tests: \`go test ./...\`
- Run with coverage: \`go test -cover ./...\`
- Run specific module tests: \`go test -v ./internal/modules/\$MODULE/...\`
- Run Bruno API tests: \`bru run api-tests/bruno --env local\`

Focus on:
- Test coverage and quality
- Edge case handling
- Performance testing
- Integration testing
- API validation
EOF
}

# Create deploy template
create_deploy_template() {
    cat > "$COMMAND_FILE" << EOF
# ${DESCRIPTION}

Please deploy: \$ARGUMENTS.

Follow these steps:
1. Verify code quality and tests
2. Build the application
3. Run database migrations
4. Deploy to target environment
5. Verify deployment health
6. Monitor application startup
7. Validate critical functionality
8. Update deployment documentation

Commands to use:
- Build application: \`make build\`
- Run migrations: \`make migrate-up\`
- Start services: \`docker-compose up -d\`
- Health check: \`curl http://localhost:9077/health/live\`

Focus on:
- Zero-downtime deployment
- Database migration safety
- Health monitoring
- Rollback procedures
- Performance validation
EOF
}

# Create monitor template
create_monitor_template() {
    cat > "$COMMAND_FILE" << EOF
# ${DESCRIPTION}

Please monitor and analyze: \$ARGUMENTS.

Follow these steps:
1. Check system health metrics
2. Analyze performance indicators
3. Review error rates and logs
4. Monitor resource usage
5. Identify bottlenecks or issues
6. Generate performance report
7. Recommend optimizations

Commands to use:
- Health check: \`curl http://localhost:9077/health/status\`
- View logs: \`tail -f server.log\`
- Monitor resources: \`top\` or \`htop\`
- Database metrics: Check connection stats

Focus on:
- Performance optimization
- Error rate analysis
- Resource utilization
- Scalability assessment
- Monitoring setup
EOF
}

# Create docs template
create_docs_template() {
    cat > "$COMMAND_FILE" << EOF
# ${DESCRIPTION}

Please update documentation for: \$ARGUMENTS.

Follow these steps:
1. Review existing documentation
2. Identify outdated or missing information
3. Update API documentation
4. Update code comments and README files
5. Review architecture diagrams
6. Update deployment guides
7. Test documentation examples
8. Ensure consistency across docs

Focus on:
- Accuracy and completeness
- Code examples and usage
- API reference updates
- Architecture explanations
- Deployment instructions
- Developer onboarding
EOF
}

# Create command based on template
create_command() {
    case "$TEMPLATE" in
        "default")
            create_default_template
            ;;
        "debug")
            create_debug_template
            ;;
        "test")
            create_test_template
            ;;
        "deploy")
            create_deploy_template
            ;;
        "monitor")
            create_monitor_template
            ;;
        "docs")
            create_docs_template
            ;;
        *)
            print_error "Unknown template: $TEMPLATE"
            print_info "Available templates: default, debug, test, deploy, monitor, docs"
            exit 1
            ;;
    esac
}

# Main execution
main() {
    validate_inputs "$@"
    
    print_status "Creating Claude Code command: $COMMAND_NAME"
    print_info "Description: $DESCRIPTION"
    print_info "Template: $TEMPLATE"
    print_info "File: $COMMAND_FILE"
    
    create_command
    
    print_status "Command created successfully! 🎉"
    print_info "Use '/$COMMAND_NAME' in Claude Code to access this command"
    
    # Show the created command
    print_status "Command preview:"
    echo "$(head -10 "$COMMAND_FILE")"
    echo "..."
}

# Run the main function
main "$@"