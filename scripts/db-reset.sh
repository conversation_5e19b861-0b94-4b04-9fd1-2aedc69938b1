#!/bin/bash

# Database Reset Wrapper Script
# Cung cấp các lệnh ngắn gọn để reset database

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_usage() {
    echo "Database Reset Utility"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  full         Complete reset: drop all tables + migrate + seed"
    echo "  migrate      Run migrations only"
    echo "  seed         Run seeders only"
    echo "  ms           Run migrations + seeders (no reset)"
    echo "  drop         Drop all tables only"
    echo ""
    echo "Options:"
    echo "  -y           Skip confirmation (for full reset)"
    echo "  -m <module>  Specific module only"
    echo "  -s <seeders> Specific seeders (comma-separated)"
    echo "  --no-seeds   Skip seeders"
    echo ""
    echo "Examples:"
    echo "  $0 full                    # Complete reset with confirmation"
    echo "  $0 full -y                 # Complete reset without confirmation"
    echo "  $0 full -m tenant          # Reset only tenant module"
    echo "  $0 ms                      # Migrate and seed (no reset)"
    echo "  $0 migrate -m auth         # Only auth module migrations"
    echo "  $0 seed -s user,tenant     # Only user and tenant seeders"
    echo "  $0 drop                    # Drop all tables only"
}

if [ $# -eq 0 ]; then
    print_usage
    exit 1
fi

COMMAND=$1
shift

case $COMMAND in
    full)
        echo -e "${BLUE}Running full database reset...${NC}"
        ./scripts/reset-db-full.sh "$@"
        ;;
    migrate)
        echo -e "${BLUE}Running migrations...${NC}"
        if [ $# -eq 0 ]; then
            make migrate-up
        else
            ./scripts/migrate-and-seed.sh --no-seeds "$@"
        fi
        ;;
    seed)
        echo -e "${BLUE}Running seeders...${NC}"
        if [ $# -eq 0 ]; then
            make seed-run
        else
            # Extract -s flag for seeders
            while [[ $# -gt 0 ]]; do
                case $1 in
                    -s)
                        SEEDERS="$2"
                        shift 2
                        ;;
                    *)
                        shift
                        ;;
                esac
            done
            if [ -n "$SEEDERS" ]; then
                SEEDERS="$SEEDERS" make seed-run
            else
                make seed-run
            fi
        fi
        ;;
    ms)
        echo -e "${BLUE}Running migrations and seeders...${NC}"
        ./scripts/migrate-and-seed.sh "$@"
        ;;
    drop)
        echo -e "${YELLOW}Dropping all tables...${NC}"
        make reset-db
        ;;
    -h|--help|help)
        print_usage
        ;;
    *)
        echo "Unknown command: $COMMAND"
        echo ""
        print_usage
        exit 1
        ;;
esac