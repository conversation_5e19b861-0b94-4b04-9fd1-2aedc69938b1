#!/bin/bash

# Migrate and Seed Script
# Chạy migrations và seeders mà không reset database

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_message() {
    echo -e "${2}${1}${NC}"
}

print_header() {
    echo -e "\n${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}\n"
}

print_success() {
    print_message "$1" "$GREEN"
}

print_warning() {
    print_message "$1" "$YELLOW"
}

print_error() {
    print_message "$1" "$RED"
}

# Function to run command with error handling
run_command() {
    local cmd="$1"
    local description="$2"
    
    print_message "Running: $description" "$BLUE"
    if eval "$cmd"; then
        print_success "✓ $description completed successfully"
    else
        print_error "✗ $description failed"
        exit 1
    fi
}

# Parse command line arguments
SKIP_SEEDS=false
MODULE=""
SEEDERS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-seeds)
            SKIP_SEEDS=true
            shift
            ;;
        -m|--module)
            MODULE="$2"
            shift 2
            ;;
        -s|--seeders)
            SEEDERS="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --no-seeds       Skip running seeders"
            echo "  -m, --module     Run migrations for specific module only"
            echo "  -s, --seeders    Run specific seeders (comma-separated)"
            echo "  -h, --help       Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                          # Run all migrations and seeders"
            echo "  $0 --no-seeds               # Skip seeders"
            echo "  $0 -m tenant                # Only run tenant module migrations"
            echo "  $0 -s user,tenant           # Run only user and tenant seeders"
            echo "  $0 -m auth --no-seeds       # Only auth module, no seeders"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Main script
print_header "Database Migration & Seed Script"

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found in current directory"
    exit 1
fi

# Load environment variables
set -a
source .env
set +a

# Check required environment variables
if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL not found in .env file"
    exit 1
fi

# Parse database info from DATABASE_URL
DB_INFO=$(echo $DATABASE_URL | sed 's/.*@tcp(\([^)]*\)).*/\1/')
DB_HOST=$(echo $DB_INFO | cut -d':' -f1)
DB_PORT=$(echo $DB_INFO | cut -d':' -f2)
DB_NAME=$(echo $DATABASE_URL | sed 's/.*\/\([^?]*\).*/\1/')

print_message "Database Configuration:" "$BLUE"
print_message "  Host: $DB_HOST" "$BLUE"
print_message "  Port: $DB_PORT" "$BLUE"
print_message "  Database: $DB_NAME" "$BLUE"

if [ -n "$MODULE" ]; then
    print_message "  Module: $MODULE" "$BLUE"
fi

if [ -n "$SEEDERS" ]; then
    print_message "  Seeders: $SEEDERS" "$BLUE"
fi

if [ "$SKIP_SEEDS" = true ]; then
    print_message "  Seeders: DISABLED" "$YELLOW"
fi

# Check database connectivity
print_message "Checking database connectivity..." "$BLUE"
if ! mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
    print_error "Failed to connect to database. Please check your database configuration."
    exit 1
fi
print_success "✓ Database connection successful"

# Step 1: Run migrations
print_header "Step 1: Running Migrations"

# Disable foreign key checks to handle circular dependencies
print_message "Disabling foreign key checks to handle circular dependencies..." "$BLUE"
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -D "$DB_NAME" -e "SET FOREIGN_KEY_CHECKS = 0;" 2>/dev/null || print_warning "⚠ Could not disable foreign key checks"

if [ -n "$MODULE" ]; then
    run_command "go run ./cmd/migrate -command up -module $MODULE" "Run migrations for module: $MODULE"
else
    # Run migrations in logical order (foreign keys disabled)
    print_message "Running migrations in logical order..." "$BLUE"
    
    # Core modules first
    run_command "go run ./cmd/migrate -command up -module a_tenant" "Run tenant migrations"
    run_command "go run ./cmd/migrate -command up -module b_website" "Run website migrations"
    
    # User and RBAC modules (circular dependency resolved by disabled foreign keys)
    run_command "go run ./cmd/migrate -command up -module c_user" "Run user migrations"
    run_command "go run ./cmd/migrate -command up -module e_rbac" "Run RBAC migrations"
    run_command "go run ./cmd/migrate -command up -module d_auth" "Run auth migrations"
    
    # Feature modules
    run_command "go run ./cmd/migrate -command up -module f_onboarding" "Run onboarding migrations"
    run_command "go run ./cmd/migrate -command up -module g_blog" "Run blog migrations"
    run_command "go run ./cmd/migrate -command up -module h_seo" "Run SEO migrations"
    run_command "go run ./cmd/migrate -command up -module i_media" "Run media migrations"
    run_command "go run ./cmd/migrate -command up -module j_ai" "Run AI migrations"
    run_command "go run ./cmd/migrate -command up -module k_ads" "Run ads migrations"
    run_command "go run ./cmd/migrate -command up -module l_notification" "Run notification migrations"
    run_command "go run ./cmd/migrate -command up -module m_trello" "Run trello migrations"
    run_command "go run ./cmd/migrate -command up -module n_settings" "Run settings migrations"
    
    # Skip problematic migration for now
    print_message "Skipping problematic API key analytics migration (partitioning issue)" "$YELLOW"
    run_command "go run ./cmd/migrate -command up -module o_apikey" "Run API key migrations (excluding analytics)" || true
fi

# Re-enable foreign key checks
print_message "Re-enabling foreign key checks..." "$BLUE"
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -D "$DB_NAME" -e "SET FOREIGN_KEY_CHECKS = 1;" 2>/dev/null || print_warning "⚠ Could not re-enable foreign key checks"

# Step 2: Run seeders (if enabled)
if [ "$SKIP_SEEDS" = false ]; then
    print_header "Step 2: Running Seeders"
    if [ -n "$SEEDERS" ]; then
        run_command "go run ./cmd/seed -command run -seeders $SEEDERS" "Run specific seeders: $SEEDERS"
    else
        run_command "go run ./cmd/seed -command run" "Run all seeders"
    fi
else
    print_message "Skipping seeders as requested" "$YELLOW"
fi

# Step 3: Show migration status
print_header "Step 3: Migration Status"
if [ -n "$MODULE" ]; then
    run_command "go run ./cmd/migrate -command status -module $MODULE" "Show migration status for module: $MODULE"
else
    run_command "go run ./cmd/migrate -command status" "Show migration status"
fi

# Final success message
print_header "Migration & Seed Complete! 🎉"
print_success "✅ Migrations executed"
if [ "$SKIP_SEEDS" = false ]; then
    print_success "✅ Seeders executed"
fi
print_success "✅ Database is ready for use"

echo ""
print_message "Database '$DB_NAME' has been successfully migrated and seeded!" "$GREEN"
echo ""