#!/bin/bash

# Reset Database Full Script
# Xóa database, chạy lại migrations và seeders

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_message() {
    echo -e "${2}${1}${NC}"
}

print_header() {
    echo -e "\n${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}\n"
}

print_success() {
    print_message "$1" "$GREEN"
}

print_warning() {
    print_message "$1" "$YELLOW"
}

print_error() {
    print_message "$1" "$RED"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run command with error handling
run_command() {
    local cmd="$1"
    local description="$2"
    local allow_fail="$3"
    
    print_message "Running: $description" "$BLUE"
    if eval "$cmd"; then
        print_success "✓ $description completed successfully"
    else
        if [ "$allow_fail" = "true" ]; then
            print_warning "⚠ $description failed (continuing anyway)"
        else
            print_error "✗ $description failed"
            exit 1
        fi
    fi
}

# Parse command line arguments
SKIP_CONFIRMATION=false
RUN_SEEDS=true
MODULE=""
SEEDERS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -y|--yes)
            SKIP_CONFIRMATION=true
            shift
            ;;
        --no-seeds)
            RUN_SEEDS=false
            shift
            ;;
        -m|--module)
            MODULE="$2"
            shift 2
            ;;
        -s|--seeders)
            SEEDERS="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  -y, --yes        Skip confirmation prompt"
            echo "  --no-seeds       Skip running seeders"
            echo "  -m, --module     Run migrations for specific module only"
            echo "  -s, --seeders    Run specific seeders (comma-separated)"
            echo "  -h, --help       Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                          # Reset all, run all migrations and seeders"
            echo "  $0 -y                       # Skip confirmation"
            echo "  $0 --no-seeds               # Skip seeders"
            echo "  $0 -m tenant                # Only run tenant module migrations"
            echo "  $0 -s user,tenant           # Run only user and tenant seeders"
            echo "  $0 -y -m auth --no-seeds    # Skip confirmation, only auth module, no seeders"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Main script
print_header "Database Reset & Migration Script"

# Check if .env file exists
ENV_FILE="$(pwd)/.env"
if [ ! -f "$ENV_FILE" ]; then
    print_error ".env file not found in current directory: $ENV_FILE"
    exit 1
fi

# Load environment variables
print_message "Loading environment from: $ENV_FILE" "$BLUE"
set -a
source "$ENV_FILE"
set +a

# Check required environment variables
if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL not found in .env file"
    exit 1
fi

# Parse database info from DATABASE_URL
DB_INFO=$(echo $DATABASE_URL | sed 's/.*@tcp(\([^)]*\)).*/\1/')
DB_HOST=$(echo $DB_INFO | cut -d':' -f1)
DB_PORT=$(echo $DB_INFO | cut -d':' -f2)
DB_NAME=$(echo $DATABASE_URL | sed 's/.*\/\([^?]*\).*/\1/')

print_message "Database Configuration:" "$BLUE"
print_message "  Host: $DB_HOST" "$BLUE"
print_message "  Port: $DB_PORT" "$BLUE"
print_message "  Database: $DB_NAME" "$BLUE"

if [ -n "$MODULE" ]; then
    print_message "  Module: $MODULE" "$BLUE"
fi

if [ -n "$SEEDERS" ]; then
    print_message "  Seeders: $SEEDERS" "$BLUE"
fi

if [ "$RUN_SEEDS" = false ]; then
    print_message "  Seeders: DISABLED" "$YELLOW"
fi

# Confirmation prompt
if [ "$SKIP_CONFIRMATION" = false ]; then
    echo ""
    print_warning "⚠️  WARNING: This will COMPLETELY DELETE all data in database '$DB_NAME'"
    print_warning "   This action cannot be undone!"
    echo ""
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_message "Operation cancelled by user" "$YELLOW"
        exit 0
    fi
fi

echo ""
print_header "Starting Database Reset Process"

# Step 1: Check if database is accessible
print_message "Checking database connectivity..." "$BLUE"
if ! mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
    print_error "Failed to connect to database. Please check your database configuration."
    exit 1
fi
print_success "✓ Database connection successful"

# Step 2: Reset database (drop all tables)
print_header "Step 1: Dropping All Tables"
run_command "go run ./cmd/reset-db" "Drop all tables"

# Step 3: Run migrations
print_header "Step 2: Running Migrations"

# Disable foreign key checks to handle circular dependencies
print_message "Disabling foreign key checks to handle circular dependencies..." "$BLUE"
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -D "$DB_NAME" -e "SET FOREIGN_KEY_CHECKS = 0;" 2>/dev/null || print_warning "⚠ Could not disable foreign key checks"

if [ -n "$MODULE" ]; then
    run_command "go run ./cmd/migrate -command up -module $MODULE" "Run migrations for module: $MODULE"
else
    # Run migrations in logical order (foreign keys disabled)
    print_message "Running migrations in logical order..." "$BLUE"
    
    # Core modules first
    run_command "go run ./cmd/migrate -command up -module a_tenant" "Run tenant migrations"
    run_command "go run ./cmd/migrate -command up -module b_website" "Run website migrations"
    
    # User and RBAC modules (circular dependency resolved by splitting foreign key constraints)
    run_command "go run ./cmd/migrate -command up -module c_user" "Run user migrations"
    run_command "go run ./cmd/migrate -command up -module e_rbac" "Run RBAC migrations"
    
    # Add foreign key constraints after both modules are migrated
    print_message "Adding foreign key constraints for circular dependencies..." "$BLUE"
    run_command "go run ./cmd/migrate -command up -module z_constraints" "Add foreign key constraints"
    
    run_command "go run ./cmd/migrate -command up -module d_auth" "Run auth migrations"
    
    # Feature modules
    run_command "go run ./cmd/migrate -command up -module f_onboarding" "Run onboarding migrations"
    run_command "go run ./cmd/migrate -command up -module g_blog" "Run blog migrations"
    run_command "go run ./cmd/migrate -command up -module h_seo" "Run SEO migrations"
    run_command "go run ./cmd/migrate -command up -module i_media" "Run media migrations"
    run_command "go run ./cmd/migrate -command up -module j_ai" "Run AI migrations"
    run_command "go run ./cmd/migrate -command up -module k_ads" "Run ads migrations"
    run_command "go run ./cmd/migrate -command up -module l_notification" "Run notification migrations"
    run_command "go run ./cmd/migrate -command up -module m_trello" "Run trello migrations"
    run_command "go run ./cmd/migrate -command up -module n_settings" "Run settings migrations"
    
    # Skip problematic migration for now
    print_message "Skipping problematic API key analytics migration (partitioning issue)" "$YELLOW"
    run_command "go run ./cmd/migrate -command up -module o_apikey" "Run API key migrations (excluding analytics)" || true
fi

# Re-enable foreign key checks
print_message "Re-enabling foreign key checks..." "$BLUE"
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -D "$DB_NAME" -e "SET FOREIGN_KEY_CHECKS = 1;" 2>/dev/null || print_warning "⚠ Could not re-enable foreign key checks"

# Step 4: Run seeders (if enabled)
if [ "$RUN_SEEDS" = true ]; then
    print_header "Step 3: Running Seeders"
    if [ -n "$SEEDERS" ]; then
        run_command "go run ./cmd/seed -command run -seeders $SEEDERS" "Run specific seeders: $SEEDERS"
    else
        run_command "go run ./cmd/seed -command run" "Run all seeders"
    fi
else
    print_message "Skipping seeders as requested" "$YELLOW"
fi

# Step 5: Verify database state
print_header "Step 4: Verification"
print_message "Checking database tables..." "$BLUE"
TABLES=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -D "$DB_NAME" -e "SHOW TABLES;" 2>/dev/null | grep -v "Tables_in_" | wc -l)
print_success "✓ Database now has $TABLES tables"

# Final success message
print_header "Database Reset Complete! 🎉"
print_success "✅ All tables dropped"
print_success "✅ Migrations executed"
if [ "$RUN_SEEDS" = true ]; then
    print_success "✅ Seeders executed"
fi
print_success "✅ Database is ready for use"

echo ""
print_message "Database '$DB_NAME' has been successfully reset and is ready for use!" "$GREEN"
echo ""