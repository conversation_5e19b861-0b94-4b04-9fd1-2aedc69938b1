#!/bin/bash

# List Claude Commands Script
# This script lists all available Claude Code commands with descriptions

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CLAUDE_COMMANDS_DIR="${PROJECT_ROOT}/.claude/commands"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_command() {
    echo -e "${GREEN}/$1${NC} - $2"
}

print_info() {
    echo -e "${BLUE}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

# Extract description from command file
extract_description() {
    local file="$1"
    local description=""
    
    # Try to get description from the first line after the title
    if [ -f "$file" ]; then
        # Get the first line that starts with "Please"
        description=$(grep -m 1 "^Please" "$file" 2>/dev/null | sed 's/Please //' | sed 's/: \$ARGUMENTS.*//' | sed 's/\$ARGUMENTS.*//')
        
        # If no "Please" line found, try to get from title
        if [ -z "$description" ]; then
            description=$(head -1 "$file" | sed 's/^# //')
        fi
    fi
    
    echo "$description"
}

# Show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -v, --verbose  Show detailed command information"
    echo "  -c, --count    Show command count only"
    echo ""
    echo "Examples:"
    echo "  $0              List all commands"
    echo "  $0 -v           List commands with detailed information"
    echo "  $0 -c           Show command count"
}

# List all commands
list_commands() {
    local verbose=false
    local count_only=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -c|--count)
                count_only=true
                shift
                ;;
            *)
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check if commands directory exists
    if [ ! -d "$CLAUDE_COMMANDS_DIR" ]; then
        print_warning "No .claude/commands directory found"
        print_info "Run './scripts/setup-claude-commands.sh' to create commands"
        exit 1
    fi
    
    # Get all command files
    local command_files=("$CLAUDE_COMMANDS_DIR"/*.md)
    
    # Check if any commands exist
    if [ ! -f "${command_files[0]}" ]; then
        print_warning "No commands found in .claude/commands/"
        print_info "Run './scripts/setup-claude-commands.sh' to create commands"
        exit 1
    fi
    
    # Count commands
    local command_count=0
    for file in "${command_files[@]}"; do
        if [ -f "$file" ] && [ "$(basename "$file")" != "README.md" ]; then
            ((command_count++))
        fi
    done
    
    # If count only requested
    if [ "$count_only" = true ]; then
        echo "$command_count"
        exit 0
    fi
    
    # Display header
    print_header "═══════════════════════════════════════════════════════════"
    print_header "              Claude Code Commands ($command_count)"
    print_header "═══════════════════════════════════════════════════════════"
    echo
    
    # Display commands in simple format (compatible with older bash versions)
    echo -e "${CYAN}🔍 Debug & Troubleshooting${NC}"
    echo -e "${GREEN}  debug-issue${NC}              - Debug and fix GitHub issues"
    echo -e "${GREEN}  analyze-logs${NC}             - Analyze application logs"
    echo -e "${GREEN}  code-review${NC}              - Perform comprehensive code reviews"
    echo ""
    
    echo -e "${CYAN}🧪 Testing & Validation${NC}"
    echo -e "${GREEN}  run-tests${NC}                - Run and analyze tests"
    echo -e "${GREEN}  test-api${NC}                 - Test API endpoints with Bruno"
    echo ""
    
    echo -e "${CYAN}🚀 Deployment & Infrastructure${NC}"
    echo -e "${GREEN}  deploy${NC}                   - Deploy application to environments"
    echo -e "${GREEN}  health-check${NC}             - Perform system health checks"
    echo ""
    
    echo -e "${CYAN}📊 Monitoring & Performance${NC}"
    echo -e "${GREEN}  monitor-performance${NC}      - Monitor and analyze performance"
    echo -e "${GREEN}  security-audit${NC}           - Perform security audits"
    echo ""
    
    echo -e "${CYAN}📝 Documentation${NC}"
    echo -e "${GREEN}  update-docs${NC}              - Update project documentation"
    echo -e "${GREEN}  generate-api-docs${NC}        - Generate API documentation"
    echo ""
    
    echo -e "${CYAN}🏗️  Project-Specific${NC}"
    echo -e "${GREEN}  project-setup-database${NC}   - Setup project database"
    echo -e "${GREEN}  project-develop-module${NC}   - Develop or enhance modules"
    echo -e "${GREEN}  project-optimize-performance${NC} - Optimize application performance"
    echo -e "${GREEN}  create-migration${NC}         - Create database migrations"
    echo ""
    
    echo -e "${CYAN}🛠️  Utilities${NC}"
    echo -e "${GREEN}  clean-project${NC}            - Clean up project files"
    echo -e "${GREEN}  backup-data${NC}              - Backup project data"
    echo ""
    
    # Display usage information
    print_header "═══════════════════════════════════════════════════════════"
    print_info "Usage: Type '/' in Claude Code to access these commands"
    print_info "Example: /debug-issue #123"
    print_info "         /test-api auth"
    print_info "         /deploy staging"
    echo
    print_info "To add new commands: ./scripts/add-claude-command.sh <name> <description>"
    print_info "To setup commands: ./scripts/setup-claude-commands.sh"
}

# Main execution
main() {
    list_commands "$@"
}

# Run the main function
main "$@"