#!/bin/bash

# Update Makefile with Claude Commands
# This script adds Claude Code command management to the Makefile

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
MAKEFILE="${PROJECT_ROOT}/Makefile"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Make<PERSON>le exists
if [ ! -f "$MAKEFILE" ]; then
    print_error "Makefile not found at $MAKEFILE"
    exit 1
fi

# Check if <PERSON> commands section already exists
if grep -q "# Claude Code Commands" "$MAKEFILE"; then
    print_warning "Claude commands section already exists in <PERSON><PERSON>le"
    print_status "To update, remove the existing section and run this script again"
    exit 0
fi

# Add <PERSON> commands section to <PERSON><PERSON>le
cat >> "$MAKEFILE" << 'EOF'

# Claude Code Commands
.PHONY: claude-setup claude-list claude-add claude-help

claude-setup:
	@echo "Setting up Claude Code commands..."
	@./scripts/setup-claude-commands.sh

claude-list:
	@echo "Listing Claude Code commands..."
	@./scripts/list-claude-commands.sh

claude-add:
	@echo "Usage: make claude-add NAME='command-name' DESC='description' [TEMPLATE=template]"
	@echo "Example: make claude-add NAME='fix-bug' DESC='Fix a specific bug' TEMPLATE=debug"
	@if [ -z "$(NAME)" ] || [ -z "$(DESC)" ]; then \
		echo "Error: NAME and DESC are required"; \
		exit 1; \
	fi
	@./scripts/add-claude-command.sh "$(NAME)" "$(DESC)" "$(TEMPLATE)"

claude-help:
	@echo "Claude Code Commands:"
	@echo "  claude-setup    - Setup Claude commands directory and files"
	@echo "  claude-list     - List all available Claude commands"
	@echo "  claude-add      - Add a new Claude command"
	@echo "  claude-help     - Show this help message"
	@echo ""
	@echo "Examples:"
	@echo "  make claude-setup"
	@echo "  make claude-list"
	@echo "  make claude-add NAME='analyze-bug' DESC='Analyze and fix bugs' TEMPLATE=debug"
	@echo "  make claude-add NAME='deploy-prod' DESC='Deploy to production' TEMPLATE=deploy"
	@echo ""
	@echo "Available templates: default, debug, test, deploy, monitor, docs"

EOF

print_status "Claude commands section added to Makefile successfully! 🎉"
print_status "Available commands:"
print_status "  make claude-setup    - Setup Claude commands"
print_status "  make claude-list     - List all commands"
print_status "  make claude-add      - Add new command"
print_status "  make claude-help     - Show help"
print_status ""
print_status "Run 'make claude-help' for more details"