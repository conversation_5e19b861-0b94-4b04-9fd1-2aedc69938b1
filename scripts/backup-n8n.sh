#!/bin/bash

# N8N Database Backup Script
# This script backs up the n8n SQLite database from Docker container to the repository

set -e

# Configuration
CONTAINER_NAME="blog-api-n8n"
DB_PATH="/home/<USER>/.n8n/database.sqlite"
BACKUP_DIR="backups/n8n"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="database_${TIMESTAMP}.sqlite"
LATEST_BACKUP="database_latest.sqlite"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

# Check if n8n container is running
if ! docker ps --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    log_warn "N8N container '${CONTAINER_NAME}' is not running. Skipping backup."
    exit 0
fi

log_info "Starting N8N database backup..."

# Check if database file exists in container
if ! docker exec "${CONTAINER_NAME}" test -f "${DB_PATH}"; then
    log_error "Database file not found in container at: ${DB_PATH}"
    exit 1
fi

# Get database file size
DB_SIZE=$(docker exec "${CONTAINER_NAME}" stat -c%s "${DB_PATH}" 2>/dev/null || echo "0")
log_info "Database size: ${DB_SIZE} bytes"

# Copy database from container to backup directory
log_info "Copying database from container..."
if docker cp "${CONTAINER_NAME}:${DB_PATH}" "${BACKUP_DIR}/${BACKUP_FILE}"; then
    log_info "Database backed up successfully: ${BACKUP_DIR}/${BACKUP_FILE}"
    
    # Create/update latest backup symlink
    if [ -f "${BACKUP_DIR}/${LATEST_BACKUP}" ]; then
        rm "${BACKUP_DIR}/${LATEST_BACKUP}"
    fi
    cp "${BACKUP_DIR}/${BACKUP_FILE}" "${BACKUP_DIR}/${LATEST_BACKUP}"
    log_info "Latest backup updated: ${BACKUP_DIR}/${LATEST_BACKUP}"
    
    # Add to git if not already tracked
    if command -v git &> /dev/null; then
        # Check if we're in a git repository
        if git rev-parse --git-dir > /dev/null 2>&1; then
            git add "${BACKUP_DIR}/${BACKUP_FILE}" "${BACKUP_DIR}/${LATEST_BACKUP}" 2>/dev/null || true
            log_info "Backup files added to git staging area"
        fi
    fi
    
    # Clean up old backups (keep last 10)
    BACKUP_COUNT=$(find "${BACKUP_DIR}" -name "database_*.sqlite" -not -name "${LATEST_BACKUP}" | wc -l)
    if [ "${BACKUP_COUNT}" -gt 10 ]; then
        log_info "Cleaning up old backups (keeping last 10)..."
        find "${BACKUP_DIR}" -name "database_*.sqlite" -not -name "${LATEST_BACKUP}" -type f | \
            sort | head -n -10 | xargs rm -f
        log_info "Old backups cleaned up"
    fi
    
else
    log_error "Failed to backup database"
    exit 1
fi

log_info "N8N database backup completed successfully!"

# Print backup info
log_info "Backup details:"
echo "  - File: ${BACKUP_DIR}/${BACKUP_FILE}"
echo "  - Size: $(ls -lh "${BACKUP_DIR}/${BACKUP_FILE}" | awk '{print $5}')"
echo "  - Timestamp: ${TIMESTAMP}"