#!/bin/bash

# Setup Claude Code Commands Script
# This script creates the .claude/commands folder and populates it with useful commands
# Based on Claude Code best practices from https://www.anthropic.com/engineering/claude-code-best-practices

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CLAUDE_COMMANDS_DIR="${PROJECT_ROOT}/.claude/commands"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create .claude directory structure
create_claude_directory() {
    print_status "Creating .claude directory structure..."
    
    if [ ! -d "${PROJECT_ROOT}/.claude" ]; then
        mkdir -p "${PROJECT_ROOT}/.claude"
        print_status "Created .claude directory"
    else
        print_warning ".claude directory already exists"
    fi
    
    if [ ! -d "${CLAUDE_COMMANDS_DIR}" ]; then
        mkdir -p "${CLAUDE_COMMANDS_DIR}"
        print_status "Created .claude/commands directory"
    else
        print_warning ".claude/commands directory already exists"
    fi
}

# Create development commands
create_dev_commands() {
    print_status "Creating development commands..."
    
    # Debug Issue Command
    cat > "${CLAUDE_COMMANDS_DIR}/debug-issue.md" << 'EOF'
# Debug Issue

Please analyze and fix the GitHub issue: $ARGUMENTS.

Follow these steps:
1. Use `gh issue view $ARGUMENTS` to get the issue details
2. Understand the problem and examine related code files
3. Identify the root cause and propose a solution
4. Implement the fix with proper testing
5. Create a pull request with clear description
6. Update the issue with resolution details

Focus on:
- Code quality and maintainability
- Proper error handling
- Test coverage
- Documentation updates if needed
EOF

    # Analyze Logs Command
    cat > "${CLAUDE_COMMANDS_DIR}/analyze-logs.md" << 'EOF'
# Analyze Logs

Please analyze the application logs for issues: $ARGUMENTS.

Follow these steps:
1. Check the main server log file: `server.log`
2. Look for error patterns, warnings, and anomalies
3. Analyze performance metrics and response times
4. Check for database connection issues
5. Review authentication and authorization logs
6. Identify any security concerns
7. Provide recommendations for improvements

Focus on:
- Error patterns and frequency
- Performance bottlenecks  
- Security issues
- System health indicators
EOF

    # Code Review Command
    cat > "${CLAUDE_COMMANDS_DIR}/code-review.md" << 'EOF'
# Code Review

Please perform a comprehensive code review of: $ARGUMENTS.

Follow these steps:
1. Examine code structure and organization
2. Check for Go best practices and conventions
3. Review error handling and logging
4. Assess test coverage and quality
5. Look for security vulnerabilities
6. Check performance implications
7. Review documentation and comments
8. Suggest improvements and optimizations

Focus on:
- Code quality and maintainability
- Security best practices
- Performance considerations
- Test coverage
- Documentation completeness
EOF

    # Database Migration Command
    cat > "${CLAUDE_COMMANDS_DIR}/create-migration.md" << 'EOF'
# Create Database Migration

Please create a new database migration for: $ARGUMENTS.

Follow these steps:
1. Analyze the current database schema
2. Understand the required changes
3. Create both up and down migration files
4. Follow the project's migration naming convention
5. Include proper constraints and indexes
6. Test the migration in development
7. Update any related models or repositories
8. Document the changes in the migration

Focus on:
- MySQL 8+ compatibility
- Proper constraint definitions
- Index optimization
- Data integrity
- Rollback capability
EOF

    # Test Command
    cat > "${CLAUDE_COMMANDS_DIR}/run-tests.md" << 'EOF'
# Run Tests

Please run and analyze tests for: $ARGUMENTS.

Follow these steps:
1. Run the specific test suite or all tests
2. Analyze test results and coverage
3. Fix any failing tests
4. Improve test coverage if needed
5. Check for flaky tests
6. Optimize test performance
7. Update test documentation

Commands to use:
- `go test ./...` - Run all tests
- `go test -v ./internal/modules/$MODULE/...` - Run specific module tests
- `go test -cover ./...` - Run tests with coverage
- `make test` - Run tests via Makefile

Focus on:
- Test coverage and quality
- Performance of test suite
- Reliability and stability
EOF

    # API Testing Command
    cat > "${CLAUDE_COMMANDS_DIR}/test-api.md" << 'EOF'
# API Testing

Please test the API endpoints for: $ARGUMENTS.

Follow these steps:
1. Use Bruno API testing tool in `api-tests/bruno/`
2. Run the specific test collection
3. Verify request/response schemas
4. Check authentication and authorization
5. Test error handling and edge cases
6. Validate multi-tenant functionality
7. Check performance and response times
8. Update test documentation

Commands to use:
- `bru run api-tests/bruno --env local`
- `bru run api-tests/bruno/$MODULE --env local`

Focus on:
- API functionality and correctness
- Authentication and authorization
- Error handling
- Performance
- Multi-tenant isolation
EOF

    print_status "Development commands created successfully"
}

# Create deployment commands
create_deployment_commands() {
    print_status "Creating deployment commands..."
    
    # Deploy Command
    cat > "${CLAUDE_COMMANDS_DIR}/deploy.md" << 'EOF'
# Deploy Application

Please deploy the application to: $ARGUMENTS.

Follow these steps:
1. Run all tests to ensure code quality
2. Build the application binary
3. Update Docker images if needed
4. Run database migrations
5. Deploy to the target environment
6. Verify deployment health
7. Monitor application logs
8. Update deployment documentation

Commands to use:
- `make build` - Build the application
- `make test` - Run tests
- `make migrate-up` - Run migrations
- `docker-compose up -d` - Start services

Focus on:
- Zero-downtime deployment
- Database migration safety
- Health checks
- Rollback procedures
EOF

    # Health Check Command
    cat > "${CLAUDE_COMMANDS_DIR}/health-check.md" << 'EOF'
# Health Check

Please perform a comprehensive health check of: $ARGUMENTS.

Follow these steps:
1. Check application health endpoints
2. Verify database connectivity
3. Test Redis and cache systems
4. Check external service integrations
5. Monitor resource usage
6. Review recent logs for errors
7. Test critical API endpoints
8. Validate monitoring systems

Commands to use:
- `curl http://localhost:9077/health/live`
- `curl http://localhost:9077/health/ready`
- `curl http://localhost:9077/health/status`

Focus on:
- System availability
- Performance metrics
- Resource utilization
- Error rates
- Service dependencies
EOF

    print_status "Deployment commands created successfully"
}

# Create monitoring commands
create_monitoring_commands() {
    print_status "Creating monitoring commands..."
    
    # Monitor Performance Command
    cat > "${CLAUDE_COMMANDS_DIR}/monitor-performance.md" << 'EOF'
# Monitor Performance

Please monitor and analyze performance for: $ARGUMENTS.

Follow these steps:
1. Check application metrics and logs
2. Monitor database performance
3. Analyze API response times
4. Review resource usage (CPU, memory)
5. Check for bottlenecks
6. Monitor error rates
7. Analyze traffic patterns
8. Provide optimization recommendations

Tools to use:
- Grafana dashboards (if configured)
- Application logs
- Database metrics
- System monitoring tools

Focus on:
- Response time optimization
- Resource utilization
- Error rate reduction
- Scalability improvements
EOF

    # Security Audit Command
    cat > "${CLAUDE_COMMANDS_DIR}/security-audit.md" << 'EOF'
# Security Audit

Please perform a security audit of: $ARGUMENTS.

Follow these steps:
1. Review authentication and authorization
2. Check for SQL injection vulnerabilities
3. Analyze input validation and sanitization
4. Review CORS and security headers
5. Check for sensitive data exposure
6. Analyze dependency vulnerabilities
7. Review API security practices
8. Check multi-tenant isolation

Commands to use:
- `go mod audit` - Check dependencies
- `gosec ./...` - Security scanner
- Review authentication flows

Focus on:
- Security best practices
- Vulnerability identification
- Data protection
- Access control
- Input validation
EOF

    print_status "Monitoring commands created successfully"
}

# Create documentation commands
create_documentation_commands() {
    print_status "Creating documentation commands..."
    
    # Update Documentation Command
    cat > "${CLAUDE_COMMANDS_DIR}/update-docs.md" << 'EOF'
# Update Documentation

Please update the documentation for: $ARGUMENTS.

Follow these steps:
1. Review existing documentation
2. Identify outdated or missing information
3. Update API documentation
4. Update code comments and README files
5. Review and update architecture diagrams
6. Update deployment guides
7. Check documentation consistency
8. Test documentation examples

Focus on:
- Accuracy and completeness
- Code examples and usage
- Architecture explanations
- API reference updates
- Deployment instructions
EOF

    # Generate API Documentation Command
    cat > "${CLAUDE_COMMANDS_DIR}/generate-api-docs.md" << 'EOF'
# Generate API Documentation

Please generate comprehensive API documentation for: $ARGUMENTS.

Follow these steps:
1. Review all API endpoints in the module
2. Document request/response schemas
3. Include authentication requirements
4. Add example requests and responses
5. Document error codes and messages
6. Include rate limiting information
7. Add multi-tenant considerations
8. Update Bruno test collections

Focus on:
- Complete endpoint coverage
- Clear request/response examples
- Authentication and authorization
- Error handling documentation
- Multi-tenant specifics
EOF

    print_status "Documentation commands created successfully"
}

# Create project-specific commands
create_project_commands() {
    print_status "Creating project-specific commands..."
    
    # Database Setup Command
    cat > "${CLAUDE_COMMANDS_DIR}/project-setup-database.md" << 'EOF'
# Setup Database

Please setup the database for the Blog API v3 project: $ARGUMENTS.

Follow these steps:
1. Start MySQL container: `docker-compose up -d mysql`
2. Wait for database to be ready
3. Run migrations: `make migrate-up`
4. Run seeders: `make seed`
5. Verify database schema
6. Check sample data
7. Test database connectivity

Commands to use:
- `make migrate-up` - Run all migrations
- `make migrate-status` - Check migration status
- `make seed` - Run seeders
- `make reset-db` - Reset database (development only)

Focus on:
- MySQL 8+ compatibility
- Multi-tenant schema
- Data integrity
- Performance optimization
EOF

    # Module Development Command
    cat > "${CLAUDE_COMMANDS_DIR}/project-develop-module.md" << 'EOF'
# Develop Module

Please develop or enhance the module: $ARGUMENTS.

Follow these steps:
1. Analyze module requirements
2. Create/update database migrations
3. Implement models and repositories
4. Create business logic services
5. Implement HTTP handlers
6. Add route registration
7. Create comprehensive tests
8. Update Bruno API tests
9. Update documentation

Module structure:
- `models/` - Domain models
- `repositories/` - Data access layer
- `services/` - Business logic
- `handlers/` - HTTP handlers
- `routes.go` - Route registration

Focus on:
- Multi-tenant support
- RBAC integration
- Error handling
- Test coverage
- API documentation
EOF

    # Performance Optimization Command
    cat > "${CLAUDE_COMMANDS_DIR}/project-optimize-performance.md" << 'EOF'
# Optimize Performance

Please optimize performance for: $ARGUMENTS.

Follow these steps:
1. Profile the application
2. Identify bottlenecks
3. Optimize database queries
4. Implement caching strategies
5. Optimize API endpoints
6. Review resource usage
7. Implement performance monitoring
8. Test performance improvements

Focus on:
- Database query optimization
- Caching implementation
- API response times
- Memory usage
- Concurrent request handling
- Multi-tenant performance
EOF

    print_status "Project-specific commands created successfully"
}

# Create utility commands
create_utility_commands() {
    print_status "Creating utility commands..."
    
    # Clean Project Command
    cat > "${CLAUDE_COMMANDS_DIR}/clean-project.md" << 'EOF'
# Clean Project

Please clean up the project: $ARGUMENTS.

Follow these steps:
1. Remove temporary files and directories
2. Clean build artifacts
3. Remove unused dependencies
4. Clean up log files
5. Remove test artifacts
6. Clean Docker containers and images
7. Update .gitignore if needed

Commands to use:
- `make clean` - Clean build artifacts
- `rm -rf ./bin/*` - Remove binaries
- `rm -rf ./tmp/*` - Remove temporary files
- `docker system prune` - Clean Docker

Focus on:
- Disk space optimization
- Build artifact cleanup
- Dependency management
- Development environment cleanup
EOF

    # Backup Command
    cat > "${CLAUDE_COMMANDS_DIR}/backup-data.md" << 'EOF'
# Backup Data

Please backup project data: $ARGUMENTS.

Follow these steps:
1. Backup database
2. Backup configuration files
3. Backup uploaded files
4. Backup logs (if needed)
5. Create backup archive
6. Verify backup integrity
7. Document backup location

Commands to use:
- `scripts/backup-n8n.sh` - Backup N8N data
- Database backup commands
- File system backups

Focus on:
- Data integrity
- Backup completeness
- Recovery procedures
- Backup scheduling
EOF

    print_status "Utility commands created successfully"
}

# Create .gitignore entry for .claude directory
update_gitignore() {
    print_status "Updating .gitignore for .claude directory..."
    
    GITIGNORE_FILE="${PROJECT_ROOT}/.gitignore"
    
    # Check if .claude entry exists
    if [ -f "$GITIGNORE_FILE" ] && grep -q "^\.claude/" "$GITIGNORE_FILE"; then
        print_warning ".claude/ already in .gitignore"
    else
        echo "" >> "$GITIGNORE_FILE"
        echo "# Claude Code settings (keep commands, ignore local settings)" >> "$GITIGNORE_FILE"
        echo ".claude/settings.local.json" >> "$GITIGNORE_FILE"
        echo ".claude/logs/" >> "$GITIGNORE_FILE"
        print_status "Added .claude entries to .gitignore"
    fi
}

# Create README for commands
create_commands_readme() {
    print_status "Creating commands README..."
    
    cat > "${CLAUDE_COMMANDS_DIR}/README.md" << 'EOF'
# Claude Code Commands

This directory contains project-specific commands for Claude Code. These commands are designed to streamline common development workflows and tasks.

## Available Commands

### Development Commands
- `/debug-issue` - Debug and fix GitHub issues
- `/analyze-logs` - Analyze application logs
- `/code-review` - Perform comprehensive code reviews
- `/create-migration` - Create database migrations
- `/run-tests` - Run and analyze tests
- `/test-api` - Test API endpoints with Bruno

### Deployment Commands
- `/deploy` - Deploy application to environments
- `/health-check` - Perform system health checks

### Monitoring Commands
- `/monitor-performance` - Monitor and analyze performance
- `/security-audit` - Perform security audits

### Documentation Commands
- `/update-docs` - Update project documentation
- `/generate-api-docs` - Generate API documentation

### Project-Specific Commands
- `/project-setup-database` - Setup project database
- `/project-develop-module` - Develop or enhance modules
- `/project-optimize-performance` - Optimize application performance

### Utility Commands
- `/clean-project` - Clean up project files
- `/backup-data` - Backup project data

## Usage

To use these commands in Claude Code:
1. Type `/` to open the slash commands menu
2. Select the desired command
3. Provide any required arguments
4. The command will execute with the specified parameters

## Command Structure

Commands are stored as Markdown files and can include:
- Step-by-step instructions
- Relevant commands to execute
- Focus areas for attention
- Project-specific context

## Adding New Commands

To add new commands:
1. Create a new `.md` file in this directory
2. Follow the existing command structure
3. Include clear instructions and context
4. Test the command before committing

## Best Practices

- Keep commands focused and specific
- Include error handling considerations
- Document any prerequisites
- Use the `$ARGUMENTS` placeholder for parameters
- Follow the project's coding and documentation standards

## Maintenance

These commands should be regularly updated to:
- Reflect changes in project structure
- Include new tools and processes
- Remove obsolete procedures
- Improve clarity and effectiveness
EOF

    print_status "Commands README created successfully"
}

# Main execution
main() {
    print_status "Setting up Claude Code commands for Blog API v3..."
    print_status "Project root: ${PROJECT_ROOT}"
    
    # Create directory structure
    create_claude_directory
    
    # Create command files
    create_dev_commands
    create_deployment_commands
    create_monitoring_commands
    create_documentation_commands
    create_project_commands
    create_utility_commands
    
    # Update project files
    update_gitignore
    create_commands_readme
    
    print_status "Setup complete! 🎉"
    print_status "Commands available in: ${CLAUDE_COMMANDS_DIR}"
    print_status "Use '/' in Claude Code to access these commands"
    
    # List created commands
    print_status "Created commands:"
    ls -la "${CLAUDE_COMMANDS_DIR}/"*.md | awk '{print "  - " $9}' | sed 's/.*\///' | sed 's/\.md$//'
}

# Run the main function
main "$@"