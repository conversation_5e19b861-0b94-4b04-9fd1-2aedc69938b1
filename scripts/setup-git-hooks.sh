#!/bin/bash

# Git Hooks Setup Script
# This script sets up git hooks for the project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
log_info() {
    echo -e "${GREEN}[SETUP]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[SETUP]${NC} $1"
}

log_error() {
    echo -e "${RED}[SETUP]${NC} $1"
}

# Get the repository root directory
REPO_ROOT=$(git rev-parse --show-toplevel 2>/dev/null)
if [ $? -ne 0 ]; then
    log_error "This script must be run from within a git repository"
    exit 1
fi

HOOKS_DIR="${REPO_ROOT}/.git/hooks"
CUSTOM_HOOKS_DIR="${REPO_ROOT}/.githooks"

log_info "Setting up git hooks for N8N database backup..."

# Check if custom hooks directory exists
if [ ! -d "${CUSTOM_HOOKS_DIR}" ]; then
    log_error "Custom hooks directory not found: ${CUSTOM_HOOKS_DIR}"
    exit 1
fi

# Create git hooks directory if it doesn't exist
mkdir -p "${HOOKS_DIR}"

# Install pre-commit hook
if [ -f "${CUSTOM_HOOKS_DIR}/pre-commit" ]; then
    log_info "Installing pre-commit hook..."
    cp "${CUSTOM_HOOKS_DIR}/pre-commit" "${HOOKS_DIR}/pre-commit"
    chmod +x "${HOOKS_DIR}/pre-commit"
    log_info "Pre-commit hook installed successfully"
else
    log_error "Pre-commit hook not found: ${CUSTOM_HOOKS_DIR}/pre-commit"
    exit 1
fi

# Test the pre-commit hook
log_info "Testing pre-commit hook..."
if "${HOOKS_DIR}/pre-commit"; then
    log_info "Pre-commit hook test passed"
else
    log_warn "Pre-commit hook test failed, but hook is installed"
fi

# Create backups directory if it doesn't exist
BACKUP_DIR="${REPO_ROOT}/backups/n8n"
mkdir -p "${BACKUP_DIR}"
log_info "Created backup directory: ${BACKUP_DIR}"

# Add .gitignore for backup directory
GITIGNORE_CONTENT="# N8N database backups - keep latest but ignore timestamped files
database_*.sqlite
!database_latest.sqlite"

if [ ! -f "${BACKUP_DIR}/.gitignore" ]; then
    echo "${GITIGNORE_CONTENT}" > "${BACKUP_DIR}/.gitignore"
    log_info "Created .gitignore for backup directory"
else
    log_info "Backup directory .gitignore already exists"
fi

log_info "Git hooks setup completed successfully!"
log_info ""
log_info "What was installed:"
log_info "  - Pre-commit hook: Automatically backs up N8N database before each commit"
log_info "  - Backup directory: ${BACKUP_DIR}"
log_info "  - Backup script: ${REPO_ROOT}/scripts/backup-n8n.sh"
log_info ""
log_info "Usage:"
log_info "  - Manual backup: ./scripts/backup-n8n.sh"
log_info "  - Automatic backup: Runs automatically before each git commit"
log_info ""
log_info "Note: Only the latest backup (database_latest.sqlite) will be tracked in git"